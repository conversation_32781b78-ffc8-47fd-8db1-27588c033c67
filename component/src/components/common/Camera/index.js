import React, {
  useState,
  useRef,
  forwardRef,
  useImperative<PERSON>andle,
  useEffect,
} from "react";

import ModalWebcam from "./ModalWebcam";
import { message } from "antd";
import { detectMob } from "lib-utils";
const Camera = (props, ref) => {
  const refWebcam = useRef(null);
  const refCallback = useRef(null);
  const refUpload = useRef(null);
  const refIndex = useRef(0);
  const refListCamera = useRef([]);
  const refHtml5Qrcode = useRef();
  const [state, _setState] = useState({});

  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };
  const getCameras = () => {
    return new Promise(async (resolve, reject) => {
      refHtml5Qrcode.current = await import("html5-qrcode");
      refHtml5Qrcode.current.Html5Qrcode.getCameras()
        .then((devices) => {
          if (devices && devices.length) {
            resolve(devices);
          } else {
            resolve([]);
          }
        })
        .catch((err) => {
          reject([]);
        });
    });
  };
  useImperativeHandle(ref, () => ({
    show: async (data = {}, upload, callback) => {
      setState({
        title: data.title,
        show: data.disabled ? false : true,
        propSelect: data.propSelect,
        isScan: data.isScan,
        handleTake: data.handleTake,
        imageCompare: data.imageCompare,
        uploadFunc: upload,
        autoDetect: data.autoDetect,
      });

      refUpload.current = upload;
      refCallback.current = callback;
    },
    hide: onClose,
    getData: () => {
      return refWebcam.current?.getDataWebcam();
    },
    onTake: () => {
      return refWebcam.current?.onTake();
    },
    reTakePhoto: () => {
      return refWebcam.current?.reTakePhoto();
    },
  }));


  useEffect(() => {
    if (state.show) {
      getCameras()
        .then((listDevice) => {
          let listCamera = listDevice;
          if (window.filterCamera)
            listCamera = window.filterCamera(listCamera);
          else {
            if (detectMob()) {
              const cameras = {
                front: [],
                back: [],
              };
              listDevice.forEach((item) => {
                if (item.label.includes("front") || item.label.includes("trước")) {
                  cameras.front.push(item);
                } else {
                  cameras.back.push(item);
                }
              });
              listCamera = [
                cameras.front.length ? cameras.front[0] : "",
                cameras.back.length ? cameras.back[cameras.back.length - 1] : "",
              ].filter((item) => item);
            }
          }
          refListCamera.current = listCamera;
          if (listCamera?.length === 1) {
            refIndex.current = 0;
            showWebcame(refListCamera.current[0].id);
          } else {
            if (listCamera?.length > 1) {
              refIndex.current = 1;
              showWebcame(refListCamera.current[1].id);
            }
          }
        })
        .catch((e) => {
          console.log(e);
        });
    }
  }, [state.show]);

  const onTakePicture = async (file, base64) => {
    if (refUpload.current) {
      const image = await refUpload.current(file, base64);
      if (image) {
        onClose(false, image);
      }
    }
  };

  const onClose = (show, data) => {
    releaseWebcame();
    setState({ show });
    if (refCallback.current) refCallback.current(data);
  };

  const showWebcame = (deviceId) => {
    if (state.isScan) {
      const html5qrCodeScanner = new refHtml5Qrcode.current.Html5Qrcode(
        "camera"
      );
      window.html5qrCodeScanner = html5qrCodeScanner;
      html5qrCodeScanner
        .start(
          deviceId ? { deviceId: { exact: deviceId } } : { facingMode: "user" },
          { fps: 100, qrbox: { width: 500, height: 500 } },
          (qrCodeMessage) => {
            if (qrCodeMessage) {
              // refCallback.current && refCallback.current(qrCodeMessage);
              onClose(false, qrCodeMessage);
            }
          },
          (errorMessage) => {
            // console.log(errorMessage);
          }
        )
        .catch((err) => {
          // refCallback.current &&
          //   refCallback.current("Không tìm thấy thông tin");
        });
    } else {
      var video = document.querySelector("#videoElement");
      if (navigator?.mediaDevices?.getUserMedia) {
        navigator.mediaDevices
          .getUserMedia({
            video: {
              deviceId: { exact: deviceId },
            },
          })
          .then(function (stream) {
            video.srcObject = stream;
          })
          .catch(function (errr) {
            message.error(errr);
          });
      }
    }
  };

  const releaseWebcame = async () => {
    try {
      if (state.isScan) {
        window.html5qrCodeScanner
          .stop()
          .then((ignore) => {
            console.log("stopped after successful scan");
          })
          .catch((err) => {
            console.log("fails to stop after succesfull scan result ");
          });
      } else {
        var video = document.querySelector("#videoElement");
        if (video) {
          const stream = video?.srcObject;
          const tracks = stream?.getTracks() || [];

          tracks.forEach(function (track) {
            track.stop();
          });
          video.srcObject = null;
        }
      }
    } catch (error) {
      // message.error(error);
    }
  };
  const onChangeCamera = () => {
    if (refListCamera.current.length > 1) {
      releaseWebcame();
      if (refIndex.current < refListCamera.current.length - 1) {
        refIndex.current = refIndex.current + 1;
        showWebcame(refListCamera.current[refIndex.current]?.id);
      } else {
        refIndex.current = 0;
        showWebcame(refListCamera.current[0]?.id);
      }
    }
  };
  const reTakePhoto = () => {
    setTimeout(() => {
      showWebcame(refListCamera.current[refIndex.current]?.id);
    }, 500);
  };
  return (
    <>
      {state.show && (
        <ModalWebcam
          ref={refWebcam}
          show={state.show}
          title={state.title}
          modalActions={state.uploadFunc && onTakePicture}
          onClose={onClose}
          disabled={state.disabled}
          propSelect={state.propSelect}
          onChangeCamera={onChangeCamera}
          releaseWebcame={releaseWebcame}
          onreTakePhoto={reTakePhoto}
          isScan={state.isScan}
          handleTake={state.handleTake}
          imageCompare={state.imageCompare}
          autoDetect={state.autoDetect}
        />
      )}
    </>
  );
};

export default forwardRef(Camera);
