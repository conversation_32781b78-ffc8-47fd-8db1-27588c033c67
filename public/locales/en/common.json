{"chaoMung": "Welcome", "designedBy": "Designed by", "bestViewedOnChrome": "This website is best viewed in Google Chrome", "hayLuaChonChucNang": "Please select funtion", "suDungDoPhanGiaiHD+": "Please use a screen resolution of 1366x768 or higher", "timKiem": "Search", "tai": "at", "lan": "Time(s)", "co": "Yes", "khong": "No", "chonPhongKham": "Select clinic", "traCuuNb": "Search patient", "boQua": "<PERSON><PERSON>", "quayLai": "Go back", "troLai": "Return", "dongY": "Agree", "maHoSo": "Patient file code", "maNb": "Patient code", "maHs": "Record code", "tenNb": "Patient name", "timTenNb": "Search patient name", "nhapTenNb": "Enter patient name", "nhapMaHoSo": "Enter patient file code", "timMaHoSo": "Search patient file code", "giaTriThe": "Health insurance card validity", "soGiayToTuyThan": "ID number", "ngaySinh": "DOB", "gioSinh": "Time of birth", "canNang": "Weight", "nhapCanNang": "Enter Weight", "email": "Email", "nhapEmail": "Enter email", "vuiLongNhapDungDinhDangDiaChiEmail": "Please enter the correct format of email address!", "gioiTinh": "Gender", "chonGioiTinh": "Select gender", "vuiLongChonGioiTinh": "Please select gender", "diaChi": "Address", "danToc": "Ethnicity", "chonDanToc": "Select ethnicity", "ngheNghiep": "Occupation", "chonNgheNghiep": "Select occupation", "tenNguoiBaoLanh": "Name of guarantor", "nhapTenNguoiBaoLanh": "Enter guarantor name", "sdtNguoiBaoLanh": "Phone number of Guarantor", "nhapSdtNguoiBaoLanh": "Enter Phone number of Guarantor", "vuiLongNhapSdtNguoiBaoLanh": "Please enter phone number of Guarantor", "matTruoc": "Front side", "matSau": "Back side", "cmndCanCuoc": "ID card", "quocTich": "Nationality", "chonQuocTich": "Select nationality", "vuiLongChonQuocTich": "Please select nationality!", "soBHYT": "Health insurance number", "sdt": "Phone number", "tuoi": "Age", "thang": "Month", "tu": "From", "den": "To", "loi": "Error", "maLoi": "Error code", "canhBao": "Warning", "xacNhan": "Confirm", "tiepNhan": "Accept", "dashboard": "Dashboard", "huy": "Cancel", "xemChiTiet": "View Details", "chonTatCa": "Select all", "boChonTatCa": "Deselect all", "dichVu": "Service", "tenDichVu": "Name of service", "timTenDichVu": "Search service name", "vuiLongChonDichVu": "Please select service!", "doiTuong": "Subject", "chonDoiTuong": "Select subject", "vuiLongChonDoiTuong": "Please select the subject", "tenThuoc": "Medication name", "vuiLongNhapTenThuoc": "Please enter medication name!", "soLuong": "Quantity", "timSoLuong": "Search quantity", "nhapSoLuong": "Enter quantity", "vuiLongNhapSoLuong": "Please enter quantity!", "thuMuc": "Folder", "hoatChat": "Active ingredients", "vuiLongNhapHoatChat": "Please enter active ingredients!", "donViTinh": "Unit of measurement", "nhapDonViTinh": "Enter the unit of measurement", "vuiLongChonDonViTinh": "Please select an unit of measurement!", "hamLuong": "Content", "vuiLongNhapHamLuong": "Please enter content!", "ghiChu": "Note", "nhapGhiChu": "Enter note", "trangThaiDichVu": "Service status", "phong": "Room", "timPhong": "Search room", "chonPhong": "Select room", "vuiLongNhapPhong": "Please enter room number", "chonTenPhong": "Select room name", "luu": "Save", "luuF4": "Save [F4]", "saoChep": "Copy", "luuThongTin": "Save information", "luuThayDoi": "Save changes", "luuY": "Caution", "vuiLongNhapLuuY": "Please enter caution!", "tim": "Search", "xoaDuLieu": "Delete data", "khongCoDuLieuPhuHop": "No matching data", "stt": "Ordinal number", "tuTuc": "Self-pay", "thanhTien": "Total amount", "khac": "Others", "thongTinChiTiet": "Detailed information", "inPhieu": "Print receipt", "inDonThuoc": "Print prescription", "xoaPhieu": "Delete receipt", "hoanPhieu": "Refund receipt", "vuiLongChonNgay": "Please select a date", "thongBao": "Notification", "daHieu": "Understood", "dong": "Close", "thoat": "Exit", "trangThai": "Status", "trangThaiHoan": "Status of Return", "chonTrangThai": "Select status", "tatCa": "All", "tatCaKhoa": "All departments", "thaoTac": "Operation", "daChon": "Selected", "daPhat": "Distributed", "in": "Print", "chon": "Select", "tuNgay": "From date", "denNgay": "To date", "tuThoiGian": "From", "denThoiGian": "To", "luaChon": "Select", "ketQua": "Result", "chonMauKetQua": "Select format of result", "ketLuan": "Conclusion", "nam": "Male", "nu": "Female", "lanPhut": "bpm", "capNhatThanhCong": "Updated data successfully!", "capNhatKhongThanhCong": "Failed to update data!", "daLuuDuLieu": "Data saved", "suaThongTinThanhCong": "Information changed successfully!", "xayRaLoiVuiLongThuLaiSau": "Error occurred, please try again later", "khongTonTaiDichVuNguoiBenh": "Patient service does not exist", "danhSachNguoiBenh": "List of patients", "tuyChon": "Option", "huyTiepNhan": "Cancel reception", "hoVaTen": "Full name", "nhapHoVaTen": "Enter full name", "vuiLongNhapHoVaTen": "Please enter patient name!", "soDienThoai": "Phone number", "soCanCuoc": "ID number", "nhapSoDienThoai": "Enter phone number", "nhapSoCmndCanCuoc": "Enter ID number/ citizen identity number", "nhapSoCanCuoc": "Enter ID number", "capNhatThongTin": "Update information", "tinNhan": "Message", "boLoc": "Filter", "tuTra": "Self-pay", "khongTuTra": "No self-pay", "chonTuTra": "Select self-pay", "themMoi": "Add new", "themMoiF1": "Add new [F1]", "capNhat": "Update", "timHoTenNguoiBenh": "Search patient name", "hoTenNguoiBenh": "<PERSON><PERSON> ‘s full name", "tienIch": "Extension", "nhan": "Press", "xemThem": "See more", "chonNgay": "Select date", "chupAnh": "Take a photo", "thongTin": "Information", "khoa": "Department", "chonKhoa": "Select department", "timKhoa": "Search department", "chuyenKhoa": "Specialized department", "vatTu": "Supplies", "suatAn": "<PERSON><PERSON>", "thuoc": "Medication", "tongTien": "Total amount", "chiTiet": "Detail", "duLieuKhongHopLe": "Invalid data", "hoac": "Or", "loiDan": "Advice", "thanhToan": "Payment", "ttThanhToan": "Total payment", "chiDinhDichVu": "Indicated service", "danhSachNguoiBenhTiepTheo": "List of patients in line", "banCoChacMuonXoaPhieu": "Do you want to delete receipt?", "banCoChacMuonXoa": "Do you want to delete?", "vnd": "VND", "gia": "Price", "thieuTenDanhMuc": "Missing catalogory name", "lyDo": "Reason", "vuiLongChonLyDo": "Please select reason", "donTuTruc": "On-duty cabinet order", "chonVatTu": "Select supplies", "tenNguoiBenh": "Patient name", "nhapTenNguoiBenh": "Enter patient name", "vuiLongChonBenhNhan": "Please select patient", "maNguoiBenh": "Patient code", "nhapMaNguoiBenh": "Enter patient code", "hoVaTenNguoiBenh": "<PERSON><PERSON> ‘s full name", "nhapHoVaTenNguoiBenh": "Enter full name of patient", "diaChiNguoiBenh": "Address of patient", "nhapDiaChiNguoiBenh": "Enter address of patient", "timKiemNguoiBenh": "Search patient", "hienTai": "Current", "nhapDiaChi": "Enter address", "xoa": "Delete", "loai": "Type", "inKetQua": "Print results", "quayLaiEsc": "Back to [ESC]", "moRong": "Extend", "thuGon": "Collapse", "thuNho": "Minimize", "import": "Import data", "xuatDuLieu": "Export data", "maBenhAn": "Medical record code", "maBa": "Medical record code", "nhapMaBenhAn": "Enter medical record code", "choKham": "Waiting for examination", "dangKham": "Ongoing examination", "dangThucHienDv": "Executing service", "choKetLuan": "Waiting for conclusion", "daKetLuan": "Concluded", "dangKetLuan": "Concluding", "caiDatHienThiBang": "Table display settings", "noteKeoThaCot": "Check the column name for display and drag & drop to the location to display", "timTen": "Search name", "veMacDinh": "Back to default", "luuLai": "Save", "soPhieu": "Delivery No.", "timSoPhieu": "Search note number", "dangXuat": "Log out", "doiMatKhau": "Change password", "taiKhoan": "Account", "troGiup": "Help", "timKiemManHinh": "Search screen name", "timKiemTenPhieuIn": "Search for printed slip name", "thongTinDichVu": "Service Information", "cachThucCanThiep": "Intevention Protocol", "phuongThucCanThiep": "Intervetion Mode", "deNghi": "Suggestion", "tong": "Total", "traKetQuaDvThanhCong": "Service result released successfully", "lieuDung": "Dosage", "chonLieuDung": "Select dosage", "dichVuTrongGoi": "Service in package", "dichVuTrongBo": "Service in set", "ngayApDung": "Applied date", "timNgay": "Search date", "xoaDuLieuThanhCong": "Delete data successfully", "themMoiThanhCongDuLieu": "New data added successfully!", "noiDung": "Content", "trangThaiNb": "Patient status", "khoaNhapVien": "Inpatient department", "soNgayDT": "Number of treatment days", "danhSachPhieu": "List of notes", "vuiLongCho": "Please wait", "sua": "Edit", "xemChiTietThongTin": "View detail information", "suaChiTietThongTin": "Edit detail information", "hanThe": "Card expiration date", "namSinh": "Year of birth", "maDv": "Service code", "ngayThucHien": "Execution date", "daTT": "Payment executed", "tiepTheo": "Next", "soTien": "Amount", "kyHieu": "Sign", "nhapSoTien": "Enter amount", "phieu": "Receipt", "nhapMaDichVu": "Enter service code", "nhapTenDichVu": "Enter service name", "hoanThanh": "Completed", "choDuyet": "Waiting for approval", "huyHoanThanh": "Cancel completion", "loaiDichVu": "Type of service", "khongTinhTien": "No charge", "coTinhTien": "With charge", "chiDinhThuoc": "Order of medicine", "chiDinhVacxin": "Order of vaccine", "chiDinhNgoaiDieuTri": "Order services outside of treatment", "tyLeTt": "Rate of payment", "chiDinhVatTu": "Order of supplies", "chiDinhSuatAn": "Order of meals", "thongTinPhongGiuong": "Information of Room and bed", "khongTheKetNoiDenServer": "Disconnected! Please check the device's internet connection!", "dangCapNhatHeThong": "Connection is interrupted. Please wait for processing!", "daKhoiPhucKetNoi": "Connection restored!", "nhapMaHoTenQuetQrNb": "Enter record code, full name, QR code scanning", "banChacChanMuonXoa": "Are you sure you want to delete", "diem": "Point", "luot": "Turn", "phongChiDinh": "Indicated room", "chiDinhHoaChat": "Indicated chemicals", "caiDatTimKiemHangHoa": "Search Settings of Commodity", "hangHoaConTon": "Commodity in stock", "tatCaHangHoa": "All commodity", "banCoChacMuonXoaBanGiNay": "Are you sure you want to delete this record?", "danhSachDichVu": "List of services", "scanBieuMauHsba": "Scan medical record forms", "tt30": "TT30", "goi": "Call", "inGiayTo": "Print documents", "luaChonChuDe": "Select theme", "thietLapHienThi": "Display setting", "thoiGian": "Time", "chonThoiGian": "Select time", "chanDoan": "Diagnosis", "coKetQua": "Results available", "huyKetQua": "Cancel results", "them": "Add", "tuyChonIn": "Print option", "toBiaBenhAn": "Cover of medical record", "trangChu": "Home page", "dienGiai": "Description", "cachDung": "How to use", "chiDinhPHCN": "Order of rehabilitation", "chonVacxin": "Select vaccine", "nhapTenVacxin": "Enter vaccine name", "nhapTenKhangNguyen": "Enter antigen", "thuongTru": "Permanent address", "tamTru": "Temporary residence", "tuKhoa": "Key word", "vuiLongNhapTuKhoa": "Please enter key word", "goiKham": "Call the patient", "sapXepTinhNang": "Arrange Function", "duongDung": "Route of administration", "lan/ngay": "Time/day", "sl/lan": "Quantity/time", "soNgay": "Number of days", "tuyChinhGiaoDienPhanMem": "Customize software interface", "tinhNang": "Feature", "henDieuTri": "Appointment for treatment", "xoaHoSo": "Delete record", "banCoChacMuonXoaBanGhiDuocChon": "Are you sure you want to delete selected records?", "banCoChacMuonXoaBanGhiDuocLoc": "Are you sure you want to delete filtered records?", "slHuy": "Cancellation amount", "lyDoHuy": "Reason for cancellation", "slHuyLonHonSL": "The number for cancellation exceeds the number", "timMaBenhAnDaiHan": "Search long-term medical record codes", "maNbLienKet": "Link code of patient", "guiHoSo": "Send record", "banCoChacChanMuonGuiGiamDinhCacHoSoDuocChon": "Are you sure you want to send for assessment of the selected records?", "banCoChacChanMuonGuiGiamDinhCacHoSoDuocLoc": "Are you sure you want to send for assessment the filtered records?", "soLuongHuy": "Cancellation amount", "ngayDangKy": "Registration date", "taoMoi": "Create new", "soLuongSoCap": "Primary quantity", "ngay": "date", "gio": "hour", "slSang": "Morning count", "slChieu": "Afternoon count", "slToi": "Evening count", "slDem": "Night count", "dvtSuDung": "Unit of usage", "donViTien": "VND", "slSoCap": "Primary quantity", "phanLoaiNb": "Patient classification", "maPtttQuocTe": "International payment method code (ICD 9)", "chonMaPtttQuocTe": "Select International payment method code (ICD 9)", "xacNhanChuyenPhong": "Confirm to switch from {1} to {2}", "xacNhanBanGiaoThanhCong": "Confirm successful handover", "xacNhanSuDungThuocThanhCong": "Confirm successful medication use", "cot": "Column", "phaiLonHon0": "must bigger than 0", "giaBH": "Insured price", "giaKhongBH": "Uninsured price", "phuThu": "Surcharge", "thoiGianThucHien": "Performance time", "nhapHoacQuetQrMaNb": "Enter or scan the patient QR code", "soTienConLai": "Remaining amount", "ngaySDThuoc": "Date of medication use", "scanGiayTo": "Scan document", "uuTien": "Priority", "giaBhyt": "Health Insurance price", "chuaCauHinhThongTinVisualize": "Information of Visualization screen has not been configured", "khongTheLayThongTinLinkVisualize": "Unable to retrieve the Visualize link information", "appiSofHToolChuaDuocKhoiDong": "iSofHTool application has not been started. Please check again", "donGia": "Unit price", "bh": "Insurance", "ngoaiTru": "Outpatient", "theTam": "Temporary card", "banCoChacMuonXoaBanGhiNay": "Are you sure you want to delete this record?", "chinhSua": "Edit", "timTenNbQrNbMaNbMaHoSo": "Search patient name, patient QR, patient code, patient file code", "slDuyet": "Approved quantity", "soHangHienThi": "Display number of rows", "hang": "Row", "trenTong": "of total", "xuatFile": "Export file", "sapXepCotTrongBang": "Sort columns in table", "lamMoi": "Refresh", "TIEP_DON": "Reception management", "KHAM_BENH": "Examination", "PHA_CHE_THUOC": "Compounding medicine", "QUAN_LY_DINH_DUONG": "Nutritional management", "QMS": "QMS", "KIOSK": "Number obtainment kiosk", "THIET_LAP": "Settings", "DANH_MUC": "Category", "QUAN_TRI_HE_THONG": "System management", "QUAN_LY_THONG_BAO": "Manage notifications", "QUYET_TOAN_BHYT": "Health insurance settlement", "DANH_SACH_GIAY_DAY_CONG": "List of papers for portal deposit", "KHO_MAU": "Blood storage", "QUAN_LY_KHO": "Inventory management", "DASHBOARD": "Dashboard", "KY_SO": "Digital signature", "KE_HOACH_TONG_HOP": "General plan", "BAO_CAO": "Report", "THU_NGAN": "Cashier", "NHA_THUOC": "Pharmacy", "GOI_DICH_VU": "Service package", "PHAU_THUAT_THU_THUAT": "Surgery/ Procedure", "PHUC_HOI_CHUC_NANG": "Rehabilitation", "CDHA": "Diagnostic Imaging", "TDCN": "Functional investigation", "CHAN_DOAN_HINH_ANH_THAM_DO_CHUC_NANG": "Diagnostic imaging and functional testing", "KHO": "Warehouse", "XET_NGHIEM": "Test", "SINH_HIEU": "Vital signs", "KHAM_SUC_KHOE_HOP_DONG": "Health check contract", "DIEU_TRI_DAI_HAN": "Long-term treatment", "TIEM_CHUNG": "Vaccination", "QUAN_LY_NOI_TRU": "Inpatient management", "HO_SO_BENH_AN": "Medical record", "THEO_DOI_DIEU_TRI": "Treatment monitoring", "DANH_MUC_VI_TRI_CHAN_THUONG": "List of injury locations", "giaTri": "Value", "trong": "Empty", "matKhau": "Password", "chonThoiDiem": "Select a time", "chonDuongDung": "Select the route of administration", "chupLai": "Capture", "taiAnhLen": "Upload image", "vuiLongChonDungDinhDangAnh": "Please choose the correct format of image", "vuiLongChonBieuMau": "Please select form", "vuiLongChonAnh": "Please select an image", "traMau": "Return blood", "capNhatThanhCongDuLieuThietLapChung": "Successfully updated general data settings!", "chiTietFile": "File detail", "taiLenTepThuMuc": "Upload files/folders", "vuiLongTaiLenTepThuMuc": "Please upload files/folders!", "vuiLongImportFileExcel": "Please import file (excel)!", "chonSheet": "Select sheet", "chonDong": "Select line", "taoMoiDaGiuCho": "Create new. A slot was reserved", "anhDaiDien": "Avatar", "vuiLongNhapTuNgay": "Please enter from date", "khongCoDuLieu": "No data", "taiLai": "Reload", "tepTaiLen": "Upload file", "thoiGianApp": "App Time", "thoiGianDb1": "Database1 time", "thoiGianDb2": "Database2 Time", "doTre": "Latency", "matKetNoiDbPhu": "Lost secondary database connection. Please contact technical department!", "saiGioServer": "Wrong server time. Please contact technical department", "soLuongSoCapLinhDu": "Primary Quantity Issued in Surplus", "slThuCapLinhKhoTaiKhoa": "Secondary Quantity Issued from Department Warehouse", "slSoCapLinhKhoTaiKhoa": "Primary Quantity Issued from Department Warehouse", "huyTimKiem": "Cancel search", "nhaSanXuat": "Manufacturer", "timHovaTen": "Find first and last name", "vuiLongChonHoVaTen": "Please select your first and last name", "chonHoTen": "Choose your full name", "tapTin": "File", "thoiGianRaVien": "Discharge time", "chonDoiTuongKCB": "Select the Subject for examination and treatment", "xoaDichVu": "Delete service", "CDHA-TDCN": "Imaging diagnosis - Functional exploration", "toanManHinh": "Full screen", "duocThuocVatTu": "Pharmacy - drugs, supplies", "quanLyPhacDoDieuTri": "Manage treatment regimens", "datKham": "Book an examination", "yTe4Cham0": "Healthcare 4.0", "chonAnh": "Select photo", "nb": "Patient", "tinTuc": "News", "chonToaNha": "Select building", "lichSuKhamChuaBenh": "History of Medical examination and treatment", "chonDichVu": "Select service!", "chonDoiTac": "Choose a partner", "loaiDoiTuong": "Object Type", "chuaPhat": "Not released yet", "khongThanhToan": "No payment", "CĐHA_TDCN": "CDHA - TDCN", "GIOI_THIEU_PHAN_MEM": "Software Introduction", "HUONG_DAN_KHAC": "Other instructions", "QUAN_LY_PHAU_THUAT_THU_THUAT": "Surgical procedure management", "XEP_HANG_CHO_QMS": "Queue for QMS", "KIOSK_TIEP_DON": "Reception kiosk", "ketThuc": "End", "quetMaQR": "Scan QR code", "vuiLongXoaThuocDungDungKemTruoc": "Please remove concomitant medications first!", "hoaChat": "Chemical", "timTenFileLog": "Find log file name", "thuTuMacDinh": "Default order", "thuTuChuCaiADenZ": "Alphabetical order (A->Z)", "thuTuChuCaiZDenA": "Alphabetical order (Z->A)", "thuTuTuyChinh": "Custom Order", "taoMoiThuTu": "Create new order", "khongCo": "Do not have", "xoaKhongThanhCong": "Delete failed", "vuiLongDienItNhatMotThongTin": "Please fill in at least one information", "khongTonTaiThongTinPhauThuat": "No surgical information available", "taiDuLieuKhongThanhCong": "Data download failed", "goiMo": "Surgical package", "vuiLongChonGoiMo": "Please select surgery package", "thieuThongTinChanDoan": "Lack of diagnostic information", "nhapChanDoan": "Enter diagnosis", "luuThuTu": "Save order", "daLuuThuTu": "Order saved", "luuKhongThanhCong": "Save failed", "tuChoiDuyetChiPhi": "Refuse to approve expenses", "choDuyetChiPhi": "Waiting for cost approval", "daDuyetChiPhi": "Approved cost", "xong": "Finished", "khongTimThayKetQuaPhuHop": "No matching results found!", "dangTaiXuongFile": "Downloading file...", "taiXuongThanhCong": "File download successful", "taiXuongLoi": "Download error", "vuiLongNhapSoLuongLon0": "Please enter a quantity greater than 0", "maTheRFID": "RFID card code", "ok": "OK", "tuDienYKhoa": "Medical dictionary", "thang1": "January", "thang2": "February", "thang3": "March", "thang4": "April", "thang5": "May", "thang6": "June", "thang7": "July", "thang8": "August", "thang9": "September", "thang10": "October", "thang11": "November", "thang12": "December", "dangKyKhuonMat": "Face Registration", "xacThucKhuonMatThanhCong": "Face verification successful", "thayDoiAnhChupKhuonMat": "Change face photo", "xoaAnhChupKhuonMat": "Delete face photo", "vuiLongNhapTuKhoaTimKiemTaiDay": "Please enter search keywords here", "banCoMuonThayDoiAnhChupKhuonMat": "Do you want to change your face photo?", "banCoMuonXoaAnhChupKhuonMat": "Do you want to delete the face photo?", "thayDoiAnhChupKhuonMatThanhCong": "Face photo change successful", "xoaAnhChupKhuonMatThanhCong": "Face photo deleted successfully", "dangKyKhuonMatThanhCong": "Face registration successful", "khongTimThayAnhKhuonMatCu": "Old face photo not found", "khongTimThayAnhKhuonMat": "Face photo not found", "vuiLongNhapMaHoacTenNguoiBenh": "Please enter patient code or name", "moTaLuuy": "Note/Description", "nhapMoTaLuuY": "Enter note/description", "nguoiBaoLanh": "Guarant<PERSON>", "congTyBaoHiemBaoLanh": "Insurance company guarantee", "nguoiGioiThieu": "Referral", "nhap": "Enter", "cacNgayTruoc": "Previous days", "huyTraMau": "Cancel blood", "danhSachNguoiBenhDuPhong": "List of patients for prevention", "chonThoiGianVaoVien": "Select admission time", "chiDinhChePhamDinhDuong": "Nutritional product indications", "a": "A", "b": "B", "loaiChiDinh": "Type of designation", "Y_HOC_CO_TRUYEN": "Traditional medicine", "voiceToTextOnOff": "Turn on/off voice -> text", "thuocVatTu": "Medicines - Supplies", "vatTuTheoXN": "Medical supplies by laboratory test", "vuiLongNhapGiaTri": "Please enter value!", "nguoiDuyet": "The reviewer", "phongThucHien": "Room of execution", "chonPhongThucHien": "Select room to perform", "tt20": "TT20", "canLamSang": "Paraclinical", "dieuTri": "Treatment", "mienCCT": "CCT exemption", "phaChung": "Mix together", "vuiLongKiemTraLaiThongTinDaNhap": "Please check the information entered!", "taiXuong": "Download", "xem": "See", "khongCoKetQuaTaiXuong": "No download results", "khongCoThongTinNguoiBenh": "No patient information available", "nhapCauTruyVanSql": "Enter SQL query here...", "themMoiKhongThanhCong": "Add new failed", "xoaThanhCong": "Delete successful", "themMoiThanhCong": "New addition successful", "chonCoSoChiNhanhLamViec": "Select branch office", "tatCaDoiTac": "All partners", "tatCaMaHoSo": "All profile codes", "tatCaDichVu": "All services", "xuatExcel": "Excel export", "thongKe": "Statistical", "slTheoNgay": "Primary Quantity Issued from Department Warehouse", "chua": "Not yet", "neuCo": "If any", "taiFileLogHeThong": "Download system log file", "guiYeuCauHoTro": "Submit a support request [Ctrl + F1]", "tieuDe": "Title", "nhapTieuDe": "Enter title", "loaiYeuCau": "Request Type", "chonLoaiYeuCau": "Select request type", "urlManHinh": "Screen url", "fileDinhKem": "Attached file", "guiYeuCauHoTroThanhCong": "Support request sent successfully!", "gioiHanSoLuongKyTu": "Maximum number of characters: {{soLuong}} characters", "truongGioihanSoLuongKyTu": "The {{tenTruong}} field cannot exceed {{soLuong}} characters.", "numTuanTruoc": "{{num}} last week", "numThangTruoc": "{{num}} months ago", "numNamTruoc": "{{num}} years ago", "sau": "After", "kham": "Examination", "duyet": "Approve", "ngayDuyetPhieu": "Date of approval", "thoiGianLocal": "Local time", "maHoSoCu": "Old file code", "xoaBoLoc": "Remove filter", "chiaSeDangNhap": "Share login", "maChiaSe": "Share code", "thoiGianHetHan": "Expiration time", "chonThoiGianHetHan": "Choose expiration time.", "copyMa": "Copy code", "chiaSeDangNhapDescription": "Login sharing is a form of sharing login information without directly sending account/password information. The partner will automatically log out when the sharing time expires.", "chiaSe": "Share", "napTaiKhoan": "Top up account", "loiKhiDocFile": "Error reading file", "nhapMaDangNhapDuocChiaSe": "Enter the shared login code", "maDangNhapKhongHopLe": "Invalid login code", "thoiGianHetHanDaQuaHan": "The expiration time has passed!", "thucHien": "Implement", "chonFile": "Select file", "khongTonTaiFile": "File does not exist.", "thoiGianHeThong": "System time", "taiKhoanThieuThongTinChungChiHanhNghe": "The account lacks information on the professional certification.", "chonNgayIn": "Select print date", "vuiLongChonNgayIn": "Please select the print date", "lichSuKiemTraTheBHYT": "History of health insurance card verification", "khoiDongLaiISofhTool": "Restart iSofHTools", "huyetApTamThuCanLonHonHuyetApTamTruong": "Systolic blood pressure needs to be higher than diastolic blood pressure", "updateISofHToolsNewVersion": "iSofHTools has released a new version {{version}}. <br/>Click agree to automatically update.", "vuiLongKiemTraLai": "Please check again", "xacNhanNhap": "Confirm entry", "khongDuocBoTrong": "Cannot be left blank", "me": "Mother", "con": "Child", "quanLyVanTay": "Manage fingerprints", "dangNhapBangVanTay": "Login with fingerprint", "khongTonTaiTaiKhoanHoacTaiKhoanChuaThietLapVanTay": "Account does not exist or account has not set up fingerprint", "nhapTaiKhoanDeTiepTuc": "Enter your account to continue", "vuiLongNhapTaiKhoanVaMatKhau": "Please enter your account and password", "tinhNangCoTrenIsofhtoolVersion": "Feature available on iSofHTools version {{version}} or later. Please update", "banDaHuyQuetVanTay": "You have canceled fingerprint scanning", "chuKyKhongHopLe": "Invalid signature", "layVanTayKhongThanhCong": "Fingerprint capture failed", "kyVanTay": "Fingerprint signature", "banCoMuonXoaPhieu": "Do you want to delete the {{ma<PERSON>hi<PERSON>}} ticket of patient {{tenNb}}?", "maDichVu": "Service code", "nhapSoThuTu": "Enter order number", "vuiLongNhapSoThuTu": "Please enter order number", "thoiGianKetThucBenhAn": "Time to close medical record", "chuaXacDinh": "Not determined", "chiDinhCdhaTdcn": "Indication for Diagnostic Imaging - Technical Diagnosis", "chiDinhPttt": "Physical therapy indication", "chiDinhXetNghiem": "Test indication", "phatLoaGoiLaiNguoiBenh": "Announce a callback for the patient.", "nbCoPhieuThuChuaTTOrPhieuHoanChuaDuyetHoan": "The patient has an unpaid receipt or an unapproved refund receipt.", "noiSinh": "Place of birth", "chonLoai": "Select type", "heThongDaGuiYeuCauGuiSmsThanhCong": "The system has successfully sent the SMS request.", "vuiLongKiemTraLaiDuLieuDaNhap": "Please check the entered data again!", "maLK": "Link code", "khongCoThongTinLichSuKCB": "No medical history information available.", "chonBenhAnDeTruyXuatThongTin": "Select the medical record to retrieve information.", "chonMotBenhAnTuDanhSachBenTrai": "Select a medical record from the list of medical records on the left.", "daHetTon": "The medication/supply {{tenDichVu}} is out of stock.", "dangKyVanTay": "Register fingerprint", "taiLenThanhCong": "Upload successful"}