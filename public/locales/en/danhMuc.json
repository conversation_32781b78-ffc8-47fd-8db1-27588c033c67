{"ma": "Code", "ten": "name", "timTen": "Search name", "timMa": "Search code", "moTa": "Description", "vuiLongNhapMoTa": "Please enter a description", "vuiLongNhapMoTaKhongQua1000KyTu": "Please enter a description no more than 1000 characters", "vuiLongNhapGiaTriKhongQua1000KyTu": "Please enter a value with no more than 1000 characters", "coQuanCongTac": "Workplace", "coHieuLuc": "<PERSON><PERSON>", "khongHieuLuc": "Invalid", "chonHieuLuc": "Select validity", "timCoQuanCongTac": "Search workplace", "vuiLongNhapTen": "Please enter a name", "vuiLongNhapTenTitleKhongQua1000KyTu": "Please enter the name {{ title }} no more than 1000 characters", "vuiLongNhapCoQuanCongTa": "Please enter workplace", "thuocDauSao": "Asterisk-marked medication", "thongTinChiTiet": "Detailed information", "nhapMa": "Enter code", "hieuLuc": "Validity", "vuiLongChonDichVu": "Please select service!", "vuiLongChonAnhLuocDoPhauThuatThuThuat": "Please select a photo of the surgery and procedure diagram", "nhapTenDichVu": "Enter service name", "chonTenDichVu": "Select the service name", "vuiLongChonTenDichVu": "Please select a service name", "tenDichVu": "Name of service", "vuiLongNhapTenDichVu": "Please enter service name", "vuiLongNhapTenDichVuKhongQua1000KyTu": "Please enter service name with no more than 1000 characters", "maDichVu": "Service code", "vuiLongNhapMaDichVu": "Please enter service code", "vuiLongNhapGiaTriKhongQua20KyTu": "Please enter a value no more than 20 characters!", "cachThucCanThiep": "Intevention Protocol", "ketQua": "Result", "nhapTen": "Enter name", "tenMau": "Form name", "huy": "Cancel", "luu": "Save", "ketLuan": "Conclusion", "danhMucMauKetQuaCDHATDCN": "Result Sample list of Diagnostic Imaging - Functional Imaging", "soNgayCanhBaoHsd": "Number of warning days of expiry date", "themMoi": "Add new", "mucDichSuDung": "Purpose of use", "chonMucDichSuDung": "Select purpose of use", "quanTriHeThong": "System management", "nhomTinhNang": "Group of features", "danhMucVaiTro": "Category of Roles", "quanLyTaiKhoan": "Account management", "loaiDoiTuong": "Type of Subject", "danhMucLoaiHinhThanhToan": "Category of Payment Method", "loaiHinhThanhToan": "Payment method", "chonLoaiHinhThanhToan": "Select payment method", "danhMucGoiDichVu": "List of service packages", "khoaChiDinhGoi": "Package Indicated by Department", "dichVuTrongGoi": "Service in package", "thongTinGoi": "Package information", "hanCheKhoaChiDinh": "Restriction of indicating department", "chonHanCheKhoaChiDinh": "Select restriction of indicating department", "loaiDv": "Type of service", "goiDichVu": "Service package", "danhMucLoaiDoiTuong": "Category of Subject Type", "macDinh": "<PERSON><PERSON><PERSON>", "maGiuongGuiBHYT": "Bed code for send to health insurance", "timMaGiuongGuiBHYT": "Search bed code for send to health insurance", "nhapMaGiuongGuiBHYT": "Enter bed code for send to health insurance", "thongTinGoiDichVu": "Service package information", "quay": "The counter", "danhMucQuay": "Category of Counter", "khongLaySo": "Not taking numbers", "chonKhongLaySo": "Choose to not take numbers", "maQuay": "Counter code", "timMaQuay": "Search counter code", "vuiLongNhapMaQuay": "Please enter counter code", "vuiLongNhapMaQuayKhongQua20KyTu": "Please enter counter code with no more than 20 characters!", "tenQuay": "Counter name", "timTenQuay": "Search counter name", "vuiLongNhapTenQuay": "Please enter counter name", "vuiLongNhapTenQuayKhongQua1000KyTu": "Please enter counter name with no more than 1000 characters!", "loaiQuay": "Counter type", "chonLoaiQuay": "Select counter's type", "timLoaiQuay": "Search counter type", "vuiLongChonLoaiQuay": "Please select counter type!", "soLuongHangCho": "Queue number", "timSoLuongHangCho": "Search queue number", "vuiLongNhapSoLuongHangCho": "Please enter queue number!", "vuiLongNhapSoLuongHangChoKhongQua2KyTu": "Please enter queue number with no more than 2 characters!", "maThuoc": "Medication code", "vuiLongNhapMaThuoc": "Please enter medication code", "vuiLongNhapMaThuocKhongQua20KyTu": "Please enter medication code with no more than 20 characters!", "tenThuoc": "Medication name", "vuiLongNhapTenThuoc": "Please enter medication name!", "vuiLongNhapTenThuocKhongQua1024KyTu": "Please enter medication name with no more than 1024 characters!", "maHoatChat": "Active ingredient code", "vuiLongNhapMaHoatChat": "Please enter active ingredient code", "chonMaHoatChat": "Select active ingredient code", "tenHoatChat": "Active ingredient name", "nhapTenHoatChat": "Enter the name of the active ingredient", "hamLuong": "Content", "nhapHamLuong": "Enter content", "nhomThuoc": "Medicine group", "chonMaNhomThuoc": "Select medicine group code", "phanNhomThuoc": "Medicine subgroup", "chonPhanNhomThuoc": "Select medicine subgroup", "phanLoaiThuoc": "Medicine classification", "vuiLongChonPhanLoaiThuoc": "Please select medicine classification", "chonPhanLoaiThuoc": "Select medicine classification", "donViSoCap": "Primary unit", "batBuocDienDonViSoCapKhiHeSoDinhMucLonHon1": "Must fill primary unit when Rated Coefficient > 1", "chonDonViSoCap": "Select primary unit", "donViThuCap": "Secondary unit", "vuiLongChonDonViThuCap": "Please select secondary unit", "chonDonViThuCap": "Select secondary unit", "heSoDinhMuc": "Rated Coefficient", "vuiLongNhapHeSoDinhMuc": "Please enter Rated Coefficient!", "vuiLongNhapHeSoDinhMucLonHon0": "Please enter Rated Coefficient bigger than 0", "nhapHeSoDinhMuc": "Enter Rated Coefficient", "dungTich": "Capacity (ml)/1 primary Unit of measurement", "nhapDungTich": "Enter capacity (ml)/1 primary Unit of measurement", "quyCach": "Specification", "nhapQuyCach": "Enter Specification", "nuocSanXuat": "Country of manufacture", "chonNuocSanXuat": "Select Country of manufacture", "chonNhaSanXuat": "Select manufacturer", "maThietBi": "Device code", "vuiLongNhapMaThietBi": "Please enter device code", "hoiSucTichCuc": "Intensive care", "chonHoiSucTichCuc": "Select intensive care", "nhaCungCap": "Provider", "chonNhaCungCap": "Select provider", "giaSauVAT1DvtSoCap": "Price after VAT/ 1 Unit of measurement", "nhapGiaSauVAT1DvtSoCap": "Enter price after VAT/ 1 Unit of measurement", "giaTran": "Price ceiling", "nhapGiaTran": "Enter price ceiling", "tranBaoHiem": "Insurance ceiling", "nhapTranBaoHiem": "Enter insurance ceiling", "tyLeBhThanhToan": "Insurance payment rate", "nhapTyLeBhThanhToan": "Enter insurance payment rate", "vuiLongNhapTyLeBhThanhToanKhongQua3KyTu": "Please enter insurance payment rate with no more than 3 characters!", "tyLeThanhToanDichVu": "Service payment rate", "vuiLongNhapTyLeThanhToanDichVu": "Please enter service payment rate!", "vuiLongNhapTyLeDvThanhToanKhongQua3KyTu": "Please enter service payment rate with no more than 3 characters!", "nhomDichVuCap1": "Service group level 1", "vuiLongChonNhomDichVuCap1": "Please select service group level 1", "chonNhomdichVuCap1": "Select service group level 1", "nhomDichVuCap2": "Service group level 2", "vuiLongChonNhomDichVuCap2": "Please select service group level 2", "chonNhomdichVuCap2": "Select service group level 2", "nhomDichVuCap3": "Service group level 3", "chonNhomdichVuCap3": "Select service group level 3", "maTuongDuong": "Equivalence code", "nhapMaTuongDuong": "Enter Equivalence code", "tenTuongDuong": "Equivalence name", "nhapTenTuongDuong": "Enter Equivalence name", "soVisa": "Visa number", "nhapSoVisa": "Enter visa number", "maLienThongDuocQuocGia": "National pharmaceutical communication code", "nhapMaLienThongDuocQuocGia": "Enter National pharmaceutical communication code", "nguonChiTraKhac": "Payment by other sources", "hienThiLenKiosk": "Display on KIOSK", "khoaChiDinhTheoDvtSoCap": "Indicating department by primary Unit of measurement", "chonKhoaChiDinhTheoDvtSoCap": "Select indicating department by primary Unit of measurement", "duongDung": "Route of administration", "chonDuongDung": "Select the route of administration", "nhomThau": "Contracting group", "goiThau": "Bidding package", "quyetDinhThau": "Bidding decision", "khongDuocNhapQua25KyTu": "Do not enter more than 25 characters", "khongDuocNhapQua50KyTu": "Do not enter more than 50 characters", "khongDuocNhapQuaNumKyTu": "Do not enter more than {{ num }} characters", "choPhepKeSlLe": "Allow listing of odd quantity", "theoDoiNgaySd": "Monitoring day of use", "khongTinhTien": "No charge", "timMaThuoc": "Search medicine code", "timTenHoatChat": "Search active ingrdient name", "timTenThuoc": "Search medicine name", "chonNhomThuoc": "Search medicine subgroup", "timHamLuong": "Search content", "timQuyCach": "Search specification", "timHeSoDinhMuc": "Search Rated coefficient", "giaNhap": "Imported price", "timGiaNhap": "Search imported price", "loaiKhuVuc": "Area type", "vuiLongChonLoaiKhuVuc": "Please select area type", "nhomChiPhi": "Cost group", "chonNhomChiPhi": "Select cost group", "timTyLeBhtt": "Search Health Insurance rate", "timTenTuongDuong": "Search equivalence name", "timSoVisa": "Search visa number", "timMaLienThongDuocQuocGia": "Search National pharmaceutical communication code", "timNguonChiTraKhac": "Search other payment source", "timKhoaChiDinhTheoDvtSoCap": "Search indicating department by primary Unit of measurement", "chonNhomThau": "Select Bidding group", "chonTinhTien": "Select Invoice Preparation", "chonTheoDoiNgaySuDung": "Select tracking of day of use", "chonKeSlLe": "Allow listing of odd quantity", "timDuongDung": "Search route of administration", "timDungTich": "Search capacity (ml)/1 primary unit of measurement", "nhapQuyetDinhThau": "Enter bidding decision", "chonGoiThau": "Select bidding package", "maLoaiHinhThanhToan": "Payment method code", "tenLoaiHinhThanhToan": "Payment method name", "vuiLongNhapMaLoaiHinhThanhToan": "Please enter payment method code", "danhMucHauQuaTuongTac": "List of Interactive consequence", "vuiLongNhapTenGiaTri": "Please enter name {0}", "vuiLongNhapTenGiaTriKhongQuaKyTu": "Please enter name {0} not exceeding {1} character", "maGiaTri": "Code {0}", "tenGiaTri": "Name {0}", "mauMucDo": "Color level", "nhapMauMucDo": "Enter color level", "danhMucDacTinhDuocLy": "List of pharmacological characteristics", "danhMucMucDoTuongTac": "List of interaction level", "khaiBaoTuongTacThuoc": "Declaration of drug interaction", "dacTinhDuocLy": "Pharmacological characteristics", "hoatChat": "Active ingredients", "timHoatChat": "Find active ingredients", "bietDuoc": "Patent medicine", "mucDoTuongTac": "Interaction level", "coChe": "Mechanism", "hauQua": "Consequence", "xuTri": "Managemet", "canhBaoIcd": "ICD Warning", "chanDoanBenh": "Diagnosis of Disease", "chanKeThuoc": "Stop prescription", "xetNghiem": "Laboratory tests", "ngoaiTruICD": "Except for ICD", "canhBaoXetNghiem": "Test warning", "vuiLongNhapGiaTriKhongQuaKyTu": "Please enter {0} with no more than {1} characters", "vuiLongNhapMucDoTuongTac": "Please enter interaction level", "vuiLongNhapHauQua": "Please enter consequence", "vuiLongNhapChanDoanBenh": "Please enter diagnosis of disease", "apDungTt35": "Apply TT35", "taoChiSoCon": "Allowed to create new sub-indicator", "maMucDich": "Purpose code", "tenMucDich": "Purpose name", "donGiaKhongBh": "Uninsured unit price", "nhapDonGiaKhongBh": "Enter uninsured unit price", "vuiLongNhapSoDuong": "Please enter a positive number", "donGiaBh": "Insured unit price", "nhapDonGiaBh": "Enter insured unit price", "phuThu": "Surcharge", "nhapPhuThu": "Enter surcharge", "apDungTheoThoiGianThucHienDv": "Apply by the date of service execution", "ghiChu": "Note", "nhapGiaPhuThu": "Enter surcharge price", "nhapGiaBaoHiem": "Enter insured price", "nhapGiaKhongBaoHiem": "Enter uninsured price", "vuiLongNhapGiaKhongBaoHiem": "Please enter uninsured price", "apDungTt30": "Apply TT30", "tyLeThanhToanBh": "Insurance payment rate", "phienBanBieuMau": "Form version", "taoPhienBanBieuMau": "Creat form version", "vuiLongChonBieuMau": "Please select form", "tenPhienBan": "Version name", "loaiPhienBan": "Version type", "phienBanGoc": "Original version", "phienBanTaiLen": "Uploaded version", "tepBieuMau": "Form file", "ngayTao": "Created date", "nguoiTao": "Creator", "kichThuocAnhPhaiNhoHon": "Image size must be smaller", "vuiLongNhapHoatChat": "Please enter active ingredients!", "batBuocNhapHoatChat1HoacHoatChat2": "Must enter active ingredient 1 or active ingredient 2", "chonTruongTrenKiosk": "Please select the field displayed on the kiosk welcome screen", "maVatTu": "Supply code", "tenVatTu": "Supply name", "timTenVatTu": "Search supply name", "maGoiThau": "Bidding package code", "timMaGoiThau": "Search bidding package code", "tenGoiThau": "Bidding package name", "timTenGoiThau": "Search bidding package name", "timMaVatTu": "Search supply code", "dmChiPhiHapSayVTYTTaiSuDung": "List of cost of Steaming and Drying for reusable medical supplies", "chiPhiHapSay": "Drying costs", "nhapChiPhiHapSay": "Enter the cost of steaming", "apDungTuNgay": "Apply from", "apDungDenNgay": "Apply to", "thongTinDichVuTrongBo": "Information of in-set service", "tenBo": "Set name", "dichVuTrongBo": "Service in set", "phongThucHien": "Performance Room", "bacSiNgoaiVien": "Outpatient doctor", "title": "Category", "hangThe": "Card level", "cheDoChamSoc": "Care mode", "chiSoSong": "Vital signs", "chucVu": "Position", "chuyenKhoa": "Specialized department", "vuiLongChonChuyenKhoa": "Please select Specialized Department", "chonChuyenKhoa": "Select Specialized Department", "danToc": {"title": "Ethnicity", "maDanToc": "Ethnicity code", "nhapMaDanToc": "Please enter ethnicity code!", "nhapMaDanTocKhongQua20KyTu": "Please enter ethnicity code with no more than 20 characters!", "tenDanToc": "Ethnicity name", "nhapTenDanToc": "Please enter ethnicity name!", "nhapTenDanTocKhongQua1000KyTu": "Please enter ethnicity name with no more than 1000 characters!", "maDongBoTCQG": "National immunization synchronization code", "nhapMaTiemChungQuocGia": "Please enter national immunization synchronization code", "maTuongDuong": "Equivalence code", "nhapMaTuongDuong": "Enter Equivalence code"}, "quocGia": {"title": "Country", "maQuocGia": "Country code", "nhapMaQuocGia": "Please enter the country code!", "nhapMaQuocGiaKhongQua20KyTu": "Please enter the country code with no more than 20 characters!", "tenQuocGia": "Country name", "nhapTenQuocGia": "Please enter country name!", "nhapTenQuocGiaKhongQua1000KyTu": "Please enter the country name with no more than 1000 characters!", "maDongBoTCQG": "National immunization synchronization code", "nhapMaTiemChungQuocGia": "Please enter national immunization synchronization code", "maTuongDuong": "Equivalence code", "nhapMaTuongDuong": "Enter Equivalence code"}, "dichVuAn": "Food service", "coQuanDonVi": "Units / Agencies", "hauQuaTuongTac": "Consequence of interaction", "hinhThucNhapXuat": "Method of import and export", "hocViHamVi": "Academic rank and degree", "khoa": "Department", "huongDanSuDung": "Operating manual", "timHDSD": "Search operating manual", "lieuDung": "Dosage", "chonLieuDung": "Select dosage", "loaGoiSo": "Speaker for calling number", "timLoaGoiSo": "Search Speaker for calling number", "loaiBenhAn": "Type of medical record", "chonLoaiBenhAn": "Select type of medical record", "vuiLongChonLoaiBenhAn": "Please select type of medical record", "loaiBuaAn": "Type of meal", "khangNguyen": "Antigen", "luocDo": "diagram", "anhLuocDoPhauThuat": "Photo of Surgery diagram", "anhLuocDoPhauThuatThuThuat": "Surgical and Procedural Diagram Image", "phuongPhapCheBien": "Processing method", "chonPhuongPhapCheBien": "Select processing method", "maPhuongPhap": "Method code", "tenPhuongPhap": "Method name", "vuiLongNhapMaPhuongPhap": "Please enter the method code", "vuiLongNhapMaPhuongPhapKhongQuaNumKyTu": "Please enter the method code with no more than {{ num }} characters!", "vuiLongNhapTenPhuongPhap": "Please enter method name", "vuiLongNhapTenPhuongPhapKhongQuaNumKyTu": "Please enter method name with no more than {{ num }} characters!", "dangBaoChe": "Dosage form", "timDangBaoChe": "Search dosage form", "phanLoaiNb": "Patient classification", "phanLoaiPHCN": "Classification of rehabilitation", "loaiCapCuu": "Type of emergency", "loaiGiuong": "Bed type", "loaiPhieu": "Note type", "loiDan": "Advice", "lyDoDoiTraDv": "Reason for exchanging and returning service", "lyDoTamUng": "Reason of advance payment", "maMay": "Machine code", "mauDienBien": "Template of progression", "mauKetQuaPhauThuatThuThuat": "Template of surgery - procedure", "mauQms": "QMS template", "moiQuanHe": "Relationship", "ngayNghi": "day off", "ngheNghiep": {"title": "Occupation", "maNgheNghiep": "Occupation code", "nhapMaNgheNghiep": "Please enter occupation code!", "nhapMaNgheNghiepKhongQua20KyTu": "Please enter occupation code with no more than 20 characters!", "tenNgheNghiep": "Occupation name", "nhapTenNgheNghiep": "Please enter occupation name!", "nhapTenNgheNghiepKhongQua1000KyTu": "Please enter occupation name with no more than 1000 characters!", "maTuongDuong": "Equivalence code", "nhapMaTuongDuong": "Enter Equivalence code"}, "nguoiDaiDien": "Representative", "timNguoiDaiDien": "Find a representative", "nguonNhapKho": "Input source of warehouse", "nguyenNhanNhapVien": "Cause of admission", "nhomChiSo": "Indicator group", "nhomHoaChat": "Chemical group", "nhomVatTu": "Supply group", "noiLayBenhPham": "Location for specimen collection", "phanLoaiBmi": "BMI classification", "phuongThucThanhToan": "Payment method", "phuongPhapNhuom": "Staining method", "phuongPhapVoCam": "Emotionless method", "phong": "Room", "chonPhong": "Select room", "quanHam": "Military rank", "quayTiepDon": "Reception desk", "soHieuGiuong": "Bed code", "thangSoBanLe": "Surplus retail", "theBaoHiem": "Insurance card", "thoiGianCapCuu": "Emergency time", "toaNha": "Building", "timToaNha": "Search building", "vanBangChuyenMon": "Professional qualification", "viTriChanThuong": "Location of trauma", "viTriSinhThiet": "Location of biopsy", "xuatXu": "Country of origin", "tuongTacThuoc": "Drug interaction", "hoaChat": "Chemicals", "khuVuc": "Area", "chonKhuVuc": "Select area", "vuiLongChonKhuVuc": "Please select area", "vuiLongChonLoaGoiSo": "Please select a speaker number", "vuiLongChonToaNha": "Please select building!", "vuiLongChonKhoa": "Please select the department!", "chonKhoa": "Select department", "soLuongTiepTheo": "Next quantity", "vuiLongNhapSoLuongTiepTheo": "Please enter number of next patients", "vuiLongNhapSoLuongTiepTheoKhongQua2KyTu": "Please enter number of next patients with no more than 2 characters!", "soLuongHangDoi": "Number of queues", "vuiLongNhapSoLuongHangDoi": "Please enter number of queues", "vuiLongNhapSoLuongHangDoiKhongQua2KyTu": "Please enter number of queues with no more than 2 characters!", "mucDoUuTien": "Priority level", "nhanTheoDoi": "Tracking label", "dongBoIward": "Syncing <PERSON><PERSON>", "chonSoHieuGiuong": "Select bed code", "chonTaiKhoan": "Select account", "maPhieuLinh": "Obtainment paper code", "loaiDonThuoc": "Type of prescription", "vuiLongNhapMa": "Please enter code", "vuiLongNhapMaTitleKhongQua20KyTu": "Please enter code {{ title }} with no more than 20 characters", "thuocPhauThuat": "In surgery - procedure", "dichVuGiuong": "Bed service", "truongHopApDung": "Case for Application", "phanLoaiPttt": "Classification of Surgery/ Procedure", "chonPhanLoaiPttt": "Select classification of surgery and procedure", "soNgaySauMo": "Number of days after surgery", "vuiLongChonKhoaChiDinh": "Please select indicating department", "thietLapChonGiuong": "Settings for Bed selection", "khoaChiDinh": "Indicating department", "danhMuc": "Category", "maAtc": "ATC code", "vacXin": "Vaccine", "tenKhangNguyen": "Antigen name", "maQuanLyTiemChung": "Immunization management code", "soMuiCanTiem": "Number of shots", "lieuLuong": "Dosage", "donViTinh": "Unit of measurement", "chonDonViTinh": "Choose the unit of measurement", "nhapDonViTinh": "Enter the unit of measurement", "giaNhapSauVat": "Import price after VAT", "gioiTinhSuDung": "Gender of use", "doTuoiSuDung": "Age of use", "tenLoaiBenhDuPhong": "Name of disease for prevention", "thoiGianTiemCachNhau": "Time Interval between injections", "maVacXin": "Vaccine code", "tenVacXin": "Vaccine name", "tuTuoi": "From age", "denTuoi": "To age", "quyenSoPhieuThu": "Receipt book", "caLamViec": "Working shift", "phieuIn": "Note for printing", "viTriPhieuIn": "Location of note for printing", "manHinhPhieuIn": "Screen of note for printing", "maManHinh": "Screen code", "timTheoMaManHinh": "Search by screen code", "tenManHinh": "Screen name", "timTheoTenManHinh": "Search by screen name", "maViTri": "Location code", "timTheoMaViTri": "Search by location code", "tenViTri": "Location name", "timTheoTenViTri": "Search by location name", "manHinh": "Screen", "viTri": "Location", "maPhieu": "Note code", "timTheoMaPhieu": "Search by note code", "tenPhieu": "Note name", "timTheoTenPhieu": "Search by note name", "chonLoaiManHinh": "Select screen type", "chonLoaiViTri": "Select location type", "mauKqXnDotBien": "Result template for Mutation test", "khacNgayLamViec": "Working days are different", "vuiLongNhapMaHex": "Please enter the correct Hex code with 6 digits", "hinhThucPhatLoa": "Speaker format", "sttChonNhanh": "Ordinal numbers for quick selection", "nhapSttChonNhanh": "Enter ordinal numbers for quick selection", "donViSuDung": "Utilizing Units", "chonDonViSuDung": "Select Utilizing Units", "vuiLongChonDonViSuDung": "Please select Utilizing Units", "phanTuyenPttt": "Sugery and procedure level", "maPtttQuocTe": {"title": "International Classification of Diseases,Ninth Revision (ICD -9)", "tatCa": "All", "chuongPttt": "Surgery and Procedure Chapter", "nhomPttt": "Surgical Procedure Group", "loaiPttt": "Surgery/ Procedure type", "tenPttt": "Name of Sugery and procedure"}, "maQuyTrinh": "Process code", "vuiLongNhapMaQuyTrinh": "Please enter process code", "tenQuyTrinh": "Process name", "vuiLongNhapTenQuyTrinh": "Please enter process name", "quyTrinhXetNghiem": "Testing procedure", "theoIso": "By ISO", "tuNgay": "From date", "chonNgay": "Select date", "denNgay": "To date", "tenTaiKhoanThayDoi": "Name of the individual who make changes", "chonTenTaiKhoanThayDoi": "Select name of the individual who made changes", "tenTruong": "Field name", "chonTenTruong": "Select field name", "loaiLamTron": "Type of rounding", "daiMaNbRieng": "Unique Patient Code Width", "sinhDaiMaNbRieng": "Generate Unique Patient Code Width", "chonLoaiGiuong": "Select Bed Type", "chonMaViTri": "Select Location Code", "choSuaDoi": "Allow Service Adjustment", "tuyChonGia": "Price Option", "khoaChiDinhDv": "Department Ordering Service", "dkThanhToanBh": "Conditions of Insurance Payment", "giaTriCu": "Old Value", "giaTriMoi": "New Value", "nguoiThayDoi": "Person Making Changes", "thoiGianThayDoi": "Time of Change", "thongTinDichVu": "Service Information", "dichVuKemTheo": "Additional Service", "chiSoCon": "Sub-indicator", "lichSuChinhSua": "Edit History", "vuiLongChonLoaiKetQua": "Please enter Result Type!", "thuTuHienThi": "Display order", "timThuTuHienThi": "Find display order", "maKyHieu": "Symbol code", "maNguonNuoiBenh": "Patient source code", "vuiLongNhapMaNguonNguoiBenh": "Please enter Patient source code", "vuiLongNhapMaNguonNguoiBenhKhongQua20KyTu": "Please enter patient source code with no more than 20 characters!", "tenNguonNguoiBenh": "Patient source name", "vuiLongNhapTenNguonNguoiBenh": "Please enter the patient source name", "nhomNguonNguoiBenh": "Patient source group", "vuiLongNhapNhomNguonNguoiBenh": "Please enter patient source group", "chonNhomNguonNguoiBenh": "Select patient source group", "nguoiGioiThieu": "<PERSON><PERSON><PERSON>", "thanhToanSau": "Pay later", "danhMucVeCapCuu": "Category of emergency", "danhMucVeKho": "Category of warehouse", "danhMucVeKyInPhieu": "Category of signing, printing, and notes", "danhMucVeDichVu": "Category of service", "danhMucVeTTHanhChinh": "Category of administrative information", "danhMucChung": "General categories", "danhMucChamSocKhachHang": "Category of Customer care", "thoiGianDangXuat": "Logout time (minute)", "vaiTro": "Role", "resetMatKhau": "Reset password", "tenTaiKhoan": "Account Name", "chonTenTaiKhoan": "Choose account name", "hoTen": "Full Name", "vuiLongNhapHoTen": "Please enter your full name", "maNhanVien": "Employee code", "vuiLongNhapTenTaiKhoan": "Please enter account name", "vuiLongNhapMaNhanVien": "Please enter employee code", "vuiLongNhapTenTaiKhoanKhongQua255KyTu": "Please enter account name with no more than 255 characters", "vuiLongNhapHoTenKhongQua255KyTu": "Please enter your full name with no more than 255 characters", "tenDonViCongTac": "Name of workplace", "diaChiDonViCongTac": "Address of workplace", "ganVaiTro": "Assign roles", "timKiemVaiTro": "Search roles", "moRong": "Extend", "thuNho": "Minimize", "xacNhanResetMatKhauChoTaiKhoan": "Confirm password reset for account", "danhMucLoaiCapCuu": "Category of Type of emergency", "danhMucThoiGianCapCuu": "Category of Emergency Time", "danhMucViTriChanThuong": "Category of Location of Trauma", "danhMucChePhamMau": "Category of blood products", "danhMucDoiTac": "Category of Partner", "danhMucDuongDung": "Category of Routes of Administration", "danhMucHinhThucLoaiNhapXuat": "Category of Import type/Export type", "danhMucNhomHoaChat": "Category of Chemical Groups", "danhMucHoatChat": "Category of Active Ingredients", "danhMucKhangNguyen": "Category of Antigen", "danhMucPhuongPhapCheBien": "Category of Processing Methods", "danhMucLieuDung": "Category of Dosage", "danhMucLieuDungBacSi": "Category of Dosage - Doctor", "danhMucLoiDan": "Category of Advice", "danhMucNguonNhapKho": "Category of import source of warehouse", "danhMucNhomThuoc": "Category of Drug Groups", "danhMucNhomVatTu": "Category of Supply Groups", "danhMucPhanLoaiThuoc": "Category of Medicine classification", "danhMucPhanNhomThuoc": "Category of Medicine subgroups", "danhMucThangSoBanLe": "Category of Surplus Retail", "danhMucThuoc": "Category of Medicine", "danhMucThuocKeNgoai": "Category of External Prescription", "danhMucVatTu": "Category of Supplies", "danhMucXuatXu": "Category of Origin", "danhMucHoaChat": "Category of Chemicals", "danhMucNguyenNhanNhapVien": "Category of causes of admission", "danhMucTaiNanThuongTich": "List of Accidents and Injuries", "danhMucLoaiPhieu": "Category of note types", "danhMucMayIn": "Category of Printers", "danhMucQuyenKy": "Category of Signing Authority", "danhMucQuyen": "List of Rights", "danhMucNhomTinhNang": "List of feature groups", "danhMucBenhPham": "Category of Specimen", "danhMucBoChiDinh": "Category of Indicated Sets", "danhMucChuyenKhoa": "Category of Specialized Department", "danhMucDichVuAn": "Category of Meal services", "danhMucDichVuCDHAVaTDCN": "Category of Diagnostic imaging - Functional investigation service", "danhMucDichVuGiuong": "Category of Bed Services", "danhMucDichVuKhamBenh": "Category of medical examination service", "danhMucDichVuNgoaiDieuTri": "Category of non-treatment service", "danhMucDichVuPTTT": "Category of Surgery - Procedure service", "danhMucDichVuXetNghiem": "Category of Testing Service", "danhMucDichVuCDHA": "List of Imaging Diagnosis and Functional Exploration Services", "danhMucDonViTinh": "Category of Units of Measurement", "danhMucLyDoDoiTraDichVu": "Category of reasons for exchanging/returning services", "danhMucMauKetQuaCDHAVaTDCN": "Category of result template of diagnostic imaging and functional investigation", "danhMucMauKetQuaXetNghiem": "Category of template of test results with mutations", "danhMucMauKetQuaXNCoDotBien": "Category of template of test results with mutation", "danhMucNhomChiSo": "Category of indicators", "danhMucNhomDichVu": "Category of Service groups", "danhMucNoiLayMauBenhPham": "Category of locations for specimen collection", "danhMucPhuongPhapVoCam": "Category Insensitivity Methods", "danhMucPhuongPhapNhuom": "Category of staining methods", "danhMucViTriSinhThiet": "Category of location of biopsy", "danhMucBenhVien": "Category of hospitals", "danhMucChucVu": "Category of positions", "danhMucCoQuanDonVi": "Category of Units / Agencies", "danhMucDanToc": "Category of ethnicity", "danhMucDiaChiHanhChinh": "Category of administrative addresses", "danhMucDonViChiNhanh": "Category of Unit Branches", "danhMucHocHamHocVi": "Category of academic degree and rank", "danhMucKhuVuc": "Category of Area", "danhMucMoiQuanHe": "Category of Relationship", "danhMucNgheNghiep": "Category of Occupation", "danhMucNguoiDaiDien": "Category of Representatives", "danhMucNhanTheoDoi": "Category of Tracking labels", "danhMucQuanHam": "Category of Military Ranks", "danhMucVanBangChuyenMon": "Category of Professional qualifications", "danhMucChiPhiHapSayVTYTTaiSuDung": "Category of fee of Steaming and Drying for reusable medical supplies", "danhMucBacSiNgoaiVien": "Category of Outpatient Doctors", "danhMucCaLamViec": "Category of Working Shifts", "danhMucAnhLuocDoPhauThuat": "Categories Photos and diagrams of Surgery", "danhMucCauHoiKhamSangLoc": "Category of Screening Questions", "cauHoiKhamSangLoc": "Screening questions", "danhMucCheDoChamSoc": "Category of Care Mode", "danhMucChiSoSong": "Category of Vital Signs", "danhMucGoiMo10Ngay": "Category of 10-day surgery packages", "danhMucHoiDong": "Category of Councils", "danhMucKhoa": "Category of Departments", "danhMucKiosk": "Category of Kiosks", "danhMucLoaGoiSo": "Categories of speakers for calling numbers", "danhMucLoaiBenhAn": "Category of Type of medical record", "danhMucLoaiBuaAn": "Category of Meal Types", "danhMucLoaiGiuong": "Category of Bed types", "danhMucLyDoTamUng": "Category of reasons for advance payment", "danhMucMaMay": "Category of Machine Codes", "danhMucMaPTTTQuocTe": "Category of international surgery and procedure codes (ICD-9)", "danhMucMaPhieuLinh": "Category of obtainment papers", "danhMucMauDienBien": "Category of Templates of Progression", "danhMucMauKetQuaPTTT": "Category of result templates of Surgery and Procedure", "danhMucMauQMS": "Category of QMS templates", "danhMucNgayNghiLe": "Category of Holidays", "danhMucNhomBenhTat": "Category of Disease Groups", "danhMucNhomChiPhiCoSo": "Category of Cost groups", "danhMucPhanLoaiDanhGiaBMI": "Category of classification of BMI assessment", "danhMucPhanLoaiNguoiBenh": "Category of patient classification", "danhMucPhanLoaiPHCN": "Category of rehabilitation classification", "danhMucPhieuIn": "Category of notes for printing", "danhMucPhong": "Category of rooms", "danhMucPhuongThucThanhToan": "Category of Payment Methods", "danhMucQuyTrinhXetNghiem": "Category of Testing Procedures", "danhMucSoHieuGiuong": "Category of bed codes", "danhMucTaiLieuHDSD": "Category of Operating Manuals", "danhMucTaiSanKhac": "Category of Other Assets", "danhMucTheBaoHiem": "Category of Insurance Cards", "danhMucToaNha": "Category of Buildings", "danhMucVacXin": "Category of Vaccine", "danhMucKhaiBaoTuongTacThuoc": "Category of declaration of drug interaction", "danhMucChuongTrinhGiamGia": "Category of Discount Programs", "danhMucHangThe": "Category of Card Tiers", "danhMucNguonNguoiBenh": "Category of Patient Sources", "danhMucBaoCao": "Category of Reports", "danhMucBenhYHocCoTruyen": "List of Traditional Medicine Diseases", "danhMucNhanVien": "Category of Employees", "maNV": "Employee code", "bangChuyenMon": "Professional qualification", "chungChi": "Certificate", "thongTinKhoaPhong": "Information of Department & Room", "ngaySinh": "DOB", "soBHXH": "Social insurance number", "mstTenTKKy": "Tax code/ Signing account name", "matKhauHDDT": "Email password", "hienThiThongTin": "Display information", "anThongTin": "Hide information", "anhChuKy": "Signature photo", "datKhamOnline": "Make an online appointment for medical examination", "chungThuSoMkKy": "Digital certificate/ Password of signature", "danhHieu": "Title", "nha": "Home", "khoaQuanLy": "Department in charge of management", "tenVietTat": "Abbreviated name", "nhapTenVietTat": "Enter Abbreviated name", "loaiKetQua": "Type of Result", "nhapLoaiKetQua": "Enter Type of Result", "ketQuaThamChieu": "Reference results", "chonKetQuaThamChieu": "Select reference results", "tyLeThanhToanDV": "Service payment rate", "vuiLongNhapTyLeThanhToanDV": "Please enter Service payment rate", "vuiLongNhapTyLeThanhToanDVKhongQua3KyTu": "Please enterService payment rate with no more than 3 characters", "nhapTyLeThanhToanDV": "Enter Service payment rate", "tenBaoCao": "Report name", "chonBaoCao": "Select report", "chiSoNuThap": "Low indicator for female", "nhapChiSoNuThap": "Enter low indicator for female", "chiSoNuCao": "High indicator for female", "nhapChiSoNuCao": "Enter high indicator for female", "chiSoNamThap": "Low indicator for male", "nhapChiSoNamThap": "Enter low indicator for male", "chiSoNamCao": "High indicator for male", "nhapChiSoNamCao": "Enter high indicator for male", "loaiMau": "Type of blood", "chonLoaiMau": "Select type of blood", "theTich": "Volume", "nhapTheTich": "Enter volume", "soNgaySuDung": "Number of days of use", "nhapSoNgaySuDung": "Enter number of days of use", "dvt": "Unit of measurement", "chonDvt": "Select Unit of measurement", "nhomDVCap1": "Service group Level 1", "chonNhomDVCap1": "Select Service Group Level 1", "nhomDVCap2": "Service Group Level 2", "chonNhomDVCap2": "Select Service Group Level 2", "nhomDVCap3": "Service Group Level 3", "chonNhomDVCap3": "Select Service Group Level 3", "truongHopKeDv": "In case of prescribing services", "chonTruongHopKeDv": "Select the case where service is prescribed", "tiepDonCLS": "Paraclinical reception", "chonTiepDonCLS": "Select Paraclinical reception", "maSoQuyetDinh": "Decision code", "nhapMaSoQuyetDinh": "Enter decision code", "ngayQuyetDinh": "Decision date", "chonNgayQuyetDinh": "Select decision date", "nguonKhacChiTra": "Payment by Other sources", "chonNguonKhacChiTra": "Select payment by Other sources", "chiPhiVanChuyen": "Transportation costs", "chonChiPhiVanChuyen": "Select Transportation cost", "maGuiLISPACS": "LIS/PACS sending code", "nhapMaGuiLISPACS": "Enter the LIS/PACS sending code", "donViKetNoi": "Connection unit", "chonDonViKetNoi": "Select connection unit", "yeuCauBenhPham": "Request for specimens", "chonYeuCauBenhPham": "Select request for specimens", "dvCoKetQuaLau": "This service requires long waiting time for results", "chonDvCoKetQuaLau": "Select the service requiring long waiting time for results", "coThuNgoai": "External collection", "chonThuNgoai": "Select external collection", "chonYeuCau": "Select request", "theoYeuCau": "On request", "tuDongTaoChiSoCon": "Automatic generation of sub-indicators", "chonTuDongTaoChiSoCon": "Select Automatic generation of sub-indicators", "chonLoaiHinhThuc": "Select of type of form", "vuiLongChonApDungTuNgay": "Please select apply from date", "tenPhong": "Room name", "diaChiPhong": "Room address", "nhapDiaChiPhong": "Enter the room address", "doiTuongKhamChuaBenh": "Subject of medical examination and treatment", "chonDoiTuongKhamChuaBenh": "Select subject of medical examination and treatment", "soLuongToiDaSuDungTrong1MHS": "Maximum quantity used in 1 medical record code", "nhapSoLuongToiDaSuDungTrong1MHS": "Enter the maximum quantity to use in 1 patient file code", "soLuongToiDaSuDungTrong1Ngay": "Maximum quantity used in 1 day", "nhapSoLuongToiDaSuDungTrong1Ngay": "Enter the maximum quantity to use in 1 day", "soNgayDuocThucHienLanTiepTheo": "Number of days to be executed next time", "nhapSoNgayDuocThucHienLanTiepTheo": "Enter the number of days to be executed next time", "dieuKienThanhToanBaoHiem": "Conditions for Insurance payment", "chonNhom": "Select group", "loaiThoiGian": "Time type", "chonLoaiThoiGian": "Select time", "thoiDiemChiDinh": "Indicated time", "chonThoiDiemChiDinh": "Choose the indicated time", "nhapMaDichVu": "Enter service code", "chonLoaiDoiTuong": "Select subject type", "soLuong": "Quantity", "timSoLuong": "Search quantity", "nhapSoLuong": "Enter quantity", "vuiLongNhapSoLuong": "Please enter quantity!", "thoiGianCoGiaTri": "Valid time", "doiTuongApDung": "Subjects for application", "chuyenKhoaKSK": "Specialized Department of Health Check", "chonChuyenKhoaKSK": "Choose Specialized Department of Health Check", "soNgayCanhBaoKeDichVuBHYT": "Number of warning days of listing health insurance services", "nhapSoNgayCanhBaoKeDichVuBHYT": "Enter number of warning days of listing health insurance services", "lenTTBA": "Create summary of medical record", "tachSoLuongKhiKe": "Split quantity when listing", "thuNgoai": "External collection", "tachPhieuChiDinhKhiTrungDV": "Split prescription notes when services overlap", "dvTheoYeuCau": "Services on request", "dungChoCovid": "Used for Covid", "duongTinh": "Positive", "amTinh": "Negative", "thiLuc": "Visual acuity", "danhMucThiLuc": "Category of Visual acuity", "maThiLuc": "Visual acuity code", "tenThiLuc": "Name of Visual acuity", "vuiLongNhapMaThiLuc": "Please enter visual acuity code", "vuiLongNhapTenThiLuc": "Please enter visual acuity name", "maDoiTac": "Partner code", "timMaDoiTac": "Find Partner Code", "maDoiTacCon": "Child partner code", "nhapMaDoiTacCon": "Enter the child partner code", "tenDoiTacCon": "Child partner's name", "nhapTenDoiTacCon": "Enter the child partner's name", "vuiLongNhapMaDoiTac": "Please enter Partner Code", "vuiLongNhapMaDoiTacKhongQua20KyTu": "Please enter Partner Code with no more than 20 characters", "tenDoiTac": "Partner name", "timTenDoiTac": "Find Partner Name", "vuiLongNhapTenDoiTac": "Please enter Partner Name", "vuiLongNhapTenDoiTacKhongQua1000KyTu": "Please enter Partner Name with no more than 1000 characters", "nhomDv": "Service group", "vuiLongChonNhomDv": "Please select service group", "loaiDoiTac": "Partner type", "chonLoaiDoiTac": "Select partner type", "vuiLongChonLoaiDoiTac": "Please select partner type", "loaiDichVu": "Type of service", "vuiLongChonLoaiDichVu": "Please select service type", "maSoThue": "Tax code", "timMaSoThue": "Find tax code", "nhapMaSoThue": "Enter tax code", "soTaiKhoan": "Account number", "timSoTaiKhoan": "Find account number", "nhapSoTaiKhoan": "Enter account number", "vuiLongNhapTenNguoiDaiDien": "Please enter representative name", "chucVuNguoiDaiDien": "Position of representative", "timChucVuNguoiDaiDien": "Find a representative position", "vuiLongNhapChucVuNguoiDaiDien": "Please enter representative position", "sdtNguoiDaiDien": "Phone number of representative", "timSdtNguoiDaiDien": "Find representative's phone number", "vuiLongNhapSdtNguoiDaiDien": "Please enter Phone number of representative", "nguoiDauMoi": "Contact person", "tenNguoiDauMoi": "Name of contact person", "timNguoiDauMoi": "Find a contact person", "vuiLongNhapTenNguoiDauMoi": "Please enter name of contact person", "sdtNguoiDauMoi": "Phone number of contact person", "timSdtNguoiDauMoi": "Find the contact person's phone number", "vuiLongNhapSdtNguoiDauMoi": "Please enter phone number of contact person", "emailNguoiDauMoi": "Email of contact person", "timEmailNguoiDauMoi": "Find the contact person's email", "vuiLongNhapEmailNguoiDauMoi": "Please enter email of contact person", "tenNganHang": "Bank name", "timTenNganHang": "Find the bank name", "vuiLongNhapTenNganHang": "Please enter bank name", "chuTaiKhoanNganHang": "Bank account holder", "timChuTaiKhoanNganHang": "Find the bank account holder", "vuiLongNhapChuTaiKhoanNganHang": "Please enter Bank Account Holder", "nguoiChiCongTac": "Intermediary", "timNguoiChiCongTac": "Find someone to collaborate with", "vuiLongNhapNguoiChiCongTac": "Please enter intermediary", "sdtNguoiChiCongTac": "Number of intermediary", "timSdtNguoiChiCongTac": "Find the phone number of the person to collaborate with", "vuiLongNhapSdtNguoiCongTac": "Please enter phone number of collaborator", "diaChi": "Address", "nhapDiaChi": "Enter address", "khongGuiTenDonVi": "Do not send unit name", "hanMucQuy": "Fund limit", "soTienDaSuDung": "Amount used", "vuiLongNhapSoTienHanMucQuy": "Please enter amount of Fund Limit", "khongDuocThucHienSlNhieuCungLuc": "Not allow execution of multiple services at the same time", "chonKhongDuocThucHienSlNhieuCungLuc": "Select Not allow execution of multiple services at the same time", "nhomBenhTat": "Disease group", "tatCaNhomBenhTat": "All disease groups", "chonNhomBenhTat": "Select disease group", "maNhom": "Group code", "timTheoMa": "Search by code", "tenNhom": "Group name", "timTheoTenNhom": "Search by group name", "tenChuong": "Chapter name", "chonTenChuong": "Select chapter name", "tatCaChuongBenh": "All disease chapters", "chonChuongBenh": "Select disease chapter", "chuongBenh": "Disease chapter", "nhomBenhChinh": "Main disease group", "nhomBenhPhuI": "Disease subgroup I", "nhomBenhPhuII": "Disease sub-group II", "loaiBenh": "Type of disease", "tenBenh": "Disease name", "vuiLongNhapMaNhom": "Please enter group code", "vuiLongNhapTenNhom": "Please enter group name", "vuiLongNhapMaNhomKhongQuaNumKyTu": "Please enter group code with no more than {{num}} characters!", "vuiLongNhapTenNhomKhongQuaNumKyTu": "Please enter group name with no more than {{num}} characters!", "vuiLongChonChuongBenh": "Please select disease chapter", "nguongTamUng": "Threshold of Advance Payment", "vuiLongNhapSttNhomBenh": "Please enter ordinal numbers of disease group", "sttNhomBenh": "Ordinal number of disease group", "chonNhomBenh": "Select disease group", "capNhatThanhCongDuLieuNhomBenhTat": "Successfully updated data of disease group!", "themMoiThanhCongDuLieuNhomBenhTat": "Successfully added new data of disease group!", "capNhatThanhCongDuLieuTuongTacThuoc": "Successfully updated drug interaction data!", "themMoiThanhCongDuLieuTuongTacThuoc": "Successfully added new drug interaction data!", "capNhatThanhCongDuLieuTaiNanThuongTich": "Successfully updated accident and injury data!", "themMoiThanhCongDuLieuTaiNanThuongTich": "Successfully added new injury accident data!", "batHuongBh": "Turn on insurance benefits", "tatHuongBh": "Turn off insurance benefits", "banCoChacChanBatXacNhanHuongBaoHiemKhong": "Are you sure you want to turn on insurance benefits?", "banCoChacChanTatXacNhanHuongBaoHiemKhong": "Are you sure you want to turn off insurance benefits?", "thongTinVatTu": "Information of Supplies", "xangDau": "Petroleum", "danhMucXangDau": "List of petroleum", "maXangDau": "Petroleum code", "vuiLongNhapMaXangDau": "Please enter petroleum code", "loaiXangDau": "Type of petroleum", "chonLoaiXangDau": "Select the type of petroleum", "vuiLongNhapLoaiXangDau": "Please enter petroleum type", "danhMucPhanLoaiPhuongPhapVoCam": "Category of classification of anesthesia method", "maPhanLoai": "Classification code", "tenPhanLoai": "Classification name", "vuiLongNhapMaPhanLoai": "Please enter classification code", "vuiLongNhapTenPhanLoai": "Please enter classification name", "phanLoaiPhuongPhapVoCam": "Classification of anesthesia method", "vuiLongNhapMaPhuongPhapVoCam": "Please enter the emotionless method code", "vuiLongNhapTenPhuongPhapVoCam": "Please enter the insensitive method name", "vuiLongChonPhanLoaiPhuongPhapVoCam": "Please select classification of anesthesia method", "nhapKetQuaThamhieu": "Enter reference results", "maChiSoCon": "Sub-indicator code", "timMaChiSoCon": "Search sub-indicator code", "nhapMaChiSoCon": "Enter sub-indicator code", "vuiLongNhapMaChiSoCon": "Please enter sub-indicator code", "vuiLongNhapMaChiSoConKhongQua20KyTu": "Please enter sub-indicator code with no more than 20 characters", "tenChiSoCon": "Sub-indicator name", "timTenChiSoCon": "Search sub-indicator name", "nhapTenChiSoCon": "Enter sub-indicator name", "vuiLongNhapTenChiSoCon": "Please enter sub-indicator name", "vuiLongNhapTenChiSoConKhongQua1000KyTu": "Please enter sub-indicator name with no more than 1000 characters", "timMaTuongDuong": "Search equivalenve code", "vuiLongNhapMaTuongDuong": "Please enter the equivalence code", "timTenDuongDuong": "Search equivalent name", "nhapTenDuongDuong": "Enter the equivalence name", "vuiLongNhapTenTuongDuong": "Please enter equivalence name", "maGuiLis": "Code for sending to LIS", "timMaGuiLis": "Search Code for sending to LIS", "nhapMaGuiLis": "Enter Code for sending to LIS", "vuiLongNhapMaGuiLis": "Please enter Code for sending to LIS", "taiNanThuongTich": "Injury", "chonLoaiKetQua": "Select result type", "ketQuaBinhThuong": "Normal results", "timKetQuaBinhThuong": "Search normal results", "nhapKetQuaBinhThuong": "Enter normal results", "nhomChiSoCon": "Sub-indicator group", "chonNhomChiSoCon": "Select groups of Sub-indicators", "vuiLongChonChiSoCon": "Please select subscript!", "donVi": "Unit", "timDonVi": "Search units", "nhapDonVi": "Enter units", "vuiLongNhapMaPhieu": "Please enter note code", "vuiLongNhapNoiDungPhieu": "Please enter note content", "vuiLongChonLoaiViTri": "Please select location", "vuiLongNhapSttPhieu": "Please enter note number", "vuiLongDienDayDuTt": "Please complete all information!", "phieuDayISC": "ISC deposit note", "chonPhieuDayISC": "Select ISC deposit note", "vuiLongNhapMaPttt": "Please input the surgical procedure code", "vuiLongNhapMaPtttKhongQua20KyTu": "Please enter a surgery procedure code no longer than 20 characters", "maDayBHYT": "Health insurance push code", "vuiLongNhapMaTaiNanThuongTichKhongQua20KyTu": "Please enter the accident and injury code with no more than 20 characters!", "vuiLongNhapTenTaiNanThuongTichKhongQua1000KyTu": "Please enter the name of the injury accident in no more than 1000 characters!", "vuiLongNhapMaDayBHYT": "Please enter health insurance push code!", "vuiLongNhapMaDayBHYTKhongQua20KyTu": "Please enter health insurance push code no more than 20 characters!", "maPttt": "Surgical procedure code", "tenPttt": "Name of Sugery and procedure", "vuiLongNhapTenPttt": "Please enter the name of the surgery/procedure", "vuiLongNhapTenPtttKhongQua1000KyTu": "Please enter the surgery/procedure code with no more than 1000 characters", "maGuiHoaDon": "Invoice sending code", "vuiLongNhapMucDoUuTien": "Please enter priority level", "loaiPhuongThucTt": "Type of Payment Method", "nccKhacBv": "NCC khác BV", "tienMat": "Cash", "vuiLongNhapUuTienNhoHon": "Please enter a preference less than {{num}}", "dichVuPttt": "Surgical and Procedural Services", "banCanPhaiXoaHetCacTuongTacThuocChiSoCon": "You need to clear all subindex drug interactions!", "benhYHocCoTruyen": "Traditional medicine diseases", "maBenhYHCY": "Traditional medicine disease code", "tenBenhYHCY": "Name of traditional medicine disease", "maBenhICD10": "ICD 10 disease code", "tenBenhICD10": "Name of disease (ICD-10)", "vuiLongNhapMaBenhYHCY": "Please enter the Traditional Medicine disease code", "vuiLongNhapTenBenhYHCY": "Please enter the name of the traditional medicine disease", "vuiLongChonMaBenhICD10": "Please select ICD 10 disease code", "giaNhapSauVATNhoNhat": "Minimum import price after VAT", "nhapGiaNhapSauVATNhoNhat": "Enter the smallest import price after VAT", "giaNhapSauVATLonNhat": "Highest import price after VAT", "nhapGiaNhapSauVATLonNhat": "Enter the largest import price after VAT", "nhapThangSoBanLe": "Enter the retail surplus", "vuiLongNhapThangSoBanLe": "Please enter retail surplus", "giaTriPhaiLaSoNguyenDuong": "The value must be a positive integer", "nhomThuocCap1": "Level 1 drug group", "maNhomThuocCap1": "Level 1 drug group code", "maNhomThuocCap2": "Level 2 drug group code", "vuiLongNhapMaNhomThuocCap1": "Please enter level 1 drug group code!", "vuiLongNhapMaNhomThuocCap2": "Please enter level 2 drug group code!", "vuiLongNhapTenNhomThuocCap1": "Please enter the name of level 1 drug group!", "vuiLongChonTenNhomThuocCap1": "Please select the name of level 1 drug group!", "vuiLongNhapTenNhomThuocCap2": "Please enter the name of level 2 drug group!", "timMaNhomThuocCap1": "Find level 1 drug group code", "tenNhomThuocCap1": "Name of class 1 drug group", "timTenNhomThuocCap1": "Find the name of level 1 drug group", "nhomThuocCap2": "Level 2 drug group", "tenNhomThuocCap2": "Name of level 2 drug group", "timTenNhomThuocCap2": "Find the name of level 2 drug group", "chonNhomThuocCap1": "Choose level 1 drug group", "soLanPerNgay": "Number of times/day", "timSoLanPerNgay": "Find the number of times/day", "soVienPerLan": "Number of pills/time", "timSoVienPerLan": "Find the number of pills/day", "danhMucLoaiNhiemKhuan": "List of infection types", "loaiNhiemKhuan": "Type of infection", "maLoaiNhiemKhuan": "Infection type code", "vuiLongNhapMaLoaiNhiemKhuan": "Please enter the infection type code", "tenLoaiNhiemKhuan": "Name of type of infection", "vuiLongNhapTenLoaiNhiemKhuan": "Please enter the name of the infection type", "logo": "Logos", "maBhyt": "Health insurance code", "tenBh": "Insurance name", "nhaThuTien": "House collects money", "giuongKeHoach": "Bed plans", "giuongThucKe": "Actual number of beds listed for patients", "tinhChatKhoa": "Faculty nature", "nguongTamUngDieuTri": "Treatment advance threshold", "duocPhepChonGiuongTaiKhoa": "Allowed to choose bed in the department", "tuDongDuyetMuonNb": "Automatically approve loan from patient ", "chuyenKhoaPttt": "Transfer to the Department of Plastic Surgery", "tiepNhanNoiTru": "Boarding reception", "khongThanhToanSau": "No later payments", "khongTuDongDuyetMuonNb": "Do not automatically approve loans from patient ", "tuDongChuyenKhoaPttt": "Self-transfer to the Department of Plastic Surgery", "khongChuyenKhoaPttt": "Do not transfer to the Department of Plastic Surgery", "coTiepNhan": "Have reception", "khongTiepNhan": "No reception", "maKhoa": "Department code", "vuiLongNhapMaKhoa": "Please enter department code!", "vuiLongNhapMaKhoaKhongQuaNumKyTu": "Please enter the department code no more than {{num}} characters!", "nhapMaKhoa": "Enter the department code", "tenKhoa": "Department Name", "nhapMaBhyt": "Enter health insurance code", "vuiLongNhapNhaThuTien": "Please enter the collection house!", "chonNhaThuTien": "Choose a house that collects money", "vuiLongChonNha": "Please choose a house!", "chonNha": "Choose a house", "nhapGiuongThucKe": "Enter the real bed", "nhapGiuongKeHoach": "Enter bed planning", "chonTinhChatKhoa": "Select faculty nature", "nhapNguongTamUngDieuTri": "Enter the treatment advance threshold", "duocPhepMuonTaiKhoa": "You are allowed to borrow beds at the department", "nhapMoTa": "Enter a description", "chonTrangThaiDuyetPhieuTra": "Select payment approval status", "duyetPhieuTra": "Approve the payment slip", "soLuongPhongMoKeHoach": "Number of planned operating rooms", "vuiLongNhapSoLuongPhongMoKeHoachKhongQua3KyTu": "Please enter the number of planned operating rooms in no more than 3 characters!", "nhapSoLuongPhongMoKeHoach": "Enter the number of planned operating rooms", "vuiLongNhapTenKhoa": "Please enter department name!", "vuiLongNhapTenKhoaKhongQua1000KyTu": "Please enter the department name no more than 1000 characters!", "nhapTenKhoa": "Enter department name", "vuiLongNhapTenVietTat": "Please enter an abbreviation", "vuiLongNhapMaBhyt": "Please enter health insurance code!", "nhapTenBh": "Enter insurance name", "tuyChinhGiaoDienPhanMem": "Customize software interface", "maLoaGoiSo": "Speaker code calling number", "vuiLongNhapMaLoaGoiSo": "Please enter the calling speaker code", "vuiLongNhapMaLoaGoiSoKhongQua20KyTu": "Please enter the speaker code with no more than 20 characters!", "tenLoaGoiSo": "Name of speaker calling number", "vuiLongNhapTenLoaGoiSo": "Please enter the name of the speaker calling number", "vuiLongNhapTenLoaGoiSoKhongQua1000KyTu": "Please enter speaker name calling number no more than 1000 characters!", "loaiTiepDon": "Kind of reception", "chonLoaiTiepDon": "Select reception type", "chonHinhThucPhatLoa": "Select speaker playback method", "loaiLoaGoSo": "Dialing speaker type", "baDaiHan": "Long-term medical record", "vuiLongNhapMaLoaiBenhAn": "Please enter the medical record type code", "vuiLongNhapTenLoaiBenhAn": "Please enter the name of the medical record type", "tenLoaiBenhAn": "Name of medical record type", "maLoaiBenhAn": "Medical record type code", "maLoaiDoiTuong": "Object type code", "vuiLongNhapMaLoaiDoiTuong": "Please enter the object type code", "vuiLongNhapMaLoaiDoiTuongKhongQua20KyTu": "Please enter the object type code no more than 20 characters!", "tenLoaiDoiTuong": "Object type name", "vuiLongNhapTenLoaiDoiTuong": "Please enter the object type name", "vuiLongNhapTenLoaiDoiTuongKhongQua1000KyTu": "Please enter object type name no more than 1000 characters!", "doiTuong": "Subject", "vuiLongChonDoiTuong": "Please select the subject", "chonDoiTuong": "Select subject", "loaiMienGiam": "Type of reduction/ exemption", "chonLoaiMienGiam": "Select reduction/ exemption type", "%MienGiam": "% Exemptions", "nhap%MienGiam": "Enter the exemption %.", "tienMienGiam": "Reduction/ Exemption money", "nhapTienMienGiam": "Enter reduction/exemption amount", "quanNhan": "Military", "yeuCauTamUngNgoaiTru": "Request an outpatient advance", "khamSucKhoe": "Health check", "vuiLongNhapGhiChuKhongQua1000KyTu": "Please enter a note no more than 1000 characters!", "nhapGhiChu": "Enter note", "timMaLoaiDoiTuong": "Find the object type code", "timTenLoaiDoiTuong": "Find the object type name", "chonYeuCauTamUngNgoaiTru": "Select request an outpatient advance", "chonKhamSucKhoe": "Choose health check", "chonQuanNhan": "Choose military personnel", "chonThanhToanSau": "Select pay later", "nguongMienGiam": "Exemption threshold", "timGhiChu": "Search note", "coQuanNhan": "There are soldiers", "khongQuanNhan": "No soldiers", "coYeuCau": "Request", "khongYeuCau": "Not required", "tyLeThanhToanBaoHiem": "Insurance payment rate", "maLoaiGiuong": "Bed type code", "tenLoaiGiuong": "Name of bed type", "vuiLongNhapTenLoaiGiuong": "Please enter bed type name!", "vuiLongNhapTenKhongQua255KyTu": "Please enter a name no longer than 255 characters", "vuiLongNhapTyLeThanhToanBaoHiem": "Please enter insurance payout rate", "lyDoHoan": "Reason of return", "coHoan": "Yes refundable", "khongHoan": "Not refundable", "hoanThanhToan": "Refund payment", "vuiLongNhapMaLyDoTamUng": "Please enter the advance reason code", "vuiLongNhapMaLyDoTamUngKhongQua20KyTu": "Please enter the advance reason code no more than 20 characters!", "tenLyDoTamUng": "Name of reason for advance payment", "vuiLongNhapTenLyDoTamUng": "Please enter the name of the reason for the advance", "vuiLongNhapTenLyDoTamUngKhongQua1000KyTu": "Please enter the name of the reason for the advance payment in no more than 1000 characters", "maLyDoTamUng": "Advance reason code", "maMayGuiBHYT": "Machine code sent to health insurance", "timMaMayGuiBHYT": "Find the health insurance sender code", "vuiLongNhapMaMay": "Please enter the machine code", "vuiLongNhapMaMayKhongQua200KyTu": "Please enter the machine code no more than 200 characters", "vuiLongNhapTenMaMay": "Please enter the machine code name", "vuiLongNhapMaMayGuiBHYT": "Please enter the health insurance sender code", "tenMaMay": "Machine code name", "sttChuong": "Chapter number", "timTheoStt": "Search by status", "maChuong": "Code", "timTheoTen": "Search by name", "vuiLongNhapSttChuong": "Please enter chapter number", "nhapSttChuong": "Enter chapter status", "vuiLongNhapMaChuong": "Please enter chapter code", "vuiLongNhapMaChuongKhongQua20KyTu": "Please enter chapter code no more than 20 characters", "vuiLongNhapTenChuong": "Please enter chapter name", "vuiLongNhapTenChuongKhongQua1000KyTu": "Please enter chapter name no more than 1000 characters", "maNhomPttt": "Surgical and Procedural Group Code", "vuiLongNhapMaNhomPttt": "Please enter the surgery and procedure group code", "vuiLongNhapMaNhomPtttKhongQua20KyTu": "Please enter a surgery and procedure group code no longer than 20 characters", "vuiLongNhapTenNhomPttt": "Please enter the surgery and procedure group name", "vuiLongNhapTenNhomPtttKhongQua1000KyTu": "Please enter a surgery and procedure group name no longer than 1000 characters", "chuongPttt": "Surgery and procedure chapter", "chonChuongPttt": "Select surgery and procedure chapter", "maLoaiPttt": "Surgery and procedure type code", "vuiLongNhapMaLoaiPttt": "Please enter the surgery and procedure type code!", "vuiLongNhapMaLoaiPtttKhongQua20KyTu": "Please enter a surgery and procedure type code no longer than 20 characters!", "tenLoaiPttt": "Surgery and procedure type name", "vuiLongNhapTenLoaiPttt": "Please enter the surgery and procedure type name", "vuiLongNhapTenLoaiPtttKhongQua1000KyTu": "Please enter a surgery and procedure type name no longer than 1000 characters", "chonNhomPttt": "Select surgery and procedure group", "nhomPttt": "Surgery and procedure group", "chonLoaiPttt": "Select surgery and procedure type", "tenNhomPttt": "Surgery and procedure group name", "tatCaChuongPttt": "All surgery and procedure chapters", "tatCaNhomPttt": "All surgery and procedure groups", "tatCaLoaiPttt": "All surgery and procedure types", "chonNhomLoaiPttt": "Select surgery and procedure group type", "dienBien": "Progress", "maMauDienBien": "Sample code evolution", "vuiLongMaMauDienBien": "Please enter the evolution sample code", "vuiLongNhapMaMauDienBienKhongQua20KyTu": "Please enter the evolution sample code no more than 20 characters", "tenMauDienBien": "Evolution sample name", "vuiLongNhapTenMauDienBien": "Please enter the name of the evolution template", "vuiLongNhapTenMauDienBienKhongQua1000KyTu": "Please enter a sample name with no more than 1000 characters", "vuiLongNhapDienBien": "Please enter developments", "vuiLongChonPhong": "Please select a room", "vuiLongChonBacSi": "Please choose a doctor", "sttHienThi": "Status displayed", "nhapSttHienThi": "Enter display order number", "vuiLongNhapMaVatTu": "Please enter the material code", "vuiLongNhapMaVatTuKhongQua20KyTu": "Please enter the material code no more than 20 characters", "vuiLongNhapTenVatTu": "Please enter material name", "vuiLongNhapTenVatTuKhongQua1000KyTu": "Please enter the material name no more than 1000 characters", "chonNhomVatTu": "Select material group", "maKyHieuTenThuongMai": "Symbol code - Trade name", "nhapMaKyHieu": "Enter the symbol code", "thongSoKyThuat": "Specifications", "nhapThongSoKyThuat": "Enter specifications", "nhapGiaNhap": "Enter the entry price", "nhapGia": "Enter price", "vuiLongChonDichVuCap1": "Please select level 1 service", "chonNhomDichVuCap1": "Select service group level 1", "vuiLongChonDichVuCap2": "Please select level 2 service", "chonNhomDichVuCap3": "Select service group level 3", "chonNguonChiTraKhac": "Choose another payment source", "vatTuBo": "Kit supplies", "vatTuTheoKichCo": "Supplies by size", "stentPhuThuoc": "Drug-eluting stents", "kyThuatCao": "High tech", "vatTuTaiSuDung": "Reusable supplies", "vatTuChayMay": "Machine consumables", "giaKhongBaoHiem": "Price without insurance", "giaBaoHiem": "Insured price", "giaPhuThu": "Surcharge price", "maBaoCao": "Report code", "timMaBaoCao": "Find the report code", "timTenBaoCao": "Find the report name", "khoGiay": "Paper Size", "chonKhoGiay": "Select paper size", "kichThuocChieuDoc": "Vertical size", "timTheoKichThuocChieuDoc": "Search by vertical size", "kichThuocChieuNgang": "Horizontal size", "timTheoKichThuocChieuNgang": "Search by horizontal size", "huongGiay": "Paper orientation", "chonHuongGiay": "Select Paper Orientation", "dinhDangXuatFile": "File export format", "chonDinhDang": "Choose format", "loaiIn": "Print type", "chonLoaiIn": "Select print type", "chonKySo": "Select digital signature", "thietLapChanKy": "setting a signature", "vuiLongNhapMaBaoCao": "Please enter report code!", "vuiLongChonMaBaoCaoKhongQuaNumKyTu": "Please enter the report code no more than {{num}} characters!", "vuiLongChonMaBaoCao": "Please select a report code!", "xemSuaBieuMau": "View/Edit form", "taiLenMauBaoCao": "Upload report template", "vuiLongNhapTenBaoCao": "Please enter a report name", "vuiLongNhapTenBaoCaoKhongQuaNumKyTu": "Please enter a report name no more than {{num}} characters!", "loaiBieuMau": "Form type", "vuiLongChonLoaiBieuMau": "Please select a form type", "vuiLongNhapKichThuocChieuDoc": "Please enter vertical dimensions", "vuiLongNhapKichThuocChieuDocKhongQuaNumKyTu": "Please enter a vertical size of no more than {{num}} characters!", "vuiLongNhapKichThuocNgang": "Please enter the horizontal size", "vuiLongNhapKichThuocNgangKhongQuaNumKyTu": "Please enter a horizontal size of no more than {{num}} characters!", "choDinhDangXuatFile": "Choose the file export format", "vuiLongNhapTenLoaiPhieu": "Please enter the name of the voucher type", "vuiLongNhapTenLoaiPhieuKhongQua1000KyTu": "Please enter the voucher type name with no more than 1000 characters", "vuiLongChonLoaiIn": "Please select print type", "khuVucKy": "Signing area", "vuiLongChonKhuVucKy": "Please select a signing area", "soCapKy": "Registration number", "vuiLongNhapSoCapKy": "Please enter registration number", "loaiKy": "Sign type", "quyenKy": "Signing authority", "tenChanKy": "Signature name", "capKyNum": "Sign level {{num}}", "chonQuyenKy": "Select signing authority", "nhapTenChanKy": "Enter signature name", "maXuatXu": "Origin code", "tenXuatXu": "Name of origin", "vuiLongNhapMaXuatXu": "Please enter the origin code", "vuiLongNhapTenXuatXu": "Please enter the name of origin", "vuiLongNhapMaLieuDung": "Please enter dosage code!", "maLieuDung": "Dosage code", "timMaLieuDung": "Find the dosage code", "timTenLieuDung": "Find the dosage name", "soLuongSang": "Bright quantity", "timSoLuongSang": "Find the amount of light", "soLuongChieu": "Afternoon quantity", "timSoLuongChieu": "Find the number of dimensions", "soLuongToi": "Dark quantity", "timSoLuongToi": "Find the dark quantity", "soLuongDem": "Night quantity", "timSoLuongDem": "Find the number of nights", "thoiDiemDung": "Timing of use", "timThoiDiemDung": "Find when to use it", "tatCa": "All", "chonInNhanh": "Select quick print", "danhSachThuoc": "List of medications", "soLuongDungSang": "Morning dosage", "chiDuocPhepNhapSoNguyenHoacPhanSo": "Only integers or fractions are allowed (Integer / Integer)", "thieuThongTinDuongDung": "Missing route of administration", "tenLieuDung": "Dosage name", "vuiLongNhapTenLieuDung": "Please enter dosage", "soLuongDungChieu": "Afternoon dosage", "vuiLongNhapSoLuongDungChieu": "Enter afternoon dosage", "soLuongDungToi": "Evening dosage", "vuiLongNhapSoLuongDungToi": "Enter evening dosage", "soLuongDungDem": "Night dosage", "vuiLongNhapSoLuongDungDem": "Enter night dosage", "nhapThoiDiemDung": "Enter time of use", "vuiLongNhapThoiDiemDung": "Please enter time of use", "vuiLongNhapSoLuongDungSang": "Enter morning dosage", "vuiLongChonDuongDung": "Please select route of administration", "vuiLongNhapGhiChu": "Please enter a note", "bacSiChiDinh": "Indicated by doctor", "vuiLongChonBacSiChiDinh": "Please select the prescribing doctor", "maLoaiPhieu": "Coupon type code", "tenLoaiPhieu": "Name of ticket type", "vuiLongNhapMaLoaiPhieu": "Please enter voucher type code", "vuiLongNhapMaLoaiPhieuKhongQua20KyTu": "Please enter the voucher type code with no more than 20 characters", "maLoiDan": "Code of instructions", "vuiLongNhapMaLoiDan": "Please enter the prompt code", "tenLoiDan": "Name of advice", "vuiLongNhapTenLoiDan": "Please enter a warning name", "vuiLongNhapTenLoiDanKhongQua1000KyTu": "Please enter a warning name no more than 1000 characters", "danhChoThuoc": "For medicine", "vuiLongNhapMaNguonNhap": "Please enter the import source code", "vuiLongNhapMaNguonNhapKhongQua50KyTu": "Please enter source code no more than 50 characters", "tenNguonNhap": "Input source name", "vuiLongNhapTenNguonNhap": "Please enter the input source name", "vuiLongNhapTenNguonNhapKhongQua1000KyTu": "Please enter the input source name no more than 1000 characters", "thau": "Contractor", "khongThau": "No bid", "maNguonNhap": "Imported source code", "coThau": "There are bids", "tenChiSoSong": "Alive index name", "vuiLongNhapTenChiSoSong": "Please enter the live index name", "vuiLongNhapTenChiSoSongKhongQua1000KyTu": "Please enter a live index name no more than 1000 characters", "vuiLongNhapDonVi": "Please enter units", "giaTriToiThieu": "Minimum value", "vuiLongNhapGiaTri": "Please enter value", "giaTriToiDa": "Maximum value", "khoaApDung": "Faculty of application", "maChiSoSong": "Live index code", "nhapMaChiSoSong": "Enter the vital index code", "maNhomChiPhi": "Cost group code", "vuiLongNhapMaNhomChiPhi": "Please enter the cost group code", "vuiLongNhapMaNhomChiPhiKhongQua20KyTu": "Please enter the cost group code with no more than 20 characters", "tenNhomChiPhi": "Cost group name", "vuiLongNhapTenNhomChiPhi": "Please enter a cost group", "vuiLongNhapTenNhomChiPhiKhongQua1000KyTu": "Please enter a cost group no more than 1000 characters", "nhomDonViTinh": "Unit group", "nhapNhomDonViTinh": "Enter the unit group", "maNhomDonViTinh": "Unit group code", "vuiLongNhapMaNhomDonViTinh": "Please enter the unit group code", "maPhaiCoItNhatMotKyTuLaChu": "The code must have at least one letter character", "nhapMaNhomDonViTinh": "Enter the unit group code", "tenNhomDonViTinh": "Name of unit group", "chonTenNhomDonViTinh": "Select the name of the unit group", "vuiLongNhapTenNhomDonViTinh": "Please enter the unit group name", "vuiLongNhapMaDonViTinh": "Please enter the unit code", "nhapTenNhomDonViTinh": "Enter the unit group name", "maDonViTinh": "Unit code", "nhapMaDonViTinh": "Enter the unit code", "tenDonViTinh": "Unit of measurement", "nhapTenDonViTinh": "Enter the unit name", "vuiLongNhapTenDonViTinh": "Please enter the unit name", "chonNhomDonViTinh": "Select a unit group", "maDotBien": "Mutation code", "vuiLongNhapMaDotBien": "Please enter the mutation code", "danhPhap": "Nomenclature", "nhapDanhPhap": "Enter nomenclature", "tenDotBien": "Mutant name", "vuiLongNhapTenDotBien": "Please enter a mutation name", "yNghiaDotBien": "Meaning of mutation", "nhapYNghiaDotBien": "Enter the mutation meaning", "dotBien": "Mutation", "danhMucMauKQXNCoDotBien": "List of laboratory samples with mutations", "dacTinh": "characteristic", "maDanhGia": "Evaluation code", "vuiLongNhapMaDanhGia": "Please enter review code", "vuiLongNhapMaDanhGiaKhongQua20KyTu": "Please enter the review code no more than 20 characters", "nhapMaDanhGia": "Enter the review code", "tenDanhGia": "Review name", "vuiLongNhapTenDanhGia": "Please enter a review name", "nhapTenDanhGia": "Enter a review name", "giaTriNhoNhat": "Smallest value", "nhapGiaTri": "Enter value", "giaTriLonNhat": "Greatest value", "giaTriLonNHatPhaiLonHonGiaTriNhoNhat": "The maximum value must be greater than the minimum value", "danhGia": "Evaluate", "vuiLongNhapTenDanhGiaKhongQua1000KyTu": "Please enter a review name no more than 1000 characters", "danhMucDichVuChung": "List of general services", "maGoi": "package code", "timMaGoi": "Find the package code", "tenGoi": "Package name", "donGiaKhongBaoHiem": "Unit price without insurance", "maDichVuHangHoa": "Service and goods codes", "timTheoMaDichVuHangHoa": "Search by service and product code", "tenDichVuHangHoa": "Name of service or goods", "timTheoTenDichVuHangHoa": "Search by service or product name", "timNhomDvCap1": "Find level 1 service group", "donGiaBH": "Insured unit price", "donGiaKhongBH": "Uninsured unit price", "vuiLongNhapMaGoi": "Please enter the package code", "vuiLongNhapMaGoiKhongQua20KyTu": "Please enter the package code no more than 20 characters", "vuiLongNhapMaGoiCap1": "Please enter level 1 package code", "vuiLongNhapTenGoi": "Please enter package name", "vuiLongNhapTenGoiKhongQua1000KyTu": "Please enter a package name no more than 1000 characters", "vuiLongNhapDonGiaKhongBH": "Please enter unit price without insurance", "vuiLongChonPhongThucHien": "Please select the Performance Room", "chonPhongThucHien": "Please select the Performance Room", "maMayTinh": "Computer code", "timMaMayTinh": "Find computer code", "tenMayIn": "Printer name", "timTenMayIn": "Find the printer name", "diaChiIp": "IP address", "timTheoDiaChiIp": "Search by IP address", "capNhatThanhCongDuLieuMayIn": "Successfully updated printer data!", "vuiLongChonKhoGiay": "Please select paper size!", "vuiLongChonHuongGiay": "Please select paper orientation", "capNhatThanhCongDuLieuThietLapChanKy": "Successfully updated signature setup data!", "themMoiThanhCongDuLieuThietLapChanKy": "Successfully added new signature setup data!", "themMoiThanhCongDuLieuBaoCao": "Successfully added new report data!", "capNhatThanhCongDuLieuBaoCao": "Successfully updated report data!", "capNhatThanhCongDuLieuXuatXu": "Successfully updated origin data!", "themMoiThanhCongDuLieuXuatXu": "Successfully added new origin data!", "xoaBanGhiThanhCong": "Deleted record successfully", "maNhomChiSo": "Index group code", "nhapMaNhomChiSo": "Enter the metric group code", "vuiLongNhapMaNhomChiSo": "Please enter the metric group code", "tenNhomChiSo": "Index group name", "nhapTenNhomChiSo": "Enter the metric group name", "vuiLongNhapTenNhomChiSo": "Please enter the index group name", "vaiTroHeThong": "system role", "maVaiTro": "Role code", "vuiLongNhapMaVaiTro": "Please enter role code", "vuiLongNhapMaVaiTroKhongQua50KyTu": "Please enter role code no more than 50 characters!", "vuiLongChonQuyen": "Please choose the right!", "quyenDuocGan": "Assigned permissions", "chonNhomTinhNang": "Select feature group", "timKiemQuyen": "Search permissions", "capNhatThanhCongDuLieuVaiTroHeThong": "Successfully updated system role data!", "themMoiThanhCongDuLieuVaiTroHeThong": "Successfully added new system role data!", "vuiLongNhapKetQuaThamChieu": "Please enter reference results", "themMoiThanhCongDuLieuLoaiBenhAn": "Successfully added new medical record data!", "capNhatThanhCongDuLieuKhoa": "Successfully updated faculty data!", "themMoiThanhCongDuLieuKhoa": "Successfully added new faculty data!", "capNhatKhongThanhCongDuLieuKhoa": "Failed to update faculty data!", "themMoiKhongThanhCongDuLieuKhoa": "Failed to add new faculty data!", "dongBoThanhCong": "Synchronization successful", "themMoiThanhCongDuLieuThietLapChung": "Successfully added new common setup data!", "uploadVideoThanhCong": "Upload video successfully", "khongTimThayThongTinBenhNhan": "Patient information not found", "capNhatThanhCongDuLieuLoaGoiSo": "Successfully updated call speaker data!", "themMoiThanhCongDuLieuLoaGoiSo": "Successfully added new speaker call data!", "capNhatThanhCongDuLieuLoaiDoiTuong": "Successfully updated object type data!", "themMoiThanhCongDuLieuLoaiDoiTuong": "Successfully added new data object type!", "capNhatDuLieuThanhCong": "Data update successful!", "themMoiDuLieuThanhCong": "Added new data successfully!", "capNhatThanhCongDuLieuMaMay": "Successfully updated machine code data!", "themMoiThanhCongDuLieuMaMay": "Successfully added new machine code data!", "capNhatThanhCongDuLieuChuongPttt": "Successfully updated surgery and procedure chapter data!", "themMoiThanhCongDuLieuChuongPttt": "Successfully added new surgery and procedure chapter data!", "capNhatThanhCongDuLieuNhomPttt": "Successfully updated surgery and procedure group data!", "themMoiThanhCongDuLieuNhomPttt": "Successfully added new surgery and procedure group data!", "capNhatThanhCongDuLieuLoaiBenh": "Successfully updated disease type data!", "themMoiThanhCongDuLieuLoaiBenh": "Successfully added new disease type data!", "capNhatThanhCongDuLieuTenPttt": "Successfully updated surgery and procedure name data!", "themMoiThanhCongDuLieuTenPttt": "Successfully added new surgery and procedure name data!", "doiTuongKCB": "Subject for examination and treatment", "nhaChiDinh": "Designated house", "chonNhaChiDinh": "Choose a designated home", "phongLayMau": "Sample collection room", "chonPhongLayMau": "Select the sampling room", "diaDiem": "Location", "vuiLongNhapKhoaChiDinh": "Please enter the designated department", "chonKhoaChiDinh": "Please select the Indicating Department", "nhapNhomDVCap1": "Enter level 1 service group", "vuiLongNhapNhomChiDinhCap1": "Please enter level 1 designation group", "vuiLongChonPhongLayMau": "Please select the sampling room", "nhapDiaDiem": "Enter location", "slHangDoi": "Queue quantity", "nhapSlHangDoi": "Enter queue quantity", "maChucVu": "Position code", "vuiLongNhapMaChucVu": "Please enter position code", "nhapMaChucVu": "Enter the position code", "tenChucVu": "Position name", "nhapTenChucVu": "Enter position name", "vuiLongNhapTenChucVu": "Please enter position name", "maQuyenKy": "Signing authority code", "timMaQuyenKy": "Find the signing authority code", "tenQuyenKy": "Name of signing authority", "timTenQuyenKy": "Find the signing authority name", "vuiLongNhapMaQuyenKy": "Please enter the signing authority code", "vuiLongNhapTenQuyenKy": "Please enter the signing authority name", "maBenhPham": "Specimen code", "tenBenhPham": "Specimen name", "vuiLongNhapMaBenhPham": "Please enter the patient code", "vuiLongNhapTenBenhPham": "Please enter the specimen name", "benhPham": "Specimen", "capNhatThanhCongDuLieuBenhPham": "Successfully updated patient data!", "themMoiThanhCongDuLieuBenhPham": "Successfully added new patient data!", "yeuCauDotDung": "Request batch", "PLThuoc": "drug classification", "vuiLongNhapMaPhanLoaiThuoc": "Please enter the drug classification code", "vuiLongNhapMaPhanLoaiThuocKhongQua20KyTu": "Please enter the drug classification code with no more than 20 characters", "vuiLongNhapTenPhanLoaiThuoc": "Please enter the drug classification name", "vuiLongNhapTenPhanLoaiThuocKhongQua1000KyTu": "Please enter the drug classification name with no more than 1000 characters", "chonLoaiDonThuoc": "Select the prescription type", "vuiLongNhapLabelTitleKhongQuaCountKyTu": "Please enter {{ label }} {{ title }} with no more than {{ count }} characters", "VuiLongNhapTenTitle": "Please enter name {{ title }}", "VuiLongNhapMaTitle": "Please enter code {{ title }}", "themMoiThanhCongDuLieuTitle": "Successfully added new data {{ title }}!", "themMoiKhongThanhCongDuLieuTitle": "Failed to add new data {{ title }}!", "capNhatThanhCongDuLieuTitle": "Successfully updated data {{ title }}!", "capNhatKhongThanhCongDuLieuTitle": "Failed to update data {{ title }}!", "maBoChiDinh": "Specifier code", "timMaBoChiDinh": "Find the designator code", "tenBoChiDinh": "Specifier name", "timTenBoChiDinh": "Find the designator name", "chonLoaiDichVu": "Select service type", "taiKhoanBoChiDinh": "The account specifies the set", "chonTaiKhoanChiDinhBo": "Select the account that specifies the set", "boThuocKeNgoai": "Over-the-counter medicine set", "thongTinBoChiDinh": "Specifier information", "vuiLongNhapTenBoChiDinh": "Please enter the designator name", "vuiLongNhapTenBoChiDinhKhongQua1000KyTu": "Please enter a designator name no more than 1000 characters!", "vuiLongChonKeDichVu": "Please select service list", "chonHopKeDv": "Select service settlement", "coHanChe": "There are limitations", "khongHanChe": "Unlimited", "goiPhauThuatThuThuat": "Surgical procedure package", "themMoiThanhCongDuLieuGoiDichVu": "Successfully added new service pack data!", "capNhatThanhCongDuLieuGoiDichVu": "Successfully updated service pack data!", "themMoiThanhCongDuLieuChiTietGoiDichVu": "Successfully added new service package details data!", "capNhatThanhCongDuLieuChiTietGoiDichVu": "Successfully updated service package details!", "lichSuThayDoiThongTin": "Information change history", "doiTacCon": "Child partner", "capNhatThanhCongDuLieuDoiTac": "Successfully updated partner data!", "themMoiThanhCongDuLieuDoiTac": "Successfully added new partner data!", "capNhatThanhCongDuLieuDoiTacCon": "Successfully updated child partner data!", "themMoiThanhCongDuLieuDoiTacCon": "Successfully added new child partner data!", "maChuyenKhoa": "Specialty code", "vuiLongNhapMaChuyenKhoa": "Please enter the specialty code", "vuiLongNhapMaChuyenKhoaKhongQuaNumKyTu": "Please enter the specialty code with no more than {{num}} characters!", "tenChuyenKhoa": "Name of specialty", "vuiLongNhapTenChuyenKhoa": "Please enter the name of the specialty", "vuiLongNhapTenChuyenKhoaKhongQuaNumKyTu": "Please enter a specialty name with no more than {{num}} characters!", "capNhatThanhCongDuLieuChuyenKhoa": "Successfully updated specialty data!", "themMoiThanhCongDuLieuChuyenKhoa": "Successfully added new specialist data!", "vuiLongNhapMaDuongDung": "Please enter the route code", "vuiLongNhapMaDuongDungKhongQua20KyTu": "Please enter the route code no more than 20 characters!", "tenDuongDung": "Route name used", "vuiLongNhapTenDuongDung": "Please enter the route name", "vuiLongNhapTenDuongDungKhongQua1000KyTu": "Please enter a username no more than 1000 characters!", "nhapThuTuHienThi": "Enter display order", "vuiLongNhapDonGiaBh": "Please enter insurance price", "vuiLongNhapKhongBh": "Please enter no insurance", "vuiLongNhapPhuThu": "Please enter Surcharge", "vuiLongChonDvt": "Please select a unit of measurement", "vuiLongChonNhomDvCapNum": "Please select service group level {{num}}", "nhomDvCapNum": "Service Group Level {{num}}", "timMaNhomThuocCap2": "Find level 2 drug group code", "timTenMau": "Find the model name", "timKetLuan": "Find Conclusion", "timKetQua": "Search result", "viThe": "Microscopic exam", "timViThe": "Find the body", "daiThe": "Gross description", "timDaiThe": "Find the general", "tenXetNghiem": "Test name", "timTenXetNghiem": "Find the test name", "vuiLongNhapMaKhongQuaNumKyTu": "Please enter code no more than {{num}} characters!", "vuiLongNhapTenMau": "Please enter a model name", "vuiLongNhapTenMauKhongQuaNumKyTu": "Please enter a model name no more than {{num}} characters!", "vuiLongNhapKetLuan": "Please enter conclusion!", "vuiLongNhapKetQua": "Please enter results", "vuiLongNhapViThe": "Please enter position", "vuiLongNhapDaiThe": "Please enter roughly", "vuiLongChonTenXetNghiem": "Please select test name", "chiTietMauKetQua": "Sample results details", "capNhatThanhCongDuLieuMauKqXn": "Successfully updated test result sample data!", "themMoiThanhCongDuLieuMauKqXn": "Successfully added new test result sample data!", "capNhatThanhCongDuLieuPhieuIn": "Successfully updated printed ticket data!", "themMoiThanhCongDuLieuPhieuIn": "Successfully added new print ticket data!", "capNhatThanhCongDuLieuViTriPhieuIn": "Successfully updated print ticket location data!", "themMoiThanhCongDuLieuViTriPhieuIn": "Successfully added new print ticket location data!", "capNhatThanhCongDuLieuManHinhPhieuIn": "Successfully updated print ticket screen data!", "themMoiThanhCongDuLieuManHinhPhieuIn": "Successfully added new print ticket screen data!", "vuiLongChonMaViTri": "Please enter the location code", "vuiLongNhapNoiDungViTri": "Please enter location content", "vuiLongChonManHinh": "Please select screen", "vuiLongNhapMaManHinhPhieuIn": "Please enter the code on the print ticket screen", "vuiLongNhapMaManHinhPhieuInKhongQuaNumKyTu": "Please enter the printed coupon screen code no more than {{num}} characters!", "vuiLongNhapTenManHinhPhieuIn": "Please enter the print ticket screen name", "vuiLongNhapTenManHinhPhieuInKhongQuaNumKyTu": "Please enter the print ticket screen name no more than {{num}} characters!", "mauKetQuaPttt": "Surgery and Procedure result template", "maMauKetQuaPttt": "Surgery and Procedure result template code", "vuiLongNhapMaMauKetQuaPttt": "Please enter the Surgery and Procedure result template code", "vuiLongNhapMaMauKetQuaPtttKhongQua20KyTu": "Please enter a Surgery and Procedure result template code no longer than 20 characters", "tenMauKetQuaPttt": "Surgery and Procedure result template name", "vuiLongNhapTenMauKetQuaPttt": "Please enter the Surgery and Procedure result template name", "vuiLongNhapTenMauKetQuaPtttKhongQua1000KyTu": "Please enter a Surgery and Procedure result template name no longer than 1000 characters", "vuiLongChonPhuongPhap": "Please select a method", "chanDoanSauPttt": "Diagnosis after surgery and procedure", "vuiLongNhapChanDoan": "Please enter the diagnosis", "phuongPhapPttt": "Surgery and Procedure method", "vuiLongNhapPhuongPhap": "Please enter the method", "cachThucPttt": "Surgery and Procedure approach", "vuiLongNhapCachThuc": "Please enter method", "danhMucMauKetQuaPhauThuatThuThuat": "Sample list of surgery and procedure results", "tinhtp": "Province/City", "quanhuyen": "District (Town)", "xaphuong": "Commune, Ward", "timQuocGia": "Find country", "timTinhtp": "Find province/city", "timQuanhuyen": "Find District/District", "timXaphuong": "Find commune/ward", "maXaphuong": "Commune/ward code", "timMaXaphuong": "Find commune/ward code", "tenXaphuong": "Name of commune/ward", "timTenXaphuong": "Find commune/ward name", "timTenVietTat": "Find the abbreviation", "maQuanhuyen": "District code", "vuiLongNhapMaQuanhuyen": "Please enter district code!", "vuiLongNhapMaQuanhuyenKhongQua20KyTu": "Please enter district code no more than 20 characters!", "tenQuanhuyen": "Name of district/district", "vuiLongNhapTenQuanhuyen": "Please enter district name!", "vuiLongNhapTenQuanhuyenKhongQua1000KyTu": "Please enter district name no more than 1000 characters!", "vuiLongNhapQuanhuyen": "Please enter district/district", "vuiLongNhapTenVietTatKhongQua2KyTu": "Please enter an abbreviation of no more than 2 characters!", "vuiLongNhapChonTinhtp": "Please enter province/city!", "chonTinhtp": "Select province/city", "vuiLongNhapChonQuocGia": "Please enter your country!", "chonQuocGia": "Select country", "tatCaQuocGia": "All Countries", "tatCaTinh": "All Provinces", "chonTinh": "Select province", "maTinhtp": "City/province code", "vuiLongNhapMaTinhtp": "Please enter province/city code!", "vuiLongNhapMaTinhtpKhongQua20KyTu": "Please enter the province/city code with no more than 20 characters!", "tenTinhtp": "Name of province/city", "vuiLongNhapTenTinhtp": "Please enter province/city name!", "vuiLongNhapTenTinhtpKhongQua1000KyTu": "Please enter province/city name no more than 1000 characters!", "vuiLongNhapTinhtp": "Please enter province/city", "maTinh": "Province code", "tenTinh": "Province name", "vuiLongNhapMaXaphuong": "Please enter commune/ward code!", "vuiLongNhapMaXaphuongKhongQua20KyTu": "Please enter the commune/ward code with no more than 20 characters!", "vuiLongNhapTenXaphuong": "Please enter commune/ward name!", "vuiLongNhapTenXaphuongKhongQua1000KyTu": "Please enter commune/ward name no more than 1000 characters!", "vuiLongNhapXaphuong": "Please enter commune/ward", "vuiLongNhapChonQuanhuyen": "Please enter your district!", "chonQuanhuyen": "Select district", "tatCaHuyen": "All Districts", "maPhong": "Room code", "timMaPhong": "Find the room code", "timTenPhong": "Find the room name", "loaiPhong": "Kind of room", "chonLoaiPhong": "Select room type", "timTenKhoa": "Find the department name", "timTenChuyenKhoa": "Find the name of the specialty", "timDiaDiem": "Find locations", "timTenNha": "Find house name", "thoiGianThucHienTrungBinh": "Average execution time", "timThoiGianThucHienTrungBinh": "Find the average execution time", "chonNoiTru": "Choose boarding", "noiTru": "Inpatient", "chonNgoaiTru": "Choose outpatient", "ngoaiTru": "Outpatient", "coMacDinh": "Yes default", "khongMacDinh": "No default", "chonMacDinh": "Choose default", "checkInVaoQmsPhong": "Check in to QMS room", "ngoaiVien": "External (out-of-hospital)", "vuiLongNhapMaPhong": "Please enter room code", "vuiLongNhapMaPhongKhongQuaNumKyTu": "Please enter the room code no more than {{num}} characters!", "vuiLongNhapTenPhong": "Please enter room name", "vuiLongNhapTenPhongKhongQuaNumKyTu": "Please enter room name no more than {{num}} characters!", "vuiLongChonLoaiPhong": "Please select room type", "vuiLongNhapDiaDiem": "Please enter a location", "vuiLongNhapThoiGianThucHienTrungBinh": "Please enter the average execution time", "capNhatThanhCongDuLieuPhong": "Successfully updated room data!", "themMoiThanhCongDuLieuPhong": "Successfully added new room data!", "donViYTe": "Medical unit", "chiNhanh": "Branch", "maChiNhanh": "Branch code", "vuiLongNhapMaChiNhanh": "Please enter branch code!", "vuiLongNhapMaChiNhanhKhongQua20KyTu": "Please enter branch code no more than 20 characters!", "tenChiNhanh": "Branch name", "vuiLongNhapTenChiNhanh": "Please enter branch name!", "maDonViYTe": "Medical unit code", "vuiLongChonNguonNguoiBenh": "Please select patient source!", "chonDonViYTe": "Select medical unit", "laCoQuanChuQuan": "Is the governing body", "timMaChiNhanh": "Find branch code", "timTenChiNhanh": "Find branch name", "vuiLongNhapMaDonViYTe": "Please enter the medical unit code!", "vuiLongNhapMaDonViYTeKhongQua20KyTu": "Please enter the medical unit code no more than 20 characters!", "tenDonViYTe": "Name of medical unit", "vuiLongNhapTenDonViYTe": "Please enter the name of the medical unit!", "timMaDonViYTe": "Find the medical unit code", "timTenDonViYTe": "Find the name of the medical unit", "maHoaChat": "Chemical code", "vuiLongNhapMaHoaChat": "Please enter chemical code", "vuiLongNhapMaHoaChatKhongQua20KyTu": "Please enter chemical code no more than 20 characters!", "timMaHoaChat": "Find the chemical code", "timTenHoaChat": "Find the chemical name", "vuiLongNhapTenHoaChat": "Please enter chemical name", "vuiLongNhapTenHoaChatKhongQua1000KyTu": "Please enter chemical name no more than 1000 characters!", "timTenDonViTinh": "Find the unit name", "timNhomHoaChat": "Find chemical groups", "timNuocSanXuat": "Search the country of manufacture", "timNhaSanXuat": "Search manufacturer", "timNhaCungCap": "Search supplier", "timGiaTran": "Search ceiling price", "timTyLeBhThanhToan": "Find the insurance payout rate", "timTyLeThanhToanDV": "Find service payment rate", "timNhomDvCap2": "Find a level 2 service group", "timNhomDvCap3": "Find a level 3 service group", "vuiLongNhapGiaNhap": "Please enter entry price", "vuiLongNhapGiaTran": "Please enter ceiling price", "vuiLongNhapTranBaoHiem": "Please enter insurance ceiling", "vuiLongNhapTyLeBhThanhToan": "Please enter the payment insurance rate", "vuiLongChonNhomDvCap1": "Please choose a level 1 service group!", "vuiLongChonNhomDvCap2": "Please choose a level 2 service group!", "capNhatThanhCongDuLieuHoaChat": "Successfully updated chemical data!", "themMoiThanhCongDuLieuHoaChat": "Successfully added new chemical data!", "maToaNha": "Building code", "nhapMaToaNha": "Enter the building code", "kyHieuHoaDon": "Invoice symbol", "nhapKyHieuHoaDon": "Enter Invoice Symbol", "vuiLongNhapMaToaNha": "Please enter the building code!", "tenToaNha": "Building name", "nhapTenToaNha": "Enter the building name", "vuiLongNhapTenToaNha": "Please enter building name!", "nhapLoaiQms": "Enter the QMS type", "maMauQms": "QMS sample code", "vuiLongNhapMaMauQms": "Please enter the QMS sample code", "tenMauQms": "QMS model name", "vuiLongNhapTenMauQms": "Please enter the QMS model name", "linkMauQms": "QMS sample link", "vuiLongNhapLinkMauQms": "Please enter the QMS sample link", "maHinhThucNhapLoaiXuat": "Input form code/Output type", "vuiLongNhapMaHinhThucNhapLoaiXuat": "Please enter the import form code/Export type", "vuiLongNhapMaHinhThucNhapLoaiXuatKhongQua20KyTu": "Please enter the import/export type code no more than 20 characters!", "tenHinhThucNhapLoaiXuat": "Input type name/Output type", "vuiLongNhapTenHinhThucNhapLoaiXuat": "Please enter the name of the import form/Export type", "vuiLongNhapTenHinhThucNhapLoaiXuatKhongQua1000KyTu": "Please enter the name of the import type/Export type no more than 1000 characters!", "chonHinhThucNhapXuat": "Choose the import and export method", "hinhThucNhapLoaiXuat": "Input type/output type", "capNhatThanhCongDuLieuHinhThucNhapLoaiXuat": "Successfully updated input/output form data!", "themMoiThanhCongDuLieuHinhThucNhapLoaiXuat": "Successfully added new import/export form data!", "khongQua20KyTu": "No more than 20 characters!", "tenVietTatHocHamHocVi": "Abbreviated name of academic title", "vuiLongNhapTenVietTatHocHamHocVi": "Please enter the abbreviation of the academic title!", "vuiLongNhapTenVietTatHocHamHocViKhongQua20KyTu": "Please enter the academic title abbreviation no more than 20 characters!", "khongQua1000KyTu": "No more than 1000 characters!", "timMaHoiDong": "Find the council code", "tenHoiDong": "Council name", "timTenHoiDong": "Find the board name", "chonLoaiHoiDong": "Select the board type", "loaiHoiDong": "Assembly type", "thongTinHoiDong": "Council information", "chiTietHoiDong": "Assembly details", "vuiLongChonLoaiHoiDong": "Please select the assembly type", "nhapTenHoiDong": "Enter the board name", "vuiLongNhapTenHoiDong": "Please enter the board name", "vuiLongNhapTenHoiDongKhongQua1000KyTu": "Please enter a name no more than 1000 characters", "vuiLongNhapMaHoiDong": "Please enter your council code", "vuiLongNhapMaHoiDongKhongQua20KyTu": "Please enter the Council code no more than 20 characters", "maHoiDong": "Council code", "hoiDongKiemKe": "Inventory Council", "hoiDongKiemKeChiTiet": "Detailed inventory council", "timVaiTro": "Find roles", "vuiLongChonVaiTro": "Please select a role", "chonVaiTro": "Select role", "timMaChucVu": "Find position code", "vuiLongChonHoVaTen": "Please select your first and last name", "nhapChucVu": "Enter position", "maNguoiDaiDien": "Representative code", "vuiLongNhapMaNguoiDaiDien": "Please enter representative code!", "vuiLongNhapMaNguoiDaiDienKhongQua20KyTu": "Please enter the representative code no more than 20 characters!", "nhapMaNguoiDaiDien": "Enter the representative code", "vuiLongNhapTenNguoiDaiDienKhongQua1000KyTu": "Please enter representative name no more than 1000 characters!", "nhapTenNguoiDaiDien": "Enter the representative's name", "nhapNgaySinh": "Enter date of birth", "maNgayNghi": "Holiday code", "vuiLongNhapMaNgayNghi": "Please enter the holiday code", "vuiLongNhapMaNgayNghiKhongQua20KyTu": "Please enter the holiday code with no more than 20 characters", "tenNgayNghi": "Name of the holiday", "vuiLongNhapTenNgayNghi": "Please enter the name of the holiday", "vuiLongNhapTenNgayNghiKhongQua1000KyTu": "Please enter the holiday name in no more than 1000 characters", "vuiLongChonNgay": "Please select a date", "ngayNghiLe": "Holidays", "capNhatThanhCongDuLieuPttt": "Successfully updated surgery and procedure data!", "themMoiThanhCongDuLieuPttt": "Successfully added new surgery and procedure data!", "timSoLuongHangDoi": "Find the number of queues", "dsDoiTuong": "List of objects", "timDsDoiTuong": "Find objects", "doiTuongTiepDon": "Reception object", "vuiLongChonDoiTuongTiepDon": "Please select the reception object", "vuiLongNhapKyHieu": "Please enter symbol", "timSoHieuGiuong": "Find the bed number", "vuiLongNhapSoHieuGiuong": "Please enter bed number!", "vuiLongNhapSoHieuGiuongKhongQuaNumKyTu": "Please enter the origin name with no more than {{num}} characters!", "nhapSoHieuGiuong": "Enter bed code", "vuiLongChonLoaiGiuong": "Please select bed type", "vbChuyenMon": "Professional text", "maKhangNguyen": "Antigen code!", "vuiLongNhapMaKhangNguyen": "Please enter the antigen code", "vuiLongNhapTenKhangNguyen": "Please enter the antigen name", "tuGio": "From now", "denGio": "It's time", "maCaLamViec": "Shift code", "vuiLongNhapMaCaLamViec": "Please enter shift code!", "vuiLongNhapCaLamViecKhongQua20KyTu": "Please enter shift no more than 20 characters!", "vuiLongNhapCaLamViec": "Please enter shift!", "nhapCaLamViec": "Enter shift", "tenCaLamViec": "Name of shift", "vuiLongNhapTenCaLamViec": "Please enter shift name!", "vuiLongNhapTenCaLamViecKhongQua1000KyTu": "Please enter shift name no more than 1000 characters!", "chonTuGio": "Choose from now", "cauHoiSangLoc": "SCREENING QUESTIONS", "maCauHoi": "Question code", "timTheoMaCauHoi": "Search by question code", "noiDungCauHoi": "Content Questions", "timTheoNoiDungCauHoi": "Search by question content", "chonLoaiPhieuSangLoc": "Choose the type of screening form", "sttCauHoi": "Question number", "loaiCauHoi": "Kind of question", "chonLoaiCauHoi": "Select question type", "vuiLongNhapMaCauHoi": "Please enter question code", "vuiLongNhapNoiDungCauHoi": "Please enter question content", "vuiLongNhapSttCauHoi": "Please enter question number", "vuiLongNhapMaNhomDvCap1": "Please enter level 1 service group code!", "vuiLongNhapMaNhomDvCap1KhongQua20KyTu": "Please enter level 1 service group code with no more than 20 characters!", "tenLoaiPhieuSangLoc": "Name of screening form type", "vuiLongNhapTenNhomDvCap1": "Please enter the name of level 1 service group!", "vuiLongNhapTenNhomDvCap1KhongQua1000KyTu": "Please enter the name of the level 1 service group with no more than 1000 characters!", "mauInBangKiem": "Checklist printing template", "vuiLongNhapMauInBangKiem": "Please enter the checklist printing form", "timTheoMaNhomDvCap1": "Search by level 1 service group code", "timTheoTenNhomDvCap1": "Search by name of level 1 service group", "timMauInBangKiem": "Find a printable checklist template", "maHdsd": "User manual code", "vuiLongNhapMaHdsd": "Please enter the user manual code!", "vuiLongNhapMaHdsdKhongQua20KyTu": "Please enter the user manual code no more than 20 characters!", "nhapMaHdsd": "Enter the user manual code", "tenTaiLieuHdsd": "User manual document name", "vuiLongNhapTenTaiLieuHdsd": "Please enter the name of the user manual!", "vuiLongNhapTenKhongQua1000KyTu": "Please enter a name no more than 1000 characters!", "nhapTenTaiLieuHdsd": "Enter the name of the User Manual document", "coLoiXayRa": "An error occurred", "taiAnhBia": "Download cover photo", "vuiLongChonFileTaiLen": "Please select the file to upload!", "taiLieu": "document", "taiLenHdsd": "Upload User Manual", "taiLenAnhBiaHdsd": "Upload the cover photo of the User's Guide", "taiFileHdsdDangHtmlPdf": "Download the user manual file in html, pdf format", "maTheBaoHiem": "Insurance card code", "vuiLongNhapMaTheBh": "Please enter your insurance card code!", "vuiLongNhapMaTheBhKhongQua20KyTu": "Please enter insurance card code no more than 20 characters!", "nhapMaTheBaoHiem": "Enter your insurance card code", "tenTheBaoHiem": "Insurance card name", "vuiLongNhapTenTheBh": "Please enter your insurance card name!", "vuiLongNhapTenTheBhKhongQua1000KyTu": "Please enter insurance card name no more than 1000 characters!", "nhapTenTheBaoHiem": "Enter the insurance card name", "vuiLongNhapMucHuong": "Please enter benefit level", "nhapMucHuong": "Enter benefit level", "chuongTrinhGiamGia": "Discount program", "maChuongTrinh": "Program code", "vuiLongNhapMaChuongTrinh": "Please enter program code", "tenChuongTrinh": "Program name", "vuiLongNhapTenChuongTrinh": "Please enter program name!", "vuiLongChonTuNgayApDung": "Please select from applicable date!", "vuiLongChonDenNgayApDung": "Please select the applicable date", "hinhThucMienGiam": "Form of exemption", "vuiLongChonHinhThucGiamGia": "Please choose a discount type!", "cachThucMienGiam": "Exemption method", "vuiLongChonCachThucGiamGia": "Please choose a discount method!", "phanLoaiChuongTrinh": "Program classification", "vuiLongChonPhanLoaiChuongTrinh": "Please select program category", "loaiApDung": "Applicable type", "vuiLongChonLoaiApDungGiamGia": "Please select the type of discount applied!", "vuiLongChonLoaiMienGiam": "Please select an exemption type", "chonLaiDichVu": "Select the service again", "timTenChuongTrinh": "Find the program name", "timGiaTri": "Find value", "timMoTa": "Find description", "timApDungTuNgay": "Find applicable from date", "timApDungDenNgay": "Find applicable up to date", "timHinhThucMienGiam": "Find a form of exemption", "timCachThucMienGiam": "Find a way to get an exemption", "timLoaiApDung": "Find the applicable type", "timPhanLoaiChuongTrinh": "Find program classification", "chonDichVuApDungGiamGia": "Select the service for which the discount applies", "chonNhomDichVuApDungGiamGia": "Select the service group to which the discount applies", "vuiLongChonNhomDichVu": "Please select a service group", "tenNhomDichVu": "Service group name", "timTenNhomDichVu": "Find the service group name", "maVoucher": "Voucher code", "timMaVoucher": "Find voucher code", "vuiLongChonChuongTrinhGiamGia": "Please select a discount program!", "vuiLongNhapMaVoucher": "Please enter voucher code", "maSoBatDau": "Start code", "vuiLongNhapMaSoBatDau": "Please enter the start code!", "maSoKetThuc": "End code", "vuiLongNhapMaSoKetThuc": "Please enter the ending code!", "soLuongPhaiSoLuongDaSuDung": "Quantity must be >= used quantity", "soLuongDaSuDung": "Used quantity", "vuiLongNhapSoLuongDaSuDung": "Please enter the quantity used", "soLuongConLai": "Remaining quantity", "chonChuongTrinhGiamGia": "Choose a discount program", "themMoiHangLoat": "Add new batch", "soDiemToiThieu": "Minimum score", "timTenHangThe": "Find card class name", "vuiLongNhapSoDiemToiThieu": "Please enter the minimum score!", "maNguoiGioiThieu": "Referrer code", "vuiLongNhapMaNguoiGioiThieu": "Please enter referrer code!", "vuiLongNhapMaNguoiGioiThieuKhongQua20KyTu": "Please enter the referrer code no more than 20 characters!", "tenNguoiGioiThieu": "Referrer name", "vuiLongNhapTenNguoiGioiThieu": "Please enter the referrer's name!", "chonTenNguonNguoiBenh": "Select the patient source name", "timTenNguoiGioiThieu": "Find the referrer's name", "timMaNguonNguoiBenh": "Find the patient's source code", "timTenNguonNguoiBenh": "Find the name of the patient's source", "vuiLongNhapMaLieuDungKhongQua20KyTu": "Please enter the dose code no more than 20 characters!", "vuiLongNhapTenLieuDungKhongQua1000KyTu": "Please enter the dosage name in no more than 1000 characters!", "vuiLongNhapSoLanNgay": "Please enter the number of times/day", "soLuongLan": "Quantity/time", "vuiLongNhapSoLuongLan": "Please enter quantity/time", "capNhatThanhCongDuLieuLieuDung": "Successfully updated dosage data!", "themMoiThanhCongDuLieuLieuDung": "Successfully added new dosage data!", "vuiLongNhapMaHoatChatKhongQua20KyTu": "Please enter the active ingredient code no more than 20 characters!", "vuiLongNhapTenHoatChat": "Please enter active ingredient name", "vuiLongNhapTenHoatChatKhongQua1000KyTu": "Please enter active ingredient name no more than 1000 characters!", "chonLoaiDV": "Select service type", "capNhatThanhCongDuLieuHoatChat": "Successfully updated active ingredient data!", "themMoiThanhCongDuLieuHoatChat": "Successfully added new active ingredient data!", "themMoiKhongThanhCongDuLieuHoatChat": "Adding new active ingredient data failed!", "capNhatKhongThanhCongDuLieuHoatChat": "Failed to update active ingredient data!", "mauNen": "Background color", "mauPhanLoaiNguoiBenh": "Color classifies patients", "mauChu": "Text color", "nhapMauChu": "Enter text color", "nhapMauNen": "Enter background color", "timTenBenh": "Find the name of the disease", "tenLoaiBenh": "Name of the disease", "tenNhomBenhChinh": "Name of main disease group", "timTenNhomBenhChinh": "Find the name of the main disease group", "tenNhomBenhPhuI": "Name of disease subgroup I", "timTenNhomBenhPhuI": "Find the name of disease subgroup I", "tenNhomBenhPhuII": "Name of disease subgroup II", "timTenNhomBenhPhuII": "Find the name of subgroup II disease", "timTenChuong": "Find the chapter name", "sttChuongBenh": "Disease chapter number", "maChuongBenh": "Disease chapter code", "tenChuongBenh": "Disease chapter name", "timTheoTenChuongBenh": "Search by disease chapter name", "maNhomBenhChinh": "Main disease group code", "vuiLongNhapMaNhomBenhChinh": "Please enter the main disease group code", "vuiLongNhapMaNhomBenhChinhKhongQua20KyTu": "Please enter the main disease group code with no more than 20 characters!", "vuiLongNhapTenNhomBenhChinh": "Please enter the name of the main disease group", "vuiLongNhapTenNhomBenhChinhKhongQua1000KyTu": "Please enter the name of the main disease group in no more than 1000 characters!", "vuiLongNhapSttChuongBenh": "Please enter patient status!", "vuiLongNhapMaChuongBenh": "Please enter the disease chapter code", "vuiLongNhapMaChuongBenhKhongQua20KyTu": "Please enter the disease chapter code no more than 20 characters!", "vuiLongNhapTenChuongBenh": "Please enter disease chapter name!", "vuiLongNhapTenChuongBenhKhongQua1000KyTu": "Please enter the chapter name no more than 1000 characters!", "timTheoTenNhomBenhChinh": "Search by main disease group name", "maNhomBenhPhuI": "Disease subgroup I code", "timTheoTenNhomBenhPhuI": "Search by name of disease subgroup I", "tatCaNhomBenhChinh": "All major disease groups", "chonNhomBenhChinh": "Select main disease group", "vuiLongNhapMaNhomBenhPhuI": "Please enter the disease subgroup I code", "vuiLongNhapMaNhomBenhPhuIKhongQua20KyTu": "Please enter the disease sub-group I code with no more than 20 characters!", "vuiLongNhapTenNhomBenhPhuI": "Please enter the name of disease subgroup I!", "vuiLongNhapTenNhomBenhPhuIKhongQua1000KyTu": "Please enter the name of disease subgroup I in no more than 1000 characters!", "vuiLongChonNhomBenhChinh": "Please select the main disease group", "maLoaiBenh": "Disease type code", "vuiLongNhapMaLoaiBenh": "Please enter the disease code", "vuiLongNhapMaLoaiBenhKhongQua20KyTu": "Please enter the disease code with no more than 20 characters!", "vuiLongNhapTenLoaiBenh": "Please enter the disease name", "vuiLongNhapTenLoaiBenhKhongQua1000KyTu": "Please enter the disease name in no more than 1000 characters!", "chonNhomBenhPhuI": "Select disease subgroup I", "chonNhomBenhPhuII": "Select disease subgroup II", "timTheoTenLoaiBenh": "Search by disease name", "tatCaNhomBenhPhuI": "All subtype I diseases", "tatCaNhomBenhPhuII": "All disease sub-group II", "maTenBenh": "Disease name code", "timMaBenh": "Find disease code", "maNhomBCBYT": "Health Insurance Report Group Code", "timMaNhomBCBYT": "Find the Health Insurance Report Group Code", "maNhomChiTiet": "Detailed group code", "timMaNhomChiTiet": "Find detailed group codes", "maBenhGuiBHYT": "Disease code sent to health insurance", "timMaBenhGuiBHYT": "Find the disease code to send to health insurance", "tatCaLoaiBenh": "All kinds of diseases", "chonNhomLoaiBenh": "Select disease group", "vuiLongNhapMaTenBenh": "Please enter the disease code", "vuiLongNhapMaTenBenhKhongQua20KyTu": "Please enter the disease code with no more than 20 characters!", "tenTenBenh": "Name of the disease", "vuiLongNhapTenTenBenh": "Please enter the name of the disease!", "vuiLongNhapTenTenBenhKhongQua1000KyTu": "Please enter the disease name no more than 1000 characters!", "vuiLongNhapMaNhomBCBYT": "Please enter Health Insurance Report Group Code", "vuiLongNhapMaNhomChiTiet": "Please enter the detailed group code", "vuiLongNhapMaBenhGuiBHYT": "Please enter the disease code to send to health insurance", "maNhomBenhPhuII": "Disease subgroup II code", "vuiLongNhapMaNhomBenhPhuII": "Please enter the code for disease subgroup II", "vuiLongNhapTenNhomBenhPhuII": "Please enter the name of sub-group II!", "vuiLongNhapTenNhomBenhPhuIIKhongQua1000KyTu": "Please enter the name of disease subgroup II in no more than 1000 characters!", "capNhatThanhCongDuLieuChuongBenh": "Successfully updated disease chapter data!", "themMoiThanhCongDuLieuChuongBenh": "Successfully added new disease chapter data!", "capNhatThanhCongDuLieuNhomBenhChinh": "Successfully updated main disease group data!", "themMoiThanhCongDuLieuNhomBenhChinh": "Successfully added new main disease group data!", "capNhatThanhCongDuLieuNhomBenhPhuI": "Successfully updated disease subgroup I data!", "themMoiThanhCongDuLieuNhomBenhPhuI": "Successfully added new disease subgroup I data!", "capNhatThanhCongDuLieuNhomBenhPhuII": "Successfully updated subgroup II data!", "themMoiThanhCongDuLieuNhomBenhPhuII": "Successfully added new disease subgroup II data!", "capNhatThanhCongDuLieuTenBenh": "Successfully updated disease name data!", "themMoiThanhCongDuLieuTenBenh": "Successfully added new disease name data!", "timLoaiBenh": "Find the type of disease", "tenTitle": "Name {{ title }}", "maTitle": "Code {{ title }}", "timTitle": "Find {{ title }}", "vuiLongNhapTitle": "Please enter {{ title }}", "vuiLongChonTitle": "Please select {{ title }}", "khoaChiDinhDichVu": "Department designates the service", "timMaDichVu": "Find the service code", "timGiaBH": "Find insurance price", "timGiaPhuThu": "Find surcharge prices", "chonĐVT": "Select unit", "chonDichVuCap1": "Choose level 1 service", "chonDichVuCap2": "Select level 2 service", "chonDichVuCap3": "Choose level 3 service", "phanLoaiGiuong": "Bed classification", "chonPhanLoaiGiuong": "Select bed classification", "truongHopKeDV": "In case of prescribing services", "chonTruongHopKeDV": "Select the case where service is prescribed", "dichVu": "Service", "tuongDuong": "Equivalent", "soQuyetDinh": "Decision number", "nhapTitle": "Find {{ title }}", "chonTitle": "Select {{ title }}", "vatTu": "Supplies", "kyHieu": "Sign", "gia": "Price", "timGiaTranBH": "Find insurance ceiling price", "timTyLeBHTT": "Search Health Insurance rate", "tyLeBHThanhToan": "Insurance payment rate", "timTyLeTTDV": "Find the TTDV ratio", "timGia": "Find prices", "maChiTiet": "Detailed code", "tenChiTiet": "Name Details", "soLuongTheoBo": "Quantity per set", "giaNhapSauVAT": "Import price after VAT", "tiLeThanhToan": "Payment rate", "maAnhXa": "Mapping code", "chiTietTrungThau": "Winning bid details", "chiTietBoPhan": "Details of parts", "maKichCo": "Size code", "kichCo": "Size", "tenKichCoTrungThau": "Name of winning size", "chiTietKichCo": "Size details", "sTTTrenBangKe": "Odinal number on the statement", "timTheoSTTTrenBangKe": "Search by odinal number on the list", "trangThaiHoanThanhDV": "Service completion status", "trangThaiKhongDuocHoanThanhDV": "Service not completed status", "boQuaKLLau": "Ignore KL for a long time", "trangThaiSinhSoThuTu": "Sequence number generation state", "sinhSoRiengChoNBUuTien": "Generate a separate number for Priority patient ", "chonUuTien": "Choose priority", "sinhSoRiengChoNBNoiTru": "Generate a separate number for Inpatient patient ", "timTheoMaNhomDvCap2": "Search by level 2 service group code", "timTheoTenNhomDvCap2": "Search by name of level 2 service group", "baoCao": "Report", "luuPhimChup": "Save the captured movie", "congSuatToiDa": "Maximum capacity", "tatCaNhomDvCap1": "All Level 1 service groups", "sTTTrenBaoCao": "Serial number on the report", "timThuTu": "Find order", "maNhomDvCap3": "Level 3 service group code", "timTheoMaNhomDvCap3": "Search by level 3 service group code", "tenNhomDvCap3": "Name of level 3 service group", "timTheoTenNhomDvCap3": "Search by name of level 3 service group", "chonDvTheoYeuCau": "Select service as required", "tatCaNhomDvCap2": "All Level 2 service groups", "trangThaiHoanThanhDv": "Service completion status", "trangThaiKhongHoanThanhDv": "Status of not completing the service", "boQuaKetQuaLau": "Ignore long results", "tenBaoCaoBatBuocDien": "Report name required", "vuiLongNhapSTT": "Please enter the serial number", "nhapSTT": "Enter serial number", "STT": "Serial number", "tuyenBenhVien": "Hospital line", "hangBenhVien": "Hospital level", "timTinhThanhPho": "Find province and city", "benhVien": "Hospital", "taiLen": "Upload", "anhBanQuyenThuongHieu": "Brand copyright photo", "tongTienGoi": "Total package amount", "khongDuocNhapSLMacDinhLonHonSlKhaiBao": "Do not enter a Default Quantity greater than the declared Quantity", "soLuongMacDinh": "Default quantity", "goiDichVuChiTiet": "detailed service package", "dichVuDaTonTaiTrongBoChiDinh": "Service {{name}} already exists in the specifier. Do you want to continue further?", "maDiaChiNhung": "Embedded address code", "diaChiNhung": "Embedded address", "vuiLongNhapMaDiaChiNhung": "Please enter the embedded address code", "vuiLongNhapMaDiaChiNhungKhongQua50KyTu": "Please enter the embedded address code no more than 50 characters", "vuiLongNhapTenCap2": "Please enter level 2 name", "vuiLongNhapTenCap2KhongQua1000KyTu": "Please enter a 2nd level name no more than 1000 characters", "vuiLongNhapGroupId": "Please enter groupId", "vuiLongNhapGroupIdKhongQua1000KyTu": "Please enter groupId no more than 1000 characters", "groupId": "groupId", "tenCap2": "Level 2 name", "reportId": "reportId", "publicKey": "public<PERSON>ey", "vuiLongNhapReportId": "Please enter reportId", "vuiLongNhapPublicKey": "Please enter publicKey", "vuiLongNhapReportIdKhongQua1000KyTu": "Please enter reportId no more than 1000 characters", "vuiLongNhapPublicKeyKhongQua1000KyTu": "Please enter publicKey no more than 1000 characters", "tenCap1": "Level 1 name", "vuiLongNhapTenCap1": "Please enter level 1 name", "vuiLongNhapTenCap1KhongQua70KyTu": "Please enter a level 1 name of no more than 70 characters", "module": "<PERSON><PERSON><PERSON>", "vuiLongChonNhomTinhNang": "Please select a feature group", "vuiLongChonIcon": "Please select icon", "thuTuTrongNhomTinhNang": "Order within feature groups", "vuiLongNhapThuTuTrongNhomTinhNang": "Please enter the order in the feature group", "nhapSoThuTuTrongNhomTinhNang": "Enter the sequential number in the feature group", "vuiLongNhapMoTaKhongQua500KyTu": "Please enter a description no more than 500 characters", "laLinkPowerbi": "It's PowerBI Link", "moTrangMoi": "Open a new page", "icon": "Icon", "chonGio": "Select hours", "chonIcon": "Select icon", "quanLyTaiChinhKeToan": "Financial management - accounting", "quanLyTaiSanTrangThietBi": "Management of assets and equipment", "quanLyNhanLuc": "Workforce management", "quanLyVanBanYTe": "Medical document management", "chiDaoTuyenChiDaoTuXa": "Line steering (remote steering)", "trangThongTinDienTu": "Electronic information", "thuDienTuNoiBo": "Internal email", "quanLyDaoTao": "Training management", "quanLyNghienCuuKhoaHoc": "Scientific research management", "hoSoBenhAnDienTu": "Electronic medical records and records", "khaoSatHaiLong": "Satisfaction survey", "chiSoChatLuongBenhVien": "Hospital quality index", "suCoYKhoa": "Medical incident", "quyen": "permission", "timMaQuyen": "Find the permission code", "timTheoTenQuyen": "Search by permission name", "timTheoMoTa": "Search by description", "timMaNhomTinhNang": "Find the feature group code", "timTheoTenNhomTinhNang": "Search by feature group name", "danhMucThietLapChung": "General settings list", "noiDungThongBao": "Message content", "loaiThongBao": "Notification type", "xoaThongBao": "Delete notification", "vuiLongNhapNoiDung": "Please enter content", "vuiLongNhapNoiDungKhongQua1500KyTu": "Please enter content no more than 1500 characters", "vuiLongChonLoaiThongBao": "Please select notification type", "chonChucVu": "Select position", "capNhatThanhCongDuLieuThongBao": "Successfully updated notification data", "themMoiThanhCongDuLieuThongBao": "Successfully added new notification data", "xoaThanhCong": "Deleted successfully", "thietLapChung": "General settings", "themMoiThanhCongDuLieuThietLapPhieuLinhTra": "Successfully added new payment voucher setup data", "danhMucKhoaDuLieuBaoCaoKho": "Category Warehouse report data key", "khoaDuLieuBaoCaoKho": "Lock warehouse report data", "hangHoaCoDonViSoCapBatBuocDienHeSoDinhMucLonHon0": "Goods have primary units. Required to fill in rating coefficient > 0", "ngayThemMoi": "New added date", "xuatDuLieuMoi": "Export new data", "vuiLongChonngayThemMoi": "Please select a new add date!", "chonThuoc": "Choose medicine", "url": "URL", "vuiLongNhapUrl": "Please enter the url", "vuiLongNhapUrlKhongQua500KyTu": "Please enter URL no more than 500 characters", "danhSachLichKhoaDuLieu": "List of data locking schedules", "nhapTyLeThanhToanDichVu": "Enter service payment rate", "chonNhomDvCap1": "Select level 1 service group", "vuiLongNhapMaGiaTri": "Please enter code {0}", "kichThuocAnhBanQuyenThuongHieuPhaiNhoHon": "Brand copyright image size must be smaller", "nhomHoaChatCap1": "Chemical group level 1", "chonNhomHoaChatCap1": "Select chemical group level 1", "nhomHoaChatCap2": "Chemical group level 2", "chonNhomHoaChatCap2": "Select chemical group level 2", "chonNhomVatTuCap1": "Select level 1 material group", "nhomVatTuCap1": "Level 1 material group", "nhomVatTuCap2": "Level 2 material group", "nhomVatTuCap3": "Level 3 material group", "danhMucCOM": "COM Category", "danhMucSuatAn": "Meal Category", "danhMucMauBenhAnVaoVien": "List of hospital medical records", "mauBenhAnVaoVien": "Hospital admission form", "phieuChiDinh": "Appointment form", "maNhomHoaChatCap1": "Chemical group code level 1", "maNhomHoaChatCap2": "Chemical group code level 2", "maNhomVatTuCap1": "Material group code level 1", "maNhomVatTuCap2": "Material group code level 2", "maNhomVatTuCap3": "Material group code level 3", "vuiLongNhapMaNhomHoaChatCap1": "Please enter the chemical group code level 1", "vuiLongNhapTenNhomHoaChatCap1": "Please enter the name of the chemical group level 1", "vuiLongChonTenNhomHoaChatCap1": "Please select the name of the chemical group level 1", "vuiLongNhapMaNhomHoaChatCap2": "Please enter the chemical group code level 2", "vuiLongNhapTenNhomHoaChatCap2": "Please enter the name of the chemical group level 2", "vuiLongNhapMaNhomVatTuCap1": "Please enter the level 1 material group code", "vuiLongNhapTenNhomVatTuCap1": "Please enter the name of the level 1 material group", "vuiLongNhapMaNhomVatTuCap2": "Please enter the level 2 material group code", "vuiLongNhapMaNhomVatTuCap3": "Please enter the level 3 material group code", "vuiLongNhapTenNhomVatTuCap2": "Please enter the name of the level 2 material group", "vuiLongChonTenNhomVatTuCap1": "Please select the name of the level 1 material group", "vuiLongChonTenNhomVatTuCap2": "Please select the name of the level 2 material group", "vuiLongNhapTenNhomVatTuCap3": "Please enter the name of the level 3 material group", "timMaNhomHoaChatCap1": "Find the chemical group code level 1", "timMaNhomHoaChatCap2": "Find the chemical group code level 2", "timMaNhomVatTuCap1": "Find the code of the level 1 material group", "timMaNhomVatTuCap2": "Find the level 2 material group code", "timMaNhomVatTuCap3": "Find the code of level 3 material group", "tenNhomHoaChatCap1": "Name of chemical group level 1", "tenNhomHoaChatCap2": "Name of chemical group level 2", "tenNhomVatTuCap1": "Name of level 1 material group", "tenNhomVatTuCap2": "Name of level 2 material group", "tenNhomVatTuCap3": "Name of level 3 material group", "timTenNhomHoaChatCap1": "Find the name of the chemical group level 1", "timTenNhomVatTuCap1": "Find the name of the level 1 material group", "timTenNhomHoaChatCap2": "Find the name of the chemical group level 2", "timTenNhomVatTuCap2": "Find the name of the level 2 material group", "timTenNhomVatTuCap3": "Find the name of the level 3 material group", "chonNhomThuocCap2": "Select level 2 drug group", "chonNhomVatTuCap2": "Select level 2 material group", "chonNhomDichVuCap2": "Select level 2 service group", "vuiLongNhapTenLoiDanKhongQua4000KyTu": "Please enter a message name no more than 4000 characters", "timGiaKhongBH": "Find price without insurance", "xuatDanhSachVatTuDayCong": "Export the list of supplies pushed to the portal", "xuatDanhSachThuocDayCong": "Export the list of drugs to push the gate", "xuatDanhSachDichVuDayCong": "Export list of gateway push services", "soGioCanhBaoThuocHuy": "Number of hours the medicine needs to be destroyed", "dichVuKhamBenh": "medical examination service", "dichVuXetNghiem": "testing services", "dichVuCanLamSang": "paraclinical services", "capNhatThanhCongDuLieuNhomDonViTinh": "Unit group data updated successfully!", "themMoiThanhCongDuLieuNhomDonViTinh": "Successfully added new unit group data!", "thoiGianCachTiem": "Time between injections (days)", "khongDungChoDoiTuongBhyt": "Not for health insurance beneficiaries", "chonKhongDungChoDoiTuongBhyt": "Select not to use for health insurance subjects", "lyDoVaoVien": "Reason for admission", "vuiLongNhapLyDoVaoVien": "Please enter reason for admission", "vuiLongNhapChanDoanVaoVien": "Please enter your hospital diagnosis.", "vuiLongNhapTomTatKetQuaCls": "Please enter a summary of the paraclinical test results", "coKetQuaLau": "Long lasting results", "troLyYTe": "Medical Assistant", "yTe4Cham0": "Healthcare 4.0", "dichVuCDHA": "Imaging Diagnostic and Functional Exploration Services", "dichVuNgoaiDieuTri": "Non-therapeutic services", "dichVuPTTT": "Surgical Services - Procedures", "vuiLongNhapChanDoanNoiGioiThieu": "Please enter referral diagnosis", "vuiLongNhapQuaTrinhBenhLy": "Please enter medical history", "tienSuBanThan": "Personal history", "vuiLongNhapTienSuBanThan": "Please enter your personal history", "tienSuGiaDinh": "Family history", "vuiLongNhapTienSuGiaDinh": "Please enter family history", "vuiLongNhapToanThan": "Please enter full body", "vuiLongNhapCacBoPhan": "Please enter parts", "daChoVaoChamSocTaiKhoa": "Admitted to the care unit", "vuiLongChonDaChoVaoChamSocTaiKhoa": "Please select admitted to the department.", "khoaHangLoat": "Bulk Lock", "danhMucMauKetQuaKhamBenh": "List of sample medical examination results", "maMauKetQua": "Sample result code", "timMaMauKetQua": "Find the sample code of the result", "vuiLongNhapMaMauKetQua": "Please enter the result template code", "tenMauKetQua": "Result template name", "timTenMauKetQua": "Find the result template name", "vuiLongNhapTenMauKetQua": "Please enter the result template name", "loaiChuyenKhoaKsk": "Type of health checkup specialty", "chonLoaiChuyenKhoaKsk": "Select health checkup specialty type", "vuiLongChonLoaiChuyenKhoaKsk": "Please select a health checkup specialty type", "mauKetQuaKhamSucKhoe": "Health examination results form", "mauKetQuaKhamBenh": "Medical examination results form", "themMoiThanhCongDuLieuMauKqKsk": "Successfully added new sample health examination results data!", "capNhatThanhCongDuLieuMauKqKsk": "Successfully updated sample health examination results data!", "mauKetQuaXetNghiem": "Test result form", "lieuDungBacSi": "<PERSON><PERSON>ge - Doctor", "layKetQuaLenPhieu": "Get the results on the ballot", "chonPhieu": "Select vote", "loaiThoiGianApDung": "Applicable time type", "chonLoaiThoiGianApDung": "Select the applicable time type", "vuiLongChonLoaiThoiGianApDung": "Please select the applicable time type", "vuiLongChonBaoCao": "Please select a report", "batBuocNhap1Trong3GiaTri": "1 of 3 values ​​required", "chiDinhCungGoi": "Specify the same package", "slToiDa": "Maximum quantity", "nhapSlToiDa": "Enter maximum quantity", "themDichVuHangHoa": "Add services, goods", "timDichVuHangHoa": "Find services and goods", "nhapTuKhoaTimKiem": "Enter search keyword", "chonHoaChat": "Select chemicals", "mauKetQuaCDHATDCN": "Imaging Diagnostic and Functional Exploration Result Template", "themKho": "Add warehouse", "chonLoaiBenh": "Select disease type", "capNhatThanhCongDuLieuBoChiDinhChiTiet": "Detailed designator data updated successfully!", "themMoiThanhCongDuLieuBoChiDinhChiTiet": "New detail specifier data successfully added!", "chiPhiHapSayVTYT": "Cost of medical equipment drying", "capNhatThanhCongDuLieuChiSoCon": "Sub-index data updated successfully!", "themMoiThanhCongDuLieuChiSoCon": "New sub-index data successfully added!", "capNhatThanhCongDuLieuChuongTrinhGiamGia": "Successfully updated discount program data!", "themMoiThanhCongDuLieuChuongTrinhGiamGia": "Successfully added new discount program data!", "capNhatThanhCongDuLieuThietLapQuyTacChonGiuong": "Bed selection rule setup data successfully updated!", "themMoiThanhCongDuLieuThietLapQuyTacChonGiuong": "Successfully added new bed selection rule setup data", "danhMucThietLapKhoChiDinh": "Specified warehouse setup list", "goiPtttChiTiet": "Detailed surgical procedure package", "capNhatThanhCongDuLieuHangThe": "Card rank data updated successfully!", "themMoiThanhCongDuLieuHangThe": "New card rank data successfully added!", "capNhatThanhCongDuLieuHocHamHocVi": "Successfully updated academic title data!", "themMoiThanhCongDuLieuHocHamHocVi": "Successfully added new academic title and degree data!", "capNhatThanhCongDuLieuLoaiCapCuu": "Emergency type data updated successfully!", "themMoiThanhCongDuLieuLoaiCapCuu": "New emergency data added successfully!", "kichCoVatTu": "Material size", "capNhatThanhCongDuLieuLoaiHinhThanhToan": "Payment type data updated successfully!", "themMoiThanhCongDuLieuLoaiHinhThanhToan": "New payment type data successfully added!", "loaiDoiTuongPhuongThucTT": "Payment method object type", "luocDoPt": "Surgical Diagram", "capNhatThanhCongDuLieuVoucher": "Voucher data updated successfully!", "themMoiThanhCongDuLieuVoucher": "New voucher data successfully added!", "capNhatThanhCongDuLieuMoiQuanHe": "Relationship data updated successfully!", "themMoiThanhCongDuLieuMoiQuanHe": "New relationship data successfully added!", "capNhatThanhCongDuLieuViTriChanThuong": "Injury location data successfully updated!", "themMoiThanhCongDuLieuViTriChanThuong": "New injury location data successfully added!", "vanBang": "Diploma", "capNhatThanhCongDuLieuVBChuyenMon": "Successfully updated professional VB data!", "themMoiThanhCongDuLieuVBChuyenMon": "Successfully added new VB professional data!", "tuongTacThuocXetNghiemCsc": "Drug Interactions Sub-Index Testing", "tuongTacThuocXetNghiem": "Drug Interactions Testing", "capNhatThanhCongDuLieuThoiGianCapCuu": "Emergency time data updated successfully!", "themMoiThanhCongDuLieuThoiGianCapCuu": "New emergency time data successfully added!", "themMoiThanhCongDuLieuThietLapTichDiem": "Successfully added new points setting data!", "themMoiThanhCongDuLieuThietLapLuuTruBenhAn": "Successfully added new medical record storage setup data!", "capNhatThanhCongDuLieuTheBaoHiem": "Successfully updated insurance card data!", "themMoiThanhCongDuLieuTheBaoHiem": "Successfully added new insurance card data!", "capNhatThanhCongDuLieuQuanHam": "Military rank data updated successfully!", "themMoiThanhCongDuLieuQuanHam": "Successfully added new military rank data!", "capNhatThanhCongDuLieuPhuongPhapVoCam": "Successfully updated anesthetic method data!", "themMoiThanhCongDuLieuPhuongPhapVoCam": "Successfully added new data of the anesthetic method!", "capNhatThanhCongDuLieuNguoiDaiDien": "Representative data updated successfully!", "themMoiThanhCongDuLieuNguoiDaiDien": "New representative data successfully added!", "phatThanhCong": "Broadcast successful", "huyPhatThanhCong": "Cancel broadcast successful", "duyetTraThanhCong": "Payment successful", "huyDuyetTraThanhCong": "Cancel payment successfully", "capNhatThanhCongDuLieuNhomDvCap2": "Successfully updated level 2 service group data!", "themMoiThanhCongDuLieuNhomDvCap2": "Successfully added new level 2 service group data!", "soLuongTonToiThieu": "Minimum stock quantity", "nhapSoLuongTonToiThieu": "Enter minimum stock quantity", "thietLapQuyTacChonGiuong": "Set up bed selection rules", "securityKey": "security<PERSON>ey", "danhMucViKhuan": "Bacteria Category", "viKhuan": "Bacteria", "mucDoPheDuyetThuocKhangSinh": "Antibiotic approval levels", "doiTuongKcb": "KCB object", "danhMucDoiTuongKcb": "List of medical examination subjects", "phanLoaiChiTietKcb": "Detailed classification of KCB", "nhomDoiTuongKcb": "KCB target group", "chonNhomDoiTuongKcb": "Select KCB target group", "chonPhanLoaiChiTietKcb": "Select detailed KCB classification", "tenDoiTuongKcb": "Name of KCB object", "maDoiTuongKcb": "KCB object code", "vuiLongNhapMaDoiTuongKcb": "Please enter KCB object code", "vuiLongNhapTenDoiTuongKcb": "Please enter the name of the KCB object", "hangBangLaiXe": "Driver's license class", "danhMucHangBangLaiXe": "Driver's License Category", "yeuCauNhapSoLanNgayLonHon0": "Required number of times/day > 0", "yeuCauNhapSoLuongLanLonHon0": "Quantity required/time > 0", "chonNhomDvCap2": "Select level 2 service group", "maSinhPham": "Product code", "maHieuSinhPham": "Biological product code", "taiKhoanDoanhThu": "Revenue Account", "taiKhoanChiPhi": "Expense account", "taiKhoanKho": "Warehouse account", "vuiLongNhapTaiKhoanDoanhThu": "Please enter revenue account", "vuiLongNhapTaiKhoanChiPhi": "Please enter expense account", "vuiLongNhapTaiKhoanKho": "Please enter warehouse account", "danhMucDonViSph": "SPH Unit Catalog", "danhMucTatKhucXa": "List of refractive errors", "tatKhucXa": "Refractive error", "maTatKhucXa": "Refractive error code", "tenTatKhucXa": "Name of refractive error", "donViSph": "SPH unit", "maDonViSph": "Unit code SPH", "tenDonViSph": "Unit name SPH", "donViAxis": "Axis unit", "danhMucDonViAxis": "Axis Unit Catalog", "sph": "SPH", "axis": "Axis", "maDonViAxis": "Axis Unit Code", "tenDonViAxis": "Axis Unit Name", "maDonViCyl": "Unit code CYL", "tenDonViCyl": "Unit name CYL", "donViCyl": "CYL unit", "danhMucDonViCyl": "CYL Unit Catalog", "danhMucNhanAp": "Eye Pressure Index", "nhanAp": "intraocular pressure", "maNhanAp": "Intraocular pressure code", "tenNhanAp": "Name of intraocular pressure", "IDQuyetDinhThau": "Bid decision ID", "khaiBaoPhuCapPttt": "Declare allowances for surgery and procedures, diagnostic imaging, and laboratory tests", "thongTinKhaiBaoPhuCap": "Allowance declaration information", "mucPhuCap": "Allowance level", "phanTramHuong": "Percentage of benefits", "danhMucViTriChamCong": "List of timekeeping locations", "viTriChamCong": "Timekeeping location", "danhMucKhaiBaoPhuCapPttt": "List of declaration of allowances for social insurance, unemployment insurance, and occupational accident", "vuiLongNhapViTriChamCong": "Please enter your time slot", "timTenGoi": "Find package name", "danhMucTuDienYKhoa": "Medical dictionary directory", "maTuDienYKhoa": "Medical dictionary code", "tenTuDienYKhoa": "Medical dictionary names (Disease names, drug names, active ingredient names,...)", "tuDienYKhoa": "medical dictionary", "vuiLongTaiLenMauTuDienYKhoa": "Please upload the medical dictionary form!", "chiChoPhepTaiLenFileCoDinhDangPDF": "Only PDF files are allowed to be uploaded!", "chongChiDinh": "Contraindications", "chongThucHien": "Anti-realism", "dieuKienHanhNghe": "Conditions of practice", "lyDoChiDinh": "Reason for designation", "nhapDuLieuDanhMucNhanVien": "Enter Employee Directory data", "xuatDuLieuDanhMucNhanVien": "Export Employee Directory Data", "nhapDuLieuTTKhoaPhong": "Enter data of Department", "xuatDuLieuTTKhoaPhong": "Export Department Information", "nhapDuLieuDanhMucThuoc": "Enter Drug List data", "xuatDuLieuDanhMucThuoc": "Export Drug List data", "nhapDuLieuChePhamMau": "Enter Blood Product Data", "xuatDuLieuChePhamMau": "Blood Product Data Export", "trungTenThuongMai": "Duplicate trade name", "nhapDuLieuBoChiDinh": "Enter Specifier Data", "xuatDuLieuBoChiDinh": "Export Specifier Data", "trichCNC": "CNC Extract", "danhMucKetQuaChanDoanLaoKhangThuoc": "List of drug-resistant tuberculosis diagnostic results", "ketQuaChanDoanLaoKhangThuoc": "Diagnosis of drug-resistant tuberculosis", "ketQuaChanDoan": "Diagnostic results", "danhMucPhuongPhapChanDoan": "List of Diagnostic Methods", "phuongPhapChanDoan": "Diagnostic methods", "ppChanDoan": "Diagnostic method", "vuiLongNhapMaPP": "Please enter PP code", "vuiLongNhapTenPP": "Please enter PP name", "thongTinPPChanDoan": "Diagnostic PP information", "tenKetQua": "Result name", "tachDong": "Split line", "khongTachDong": "No line breaks", "danhMucPhanLoaiTheoTienSuDieuTri": "Category Classification by treatment history", "phanLoaiTheoTienSuDieuTri": "Classification by treatment history", "nguonKhac": "Other sources", "danhMucNguonKhac": "Other Sources Category", "themThanhCongDuLieuNguonKhac": "Successfully added another data source", "capNhatThanhCongDuLieuNguonKhac": "Source data update successful", "xoaThanhCongDuLieuNguonKhac": "Delete other source data successfully", "vuiLongNhapMaNguonKhacKhongQua20KyTu": "Please enter another source code no more than 20 characters!", "vuiLongNhapTenNguonKhacKhongQua1000KyTu": "Please enter another source name no more than 1000 characters!", "loaiNguonKhac": "Other source types", "loaiTienChiTra": "Payment type", "tinhTheoDonGiaKhongBH": "Calculated by unit price without insurance", "dinhMucThuocVTYT": "Medical equipment drug standards", "danhMucDinhMucThuocVTYT": "List of drug/medical supplies norms", "dichVuTrongDinhMuc": "Service within the norm", "dinhMuc": "Standard", "vuiLongNhapMaDinhMuc": "Please enter the rate code", "soLuongDinhMuc": "Standard quantity", "taiKhoanLienThongDTDT": "Electronic prescription inter-account", "matKhauLienThongDTDT": "Password for electronic prescription", "xaHoiHoa": "Socialize", "danhMucLuongGia": "Valuation list", "luongGia": "Pricing", "chiTietLuongGia": "Pricing details", "thongTinLuongGia": "Valuation information", "vanDeChung": {"title": "Common problem", "ma": "Common Related Issue Code", "ten": "Name General Related Issue", "loai": "Type", "nhapMa": "Enter the common related issue code", "nhapMaKhongQua20KyTu": "Please enter a generic issue code no longer than 20 characters!", "nhapTen": "Enter the name of the general related issue", "nhapTenKhongQua1000KyTu": "Please enter a general related issue name no more than 1000 characters!", "chonLoai": "Select type", "timVanDeChung": "Find common problems", "tatCaVanDeChung": "All common issues", "vuiLongChonLoai": "Please select type"}, "vanDeCuThe": {"title": "Specific problem", "ma": "Specific problem code", "ten": "Name of specific problem", "loai": "Type", "tenVanDeChung": "General problem name", "vuiLongNhapMa": "Please enter specific issue code", "nhapMa": "Enter specific issue code", "nhapMaKhongQua20KyTu": "Please enter a specific issue code no more than 20 characters!", "nhapTen": "Enter specific problem name", "nhapTenKhongQua1000KyTu": "Please enter a specific problem name no more than 1000 characters!", "chonVanDeChung": "Select general issue", "vuiLongNhapChonQuocGia": "Please enter a general issue selection.", "timVanDeCuThe": "Find specific problems"}, "thoiGianDuKienCoKetQua": "Expected time of results", "nhapThoiGianDuKienCoKetQua": "Enter expected time of results", "guiVitimes": "Send to Vitimes", "loaiXnVitimes": "Vitimes test type", "chonLoaiXnVitimes": "Select Vitimes test type", "vuiLongChonLoaiXnVitimes": "Please select Vitimes test type", "maGuiVitimes": "Vitimes sending code", "dayDmThuocSangVitimes": "Push drug list to Vitimes", "dayThuocSangVitimes": "Push medicine to <PERSON><PERSON><PERSON>", "dayDmXnSangVitimes": "Push test list to Vitimes", "dayXnSangVitimes": "Push testing to Vitimes", "dayDmThuocSangVitimesThanhCong": "Successfully pushed the drug list to Vitimes!", "dayThuocSangVitimesThanhCong": "Successfully pushed medicine to <PERSON><PERSON><PERSON>!", "dayDmXnSangVitimesThanhCong": "Successfully pushed the test list to Vitimes!", "dayXnSangVitimesThanhCong": "Successfully pushed the test to Vitimes!", "XNSaoBenhAn": "Medical record test", "danhMucXNSaoBenhAn": "List of Medical Record Tests", "maXNSaoBA": "Medical record test code", "tenXNSaoBA": "Test name in medical record", "maXNGuiLIS": "Test code sent to LIS", "nhomKetQua": "Result group", "layChiSoCon": "Get subscript", "khongLayChiSoCon": "Do not take subscript", "sttXN": "Test No.", "dinhMucThuoc": "Drug rates", "chonDinhMucThuoc": "Select drug level", "dinhMucVtyt": "Medical equipment standards", "chonDinhMucVtyt": "Select Medical equipment standards", "dieuDuongKeHoach": "Nursing planning", "hoLyKeHoach": "Nursing / medical planning", "kyThuatVienKeHoach": "Technician's work plan", "doiTuongTuoi": "Age target", "khongKiemTraTuoi": "No age check", "mienPhiGiamDocDuyet": "Free director review", "keTuGio": "From now on", "keDenGio": "Up to now", "vuiLongChonLoaiBuaAn": "Please select meal type", "kieuKy": "Signature type", "vuiLongChonLoaiKy": "Please select signature type", "guiISC": "Send ISC", "maNgoiThai": "Fetal position code", "vuiLongNhapMaNgoiThai": "Please enter fetal position code", "nhapMaNgoiThai": "Enter fetal position code", "tenNgoiThai": "Name of the fetus", "vuiLongNhapTenNgoiThai": "Please enter fetal name", "nhapTenNgoiThai": "Enter the name of the fetus", "ngoiThai": "<PERSON><PERSON>", "danhMucNgoiThai": "Fetal position list", "cachThucDe": "How to give birth", "danhMucCachThucDe": "List of ways to give birth", "maCachThucDe": "Birth method code", "vuiLongNhapMaCachThucDe": "Please enter the method code", "nhapMaCachThucDe": "Enter the birth method code", "tenCachThucDe": "Name of the method of birth", "vuiLongNhapTenCachThucDe": "Please enter the name of the birth method.", "danhMucChePhamDinhDuong": {"title": "Nutritional Products Category", "maCpdd": "CPDD Code", "vuiLongNhapMaCpdd": "Please enter Nutritional Product Code", "vuiLongNhapMaCpddKhongQua20KyTu": "Please enter Nutritional Product Code no more than 20 characters", "tenCpdd": "CPDD Name", "vuiLongNhapTenCpdd": "Please enter Nutritional Product Name", "vuiLongNhapTenCpddKhongQua500KyTu": "Please enter the name of the nutritional product, no more than 500 characters.", "cachDongGoi": "How to pack", "vuiLongNhapQuyCachKhongQua500KyTu": "Please enter a specification of no more than 500 characters.", "vuiLongNhapCachDongGoiKhongQua500KyTu": "Please enter packaging method no more than 500 characters"}, "bienPhapCamMau": "Hemostasis", "danhMucBienPhapCamMau": "List of hemostatic measures", "maBienPhapCamMau": "Hemostatic measures code", "vuiLongNhapMaBienPhapCamMau": "Please enter the hemostasis measure code", "nhapMaBienPhapCamMau": "Enter the hemostasis code", "tenBienPhapCamMau": "Name of hemostatic measure", "vuiLongNhapTenBienPhapCamMau": "Please enter the name of the hemostatic measure", "nhapTenBienPhapCamMau": "Enter the name of the hemostatic measure", "danhMucDiTatBamSinh": {"title": "List of birth defects", "name": "Congenital disability", "maDiTatBamSinh": "Birth defect code", "vuiLongNhapMaDiTatBamSinh": "Please enter birth defect code", "vuiLongNhapMaDiTatBamSinhKhongQua20KyTu": "Please enter a birth defect code of no more than 20 characters.", "tenDiTatBamSinh": "Name of birth defect", "vuiLongNhapTenDiTatBamSinh": "Please enter the name of the birth defect"}, "vuiLongNhapGiaTriKhongQua50KyTu": "Please enter a value no longer than 50 characters!", "vuiLongChonLoaiKieuKy": "Please select signature type", "themNhomDichVu": "Add service group", "chonViTriCA": "Select CA location", "thoiGianCanhBao": "Warning time", "nhapThoiGianCanhBao": "Enter warning time", "vuiLongNhapThoiGianCanhBao": "Please enter warning time", "phamViCanhBao": "Warning range", "chonPhamViCanhBao": "Select alert range", "vuiLongChonPhamViCanhBao": "Please select warning range", "soNgayCachTiemGiuaCacMui": "Number of days between injections", "canhBaoSuDungThuoc": "Drug use warning", "tuoiThoCuaThuoc": "Shelf life of the drug", "noiDungCanhBaoSuDungThuoc": "Drug use warning content", "tenHienThiManHinhPhauThuatThuThuat": "Surgical Screen Display Name - Procedure", "dvNhomDvApDung": "DV, applicable DV group", "chonNhomDichVuCap1ApDungGiamGia": "Select level 1 service group to apply discount", "chonNhomDichVuCap2ApDungGiamGia": "Select the level 2 service group to apply the discount", "timTenNhomDichVuCap1": "Find the name of the level 1 service group", "timTenNhomDichVuCap2": "Find the name of the level 2 service group", "nhapSoLien": "Enter contact number", "timLoaiDonThuoc": "Find a prescription", "timSoLienIn": "Find the serial number", "timDoiTuongNB": "Find patient  object", "guiTckt": "Send TCKT", "thongTinThau": "Bidding information", "nhapThongTinThau": "Enter bid information", "tinhGiaTheoBoChiDinh": "Calculate price by order", "taiLieuThamKhao": "References", "mucHuongThemVoiHoNgheoCanNgheo": "Additional benefits for poor/near-poor households", "canhBaoTuoi": "Age warning", "danhSachCanhBaoTuoi": "Age warning list", "nhapDenTuoi": "Enter age", "nhapTuTuoi": "Enter age", "nhaNhapKhau": "Importer", "nhapNhaNhapKhau": "Enter importer", "dieuKienBaoQuan": "Storage conditions", "nhapDieuKienBaoQuan": "Enter storage conditions", "chatLuongCamQuan": "Sensory quality", "loaiDoiTuongMacDinh": "Default object type", "vuiLongChonLoaiDoiTuongMacDinh": "Please select default object type", "loaiDoiTuongGioiHan": "Limited object type", "vuiLongChonLoaiDoiTuongGioiHan": "Please select the type of restricted object", "duocSuaGia": "Price corrected", "phanLoaiBenhVien": "Hospital classification", "vuiLongChonTenChiSoSong": "Please select a vital indicator name", "vuiLongNhapTuTuoi": "Please enter age", "vuiLongNhapDenTuoi": "Please enter up to age", "vuiLongNhapGiaTriToiThieu": "Please enter minimum value", "vuiLongNhapGiaTriToiDa": "Please enter maximum value", "vuiLongNhapTuTuoiNhoHonDenTuoi": "Please enter from younger to older age", "vuiLongNhapDenTuoiLonHonTuTuoi": "Please enter up to age greater than from age", "vuiLongNhapGiaTriToiThieuNhoHonGiaTriToiDa": "Please enter minimum value less than maximum value", "vuiLongNhapGiaTriToiDaLonHonGiaTriToiThieu": "Please enter a maximum value greater than the minimum value", "capNhatThanhCongDuLieuThietLapGiaTriCSS": "Successfully updated vital index value setup data", "vuiLongNhapTenHienThiManHinhPhauThuatThuThuat": "Please enter the name of the surgical - procedure screen display", "yeuCauLieuDung": "Dosage requirements", "vuiLongNhapTuTuoiHoacDenTuoi": "Please enter age from or to", "yeuCauPhaChe": "Mixing requirements", "tenGoiSo": "Number name", "vuiLongNhapTenGoiSo": "Please enter your call number", "nhapTenGoiSo": "Enter the number name", "kieuGoiSo": "Dial type", "chonKieuGoiSo": "Select dialing type", "apDungTt20": "Apply TT20", "sttTrongTt20": "Serial Number in TT20", "ghiChuHoatChat": "Active ingredient notes", "ven": "Ven", "nhapGhiChuHoatChat": "Enter active ingredient notes", "nhapVen": "Enter vein", "soLuongGioiHan1Ngay": "Limited quantity/day", "nhapSoLuongGioiHan1Ngay": "Enter Daily Quantity Limit", "yeuCauLapBenhAnDaiHan": "Request for long-term medical records", "hamLuongDdd": "DDD content", "whoDdd": "DDD WHO", "donViDddWho": "WHO DDD Unit", "maVaTu": "Material code", "yeuCauTheoDoi": "Request follow up", "chonLoaiLamTron": "Select rounding type", "chungLoaiPTTT": "Surgery and procedure category", "chonChungLoaiPTTT": "Select surgery and procedure category", "vuiLongChonChungLoaiPTTT": "Please select a surgery and procedure category", "nguoiThucHienMacDinh": "Default executor", "chonNguoiThucHienMacDinh": "Select default executor", "vuiLongChonNguoiThucHienMacDinh": "Please select default executor", "khoaTrucThuoc": "Affiliated Department", "timKhoaTrucThuoc": "Find affiliated departments", "chonKhoaTrucThuoc": "Select affiliated department", "khongLenPhieuThu": "No receipt", "chonKhongLenPhieuThu": "Select Do not generate a receipt", "chinhSachHoaHong": "Commission policy", "danhMucChinhSachHoaHong": "Commission Policy Category", "maChinhSach": "Policy Code", "nhapMaChinhSach": "Enter policy code", "vuiLongNhapMaChinhSach": "Please enter policy code", "nhapDoiTuongApDung": "Enter the applicable object", "vuiLongNhapDoiTuongApDung": "Please enter applicable object", "ngayHieuLuc": "Effective Date", "chonNgayHieuLuc": "Select effective date", "ngayHetHieuLuc": "Expiry date", "chonNgayHetHieuLuc": "Select expiration date", "nhapKhuVuc": "Enter area", "nhomDichVuHoaHong": "Commission Service Group", "danhMucNhomDichVuHoaHong": "Category Commission Service Group", "doiTacHoaHong": "Commission Partner", "danhMucDoiTacHoaHong": "Commission Partner Directory", "vuiLongChonDoiTac": "Please select a partner", "chonDoiTac": "Choose a partner", "vuiLongChonChinhSachHoaHong": "Please select commission policy", "chonChinhSachHoaHong": "Select commission policy", "loaiNgayLamViec": "Type of working day", "vuiLongChonLoaiNgayLamViec": "Please select working day type", "chonloaiNgayLamViec": "Select working day type", "maNhomDvCapNumber": "Service group code level {{number}}", "tenNhomDvCapNumber": "Service level group name {{number}}", "nhomDvHoaHongCapNumber": "Commission service group level {{number}}", "chonNhomDvHoaHongCapNumber": "Select commission service group level {{number}}", "chonNhomDichVuHoaHong": "Select commission service group", "ngoaiVienKhongThuTienGiaKBH": "Foreign hospital (No charge for KBH price)", "coSoChiNhanh": "Branch office", "chonCoSoChiNhanh": "Select branch", "dichVuTongHop": "Comprehensive services", "danhMucDichVuTongHop": "Comprehensive service catalog", "noiCongTac": "Place of work", "timNoiCongTac": "Find a job", "nhapNoiCongTac": "Enter place of work", "danhMucDichVuHoaHong": "Commission Service Catalog", "dichVuHoaHong": "Commission Service", "nguoiGioiThieuThanhToan": "Payment Referrer", "nhapTenNganHang": "Enter bank name", "fileHdsdDungThuoc": "Drug usage instructions file", "slKhamToiDa": "Maximum number of examinations", "nhapSlKhamToiDa": "Enter maximum number of examinations", "tienVatTu": "Material cost", "tuSo": "From number", "nhapTuSo": "Enter from number", "timTuSo": "Find from number", "denSo": "To number", "nhapDenSo": "Enter to number", "timDenSo": "Find the number", "phanTramHHChiDinh": "Commission percentage for prescription", "nhapPhanTramHHChiDinh": "Enter commission percentage for prescription", "phanTramHHThucHien": "Commission percentage for performance", "nhapPhanTramHHThucHien": "Enter commission percentage for performance", "phanTramHHGioiThieu": "Commission percentage for referral", "nhapPhanTramHHGioiThieu": "Enter commission percentage for referral", "tienHHChiDinh": "Commission amount for prescription", "nhapTienHHChiDinh": "Enter commission amount for prescription", "tienHHThucHien": "Commission amount for performance", "nhapTienHHThucHien": "Enter commission amount for performance", "tienHHGioiThieu": "Commission amount for referral", "nhapTienHHGioiThieu": "Enter referral fee", "giamGiaToiDa": "Maximum discount", "nhapGiamGiaToiDa": "Enter maximum discount", "maTckt": "TCKT code", "nhapMaTckt": "Enter TCKT code", "timMaTckt": "Find TCKT code", "tenTckt": "Name of the organization", "nhapTenTckt": "Enter TCKT name", "timTenTckt": "Find TCKT name", "nhanVienKinhDoanh": "Sales staff", "chonNhanVienKinhDoanh": "Select sales staff", "duongDan": "Path", "nhapDuongDan": "Enter path", "taoDuongDan": "Create path", "hopDong": "Contract", "nhapHopDong": "Enter contract", "nhomDvCapNumberHis": "HIS level service group {{number}}", "vuiLongChonCoSoChiNhanh": "Please select a branch", "batBuocNhapChieuCaoNb": "patient  height required", "thuocLasa": "LASA medicine", "nhomLoaiDoiTuong": "Object type group", "danhMucNhomLoaiDoiTuong": "Category Object Type Group", "maNhomLoaiDoiTuong": "Object type group code", "nhapMaNhomLoaiDoiTuong": "Enter the Object Type Group code", "vuiLongNhapMaNhomLoaiDoiTuong": "Please enter the Object Type Group code", "tenNhomLoaiDoiTuong": "Object type group name", "nhapTenNhomLoaiDoiTuong": "Enter the name of the Object Type Group", "vuiLongNhapTenNhomLoaiDoiTuong": "Please enter Object Type Group name", "chonNhomLoaiDoiTuong": "Select Object Type Group", "vuiLongChonnhomLoaiDoiTuong": "Please select Object Type Group", "huongDieuTri": "Treatment direction", "ketQuaDieuTri": "Treatment results", "hienThiTiepDon": "Show at reception", "khaiBaoLoaiDoiTuongThoiGianLaySoVaSoLuongSttToiDa": "Declare object type, time to get number and maximum Serial number", "trongTuan": "During the week", "cuoiTuan": "Weekend", "tuGioDenGio": "From now - To now", "nhapTenTaiKhoan": "Enter account name", "chonTenNganHang": "Select bank name", "daTonTaiMaCodetrongDmServiceName": "Code = {{code}} already exists, in the catalog {{serviceName}}", "vuiLongNhapKhongQuaNumKyTu": "Please enter no more than {{ num }} characters", "vuiLongNhap{{field}}": "Please enter {{field}}", "vuiLongNhap{{field}}KhongQua{{num}}KyTu": "Please enter {{field}} no more than {{num}} characters", "xoaTuongTacThuocXetNghiemConfirm": "You are deleting the drug interaction test{{content}}. <br /> Are you sure you want to continue?", "donVi{{donVi}}": "Unit {{donVi}}", "nhapDuLieuTitle": "Enter data {{ title }}", "xuatDuLieuTitle": "Export data {{ title }}", "danhMucVanDeLienQuanDenThuoc": "Category Drug-Related Issues", "nhapTenCachThucDe": "Enter the name of the birth method", "tenNhomDayCongBhyt": "Name of the group pushing the health insurance gate", "vuiLongNhapMaThuocKhongQua50KyTu": "Please enter the drug code with no more than 50 characters!", "danhMucLyDoDenKham": "List of Reasons for Consultation", "vuiLongNhapMaVatTuKhongQua50KyTu": "Please enter the material code no more than 50 characters.", "vuiLongTaiLenMauBaoCaoExcel": "Please upload the Excel (xlsx) and Word (docx) report templates!", "iSofHToolsVersion": "iSofHTools Version", "nhapISofHToolsVersion": "iSofHTools Version", "vuiLongNhapMaHoaChatKhongQua50KyTu": "Please enter the chemical code with no more than 50 characters!", "daTonTaiTenTitletrongDmServiceName": "The name = {{title}} already exists in the catalog {{serviceName}}.", "khaiBaoPhuCapPtttChiTiet": "Declare detailed allowances for surgery and procedures, imaging diagnostics, and laboratory tests.", "nhapTenHienThi": "Enter display name", "sttTrenBangKe": "Serial number on the invoice", "thoiGianThucHienDichVuPhut": "Service execution time (minutes)", "danhMucGoTat": "Shortcut Menu", "goTat": "Shortcut", "maGoTat": "Shortcut code", "nhapMaGoTat": "Enter shortcut code", "vuiLongNhapMaGoTat": "Please enter the shortcut code!", "vuiLongNhapMaGoTatKhongQuaNumKyTu": "Please enter a shortcut code no more than {{ num }} characters!", "tenGoTat": "Shortcut name", "nhapTenGoTat": "Enter shortcut name", "vuiLongNhapTenGoTat": "Please enter the shortcut name!", "vuiLongNhapTenGoTatKhongQuaNumKyTu": "Please enter a shortcut name no more than {{ num }} characters!", "cumTuDayDu": "Full phrase", "nhapCumTuDayDu": "Enter the full phrase.", "vuiLongNhapCumTuDayDu": "Please enter the complete phrase!", "vuiLongNhapCumTuDayDuKhongQuaNumKyTu": "Please enter a complete phrase no longer than {{ num }} characters!", "vuiLongNhapCachDungKhongQua255KyTu": "Please enter usage instructions no more than 255 characters!", "capBenhVien": "Hospital level", "soLuongToiDa": "Maximum quantity", "vuiLongNhapSoLuongToiDaKhongQua2KyTu": "Please enter a quantity of no more than 2 characters!", "muc1": "Level 1", "muc2": "Level 2", "muc3": "Level 3", "tachDaiSoThuTuUuTien": "Separate the priority order number strip.", "tienThucHien": "Implementation money", "nhapTienThucHien": "Deposit funds for execution", "tienGioiThieu": "Referral fee", "nhapTienGioiThieu": "Referral deposit", "tienChiDinh": "Designated money", "nhapTienChiDinh": "Deposit specified amount", "hinhThucIn": "Printing form", "vuiLongChonHinhThucIn": "Please choose the printing method.", "chonHinhThucIn": "Choose printing method", "loaiPhuCap": "Type of allowance", "taiFileMauImport": "Download the import template file {{title}}", "sinhSoTheoPhongThucHien": "Generate the number according to the performing room.", "chonTheoPhong": "Choose by room", "nhapCachDung": "Enter usage", "vuiLongNhapGhiChuKhongQuaNumKyTu": "Please enter a note no more than {{ num }} characters!", "giaPhuThuLonHonGia": "Supplementary price: <b>{{giaPhuThu}}</b> greater than (Non-insurance price - Insurance price): <b>{{gia}}</b>. Do you want to continue adding new?", "giaPhuThuBeHonGia": "Supplementary charge: <b>{{giaPhuThu}}</b> less than (Non-insurance price - Insurance price): <b>{{gia}}</b>. Do you want to continue adding new?", "vuiLongNhapBietDuoc2": "Please enter Drug 2!", "batBuocNhap1Trong4GiaTri": "You must enter one of the four values.", "vuiLongNhapDacTinhDuocLy2": "Please enter the pharmacological properties 2.", "vuiLongNhapMaAtc2": "Please enter ATC Code 2.", "viTriThucHienDaTonTaiTenLoaiPhuCap": "The location \"{{vitri}}\" already has the allowance type: \"{{loaiPhuCap}}\"", "slGioiHanLuotKcbNgoaiTruBhyt": "Enter Daily Quantity Limit", "nhapSLGioiHanLuotKcbNgoaiTruBhyt": "Enter the limit quantity/number of outpatient medical examination and treatment under health insurance.", "sinhSoTheoNoiLayMau": "Generate numbers according to the sampling location.", "chonTheoNoiLayMau": "Select by sampling location", "thongTinThanhToan": "Payment information", "MID": "MID", "nhapMID": "Enter MID", "TID": "TID", "nhapTID": "Enter TID", "bin": "BIN", "nhapBIN": "Enter BIN", "nhapTaiKhoan": "Enter account", "vuiLongNhapTruong": "Please enter the field!", "xoaDongDuLieu": "Delete the data row", "thuocNguyCoCao": "High-risk medication", "nhapMaNganHang": "Enter bank code", "vuiLongNhapMaNganHangKhongQua8KyTu": "Please enter a bank code no more than 8 characters!", "maPhongGuiBhyt": "Health insurance room code", "serviceCodeBilling": "serviceCode billing", "nhapServiceCodeBilling": "Enter serviceCode billing", "phanLoaiNBCapCuu24h": "Emergency Department Classification 24h", "tenMoiThau": "Tender invitation name", "thoiGianThucHienKhamPhut": "Duration of examination (minutes)", "nhomDaiPhieuNhapXuat": "Group of input and output vouchers", "tenHinhThucNhapVaLoaiXuat": "Name of the input form, type of output", "chonTenHinhThucNhapLoaiXuat": "Select the name of the input method, type of output.", "nhapMaNhom": "Enter group code", "maHoatChatThuocCoDinhGuiKemBhyt": "Drug active ingredient code fixed sent with health insurance", "tenThuocCoDinhGuiKemBhyt": "Fixed drug name sent with health insurance.", "nhapMaHoatChatThuoc": "Enter the active ingredient code.", "danhMucNhomDaiPhieuNhapXuat": "Category of Input, Output Voucher Groups", "tuChua": "Storage cabinet", "nhapTuChua": "Enter storage cabinet", "kiemTraHangNgay": "Daily check", "giuongThucKeToiDa": "Maximum actual bed size", "nhapGiuongThucKeToiDa": "Enter the maximum number of real beds.", "sapXepTruong": "Arrange field", "coPhim": "film size", "danhMucCoPhim": "Film Size Category", "maCoPhim": "Film size code", "nhapMaCoPhim": "Enter film size code", "tenCoPhim": "Movie title", "nhapTenCoPhim": "Enter the film size name", "vuiLongNhapMaCoPhim": "Please enter the film size code!", "vuiLongNhapTenCoPhim": "Please enter the film size name!", "vuiLongNhapGiaTriKhongQua255KyTu": "Please enter a value no more than 255 characters!", "maKhoaGiuongBhyt": "Health insurance bed code", "themNhanhMucDoTuongTac": "Add quick Engagement level", "themNhanhHauQuaTuongTac": "Quickly add Interaction consequences", "tenHauQuaTuongTac": "Name of interaction consequences", "nhapTenHauQuaTuongTac": "Enter the name of the interaction consequence", "vuiLongNhapTenHauQuaTuongTac": "Please enter the name of the interaction consequence!", "tenMucDoTuongTac": "Interaction level name", "nhapTenMucDoTuongTac": "Enter the interaction level name", "vuiLongNhapTenMucDoTuongTac": "Please enter the interaction level name!", "terminalId": "terminal.id", "nhapTerminalId": "Enter terminal.id", "secretKey": "secret.key", "nhapSecretKey": "Enter secret.key", "tenKhoaDuocPhepChiDinh": "The name of the department is allowed to be designated.", "dvKemChePhamMau": "DV with blood products", "dangTienHanhImportDuLieuTitle": "Importing data {{title}} is in progress.", "importDuLieuTitleThanhCong": "Data import {{title}} successful!", "importDuLieuTitleThatBai": "Data import {{title}} failed!", "importVaiTro": "Import role", "saoChepTaiKhoan": "Copy account", "saoChepTuTaiKhoan": "Copy from account", "danhSachVaiTro": "List of roles", "saoChepTaiKhoanThanhCong": "Account copied successfully.", "saoChepTaiKhoanThatBai": "Account copy failed.", "luuVaDongBoDanhMuc": "Save and synchronize the catalog.", "taiKhoanHDDT": "Electronic invoice account", "chuKyTay": "Hand signature", "soLuongToiDa1LanChiDinh": "Maximum quantity of 1 assignment at a time.", "nhapSoLuongToiDa1LanChiDinh": "Enter the maximum quantity for one assignment.", "luuVaTaoMoi": "Save and Create New", "khongLuuVaTaoMoi": "Don't save and Create new", "banGhiHienTaiChuaLuu": "The current record has not been saved.", "boQuaCanBangTai": "Ignore load balancing.", "danhMucPhanLoaiVTYT": "Classification list of medical equipment", "phanLoaiVTYT": "Classification of medical equipment", "chonPhanLoaiVTYT": "Select medical equipment classification", "khongChonNhieuKhoaVaPhong": "Please select either multiple designated departments or multiple rooms, do not select both at the same time.", "nhapLyDoDenKham": "Enter the reason for the examination.", "vuiLongLyDoDenKhamKhongQua4000KyTu": "Please enter the reason for the examination no more than 4000 characters.", "thuTuUuTienThucHienDV": "Priority order for performing services", "nhapThuTuUuTienThucHienDV": "Enter the priority order for performing the service.", "dvKemThuoc": "Service with medication", "capNhapLoaiCoPhimSoLuongPhim": "Need to enter Film size type, number of films.", "mucDoBangChung": "Level of evidence", "danhMucMucDoBangChung": "Evidence Level Categories", "chuaChonKhoaChiDinh": "No department selected.", "vuiLongNhapMucDoBangChung": "Please enter the level of evidence.", "thoiGianKhoiPhat": "Onset time", "vuiLongNhapMaMucDoBangChungKhongQua20KyTu": "Please enter the evidence level code no more than 20 characters!", "vuiLongNhapTenMucDoBangChung": "Please enter the level of evidence name.", "vuiLongNhapMaMucDoBangChung": "Please enter the proof level code.", "vuiLongNhapTenMucDoBangChungKhongQua1000KyTu": "Please enter the Name of the evidence level no more than 1000 characters!", "khongDuocPhepNhapKhoangTrang": "Whitespace is not allowed.", "khongDuocPhepNhapKyTuDacBiet": "Special characters are not allowed.", "vuiLongNhapMaChiSoSong": "Please enter the living index code!", "vuiLongNhapMaChiSoSongKhongQua20KyTu": "Please enter the living index code no more than 20 characters!", "nhapGiaGoc": "Enter the original price", "thuocVatTuBanGiao": "Medicine, supplies delivered", "danhMucThuocVatTuBanGiao": "List of Medicines and Supplies Delivered", "capNhatThanhCongDuLieuNguoiGioiThieu": "Successfully updated referral data.", "themMoiThanhCongDuLieuNguoiGioiThieu": "Successfully added new referral data.", "ngayHoatDong": "Activity day", "yeuCauPhanTangNguyCo": "Risk stratification requirements", "manHinhHienThi": "Display screen", "hienThiTiepDonTrenKiosk": "Display welcome on kiosk", "phanTangNguyCoBN": "Patient risk stratification", "danhMucPhanTangNguyCoBN": "Patient Risk Stratification Categories", "capNhatThanhCongSinhSoThuTuThuNgan": "Update cashier serial number generation settings successfully", "vuiLongNhapMaThuocKhongQua200KyTu": "Please enter the medication code no more than 200 characters!", "nhapMaThietBi": "Enter device code", "nhomVatTuCap4": "Group of Level 4 Supplies", "chonNhomVatTuCap4": "Select group of supplies level 4", "noiLayMauBenhPham": "Sample collection site", "maNhomVatTuCap4": "Group code for level 4 supplies", "vuiLongNhapMaNhomVatTuCap4": "Please enter the group code for supply level 4.", "vuiLongNhapTenNhomVatTuCap4": "Please enter the name of the level 4 supply group.", "timMaNhomVatTuCap4": "Find the group code for level 4 supplies.", "tenNhomVatTuCap4": "Group name of level 4 supplies", "timTenNhomVatTuCap4": "Find the name of the group of level 4 supplies.", "vuiLongNhapMaVatTuKhongQua200KyTu": "Please enter the material code with no more than 200 characters.", "chonNhomVatTuCap3": "Select group of supplies level 3", "giaTriVuotNguongToiThieu": "Value exceeding the minimum threshold", "giaTriVuotNguongToiDa": "Maximum threshold value", "vuiLongNhapGiaTriVuotNguongToiThieuNhoHonGiaTriToiThieu": "Please enter a value that exceeds the minimum threshold that is less than the minimum value.", "vuiLongNhapGiaTriVuotNguongToiDaLonHonGiaTriToiDa": "Please enter a value exceeding the maximum threshold greater than the maximum value.", "vuiLongNhapMaHoaChatKhongQua200KyTu": "Please enter the chemical code no more than 200 characters!", "nhapMaHoiDong": "Enter council code", "nhapDinhMuc": "Input standard rates", "vuiLongNhapDinhMucNgoaiTruLonHon0": "Please enter the outpatient rate > 0.", "vuiLongNhapDinhMucNoiTruLonHon0": "Please enter an inpatient rate > 0.", "dinhMucNgoaiTru": "Outpatient rate", "nhapDinhMucNgoaiTru": "Outpatient billing standards", "dinhMucNoiTru": "Inpatient rate", "nhapDinhMucNoiTru": "Inpatient rate entry", "chonNgayHoatDong": "Select activity date", "coCheThuChiNoiBo": "Internal Revenue Mechanism", "danhMucCoCheThuChiNoiBo": "Internal Revenue and Expenditure Mechanism List", "mucChi": "Cost level", "nhapMucChi": "Enter the expense level", "vuiLongNhapMucChi": "Please enter the amount of the expense!", "vuiLongNhapMucChiLonHon0": "Please enter an amount greater than 0.", "tyLeHuongTheoQuyChe": "Benefit rate according to regulations", "nhapTyLeHuongTheoQuyChe": "Enter the benefit rate according to the regulations.", "VuiLongNhapTyLeHuongTheoQuyChe": "Please enter the reimbursement rate according to the regulations!", "tyLeHuongTheoGiamDocDuyet": "Rate of reimbursement as approved by the Director", "nhapTyLeHuongTheoGiamDocDuyet": "Enter the benefit rate as approved by the Director.", "thueTndn": "Corporate income tax", "nhapThueTndn": "Corporate income tax input", "vuiLongNhapThueTndn": "Please enter the corporate income tax!", "danhMucMaTuyChinh": "Custom code list", "maTuyChinh": "Custom code", "danhSachCaLamViec": "Work shift schedule", "trua": "Lunch", "tenKhuVuc": "Region name", "logNguoiDung": "User Log", "vuiLongKiemTraLaiCaLamViec": "Please check the work shift again!", "vaiTroDuocGan": "Assigned role", "tenQuyen": "Name of the right", "anhKy": "Signature image", "vuiLongNhapCheDoAn": "Please enter the diet.", "vuiLongChonCheDoChamSoc": "Please select a care plan.", "nhomLoaiBenhAn": "Patient case group", "danhMucNhomLoaiBenhAn": "Category of Medical Record Types", "maNhomLoaiBenhAn": "Diagnosis group code", "nhapMaNhomLoaiBenhAn": "Enter the code for the type of medical record.", "vuiLongNhapMaNhomLoaiBenhAn": "Please enter the code for the medical record type group!", "vuiLongNhapMaNhomLoaiBenhAnKhongQuaNumKyTu": "Please enter the group code for the medical record that is no more than {{ num }} characters!", "tenNhomLoaiBenhAn": "Name of the medical record type group", "nhapTenNhomLoaiBenhAn": "Enter the name of the diagnosis group.", "vuiLongNhapTenNhomLoaiBenhAn": "Please enter the name of the medical record type group!", "vuiLongNhapTenNhomLoaiBenhAnKhongQuaNumKyTu": "Please enter the name of the diagnosis group no more than {{ num }} characters!", "tenCanhBao1": "Alert Name 1", "tenCanhBao2": "Warning name 2", "chonNhomLoaiBenhAn": "Select the type of medical record group.", "canhBaoSuDungDichVu": "Service usage warning", "nhapCanhBaoSuDungDichVu": "Enter service usage warning", "chuaGomTrongGiuong": "Not included in the bed day package.", "chonChuaGomTrongGiuong": "Selection not included in the bed day package.", "khongTheChonDongThoiKhongTinhTienVaChuaGomTrongGiuong": "Cannot select both 'No charge' and 'Not included in the daily bed package' at the same time.", "danhMucThangInBaoCaoKho": "Inventory report month category", "thangInBaoCaoKho": "Month in inventory report", "vuiLongNhapThang": "Please enter the month.", "vuiLongNhapNam": "Please enter the year.", "vuiLongNhapDenNgay": "Please enter the date until.", "placeholderNam": "Enter the year, for example: {{ year }}", "placeholderThang": "Enter month (1 - 12)", "danhSachPhieuInDiKem": "List of printed tickets", "soLuongNBChoPhep": "Number of patient allowed", "nhapSoLuongNBChoPhep": "Please enter the number of patient allowed", "giaTriMacDinh": "Default value", "nhomBaoCaoPhuCapPTTT": "PTTT allowance reporting group", "thietLapDieuKienHoanThanhKy": "Set up the signing completion condition", "ketNoiBenhAn": "Connect medical records", "chonKetNoiBenhAn": "Select medical records connection", "khoaLienKet": "Linked Faculty", "soLuongNguoiBenhTaiKham": "Number of patients for re-examination", "nhapSoLuongNguoiBenhTaiKham": "Enter the number of patients for re-examination", "vuiLongNhapSoLuongNguoiBenhTaiKhamLonHon0": "Please enter the number of patients for re-examination greater than 0", "chuyenKhoaToiKhoaThuong": "Transfer to normal department", "chuyenKhoaToiKhoaPhauThuat": "Transfer to surgery department", "chuyenKhoaTuKhoaPhauThuat": "Transfer from surgery department", "raVienTuKhoaThuong": "Discharge from normal department", "raVienTuKhoaPhauThuat": "Discharge from surgery department", "chonTenPhieu": "Select ticket name", "vuiLongChonTenPhieu": "Please select a voucher name", "danhMucPhanLoaiVTYTHoaChat": "VTYT/Chemical Classification Category", "phanLoaiVTYTHoaChat": "VTYT/Chemical Classification", "vuiLongNhapMaPhanLoaiKhongQua50KyTu": "Please enter a classification code of no more than 50 characters", "phanLoaiHoaChat": "Chemical classification", "danhMucLyDoChiDinhDichVu": "Category of Reasons for Service Appointment", "lyDoChiDinhDichVu": "Reason for Service Appointment"}