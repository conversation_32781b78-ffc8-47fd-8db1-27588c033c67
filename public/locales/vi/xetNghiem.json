{"phongLayMau": "<PERSON>òng lấy mẫu", "phongThucHien": "<PERSON><PERSON><PERSON> thự<PERSON> hi<PERSON>n", "soLuongHangDoi": "S<PERSON> lư<PERSON>ng hàng đợi", "nhapSoLuongHangDoi": "Nhâp số lượng hàng đợi", "khongTonTaiDichVuNguoiBenh": "<PERSON><PERSON><PERSON><PERSON> tồn tại dịch v<PERSON> ng<PERSON><PERSON> b<PERSON>nh {0}!", "quetMaQRNguoiBenh": "<PERSON><PERSON><PERSON> mã QR ngư<PERSON><PERSON> bệnh", "timKiemTheoSoPhieu": "<PERSON><PERSON><PERSON> kiếm theo số phi<PERSON>u", "xemThongTinDayDu": "<PERSON><PERSON> thông tin đ<PERSON>y đủ", "tenNguoiBenh": "<PERSON><PERSON><PERSON> b<PERSON>nh", "nhapTenNguoiBenh": "<PERSON><PERSON><PERSON><PERSON> tên ng<PERSON><PERSON> b<PERSON>nh", "soPhieu": "<PERSON><PERSON> p<PERSON>", "maDV": "Mã DV", "timMaDV": "Tìm mã DV", "tenDV": "Tên DV", "timTenDV": "<PERSON><PERSON><PERSON> tên <PERSON>", "timMaThietLap": "<PERSON><PERSON><PERSON> mã thi<PERSON><PERSON> lập", "moiChonDichVu": "<PERSON><PERSON><PERSON> chọn dịch vụ!", "timTheoGiaTri": "<PERSON><PERSON><PERSON> theo giá trị", "sl": "SL", "daGuiLIS": "Đã gửi lis", "ngayThucHien": "<PERSON><PERSON><PERSON>n", "bacSiChiDinh": "<PERSON><PERSON><PERSON> sĩ chỉ định", "timTheoMoTa": "<PERSON><PERSON><PERSON> theo mô tả", "khoaChiDinh": "Khoa chỉ định", "danhSachDichVu": "<PERSON><PERSON> s<PERSON>ch d<PERSON>ch vụ", "inPhieuYCHoanDoi": "In phiếu yc hoàn/ đổi", "xacNhanLayMau": "<PERSON><PERSON><PERSON> nh<PERSON>n lấy mẫu", "yeuCauHoan": "<PERSON><PERSON><PERSON> c<PERSON>n", "danhSachNguoibenhChuanBiLayMau": "<PERSON><PERSON> s<PERSON>ch NB chuẩn bị lấy mẫu", "goi": "GỌI", "xetNghiem": "<PERSON><PERSON><PERSON>", "layMau": "L<PERSON>y mẫu", "benhPham": "<PERSON><PERSON><PERSON> phẩm", "moTaBenhPham": "<PERSON><PERSON> t<PERSON> b<PERSON><PERSON> phẩm", "tinhChatBenhPham": "<PERSON><PERSON><PERSON> chất b<PERSON><PERSON> phẩm", "viTriSinhThiet": "<PERSON><PERSON> trí sinh thiết", "phuongPhapNhuom": "Phương p<PERSON><PERSON><PERSON>", "soGpb": "Số GPB", "soTieuBan": "<PERSON><PERSON> ti<PERSON><PERSON> bản", "soHoSo": "S<PERSON> hồ sơ", "viThe": "Vi thể", "daiThe": "<PERSON><PERSON><PERSON> thể", "nhapKetQua": "<PERSON><PERSON><PERSON><PERSON> kết quả", "nhapKetLuan": "<PERSON><PERSON><PERSON><PERSON> kết luận", "banLuan": "<PERSON><PERSON><PERSON> lu<PERSON>n", "nhapBanLuan": "<PERSON><PERSON><PERSON><PERSON> bàn luận", "vuiLongNhapBanLuan": "<PERSON>ui lòng nhập bàn luận!", "maMay": "M<PERSON> m<PERSON>", "timMaMay": "<PERSON><PERSON><PERSON> mã máy", "chonMaMay": "<PERSON><PERSON><PERSON> mã máy", "chonKetQua": "<PERSON><PERSON><PERSON> kết quả", "timKetQua": "<PERSON><PERSON><PERSON> kết quả", "ketQuaBinhThuong": "<PERSON><PERSON><PERSON> qu<PERSON> b<PERSON>nh thư<PERSON>", "danhGiaKetQua": "<PERSON><PERSON><PERSON> giá kết quả", "chonDanhGiaKetQua": "<PERSON><PERSON><PERSON> đ<PERSON>h giá kết quả", "maChiSo": "Mã chỉ số", "timMa": "<PERSON><PERSON><PERSON> mã", "timTen": "<PERSON><PERSON><PERSON> tên", "tenChiSo": "Tên chỉ số", "chiSoCao": "Chỉ số cao", "timTheoChiSoCao": "<PERSON><PERSON><PERSON> theo chỉ số cao", "timChiSoCao": "Tì<PERSON> chỉ số cao", "chiSoThap": "Chỉ số thấp", "timChiSoThap": "<PERSON><PERSON><PERSON> chỉ số thấp", "timTheoChiSoThap": "<PERSON><PERSON><PERSON> theo chỉ số thấp", "donVi": "Đơn vị", "timDonVi": "<PERSON><PERSON><PERSON> vị", "viSinhKySinhTrung": "<PERSON>i sinh - ký sinh trùng", "chonMauKetLuan": "<PERSON><PERSON><PERSON> mẫu kết luận", "giaiPhauBenh": "Gi<PERSON>i phẫu bệnh", "viSinh": "<PERSON><PERSON> sinh", "chanDoanSoBo": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON> sơ bộ", "chiTietDichVu": "<PERSON> ti<PERSON><PERSON> d<PERSON>ch vụ", "tiepNhanMau": "<PERSON><PERSON><PERSON><PERSON> nhận mẫu", "huyMau": "Hủy mẫu", "huyTiepNhanMau": "<PERSON><PERSON><PERSON> tiếp nhận mẫu", "duyetKetQua": "<PERSON><PERSON><PERSON><PERSON> kết quả", "huyDuyetKetQua": "Huỷ duyệt kết quả", "inKetQua": "In kết quả", "luuLai": "<PERSON><PERSON><PERSON>", "capNhatKetQuaThanhCong": "<PERSON><PERSON><PERSON> nh<PERSON>t kết quả thành công", "layMauThanhCong": "Lấy mẫu thành công!", "huyMauThanhCong": "H<PERSON>y mẫu thành công", "daTiepNhanDVCuaNguoiBenhVuiLongLayMau": "<PERSON><PERSON> tiếp nhận DV của ngư<PERSON>i b<PERSON>nh, vui lòng lấy mẫu!", "coKetQuaThanhCong": "<PERSON><PERSON> kết quả thành công", "huyCoKetQuaThanhCong": "<PERSON><PERSON><PERSON> có kết quả thành công", "duyetKetQuaThanhCong": "<PERSON><PERSON><PERSON><PERSON> kết quả thành công", "huyDuyetKetQuaThanhCong": "<PERSON><PERSON><PERSON> du<PERSON><PERSON>t kết quả thành công", "tiepNhanMauThanhCong": "<PERSON><PERSON><PERSON><PERSON> nhận mẫu thành công!", "huyTiepNhanMauThanhCong": "<PERSON><PERSON><PERSON> tiếp nhận mẫu thành công", "chiSoCon": "Chỉ số con", "vuiLongNhapKetQuaKhongQua1500KyTu": "<PERSON><PERSON> lòng nhập kết quả không quá 1500 ký tự!", "vuiLongNhapKetLuanKhongQua1500KyTu": "<PERSON><PERSON> lòng nhập kết luận không quá 1500 ký tự!", "vuiLongNhapBanLuanKhongQua1500KyTu": "<PERSON><PERSON> lòng nhập bàn luận không quá 1500 ký tự!", "vuiLongNhapKetLuan": "<PERSON>ui lòng nhập kết luận!", "thucHienGiaiPhauBenhViSinh": "<PERSON><PERSON><PERSON><PERSON> hiện <PERSON> phẫu b<PERSON><PERSON> - <PERSON><PERSON> sinh", "thucHienSinhHoaHuyetHoc": "<PERSON><PERSON><PERSON><PERSON> hi<PERSON> h<PERSON>- <PERSON><PERSON><PERSON><PERSON> học", "giaTriThamChieu": "<PERSON><PERSON><PERSON> trị tham chiếu", "luuKetQua": "<PERSON><PERSON><PERSON> qu<PERSON>", "xemKetQuaPdf": "<PERSON><PERSON> qu<PERSON>", "layMauBenhPham": "<PERSON><PERSON><PERSON> mẫu bệnh phẩm", "choTiepNhan": "<PERSON><PERSON> tiếp nhận", "choLayMau": "Chờ lấy mẫu", "bsChiDinh": "BS chỉ định", "tgLayMau": "TG lấy mẫu", "nguoiLayMau": "<PERSON><PERSON><PERSON><PERSON> lấy mẫu", "nguoiTiepNhan": "<PERSON><PERSON><PERSON><PERSON> ti<PERSON>n", "tgTiepNhanMau": "TG tiếp nhận mẫu", "tgCoKetQua": "TG có kết quả", "tgThucHien": "<PERSON>G th<PERSON><PERSON> hi<PERSON>n", "nguoiThucHien": "<PERSON><PERSON><PERSON><PERSON> th<PERSON> hi<PERSON>n", "tgDuyetKetQua": "TG duyệt kết quả", "nguoiDuyet": "<PERSON><PERSON><PERSON><PERSON>", "daGuiLis": "Đã gửi LIS", "daTiepNhanMau": "<PERSON><PERSON> tiếp nhận mẫu", "daCoKetQua": "Đã có KQ", "daDuyetKetQua": "Đã duyệt KQ", "daLayMau": "Đã lấy mẫu", "ghiChu": "<PERSON><PERSON><PERSON>", "timGhiChu": "<PERSON><PERSON><PERSON> ghi chú", "nhapGhiChu": "<PERSON><PERSON><PERSON><PERSON>hi <PERSON>ú", "quyTrinhXetNghiem": "<PERSON><PERSON> tr<PERSON>nh x<PERSON> ng<PERSON>", "phuongPhapXetNghiem": "Phương ph<PERSON>p x<PERSON>t ng<PERSON>", "nhapChiSoCao": "<PERSON><PERSON><PERSON><PERSON> chỉ số cao", "nhapChiSoThap": "<PERSON><PERSON><PERSON><PERSON> chỉ số thấp", "nhapMaChiSoCon": "<PERSON><PERSON><PERSON><PERSON> mã chỉ số con", "nhapTenChiSoCon": "<PERSON><PERSON><PERSON><PERSON> tên chỉ số con", "timTenNbMaNbQrNbMaHoSo": "<PERSON><PERSON><PERSON> tên NB, mã NB, QR NB, mã hồ sơ", "timMaNbQrNbMaHoSo": "<PERSON><PERSON><PERSON> mã NB, QR NB, mã hồ sơ", "nhiemKhuan": "<PERSON><PERSON><PERSON><PERSON>", "nguoiTiepNhanMau": "<PERSON><PERSON><PERSON><PERSON> ti<PERSON>n", "nguoiDuyetKetQua": "<PERSON><PERSON><PERSON><PERSON> du<PERSON>t kết quả", "thoiGianLayMau": "<PERSON><PERSON><PERSON><PERSON> gian lấy mẫu", "thoiGianTiepNhan": "<PERSON><PERSON><PERSON><PERSON> gian ti<PERSON> nh<PERSON>n", "thoiGianCoKetQua": "<PERSON><PERSON><PERSON><PERSON> gian có kết quả", "thoiGianDuyetKetQua": "<PERSON><PERSON><PERSON><PERSON> gian <PERSON> kết quả", "trangThai": "<PERSON><PERSON><PERSON><PERSON> thái", "choTiepDonCLS": "<PERSON><PERSON> tiếp đón <PERSON>", "benhVienThucHien": "<PERSON><PERSON><PERSON> viện thự<PERSON> hiện", "maDinhDanhBhytNguoiThucHien": "<PERSON><PERSON> đ<PERSON>nh danh BHYT ng<PERSON><PERSON>i thực hiện", "chonBenhPham": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> phẩm", "nhapCanhBao": "<PERSON><PERSON><PERSON><PERSON> c<PERSON> b<PERSON>o", "nguoiThChinh2": "Người TH chính 2", "nguoiThPhu1": "Người TH phụ 1", "nguoiThPhu2": "Người TH phụ 2", "nguoiThPhu3": "Người TH phụ 3", "thanhVienKhac": "<PERSON><PERSON><PERSON><PERSON> viên kh<PERSON>c", "tenNguoiTiepNhan": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON> tiế<PERSON> n<PERSON>n", "tenNguoiDuyetKetQua": "<PERSON><PERSON><PERSON><PERSON> du<PERSON>t kết quả", "eGFR": "eGFR", "eCrCl": "eCrCl", "thuocVTYTHoaChatKemXetNghiem": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, h<PERSON><PERSON> chất kèm xét nghiệm", "chonDichVuXetNghiem": "<PERSON><PERSON><PERSON> d<PERSON> v<PERSON> x<PERSON>", "maXetNghiem": "<PERSON><PERSON> x<PERSON> ng<PERSON>", "tenXetNghiem": "<PERSON><PERSON><PERSON>", "ngayThucHienXetNghiem": "<PERSON><PERSON><PERSON> th<PERSON><PERSON> hi<PERSON>n <PERSON>", "maHangHoa": "Mã hàng hoá", "tenHangHoa": "<PERSON><PERSON><PERSON> h<PERSON>", "nguoiBenhKhongTonTaiXetNghiemDeChiDinhThemDichVuKemTheo": "<PERSON><PERSON><PERSON><PERSON> bệnh không tồn tại xét nghiệm để chỉ định thêm dịch vụ kèm theo", "danhSachChiDinhThuocVTYTHoaChat": "<PERSON><PERSON> s<PERSON>ch chỉ định thuốc, VTYT, h<PERSON><PERSON> chất kèm xét nghiệm", "chiHienThiXNCoDVKemTheo": "Chỉ hiển thị XN có dv kèm theo"}