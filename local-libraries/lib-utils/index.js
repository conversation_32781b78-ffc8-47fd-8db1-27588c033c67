export const centralizedErrorHandling = async (_promise, defaultValue) => {
  try {
    return await _promise;
  } catch (error) {
    return defaultValue;
  }
};

export const toSafePromise = (promise) =>
  promise.then((res) => [null, res]).catch((err) => [err, null]);

export { createUniqueText } from "./createUniqueText";
export { removeVietnameseTones } from "./removeVietnameseTones";

export default {
  centralizedErrorHandling,
};
export const setSearchParams = (
  newParams,
  history,
  searchParams = new URLSearchParams(window.location.search)
) => {
  // Update search params
  Object.entries(newParams).forEach(([key, value]) =>
    value !== null && value !== undefined && value !== ""
      ? searchParams.set(key, JSON.stringify(value))
      : searchParams.delete(key)
  );

  const { state } = history.location;
  history.replace({
    search: searchParams.toString(),
    state,
  });
};

export const getSearchParamsObject = (
  searchParams = new URLSearchParams(window.location.search)
) => {
  const params = {};
  searchParams.forEach((value, key) => {
    try {
      params[key] = value ? JSON.parse(value) : value;
    } catch {
      params[key] = value?.eval();
    }
  });
  return params;
};

export const isIOS = () => {
  if (window.isIOS) return window.isIOS(); // chờ sẵn để có thể config bằng mã tùy chỉnh
  const toMatch = [/iPhone/i, /iPad/i, /iPod/i];

  const ua = navigator.userAgent || navigator.vendor || window.opera;

  // iPad cũ
  const isOldIPad = /iPad/.test(ua);

  // iPad mới (iPadOS giả dạng macOS)
  const isNewIPad =
    (ua.includes("Macintosh") || ua.includes("Safari")) &&
    navigator.maxTouchPoints > 1;

  return (
    isOldIPad ||
    isNewIPad ||
    toMatch.some((toMatchItem) => {
      return navigator.userAgent.match(toMatchItem);
    })
  );
};

export const detectMob = () => {
  if (window.detectMob) return window.detectMob(); // chờ sẵn để có thể config bằng mã tùy chỉnh
  const toMatch = [/Android/i, /webOS/i, /BlackBerry/i, /Windows Phone/i];
  return (
    isIOS() ||
    toMatch.some((toMatchItem) => {
      return navigator.userAgent.match(toMatchItem);
    })
  );
};