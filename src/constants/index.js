import THIET_LAP_CHUNG from "./thietLapChung";
import CACHE_KEY from "./cacheKey";
import ENUM from "./enum";
import ROLES from "./roles";
import VI_TRI_PHIEU_IN from "./viTriPhieuIn";
import MAN_HINH_PHIEU_IN from "./manHinhPhieuIn";
import MA_BIEU_MAU_EDITOR from "./maBieuMauEditor";

window.THIET_LAP_CHUNG = THIET_LAP_CHUNG;

export {
  THIET_LAP_CHUNG,
  ENUM,
  ROLES,
  CACHE_KEY,
  VI_TRI_PHIEU_IN,
  MAN_HINH_PHIEU_IN,
  MA_BIEU_MAU_EDITOR,
};

export const STEP_YEAR_AGE = 6;
export const STEP_MONTH_AGE = 12;
export const STEP_DAY_AGE = 30;
export const STEP_HOURS_AGE = 24;

export const PAGE_SIZE = 10;
export const PAGE_DEFAULT = 0;
export const SIZE_DEFAULT = 10;
export const PAGE_SIZE_LOAD_MORE = 10;
export const MAX_MONTH_AGE = 36;
export const LENGTH_ZERO_PREFIX = 4;
export const FORMAT_DATE = "DD/MM/YYYY";
export const BIRTHDAY_FORMAT = "DD/MM/YYYY";
export const TABLE_LAYOUT = { xl: 14, xxl: 14 };

export const ADD_LAYOUT = { xl: 10, xxl: 10 };
export const TABLE_LAYOUT_COLLAPSE = { xl: 8, xxl: 8 };
export const ADD_LAYOUT_COLLAPSE = { xl: 16, xxl: 16 };
export const NULL_VALUE = "NULL_VALUE";
export const MA_BAO_CAO_HOA_DON_DIEN_TU = "EMR_BA271";
export const HIEU_LUC = [
  {
    id: "true",
    ten: "Có hiệu lực",
    i18n: "danhMuc.coHieuLuc",
  },
  {
    id: "false",
    ten: "Không hiệu lực",
    i18n: "danhMuc.khongHieuLuc",
  },
];
export const CHENH_LECH = [
  {
    id: "",
    ten: "Thừa/thiếu",
  },
  {
    id: "true",
    ten: "Thừa",
  },
  {
    id: "false",
    ten: "Thiếu",
  },
];
export const HAN_CHE_KHOA = [
  {
    id: "true",
    ten: "Có hạn chế",
    i18n: "danhMuc.coHanChe",
  },
  {
    id: "false",
    ten: "Không hạn chế",
    i18n: "danhMuc.khongHanChe",
  },
];

export const TRONG_VIEN = [
  {
    id: true,
    ten: "Trong viện",
    i18n: "baoCao.trongVien",
  },
  {
    id: false,
    ten: "Ra viện",
    i18n: "baoCao.raVien",
  },
];

export const NGOAI_VIEN = [
  {
    id: false,
    ten: "Trong viện",
    i18n: "baoCao.trongVien",
  },
  {
    id: true,
    ten: "Ngoại viện",
    i18n: "danhMuc.ngoaiVien",
  },
];

export const HINH_THUC_TRONG_NGOAI_THAU = [
  { id: true, ten: "Trong thầu", i18n: "kho.trongThau" },
  { id: false, ten: "Ngoài thầu", i18n: "kho.ngoaiThau" },
];

export const THEO_THOI_GIAN_HOA_DON = [
  {
    id: true,
    ten: "Thời gian hóa đơn",
    i18n: "kho.thoiGianHoaDon",
  },
  {
    id: false,
    ten: "Thời gian duyệt",
    i18n: "kho.thoiGianDuyet",
  },
];

export const THEO_NGAY_HOA_DON = [
  {
    id: true,
    ten: "Ngày hóa đơn",
    i18n: "kho.ngayHoaDon",
  },
  {
    id: false,
    ten: "Ngày duyệt phiếu",
    i18n: "khoMau.ngayDuyetPhieu",
  },
];

export const NGOAI_TRU_ICD = [
  {
    id: "false",
    ten: "Theo ICD khai báo",
  },
  {
    id: "true",
    ten: "Ngoại trừ ICD khai báo",
  },
];

export const KHONG_TINH_TIEN = [
  {
    id: "false",
    ten: "Có tính tiền",
    i18n: "common.coTinhTien",
  },
  {
    id: "true",
    ten: "Không tính tiền",
    i18n: "common.khongTinhTien",
  },
];

export const DOT_XUAT = [
  {
    id: "true",
    ten: "Đột xuất",
  },
  {
    id: "false",
    ten: "Hằng ngày",
  },
];

export const KE_VAT_TU_KTC = [
  {
    id: "true",
    ten: "Đã kê",
    i18n: "baoCao.daKe",
  },
  {
    id: "false",
    ten: "Chưa kê",
    i18n: "baoCao.chuaKe",
  },
];

export const TRANG_THAI_LAY_MAU_BN = [
  {
    label: "Chờ tiếp nhận",
    id: "CHO_TIEP_NHAN",
    value: 25,
    i18n: "xetNghiem.choTiepNhan",
  },
  {
    label: "Chờ lấy mẫu",
    id: "CHO_LAY_MAU",
    value: [46, 38, 62],
    i18n: "xetNghiem.choLayMau",
  },
  { label: "Bỏ qua", id: "BO_QUA", value: 50, i18n: "common.boQua" },
  { label: "Hủy mẫu", id: "HUY_MAU", value: 80, i18n: "xetNghiem.huyMau" },
];
export const TRANG_THAI_HHSH_GPB = [
  {
    label: "Đã lấy mẫu",
    id: "DA_LAY_MAU",
    value: 66,
    i18n: "xetNghiem.daLayMau",
  },
  {
    label: "Đã tiếp nhận mẫu",
    id: "DA_TIEP_NHAN_MAU",
    value: 90,
    i18n: "xetNghiem.daTiepNhanMau",
  },
  {
    label: "Đã có KQ",
    id: "DA_CO_KET_QUA",
    value: 155,
    i18n: "xetNghiem.daCoKetQua",
  },
  {
    label: "Đã duyệt KQ",
    id: "DA_DUYET_KET_QUA",
    value: 160,
    i18n: "xetNghiem.daDuyetKetQua",
  },
  {
    label: "Đã xem KQ",
    id: "DA_XEM_KET_QUA",
    value: 170,
    i18n: "common.daXemKetQua",
  },
];

export const TRANG_THAI_CDHA = [
  { label: "Chờ tiếp nhận", i18Key: "cdha.choTiepDon", value: 25 },
  { label: "Đã tiếp nhận", i18Key: "cdha.daTiepNhan", value: 63 },
  { label: "Đã có KQ", i18Key: "xetNghiem.daCoKetQua", value: 155 },
  { label: "Bỏ qua", i18Key: "common.boQua", value: 50 },
];
export const TRANG_THAI_MAU = {
  THUONG: 10,
  HUY_DUYET: 20,
  DA_PHAT: 30,
  YEU_CAU_TRA: 40,
  DA_TRA: 50,
};

export const NOI_DUNG_THANH_TOAN = [
  { id: "Thanh toán tiền thuốc", ten: "Thanh toán tiền thuốc" },
  { id: "Thanh toán tiền sữa", ten: "Thanh toán tiền sữa" },
  { id: "Thanh toán tiền VTYT", ten: "Thanh toán tiền VTYT" },
  { id: "Thanh toán tiền TPCN", ten: "Thanh toán tiền TPCN" },
  { id: "Thanh toán tiền Mỹ phẩm", ten: "Thanh toán tiền Mỹ phẩm" },
];

export const LOAI_MIEN_GIAM = [
  {
    id: 1,
    ten: "Giảm theo dịch vụ của gói",
    i18n: "thuNgan.giamTheoDichVuCuaGoi",
  },
  {
    id: 2,
    ten: "Giảm theo tất cả dịch vụ của hợp đồng",
    i18n: "thuNgan.giamTheoTatCaDichVuCuaHopDong",
  },
];
export const DOI_TUONG_UU_TIEN = [
  {
    id: 1,
    ten: "Cấp cứu",
    i18n: "pttt.capCuu",
  },
  {
    id: 2,
    ten: "Trẻ em dưới 6 tuổi",
    i18n: "kiosk.treEmDuoi6Tuoi",
  },
  {
    id: 3,
    ten: "Người khuyết tật nặng",
    i18n: "kiosk.nguoiKhuyetTatNang",
  },
  {
    id: 4,
    ten: "Người từ 80 tuổi trở lên",
    i18n: "kiosk.nguoiTu80TuoiTroLen",
  },
  {
    id: 5,
    ten: "Người có công với cách mạng",
    i18n: "kiosk.nguoiCoCongVoiCachMang",
  },
  {
    id: 6,
    ten: "Phụ nữ có thai",
    i18n: "kiosk.phuNuCoThai",
  },
];

export const YES_NO = [
  {
    id: "true",
    ten: "Có",
    i18n: "common.co",
  },
  {
    id: "false",
    ten: "Không",
    i18n: "common.khong",
  },
];
export const DA_KIEM_TRA = [
  {
    id: "false",
    ten: "Chưa kiểm tra",
    i18n: "tiepDon.chuaKiemTra",
  },
  {
    id: "true",
    ten: "Đã kiểm tra",
    i18n: "tiepDon.daKiemTra",
  },
];

export const DOI_TUONG = {
  KHONG_BAO_HIEM: 1,
  BAO_HIEM: 2,
};

export const TRANG_THAI_THANH_TOAN = [
  {
    id: false,
    ten: "Chưa thanh toán",
    i18n: "thuNgan.chuaThanhToan",
  },
  {
    id: true,
    ten: "Đã thanh toán",
    i18n: "thuNgan.daThanhToan",
  },
];

export const TRANG_THAI_XUAT_HOA_DON = [
  {
    id: "true",
    ten: "Đã xuất hóa đơn",
    i18n: "thuNgan.daXuatHoaDon",
  },
  {
    id: "false",
    ten: "Chưa xuất hóa đơn",
    i18n: "thuNgan.chuaXuatHoaDon",
  },
];

export const TRANG_THAI_THANH_TOAN_PHIEU_THU = [
  {
    id: -1,
    ten: "Chưa thanh toán",
    i18n: "thuNgan.chuaThanhToan",
  },
  {
    id: 50,
    ten: "Đã thanh toán",
    i18n: "thuNgan.daThanhToan",
  },
];

export const TRANG_THAI_QR = [
  {
    id: 4,
    ten: "Chưa thanh toán chưa duyệt",
    i18n: "baoCao.chuaThanhToanChuaDuyet",
  },
  {
    id: 1,
    ten: "Chưa thanh toán đã duyệt",
    i18n: "baoCao.chuaThanhToanDaDuyet",
  },
  {
    id: 2,
    ten: "Đã thanh toán chưa duyệt",
    i18n: "baoCao.daThanhToanChuaDuyet",
  },
  {
    id: 3,
    ten: "Đã thanh toán đã duyệt",
    i18n: "baoCao.daThanhToanDaDuyet",
  },
];

export const TRANG_THAI_THANH_TOAN_NB = [
  {
    id: 100,
    ten: "Chưa thanh toán",
  },
  {
    id: 120,
    ten: "Đã thanh toán",
  },
];

export const LOAI_KHACH_HANG = [
  {
    id: "10",
    ten: "Khách hàng doanh nghiệp",
    i18n: "baoCao.khachHangDoanhNghiep",
  },
  {
    id: "0,1,2,3,5,6,15",
    ten: "Khách hàng cá nhân",
    i18n: "baoCao.khachHangCaNhan",
  },
];

export const DS_DOI_TUONG_BAO_HIEM = [
  {
    id: 1,
    ten: "Không bảo hiểm",
    i18n: "khamBenh.hanhChinh.khongBaoHiem",
  },
  {
    id: 2,
    ten: "Bảo hiểm",
    i18n: "baoCao.baoHiem",
  },
];

export const LOAI_DICH_VU = {
  KHAM: 10,
  XET_NGHIEM: 20,
  CDHA: 30,
  PHAU_THUAT_THU_THUAT: 40,
  GOI_PHAU_THUAT_THU_THUAT: 45,
  SUAT_AN: 50,
  NGOAI_DIEU_TRI: 60,
  VAN_CHUYEN: 70,
  THUOC: 90,
  VAC_XIN: 95,
  VAT_TU: 100,
  HOA_CHAT: 110,
  CHE_PHAM_MAU: 120,
  CHE_PHAM_DINH_DUONG: 125,
  GIUONG: 130,
  GOI_DICH_VU: 150,
  BO_CHI_DINH: 150,
  TIEP_DON: 200,
  NOI_TRU: 201,
  TO_DIEU_TRI: 210,
  NHA_THUOC: 220,
  DAT_KHAM: 230,
  CRM: 240,
  GOI_KSK: 250,
  // GOI_DICH_VU: 260,
  PHCN: 270,
  PHIEU_XUAT_KHO: 280,
  PHIEU_PHA_THUOC: 290,
};

export const LOAI_TO_DIEU_TRI = {
  TRONG_VIEN: 10,
  PHCN: 12,
  RA_VIEN: 20,
  DON_THUOC_30_NGAY: 30,
};

export const DOI_TUONG_SU_DUNG = {
  TIEP_DON: 10,
  NB_NGOAI_TRU: 20,
  NB_NOI_TRU: 30,
  NB_KHAM_SUC_KHOE: 40,
  NB_TIEM_CHUNG: 50,
  THUC_HIEN_TAI_KHOA: 60,
  BENH_NHI_SO_SINH: 130,
};

export const TY_LE_THANH_TOAN = [
  {
    id: 100,
    ten: "100%",
  },
  {
    id: 80,
    ten: "80%",
  },
  {
    id: 50,
    ten: "50%",
  },
];

export const LOAI_PHIEU_THU = {
  PHIEU_THU_TONG: 0,
  KHONG_BAO_HIEM: 1,
  BAO_HIEM: 2,
  PHU_THU: 3,
  NGOAI_DIEU_TRI: 5,
  NHA_THUOC: 6,
  KHAM_SUC_KHOE: 10,
  GHI_DICH_VU: 15,
};

//=========== Giới Tính
export const GIOI_TINH = {
  NAM: 1,
  NU: 2,
  CHUA_XAC_DINH: 3,
};

export const GIOI_TINH_BY_VALUE = {
  1: "Nam",
  2: "Nữ",
};

export const LOAI_PHONG = {
  LAM_SANG: 10,
  CAN_LAM_SANG: 20,
  PHONG_KHAM: 30,
  LAY_MAU_BENH_PHAM: 40,
  PHONG_GIUONG: 50,
  PHONG_GIUONG_TU_CHON: 60,
  PHONG_KHAM_TIEM_CHUNG: 70,
  PHONG_TIEM_CHUNG: 80,
  KHAC: 200,
};

export const SORT_LOAD_MORE = "ten,asc";

export const THANH_TOAN_SAU = [
  {
    id: "true",
    ten: "Thanh toán sau",
    i18n: "danhMuc.thanhToanSau",
  },
  {
    id: "false",
    ten: "Không thanh toán sau",
    i18n: "danhMuc.khongThanhToanSau",
  },
];

export const SORT_DEFAULT_DICH_VU = {
  active: 2,
  "dichVu.ma": 1,
  updatedOn: 2,
};

export const TRANG_THAI_DICH_VU = {
  CHO_TIEP_DON_CLS: 15,
  CHO_KHAM: 20,
  CHO_TIEP_NHAN: 25,
  DA_CHECKIN_KHAM: 30,
  DA_CHECKIN: 35,
  CHO_LAY_MAU: 38,
  CHUAN_BI_KHAM: 40,
  CHUAN_BI_THUC_HIEN: 43,
  CHUAN_BI_LAY_MAU: 46,
  BO_QUA: 50,
  DANG_KHAM: 60,
  DA_TIEP_NHAN: 63,
  DA_LAU_MAU: 66,
  DANG_THUC_HIEN_DICH_VU: 70,
  HUY_MAU: 80,
  TIEP_NHAN_MAU: 90,
  CHO_KET_LUAN: 100,
  DA_CHECKIN_KET_LUAN: 110,
  CHUAN_BI_KET_LUAN: 120,
  BO_QUA_KET_LUAN: 130,
  DANG_KET_LUAN: 140,
  DA_KET_LUAN: 150,
  DA_CO_KET_QUA: 155,
  DA_DUYET: 160,
  YEU_CAU_HOAN: [15, 20, 25, 30, 35, 40, 43, 50],
  DA_CHECKIN_DICH_VU: [30, 40, 60, 110, 120, 140, 38, 46, 62, 35, 43],
  DA_CHUYEN_HOI_TINH: 95,
  DA_HOAN_TAT_DICH_VU: 170,
  CHO_LAY_MAU_XET_NGHIEM: [46, 38, 62],
};

export const TRANG_THAI_DV_KHONG_THUC_HIEN = {
  KHAM: [20, 40, 50],
  XET_NGHIEM: 25,
  CDHA: [15, 25, 35, 43, 50, 63],
  PTTT: [25, 43, 63],
};

export const THUC_HIEN_DICH_VU = [
  {
    id: false,
    ten: "Chưa thực hiện",
    i18n: "khamBenh.chuaThucHien",
  },
  {
    id: true,
    ten: "Đã thực hiện",
    i18n: "goiDichVu.daThucHien",
  },
];

export const HIEN_THI_THEO_BAC_SI = [
  {
    id: true,
    ten: "Bác sĩ chỉ định",
    i18n: "baoCao.bacSiChiDinh",
  },
  {
    id: false,
    ten: "Bác sĩ thực hiện",
    i18n: "baoCao.bacSiThucHien",
  },
];

export const TRANG_THAI_NB_DONG_HO_SO = [
  {
    id: 50,
    ten: "Đã đóng hồ sơ",
    i18n: "baoCao.daDongHoSo",
  },
  {
    id: 10,
    ten: "Chưa đóng hồ sơ",
    i18n: "baoCao.chuaDongHoSo",
  },
  {
    id: 20,
    ten: "Từ chối duyệt chi phí",
    i18n: "common.tuChoiDuyetChiPhi",
  },
  {
    id: 30,
    ten: "Chờ duyệt",
    i18n: "common.choDuyet",
  },
  {
    id: 40,
    ten: "Đã duyệt",
    i18n: "quanLyNoiTru.daDuyet",
  },
];

export const SO_LUONG_DICH_VU = [
  {
    id: false,
    ten: "Dịch vụ đã thanh toán",
    i18n: "baoCao.dichVuDaThanhToan",
  },
  {
    id: true,
    ten: "Dịch vụ hoàn sau thanh toán",
    i18n: "baoCao.dichVuHoanSauThanhToan",
  },
];

//=================================== kho
export const TRANG_THAI_PHIEU = [
  { ten: "Tạo mới", id: 10, i18n: "common.taoMoi" },
  { ten: "Tạo mới, đã giữ chỗ", id: 15, i18n: "common.taoMoiDaGiuCho" },
  { ten: "Chờ duyệt", id: 20, i18n: "common.choDuyet" },
  { ten: "Chờ xác nhận nhập", id: 28, i18n: "kho.choXacNhanNhap" },
  { ten: "Hoàn thành", id: 30, i18n: "common.hoanThanh" },
];
export const LOAI_PHIEU_XUAT = [
  { ten: "Xem phiếu duyệt dự trù", id: 1, i18n: "kho.xemPhieuDuyetDuTru" },
  { ten: "Xem phiếu xuất", id: 2, i18n: "kho.xemPhieuXuat" },
  { ten: "Xem phiếu lĩnh", id: 3, i18n: "kho.xemPhieuLinh" },
  { ten: "Xuất vắc xin tiêm", id: 4, i18n: "kho.xuatVacXinTiem" },
  { ten: "Khoa trả về kho", id: 5, i18n: "kho.khoaTraVeKho" },
  { ten: "Xem phiếu đảo hạn sử dụng", id: 6, i18n: "kho.xemPhieuDaoHanSuDung" },
];

export const TK_TRANG_THAI_PHIEU_NHAP_DU_TRU = [
  { label: "Chờ duyệt", value: 20 },
  { label: "Hoàn thành", value: 30 },
];
export const TK_TRANG_THAI_PHIEU_NHAP_XUAT = [
  { label: "Tạo mới", value: 10 },
  { label: "Tạo mới, đã giữ chỗ", value: 15 },
  { label: "Chờ duyệt", value: 20 },
  { label: "Hoàn thành", value: 30 },
];

export const TRANG_THAI_DON_THUOC = [
  // { label: "Tất cả", value: 10 },
  { label: "Tạo mới", value: 15 }, // 10 , 15 , 20 = tạo mới
  { label: "Đã phát", value: 30 }, // 30 , 35  = đã phát => số liệu anh Minh đưa
  // { label: "Tất cả", value: 10 },
  // { label: "Chưa giữ chỗ", value: 20 },
  // { label: "Chờ phát", value: 30 },
  // { label: "Đã phát", value: 40 },
];

export const TRANG_THAI_PHIEU_LINH = [
  { ten: "Tạo mới", id: 10, i18n: "common.taoMoi" },
  { ten: "Tạo mới, đã giữ chỗ", id: 15, i18n: "common.taoMoiDaGiuCho" },
  { ten: "Chờ duyệt", id: 20, i18n: "common.choDuyet" },
];

export const THANG_DU_TRU = [
  { id: "1", ten: "Tháng 1", typeQuarter: "1", i18n: "common.thang1" },
  { id: "2", ten: "Tháng 2", typeQuarter: "1", i18n: "common.thang2" },
  { id: "3", ten: "Tháng 3", typeQuarter: "1", i18n: "common.thang3" },
  { id: "4", ten: "Tháng 4", typeQuarter: "2", i18n: "common.thang4" },
  { id: "5", ten: "Tháng 5", typeQuarter: "2", i18n: "common.thang5" },
  { id: "6", ten: "Tháng 6", typeQuarter: "2", i18n: "common.thang6" },
  { id: "7", ten: "Tháng 7", typeQuarter: "3", i18n: "common.thang7" },
  { id: "8", ten: "Tháng 8", typeQuarter: "3", i18n: "common.thang8" },
  { id: "9", ten: "Tháng 9", typeQuarter: "3", i18n: "common.thang9" },
  { id: "10", ten: "Tháng 10", typeQuarter: "4", i18n: "common.thang10" },
  { id: "11", ten: "Tháng 11", typeQuarter: "4", i18n: "common.thang11" },
  { id: "12", ten: "Tháng 12", typeQuarter: "4", i18n: "common.thang12" },
];
export const DANH_SACH_QUY = [
  { id: "1", ten: "Quý 1" },
  { id: "2", ten: "Quý 2" },
  { id: "3", ten: "Quý 3" },
  { id: "4", ten: "Quý 4" },
];

export const HINH_THUC_NHAP_XUAT = {
  HINH_THUC_NHAP: 10,
  LOAI_XUAT: 20,
};

export const LOAI_CHIET_KHAU = {
  PHAN_TRAM: 1,
  TIEN: 2,
};

export const DATA_TIME_QMS = [
  {
    title: "qms.sangTu",
    value: "thoiGianSangTu",
  },
  {
    title: "qms.den",
    value: "thoiGianSangDen",
  },
  {
    title: "qms.chieuTu",
    value: "thoiGianChieuTu",
  },
  {
    title: "qms.den",
    value: "thoiGianChieuDen",
  },
];

export const TRANG_THAI_DIEU_TRI = [
  {
    ten: "Đang theo dõi",
    id: false,
    i18n: "theoDoiDieuTri.dangTheoDoi",
  },
  {
    ten: "Đã kết thúc theo dõi",
    id: true,
    i18n: "theoDoiDieuTri.daKetThucTheoDoi",
  },
];

export const NHOM_DANH_MUC = {
  CHUYEN_KHOA_MAT: 0,
  CAP_CUU: 1,
  KHO: 2,
  KY_IN_PHIEU: 3,
  DICH_VU: 4,
  HANH_CHINH: 5,
  CHUNG: 6,
  KHACH_HANG: 7,
  COM_TOOL: 8,
};

export const NHOM_BAO_CAO = {
  BAO_CAO_TAI_CHINH: 0,
  BAO_CAO_DICH_VU: 1,
  BAO_CAO_PHONG_KHAM: 2,
  BAO_CAO_KHO: 3,
  BAO_CAO_KHO_VT_HC: 4,
  BAO_CAO_KHO_NHA_THUOC: 5,
  BAO_CAO_KHAM_SUC_KHOE: 6,
  BAO_CAO_GOI_LIEU_TRINH: 7,
  BAO_CAO_KE_HOACH_TONG_HOP: 8,
  BAO_CAO_PHAU_THUAT_THU_THUAT: 9,
  BAO_CAO_SUAT_AN: 10,
  BAO_CAO_TIEM_CHUNG: 11,
  BAO_CAO_KSNK: 12,
  BAO_CAO_LAO: 13,
  BAO_CAO_KHO_DINH_DUONG: 14,
  BAO_CAO_QT: 15,
  BAO_CAO_DDLS: 16,
  BAO_CAO_QUYET_TOAN_BAO_HIEM: 17,
  BAO_CAO_SANG_LOC_DD: 18,
  BAO_CAO_PHA_CHE_THUOC: 19,
};

export const MUC_DO_UU_TIEN = [
  {
    id: 10,
    ten: "5",
  },
  {
    id: 20,
    ten: "4",
  },
  {
    id: 30,
    ten: "3",
  },
  {
    id: 40,
    ten: "2",
  },
  {
    id: 50,
    ten: "1",
  },
];
export const TRANG_THAI_PHIEU_HOAN = [
  { label: "Chờ Hoàn", value: 15 }, // 10 , 15 , 20 = tạo mới
  { label: "Hoàn Thành", value: 30 }, // 30 , 35  = đã phát => số liệu anh Minh đưa
];

export const A4 = {
  width: 838,
  height: 1185, //  1.41428571429 =  29.7/21 size A3,
};
export const A3 = {
  width: 838 * 2,
  height: 1185 * 2,
};

export const LOAI_PHIEU_NHAP = [
  { ten: "Nhập từ nhà cung cấp", id: 10, i18n: "kho.nhapTuNhaCungCap" },
  { ten: "Nhập khác", id: 12, i18n: "pttt.nhapKhac" },
  { ten: "Dự trù", id: 20, i18n: "kho.duTru" },
  { ten: "Chuyển kho", id: 30, i18n: "kho.chuyenKho" },
  { ten: "Phiếu trả", id: 70, i18n: "kho.phieuTra" },
  {
    ten: "Lĩnh bù tủ trực",
    id: 80,
    i18n: "quanLyNoiTru.phieuLinh.linhBuTuTruc",
  },
  {
    ten: "Đảo hạn sử dụng",
    id: 45,
    i18n: "kho.daoHanSuDung",
  },
];

// 1 - asc || 2 - desc
export const SORT_DEFAULT = { active: 2, ma: 1, updatedOn: 2 };

export const LOAI_VAT_TU = [
  { id: true, ten: "Chạy máy", i18n: "baoCao.chayMay" },
  { id: false, ten: "Không chạy máy", i18n: "baoCao.khongChayMay" },
];

export const HOA_DON_BBBG = [
  { id: 1, ten: "Hóa đơn" },
  { id: 2, ten: "BBBG" },
];

export const TRANG_THAI_PHIEU_NHAP_XUAT = {
  TAO_MOI: 10,
  TAO_MOI_DA_GIU_CHO: 15,
  CHO_DUYET: 20,
  CHO_XAC_NHAN_NHAP: 28,
  HOAN_THANH: 30,
  DA_PHAT: 35,
};

export const TRANG_THAI_HOA_DON = {
  HD_TAO_MOI: 10,
  HD_PHAT_HANH_LOI: 15,
  HD_DA_PHAT_HANH: 20,
  HD_CHO_XOA_BO: 30,
  HD_XOA_BO: 40,
  HD_CHO_DIEU_CHINH: 45,
  HD_DIEU_CHINH: 50,
};

export const DOI_TUONG_KCB_NOI_TRU = {
  NGOAI_TRU: 1,
  DIEU_TRI_NGOAI_TRU: 2,
  DIEU_TRI_NOI_TRU: 3,
  DIEU_TRI_NOI_TRU_BAN_NGAY: 4,
};

export const BASE_LAYOUT = {
  default: {
    table: { xl: 14, xxl: 14 },
    form: { xl: 10, xxl: 10 },
  },
  collapse: {
    table: { xl: 8, xxl: 8 },
    form: { xl: 16, xxl: 16 },
  },
  fullTable: {
    table: { xl: 24, xxl: 24 },
    form: { xl: 0, xxl: 0 },
  },
};

export const TRANG_THAI_NB = {
  // enum trangThaiNb
  MOI_TIEP_DON: 5,
  CHO_LAP_BENH_AN: 10,
  CHO_TIEP_NHAN_VAO_KHOA: 20,
  DANG_DIEU_TRI: 30,
  DANG_CHUYEN_KHOA: 40,
  TAM_DUNG: 45,
  CHO_HOAN_TAT_THU_TUC_RA_VIEN: 50,
  DA_RA_VIEN: 100,
  TU_CHOI_DUYET_CHI_PHI: 102,
  CHO_DUYET_CHI_PHI: 103,
  DA_DUYET_CHI_PHI: 106,
  HEN_DIEU_TRI: 110,
  TU_CHOI_DUYET_CHI_PHI_HEN_DIEU_TRI: 112,
  CHO_DUYET_CHI_PHI_HEN_DIEU_TRI: 113,
  DA_DUYET_CHI_PHI_HEN_DIEU_TRI: 116,
  DA_THANH_TOAN_RA_VIEN: 120,
  DA_THANH_TOAN_HEN_DIEU_TRI: 130,
  DA_TIEP_DON_HEN_DIEU_TRI: 135,
  HUY_BENH_AN: 200,
};
export const DANH_SACH_LOAI_NHAP_XUAT = [
  {
    ten: "Hóa đơn bán thuốc",
    value: "120",
    _value: [120],
    i18n: "nhaThuoc.hoaDonBanThuoc",
  },
  {
    ten: "Phiếu xuất",
    value: "40, 90",
    _value: [40, 90],
    i18n: "nhaThuoc.phieuXuat",
  },
  { ten: "Phiếu nhập", value: "10", _value: [10], i18n: "nhaThuoc.phieuNhap" },
];

export const HINH_THUC_MIEN_GIAM = {
  MIEN_GIAM_THEO_PHIEU_THU: 10,
};
export const TRANG_THAI_NB_GOI_DV = {
  TAO_MOI: 10,
  DANG_SU_DUNG: 20,
  HUY_SU_DUNG: 30,
  DUNG_SU_DUNG: 40,
  KET_THUC: 50,
};
export const TRANG_THAI_NB_GOI_DV_TAM_UNG = {
  THANH_TOAN: 10,
  HUY_THANH_TOAN: 20,
};

export const NHOM_NGUON_NGUOI_BENH = {
  KHACH_MOI_ONLINE: 10,
  KHACH_MOI_OFFLINE: 20,
  KHACH_MOI_TU_DEN: 30,
  KHACH_CU: 40,
  CONG_TAC_VIEN: 50,
};

export const HUONG_DIEU_TRI_KHAM = {
  CHO_VE: 10,
  HEN_KHAM: 20,
  NHAP_VIEN: 30,
  CHUYEN_VIEN: 40,
  CHUYEN_VIEN_THEO_YEU_CAU: 42,
  KHONG_KHAM: 100,
  CHUYEN_KHAM_CHUYEN_SAU: 110,
  THEO_DOI_NGUOI_BENH: 120,
  CHUYEN_VE_CO_SO: 130,
  CAP_DON_CHO_VE: 135,
  KHAM_VE_BO_DON: 160,
};

export const KET_QUA_KHAM = {
  KHOI: 1,
  DO: 2,
  KHONG_THAY_DOI: 3,
  NANG_HON: 4,
  TU_VONG: 5,
  KHONG_DANH_GIA: 10,
};

export const HOTKEY = {
  F1: 112,
  F2: 113,
  F3: 114,
  F4: 115,
  F5: 116,
  F6: 117,
  F7: 118,
  F8: 119,
  F9: 120,
  F10: 121,
  F11: 122,
  F12: 123,
  ESC: 27,
  TAB: 9,
  S: 83,
  UP: 38,
  A: 65,
  Q: 81,
  DOWN: 40,
  ENTER: 13,
  NUM_0: 96,
  NUM_1: 97,
  NUM_2: 98,
  NUM_3: 99,
  NUM_4: 100,
  NUM_5: 101,
  NUM_6: 102,
  NUM_7: 103,
  NUM_8: 104,
  NUM_9: 105,
  CHAR_0: 48,
  CHAR_1: 49,
  CHAR_2: 50,
  CHAR_3: 51,
  CHAR_4: 52,
  CHAR_5: 53,
  CHAR_6: 54,
  CHAR_7: 55,
  CHAR_8: 56,
  CHAR_9: 57,
  BACKSPACE: 8,
  SPACE: 32,
  PAGE_UP: 33,
  PAGE_DOWN: 34,
};

export const LIST_PHIEU_IN_BANG_KE = [
  "P032",
  "P062",
  "P107",
  "P178",
  "P541",
  "P554",
  "P678",
  "P677",
  "P727",
  "P746",
  "P747",
];

export const LIST_PHIEU_IN_TRUYEN_THEM_KHOA_CHI_DINH = [
  "P086",
  "P092",
  "P183",
  "P184",
  "P197",
  "P531",
  "P532",
  "P533",
  "P534",
  "P535",
  "P536",
  "P606",
  "P637",
  "P638",
  "P666",
  "P667",
  "P669",
  "P670",
  "P664",
  "P665",
  "P689",
  "P688",
  "P036",
  "P212",
  "P054",
  "P975",
];

export const LIST_PHIEU_IN_EDITOR = [
  "P136",
  "P148",
  "P158",
  "P181",
  "P183",
  "P184",
  "P185",
  "P191",
  "P197",
  "P618",
  "P619",
  "P086",
];

export const LIST_PHIEU_IN_EDITOR_THEO_SO_PHIEU = [
  "P054",
  "P073",
  "P087",
  "P108",
  "P135",
  "P137",
  "P139",
  "P143",
  "P144",
  "P149",
  "P161",
  "P164",
  "P167",
  "P168",
  "P188",
  "P208",
  "P207",
  "P212",
  "P215",
  "P228",
  "P229",
  "P230",
  "P231",
  "P233",
  "P234",
  "P235",
  "P236",
  "P237",
  "P238",
  "P239",
  "P186",
  "P242",
  "P254",
  "P088",
  "P244",
  "P245",
  "P259",
  "P248",
  "P249",
  "P250",
  "P252",
  "P253",
  "P261", //Giấy cam đoan chấp nhận sử dụng dịch vụ y tế, trang thiết bị dụng cụ PT KTC
  "P263",
  "P266",
  "P268",
  "P271",
  "P273",
  "P275", //Bảng kiểm an toàn trong phẫu thuật
  "P277",
  "P282",
  "P281",
  "P283",
  "P284",
  "P280",
  "P290",
  "P295",
  "P296",
  "P297",
  "P322",
  "P323",
  "P324",
  "P361",
  "P366",
  "P368",
  "P372", //Phiếu xét nghiệm vi khuẩn lao
  "P373", //Phiếu xét nghiệm vi khuẩn lao
  "P370",
  "P376",
  "P377",
  "P381",
  "P386",
  "P395",
  "P396",
  "P397",
  "P407",
  "P408",
  "P409",
  "P410",
  "P414",
  "P415",
  "P421",
  "P422",
  "P423",
  "P438",
  "P440",
  "P451",
  "P452",
  "P453",
  "P454",
  "P457",
  "P458",
  "P459",
  "P460",
  "P461",
  "P462",
  "P479",
  "P483",
  "P484",
  "P486",
  "P487",
  "P500",
  "P488",
  "P466",
  "P467",
  "P503",
  "P504",
  "P505",
  "P506",
  "P507",
  "P508",
  "P509",
  "P510",
  "P511",
  "P512",
  "P513",
  "P514",
  "P515",
  "P518",
  "P519",
  "P522",
  "P525",
  "P526",
  "P527",
  "P528",
  "P529",
  "P530",
  "P538",
  "P539",
  "P547",
  "P548",
  "P550",
  "P551",
  "P556",
  "P557",
  "P558",
  "P562",
  "P569",
  "P577",
  "P578",
  "P583",
  "P584",
  "P585",
  "P586",
  "P587",
  "P588",
  "P589",
  "P590",
  "P592",
  "P593",
  "P594",
  "P595",
  "P596",
  "P597",
  "P609",
  "P608",
  "P599",
  "P600",
  "P602",
  "P607",
  "P604",
  "P603",
  "P571",
  "P576",
  "P612",
  "P622",
  "P623",
  "P626",
  "P627",
  "P628",
  "P615",
  "P614",
  "P633",
  "P634",
  "P635",
  "P636",
  "P640",
  "P641",
  "P637",
  "P638",
  "P642",
  "P643",
  "P644",
  "P645",
  "P617",
  "P618",
  "P619",
  "P658",
  "P659",
  "P660",
  "P661",
  "P662",
  "P666",
  "P667",
  "P646",
  "P647",
  "P669",
  "P670",
  "P676",
  "P664",
  "P665",
  "P679",
  "P681",
  "P682",
  "P685",
  "P686",
  "P689",
  "P688",
  "P690",
  "P691",
  "P692",
  "P693",
  "P695",
  "P694",
  "P698",
  "P699",
  "P700",
  "P701",
  "P702",
  "P703",
  "P704",
  "P705",
  "P706",
  "P708",
  "P709",
  "P710",
  "P711",
  "P719",
  "P720",
  "P721",
  "P722",
  "P723",
  "P724",
  "P725",
  "P726",
  "P730",
  "P731",
  "P733",
  "P732",
  "P738",
  "P739",
  "P740",
  "P741",
  "P744",
  "P745",
  "P751",
  "P752",
  "P758",
  "P759",
  "P760",
  "P763",
  "P764",
  "P765",
  "P768",
  "P769",
  "P770",
  "P771",
  "P772",
  "P773",
  "P774",
  "P776",
  "P777",
  "P778",
  "P779",
  "P780",
  "P756",
  "P757",
  "P754",
  "P750",
  "P755",
  "P753",
  "P781",
  "P782",
  "P783",
  "P784",
  "P791",
  "P792",
  "P793",
  "P794",
  "P796",
  "P797",
  "P799",
  "P805",
  "P806",
  "P808",
  "P810",
  "P811",
  "P815",
  "P818",
  "P812",
  "P821",
  "P822",
  "P823",
  "P825",
  "P827",
  "P830",
  "P834",
  "P835",
  "P836",
  "P838",
  "P839",
  "P840",
  "P844",
  "P845",
  "P857",
  "P858",
  "P859",
  "P860",
  "P861",
  "P871",
  "P872",
  "P877",
  "P878",
  "P879",
  "P880",
  "P885",
  "P888",
  "P889",
  "P890",
  "P891",
  "P893",
  "P896",
  "P897",
  "P899",
  "P901",
  "P902",
  "P910",
  "P911",
  "P923",
  "P931",
  "P934",
  "P938",
  "P942",
  "P945",
  "P950",
  "P952",
  "P953",
  "P960",
  "P962",
  "P972",
  "P977",
  "P989",
  "P958",
  "P984",
  "P955",
  "P995",
  "P886",
  "P964",
  "P976",
  "P1029",
  "P1048",
  "P1053",
  "P1073",
  "P1062",
  "P1058",
  "P1031",
  "P1062",
  "P1070",
  "P1068",
  "P1037",
  "P1033",
  "P1089",
  "P1085",
  "P1110",
  "P1117",
  "P1021",
  "P1066",
  "P873",
  "P1094",
  "P1195",
  "P1196",
  "P1198",
  "P1197",
  "P1218",
  "P1220",
  "P1237",
  "P1238",
  "P1243",
  "P1244",
  "P151",
  "P11131",
  "P11128",
  "P11130",
  "P11125",
];

export const LIST_PHIEU_IN_THEO_TEN_LEVEL2 = [
  "P054",
  "P087",
  "P143",
  "P205",
  "P206",
  "P207",
  "P208",
  "P212",
  "P244",
  "P245",
  "P249",
  "P250",
  "P252",
  "P259",
  "P233",
  "P086",
  "P234",
  "P290",
  "P407",
  "P408",
  "P415",
  "P440",
  "P539",
  "P549",
  "P571",
  "P606",
  "P623",
  "P626",
  "P629",
  "P972",
];

export const LIST_PHIEU_KQ_XN_KHONG_CALL_API = [
  "P330", //Kết quả xét nghiệm Huyết đồ
  "P331", //Kết quả xét nghiệm Huyết học
  "P332", //Kết quả xét nghiệm Sinh hóa
  "P333", //Kết quả xét nghiệm Tế bào cặn nước tiểu
  "P334", //Kết quả xét nghiệm Nước tiểu
  "P335", //Kết quả xét nghiệm ROTEM
  "P336", //Kết quả xét nghiệm Các bệnh truyền nhiễm
  "P337", //Kết quả xét nghiệm Vi sinh
  "P338", //Kết quả xét nghiệm Đông máu
  "P339", //Kết quả xét nghiệm Chiều cao
  "P340", //Kết quả xét nghiệm Miễn dịch
  "P341", //Kết quả xét nghiệm Giải phẫu bệnh
  "P342", //Kết quả xét nghiệm Định nhóm máu ABO gelcard
  "P343", //Phiếu kết quả xét nghiệm phản ứng hòa hợp
  "P344", //Kết quả xét nghiệm dịch não tủy
  "P345", //Phiếu kết quả xét nghiệm sinh học phân tử
  "P346", //Kết quả xét nghiệm Truyền máu
  "P347", //Kết quả xét nghiệm Khác
];

export const LIST_PHIEU_KQ_XN_CDHA = [
  "P330", //Kết quả xét nghiệm Huyết đồ
  "P331", //Kết quả xét nghiệm Huyết học
  "P332", //Kết quả xét nghiệm Sinh hóa
  "P333", //Kết quả xét nghiệm Tế bào cặn nước tiểu
  "P334", //Kết quả xét nghiệm Nước tiểu
  "P335", //Kết quả xét nghiệm ROTEM
  "P336", //Kết quả xét nghiệm Các bệnh truyền nhiễm
  "P337", //Kết quả xét nghiệm Vi sinh
  "P338", //Kết quả xét nghiệm Đông máu
  "P339", //Kết quả xét nghiệm Chiều cao
  "P340", //Kết quả xét nghiệm Miễn dịch
  "P341", //Kết quả xét nghiệm Giải phẫu bệnh
  "P342", //Kết quả xét nghiệm Định nhóm máu ABO gelcard
  "P343", //Phiếu kết quả xét nghiệm phản ứng hòa hợp
  "P344", //Kết quả xét nghiệm dịch não tủy
  "P345", //Phiếu kết quả xét nghiệm sinh học phân tử
  "P346", //Kết quả xét nghiệm Truyền máu
  "P347", //Kết quả xét nghiệm Khác
  "P348", //Kết quả Can thiệp
  "P349", //Kết quả chụp Cắt lớp vi tính
  "P350", //Kết quả chụp cộng hưởng từ
  "P351", //Kết quả chụp X-Quang
  "P352", //Kết quả DSA
  "P353", //Kết quả Nội soi
  "P354", //Kết quả Siêu âm
  "P355", //Kết quả Thăm dò chức năng,
  "P023",
  "P022",
];

export const LIST_PHIEU_KHONG_HUY_KY = [
  "P220",
  "P221",
  "P222",
  "P223",
  "P327",
  "P328",
  "P329",
];

export const LIST_PHIEU_IN_WORD_THEO_SO_PHIEU = [
  "P001", // Phiếu tổng hợp khám bệnh
  "P016", //chỉ định xét nghiệm khám bệnh,
  "P017", //chỉ định cdha khám bệnh
  "P018", //phiếu khám ở mh hsba
  "P024", //Phiếu thủ thuật
  "P025", //đơn thuốc ở mh hsba
  "P028", //đơn thuốc ở mh khám bệnh
  "P019", //chỉ định xét nghiệm
  "P020", //chỉ định cdha
  "P021", //chỉ định pttt
  "P027", //Đơn thuốc tổng hợp
  "P046", //chỉ định pttt khám bệnh,
  "P048", //phiếu chỉ định ở mh khám bệnh
  "P054",
  "P057", //phiếu chỉ định ở mh nội trú
  "P059", //Phiếu phẫu thuật
  "P060", //Giấy chứng nhận phẫu thuật
  "P064", //Phiếu thủ thuật
  "P065", //Giấy chứng nhận thủ thuật
  "P066", //Phiếu chỉ định dịch vụ ngoài điều trị
  "P069", //bảng kê chi phí thuốc trong phẫu thuật
  "P071", //Phiếu ghi thanh toán tiền phẫu thuật
  "P075", //Phiếu chỉ định tổng hợp
  "P114", //Phiếu phẫu thuật
  "P194", //Đơn thuốc vắc xin
  "P205", //phiếu trích biên bản hội chẩn
  "P206", //Giấy đề nghị hội chẩn
  "P207", //Giấy mời hội chẩn
  "P208", //biên bản hội chẩn
  "P212",
  "P214", //phiếu sơ kết
  "P219", //phiếu chỉ đinh ở mh pttt
  // "P220", //Chứng minh thư/ Giấy tờ tùy thân
  // "P221", //Giấy khai sinh
  // "P222", //Giấy chứng sinh
  // "P223", //Mục khác
  "P260", //Bảng kê chi phí VTYT trong PT ở mh hsba
  "P261", //Giấy cam đoan chấp nhận sử dụng dịch vụ y tế, trang thiết bị dụng cụ PT KTC
  "P262", //Đơn thuốc kê ngoài
  "P265", //Bảng kê chi phí thuốc trong PT ở mh hsba
  "P269", //Bảng kê chi phí phẫu thuật
  "P267", //Phiếu phát dự trù máu
  "P278", //đơn vật tư
  "P288", //phiếu hướng dẫn thanh toán qr ở in điều dưỡng
  "P289", //phiếu thủ thuật
  "P300", //Phiếu sàng lọc dinh dưỡng công cụ MST
  "P308", //Phiếu sàng lọc dinh dưỡng công cụ NRS2002
  "P309", //Phiếu sàng lọc dinh dưỡng - trẻ em
  "P325", //Bảng cam kết thực hiện nội quy bệnh viện khi nhập viện,
  "P365", //Phiếu lượng giá phục hồi chức năng
  // "P327", //Thẻ BHYT
  // "P328", //Giấy chuyển tuyến BV chuyển đến
  // "P329", //Phiếu hẹn khám lại lần trước
  "P443", //Phiếu thu tạm ứng
  "P477", //Phiếu sàng lọc dinh dưỡng cho phụ nữ có thai
  "P492", //Phiếu đơn thuốc CDHA,
  "P549", //Phiếu trích biên bản hội chẩn thông qua mổ
  "P546", //Phiếu sàng lọc nguy cơ suy dinh dưỡng người bệnh nội trú
  "P552", //Phiếu sàng lọc dinh dưỡng cho NB > 18 tuổi, không mang thai
  "P624", //Phiếu sàng lọc dinh dưỡng cho bệnh nhi ngoại trú
  "P629", //Phiếu chỉ định dịch vụ tư vấn dinh dưỡng
  "P639", //Phiếu sàng lọc dinh dưỡng - trẻ em
  "P656", //Phiếu kết quả khám khúc xạ
  "P663", //Phiếu sàng lọc dinh dưỡng - NB nhập viện không mang thai,
  "P675", //Giấy khám sức khỏe (A3)
  // "P728", //Phiếu chỉ định XN kèm máu
  "P742", //Vòng tay con
  "P743", //Vòng tay con
  "P785", //Phiếu sàng lọc dinh dưỡng cho NB ngoại trú
  "P786", //Phiếu sàng lọc dinh dưỡng cho Bệnh nhi
  "P787", //Phiếu sàng lọc dinh dưỡng cho Bệnh nhi sơ sinh
  "P789",
  "P790",
  "P801", //đơn thuốc ở mh khám bệnh
  "P802", //đơn thuốc 2 ở mh khám bệnh
  "P817", //Phiếu sàng lọc dinh dưỡng cho NB nội trú (BV Chợ Rẫy)
  "P918", //Phiếu pha chế thuốc
  "P919", //Phiếu pha chế thuốc
  "P997", //Phiếu công khai pha chế thuốc,
  "P1046",
  "P1154", //Đơn thuốc HSBA
  "P1180", //Đơn thuốc ra viện HSBA
  ...LIST_PHIEU_KQ_XN_CDHA,
  ...LIST_PHIEU_KHONG_HUY_KY,
];

export const LIST_PHIEU_IN_POPUP = ["P092"];

export const LIST_PHIEU_IN_EDITOR_ALL = Object.keys(MA_BIEU_MAU_EDITOR);

export const LIST_CHON_TIEU_CHI_PTTT = [
  "P093",
  "P111",
  "P139",
  "P164",
  "P165",
  "P161",
  "P241",
  "P186",
  "P322",
  "P409",
  "P452",
  "P458",
  "P453",
  "P457",
  "P451",
  "P459",
  "P519",
  "P518",
  "P550",
  "P551",
  "P138",
  "P570",
  "P572",
  "P577",
  "P579",
  "P625",
  "P730",
  "P733",
  "P732",
  "P758",
  "P759",
  "P763",
  "P764",
  "P770",
  "P779",
  "P900",
  "P951",
  "P1008",
  "P1009",
  "P1062",
  "P1063",
  "P1061",
  "P1063",
  "P1067",
  "P1003",
  "P873",
];
export const LIST_PHIEU_CHON_TIEU_CHI = [
  "P093",
  "P111",
  "P139",
  "P144",
  "P161",
  "P167",
  "P168",
  "P137",
  "P241",
  "P322",
  "P372",
  "P409",
  "P519",
  "P518",
  "P550",
  "P551",
  "P137",
  "P138",
  "P570",
  "P572",
  "P577",
  "P579",
  "P611",
  "P625",
  "P680",
  "P730",
  "P733",
  "P758",
  "P759",
  "P763",
  "P764",
  "P770",
  "P779",
  "P900",
  "P951",
  "P929",
  "P930",
  "P1009",
  "P1043",
  "P1044",
  "P1045",
  "P1047",
  "P1049",
  "P1050",
  "P1051",
  "P1052",
  "P1061",
  "P1063",
  "P943",
  "P944",
  "P1067",
  "P1003",
  "P873",
  "P1232",
  "P1233",
];

export const LIST_PHIEU_CHON_TIEU_CHI_IN_THEO_SO_PHIEU = [
  "P186",
  "P270",
  "P271",
  "P272",
  "P279",
  "P164",
  "P276",
  "P237",
  "P165",
  "P274",
  "P275",
  "P073",
  "P452",
  "P458",
  "P453",
  "P457",
  "P451",
  "P459",
  "P739",
  "P738",
  "P740",
  "P741",
  "P744",
];

export const MODE_FILTER_GIUONG = {
  ALL: 1,
  EMPTY: 2,
  EXIST: 3,
  REVERSE: 4,
  FAMILY: 5,
};

export const KET_QUA_DIEU_TRI = {
  DO: 1,
  KHOI: 2,
  KHONG_THAY_DOI: 3,
  NANG_HON: 4,
  TU_VONG: 5,
  KHONG_DANH_GIA: 10,
};

export const HUONG_DIEU_TRI_NOI_TRU = {
  RA_VIEN: 15,
  CHUYEN_VIEN: 40,
  CHUYEN_VIEN_THEO_YEU_CAU: 42,
  TRON_VIEN: 50,
  XIN_RA_VIEN: 60,
};

export const DS_TINH_CHAT_KHOA = {
  LAM_SANG: 10,
  CDHA_TDCN: 20,
  PHAU_THUAT: 30,
  CAP_CUU: 40,
  DIEU_TRI_NGOAI_TRU: 50,
  NGOAI_TRU: 60,
  NOI_TRU: 70,
  TIEP_DON: 80,
  TIEP_DON_NOI_TRU: 85,
  TIEP_DON_ONLINE: 90,
  NGAT_DIEU_TRI_NOI_TRU: 95,
  Y_HOC_CO_TRUYEN: 150,
  TO_DIEU_TRI_SAN_KHOA: 160,
  TO_DIEU_TRI_SO_SINH: 170,
  XAC_NHAN_NHAP_KHO: 180,
};

export const DOI_TUONG_KCB = {
  NGOAI_TRU: 1,
  DIEU_TRI_NGOAI_TRU: 2,
  DIEU_TRI_NOI_TRU: 3,
  DIEU_TRI_NOI_TRU_BAN_NGAY: 4,
  DIEU_TRI_DAI_HAN_CO_KHAM_LINH_THUOC: 5,
  DIEU_TRI_LUU_TRU_TAI_TRAM_Y_TE_TUYEN_XA: 6,
  NHAN_THUOC_THEO_HEN: 7,
  DIEU_TRI_DAI_HAN_CO_KHAM_SU_DUNG_THUOC: 8,
  DIEU_TRI_NOI_TRU_DUOI_BON_GIO: 9,
  CAC_TRUONG_HOP_KHAC: 10,
};

export const GIAY_IN_BIEN_BAN_HOI_CHAN = [
  {
    value: 1,
    ten: "phieuTrichBienBanHoiChan",
    path: "/trich-bien-ban",
    maPhieu: "P205",
  },
  {
    value: 2,
    ten: "giayDeNghiHoiChan",
    path: "/giay-de-nghi",
    maPhieu: "P206",
  },
  {
    value: 3,
    ten: "giayMoiHoiChan",
    path: "/giay-moi",
    maPhieu: "P207",
  },
  {
    value: 4,
    ten: "bienBanHoiChan",
    path: "/bien-ban",
    maPhieu: "P208",
  },
];
export const FORMAT_DATE_TIME = "DD/MM/YYYY HH:mm:ss";
export const LOAI_NHAP_XUAT = {
  NHAP_TU_NCC: 10,
  DU_TRU: 20,
  NHAP_KHAC: 12,
  XUAT_CHUYEN_KHO: 30,
  XUAT_TRA_NHA_CUNG_CAP: 40,
  XUAT_DAO_HAN_SU_DUNG: 45,
  XUAT_TRA_TAI_KHOA: 50,
  PHIEU_TRA: 70,
  LINH_BU_TU_TRUC: 80,
  LINH_NOI_TRU: 85,
  CHE_PHAM_MAU: 88,
  XUAT_KHAC: 90,
  NB_NGOAI_TRU: 100,
  NB_TRA: 102,
  PHAT_NOI_TRU: 105,
  NB_NOI_TRU: 110,
  NB_TU_TRUC: 115,
  NHA_THUOC: 120,
  XUAT_VAC_XIN_TIEM: 130,
};

export const THEO_SO_LUONG_TON_KHO = {
  CON_TON: 10,
  CON_TON_KHA_DUNG: 15,
  HET_TON: 20,
};

export const LOAI_DOI_TAC = {
  NHA_SAN_XUAT: 10,
  NHA_CUNG_CAP: 20,
  KHACH_HANG: 30,
  CONG_TY_KSK: 40,
  CONG_TY_BAO_HIEM: 50,
  NGUON_TAI_TRO: 60,
  BAO_HIEM_BAO_LANH: 70,
  DE_TAI_NGHIEN_CUU: 80,
  THANH_TOAN: 90,
};

export const DINH_DANG_HOA_DON = {
  HTML: 10,
  PDF: 20,
};

export const NGUON_TONG_THU = [
  {
    id: true,
    ten: "Tổng Thu",
    i18n: "baoCao.tongThu",
  },

  {
    id: false,
    ten: "Tổng Chi",
    i18n: "baoCao.tongChi",
  },
];

export const TEN_CO_CHE_DUYET_PHAT = {
  DUYET_PHAT_CHUNG: "DUYET_PHAT_CHUNG",
  DUYET_PHAT_NGAY_KHI_KE: "DUYET_PHAT_NGAY_KHI_KE",
  DUYET_SAU_KHO_XUAT_HUY: "DUYET_SAU_KHO_XUAT_HUY",
};

export const TEN_LOAI_DICH_VU = {
  KHAM: "KHAM",
  XET_NGHIEM: "XET_NGHIEM",
  CDHA_TDCN: "CDHA_TDCN",
  PHAU_THUAT_THU_THUAT: "PHAU_THUAT_THU_THUAT",
  SUAT_AN: "SUAT_AN",
  NGOAI_DIEU_TRI: "NGOAI_DIEU_TRI",
  VAN_CHUYEN: "VAN_CHUYEN",
  THUOC: "THUOC",
  VAT_TU: "VAT_TU",
  HOA_CHAT: "HOA_CHAT",
  CHE_PHAM_MAU: "CHE_PHAM_MAU",
  CHE_PHAM_DINH_DUONG: "CHE_PHAM_DINH_DUONG",
  GIUONG: "GIUONG",
  BO_CHI_DINH: "BO_CHI_DINH",
  TIEP_DON: "TIEP_DON",
  NOI_TRU: "NOI_TRU",
  TO_DIEU_TRI: "TO_DIEU_TRI",
  NHA_THUOC: "NHA_THUOC",
  DAT_KHAM: "DAT_KHAM",
  CRM: "CRM",
};

export const TRANG_THAI_HOAN = {
  THUONG: 0,
  CHO_DUYET_HOAN: 10,
  CHO_DUYET_DOI: 20,
  DA_HOAN: 30,
  KHONG_THUC_HIEN: 40,
};

export const LOAI_CHUYEN_VIEN = {
  CHUYEN_VIEN: 10,
  CHUYEN_KHAM_CHUYEN_KHOA: 20,
};

export const DU_DIEU_KIEN_CHUYEN_TUYEN = {
  PHU_HOP: 1,
  KHONG_PHU_HOP: 2,
};

export const PHIEU_THU_NB = [
  {
    id: 10,
    ten: "Nb khám sức khỏe",
  },

  {
    id: 20,
    ten: "Nb thường",
  },
];

export const TRANG_THAI_DO_SINH_HIEU = [
  {
    id: 10,
    ten: "Chưa đo",
    i18n: "sinhHieu.chuaDo",
  },

  {
    id: 20,
    ten: "Đã đo",
    i18n: "sinhHieu.daDo",
  },
];

export const LOAI_GIAY_GUI_CONG_BHXH = [
  { id: 1, ten: "CT03" },
  { id: 2, ten: "CT04" },
  { id: 3, ten: "CT05" },
  { id: 4, ten: "CT06" },
  { id: 5, ten: "CT07" },
  { id: 6, ten: "NB tử vong", i18n: "giayDayCong.nguoiBenhTuVong" },
  { id: 7, ten: "Giấy KSK lái xe", i18n: "giayDayCong.giayKSKLaiXe" },
];

export const NHOM_CHI_PHI = [
  { id: 0, ten: "Ngoài danh mục BHYT" },
  { id: 1, ten: "Xét nghiệm" },
  { id: 2, ten: "Chẩn đoán hình ảnh" },
  { id: 3, ten: "Thăm dò chức năng" },
  { id: 4, ten: "Thuốc trong danh mục BHYT" },
  { id: 5, ten: "Thuốc ngoài danh mục BHYT" },
  { id: 6, ten: "Thuốc thanh toán theo tỷ lệ" },
  { id: 7, ten: "Máu và chế phẩm máu" },
  { id: 8, ten: "Thủ thuật, phẫu thuật" },
  { id: 9, ten: "Dịch vụ kỹ thuật (DVKT) thanh toán theo tỷ lệ" },
  { id: 10, ten: "Vật tư y tế trong danh mục BHYT" },
  { id: 11, ten: "Vật tư y tế (VTYT) thanh toán theo tỷ lệ" },
  { id: 12, ten: "Vận chuyển" },
  { id: 13, ten: "Khám bệnh" },
  { id: 14, ten: "Ngày giường bệnh ban ngày" },
  { id: 15, ten: "Ngày giường bệnh điều trị nội trú, bao gồm cả giường lưu" },
];

export const LOAI_DON_THUOC = {
  NHA_THUOC: 10,
  THUOC_KHO: 20,
  HUONG_THAN: 30,
  KE_NGOAI: 150,
  DA_KE: 500,
};

export const LIST_LOAI_DON_THUOC = [
  {
    id: 10,
    ten: "Thuốc nhà thuốc",
    i18n: "khamBenh.donThuoc.thuocNhaThuoc",
  },
  {
    id: 20,
    ten: "Thuốc Kho",
    i18n: "khamBenh.donThuoc.thuocKho",
  },
  { id: 150, ten: "Thuốc kê ngoài", i18n: "khamBenh.donThuoc.thuocKeNgoai" },
  { id: 500, ten: "Thuốc đã kê", i18n: "khamBenh.donThuoc.thuocDaKe" },
];

export const TRANG_THAI_HIEN_THI = {
  DANG_KHAM_THUC_HIEN: 10,
  TIEP_THEO: 20,
  DA_XAC_NHAN: 40,
  CHO_XÁC_NHAN: 50,
  CHO_KET_LUAN: 55,
  GOI_NHO: 60,
};

export const LIST_LOAI_QMS = {
  QMS_TIEP_DON: 10,
  QMS_CDHA_TDCN: 20,
  QMS_KHAM_BENH: 30,
  QMS_XET_NGHIEM: 40,
  QMS_THU_NGAN: 50,
  QMS_MAN_HINH_PHU_THU_NGAN: 60,
  QMS_PTTT: 70,
  KIOSK_QUET_VA_GOI_NB_VAO_QUAY: 80,
};
export const LIST_LOAI_DANH_SACH_QMS = {
  THEO_PHONG: 10,
  THEO_KHOA: 20,
  KHU_VUC: 25,
  TOAN_VIEN: 30,
};

export const TRANG_THAI_DICH_VU_CDHA = {
  TIEP_NHAN: [25, 35, 43, 50],
  DA_TIEP_NHAN: [63],
  DA_CO_KET_QUA: [155],
  CHO_TIEP_DON: [15],
  CHO_TIEP_NHAN: [25, 35, 43],
  YEU_CAU_HOAN: [25, 35, 43, 50],
};

export const LOAI_IN_BANG_KE_CHI_PHI = {
  BAO_HIEM: 10,
  BANG_KE_DICH_VU_BH: 12,
  TONG_HOP: 20,
  BANG_KE_DICH_VU: 22,
  NOI_TRU: 30,
  KCB_NOI_TRU: 33,
  NOI_TRU_KHONG_BAO_HIEM: 35,
  MIEN_PHI: 50,
  THU_NGOAI: 52,
  BANG_KE_BHYT: 70,
  BANG_KE_THU_PHI: 71,
  BANG_KE_HAO_PHI: 73,
  BANG_KE_CHI_PHI_DV_NGOAI_TRU: 75,
};

export const LOAI_THOI_GIAN = {
  THE0_THOI_GIAO_VAO_VIEN: 10,
  THEO_THOI_GIAN_CHI_DINH: 20,
  THEO_THOI_GIAN_THUC_HIEN: 30, // Thời gian thực hiện
  THEO_THOI_GIAN_THANH_TOAN: 40, // Thời gian thanh toán
  THEO_THOI_GIAO_HEN_KHAM: 50,
  THEO_THOI_GIAO_PHAT_HANH_HOA_DON: 60,
  THEO_THOI_GIAO_RA_VIEN: 70, // Thời gian ra viện
  THEO_THOI_GIAO_TAO_HO_SO_GIAM_DINH_XML: 80,
  THEO_THOI_GIAO_KET_THUC: 90,
  THEO_THOI_GIAO_CHUYEN_VIEN: 100,
  THEO_THOI_GIAO_TU_VONG: 110,
  THEO_THOI_GIAN_TAM_UNG: 130,
  THEO_THOI_GIAN_CO_KET_QUA: 180,
  THEO_THOI_GIAN_TIEP_NHAN: 190,
  THEO_THOI_GIAN_TIEP_NHAN_HOAC_XAC_NHAN_KHONG_THUC_HIEN: 220,
  THEO_THOI_GIAN_TONG_KET_THANH_TOAN: 240,
  THEO_THOI_GIAN_CHOT_SO: 250,
  THEO_THOI_GIAN_XAC_NHAN_DOI_TRA: 260,
};

export const LOAI_IN = {
  IN_NHANH: 10,
  IN_NHANH_PREVIEW: 15,
  MO_TAB: 20,
};

export const LOAI_THOI_GIAN_IN = [
  { id: 30, ten: "Thời gian thực hiện", i18n: "baoCao.thoiGianThucHien" },
  { id: 120, ten: "Thời gian áp dụng gói", i18n: "baoCao.thoiGianApDungGoi" },
];

export const LOAI_THOI_GIAN_DUYET_PHAT = [
  {
    id: 10,
    ten: "Theo thời gian duyệt phát",
    i18n: "baoCao.theoThoiGianDuyetPhat",
  },
  {
    id: 20,
    ten: "Theo thời gian thực hiện",
    i18n: "baoCao.theoThoiGianThucHien",
  },
];

export const LOAI_DIA_CHI = [
  {
    id: 1,
    ten: "Thường trú",
    i18n: "common.thuongTru",
  },
  {
    id: 2,
    ten: "Tạm trú",
    i18n: "common.tamTru",
  },
];

export const LOAI_KHAM_TIEM_CHUNG = {
  SANG_LOC: 10,
  KHAM_CHUNG: 20,
};

export const TRANG_THAI_TAI_KHOA = {
  NB_TRONG_KHOA: 10,
  NB_DA_CHUYEN_KHOA: 20,
  NB_DA_CHUYEN_DEN: 30,
  NB_CHO_TIEP_DON_THEO_HEN: 40,
  NB_DANG_KY_PHCN: 50,
};

export const DATA_MODE_DS_NB = {
  DRAWER: 1,
  MODULE: 2,
};

export const LOAI_KHU_VUC = {
  TIEP_DON: 10,
  THU_NGAN: 20,
};

export const THOI_DIEM_DUNG = {
  TRUOC_AN: "Trước ăn",
  SAU_AN: "Sau ăn",
  TRONG_KHI_AN: "Trong khi ăn",
  KHI_SOT: "Khi sốt",
  BUOI_TOI: "Buổi tối",
};

export const DATA_MODE_THONG_KE_TIEP_DON_NHA_THUOC = {
  DRAWER: 1,
  MODULE: 2,
};

export const TRO_THO = {
  BOP_BONG: 10,
  MAY_THO: 20,
};
export const LOAI_THUOC = {
  BHYT: 10,
  TU_TRUC: 20,
  THUOC_LINH_NOI_TRU: 30,
  RA_VIEN: 40,
  NHA_THUOC: 50,
  DAU_SAO: 100,
};
export const LOAI_BANG_KE = [
  "Bảo hiểm",
  "Thu phí",
  "Tiện ích",
  "Hao phí",
  "Tăng cường",
];

export const TRANG_THAI_THUOC = {
  TAO_MOI: { ten: "Tạo mới", i18: "common.taoMoi", id: 10 },
  TAO_MOI_GIU_CHO: { ten: "Tạo mới giữ chỗ", i18: "kho.taoMoiGiuCho", id: 15 },
  CHO_DUYET: { ten: "Chờ duyệt", i18: "common.choDuyet", id: 20 },
  DA_TAO_PHIEU_LINH: {
    ten: "Đã tạo phiếu lĩnh",
    i18: "quanLyNoiTru.capPhatThuoc.daTaoPhieuLinh",
    id: 22,
  },
  CHO_DUYET_PHIEU_LINH: {
    ten: "Chờ duyệt phiếu lĩnh",
    i18: "quanLyNoiTru.capPhatThuoc.choDuyetPhieuLinh",
    id: 24,
  },
  DA_DUYET_DUOC_LAM_SANG: {
    ten: "Đã duyệt dược lâm sàng",
    i18: "kho.daDuyetDLS",
    id: 26,
  },
  DA_PHAT: { ten: "Đã phát", i18: "kho.daPhat", id: 30 },
  DA_BAN_GIAO: {
    ten: "Đã bàn giao",
    i18: "quanLyNoiTru.capPhatThuoc.daBanGiao",
    id: 40,
  },
  DA_SU_DUNG: {
    ten: "Đã sử dụng",
    i18: "quanLyNoiTru.capPhatThuoc.daSuDung",
    id: 50,
  },
  TU_CHOI_DUYET_DUOC_LAM_SANG: {
    ten: "Từ chối duyệt dược lâm sàng",
    i18: "kho.tuChoiDuyetDLS",
    id: 25,
  },
  NGUNG_SU_DUNG: { ten: "Ngưng sử dụng", i18: "kho.ngungSuDung", id: 60 },
};

//trạng thái NB
//5: Mới tiếp đón, 10: Chờ lập bệnh án, 20: Chờ tiếp nhận vào khoa, 30: Đang điều trị
//40: Đang chuyển khoa, 45: Tạm dừng, 50: Chờ hoàn tất thủ tục ra viện, 100: Đã ra viện
//110: Hẹn điều trị, 120: Đã thanh toán ra viện, 130: Đã thanh toán, Hẹn điều trị
//135: Đã tiếp đón hẹn điều trị, 200: Hủy bệnh án
export const TRANG_THAI_NB_BY_DOI_TUONG_KCB = {
  2: [20, 30, 40, 50, 100, 110, 130, 135, 103, 106, 102], //Điều trị ngoại trú
  3: [20, 30, 40, 50, 100, 120, 103, 106, 102], //Điều trị nội trú
  4: [20, 30, 40, 50, 100, 110, 120, 130, 135, 103, 106, 102], //Điều trị nội trú ban ngày
};

export const DOI_TUONG_KCB_BA_DAI_HAN = [
  {
    id: 5,
    ten: "Điều trị dài hạn có khám và lĩnh thuốc",
  },
  {
    id: 8,
    ten: "Điều trị dài hạn có khám, DVKT và/hoặc sử dụng thuốc",
  },
];

export const LOAI_PHA_CHE = {
  BANG_PHA_CHE: { ten: "Bảng pha chế", id: 10 },
  BANG_TOC_DO_TRUYEN: { ten: "Bảng tốc độ truyền", id: 20 },
  BANG_KET_QUA: { ten: "Bảng kết quả", id: 30 },
};
export const TRANG_THAI_PHA_CHE = {
  TAO_MOI: 10,
  CHO_DUYET: 20,
  DA_DUYET: 30,
  DA_PHA_CHE: 40,
  DA_GIAO: 50,
};
export const TRANG_THAI_PHIEU_XUAT_PHA_CHE = {
  TAO_MOI: 10,
  CHO_DUYET: 20,
  HOAN_THANH: 30,
};
export const TRANG_THAI_PHA_CHE_THUOC = {
  //enums?name=TrangThaiPhaCheThuoc
  TAO_MOI: 10,
  DUYET_DUOC: 12,
  XAC_NHAN_NB: 14,
  DA_PHA_CHE: 20,
  DA_GIAO: 30,
  HUY_PHA_CHE: 40,
};

export const LIST_THOI_DIEM_DUNG = [
  {
    key: 1,
    label: THOI_DIEM_DUNG.TRUOC_AN,
  },
  { key: 2, label: THOI_DIEM_DUNG.SAU_AN },
  {
    key: 3,
    label: THOI_DIEM_DUNG.TRONG_KHI_AN,
  },
  {
    key: 4,
    label: THOI_DIEM_DUNG.KHI_SOT,
  },
  {
    key: 5,
    label: THOI_DIEM_DUNG.BUOI_TOI,
  },
];

export const LOAI_KHO = {
  NHAP_TU_NCC: 10,
  NHA_THUOC: 20,
  BAN_THUOC: 30,
  KHO_KY_GUI: 40,
  KHO_HUY: 60,
};

export const TRANG_THAI_QUYET_TOAN = {
  MOI: 10,
  TAO_LAI: 20,
  DONG_BO_LOI: 30,
  DA_DONG_BO: 40,
};

export const TRANG_THAI_DUYET_BH = {
  DA_DUYET_BH: 20,
  CHUA_DUYET_BH: 10,
};

export const TRANG_THAI_TAI_KHOAN = {
  DANG_KHOA: 10,
  HOAT_DONG: 0,
};
export const TRANG_THAI_DAY_CONG = {
  TAO_MOI: 10,
  TAO_LAI: 20,
  GUI_THAT_BAI: 30,
  GUI_THANH_CONG: 40,
};

export const LOAI_BC_THONG_TIN_NB = {
  P198: 20, //bc_thong_tin_chung_nb
  P199: 10, //bc_tien_su_kham_suc_khoe
  P200: 50, //bc_ket_qua_kham_lam_sang
  P201: 40, //bc_dau_hieu_sinh_ton
  P202: 30, //bc_ket_qua_cdha
  P203: 60, //bc_chup_chieu_x_quang
  P204: 70, //bc_tom_tat_khuyen_nghi
};

export const DS_LOAI_BC_KSK15 = [
  {
    id: 20,
    ten: "Báo cáo thông tin chung NB",
    i18n: "baoCao.baoCaoThongTinChungNB",
  },
  {
    id: 10,
    ten: "Báo cáo tiền sử khám sức khỏe",
    i18n: "baoCao.baoCaoTienSuKhamSucKhoe",
  },
  {
    id: 50,
    ten: "Báo cáo kết quả khám lâm sàng",
    i18n: "baoCao.baoCaoKetQuaKhamLamSang",
  },
  {
    id: 40,
    ten: "Báo cáo dấu hiệu sinh tồn",
    i18n: "baoCao.baoCaoDauHieuSinhTon",
  },
  { id: 30, ten: "Báo cáo kết quả CDHA", i18n: "baoCao.baoCaoKetQuaCDHA" },
  {
    id: 70,
    ten: "Báo cáo tóm tắt khuyến nghị",
    i18n: "baoCao.baoCaoTomTatKhuyenNghi",
  },
  {
    id: 1,
    ten: "Phiếu kết quả xét nghiệm",
    i18n: "baoCao.phieuKetQuaXetNghiem",
  },
  {
    id: 2,
    ten: "Phiếu kết quả CDHA-TDCN",
    i18n: "baoCao.phieuKetQuaCDHA_TDCN",
  },
  {
    id: 60,
    ten: "Báo cáo chụp chiếu X-quang",
    i18n: "baoCao.baoCaoChupChieuXQuang",
  },
];

export const MODULE_KEY = {
  TIEP_DON: "TIEP_DON",
  KHAM_BENH: "KHAM_BENH",
  THEO_DOI_DIEU_TRI: "THEO_DOI_DIEU_TRI",
  HO_SO_BENH_AN: "HO_SO_BENH_AN",
  QUAN_LY_NOI_TRU: "QUAN_LY_NOI_TRU",
  TIEM_CHUNG: "TIEM_CHUNG",
  DIEU_TRI_DAI_HAN: "DIEU_TRI_DAI_HAN",
  KHAM_SUC_KHOE_HOP_DONG: "KHAM_SUC_KHOE_HOP_DONG",
  SINH_HIEU: "SINH_HIEU",
  XET_NGHIEM: "XET_NGHIEM",
  CDHA_TDCN: "CDHA_TDCN",
  PHUC_HOI_CHUC_NANG: "PHUC_HOI_CHUC_NANG",
  PHAU_THUAT_THU_THUAT: "PHAU_THUAT_THU_THUAT",
  GOI_DICH_VU: "GOI_DICH_VU",
  NHA_THUOC: "NHA_THUOC",
  THU_NGAN: "THU_NGAN",
  BAO_CAO: "BAO_CAO",
  KE_HOACH_TONG_HOP: "KE_HOACH_TONG_HOP",
  KY_SO: "KY_SO",
  DASHBOARD: "DASHBOARD",
  QUAN_LY_KHO: "QUAN_LY_KHO",
  KHO_MAU: "KHO_MAU",
  DANH_SACH_GIAY_DAY_CONG: "DANH_SACH_GIAY_DAY_CONG",
  QUYET_TOAN_BHYT: "QUYET_TOAN_BHYT",
  QUAN_LY_THONG_BAO: "QUAN_LY_THONG_BAO",
  QUAN_TRI_HE_THONG: "QUAN_TRI_HE_THONG",
  DANH_MUC: "DANH_MUC",
  THIET_LAP: "THIET_LAP",
  KIOSK: "KIOSK",
  QMS: "QMS",
  QUAN_LY_DINH_DUONG: "QUAN_LY_DINH_DUONG",
  PHA_CHE_THUOC: "PHA_CHE_THUOC",
  HOI_CHAN: "HOI_CHAN",
  LIEN_THONG_DON_THUOC: "LIEN_THONG_DON_THUOC",
  KIEM_SOAT_NHIEM_KHUAN: "KIEM_SOAT_NHIEM_KHUAN",
  PHAC_DO_DIEU_TRI: "PHAC_DO_DIEU_TRI",
  DO_THI_LUC: "DO_THI_LUC",
  QUAN_LY_BAO_CAO_ADR: "QUAN_LY_BAO_CAO_ADR",
  KPIS: "KPIS",
  DV_DIEU_DUONG: "DV_DIEU_DUONG",
  QUAN_LY_DIEU_TRI_LAO: "QUAN_LY_DIEU_TRI_LAO",
  HEN_NOI_SOI: "HEN_NOI_SOI",
  QUAN_LY_NHAN_LUC: "QUAN_LY_NHAN_LUC",
  QUAN_LY_HOA_HONG: "QUAN_LY_HOA_HONG",
  QUAN_LY_YEU_CAU: "QUAN_LY_YEU_CAU",
};

export const LOAI_BIEU_MAU = {
  BIEU_MAU_CHI_XEM: 10,
  MAN_HINH: 20,
  BIEU_MAU_CHINH_SUA: 50,
  BIEU_MAU_SCAN: 60,
};

export const LIST_SCAN_CHUNG_TU = [
  { id: true, ten: "Đã scan", i18n: "kho.daScan" },
  { id: false, ten: "Chưa scan", i18n: "kho.chuaScan" },
];

export const LOAI_BAO_CAO = [
  { id: true, ten: "Phẫu thuật", i18n: "dashboard.phauThuat" },
  { id: false, ten: "Thủ thuật", i18n: "pttt.thuThuat" },
];

export const HINH_THUC_TT_HD_KSK = {
  THANH_TOAN_THEO_HOP_DONG: 10,
  TU_THANH_TOAN: 20,
};

export const LIST_PHIEU_KY_CUSTOM = [
  "P209", //Giấy chứng tử
  "P196", // Giấy KSK lái xe
  "P254", // Giấy KSK lái xe
];

export const TRANG_THAI_KY = {
  CHUA_KY: 0,
  TRINH_KY: 10,
  TRINH_KY_USB_TOKEN: 20,
  DA_KY: 50,
  HOAN_THANH: 60,
  TU_CHOI_KY: 90,
  DA_HUY: 100,
};

export const TRANG_THAI_THANH_TOAN_QR = {
  MOI: 10,
  TAO_QR_LOI: 20,
  TAO_QR: 30,
  THANH_TOAN_LOI: 45,
  THANH_TOAN: 50,
};

export const LOAI_PHUONG_THUC_TT = {
  QR_CODE: 2,
  CHUA_TAM_UNG: 15,
};

export const LOAI_MH_PHU = {
  PHIEU_THU: 1,
  TAM_UNG: 2,
};

export const TRANG_THAI_PHIEU_THU_THANH_TOAN = {
  CHUA_THANH_TOAN: 0,
  TAO_QR: 10,
  DA_THANH_TOAN: 50,
  CHUA_THANH_TOAN_TAO_QR: -1,
};

export const LIST_TRANG_THAI_THANH_TOAN = [
  { id: -1, ten: "Chưa thanh toán", i18n: "thuNgan.chuaThanhToan" },
  { id: 50, ten: "Đã thanh toán", i18n: "thuNgan.daThanhToan" },
];

export const LOAI_LICH_HEN_KHAM = {
  HEN_TAI_KHAM: 10,
  HEN_KET_LUAN: 20,
  HEN_THUC_HIEN_DICH_VU_KY_THUAT: 30,
  HEN_TIEM_VAC_XIN: 40,
  HEN_DIEU_TRI_NGOAI_TRU: 50,
  HEN_KHAM_CMU: 60,
};

export const TRANG_THAI_HEN_KHAM = {
  TAO_MOI: 10,
  DA_NHAC_LICH: 20,
  DA_TIEP_DON: 30,
  HUY_LICH: 40,
};

export const PHAN_LOAI_KCB = {
  CSKCB_BAN_DAU: 10,
  THONG_TUYEN_HUYEN: 20,
  CHUYEN_TUYEN: 30,
  HEN_KHAM: 40,
  HIEN_TANG: 50,
  THE_TAM: 60,
  LAO: 70,
  HIV: 80,
  COVID: 90,
  CAP_CUU: 100,
  TRAI_TUYEN_KV_K1_K2_K3: 110,
  THONG_TUYEN_TINH: 120,
  KHONG_BAO_HIEM: 130,
};

export const LOAI_DM_THI_LUC = {
  THI_LUC: 10, // DM Thị lực
  NHAN_AP: 20,
  AXIS: 30, //AXIS
  CYL: 40,
  SPH: 50,
  TAT_KHUC_XA: 60,
};

export const TRANG_THAI_DONG_BO_CHUNG_TU = {
  CHUA_DONG_BO: 10,
  DA_DONG_BO: 20,
  DONG_BO_LOI: 30,
};

export const LOAI_CHUNG_TU = {
  PHIEU_THU_THANH_TOAN: 10,
  HOAN_TRA_DICH_VU: 20,
  THU_QUYET_TOAN_TAM_UNG: 30,
  PHIEU_QUYET_TOAN_TAM_UNG: 40,
  NHAP_TU_NHA_CUNG_CAP: 50,
  NHAP_KHO_NGUOI_BENH_TRA_LAI: 60,
  NHAP_KHO_KHAC: 70,
  XUAT_KHO: 80,
  XUAT_KHO_CHO_NGUOI_BENH: 90,
  XUAT_DIEU_CHUYEN_KHO: 100,
  HOA_DON_DIEN_TU: 110,
  HOA_DON_DIEU_CHINH: 120,
};

export const PHAN_LOAI_DOI_TUONG = {
  KHAM_MOI: 10,
  TAI_KHAM: 20,
  DIEU_TRI_NGOAI_TRU: 30,
};

export const LOAI_MUON = {
  MUON_KHOA_PHONG: 1,
  MUON_GIAM_DINH_BAO_HIEM: 2,
};

export const HINH_THUC_TT_KSK = {
  TRONG_HOP_DONG: 10,
  TRONG_HOP_DONG_TU_THANH_TOAN: 15,
  TT_THEO_HD: 20,
  TU_THANH_TOAN: 30,
};

export const TANG_GIAM_KPIS = {
  TANG: 10,
  GIAM: 20,
};

export const KET_QUA_KPIS = {
  DAT: 10,
  KHONG_DAT: 20,
};

export const LOAI_LAO = {
  LAO_THUONG: 10,
  LAO_KHANG_THUOC: 20,
  LAO_TIEM_AN: 30,
};
export const TRANG_THAI_DANG_KY_THUOC_LAO = {
  TAO_MOI: 10,
  CHUYEN_LAO_KHANG: 20,
  HOAN_THANH: 30,
  HUY: 40,
  DANG_KY_THUOC_LAO_NGOAI_GIO: 50,
};

export const LOAI_HOI_CHAN = [
  {
    id: 1,
    ten: "Hội chẩn thường",
    i18n: "hoiChan.hoiChanThuong",
  },
  {
    id: 2,
    ten: "Hội chẩn lao kháng thuốc",
    i18n: "hoiChan.hoiChanLaoKhangThuoc",
  },
];

export const TRANG_THAI_DONG_BO_VITIMES = {
  TAO_MOI: 10,
  DA_DONG_BO: 20,
  DONG_BO_LOI: 30,
};
export const LOAI_QUAY = {
  TIEP_DON: 10,
  THU_NGAN: 20,
};

export const PHIEU_THU_CHI = [
  {
    id: 10,
    ten: "Thu viện phí",
    i18n: "baoCao.thuVienPhi",
  },
  {
    id: 20,
    ten: "Chi viện phí",
    i18n: "baoCao.chiVienPhi",
  },
  {
    id: 30,
    ten: "Thu tạm ứng",
    i18n: "baoCao.thuTamUng",
  },
];

export const TRANG_THAI_BAO_CAO = {
  CHUA_GUI: 10,
  DA_GUI: 20,
};
export const PHAN_LOAI_BAO_CAO = [
  {
    id: "ctcl",
    ten: "CTCL",
    i18n: "baoCao.ctcl",
  },
  {
    id: "yTeCong",
    ten: "Y tế công",
    i18n: "baoCao.yTeCong",
  },
  {
    id: "yTeTu",
    ten: "Y tế tư",
    i18n: "baoCao.yTeTu",
  },
];

export const VALIDATE_CASE_SDT = {
  NO_VALIDATE: "0",
  VALIDATE: "1",
  VALIDATE_SDT_MA_GTTT: "2",
};

export const TRANG_THAI_PHIEU_MUON_TRA_BA = {
  TAO_MOI: 10,
  CHO_DUYET: 20,
  DA_DUYET: 30,
};

export const PAGE_TYPE = {
  FORM_A4: "A4",
  FORM_A3: "A3",
};

export const HUONG_GIAY = {
  DOC: 10,
  NGANG: 20,
};

export const KHO_GIAY = {
  A4: 10,
  A5: 20,
  A6: 30,
  TUY_CHINH: 200,
};

export const HINH_THUC_IN = {
  MOT_MAT: 10,
  HAI_MAT: 20,
};

export const MAU_BAO_CAO_HOAN_TRA = [
  {
    id: 10,
    ten: "Mẫu báo cáo chung",
    i18n: "baoCao.mauBaoCaoChung",
  },
  {
    id: 20,
    ten: "Mẫu báo cáo thuốc trả theo hóa đơn (cho BVP)",
    i18n: "baoCao.mauBaoCaoThuocTraTheoDon",
  },
];
export const VAT_HOAN_TRA_THUOC_NHA_THUOC = [
  {
    id: 5,
    ten: "5%",
  },
  {
    id: 8,
    ten: "8%",
  },
  {
    id: 10,
    ten: "10%",
  },
];

export const LOAI_CHI_DINH = {
  THUONG: 0,
  DOT_XUAT: 10,
  BO_SUNG: 20,
};

export const IN_MAU_BAO_CAO = [
  {
    id: 10,
    ten: "Mẫu thủ thuật",
    i18n: "baoCao.mauThuThuat",
  },
  {
    id: 20,
    ten: "Nhóm theo phân loại PTTT",
    i18n: "baoCao.nhomTheoPhanLoaiPTTT",
  },
  {
    id: 30,
    ten: "Nhóm theo chuyên khoa",
    i18n: "baoCao.nhomTheoChuyenKhoa",
  },
];

export const LOAI_AP_DUNG_GIAM_GIA = {
  THEO_NHOM_DV_CAP_1: 10,
  THEO_NHOM_DV_CAP_2: 15,
  THEO_DICH_VU: 20,
};

export const LOAI_DANG_KY = {
  PHUC_HOI_CHUC_NANG: 10,
  Y_HOC_CO_TRUYEN: 20,
  DIEU_TRI_KET_HOP: 30,
};

export const LIST_LOAI_DANG_KY = [
  {
    id: LOAI_DANG_KY.PHUC_HOI_CHUC_NANG,
    ten: "PHCN",
    i18n: "phcn.PHCN",
  },
  {
    id: LOAI_DANG_KY.Y_HOC_CO_TRUYEN,
    ten: "YHCT",
    i18n: "phcn.YHCT",
  },
];

export const DS_LOAI_KY = [
  { ten: "Ký số", id: 0 },
  { ten: "Ký điện tử", id: 1 },
  { ten: "Ký người bệnh/ người nhà", id: 2 },
];

export const LOAI_GIAY_TO_TUY_THAN = {
  CAN_CUOC: 1,
  CMND: 2,
  KHONG_CO_GIAY_TO: 6,
};

export const MAN_HINH = {
  KHAM_BENH: "Khám bệnh",
  HO_SO_BENH_AN: "Hồ sơ bệnh án",
  DIEU_TRI_NOI_TRU: "Quản lý nội trú",
  NOI_TRU: "Nội trú",
  DANH_SACH_TO_DIEU_TRI: "Tờ điều trị",
  THU_NGAN: "Thu ngân",
  PHIEU_THU: "Phiếu thu",
  BAO_CAO: "Báo cáo",
  TAM_UNG: "Tạm ứng",
  TIEP_DON: "Tiếp đón",
  DANH_SACH_PHIEU_THU: "Danh sách phiếu thu",
};

export const HIEN_THI_LOAI_THONG_BAO = {
  1: "thietLap.thongBaoDaCoKetQuaCLScuaNB",
  2: "thietLap.thongBaoCoPhieuTrinhKy",
  3: "thietLap.coThuocBiTuChoiDuyetDLS",
  4: "thietLap.thongBaoDaCoKetQuaTaiXuong",
};

export const LOAI_SANG_LOC_NGUY_CO_SDD = {
  // enums: LoaiSangLocSuyDd
  MST: 10,
  TRE_EM: 20,
  NRS_2002: 30,
  SGA: 40,
  PHU_NU_CO_THAI: 50,
  NOI_TRU: 60,
  NOI_TRU_2: 62,
  NB_18_TUOI: 70,
  BENH_NHI_NGOAI_TRU: 80,
  BENH_NHI_NOI_TRU: 90,
  NHAP_VIEN_KHONG_MANG_THAI: 100,
  NB_NGOAI_TRU: 110,
  BENH_NHI: 120,
  BENH_NHI_SO_SINH: 130,
};

export const PHAN_LOAI_CHI_SO_SONG = {
  BINH_THUONG: 10,
  BAT_THUONG: 20,
};

export const PHAN_LOAI_DOI_TUONG_KCB = [
  {
    id: 1,
    ten: "Ngoại trú",
    i18n: "common.ngoaiTru",
    referIds: [
      DOI_TUONG_KCB.NGOAI_TRU, //1
      DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_LINH_THUOC, //5
      DOI_TUONG_KCB.NHAN_THUOC_THEO_HEN, //7
      DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_SU_DUNG_THUOC, //8
      DOI_TUONG_KCB.CAC_TRUONG_HOP_KHAC, //10
    ],
  },
  {
    id: 2,
    ten: "Nội trú",
    i18n: "danhMuc.noiTru",
    referIds: [
      DOI_TUONG_KCB.DIEU_TRI_NGOAI_TRU, //2
      DOI_TUONG_KCB.DIEU_TRI_NOI_TRU, //3
      DOI_TUONG_KCB.DIEU_TRI_NOI_TRU_BAN_NGAY, //4
      DOI_TUONG_KCB.DIEU_TRI_LUU_TRU_TAI_TRAM_Y_TE_TUYEN_XA, //6
      DOI_TUONG_KCB.DIEU_TRI_NOI_TRU_DUOI_BON_GIO, //9
    ],
  },
];

export const PHAN_LOAI_DOI_TUONG_KCB2 = [
  {
    id: 1,
    ten: "Ngoại trú",
    i18n: "common.ngoaiTru",
    referIds: [
      DOI_TUONG_KCB.NGOAI_TRU, //1
      DOI_TUONG_KCB.DIEU_TRI_NGOAI_TRU, //2
      DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_LINH_THUOC, //5
      DOI_TUONG_KCB.NHAN_THUOC_THEO_HEN, //7
      DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_SU_DUNG_THUOC, //8
      DOI_TUONG_KCB.CAC_TRUONG_HOP_KHAC, //10
    ],
  },
  {
    id: 2,
    ten: "Nội trú",
    i18n: "danhMuc.noiTru",
    referIds: [
      DOI_TUONG_KCB.DIEU_TRI_NOI_TRU, //3
      DOI_TUONG_KCB.DIEU_TRI_NOI_TRU_BAN_NGAY, //4
      DOI_TUONG_KCB.DIEU_TRI_LUU_TRU_TAI_TRAM_Y_TE_TUYEN_XA, //6
      DOI_TUONG_KCB.DIEU_TRI_NOI_TRU_DUOI_BON_GIO, //9
    ],
  },
];

export const NHOM_DON_VI_TINH = {
  ONG: "Ống",
  VIEN: "Viên",
};

export const SPLIT_PANEL_CACHEKEY = {
  TIEP_DON_DICH_VU_1: "TIEP_DON_DICH_VU_1",
  TIEP_DON_DICH_VU_2: "TIEP_DON_DICH_VU_2",
  TIEP_DON_DICH_VU_3: "TIEP_DON_DICH_VU_3",
  TIEP_DON_DICH_VU_4: "TIEP_DON_DICH_VU_4",
  TIEP_DON_DICH_VU_5: "TIEP_DON_DICH_VU_5",
  TIEP_DON_DICH_VU_6: "TIEP_DON_DICH_VU_6",
};

export const KIEU_GOI_SO = {
  STT_QUAY: "Xin mời người bệnh số thứ tự {STT} {UU_TIEN} vào quầy {QUAY}",
  STT_PHONG: "Xin mời người bệnh số thứ tự {STT} {UU_TIEN} vào phòng {PHONG}",
  TEN_NGUOI_BENH_QUAY:
    "Xin mời người bệnh {TEN_NGUOI_BENH} {UU_TIEN} vào quầy {QUAY}",
  TEN_NGUOI_BENH_PHONG:
    "Xin mời người bệnh {TEN_NGUOI_BENH} {UU_TIEN} vào phòng {PHONG}",
  TEN_NGUOI_BENH_STT_QUAY:
    "Xin mời người bệnh {TEN_NGUOI_BENH} số thứ tự {STT} {UU_TIEN} vào quầy {QUAY}",
  TEN_NGUOI_BENH_STT_PHONG:
    "Xin mời người bệnh {TEN_NGUOI_BENH} số thứ tự {STT} {UU_TIEN} vào phòng {PHONG}",
  TEN_NGUOI_BENH_TUOI_QUAY:
    "Xin mời người bệnh {TEN_NGUOI_BENH} {TUOI} {UU_TIEN} vào quầy {QUAY}",
  TEN_NGUOI_BENH_TUOI_PHONG:
    "Xin mời người bệnh {TEN_NGUOI_BENH} {TUOI} {UU_TIEN} vào phòng {PHONG}",
  TEN_NGUOI_BENH_TUOI_STT_QUAY:
    "Xin mời người bệnh {TEN_NGUOI_BENH} {TUOI} số thứ tự {STT} {UU_TIEN} vào quầy {QUAY}",
  TEN_NGUOI_BENH_TUOI_STT_PHONG:
    "Xin mời người bệnh {TEN_NGUOI_BENH} {TUOI} số thứ tự {STT} {UU_TIEN} vào phòng {PHONG}",
};

export const LIST_KIEU_GOI_SO = [
  {
    id: KIEU_GOI_SO.STT_QUAY,
    ten: KIEU_GOI_SO.STT_QUAY,
  },
  {
    id: KIEU_GOI_SO.STT_PHONG,
    ten: KIEU_GOI_SO.STT_PHONG,
  },
  {
    id: KIEU_GOI_SO.TEN_NGUOI_BENH_QUAY,
    ten: KIEU_GOI_SO.TEN_NGUOI_BENH_QUAY,
  },
  {
    id: KIEU_GOI_SO.TEN_NGUOI_BENH_PHONG,
    ten: KIEU_GOI_SO.TEN_NGUOI_BENH_PHONG,
  },
  {
    id: KIEU_GOI_SO.TEN_NGUOI_BENH_STT_QUAY,
    ten: KIEU_GOI_SO.TEN_NGUOI_BENH_STT_QUAY,
  },
  {
    id: KIEU_GOI_SO.TEN_NGUOI_BENH_STT_PHONG,
    ten: KIEU_GOI_SO.TEN_NGUOI_BENH_STT_PHONG,
  },
  {
    id: KIEU_GOI_SO.TEN_NGUOI_BENH_TUOI_QUAY,
    ten: KIEU_GOI_SO.TEN_NGUOI_BENH_TUOI_QUAY,
  },
  {
    id: KIEU_GOI_SO.TEN_NGUOI_BENH_TUOI_PHONG,
    ten: KIEU_GOI_SO.TEN_NGUOI_BENH_TUOI_PHONG,
  },
  {
    id: KIEU_GOI_SO.TEN_NGUOI_BENH_TUOI_STT_QUAY,
    ten: KIEU_GOI_SO.TEN_NGUOI_BENH_TUOI_STT_QUAY,
  },
  {
    id: KIEU_GOI_SO.TEN_NGUOI_BENH_TUOI_STT_PHONG,
    ten: KIEU_GOI_SO.TEN_NGUOI_BENH_TUOI_STT_PHONG,
  },
];

export const LIST_THUOC_NOI = [
  {
    id: true,
    ten: "Thuốc nội",
    i18n: "baoCao.thuocNoi",
  },
  {
    id: false,
    ten: "Thuốc ngoại",
    i18n: "baoCao.thuocNgoai",
  },
];

export const TRANG_THAI_LAY_MAU_BN_HOAN = [
  { ten: "Chờ tiếp nhận", id: 25, i18n: "xetNghiem.choTiepNhan" },
  { ten: "Chờ lấy mẫu", id: 38, i18n: "xetNghiem.choLayMau" },
  { ten: "Bỏ qua", id: 50, i18n: "common.boQua" },
  { ten: "Hủy mẫu", id: 80, i18n: "xetNghiem.huyMau" },
  { ten: "Đã hoàn", id: 30, i18n: "cdha.daHoan" },
];

export const HIEN_THI_SL_NB_THEO_PK = [
  {
    id: "1",
    ten: "Hiển thị phòng khám theo khu vực",
    i18n: "tiepDon.hienThiPhongKhamTheoKhuVuc",
  },
  {
    id: "2",
    ten: "Hiển thị tất cả phòng khám",
    i18n: "tiepDon.hienThiTatCaPhongKham",
  },
];

export const TRANG_THAI_HEN_NOI_SOI = {
  CHUA_HEN: 10,
  DA_HEN: 20,
  HUY_HEN: 30,
};

export const LIST_SO_SANH = [
  {
    label: "Nhỏ hơn",
    ten: "<",
    id: 10,
  },
  {
    label: "Nhỏ hơn hoặc bằng",
    ten: "≤",
    id: 20,
  },
  {
    label: "Bằng",
    ten: "=",
    id: 30,
  },
  {
    label: "Lớn hơn",
    ten: ">",
    id: 50,
  },
  {
    label: "Lớn hơn hoặc bằng",
    ten: "≥",
    id: 40,
  },
];

export const MAN_HINH_AP_DUNG = {
  KHAM_BENH: 10,
  NOI_TRU: 20,
};

export const HIEN_THI_LOAI_QMS = {
  HIEN_THI_STT: 10,
  HIEN_THI_HO_TEN_STT: 20,
  HIEN_THI_HO_TEN_TUOI: 30,
  HIEN_THI_HO_TEN_STT_TUOI: 40,
};

export const TRANG_THAI_CHOT_SO = [
  {
    id: 20,
    ten: "Đã chốt sổ",
    i18n: "baoCao.daChotSo",
  },
  {
    id: 10,
    ten: "Chưa chốt sổ",
    i18n: "baoCao.chuaChotSo",
  },
];

export const LOAI_PHIEU_THU_NHAP_VAO = [
  {
    id: 10,
    ten: "Biên lai thu phí",
    i18n: "baoCao.bienLaiThuPhi",
  },
  {
    id: 20,
    ten: "Hóa đơn thu phí",
    i18n: "baoCao.hoaDonThuPhi",
  },
  {
    id: 30,
    ten: "Thu nhà thuốc",
    i18n: "baoCao.thuNhaThuoc",
  },
];

export const TRANG_THAI_XAC_NHAN_BHYT = {
  CHUA_XAC_NHAN: 10,
  DA_XAC_NHAN: 20,
};

export const LOAI_YEU_CAU = {
  // enums?name=LoaiYeuCau API: /api/crm/v1/
  TINH_NANG: 10,
  LOI: 50,
};

export const CO_CHE_DUYET_PHAT = {
  //enums?name=CoCheDuyetPhat
  DUYET_PHAT_CHUNG: 10,
  DUYET_PHAT_NGAY_KHI_KE: 20,
  DUYET_SAU_KHI_XUAT_HUY: 30,
  XAC_NHAN_NHAP: 40,
  KHONG_DUYET_NHAP: 50,
};

export const TIEP_DON_CLS = [
  {
    id: 1,
    ten: "Ngoại trú",
    i18n: "danhMuc.ngoaiTru",
  },
  {
    id: 3,
    ten: "Nội trú",
    i18n: "danhMuc.noiTru",
  },
];

export const CO_KHONG = [
  {
    id: true,
    ten: "Có",
    i18n: "common.co",
  },
  {
    id: false,
    ten: "Không",
    i18n: "common.khong",
  },
];

export const TRANG_THAI_HOAN_THANH_TOAN = {
  MOI: 10,
  CHO_KTT_KY: 11,
  KTT_DA_KY: 12,
  CTK_DA_KY: 13,
  CHO_XU_LY: 20,
  DANG_XU_LY: 30,
  THANH_CONG: 40,
  THAT_BAI: 50,
  QUA_THOI_GIAN: 60,
  CHO_XU_LY_NGAY_HOM_SAU: 70,
  KHONG_XU_LY: 80,
};

export const HINH_THUC_HOAN_THANH_TOAN = {
  CHUYEN_TIEN_NHANH_DON_247: 10,
  CHUYEN_TIEN_NHANH_THEO_LO: 20,
  CHUYEN_TIEN_THUONG_DON: 30,
  CHUYEN_TIEN_THUONG_THEO_LO: 40,
};

export const TRANG_THAI_THANH_TOAN_NCC = {
  CHUA_THANH_TOAN: 10,
  DA_THANH_TOAN: 50,
};

export const TRANG_THAI_BAN_GIAO_THUOC = {
  CHUA_BAN_GIAO: 10,
  DA_BAN_GIAO: 20,
};

export const TRANG_THAI_XAC_NHAN_BAO_HIEM = [
  {
    id: 10,
    ten: "Chưa xác nhận BHYT",
    i18n: "baoCao.chuaXacNhanBhyt",
  },
  {
    id: 20,
    ten: "Đã xác nhận BHYT",
    i18n: "baoCao.daXacNhanBhyt",
  },
];

export const PHAN_LOAI = {
  DA_BAO_GOM: 10,
  CHUA_BAO_GOM_AP_TRAN: 20,
  CHUA_BAO_GOM_KHONG_AP_TRAN: 30,
};

export const LOAI_DANH_GIA_DD = {
  //enums?name=loaidanhgiadd
  CAN_NANG_THEO_TUOI: 10,
  CHIEU_CAO_THEO_TUOI: 20,
  BMI_THEO_TUOI: 30,
  CAN_NANG_THEO_CHIEU_CAO: 40,
};

export const TRANG_THAI_HOI_CHAN = {
  CHUA_DUYET: 0,
  HOAN_THANH: 10,
};

export const TRANG_THAI_THE_BAO_HIEM = {
  THE_DUNG: 10,
  THE_LOI: 20,
  CHUA_KIEM_TRA: 30,
  DA_DUYET_THE: 50,
};

export const DOI_TUONG_NGUOI_BENH = [
  {
    id: 1,
    ten: "Dịch vụ",
    i18n: "common.dichVu",
  },
  {
    id: 2,
    ten: "Bảo hiểm",
    i18n: "baoCao.baoHiem",
  },
];

export const LOAI_PTTT = [
  {
    id: 30,
    ten: "Cấp cứu",
    i18n: "pttt.capCuu",
  },
  {
    id: 40,
    ten: "Chương trình/ Phiên",
    i18n: "hoiChan.chuongTrinhPhien",
  },
  {
    id: 60,
    ten: "Bán cấp",
    i18n: "hoiChan.banCap",
  },
];

export const LOAI_GIA = {
  GIA_KHONG_BAO_HIEM: 10,
  GIA_BAO_HIEM: 20,
  GIA_PHU_THU: 30,
};

export const HIEN_THI_PHIEU_IN = {
  LUON_HIEN_THI_HSBA: 10,
  IN_HSBA: 20,
  IN_HO_SO_SINH: 30,
};

export const LOAI_THU_NHAP_VAO = [
  {
    id: 10,
    ten: "Thu theo khung giá",
    i18n: "baoCao.thuTheoKhungGia",
  },
  {
    id: 20,
    ten: "Thu theo yêu cầu",
    i18n: "baoCao.thuTheoTheoYeuCau",
  },
  {
    id: 30,
    ten: "Thu nhà thuốc",
    i18n: "baoCao.thuNhaThuoc",
  },
];

export const THEME_KEY = {
  HO_SO_BENH_AN: "hoSoBenhAn",
  CHI_TIET_HO_SO_BENH_AN: "chiTietHoSoBenhAn",
  EDITOR: "editor",
  LUU_TRU_BENH_AN: "luuTruBenhAn",
};
export const PHAN_LOAI_KET_QUA_XET_NGHIEM = {
  BINH_THUONG: 0,
  THAP: 10,
  CAO: 20,
  BAT_THUONG: 30,
};

export const TAI_TRO_BHBL = [
  {
    id: "true",
    ten: "Tài trợ - BHBL",
    i18n: "baoCao.taiTroBHBL",
  },
  {
    id: "false",
    ten: "Không Tài trợ - BHBL",
    i18n: "baoCao.khongTaiTroBHBL",
  },
];

export const LOAI_PHU_CAP_PTTT = {
  DICH_VU_BAC_SI: 10,
  THEO_QUY_DINH_NHA_NUOC: 20,
};

export const TRANG_THAI_PHIEU_NHAP_XUAT_2 = {
  CHUA_SOAN_DON: 10,
  CHO_PHAT: 20,
};
