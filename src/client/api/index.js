export const FILE = "/files";
export const USER_INFO = "/utils/me";
export const COM_API_URL = "/api/com/v1";

export const ZONES = "/dm-xa-phuong";
export const SSO_LOGIN = "/auth/sso-login";
export const AUTH_LOGIN = "/auth/login";
export const AUTH_LOGOUT = "/auth/logout";
export const AUTH_REFRESH = "/auth/refresh";
export const NB_GIAY_TO_TUY_THAN = "/nb-giay-to-tuy-than";
export const NB_LAY_SO_TIEP_DON = "/nb-lay-so-tiep-don";
export const NB_LAY_SO = "/nb-lay-so";
export const NB_DOT_DIEU_TRI = "/nb-dot-dieu-tri";
export const NB_MA_BENH_AN = "/nb-ma-benh-an";
export const NB_GOI_DICH_VU = "/nb-goi-dv";
export const NB_GOI_DICH_VU_TAM_UNG = "/nb-goi-dv-tam-ung";
export const NB_GOI_DICH_VU_HOAN_UNG = "/nb-goi-dv-hoan-ung";
export const NB_GOI_DICH_VU_CHI_TIET = "/nb-goi-dv-chi-tiet";
export const NB_THE_BAO_HIEM = "/nb-the-bao-hiem";
export const NB_DIA_CHI = "/nb-dia-chi";
export const NB_THONG_TIN = "/nb-thong-tin";
export const NB_TIEM_CHUNG = "/nb-tiem-chung";
export const NB_DV_KY_THUAT = "/nb-dv-ky-thuat";
export const NB_BO_CHI_DINH = "/nb-bo-chi-dinh";
export const NB_CHI_SO_SONG = "/nb-chi-so-song";
export const NB_THE_NB = "/nb-the-nb";
export const UTILS = "/utils";
export const C06 = "/c06";
// categories
export const DM_CHUC_VU = "/dm-chuc-vu";
export const DM_LY_DO_DOI_TRA = "/dm-ly-do-doi-tra";
export const DM_QUOC_GIA = "/dm-quoc-gia";
export const DM_TINH_THANH = "/dm-tinh-thanh-pho";
export const DM_QUAN_HUYEN = "/dm-quan-huyen";
export const DM_QUAN_HUYEN_TONG_HOP = "/dm-quan-huyen/tong-hop";
export const DM_MA_BENH = "/dm-ma-benh";
export const DM_MA_PTTT = "/dm-ma-pt-tt";
export const DM_NHOM_PTTT = "/dm-nhom-pt-tt";
export const DM_NHOM_PHA_CHE = "/dm-pha-che";
export const DM_CHI_TIET_PHA_CHE = "/dm-pha-che-chi-tiet";
export const DM_CHUONG_PTTT = "/dm-chuong-pt-tt";
export const DM_LOAI_PTTT = "/dm-loai-pt-tt";
export const DM_NGHE_NGHIEP = "/dm-nghe-nghiep";
export const DM_NGUOI_DAI_DIEN = "/dm-nguoi-dai-dien";
export const DM_DAN_TOC = "/dm-dan-toc";
export const DM_KHU_VUC = "/dm-khu-vuc";
export const DM_LOI_DAN = "/dm-loi-dan";
export const DM_MOI_QUAN_HE = "/dm-moi-quan-he";
export const DM_QUAN_HAM = "/dm-quan-ham";
export const DM_PHONG = "/dm-phong";
export const DM_PHONG_THOI_GIAN_HOAT_DONG = "/dm-phong-thoi-gian-hoat-dong";
export const DM_DV_KY_THUAT = "/dm-dv-ky-thuat";
export const DM_DICH_VU = "/dm-dich-vu";
export const DM_THE_BAO_HIEM = "/dm-the-bao-hiem";
export const DM_BENH_PHAM = "/dm-benh-pham";
export const DM_VAN_BANG = "/dm-van-bang";
export const DM_QUAY_TIEP_DON = "/dm-quay-tiep-don";
export const DM_TOA_NHA = "/dm-toa-nha";
export const DM_LOAI_CAP_CUU = "/dm-loai-cap-cuu";
export const DM_VI_TRI_CHAN_THUONG = "/dm-vi-tri-chan-thuong";
export const DM_NHOM_CHI_SO = "/dm-nhom-chi-so-con";
export const DM_BENH_VIEN = "/dm-benh-vien";
export const DM_LIEU_DUNG = "/dm-lieu-dung";
export const DM_NHOM_CHI_PHI = "/dm-nhom-chi-phi";
export const DM_LOAI_BUA_AN = "/dm-loai-bua-an";
export const DM_LY_DO_TAM_UNG = "/dm-ly-do-tam-ung";
export const DM_LY_DO_DEN_KHAM = "/dm-ly-do-den-kham";
export const DM_LY_DO_CHI_DINH_DICH_VU = "/dm-ly-do-chi-dinh"
export const DM_DV_NHOM_CHI_PHI = "/dm-dv-nhom-chi-phi";
export const DM_THIET_LAP_STT_UU_TIEN = "/dm-thiet-lap/stt-thu-ngan";
export const DM_DUONG_DUNG = "/dm-duong-dung";
export const DM_MA_MAY = "/dm-ma-may";
export const DM_HOAT_CHAT = "/dm-hoat-chat";
export const DM_NHA_SAN_XUAT = "/dm-nha-san-xuat";
export const DM_LOAI_BENH_AN = "/dm-loai-benh-an";
export const DM_PHUONG_PHAP_VO_CAM = "/dm-phuong-phap-vo-cam";
export const DM_PHUONG_PHAP_NHUOM = "/dm-phuong-phap-nhuom";
export const DM_VI_TRI_SINH_THIET = "/dm-vi-tri-sinh-thiet";
export const DM_PHAN_LOAI_DICH_VU_KHO = "/dm-phan-loai-dv-kho";
export const DM_PHAN_LOAI_PHCN = "/dm-phan-loai-phcn";
export const DM_LOA_GOI_SO = "/dm-loa-goi-so";
export const DM_TAI_NAN_THUONG_TICH = "/dm-tai-nan-thuong-tich";
export const DM_NHOM_DICH_VU_KHO_CAP_1 = "/dm-nhom-dv-kho-cap1";
export const DM_NHOM_DICH_VU_KHO_CAP_2 = "/dm-nhom-dv-kho-cap2";
export const DM_NHOM_DICH_VU_KHO_CAP_3 = "/dm-nhom-dv-kho-cap3";
export const DM_NHOM_DICH_VU_KHO_CAP_4 = "/dm-nhom-dv-kho-cap4";
export const DM_PHAN_NHOM_DICH_VU_KHO = "/dm-phan-nhom-dv-kho";
export const DM_THOI_GIAN_CAP_CUU = "/dm-thoi-gian-cap-cuu";
export const DM_CHUONG_BENH = "/dm-chuong-benh";
export const DM_NHOM_BENH = "/dm-nhom-benh";
export const DM_LOAI_BENH = "/dm-loai-benh";
export const DM_DICH_VU_KHO = "/dm-dv-kho";
export const DM_KHOA_CHI_DINH_DICH_VU = "/dm-dv-khoa-chi-dinh";
export const DM_DICH_VU_KEM_THEO = "/dm-dv-kem-theo";
export const DM_CHI_SO_CON = "/dm-chi-so-con";
export const DM_GOI_DICH_VU_CHI_TIET = "/dm-goi-dv-chi-tiet";
export const DM_DV_PHONG = "/dm-dv-phong";
export const DM_DV_GIA = "/dm-dv-gia";
export const DM_LOAI_DOI_TUONG = "/dm-loai-doi-tuong";
export const DM_DV_DIEU_KIEN_BHYT = "/dm-dv-dieu-kien-bhyt";
export const DM_LOAI_DOI_TUONG_LOAI_HINH_TT = "/dm-loai-doi-tuong-loai-hinh-tt";
export const DM_LOAI_DOI_TUONG_PHUONG_THUC_TT =
  "/dm-loai-doi-tuong-phuong-thuc-tt";
export const DM_KHOA = "/dm-khoa";
export const DM_KHO = "/dm-kho";
export const DM_HOCHAM_HOCVI = "/dm-hoc-ham-hoc-vi";
export const DM_DON_VI = "/dm-don-vi";
export const DM_BAO_CAO = "/dm-bao-cao";
export const DM_MAY_IN = "/nv-may-tinh";
export const DM_CHUYEN_KHOA = "/dm-chuyen-khoa";
export const DM_DON_VI_TINH = "/dm-don-vi-tinh";
export const DM_NHOM_DICH_VU_CAP_1 = "/dm-nhom-dich-vu-cap1";
export const DM_NHOM_DICH_VU_CAP_2 = "/dm-nhom-dich-vu-cap2";
export const DM_NHOM_DICH_VU_CAP_3 = "/dm-nhom-dich-vu-cap3";
export const DM_NHOM_DON_VI_TINH = "/dm-nhom-don-vi-tinh";
export const DM_DICH_VU_KHO_LIEU_DUNG = "/dm-dv-kho-lieu-dung";
export const DM_DOI_TAC = "/dm-doi-tac";
export const LICH_SU = "/lich-su";
export const DM_DOI_TAC_LICH_SU = "/dm-doi-tac-lich-su";
export const DM_THIET_LAP = "/dm-thiet-lap";
export const DM_NHOM_TINH_NANG = "/dm-nhom-tinh-nang";
export const DM_QUYEN = "/dm-quyen";
export const DM_MAU_KET_QUA_XN = "/dm-mau-ket-qua-xn";
export const DM_MAU_KET_QUA_KSK = "/dm-mau-kham-ksk";
export const DM_MAU_KET_QUA_PT_TT = "/dm-mau-kq-cdha-tdcn-pt-tt";
export const DM_VAI_TRO = "/dm-vai-tro";
export const DM_NOI_LAY_BENH_PHAM = "/dm-noi-lay-benh-pham";
export const DM_PHUONG_THUC_TT = "/dm-phuong-thuc-tt";
export const DM_BO_CHI_DINH = "/dm-bo-chi-dinh";
export const DM_BO_CHI_DINH_CHI_TIET = "/dm-bo-chi-dinh-chi-tiet";
export const NB_SO_PHIEU_CLS = "/nb-so-phieu-cls";
export const DM_CHUONG_TRINH_GIAM_GIA = "/dm-chuong-trinh-giam-gia";
export const DM_MA_GIAM_GIA = "/dm-ma-giam-gia";
export const DM_NGUOI_GIOI_THIEU = "/dm-nguoi-gioi-thieu";
export const DM_NGUOI_GIOI_THIEU_THANH_TOAN = "/dm-nguoi-gioi-thieu-thanh-toan";
export const DM_NGUON_NB = "/dm-nguon-nb";
export const DM_HANG_THE = "/dm-hang-the";
export const DM_XUAT_XU = "/dm-xuat-xu";
export const DM_THANG_SO_BAN_LE = "/dm-thang-so-ban-le";
export const DM_QUYEN_KY = "/dm-quyen-ky";
export const DM_BAO_CAO_CHAN_KY = "/dm-bao-cao-chan-ky";
export const DM_BAO_CAO_HOAN_THANH_KY = "/dm-bao-cao-hoan-thanh-ky";
export const DM_LOAI_BAO_CAO = "/dm-loai-bao-cao";
export const DM_THIET_LAP_HANG_DOI = "/dm-thiet-lap-hang-doi";
export const DM_KIOSK = "/dm-kiosk";
export const DM_TEMPLATE = "/dm-mau-qms";
export const DM_THUOC_CHI_DINH_NGOAI = "/dm-thuoc-chi-dinh-ngoai";
export const DM_THUOC_CHI_DINH_NGOAI_LIEU_DUNG =
  "/dm-thuoc-chi-dinh-ngoai-lieu-dung";
export const DM_HUONG_DAN_SU_DUNG = "/dm-tai-lieu-hdsd";
export const DM_MAU_KET_QUA_CDHA = "/dm-mau-kq-cdha-tdcn-pt-tt";
export const DM_HOI_DONG_KIEM_KE = "/dm-hoi-dong-kiem-ke";
export const DM_HOI_DONG_KIEM_KE_CHI_TIET = "/dm-hoi-dong-kiem-ke-chi-tiet";
export const DM_CHI_NHANH = "/dm-co-so-kcb";
export const DM_DON_VI_Y_TE = "/dm-don-vi-kcb";
export const DM_KICH_CO_VT = "/dm-kich-co-vt";
export const DM_MAU_DIEN_BIEN = "/dm-mau-dien-bien";
export const DM_CHI_SO_SONG = "/dm-chi-so-song";
export const DM_NGAY_NGHI_LE = "/dm-ngay-nghi-le";
export const DM_PHAN_LOAI_BMI = "/dm-phan-loai-bmi";
export const DM_HOP_DONG_KSK = "/dm-hop-dong-ksk";
export const DM_HOP_DONG_KSK_TRANG_THAI_TT = "/dm-hd-ksk-trang-thai-tt";
export const DM_HOP_DONG_KSK_GIAM_GIA = "/dm-hd-ksk-giam-gia";
export const DM_LOAI_GIUONG = "/dm-loai-giuong";
export const DM_SO_HIEU_GIUONG = "/dm-giuong";
export const DM_DICH_VU_GIUONG = "/dm-dv-giuong";
export const DM_THIET_LAP_CHON_GIUONG = "/dm-thiet-lap-chon-giuong";
export const DM_BAC_SI_NGOAI_VIEN = "/dm-bac-si-ngoai-vien";
export const DM_GOI_DV = "/dm-goi-dv";
export const DM_CHE_DO_CHAM_SOC = "/dm-che-do-cham-soc";
export const DM_MAU_DU_LIEU = "/dm-mau-du-lieu";
export const DM_LOAI_HINH_THANH_TOAN = "/dm-loai-hinh-thanh-toan";
export const DM_HAU_QUA_TUONG_TAC = "/dm-hau-qua-tuong-tac";
export const DM_DAC_TINH_DUOC_LY = "/dm-dac-tinh-duoc-ly";
export const DM_MUC_DO_TUONG_TAC = "/dm-muc-do-tuong-tac";
export const DM_MUC_DO_BANG_CHUNG = "/dm-muc-do-bang-chung";
export const DM_TUONG_TAC_THUOC = "/dm-tuong-tac-thuoc";
export const DM_DV_MUC_DICH = "/dm-dv-muc-dich";
export const DM_BAO_CAO_LICH_SU = "/dm-bao-cao-lich-su";
export const DM_CHI_PHI_HAP_SAY_VTYT = "/dm-dv-kho-phi-hap-say";
export const DM_NHAN_THEO_DOI = "/dm-nhan-gay-me";
export const DM_KHANG_NGUYEN = "/dm-khang-nguyen";
export const DM_LUOC_DO_PT = "/dm-luoc-do-pt";
export const DM_PHUONG_PHAP_CHE_BIEN = "/dm-phuong-phap-che-bien";
export const DM_PHAN_LOAI_NB = "/dm-phan-loai-nb";
export const DM_MA_PHIEU_LINH = "/dm-phieu-linh";
export const DM_CA_LAM_VIEC = "/dm-ca-lam-viec";
export const DM_PHIEU_SANG_LOC_TC = "/dm-phieu-sang-loc-tc";
export const DM_CAU_HOI_SANG_LOC_TC = "/dm-cau-hoi-sang-loc-tc";
export const DM_MAU_KQ_XN_DOT_BIEN = "/dm-mau-kq-xn-dot-bien";
export const DM_MAN_HINH_PHIEU_IN = "/dm-man-hinh-phieu-in";
export const DM_VI_TRI_PHIEU_IN = "/dm-vi-tri-phieu-in";
export const DM_PHIEU_IN = "/dm-phieu-in";
export const DM_QUY_TRINH_XET_NGHIEM = "/dm-quy-trinh-xn";
export const DM_THIET_LAP_POWERBI = "/dm-dashboard-bi";
export const DM_THI_LUC = "/dm-thi-luc";
export const DM_XANG_DAU = "/dm-xang-dau";
export const DM_PHAN_LOAI_PHUONG_PHAP_VO_CAM = "/dm-phan-loai-pp-vo-cam";
export const DM_TUONG_TAC_THUOC_XN = "/dm-tuong-tac-thuoc-xn";
export const DM_TUONG_TAC_THUOC_XN_CSC = "/dm-tuong-tac-thuoc-xn-csc";
export const DM_DV_MA_PTTT = "/dm-dv-ma-pt-tt";
export const DM_BENH_Y_HOC_CO_TRUYEN = "/dm-ma-benh-yhct";
export const DM_LOAI_NHIEM_KHUAN = "/dm-loai-nhiem-khuan";
export const DM_PHAC_DO_DIEU_TRI = "/dm-phac-do-dieu-tri";
export const DM_PHAC_DO_DIEU_TRI_KHOA_CHI_DINH =
  "/dm-phac-do-dieu-tri-khoa-chi-dinh";
export const DM_PHAC_DO_DIEU_TRI_DICH_VU = "/dm-phac-do-dieu-tri-dich-vu";
export const DM_MAU_BENH_AN_VAO_VIEN = "/dm-mau-benh-an";
export const DM_VI_KHUAN = "/dm-vi-khuan";
export const DM_MA_DOI_TUONG_KCB = "/dm-ma-doi-tuong-kcb";
export const DM_BANG_LAI_XE = "/dm-bang-lai-xe";
export const DM_TRA_CUU_TT = "/dm-tra-cuu-tt";
export const DM_DK_HANH_NGHE = "/dm-dk-hanh-nghe";
export const DM_LAO_KHANG_THUOC = "/dm-lao-khang-thuoc";
export const DM_PHUONG_PHAP_CHAN_DOAN = "/dm-phuong-phap-chan-doan";
export const DM_DINH_MUC_THUOC_VTYT = "/dm-dinh-muc-thuoc-vtyt";
export const DM_PHUONG_PHAP_CD_LAO_KHANG_THUOC =
  "/dm-phuong-phap-cd-lao-khang-thuoc";
export const DM_PHAN_LOAI_TIEN_SU = "/dm-phan-loai-tien-su";
export const DM_LOAI_PHIEU_IN = "/dm-loai-phieu-in";

export const DM_NGUON_KHAC = "/dm-nguon-khac";
export const DM_VAN_DE_THUOC = "/dm-van-de-thuoc";
export const DM_VAN_DE_THUOC_CHI_TIET = "/dm-van-de-thuoc-chi-tiet";
export const DM_XN_SAO_BENH_AN = "/dm-xn-sao-benh-an";
export const DM_NGOI_THAI = "/dm-ngoi-thai";
export const DM_HINH_THUC_SINH = "/dm-hinh-thuc-sinh";
export const DM_BIEN_PHAP_CAM_MAU = "/dm-bien-phap-cam-mau";
export const DM_DI_TAT_BAM_SINH = "/dm-di-tat-bam-sinh";
export const DM_LIEN_IN = "/dm-lien-in";
export const DM_TUONG_TAC_THUOC_TUOI = "/dm-tuong-tac-thuoc-tuoi";
export const DM_NHOM_LOAI_DOI_TUONG = "/dm-nhom-loai-doi-tuong";
export const DM_NHOM_LOAI_BENH_AN = "/dm-nhom-benh-an";
export const DM_THANG_BAO_CAO = "/dm-thang-bao-cao";
export const DM_MA_LUU_TRU_BA = "/dm-ma-luu-tru-ba";
export const DM_TAC_NHAN_DI_UNG = "/dm-tac-nhan-di-ung";
export const DM_THAM_GIA_HOI_CHAN = "/dm-tham-gia-hoi-chan";

export const DM_HOA_HONG = "/dm-hoa-hong";
export const DM_NHOM_DICH_VU_HOA_HONG = "/dm-nhom-dich-vu";
export const DM_DOI_TAC_HOA_HONG = "/dm-doi-tac-hoa-hong";
export const DM_DV_VAT_TU = "/dm-dv-vat-tu";
export const DM_HOA_HONG_CHI_TIET = "/dm-hoa-hong-chi-tiet";
export const DM_HUONG_DIEU_TRI = "/dm-huong-dieu-tri";
export const DM_KET_QUA_DIEU_TRI = "/dm-ket-qua-dieu-tri";
export const DM_PHIM_TAT = "/dm-phim-tat";
export const DM_DOI_TAC_THANH_TOAN = "/dm-doi-tac-thanh-toan";
export const DM_THIET_LAP_SO_PHIEU_NHAP_XUAT =
  "/dm-thiet-lap-so-phieu-nhap-xuat";
export const DM_CO_PHIM = "/dm-co-phim";
export const DM_THUOC_BAN_GIAO = "/dm-thuoc-ban-giao";
export const DM_PHAN_TANG_NGUY_CO = "/dm-phan-tang-nguy-co";
export const DM_PHU_CAP_DV_KY_THUAT = "/dm-phu-cap-dv-ky-thuat";
export const NV_THIET_LAP_UI = "/nv-thiet-lap-ui";
export const DT_LOG = "/dt-log";
export const DM_CHI_SO_DINH_DUONG = "/dm-chi-so-dinh-duong";
export const DM_LOAI_PHCN = "/dm-loai-phcn";
export const DM_DIEU_KIEN_CHI_DINH = "/dm-dieu-kien-chi-dinh";
export const DM_NHOM_HUONG_PHU_CAP_PTTT = "/dm-nhom-huong-phu-cap-pt-tt";
export const DM_HUONG_PHU_CAP_PTTT = "/dm-huong-phu-cap-pt-tt";
export const DM_NHOM_PHU_CAP_PT_TT = "/dm-nhom-phu-cap-pt-tt";
export const DM_PROTOCOL_TEN_TRUONG = "/dm-protocol-ten-truong";
export const DM_PROTOCOL = "/dm-protocol";
export const DM_PROTOCOL_CHI_TIET = "/dm-protocol-chi-tiet";

//setting print
export const THONG_TIN_MAY_TINH = "/thong-tin-may-tinh";

// thu ngan
export const NB_PHIEU_THU = "/nb-phieu-thu";
export const NB_DICH_VU = "/nb-dich-vu";
export const NB_PHIEU_DOI_TRA = "/nb-phieu-doi-tra";
export const NB_HOA_DON_TONG_HOP = "/nb-hoa-don/tong-hop";
export const NB_DV_PHAT_HANH_HOA_DON = "/nb-dich-vu/phat-hanh-hoa-don";
export const NB_HOA_DON = "/nb-hoa-don";
export const NB_HOA_DON_CHI_TIET_TONG_HOP = "/nb-hoa-don-chi-tiet/tong-hop";
export const PHAT_HANH_HOA_DON = "/phat-hanh-hoa-don";
export const BANG_KE_KEM_HDDT_XUAT_GOP = "/bang-ke-kem-hddt-xuat-gop";
export const BIEN_BAN_DIEU_CHINH = "/xem-bien-ban";
export const NB_TAM_UNG = "/nb-tam-ung";
export const NB_THANH_TOAN = "/nb-thanh-toan";
export const NB_TONG_KET_THANH_TOAN = "/nb-tong-ket-thanh-toan";
export const NB_HOAN_THANH_TOAN = "/nb-hoan-thanh-toan";
export const GIAY_DE_NGHI_DOI_TRA_DV = "/giay-de-nghi-doi-tra-dv";
// Xet nghiem
export const NB_DICH_VU_XET_NGHIEM = "/nb-dv-xet-nghiem";
export const NB_DICH_VU_XET_NGHIEM_CHI_SO_CON = "/nb-dv-xet-nghiem-chi-so-con";

// Kham benh
export const NB_DV_KHAM_BENH = "/nb-dv-kham";
export const NB_DV_KHO = "/nb-dv-kho";
export const NB_DV_THUOC_CHI_DINH_NGOAI = "/nb-dv-thuoc-chi-dinh-ngoai";
export const NB_DV_KHAM_SUC_KHOE = "/nb-kham-ksk";
export const NB_GIAY_NGHI_BAO_HIEM = "/nb-giay-nghi-bao-hiem";
export const NB_BIEN_BAN_KIEM_DIEM_TU_VONG = "/nb-bien-ban-kiem-diem-tu-vong";
export const NB_DV_KHAM_SUC_KHOE_BO_SUNG = "/nb-kham-ksk-bo-sung";
export const NB_DV_PHONG = "/nb-dv-phong";

// Nhan vien
export const DM_NHAN_VIEN = "/dm-nhan-vien";
export const DM_NHAN_VIEN_KHOA = "/dm-nhan-vien-khoa";

// Quan ly tai khoa
export const DM_TAI_KHOAN = "/dm-tai-khoan";
export const RESET_MAT_KHAU = "/reset-mat-khau";
export const SAO_CHEP = "/sao-chep";

// Quan ly thong bao
export const NV_THONG_BAO = "/nv-thong-bao";

// CLS, Phau thuat, thu thuat
export const NB_DV_CLS_PT_TT = "/nb-dv-cdha-tdcn-pt-tt";

// Ngoài điều trị
export const NB_DV_NGOAI_DIEU_TRI = "/nb-dv-ngoai-dieu-tri";

// Suất ăn
export const NB_DV_SUAT_AN = "/nb-dv-suat-an";

// Gói PTTT
export const NB_GOI_PT_TT = "/nb-goi-pt-tt";
export const DM_GOI_PT_TT = "/dm-goi-pt-tt";
export const DM_GOI_PT_TT_CHI_TIET = "/dm-goi-pt-tt-chi-tiet";

// Hinh thuc nhap, loai xuat
export const DM_HINH_THUC_NHAP_XUAT = "/dm-hinh-thuc-nhap-xuat";

export const TACH_GOP_PHIEU_XN = "/dm-thiet-lap/tach-phieu-xn";

export const DM_NGUON_NHAP_KHO = "/dm-nguon-nhap-kho";
export const TACH_GOP_PHIEU_DVKT =
  "/dm-thiet-lap/tach-phieu-dv-cdha-tdcn-pt-tt";

// Kho
export const KHO_THIET_LAP_THOI_GIAN = "/kho-thiet-lap-thoi-gian";
export const KHO_THIET_LAP_CHON_KHO = "/kho-thiet-lap-chon-kho";
export const KHO_QUYET_DINH_THAU = "/kho-quyet-dinh-thau";
export const DM_KHO_TRUC_THUOC = "/dm-kho-truc-thuoc";
export const DM_KHO_CHAN_KY = "/dm-kho-chan-ky";

export const KHO_QUYET_DINH_THAU_CHI_TIET = "/kho-quyet-dinh-thau-chi-tiet";
export const KHO_QUYET_DINH_THAU_CHI_TIET_GIAM_GIA =
  "/kho-quyet-dinh-thau-chi-tiet-giam-gia";
export const KHO_QUYET_DINH_THAU_CHI_TIET_CO_SO =
  "/kho-quyet-dinh-thau-chi-tiet-co-so";
export const KHO_PHIEU_NHAP_XUAT = "/kho-phieu-nhap-xuat";
export const KHO_PHIEU_NHAP_XUAT_CHI_TIET = "/kho-phieu-nhap-xuat-chi-tiet";
export const NB_DV_THUOC = "/nb-dv-thuoc";
export const NB_DV_CHE_PHAM_DINH_DUONG = "/nb-dv-che-pham-dd";
export const NB_DV_THUOC_NHA_THUOC = "/nb-dv-thuoc-nha-thuoc";
export const NB_DV_THUOC_DA_CHI_DINH = "/nb-dv-thuoc-da-chi-dinh";
export const KHO_TON_KHO = "/kho-ton-kho";
export const THEO_DOI_NGUOI_BENH_COVID = "/nb-theo-doi-covid";
export const NB_COVID = "/nb-covid";
export const NB_DV_THUOC_DIEU_TRI_COVID = "/nb-dv-thuoc-dieu-tri-covid";
export const NB_DV_CHE_PHAM_MAU = "/nb-dv-che-pham-mau";

export const NHAN_VIEN_KHO = "/dm-nhan-vien-kho";
export const NB_DV_VAT_TU = "/nb-dv-vat-tu";
export const NB_DV_HOA_CHAT = "/nb-dv-hoa-chat";
export const NB_DV_CHE_PHAM_DD = "/nb-dv-che-pham-dd";

export const KHO_THIET_LAP_DV = "/kho-thiet-lap-dv";

export const KHO_PHIEU_NHAP_XUAT_GPP = "/kho-phieu-nhap-xuat-gpp";
export const NB_CAP_PHAT_THUOC = "/nb-dv-thuoc-cap-phat-thuoc";
export const NB_TU_VAN_THUOC = "/nb-tu-van-thuoc";
export const NB_TU_VAN_THUOC_CHI_TIET = "/nb-tu-van-thuoc-chi-tiet";
export const NV_BAN_GIAO_THUOC = "/nv-ban-giao-thuoc";
export const NV_BAN_GIAO_THUOC_CHI_TIET = "/nv-ban-giao-thuoc-chi-tiet";
//ky va in

export const VONG_TAY_NGUOI_BENH = NB_DOT_DIEU_TRI + "/vong-tay";
export const NB_HO_SO = "/nb-ho-so";

//Ky So
export const KY_SO_THIET_LAP_QUYEN_KY = "/dm-nhan-vien-quyen-ky";
export const DANH_SACH_PHIEU_CHO_KY = "/nv-lich-su-ky";

// Ivisitor

export const IVISITOR_QRCODE = "/ivisitor/qr-code";

// bao cao da in

export const BAO_CAO_DA_IN = "/bc-bao-cao";
export const BC_PK = "/bc-pk";
export const BAO_CAO_KHO = "/bc-kho";
export const BAO_CAO_NHA_THUOC = "/bc-nt";
export const BAO_CAO_TIEM_CHUNG = "/bc-tc";
export const BAO_CAO_KSNK = "/bc-ksnk";
export const BC_LAO = "/bc-lao";
export const BC_KHO_DINH_DUONG = "/bc-kho-dinh-duong";
export const BC_DDLS = "/bc-ddls";
export const BC_PHA_CHE = "/bc-pha-che";

//Quyet toan BHYT
export const NB_CHO_TAO_HO_SO = "/nb-cho-tao-ho-so";
export const NB_79A_XML = "/nb-79a-xml";
export const NB_79A_XML0 = "/nb-79a-xml0";
export const NB_79A_XML1 = "/nb-79a-xml1";
export const NB_79A_XML2 = "/nb-79a-xml2";
export const NB_79A_XML3 = "/nb-79a-xml3";
export const NB_79A_XML4 = "/nb-79a-xml4";
export const NB_79A_XML5 = "/nb-79a-xml5";
export const NB_79A_XML1_XOA = "/nb-79a-xml1-xoa";

export const MAU_XUAT_DU_LIEU_XML = {
  XUAT_DU_LIEU_MAU_79_80: "/bc-pk/tc-35",
  XUAT_DU_LIEU_MAU_19_DAY_CONG: "/bc-pk/tc-43",
  XUAT_DU_LIEU_MAU_19_CUA_VIEN: "/bc-pk/tc-43.1",
  XUAT_DU_LIEU_MAU_20_DAY_CONG: "/bc-pk/tc-44",
  XUAT_DU_LIEU_MAU_20_CUA_VIEN: "/bc-pk/tc-44.1",
  XUAT_DU_LIEU_MAU_21: "/bc-pk/tc-45",
  XUAT_DU_LIEU_XML1: "/bc-pk/tc-47",
  XUAT_DU_LIEU_DM_THUOC: "/bc-qt/qt-03",
  XUAT_DU_LIEU_DM_VTYT: "/bc-qt/qt-04",
  XUAT_DU_LIEU_DM_DVKT: "/bc-qt/qt-05",
  XUAT_DU_LIEU_XML2_1: "/bc-qt/qt-06",
  XUAT_DU_LIEU_XML3_1: "/bc-qt/qt-07",
  XUAT_DU_LIEU_XML4_1: "/bc-qt/qt-08",
  XUAT_DU_LIEU_XML5_1: "/bc-qt/qt-09",
  XUAT_DU_LIEU_XML_QT10: "/bc-qt/qt-10",
  XUAT_DU_LIEU_XML_QT11: "/bc-qt/qt-11",
  XUAT_DU_LIEU_XML_QT12: "/bc-qt/qt-12",
  KIEM_TRA_XML_130: "/nb-79a-xml1/kiem-tra-xml-130",
};

//pacs
export const PACS = "/pacs";
export const IWARD = "/iward";

//pdf generate
export const GENERATE_PDF = "from-html";
export const PDF_SAKURA_HEADLESS = "/api/pdf/v1/sakura-headless";
export const GET_EXTERNAL_PDF = "from-pdf";

export const NGOAI_VIEN = "/ngoai-vien";

//nội trú
export const NB_TO_DIEU_TRI = "/nb-to-dieu-tri";
export const NB_CHUYEN_KHOA = "/nb-chuyen-khoa";
export const NB_PHIEU_SO_KET = "/nb-phieu-so-ket";
export const NB_PHIEU_LINH_SUAT_AN = "/nb-phieu-linh-suat-an";
export const NB_DV_GIUONG = "/nb-dv-giuong";
export const NB_BIEN_BAN_HOI_CHAN = "/nb-bien-ban-hoi-chan";
export const NB_BIEN_BAN_HOI_CHAN_TU_VAN = "/nb-bien-ban-hoi-chan-tu-van";
export const NB_TU_VONG = "/nb-tu-vong";
export const NB_GIAY_CHUNG_SINH = "/nb-giay-chung-sinh";
export const NB_KHAM_KSK_LAI_XE = "/nb-kham-ksk-lai-xe";
export const NB_GIAY_NGHI_DUONG_THAI = "/nb-giay-nghi-duong-thai";
export const NB_CHUYEN_DA = "/nb-chuyen-da";
export const NB_CHUYEN_DA_CHI_TIET = "/nb-chuyen-da-chi-tiet";
export const FORM_SIGN_STATUS = "/nb-ho-so-ba/trang-thai-ky";
export const NB_CHUYEN_VIEN = "/nb-chuyen-vien";
export const PHIEU_CHUYEN_VIEN = "/phieu-chuyen-vien";
export const NB_PHIEU_DANG_KY_KHAM = "/nb-phieu-dang-ky-kham";
export const DU_LIEU_MAU = "/du-lieu-mau";
export const BM_DU_LIEU_MAU = "/bm-du-lieu-mau";
export const DU_LIEU = "/du-lieu";
export const NV_QUAN_LY_GIUONG = "/nv-quan-ly-giuong";
export const NB_THONG_TIN_SAN_PHU = "/nb-thong-tin-san-phu";
export const NB_PHA_CHE_THUOC = "/nb-pha-che-thuoc";
export const NB_PHA_CHE_THUOC_CHI_TIET = "/nb-pha-che-thuoc-chi-tiet";
export const NB_CHOT_DOT_DIEU_TRI = "/nb-chot-dot-dieu-tri";

// hồ sơ bệnh án
export const NB_FILE_TAI_LEN = "/nb-file-tai-len";

export const NB_MUON_NB = "/nb-muon-nb";

export const NB_LUU_TRU_BA = "/nb-luu-tru-ba";
export const NB_LUU_TRU_PHIM = "/nb-luu-tru-phim";
export const CHI_SO_SONG_CHUNG = "/chi-so-song-chung";
export const DASHBOARD = "/dashboard";
export const SCAN = "/scan";

export const NB_TOM_TAT_BA = "/nb-tom-tat-ba";

//phục hồi chức năng
export const NB_PHUC_HOI_CN = "/nb-phuc-hoi-cn";
export const NB_HANG_TIEU_HOA = "/nb-hang-tieu-hao";
export const NB_LUONG_GIA_PHCN = "/nb-luong-gia-phcn";
export const NB_PHUC_HOI_CN_KHAM = "/nb-phuc-hoi-cn-kham";

export const NB_LICH_HEN_KHAM = "/nb-lich-hen-kham";

//tiêm chủng
export const NB_KHAM_TIEM_CHUNG = "/nb-kham-tiem-chung";
export const NB_DV_VACXIN = "/nb-dv-vacxin";
export const NB_PHIEU_TIEM_CHUNG = "/nb-phieu-tiem-chung";
export const NB_LICH_SU_TIEM_VACXIN = "/nb-lich-su-tiem-vacxin";

export const LOG_BAN_GHI = "/log-ban-ghi";
export const NB_PHA_CHE = "/nb-pha-che";
export const NB_PHA_CHE_CHI_TIET = "/nb-pha-che-chi-tiet";
export const NB_PHIEU_XUAT_PHA_CHE = "/nb-phieu-xuat-pha-che";

//kế hoạch tổng hợp
export const NB_DUYET_BAO_HIEM = "/nb-duyet-bao-hiem";
export const NB_DON_THUOC = "/nb-don-thuoc";
export const NB_MUON_BA = "/nb-muon-ba";
export const NB_MUON_BA_CHI_TIET = "/nb-muon-ba-chi-tiet";

//kiểm soát nhiễm khuẩn
export const NB_KIEM_SOAT_NHIEM_KHUAN = "/nb-kiem-soat-nhiem-khuan";
export const NB_PHAC_DO_DIEU_TRI = "/nb-phac-do-dieu-tri";

//quản lý dinh dưỡng
export const NB_SANG_LOC_SUY_DD = "/nb-sang-loc-suy-dd";

//NB dịch vụ kỹ thuật
export const NB_DV_KY_THUAT_NGUOI_BENH = "/nb-dv-ky-thuat/nguoi-benh";

//NB khám chuyên khoa mắt
export const NB_KHAM_CHUYEN_KHOA_MAT = "/nb-kham-ck-mat";

//Nb chuyên khoa TMH
export const NB_KHAM_CHUYEN_KHOA_TMH = "/nb-kham-ck-tmh";

//Nb chuyên khoa CMU
export const NB_KHAM_CHUYEN_KHOA_CMU = "/nb-kham-ck-cmu";

//Nb chuyên khoa Răng hàm mặt
export const NB_KHAM_CHUYEN_KHOA_RHM = "/nb-kham-ck-rhm";

//Nb chuyên khoa da liễu
export const NB_KHAM_CHUYEN_KHOA_DA_LIEU = "/nb-kham-ck-da-lieu";

//Nb chuyên khoa khám ngoại
export const NB_KHAM_CHUYEN_KHOA_NGOAI = "/nb-kham-ck-ngoai";

export const NB_KHAM_CHUYEN_KHOA_SAN = "/nb-kham-ck-san";

export const NB_KHAM_CHUYEN_KHOA_NAM = "/nb-kham-ck-nam";

export const NB_KHAM_CHUYEN_KHOA_IVF = "/nb-kham-ck-ivf";

export const MISA = "/misa";

//Danh mục phụ cấp
export const DM_PHU_CAP_PTTT = "/dm-phu-cap-pt-tt";
export const DM_PHU_CAP_PTTT_CHI_TIET = "/dm-phu-cap-pt-tt-chi-tiet";
export const DM_VI_TRI_CHAM_CONG = "/dm-vi-tri-cham-cong";
export const DM_LUONG_GIA_PHCN = "/dm-luong-gia-phcn";
export const DM_LUONG_GIA_PHCN_CHI_TIET = "/dm-luong-gia-phcn-chi-tiet";

//Báo cáo Adr
export const NB_PHIEU_ADR = "/nb-phieu-adr";
export const NB_THU_TU_HSBA = "/nb-thu-tu-hsba";

// Thuốc lao
export const NB_THUOC_LAO = "/nb-thuoc-lao";
export const NB_CAM_KET_DIEU_TRI_LAO = "/nb-cam-ket-dieu-tri-lao";
export const NB_DIEU_TRI_LAO = "/nb-dieu-tri-lao";

// Chỉ số KPIs
export const KPI_DOANH_THU = "/kpi-doanh-thu";
export const KPI_NGAY_GIUONG = "/kpi-ngay-giuong-dtri";
export const KPI_DIEN = "/kpi-dien";
export const KPI_NUOC = "/kpi-nuoc";
export const KPI_XUAT_TOAN = "/kpi-xuat-toan";
export const KPI_CONG_SUAT_GIUONG_TIEN_ICH = "/kpi-cong-suat-giuong-tien-ich";
export const KPI_CONG_SUAT_GIUONG = "/kpi-cong-suat-giuong";
export const KPI_TIEM_CHUNG = "/kpi-tiem-chung";
export const KPI_TIEM_CHUNG_SIEU_NHAN = "/kpi-tiem-chung-sieu-nhan";
export const KPI_KHAM_ROBOT = "/kpi-kham-robot";
export const KPI_PHAU_THUAT_TRONG_NGAY = "/kpi-phau-thuat-trong-ngay";
export const KPI_KHAM_SIEU_NHAN = "/kpi-kham-sieu-nhan";
export const KPI_KHAM_TEN_LUA = "/kpi-kham-ten-lua";
export const KPI_PHAU_THUAT_CHUONG_TRINH = "/kpi-phau-thuat-chuong-trinh";
export const KPI_KHAM_CHUNG = "/kpi-kham-chung";
export const KPI_NB_RA_VIEN = "/kpi-nb-ra-vien";
export const KPI_NB_PHAN_HOI_CHUYEN_TUYEN = "/kpi-nb-phan-hoi-chuyen-tuyen";
export const KPI_NB_SU_DUNG_THUOC = "/kpi-nb-su-dung-thuoc";
export const KPI_NB_TRO_LAI = "/kpi-nb-tro-lai";
export const KPI_NB_TU_VONG = "/kpi-nb-tu-vong";
export const KPI_PTTT_DAC_BIET = "/kpi-pttt-dac-biet";
export const KPI_DOANH_THU_DICH_VU = "/kpi-doanh-thu-dich-vu";
export const KPI_NB_SU_DUNG_BDG = "/kpi-nb-su-dung-bdg";

//hẹn nội soi
export const DM_HEN_NOI_SOI = "/dm-hen-noi-soi";
export const DM_HEN_NOI_SOI_XN = "/dm-hen-noi-soi-xn";
export const NB_HEN_NOI_SOI = "/nb-hen-noi-soi";

export const NB_KHAM_CMU = "/nb-kham-cmu";

// Quản lý nhân lực
export const BC_QUAN_LY_NHAN_LUC = "/bc-quan-ly-nhan-luc";

export const BC_QUAN_TRI = "/bc-quan-tri";
export const BC_QT = "/bc-qt";
export const BC_SANG_LOC_DD = "/bc-sang-loc-dd";
export const BC_THANG_THEO_KHOA = "/bc-thang-theo-khoa";
export const NB_BENH_AN_MUC_A = "/nb-benh-an-muc-a";

// Hoa hồng
export const NB_HOA_HONG = "/nb-hoa-hong";
export const DT_THANH_TOAN = "/dt-thanh-toan";
export const NHA_THUOC = "/nha-thuoc";

//Support
export const DT_YEU_CAU = "/dt-yeu-cau";
export const NV_CHOT_SO = "/nv-chot-so";

//Xác nhận BHYT
export const NB_XAC_NHAN_BAO_HIEM = "/nb-xac-nhan-bao-hiem";

//Lịch sử import
export const LICH_SU_IMPORT = "/lich-su-import";
export const ISC = "/isc";

export const NV_THIET_LAP = "/nv-thiet-lap";
export const KIOSK2 = "/kiosk2";

export const MIMS = "/mims";

// ANA
export const ANA = "/ana";
