import styled from "styled-components";

const Main = styled("div")`
  font-family: Arial, sans-serif;
  font-size: 12px;
  margin: 20px;

  .title-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  & .filter-box {
    display: flex;
    align-items: center;
    justify-content: flex-start;

    margin-bottom: 5px;

    &-label {
      margin-left: 10px;
      margin-right: 5px;
      width: fit-content;
    }

    .custom-select {
      width: 140px;
    }
    .custom-select .ant-select-selection-item,
    .custom-select .ant-select-selection-placeholder {
      font-size: 12px !important;
    }
  }

  h2 {
    color: blue;
    text-align: center;
    font-size: 16px;
    font-weight: bold;
  }
  table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
  }
  table,
  th,
  td {
    border: 1px solid #000;
  }
  th,
  td {
    padding: 5px;
    text-align: center;
  }
  th {
  }
  .sub-header {
    font-style: italic;
  }

  .bottom-sign {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 50px;

    &-item {
      &-title {
        font-weight: bold;
      }
      &-note {
        font-style: italic;
      }
    }
  }
`;

const TableDiv = styled("div")`
  margin-top: 20px;

  & h3 {
    color: blue;
    font-weight: bold;
    font-size: 14px;
  }

  & .filter-box {
    display: flex;
    align-items: center;
    justify-content: flex-start;

    margin-bottom: 5px;

    &-label {
      margin: 0 10px;
    }

    .custom-range-picker {
      width: 300px;
    }
    .custom-range-picker .ant-picker-input > input {
      font-size: 12px !important;
    }

    .custom-select {
      width: 160px;
    }
    .custom-select .ant-select-selection-item,
    .custom-select .ant-select-selection-placeholder {
      font-size: 12px !important;
    }
  }

  .pagination {
    text-align: right;
    & span {
      margin: 0 2px;
    }
  }
`;

export { Main, TableDiv };
