import React, { useState } from "react";
import { TableDiv } from "../styled";
import { DatePicker, Select } from "components";

const { RangePicker } = DatePicker;

const TableTheoDoiChamSoc = ({ data = [], rowsPerPage = 20 }) => {
  const [currentPage, setCurrentPage] = useState(1);

  const totalPages = Math.ceil(data.length / rowsPerPage);

  const handlePrev = () => {
    setCurrentPage((prev) => Math.max(prev - 1, 1));
  };

  const handleNext = () => {
    setCurrentPage((prev) => Math.min(prev + 1, totalPages));
  };

  const startIndex = (currentPage - 1) * rowsPerPage;
  const currentData = data.slice(startIndex, startIndex + rowsPerPage);

  return (
    <TableDiv>
      <h3>4. Theo dõi chăm sóc Điều dưỡng</h3>

      <div className="filter-box">
        <div className="filter-box-label">Thời gian: </div>
        <RangePicker
          className="custom-range-picker"
          placeholder={["Từ thời gian", "Đến thời gian"]}
          format="DD/MM/YYYY HH:mm"
          showTime
        />

        <div className="filter-box-label">Phân cấp chăm sóc: </div>
        <Select
          className="custom-select"
          placeholder={"Chọn phân cấp chăm sóc"}
        />
      </div>

      <table>
        <thead>
          <tr>
            <th>STT</th>
            <th>Mã hồ sơ</th>
            <th>Họ tên BN</th>
            <th>Năm sinh</th>
            <th>Số giường/phòng</th>
            <th>Phân cấp chăm sóc</th>
            <th>Bàn giao</th>
            <th>Ghi chú</th>
          </tr>
        </thead>
        <tbody>
          {currentData.map((row, index) => (
            <tr key={index} className="text-center">
              <td>{startIndex + index + 1}</td>
              <td>{row.maHoSo}</td>
              <td>{row.tennb}</td>
              <td>
                {row.ngaySinh ? moment(row.ngaySinh).format("DD/MM/YYYY") : ""}
              </td>
              <td>{`${row.soHieuGiuong || ""}/${row.tenPhong || ""}`}</td>
              <td>{row.phanCapChamSoc}</td>
              <td>{row.banGiao}</td>
              <td>{row.ghiChu1}</td>
            </tr>
          ))}
        </tbody>
      </table>

      <div className="pagination">
        <button onClick={handlePrev} disabled={currentPage === 1}>
          {`<`}
        </button>
        <span>
          Trang {currentPage} / {totalPages}
        </span>
        <button onClick={handleNext} disabled={currentPage === totalPages}>
          {`>`}
        </button>
      </div>
    </TableDiv>
  );
};

export default TableTheoDoiChamSoc;
