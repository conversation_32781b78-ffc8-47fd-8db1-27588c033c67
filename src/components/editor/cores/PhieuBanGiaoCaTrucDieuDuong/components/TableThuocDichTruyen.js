import moment from "moment";
import React, { useState } from "react";
import { TableDiv } from "../styled";
import { DatePicker, Select } from "components";
import { ENUM } from "constants/index";
import { useEnum } from "hooks";

const { RangePicker } = DatePicker;

const TableThuocDichTruyen = ({ data = [], rowsPerPage = 20 }) => {
  const [listTrangThaiThuoc] = useEnum(ENUM.TRANG_THAI_THUOC);

  const [currentPage, setCurrentPage] = useState(1);

  const totalPages = Math.ceil(data.length / rowsPerPage);

  const handlePrev = () => {
    setCurrentPage((prev) => Math.max(prev - 1, 1));
  };

  const handleNext = () => {
    setCurrentPage((prev) => Math.min(prev + 1, totalPages));
  };

  const startIndex = (currentPage - 1) * rowsPerPage;
  const currentData = data.slice(startIndex, startIndex + rowsPerPage);

  return (
    <TableDiv>
      <h3>1. <PERSON><PERSON><PERSON><PERSON>, d<PERSON><PERSON> t<PERSON><PERSON><PERSON><PERSON> chưa thực hiện / đang thực hiện</h3>

      <div className="filter-box">
        <div className="filter-box-label">Thời gian: </div>
        <RangePicker
          className="custom-range-picker"
          placeholder={["Từ thời gian", "Đến thời gian"]}
          format="DD/MM/YYYY HH:mm"
          showTime
        />

        <div className="filter-box-label">Trạng thái: </div>
        <Select
          data={[
            { id: 1, ten: "Chưa thực hiện" },
            { id: 2, ten: "Đang thực hiện" },
          ]}
          className="custom-select"
          placeholder={"Chọn trạng thái"}
        />
      </div>

      <table>
        <thead>
          <tr>
            <th>STT</th>
            <th>Mã hồ sơ</th>
            <th>Họ tên BN</th>
            <th>Năm sinh</th>
            <th>Số giường / phòng</th>
            <th>Tên thuốc - liều dùng - ghi chú</th>
            <th>SL</th>
            <th>Đơn vị</th>
            <th>Sáng</th>
            <th>Chiều</th>
            <th>Tối</th>
            <th>Đêm</th>
            <th>Trạng thái</th>
            <th>Ghi chú</th>
          </tr>
        </thead>
        <tbody>
          {currentData.map((row, index) => (
            <tr key={index}>
              <td>{startIndex + index + 1}</td>
              <td>{row.maHoSo}</td>
              <td>{row.tennb}</td>
              <td>
                {row.ngaySinh ? moment(row.ngaySinh).format("DD/MM/YYYY") : ""}
              </td>
              <td>{`${row.soHieuGiuong || ""}/${row.tenPhong || ""}`}</td>
              <td>{`${row.tenDichVu || ""}-${row.tenLieuDung || ""}-${
                row.ghiChu || ""
              }-${row.ghiChuCapPhatThuoc || ""}`}</td>
              <td>{row.soLuong}</td>
              <td>{row.tenDonViTinh}</td>
              <td>{row.slSang}</td>
              <td>{row.slChieu}</td>
              <td>{row.slToi}</td>
              <td>{row.slDem}</td>
              <td>
                {listTrangThaiThuoc.find(
                  (x) => x.id == row.trangThaiPhieuNhapXuat
                )?.ten || ""}
              </td>
              <td>{row.ghiChu1}</td>
            </tr>
          ))}
        </tbody>
      </table>

      <div className="pagination">
        <button onClick={handlePrev} disabled={currentPage === 1}>
          {`<`}
        </button>
        <span>
          Trang {currentPage} / {totalPages}
        </span>
        <button onClick={handleNext} disabled={currentPage === totalPages}>
          {`>`}
        </button>
      </div>
    </TableDiv>
  );
};

export default TableThuocDichTruyen;
