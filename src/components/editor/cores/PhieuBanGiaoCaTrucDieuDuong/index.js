import React, { forwardRef } from "react";
import T from "prop-types";
import { useDispatch } from "react-redux";
import { Main } from "./styled";
import TableThuocDichTruyen from "./components/TableThuocDichTruyen";
import TableChePhamMau from "./components/TableChePhamMau";
import TableTheoDoiChamSoc from "./components/TableTheoDoiChamSoc";
import TableDVKT from "./components/TableDVKT";
import BanGiaoKhac from "./components/BanGiaoKhac";
import { Select } from "components";

const PhieuBanGiaoCaTrucDieuDuong = forwardRef((props, ref) => {
  const {
    component: { init },
  } = useDispatch();

  const { mode, component, form } = props;
  const itemProps = component.props;

  return (
    <Main>
      <div className="title-header">
        <div className="title-header-left">
          <h3>{form?.tenBenhVien || ""}</h3>
          <p>Khoa: ...</p>
        </div>
        <div className="title-header-right">
          <p>Số phiếu: {form?.soPhieu || ""}</p>
        </div>
      </div>

      <h2>PHIẾU BÀN GIAO CA TRỰC ĐIỀU DƯỠNG</h2>

      <p>Thời gian bàn giao (ngày, giờ): .......</p>
      <p>Điều dưỡng bàn giao: .......</p>
      <p>Điều dưỡng nhận ca: .......</p>
      <p>Số lượng bệnh nhân bàn giao: ..........</p>

      <div className="filter-box">
        <div className="filter-box-label">Nội dung bàn giao: </div>
        <Select
          data={[
            { id: 1, ten: "Thuốc" },
            { id: 2, ten: "Chế phẩm máu" },
            { id: 3, ten: "DVKT" },
            { id: 4, ten: "Theo dõi chăm sóc" },
          ]}
          className="custom-select"
          placeholder={"Chọn nội dung bàn giao"}
        />

        <div className="filter-box-label">Phòng (giường): </div>
        <Select className="custom-select" placeholder={"Chọn phòng (giường)"} />

        <div className="filter-box-label">Điều dưỡng phụ trách: </div>
        <Select className="custom-select" placeholder={"Chọn điều dưỡng"} />
      </div>

      <TableThuocDichTruyen data={form?.dsThuoc || []} />

      <TableChePhamMau data={form?.dsChePhamMau || []} />

      <TableDVKT data={form?.dsDvKyThuat || []} />

      <TableTheoDoiChamSoc data={form?.dsChamSoc || []} />

      <BanGiaoKhac />

      <div className="bottom-sign">
        <div className="bottom-sign-item">
          <div className="bottom-sign-item-title">Điều dưỡng bàn giao</div>
          <div className="bottom-sign-item-note">(Ký, ghi rõ họ tên)</div>
        </div>

        <div className="bottom-sign-item">
          <div className="bottom-sign-item-title">Điều dưỡng bàn giao</div>
          <div className="bottom-sign-item-note">(Ký, ghi rõ họ tên)</div>
        </div>
      </div>
    </Main>
  );
});

PhieuBanGiaoCaTrucDieuDuong.defaultProps = {
  form: {},
};

PhieuBanGiaoCaTrucDieuDuong.propTypes = {
  form: T.shape({}),
};

export default PhieuBanGiaoCaTrucDieuDuong;
