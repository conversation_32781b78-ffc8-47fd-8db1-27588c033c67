import React, {
  useState,
  useEffect,
  useImperativeHandle,
  forwardRef,
  useRef,
  useMemo,
} from "react";
import T from "prop-types";
import {
  Button,
  Row,
  Col,
  Select,
  Input,
  Checkbox,
  InputNumber,
  Drawer,
  Form,
  Radio,
  message,
  Collapse,
  List,
} from "antd";
import { formItemLayout } from "components/constanst";
import components from "components/editor/cores";
import { get, cloneDeep } from "lodash";
import TableControl from "../TableControl";
import { Main } from "./styled";
import { format } from "../../DatePicker/constants";
import {
  SaveOutlined,
  EditOutlined,
  PlusOutlined,
  DeleteOutlined,
} from "@ant-design/icons";
import {
  EditorTool,
  MarginConfig,
  FontSizeConfig,
} from "components/editor/config";
import { useTranslation } from "react-i18next";
import ConfigChanKy from "./ConfigChanKy";
import { DragDropContext, Draggable, Droppable } from "react-beautiful-dnd";
import {
  LIST_PHIEU_DICH_TRUYEN,
  LIST_PHIEU_TONG_HOP_THU_THUAT_HUT_HOM,
  TABLE_TYPES,
  DEFAULT_DUNG_CU,
} from "./ultils";
import { SVG } from "assets";

const { FieldName } = EditorTool;
const { Panel } = Collapse;

const TableProperties = forwardRef((props, ref) => {
  const [state, _setState] = useState({
    fieldName: "",
    categoriesQty: 0,
    fields: "",
    type: "normal",
    rows: [],
    cols: [],
    defaultRows: [],
    disabled: false,
    focusObj: {},
    visible: false,
    maxRow: 2,
    filteredOptions: [],
    markSpanRow: false,
    textTenChanKy: "Chữ ký điều dưỡng",
    fieldNameKy: "",
    disableIfSigned: true,
    allowReset: true,
    textTenChanKy1: "Bác sĩ ba tra thuốc",
    fieldNameKy1: "",
    disableIfSigned1: true,
    allowReset1: true,
    anChuKy1: false,

    textTenChanKy2: "Kiểm tra kép",
    fieldNameKy2: "",
    disableIfSigned2: true,
    allowReset2: true,
    anChuKy2: false,

    textTenChanKy3: "Điều dưỡng thực hiện",
    fieldNameKy3: "",
    disableIfSigned3: true,
    allowReset3: true,
    anChuKy3: false,

    textTenChanKy4: "Người bệnh/ Người nhà",
    fieldNameKy4: "",
    textTenChanKy5: "Người thực hiện",
    fieldNameKy5: "",
    disableIfSigned4: true,
    allowReset4: true,
    anChuKy4: false,

    listSapXepPhieuDichTruyen: LIST_PHIEU_DICH_TRUYEN,
    listSapXepPhieuTongHopThuThuatHutDom: LIST_PHIEU_TONG_HOP_THU_THUAT_HUT_HOM,
    minRow: 1,
    viTriKyBatDau: 0,
    dungCuList: DEFAULT_DUNG_CU,
    editingDungCuIndex: -1,
    fieldName2: "",

    // replication row
    variable: [],
  });
  const { t } = useTranslation();
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  const { apiFields, handleSubmit, updateComponents } = props;
  const tableRef = useRef(null);

  useEffect(() => {
    if (props.state.key) {
      let newState = {
        ...props.state.props,
        rows: props.state.props.rows,
        categoriesQty: props.state.props.categoriesQty,
        fieldName: props.state.props.fieldName,
        dieuDuong: props.state.props.dieuDuong,
        numberDateInRow: props.state.props.numberDateInRow || 14,
        numberLineInFirstPage: props.state.props.numberLineInFirstPage || 20,
        numberLineInNextPage: props.state.props.numberLineInNextPage || 50,
        colDateWidth: props.state.props.colDateWidth || 40,
        fields: props.state.props.fields,
        type:
          props.state.props.type == "analytic"
            ? "vat-tu-tieu-hao"
            : props.state.props.type || "normal",
        disabled: props.state.props.disabled,
        defaultRows: props.state.props.defaultRows,
        cols: props.state.props.cols,
        rowHeight: props.state.props.rowHeight,
        lineHeightText: props.state.props.lineHeightText,
        fontSize: props.state.props.fontSize || 12,
        marginTop: props.state.props.marginTop,
        marginRight: props.state.props.marginRight,
        marginLeft: props.state.props.marginLeft,
        marginBottom: props.state.props.marginBottom,
        maxRow: props.state.props.maxRow || "",

        hideBorder: props.state.props.hideBorder || false,
        readOnly: props.state.props.readOnly || false,

        filteredOptions: props.state.props.cols || [],
        disableToRow: props.state.props.disableToRow || 0,
        listColDisable: props.state.props.listColDisable || [],
        hideBtnAddRow: props.state.props.hideBtnAddRow,

        isDeleteRow: props.state.props.isDeleteRow,
        showSetting: props.state.props.showSetting,
        sttBatDau: props.state.props.sttBatDau,
        isPhieuChamSoc: props.state.props.isPhieuChamSoc,
        hienCachDung:
          props.state.props.hienCachDung === undefined
            ? true
            : props.state.props.hienCachDung,
        hienThuocDichTruyen:
          props.state.props.hienThuocDichTruyen === undefined
            ? true
            : props.state.props.hienThuocDichTruyen,
        hienThiThucHienThuoc:
          props.state.props.hienThiThucHienThuoc === undefined
            ? true
            : props.state.props.hienThiThucHienThuoc,

        markSpanRow: props.state.props.markSpanRow,
        textTenChanKy: props.state.props.textTenChanKy || "Chữ ký điều dưỡng",
        capKy: props.state.props.capKy,
        fieldNameKy: props.state.props.fieldNameKy,
        disableIfSigned: props.state.props.disableIfSigned,
        allowReset: props.state.props.allowReset,
        showTenVietTat: props.state.props.showTenVietTat,
        textTenChanKy1:
          props.state.props.textTenChanKy1 || "Bác sĩ ba tra thuốc",
        capKy1: props.state.props.capKy1,
        fieldNameKy1: props.state.props.fieldNameKy1,
        disableIfSigned1: props.state.props.disableIfSigned1,
        allowReset1: props.state.props.allowReset1,
        anChuKy1: props.state.props.anChuKy1,

        showTenVietTat1: props.state.props.showTenVietTat1,
        textTenChanKy2: props.state.props.textTenChanKy2 || "Kiểm tra kép",
        capKy2: props.state.props.capKy2,
        fieldNameKy2: props.state.props.fieldNameKy2,
        disableIfSigned2: props.state.props.disableIfSigned2,
        allowReset2: props.state.props.allowReset2,
        anChuKy2: props.state.props.anChuKy2,

        showTenVietTat2: props.state.props.showTenVietTat2,
        textTenChanKy3:
          props.state.props.textTenChanKy3 || "Điều dưỡng thực hiện",
        capKy3: props.state.props.capKy3,
        fieldNameKy3: props.state.props.fieldNameKy3,
        disableIfSigned3: props.state.props.disableIfSigned3,
        allowReset3: props.state.props.allowReset3,
        anChuKy3: props.state.props.anChuKy3,

        showTenVietTat3: props.state.props.showTenVietTat3,
        textTenChanKy4:
          props.state.props.textTenChanKy4 || "Người bệnh/ Người nhà",
        capKy4: props.state.props.capKy4,
        fieldNameKy4: props.state.props.fieldNameKy4,
        disableIfSigned4: props.state.props.disableIfSigned4,
        allowReset4: props.state.props.allowReset4,
        anChuKy4: props.state.props.anChuKy4,

        showTenVietTat4: props.state.props.showTenVietTat4,
        listSapXepPhieuDichTruyen:
          props.state.props.listSapXepPhieuDichTruyen || LIST_PHIEU_DICH_TRUYEN,
        romanToInt: props.state.props.romanToInt,
        listSapXepPhieuTongHopThuThuatHutDom:
          props.state.props.listSapXepPhieuTongHopThuThuatHutDom ||
          LIST_PHIEU_TONG_HOP_THU_THUAT_HUT_HOM,
        minRow: props.state.props.minRow,
        viTriKyBatDau: props.state.props.viTriKyBatDau || 0,

        hideDuongTruyen: props.state.props.hideDuongTruyen,
        hienThiNgayCoChiDinh: props.state.props.hienThiNgayCoChiDinh,
        isShowEn: props.state.props.isShowEn,

        gopVoiDuLieu2: props.state.props.gopVoiDuLieu2,
        dungCuList: props.state.props.dungCuList || DEFAULT_DUNG_CU,

        //tien-su-di-ung
        hienThiTiengNga: props.state.props.hienThiTiengNga,
        hienThiTacNhanDiUng: props.state.props.hienThiTacNhanDiUng,

        //replication row
        hienThiDuLieuTuAPI: props.state.props.hienThiDuLieuTuAPI,
        dataApi: props.state.props.dataApi, //api danh sách
        variable: props.state.props.variable || [], //các biến tham số cho api

        // tom-tat-benh-an
        allowSetService: props.state.props.allowSetService,
        hienThiDangText: props.state.props.hienThiDangText,
        showAllData: props.state.props.showAllData,
        hideHeader: props.state.props.hideHeader || false,
        dauNganCach: props.state.props.dauNganCach || "",
        hienThiKetLuan:
          props.state.props.hienThiKetLuan === undefined
            ? true
            : props.state.props.hienThiKetLuan,
        hienThiKetQua:
          props.state.props.hienThiKetQua === undefined
            ? true
            : props.state.props.hienThiKetQua,
        hienThiThoiGian:
          props.state.props.hienThiThoiGian === undefined
            ? true
            : props.state.props.hienThiThoiGian,

        disableThuocGoc: props.state.props.disableThuocGoc,
        showThangDiemMorse: props.state.props.showThangDiemMorse,
        fieldName2: props.state.props.fieldName2 || "",

        copyDuLieuHangTrenKhiThemMoi:
          props.state.props.copyDuLieuHangTrenKhiThemMoi,

        //phieu-theo-doi-nb-sau-mo
        isFormThuDuc: props.state.props.isFormThuDuc,

        // phieu-truyen-dich
        macDinhGhiChuLaCachDung: props.state.props.macDinhGhiChuLaCachDung,
        khongXuLyGopThuoc: props.state.props.khongXuLyGopThuoc,
        showHamLuong:
          props.state.props.showHamLuong === undefined
            ? true
            : props.state.props.showHamLuong,
        showHoatChat:
          props.state.props.showHoatChat === undefined
            ? true
            : props.state.props.showHoatChat,
        khoaBieuMauSauKy: props.state.props.khoaBieuMauSauKy,
        hienThiThuocKeNgoai: props.state.props.hienThiThuocKeNgoai,
        hienThiIdThuoc: props.state.props.hienThiIdThuoc,

        //thuc-hien-va-cong-khai-thuoc
        kyNhieuChanKyDieuDuong:
          props.state.props.kyNhieuChanKyDieuDuong || false,
        hienThiTenDieuDuong: props.state.props.hienThiTenDieuDuong || false,
        hienThiDuLieuMoiLenDau: props.state.props.hienThiDuLieuMoiLenDau,
        hienThiThuocToDieuTri: props.state.props.hienThiThuocToDieuTri || false
      };

      if (props.state.props.gridData) {
        newState.type = "gridData";
      }

      setState(newState);
    }
  }, [props.state]);

  const signFields = useMemo(() => {
    if (apiFields) {
      return apiFields.filter((item) => apiFields.includes(item + "_ngayKy"));
    }
    return [];
  }, [apiFields]);

  useImperativeHandle(ref, () => {
    return {
      ...state,
      marginTop: state.marginTop,
      marginRight: state.marginRight,
      marginLeft: state.marginLeft,
      marginBottom: state.marginBottom,
      fields: state.fields,
      fieldName: state.fieldName,
      dieuDuong: state.dieuDuong,
      numberDateInRow: state.numberDateInRow,
      numberLineInFirstPage: state.numberLineInFirstPage,
      numberLineInNextPage: state.numberLineInNextPage,
      colDateWidth: state.colDateWidth,

      categoriesQty: state.categoriesQty,
      rows: state.rows,
      defaultRows: state.defaultRows,
      disabled: state.disabled,
      cols: state.cols,
      type: state.type,
      rowHeight: state.rowHeight,
      lineHeightText: state.lineHeightText,
      fontSize: state.fontSize,
      maxRow: state.maxRow || "",

      hideBorder: state.hideBorder || false,
      readOnly: state.readOnly || false,

      listColDisable: state.listColDisable,
      disableToRow: state.disableToRow,
      hideBtnAddRow: state.hideBtnAddRow,

      isDeleteRow: state.isDeleteRow,
      showSetting: state.showSetting,
      sttBatDau: state.sttBatDau,
      isPhieuChamSoc: state.isPhieuChamSoc,
      hienCachDung: state.hienCachDung,
      hienThuocDichTruyen: state.hienThuocDichTruyen,
      hienThiThucHienThuoc: state.hienThiThucHienThuoc,
      hienThiMoTaDvkt: state.hienThiMoTaDvkt,

      markSpanRow: state.markSpanRow,
      textTenChanKy: state.textTenChanKy,
      capKy: state.capKy,
      fieldNameKy: state.fieldNameKy,
      disableIfSigned: state.disableIfSigned,
      allowReset: state.allowReset,
      showTenVietTat: state.showTenVietTat,
      gopVoiDuLieu2: state.gopVoiDuLieu2,

      textTenChanKy1: state.textTenChanKy1,
      capKy1: state.capKy1,
      fieldNameKy1: state.fieldNameKy1,
      disableIfSigned1: state.disableIfSigned1,
      allowReset1: state.allowReset1,
      anChuKy1: state.anChuKy1,

      showTenVietTat1: state.showTenVietTat1,
      textTenChanKy2: state.textTenChanKy2,
      capKy2: state.capKy2,
      fieldNameKy2: state.fieldNameKy2,
      disableIfSigned2: state.disableIfSigned2,
      allowReset2: state.allowReset2,
      anChuKy2: state.anChuKy2,

      showTenVietTat2: state.showTenVietTat2,
      textTenChanKy3: state.textTenChanKy3,
      capKy3: state.capKy3,
      fieldNameKy3: state.fieldNameKy3,
      disableIfSigned3: state.disableIfSigned3,
      allowReset3: state.allowReset3,
      anChuKy3: state.anChuKy3,

      showTenVietTat3: state.showTenVietTat3,
      textTenChanKy4: state.textTenChanKy4,
      capKy4: state.capKy4,
      fieldNameKy4: state.fieldNameKy4,
      disableIfSigned4: state.disableIfSigned4,
      allowReset4: state.allowReset4,
      anChuKy4: state.anChuKy4,

      showTenVietTat4: state.showTenVietTat4,
      listSapXepPhieuDichTruyen: state.listSapXepPhieuDichTruyen,
      romanToInt: state.romanToInt,
      listSapXepPhieuTongHopThuThuatHutDom:
        state.listSapXepPhieuTongHopThuThuatHutDom,
      minRow: state.minRow,
      viTriKyBatDau: state.viTriKyBatDau,

      hideDuongTruyen: state.hideDuongTruyen,
      hienThiNgayCoChiDinh: state.hienThiNgayCoChiDinh,
      isShowEn: state.isShowEn,
      dungCuList: state.dungCuList,

      //tien-su-di-ung
      hienThiTiengNga: state.hienThiTiengNga,
      hienThiTacNhanDiUng: state.hienThiTacNhanDiUng,

      //replication row
      hienThiDuLieuTuAPI: state.hienThiDuLieuTuAPI,
      dataApi: state.dataApi,
      variable: state.variable,

      // tom-tat-benh-an
      allowSetService: state.allowSetService,
      hienThiDangText: state.hienThiDangText,
      showAllData: state.showAllData,
      hideHeader: state.hideHeader || false,
      dauNganCach: state.dauNganCach || "",
      hienThiKetQua: state.hienThiKetQua,
      hienThiKetLuan: state.hienThiKetLuan,
      hienThiThoiGian: state.hienThiThoiGian,

      disableThuocGoc: state.disableThuocGoc,
      showThangDiemMorse: state.showThangDiemMorse,
      fieldName2: state.fieldName2 || "",

      copyDuLieuHangTrenKhiThemMoi: state.copyDuLieuHangTrenKhiThemMoi,

      //phieu-theo-doi-nb-sau-mo
      isFormThuDuc: state.isFormThuDuc,

      //phieu-truyen-dich
      macDinhGhiChuLaCachDung: state.macDinhGhiChuLaCachDung,
      khongXuLyGopThuoc: state.khongXuLyGopThuoc,
      showHamLuong: state.showHamLuong,
      showHoatChat: state.showHoatChat,
      khoaBieuMauSauKy: state.khoaBieuMauSauKy,
      hienThiThuocKeNgoai: state.hienThiThuocKeNgoai,
      hienThiIdThuoc: state.hienThiIdThuoc,

      //thuc-hien-va-cong-khai-thuoc
      kyNhieuChanKyDieuDuong: state.kyNhieuChanKyDieuDuong || false,
      hienThiTenDieuDuong: state.hienThiTenDieuDuong || false,
      hienThiDuLieuMoiLenDau: state.hienThiDuLieuMoiLenDau,
      hienThiThuocToDieuTri: state.hienThiThuocToDieuTri || false
    };
  });

  const changeDataFormEMR = (e) => {
    onChangeValue("disabled")(!e.target.checked);
  };

  const onChangeValueCheckbox = (key) => (e) => {
    onChangeValue(key)(e.target.checked);
  };

  const showDrawer = (open) => () => {
    setState({
      visible: open,
    });
  };

  const handleChangeColKey = (key, isStt) => (e) => {
    const checked = e.target?.checked;
    const { col } = tableRef.current;
    const currentCols = props.state.props.cols || [];
    const currentCol = currentCols.find((item) => item.key === col.key) || {};
    const newCols = currentCols.map((item) =>
      item.key === currentCol.key ? currentCol : item
    );

    currentCol[key] = isStt ? checked : e.target.value;
    setState({
      cols: newCols,
    });
  };

  const handleChangeRowField = (rowField) => (e) => {
    let value = "";
    if (["rowKey", "className"].includes(rowField)) {
      value = e.target?.value;
    } else {
      value = e;
    }
    const { row } = tableRef.current;
    const currentRows = props.state.props.rows || [];
    const currentRow = currentRows.find((item) => item.key === row.key) || {};
    const newRows = currentRows.map((item) =>
      item.key === currentRow.key ? { ...currentRow, [rowField]: value } : item
    );
    setState({
      rows: newRows,
      focusObj: {
        ...state.focusObj,
        row: { ...currentRow, [rowField]: value },
      },
    });
  };

  const handleChangeColComponent = (value) => {
    const { col } = tableRef.current;
    const currentCols = props.state.props.cols || [];
    const currentCol = currentCols.find((item) => item.key === col.key) || {};
    currentCol.component = value;
    const newCols = currentCols.map((item) =>
      item.key === currentCol.key ? currentCol : item
    );
    setState({
      cols: newCols,
      currentCol,
    });
  };

  const handleChangeFormat = (value) => {
    const { col } = tableRef.current;
    const currentCols = props.state.props.cols || [];
    const currentCol = currentCols.find((item) => item.key === col.key) || {};
    currentCol.dateTimeFormat = value;
    const newCols = currentCols.map((item) =>
      item.key === currentCol.key ? currentCol : item
    );
    setState({
      cols: newCols,
    });
  };

  const submit = () => {
    handleSubmit();
  };

  const handleChangeColFixed = (e) => {
    const checked = e.target.checked;
    const { col } = tableRef.current;
    const currentCols = props.state.props.cols || [];
    const currentCol = currentCols.find((item) => item.key === col.key) || {};
    const newCols = currentCols.map((item) =>
      item.key === currentCol.key ? { ...currentCol, fixed: checked } : item
    );
    setState({
      cols: newCols,
      focusObj: { ...state.focusObj, col: { ...currentCol, fixed: checked } },
    });
  };

  const handleChangeColPlusBtn = (e) => {
    const checked = e.target.checked;
    const { col } = tableRef.current;
    const currentCols = props.state.props.cols || [];
    const currentCol = currentCols.find((item) => item.key === col.key) || {};
    const newCols = currentCols.map((item) =>
      item.key === currentCol.key ? { ...currentCol, plusBtn: checked } : item
    );
    setState({
      cols: newCols,
      focusObj: { ...state.focusObj, col: { ...currentCol, plusBtn: checked } },
    });
  };

  const handleChangeRowCopied = (e) => {
    const checked = e.target.checked;
    const { row } = tableRef.current;
    const currentRows = props.state.props.rows || [];
    const currentRow = currentRows.find((item) => item.key === row.key) || {};
    const newRows = currentRows.map((item) =>
      item.key === currentRow.key ? { ...currentRow, copied: checked } : item
    );
    setState({
      rows: newRows,
      focusObj: { ...state.focusObj, row: { ...currentRow, copied: checked } },
    });
  };

  const handleChangeRowFixed = (e) => {
    const checked = e.target.checked;
    const { row } = tableRef.current;
    const currentRows = props.state.props.rows || [];
    const currentRow = currentRows.find((item) => item.key === row.key) || {};
    const newRows = currentRows.map((item) =>
      item.key === currentRow.key ? { ...currentRow, fixed: checked } : item
    );
    setState({
      rows: newRows,
      focusObj: { ...state.focusObj, row: { ...currentRow, fixed: checked } },
    });
  };

  const handleChangeRowHeader = (e) => {
    const checked = e.target.checked;
    const { row } = tableRef.current;
    const currentRows = props.state.props.rows || [];
    const currentRow = currentRows.find((item) => item.key === row.key) || {};
    const newRows = currentRows.map((item) =>
      item.key === currentRow.key ? { ...currentRow, isHeader: checked } : item
    );
    setState({
      rows: newRows,
      focusObj: {
        ...state.focusObj,
        row: { ...currentRow, isHeader: checked },
      },
    });
  };

  const handleChangeCategoriesQty = (value) => {
    setState({
      categoriesQty: value,
    });
  };

  const getFieldName = () => {
    if (state.type == "tien-su-di-ung") {
      let list = ["dong"]; //với bảng dạng tiền sử dị ứng thì các trường sẽ nằm từ
      /* dong1:, dong2: dong3: dong4: dong5, dong6 
      hiển thị chung là dong để hiển thị dữ liệu lên bảng
      */
      return list;
    }
    return apiFields;
  };

  const onChangeInput = (type) => (e) => {
    if (type == "maxRow") {
      if (/^\d+$/.test(e.target.value) || e.target.value == "") {
        setState({
          [type]: e.target.value,
        });
      }
    } else {
      let newState = {
        [type]: e.target.value,
      };
      if (type == "type" && e.target.value == "tien-su-di-ung") {
        newState.cols = [{ key: 1, width: 100 }];
      }
      if (type == "type" && e.target.value == "vat-tu-tieu-hao") {
        newState.disabled = true;
      }
      setState(newState);
    }
  };

  const onChangeValue = (type) => (value) => {
    if (type == "focusObj") {
      setState({
        [type]: value,
        currentCol: value?.col,
      });
    } else if (type === "listColDisable") {
      const filteredOptions = state.filteredOptions.filter(
        (item) => item.key != value
      );
      setState({
        [type]: value,
        filteredOptions,
      });
    } else {
      setState({
        [type]: value,
      });
    }
  };

  const onChangeCheckbox = (type) => (e) => {
    onChangeValue(type)(e.target.checked);
  };

  const onChangeMargin = (type, value) => {
    let key = null;
    switch (type) {
      case "top":
        key = "marginTop";
        break;
      case "bottom":
        key = "marginBottom";
        break;
      case "left":
        key = "marginLeft";
        break;
      case "right":
        key = "marginRight";
        break;
    }
    setState({
      [key]: value,
    });
  };

  const reorder = (list, startIndex, endIndex) => {
    const result = Array.from(list);
    const [removed] = result.splice(startIndex, 1);
    result.splice(endIndex, 0, removed);

    return result;
  };

  const onDragEnd = (key) => (result) => {
    try {
      if (!result.destination) {
        return;
      }
      const items = reorder(
        state[key],
        result.source.index,
        result.destination.index
      );
      setState({
        [key]: items,
      });
    } catch (error) {
      console.log(error);
    }
  };

  const onChangeSapXepPhieuDichTruyen = (key) => (e) => {
    const item = state.listSapXepPhieuDichTruyen.find(
      (item) => item.fieldName === key
    );
    item.show = e.target.checked;
    setState({
      listSapXepPhieuDichTruyen: state.listSapXepPhieuDichTruyen,
    });
  };

  const onChangeSapXepPhieuTongHopThuThuatHutDom = (key) => (e) => {
    const item = state.listSapXepPhieuTongHopThuThuatHutDom.find(
      (item) => item.fieldName === key
    );
    item.show = e.target.checked;
    setState({
      listSapXepPhieuTongHopThuThuatHutDom:
        state.listSapXepPhieuTongHopThuThuatHutDom,
    });
  };

  const onSetDefault = () => {
    setState({
      listSapXepPhieuDichTruyen: LIST_PHIEU_DICH_TRUYEN,
    });
  };

  const startEditing = (index) => {
    setState({ editingDungCuIndex: index });
  };

  const handleChangeDungCu = (index, e) => {
    const newDungCuList = [...state.dungCuList];
    newDungCuList[index] = e.target.value;
    setState({ dungCuList: newDungCuList });
  };

  const resetDungCuList = () => {
    setState({ dungCuList: DEFAULT_DUNG_CU });
  };

  const updateVariable = (item, key) => (e) => {
    let value = "";
    if (e?.target?.hasOwnProperty("checked")) value = e.target.checked;
    else if (e?.target?.hasOwnProperty("value")) value = e.target.value;
    else value = e;
    const newList = state.variable.map((obj) =>
      obj.key === item.key
        ? {
            ...obj,
            [key]: value,
          }
        : obj
    );
    setState({
      variable: newList,
    });
    updateComponents({
      ...props.state,
      props: {
        ...props.state.props,
        checkList: newList,
      },
    });
  };

  const removeVariable = (itemKey) => () => {
    const newList = state.variable.filter((item) => item.key !== itemKey);
    setState({
      variable: newList,
    });
    updateComponents({
      ...props.state,
      props: {
        ...props.state.props,
        variable: newList,
      },
    });
  };

  const addVariable = () => {
    const newList = state.variable;
    const item = {
      name: "",
      value: null,
      fromUrl: true,
      key: moment().valueOf(),
    };
    const list = [...newList, item];
    setState({
      variable: list,
    });
    updateComponents({
      ...props.state,
      props: {
        ...props.state.props,
        variable: list,
      },
    });
  };

  const renderCheckbox = (item) => {
    return (
      <>
        <Col span={16}>{t(item.i18n)}</Col>
        <Col span={8}>
          <Checkbox
            onChange={onChangeValueCheckbox(item.key)}
            checked={state[item.key]}
          />
        </Col>
      </>
    );
  };

  return (
    <Main>
      <Row gutter={[12, 12]}>
        <Col span={24}>
          <div>{"Type: "}</div>
          <Radio.Group
            value={state.type}
            onChange={onChangeInput("type")}
            className="radio-type"
            style={{ marginLeft: 10 }}
          >
            {TABLE_TYPES.map((item) => (
              <Radio key={item.value} value={item.value}>
                {item.label}
              </Radio>
            ))}
          </Radio.Group>
        </Col>

        <Col span={8}>{"Field name: "}</Col>
        <Col span={16}>
          <FieldName
            style={{ width: "100%" }}
            onSelect={onChangeValue("fieldName")}
            value={state.fieldName}
            apiFields={getFieldName() || []}
          />
        </Col>
        <Col span={8}>
          <span>{"Field name 2: "}</span>
        </Col>
        <Col span={16}>
          <FieldName
            style={{ width: "100%" }}
            onSelect={onChangeValue("fieldName2")}
            value={state.fieldName2}
            apiFields={apiFields}
          />
        </Col>
        <Col span={24}>
          <i>FieldName2 dùng để set giá trị khi fieldName bị null</i>
        </Col>
      </Row>
      {[
        "cong-khai-dich-vu-kham",
        "phieu-truyen-dich",
        "phieu-tong-hop-thu-thuat-hut-dom",
        "thuc-hien-va-cong-khai-vtyt",
        "theo-doi-mang-bung",
        "phieu-cham-soc-nguoi-benh-sau-thu-thuat-pha-thai",
      ].includes(state.type) && (
        <>
          <div>{"Cài đặt chữ ký: "}</div>
          <Collapse style={{ marginTop: 10, marginBottom: 10 }}>
            <Panel header={state[`textTenChanKy`]}>
              <ConfigChanKy
                onChangeCheckbox={onChangeCheckbox}
                onChangeInput={onChangeInput}
                onChangeValue={onChangeValue}
                state={state}
                getFieldName={getFieldName}
              />
            </Panel>
          </Collapse>

          {[
            "cong-khai-dich-vu-kham",
            "thuc-hien-va-cong-khai-vtyt",
            "theo-doi-mang-bung",
          ].includes(state.type) && (
            <>
              <Collapse style={{ marginTop: 10, marginBottom: 10 }}>
                <Panel header={state[`textTenChanKy4`]}>
                  <ConfigChanKy
                    onChangeCheckbox={onChangeCheckbox}
                    onChangeInput={onChangeInput}
                    onChangeValue={onChangeValue}
                    state={state}
                    getFieldName={getFieldName}
                    index={4}
                  />
                </Panel>
              </Collapse>
            </>
          )}
        </>
      )}

      {[
        "thuc-hien-va-cong-khai-thuoc",
        "thuc-hien-va-cong-khai-thuoc-dktd",
      ].includes(state.type) && (
        <>
          <div>{"Cài đặt chữ ký: "}</div>
          <Collapse style={{ marginTop: 10, marginBottom: 10 }}>
            {Array.from(Array(4).keys()).map((item) => (
              <Panel header={state[`textTenChanKy${item + 1}`]} key={item}>
                <ConfigChanKy
                  onChangeCheckbox={onChangeCheckbox}
                  onChangeInput={onChangeInput}
                  onChangeValue={onChangeValue}
                  state={state}
                  getFieldName={getFieldName}
                  index={item + 1}
                />
              </Panel>
            ))}
          </Collapse>
        </>
      )}
      {["cong-khai-dich-vu-kham"].includes(state.type) && (
        <>
          <Row gutter={[12, 12]} style={{ marginBottom: 10 }}>
            {renderCheckbox({
              key: "hienThiNgayCoChiDinh",
              i18n: "editor.hienThiNgayCoChiDinh",
            })}
          </Row>
        </>
      )}
      {["phieu-theo-doi-nb-sau-mo"].includes(state.type) && (
        <>
          <Row gutter={[12, 12]} style={{ marginBottom: 10 }}>
            {renderCheckbox({
              key: "hideDuongTruyen",
              i18n: "editor.anDuongTruyenHienThangDoDanhGiaMucDoViemTinhMach",
            })}
            {renderCheckbox({
              key: "showThangDiemMorse",
              i18n: "editor.hienThiThangDiemMorse",
            })}
            {renderCheckbox({
              key: "isFormThuDuc",
              i18n: "editor.formDanhChoBVDKKVThuDuc",
            })}
          </Row>
        </>
      )}
      {state.type === "vat-tu-tieu-hao" && (
        <>
          <Row gutter={[12, 12]} style={{ marginBottom: 10 }}>
            <Col span={8}>
              <span>{t("editor.dataĐieuDuong")}:</span>
            </Col>
            <Col span={16}>
              <FieldName
                style={{ width: "100%" }}
                onSelect={onChangeValue("dieuDuong")}
                value={state.dieuDuong}
                apiFields={apiFields}
              />
            </Col>
          </Row>
          <Row gutter={[12, 12]} style={{ marginBottom: 10 }}>
            <Col span={8}>
              <span>{t("editor.soNgayTrenHang")}:</span>
            </Col>
            <Col span={16}>
              <InputNumber
                type="number"
                size={"small"}
                min={1}
                value={state.numberDateInRow}
                onChange={onChangeValue("numberDateInRow")}
              />
            </Col>
          </Row>
          <Row gutter={[12, 12]} style={{ marginBottom: 10 }}>
            <Col span={8}>
              <span>{t("editor.soDongTrangDau")}:</span>
            </Col>
            <Col span={16}>
              <InputNumber
                type="number"
                size={"small"}
                min={1}
                value={state.numberLineInFirstPage}
                onChange={onChangeValue("numberLineInFirstPage")}
              />
            </Col>
          </Row>
          <Row gutter={[12, 12]} style={{ marginBottom: 10 }}>
            <Col span={8}>
              <span>{t("editor.soDongTrangTiepTheo")}</span>
            </Col>
            <Col span={16}>
              <InputNumber
                type="number"
                size={"small"}
                min={1}
                value={state.numberLineInNextPage}
                onChange={onChangeValue("numberLineInNextPage")}
              />
            </Col>
          </Row>
          <Row gutter={[12, 12]} style={{ marginBottom: 10 }}>
            <Col span={8}>
              <span>{t("editor.doRongCuaCotNgay")}:</span>
            </Col>
            <Col span={16}>
              <InputNumber
                type="number"
                size={"small"}
                min={1}
                value={state.colDateWidth}
                onChange={onChangeValue("colDateWidth")}
                addonAfter="px"
              />
            </Col>
          </Row>
        </>
      )}
      {[
        "thuc-hien-va-cong-khai-thuoc",
        "thuc-hien-va-cong-khai-thuoc-dktd",
      ].includes(state.type) && (
        <>
          <Row gutter={[12, 12]} style={{ marginBottom: 10 }}>
            {renderCheckbox({
              key: "hienCachDung",
              i18n: "editor.hienCachDung",
            })}
            {renderCheckbox({
              key: "hienThuocDichTruyen",
              i18n: "editor.hienThuocDichTruyen",
            })}
            {renderCheckbox({
              key: "hienThiThucHienThuoc",
              i18n: "editor.hienThiThucHienThuoc",
            })}
          </Row>
        </>
      )}
      <Row gutter={[12, 12]} style={{ marginBottom: 10 }}>
        {state.type !== "vat-tu-tieu-hao" &&
          state.type !== "tien-su-di-ung" &&
          state.type !== "replicationRow" &&
          state.type !== "ban-giao-thuoc" && (
            <>
              <Col span={16}>{t("editor.hangMacDinh")}</Col>
              <Col span={8}>
                <InputNumber
                  value={state.defaultRows}
                  onChange={onChangeValue("defaultRows")}
                  size={"small"}
                  min={1}
                />
              </Col>
            </>
          )}
        <>
          <Col span={16}>
            <span>{t("editor.duLieuTuEMR")}:</span>
          </Col>
          <Col span={8}>
            <Checkbox onChange={changeDataFormEMR} checked={!state.disabled} />
          </Col>
        </>
        {state.type === "tien-su-di-ung" && (
          <>
            {renderCheckbox({
              key: "hienThiTiengNga",
              i18n: "editor.hienThiTiengNga",
            })}
            {renderCheckbox({
              key: "hienThiTacNhanDiUng",
              i18n: "editor.hienThiTacNhanDiUng",
            })}
            {renderCheckbox({
              key: "isFormThuDuc",
              i18n: "editor.formDanhChoBVDKKVThuDuc",
            })}
          </>
        )}
        <Col span={12}>
          <span>{t("editor.khoangCachNgoai")}:</span>
        </Col>

        <Col span={12}>
          <MarginConfig
            onChange={onChangeMargin}
            top={state.marginTop}
            bottom={state.marginBottom}
            right={state.marginRight}
            left={state.marginLeft}
          />
        </Col>
        {renderCheckbox({
          key: "markSpanRow",
          i18n: "editor.hienThiDanhDauDong",
        })}
        <Col span={12}>
          <span>{t("editor.doCaoDong")}:</span>
        </Col>
        <Col span={12}>
          <InputNumber
            type="number"
            size={"small"}
            min={1}
            value={state.rowHeight}
            onChange={onChangeValue("rowHeight")}
            placeholder={24}
          />
        </Col>
        <Col span={12}>
          <span>{t("editor.coChu")}:</span>
        </Col>
        <Col span={12}>
          <FontSizeConfig
            changeFont={onChangeValue("fontSize")}
            fontSize={state.fontSize}
          />
        </Col>
      </Row>
      <Row gutter={[12, 12]}>
        <Col span={12}>
          <span>{t("editor.doCaoDongVanBan")}:</span>
        </Col>
        <Col span={12}>
          <InputNumber
            type="number"
            size={"small"}
            min={1}
            step={0.1}
            value={state.lineHeightText}
            placeholder={1.5}
            onChange={onChangeValue("lineHeightText")}
          />
        </Col>
      </Row>

      {state.type == "replicationRow" && (
        <Row gutter={[12, 12]} style={{ marginTop: 5 }}>
          <Col span={16}>
            <span>{t("editor.viTriKyBatDau")}:</span>
          </Col>
          <Col span={8}>
            <Input
              size={"small"}
              min={0}
              type="number"
              value={state.viTriKyBatDau}
              onChange={onChangeInput("viTriKyBatDau")}
            />
          </Col>
          <Col span={16}>
            <span>{t("editor.soLuongHangToiDa")}:</span>
          </Col>
          <Col span={8}>
            <Input
              size={"small"}
              min={2}
              type="number"
              value={state.maxRow}
              onChange={onChangeInput("maxRow")}
            />
          </Col>
          <Col span={16}>
            <span>{t("editor.soDongMacDinh")}:</span>
          </Col>
          <Col span={8}>
            <Input
              size={"small"}
              min={2}
              type="number"
              value={state.minRow}
              onChange={onChangeInput("minRow")}
            />
          </Col>
          {renderCheckbox({
            key: "hienThiDuLieuMoiLenDau",
            i18n: "editor.hienThiDuLieuMoiLenDau",
          })}
          {renderCheckbox({
            key: "gopVoiDuLieu2",
            i18n: "editor.gopDuLieuVoiTruongDuLieu2",
          })}
          {renderCheckbox({
            key: "copyDuLieuHangTrenKhiThemMoi",
            i18n: "editor.copyDuLieuHangTrenKhiThemMoi",
          })}
          {renderCheckbox({
            key: "hideBtnAddRow",
            i18n: "editor.anNutThemMoiDong",
          })}
          {renderCheckbox({
            key: "showSetting",
            i18n: "editor.hienCaiDat",
          })}
          {renderCheckbox({
            key: "isDeleteRow",
            i18n: "editor.choPhepXoaDong",
          })}
          {renderCheckbox({
            key: "isPhieuChamSoc",
            i18n: "editor.phieuChamSoc",
          })}

          {/* Hiển thị dữ liệu từ API */}
          {renderCheckbox({
            key: "hienThiDuLieuTuAPI",
            i18n: "editor.hienThiDuLieuTuAPI",
          })}
          {state.hienThiDuLieuTuAPI && (
            <>
              <Col span={8}>
                <span>{"API"}:</span>
              </Col>
              <Col span={16}>
                <Input
                  size="small"
                  value={state.dataApi}
                  onChange={onChangeInput("dataApi")}
                />
              </Col>
            </>
          )}
          <div className="khai-bao-tham-so">
            <fieldset>
              <legend>
                <span>{t("editor.khaiBaoThamSo")}</span>
              </legend>
              <ul>
                {state.variable.map((item) => (
                  <li key={item.key} className={"item-main"}>
                    <div className={"item-option"}>
                      <span className="option-label">
                        {t("editor.thamSo")}:
                      </span>
                      <Input
                        placeholder={t("editor.nhapTenThamSo")}
                        className="option-content"
                        style={{ flex: 1 }}
                        value={item.name}
                        onChange={updateVariable(item, "name")}
                        size={"small"}
                      />
                    </div>
                    <div className={"item-option"}>
                      <span className="option-label">
                        {t("editor.giaTri")}:
                      </span>
                      <FieldName
                        apiFields={apiFields}
                        value={item.value}
                        placeholder={t("editor.chonTruongDuLieu")}
                        onSelect={updateVariable(item, "value")}
                      />
                      <Button
                        icon={<DeleteOutlined />}
                        size={"small"}
                        onClick={removeVariable(item.key)}
                      />
                    </div>
                    <div className={"item-option"}>
                      <span className="option-label">{t("editor.tuUrl")}</span>
                      <Checkbox
                        onChange={updateVariable(item, "fromUrl")}
                        checked={item.fromUrl}
                      />
                    </div>
                  </li>
                ))}
              </ul>
              <Button
                className={"add-btn"}
                icon={<PlusOutlined />}
                size={"small"}
                onClick={addVariable}
              />
            </fieldset>
          </div>
        </Row>
      )}

      {state.type == "tom-tat-benh-an" && (
        <>
          <Row gutter={[12, 12]}>
            {renderCheckbox({
              key: "allowSetService",
              i18n: "editor.choPhepChonDichVuHienThi",
            })}
            {state.allowSetService &&
              renderCheckbox({
                key: "hienThiDangText",
                i18n: "editor.hienThiDangText",
              })}
            {renderCheckbox({
              key: "dauNganCach",
              i18n: "editor.dauNganCach",
            })}
            {renderCheckbox({
              key: "readOnly",
              i18n: "editor.chiDoc",
            })}
            {renderCheckbox({
              key: "hideHeader",
              i18n: "editor.hienTieuDe",
            })}
            {renderCheckbox({
              key: "showAllData",
              i18n: "editor.hienTatCaDuLieu",
            })}
            {renderCheckbox({
              key: "hienThiKetLuan",
              i18n: "editor.hienThiKetLuan",
            })}
            {renderCheckbox({
              key: "hienThiKetQua",
              i18n: "editor.hienThiKetQua",
            })}
            {renderCheckbox({
              key: "hienThiThoiGian",
              i18n: "editor.hienThiThoiGian",
            })}
          </Row>
        </>
      )}
      {"phieu-truyen-dich" === state.type && (
        <Row gutter={[12, 12]}>
          {renderCheckbox({
            key: "disableThuocGoc",
            i18n: "editor.chanXoaSuaThoiGianTenThuocGoc",
          })}
          {renderCheckbox({
            key: "isShowEn",
            i18n: "editor.hienThiTiengAnh",
          })}
          {renderCheckbox({
            key: "romanToInt",
            i18n: "editor.tocDoSoLaMa",
          })}
          {renderCheckbox({
            key: "macDinhGhiChuLaCachDung",
            i18n: "editor.macDinhGhiChuLaCachDung",
          })}
          {renderCheckbox({
            key: "khongXuLyGopThuoc",
            i18n: "editor.khongXuLyGopThuoc",
          })}
          {renderCheckbox({
            key: "khoaBieuMauSauKy",
            i18n: "editor.khoaBieuMauSauKy",
          })}
          {renderCheckbox({
            key: "hienThiThuocKeNgoai",
            i18n: "editor.hienThiThuocKeNgoai",
          })}
          {renderCheckbox({
            key: "hienThiIdThuoc",
            i18n: "editor.hienThiIdThuoc",
          })}

          <Col span={24}>
            <Button size="small" type="primary" onClick={onSetDefault}>
              {t("editor.sapXepMacDinh")}
            </Button>
          </Col>
          <Col span={24}>
            <DragDropContext onDragEnd={onDragEnd("listSapXepPhieuDichTruyen")}>
              <Droppable droppableId="droppable">
                {(provided, snapshot) => (
                  <div {...provided.droppableProps} ref={provided.innerRef}>
                    {(state.listSapXepPhieuDichTruyen || []).map(
                      (item, index) => (
                        <Draggable
                          key={item.fieldName}
                          draggableId={item.fieldName}
                          index={index}
                        >
                          {(provided, snapshot) => (
                            <div
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              {...provided.dragHandleProps}
                              className="item"
                            >
                              <Checkbox
                                checked={item.show}
                                onChange={onChangeSapXepPhieuDichTruyen(
                                  item.fieldName
                                )}
                              >
                                {t(`editor.${item.fieldName}`)}
                              </Checkbox>
                              <SVG.IcDrag className="ic-drag" />
                            </div>
                          )}
                        </Draggable>
                      )
                    )}
                    {provided.placeholder}
                  </div>
                )}
              </Droppable>
            </DragDropContext>
          </Col>
        </Row>
      )}
      {"phieu-tong-hop-thu-thuat-hut-dom" === state.type && (
        <Col span={24}>
          <DragDropContext
            onDragEnd={onDragEnd("listSapXepPhieuTongHopThuThuatHutDom")}
          >
            <Droppable droppableId="droppable">
              {(provided, snapshot) => (
                <div {...provided.droppableProps} ref={provided.innerRef}>
                  {(state.listSapXepPhieuTongHopThuThuatHutDom || []).map(
                    (item, index) => (
                      <Draggable
                        key={item.fieldName}
                        draggableId={item.fieldName}
                        index={index}
                      >
                        {(provided, snapshot) => (
                          <div
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            {...provided.dragHandleProps}
                            className="item"
                          >
                            <Checkbox
                              checked={item.show}
                              onChange={onChangeSapXepPhieuTongHopThuThuatHutDom(
                                item.fieldName
                              )}
                            >
                              {t(`editor.${item.fieldName}`)}
                            </Checkbox>
                            <SVG.IcDrag className="ic-drag" />
                          </div>
                        )}
                      </Draggable>
                    )
                  )}
                  {provided.placeholder}
                </div>
              )}
            </Droppable>
          </DragDropContext>
        </Col>
      )}
      {["phieu-truyen-dich", "thuc-hien-va-cong-khai-thuoc"].includes(
        state.type
      ) && (
        <Row gutter={[12, 12]} style={{ marginTop: 5 }}>
          {renderCheckbox({
            key: "showHamLuong",
            i18n: "editor.hienThiHamLuong",
          })}
          {renderCheckbox({
            key: "showHoatChat",
            i18n: "editor.hienThiHoatChat",
          })}
        </Row>
      )}
      {state.type === "kiem-diem-dem-dung-cu" && (
        <div>
          <Collapse style={{ marginTop: 10, marginBottom: 10 }}>
            <Panel header="Thiết lập danh sách dụng cụ">
              <Row gutter={[12, 12]}>
                <Col span={24}>
                  <Button
                    size="small"
                    onClick={resetDungCuList}
                    style={{ marginBottom: 12 }}
                  >
                    Khôi phục mặc định
                  </Button>
                </Col>
                <Col span={24}>
                  <List
                    bordered
                    dataSource={state.dungCuList}
                    renderItem={(item, index) => (
                      <List.Item>
                        {state.editingDungCuIndex === index ? (
                          <Input
                            value={state.dungCuList[index]}
                            onChange={(e) => handleChangeDungCu(index, e)}
                            onBlur={() => setState({ editingDungCuIndex: -1 })}
                            autoFocus
                            style={{ width: "100%" }}
                          />
                        ) : (
                          <div
                            style={{
                              display: "flex",
                              justifyContent: "space-between",
                              width: "100%",
                            }}
                          >
                            <span>{item || "(Trống)"}</span>
                            <EditOutlined
                              onClick={() => startEditing(index)}
                              style={{ cursor: "pointer" }}
                            />
                          </div>
                        )}
                      </List.Item>
                    )}
                  />
                </Col>
              </Row>
            </Panel>
          </Collapse>
        </div>
      )}
      {["thuc-hien-va-cong-khai-thuoc"].includes(state.type) && (
        <>
          <Row gutter={[12, 12]}>
            {renderCheckbox({
              key: "kyNhieuChanKyDieuDuong",
              i18n: "editor.kyNhieuChanKyDieuDuong",
            })}
          </Row>
          <Row gutter={[12, 12]}>
            {renderCheckbox({
              key: "hienThiTenDieuDuong",
              i18n: "editor.hienThiTenDieuDuong",
            })}
          </Row>
          <Row gutter={[12, 12]}>
            {renderCheckbox({
              key: "hienThiThuocToDieuTri",
              i18n: "editor.hienThiThuocToDieuTri",
            })}
          </Row>
        </>
      )}
      <Row gutter={[12, 12]}>
        {renderCheckbox({
          key: "hideBorder",
          i18n: "editor.anVien",
        })}
      </Row>
      <Row gutter={[12, 12]}>
        <Col span={24}>
          <span>{t("editor.thietLapTieuDeDungCuVatTuCanKiemDem")}:</span>
        </Col>
      </Row>
      <Row gutter={[12, 12]}>
        {state.type !== "vat-tu-tieu-hao" &&
          state.type !== "tien-su-di-ung" &&
          state.type !== "tom-tat-benh-an" &&
          state.type !== "ban-giao-thuoc" && (
            <Col span={24}>
              <Button
                size={"small"}
                block
                onClick={showDrawer(true)}
                type="primary"
              >
                {t("editor.caiDatNangCao")}
              </Button>
            </Col>
          )}

        <Drawer
          title="Table config"
          placement={"right"}
          closable={false}
          onClose={showDrawer(false)}
          visible={state.visible}
          width={720}
        >
          <Row gutter={[12, 12]}>
            <Col span={24}>
              <TableControl
                ref={tableRef}
                rows={props.state.props.rows}
                cols={props.state.props.cols}
                handleChangeFocus={onChangeValue("focusObj")}
              />
            </Col>

            <Col span={16}>
              <Form {...formItemLayout}>
                {state.type !== "replicationRow" && (
                  <Form.Item
                    style={{ marginBottom: 0 }}
                    label={"Categories quantity"}
                  >
                    <InputNumber
                      value={state.categoriesQty}
                      size={"small"}
                      onChange={handleChangeCategoriesQty}
                    />
                  </Form.Item>
                )}
                {state.type !== "replicationRow" && (
                  <>
                    <Form.Item style={{ marginBottom: 0 }} label={"Column key"}>
                      <Input
                        value={get(state.focusObj, "col.colKey", "")}
                        size={"small"}
                        onChange={handleChangeColKey("colKey", false)}
                      />
                    </Form.Item>
                  </>
                )}
                {(state.type === "replicationRow" ||
                  state.type === "normal") && (
                  <>
                    <Form.Item style={{ marginBottom: 0 }} label={"Cột STT"}>
                      <Checkbox
                        onChange={handleChangeColKey("isStt", true)}
                        checked={get(state.focusObj, "col.isStt", false)}
                      />
                    </Form.Item>
                    {get(state.focusObj, "col.isStt", false) && (
                      <Form.Item
                        style={{ marginBottom: 0 }}
                        label={t("editor.sttBatDauTu")}
                      >
                        <InputNumber
                          style={{ width: "100%" }}
                          value={state.sttBatDau}
                          size={"small"}
                          min={0}
                          onChange={onChangeValue("sttBatDau")}
                        />
                      </Form.Item>
                    )}
                  </>
                )}
                <Form.Item style={{ marginBottom: 0 }} label={"ClassName"}>
                  <Input
                    value={get(state.focusObj, "row.className", "")}
                    size={"small"}
                    onChange={handleChangeRowField("className")}
                  />
                </Form.Item>
                <Form.Item style={{ marginBottom: 0 }} label={"Row header"}>
                  <Checkbox
                    onChange={handleChangeRowHeader}
                    checked={get(state.focusObj, "row.isHeader", false)}
                  />
                </Form.Item>
                <Form.Item style={{ marginBottom: 0 }} label={"Row key"}>
                  <Input
                    value={get(state.focusObj, "row.rowKey", "")}
                    size={"small"}
                    onChange={handleChangeRowField("rowKey")}
                  />
                </Form.Item>
                {state.type == "replicationRow" && (
                  <>
                    <Form.Item
                      style={{ marginBottom: 0 }}
                      label={t("editor.khongSuaDenDongSo")}
                    >
                      <InputNumber
                        style={{ width: "100%" }}
                        value={state.disableToRow}
                        size={"small"}
                        min={0}
                        onChange={onChangeValue("disableToRow")}
                      />
                    </Form.Item>
                    {state.disableToRow ? (
                      <Form.Item
                        style={{ marginBottom: 0 }}
                        label={t("editor.danhSachCotKhongSua")}
                      >
                        <Select
                          mode="multiple"
                          value={state.listColDisable}
                          size={"small"}
                          onChange={onChangeValue("listColDisable")}
                        >
                          {state.filteredOptions.map((item) => (
                            <Select.Option key={item.key} value={item.key}>
                              {item.key}
                            </Select.Option>
                          ))}
                        </Select>
                      </Form.Item>
                    ) : null}
                  </>
                )}
                <Form.Item
                  label={t("editor.duLieuChuKy")}
                  className={"props-form-item"}
                >
                  <Select
                    style={{ width: "100%" }}
                    size={"small"}
                    showSearch
                    onSelect={handleChangeRowField("signedField")}
                    value={get(state.focusObj, "row.signedField", "")}
                  >
                    <Select.Option value={""}>
                      <span>--- {t("editor.deTrong")} ---</span>
                    </Select.Option>
                    {signFields.map((item) => {
                      return (
                        <Select.Option key={item} value={item}>
                          <span title={item}>{item}</span>
                        </Select.Option>
                      );
                    })}
                  </Select>
                </Form.Item>
                {state.type !== "replicationRow" && (
                  <Form.Item
                    style={{ marginBottom: 0 }}
                    label={"Component type"}
                  >
                    <Select
                      value={get(state.focusObj, "col.component", "")}
                      size={"small"}
                      style={{ width: "100%" }}
                      onChange={handleChangeColComponent}
                    >
                      {Object.keys(components).map((item) => (
                        <Select.Option key={item}>{item}</Select.Option>
                      ))}
                    </Select>
                  </Form.Item>
                )}
                {state.type == "gridData" &&
                state?.currentCol?.component == "date" ? (
                  <Form.Item style={{ marginBottom: 0 }} label={"Format"}>
                    <Select
                      value={get(state.focusObj, "col.dateTimeFormat", "")}
                      size={"small"}
                      style={{ width: "100%" }}
                      onChange={handleChangeFormat}
                    >
                      {Object.keys(format).map((key) => (
                        <Select.Option key={key} value={key}>
                          {format[key].label}
                        </Select.Option>
                      ))}
                    </Select>
                  </Form.Item>
                ) : null}
                {state.type !== "replicationRow" && (
                  <>
                    <Form.Item
                      style={{ marginBottom: 0 }}
                      label={"Fixed column"}
                    >
                      <Checkbox
                        onChange={handleChangeColFixed}
                        checked={get(state.focusObj, "col.fixed", false)}
                      />
                    </Form.Item>

                    <Form.Item style={{ marginBottom: 0 }} label={"Fixed row"}>
                      <Checkbox
                        onChange={handleChangeRowFixed}
                        checked={get(state.focusObj, "row.fixed", false)}
                      />
                    </Form.Item>

                    <Form.Item
                      style={{ marginBottom: 0 }}
                      label={"Have plus btn"}
                    >
                      <Checkbox
                        onChange={handleChangeColPlusBtn}
                        checked={get(state.focusObj, "col.plusBtn", false)}
                      />
                    </Form.Item>

                    <Form.Item style={{ marginBottom: 0 }} label={"Row copied"}>
                      <Checkbox
                        onChange={handleChangeRowCopied}
                        checked={get(state.focusObj, "row.copied", false)}
                      />
                    </Form.Item>

                    <Form.Item style={{ marginBottom: 0 }} label={"Fields"}>
                      <Input.TextArea
                        rows={3}
                        value={state.fields}
                        size={"small"}
                        onChange={onChangeInput("fields")}
                      />
                    </Form.Item>
                  </>
                )}
              </Form>
            </Col>
            <Col
              span={24}
              className={"action-footer"}
              style={{ marginTop: 15 }}
            >
              <Button
                icon={<SaveOutlined />}
                onClick={submit}
                style={{ marginRight: "6px" }}
                type={"primary"}
                className="action-item"
              >
                {t("common.luu")}
              </Button>
              <Button onClick={showDrawer(false)} className="action-item">
                {t("common.huy")}
              </Button>
            </Col>
          </Row>
        </Drawer>
      </Row>
    </Main>
  );
});

TableProperties.defaultProps = {
  state: {},
};

TableProperties.propTypes = {
  state: T.shape({}),
};

export default TableProperties;
