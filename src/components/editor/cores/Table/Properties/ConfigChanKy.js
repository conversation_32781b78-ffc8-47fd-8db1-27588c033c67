import React from "react";
import { Row, Col, InputNumber, Checkbox, Input, Select } from "antd";
import {
  AlignConfig,
  EditorTool,
  FontSizeConfig,
} from "components/editor/config";
import { useTranslation } from "react-i18next";
import { DS_LOAI_KY, VI_TRI_CA } from "../../ImageSign/constanst";
import { FontColorsOutlined } from "@ant-design/icons";

const { FieldName, PickColor } = EditorTool;

const ConfigChanKy = ({
  onChangeCheckbox,
  onChangeInput,
  onChangeValue,
  state,
  getFieldName,
  index = "",
}) => {
  const { t } = useTranslation();

  return (
    <>
      <Row gutter={[12, 12]} style={{ marginBottom: 10 }}>
        <Col span={8}>
          <span>{t("danhMuc.tenChanKy")}:</span>
        </Col>
        <Col span={16}>
          <Input
            value={state[`textTenChanKy${index}`]}
            readOnly
            onChange={onChangeInput(`textTenChanKy${index}`)}
          />
        </Col>
      </Row>
      <Row gutter={[12, 12]} style={{ marginBottom: 10 }}>
        <Col span={8}>
          <span>{t("editor.capKy")}:</span>
        </Col>
        <Col span={16}>
          <InputNumber
            type="number"
            size={"small"}
            min={1}
            value={state[`capKy${index}`]}
            onChange={onChangeValue(`capKy${index}`)}
          ></InputNumber>
        </Col>
      </Row>
      <Row gutter={[12, 12]} style={{ marginBottom: 10 }}>
        <Col span={16}>{t("editor.hienTenChuKyVietTat")}</Col>
        <Col span={8}>
          <Checkbox
            // style={{ width: "100%" }}
            onChange={onChangeCheckbox(`showTenVietTat${index}`)}
            checked={state[`showTenVietTat${index}`]}
          // checked={state[`showPatientSign${index}`]}
          // onChange={onChangeCheckbox(`showPatientSign${index}`)}
          />
        </Col>
      </Row>

      <Row gutter={[12, 12]} style={{ marginBottom: 10 }}>
        <Col span={8}>
          <span>{"Field name Ký: "}:</span>
        </Col>
        <Col span={16}>
          <FieldName
            style={{ width: "100%" }}
            onSelect={onChangeValue(`fieldNameKy${index}`)}
            value={state[`fieldNameKy${index}`]}
            apiFields={getFieldName() || []}
          />
        </Col>
      </Row>
      <Row gutter={[12, 12]}>
        <Col span={8}>
          <span>{t("editor.loaiKy")}</span>
        </Col>
        <Col span={16}>
          <Select
            showSearch
            size={"small"}
            style={{ width: "100%" }}
            value={state[`loaiKy${index}`]}
            onSelect={onChangeValue(`loaiKy${index}`)}
          >
            {DS_LOAI_KY.map((item, index) => (
              <Select.Option key={index} value={item.id}>
                {t(item.ten)}
              </Select.Option>
            ))}
          </Select>
        </Col>

        {state[`loaiKy${index}`] === 2 ? (
          <>
            <Col span={8}>
              <span>{t("editor.tenChanKyNb")}:</span>
            </Col>
            <Col span={16}>
              <Input
                value={state[`textSignPatient${index}`]}
                onChange={onChangeInput(`textSignPatient${index}`)}
              />
            </Col>
          </>
        ) : (
          <></>
        )}
        <Col span={16}>
          <span>{t("editor.hienThiNguoiKy")}</span>
        </Col>
        <Col span={8}>
          <Checkbox
            checked={state[`showPatientSign${index}`]}
            onChange={onChangeCheckbox(`showPatientSign${index}`)}
          />
        </Col>

        {state[`showPatientSign${index}`] && (
          <>
            <Col span={16}>
              <span>{t("editor.anNhanTenNguoiKyVaThoiGian")}</span>
            </Col>
            <Col span={8}>
              <Checkbox
                checked={state[`anNhanTenVaThoiGian${index}`]}
                onChange={onChangeCheckbox(`anNhanTenVaThoiGian${index}`)}
              />
            </Col>
            <Col span={16}>
              <span>{t("editor.anThoiGianKy")}</span>
            </Col>
            <Col span={8}>
              <Checkbox
                checked={state[`anThoiGianKy${index}`]}
                onChange={onChangeCheckbox(`anThoiGianKy${index}`)}
              />
            </Col>
            <Col span={8}>
              <span>{t("editor.viTriNguoiKy")}</span>
            </Col>
            <Col span={16}>
              <Select
                showSearch
                size={"small"}
                style={{ width: "100%" }}
                value={state[`viTriCa${index}`]}
                onSelect={onChangeValue(`viTriCa${index}`)}
              >
                {VI_TRI_CA.map((item, index) => (
                  <Select.Option key={index} value={item.id}>
                    {t(item.ten)}
                  </Select.Option>
                ))}
              </Select>
            </Col>
            <Col span={8}>
              <span>{t("editor.canLeNguoiKy")}</span>
            </Col>
            <Col span={16}>
              <AlignConfig
                changeAlign={onChangeValue(`canLeNguoiKy${index}`)}
                contentAlign={state[`canLeNguoiKy${index}`]}
              />
            </Col>
            <Col span={8}>
              <span>{t("editor.mauChuNguoiKy")}</span>
            </Col>
            <Col span={16}>
              <PickColor
                iconComponent={FontColorsOutlined}
                title={t("editor.chonMauChu")}
                dataColor={state[`contentColor${index}`] || "black"}
                changeColor={onChangeValue(`contentColor${index}`)}
              />
            </Col>
            <Col span={8}>
              <span>{t("editor.coChuNguoiKy")}</span>
            </Col>
            <Col span={16}>
              <FontSizeConfig
                changeFont={onChangeValue(`fontSize${index}`)}
                fontSize={state[`fontSize${index}`]}
              />
            </Col>
          </>
        )}

        <Col span={16}>
          <span>{t("editor.hienThiCA")}</span>
        </Col>
        <Col span={8}>
          <Checkbox
            checked={state[`showCa${index}`]}
            onChange={onChangeCheckbox(`showCa${index}`)}
          />
        </Col>
        {state[`showCa${index}`] && (
          <>
            <Col span={8}>
              <span>{t("editor.viTriCA")}</span>
            </Col>
            <Col span={16}>
              <Select
                showSearch
                size={"small"}
                style={{ width: "100%" }}
                value={state[`viTriAnhCa${index}`]}
                onSelect={onChangeValue(`viTriAnhCa${index}`)}
              >
                {VI_TRI_CA.map((item, index) => (
                  <Select.Option key={index} value={item.id}>
                    {t(item.ten)}
                  </Select.Option>
                ))}
              </Select>
            </Col>
            <Col span={8}>
              <span>{t("editor.canLeCa")}</span>
            </Col>
            <Col span={16}>
              <AlignConfig
                changeAlign={onChangeValue(`canLeCa${index}`)}
                contentAlign={state[`canLeCa${index}`]}
              />
            </Col>
            <Col span={8}>
              <span>{t("editor.doRongCA")}</span>
            </Col>
            <Col span={16}>
              <InputNumber
                type="number"
                size={"small"}
                min={1}
                value={state[`widthCa${index}`]}
                onChange={onChangeValue(`widthCa${index}`)}
              ></InputNumber>
            </Col>
          </>
        )}
      </Row>
      <Row gutter={[12, 12]} style={{ marginBottom: 10 }}>
        <Col span={16}>
          <span>{t("editor.khoaBieuMauSauKhiKy")}:</span>
        </Col>
        <Col span={8}>
          <Checkbox
            checked={state[`disableIfSigned${index}`]}
            onChange={onChangeCheckbox(`disableIfSigned${index}`)}
          />
        </Col>
      </Row>
      <Row gutter={[12, 12]} style={{ marginBottom: 10 }}>
        <Col span={16}>
          <span>{t("editor.choPhepHuyKhiDaKy")}:</span>
        </Col>
        <Col span={8}>
          <Checkbox
            checked={state[`allowReset${index}`]}
            onChange={onChangeCheckbox(`allowReset${index}`)}
          />
        </Col>
      </Row>
      <Row gutter={[12, 12]} style={{ marginBottom: 10 }}>
        <Col span={16}>
          <span>{t("editor.anChuKy")}:</span>
        </Col>
        <Col span={8}>
          <Checkbox
            checked={state[`anChuKy${index}`]}
            onChange={onChangeCheckbox(`anChuKy${index}`)}
          />
        </Col>
      </Row>
      <Row gutter={[12, 12]} style={{ marginBottom: 10 }}>
        <Col span={16}>
          <span>{t("editor.kyAnhVanTay")}:</span>
        </Col>
        <Col span={8}>
          <Checkbox
            checked={state[`kyAnhVanTay${index}`]}
            onChange={onChangeCheckbox(`kyAnhVanTay${index}`)}
          />
        </Col>
      </Row>
    </>
  );
};

export default ConfigChanKy;
