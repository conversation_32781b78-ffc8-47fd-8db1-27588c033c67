import React, {
  forwardRef,
  memo,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { GlobalStyle, Main } from "./styled";
import { useDispatch } from "react-redux";
import { <PERSON><PERSON>, TimePicker } from "antd";
import { MODE } from "utils/editor-utils";
import {
  SettingOutlined,
  EyeInvisibleOutlined,
  EyeOutlined,
} from "@ant-design/icons";
import { useEnum, useStore, useThietLap } from "hooks";
import { ENUM, THIET_LAP_CHUNG } from "constants/index";
import stringUtils from "mainam-react-native-string-utils";
import moment from "moment";
import { getTypeTime } from "../../PhieuThucHienVaCongKhaiThuoc/constants";
import { cloneDeep, flatten, groupBy, sortBy } from "lodash";
import GioSuDungs from "./components/Row";
import { getAllQueryString } from "hooks/useQueryString/queryString";
import ImageSign from "../../ImageSign";
import { SVG } from "assets";
import { Tooltip } from "components";

export const ContextPhieu = React.createContext();
export const ContextPhieuProvider = ContextPhieu.Provider;

const CHUKY_INDEX = {
  dsChuKyBacSi: 1,
  dsChuKyKiemTra: 2,
  dsChuKyDieuDuong: 3,
  dsChuKyNguoiNha: 4,
};

const DEFAULT_DS_THOI_GIAN_SD = [
  { stt: 1, time: "06:00", field: "slSang" },
  { stt: 2, time: "08:00", field: "slChieu" },
  { stt: 3, time: "14:00", field: "slToi" },
  { stt: 4, time: "20:00", field: "slDem" },
];

const ThucHienVaCongKhaiThuocBVDKKVTD = forwardRef((props, ref) => {
  const [state, _setState] = useState({
    dsThuoc: [],
    dsNgayYLenh: [],
    showBoSung: true,
    dsThoiGian: [],
  });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };
  const { tuThoiGian, denThoiGian } = getAllQueryString();
  const { component, mode, form, formChange } = props;
  const itemProps = component.props || {};
  const init = useDispatch().component.init;
  const [listDonViTocDoTruyen] = useEnum(ENUM.DON_VI_TOC_DO_TRUYEN);
  const refdsThoiGianSuDung = useRef([]);

  const [MA_DUONG_DUNG_DICH_TRUYEN, loadFinish] = useThietLap(
    THIET_LAP_CHUNG.MA_DUONG_DUNG_DICH_TRUYEN
  );
  const [dataTHOI_GIAN_VA_TEN_BUOI_SANG] = useThietLap(
    THIET_LAP_CHUNG.THOI_GIAN_VA_TEN_BUOI_SANG,
    ""
  );
  const [dataTHOI_GIAN_VA_TEN_BUOI_CHIEU] = useThietLap(
    THIET_LAP_CHUNG.THOI_GIAN_VA_TEN_BUOI_CHIEU,
    ""
  );
  const [dataTHOI_GIAN_VA_TEN_BUOI_TOI] = useThietLap(
    THIET_LAP_CHUNG.THOI_GIAN_VA_TEN_BUOI_TOI,
    ""
  );
  const [dataTHOI_GIAN_VA_TEN_BUOI_DEM] = useThietLap(
    THIET_LAP_CHUNG.THOI_GIAN_VA_TEN_BUOI_DEM,
    ""
  );

  const handleFocus = () => {
    if (mode === MODE.config) {
      init(component);
    }
  };
  const refDataThuoc = useRef({});
  const refDsThuoc = useRef([]);

  const configThoiGianBuoi = useMemo(() => {
    if (
      dataTHOI_GIAN_VA_TEN_BUOI_SANG &&
      dataTHOI_GIAN_VA_TEN_BUOI_CHIEU &&
      dataTHOI_GIAN_VA_TEN_BUOI_TOI &&
      dataTHOI_GIAN_VA_TEN_BUOI_DEM
    )
      return {
        SANG: dataTHOI_GIAN_VA_TEN_BUOI_SANG,
        CHIEU: dataTHOI_GIAN_VA_TEN_BUOI_CHIEU,
        TOI: dataTHOI_GIAN_VA_TEN_BUOI_TOI,
        DEM: dataTHOI_GIAN_VA_TEN_BUOI_DEM,
      };

    return null;
  }, [
    dataTHOI_GIAN_VA_TEN_BUOI_SANG,
    dataTHOI_GIAN_VA_TEN_BUOI_CHIEU,
    dataTHOI_GIAN_VA_TEN_BUOI_TOI,
    dataTHOI_GIAN_VA_TEN_BUOI_DEM,
  ]);

  //Lấy ds thời gian
  useEffect(() => {
    if (Object.keys(form || {}).length) {
      let _dsNgay = [];
      const tuNgay = moment(tuThoiGian); // ví dụ: '2025-08-01'
      const denNgay = moment(denThoiGian); // ví dụ: '2025-08-04'

      for (
        let date = tuNgay.clone();
        date.isSameOrBefore(denNgay);
        date.add(1, "days")
      ) {
        _dsNgay.push({ time: date.format("YYYY-MM-DD"), data: [] }); // In ra dạng YYYY-MM-DD
      }

      let _dsThuoc = form.dsThuoc || [];

      //Duyệt qua toàn bộ thời gian sử dụng của thuốc
      _dsThuoc.forEach((thuoc) => {
        (thuoc.dsThoiGianSuDung || []).forEach((thoiGian) => {
          const _thoiGianFormat = moment(thoiGian.tuThoiGian).format(
            "YYYY-MM-DD"
          );
          const _ngayIdx = _dsNgay.findIndex((x) => x.time == _thoiGianFormat);

          if (_ngayIdx > -1) {
            if (
              _dsNgay[_ngayIdx].data.findIndex(
                (x) => x.stt === thoiGian.stt
              ) === -1
            ) {
              _dsNgay[_ngayIdx].data.push({
                stt: thoiGian.stt,
                thoiGian: thoiGian.tuThoiGian
                  ? moment(thoiGian.tuThoiGian)
                  : null,
              });
            }
          }
        });
      });

      //Fill đủ mỗi ngày ít nhất 4 cột
      _dsNgay = _dsNgay.map((item) => {
        DEFAULT_DS_THOI_GIAN_SD.forEach((element) => {
          if (item.data.findIndex((x) => x.stt == element.stt) == -1) {
            item.data.push({
              stt: element.stt,
              thoiGian: moment(`${item.time} ${element.time}:00`),
            });
          }
        });

        return item;
      });

      setState({ dsThoiGian: _dsNgay });
    }
  }, [Object.keys(form || {}).length, tuThoiGian, denThoiGian]);

  useEffect(() => {
    if (Object.keys(form || {}).length && loadFinish) {
      const _listDichTruyen = (MA_DUONG_DUNG_DICH_TRUYEN || "").split(",");
      let _dsThuoc = form.dsThuoc || [];
      let dsIdThuocDichTruyen = [];

      if (!itemProps?.hienThuocDichTruyen) {
        _dsThuoc = _dsThuoc.filter(
          (x) => !_listDichTruyen.includes(x.maDuongDung)
        );
        dsIdThuocDichTruyen = (form.dsThuoc || [])
          .filter((x) => _listDichTruyen.includes(x.maDuongDung))
          .map((item) => item.id);
        _dsThuoc = _dsThuoc.filter(
          (x1) => !dsIdThuocDichTruyen.includes(x1.dungKemId)
        );
      }

      let dsThuoc =
        _dsThuoc.filter((x) => {
          const thoiGianThucHien = new Date(x.thoiGianThucHien).getTime();
          const tuNgay = new Date(tuThoiGian).getTime();
          const denNgay = new Date(denThoiGian).getTime();
          return thoiGianThucHien >= tuNgay && thoiGianThucHien <= denNgay;
        }) || [];
      dsThuoc.forEach((thuoc) => {
        thuoc.thoiGianThucHien2 = moment(thuoc.thoiGianThucHien).format(
          "YYYY-MM-DD"
        );
      });

      dsThuoc = dsThuoc.map((item) => {
        DEFAULT_DS_THOI_GIAN_SD.forEach((element) => {
          if (!item.dsThoiGianSuDung) {
            item.dsThoiGianSuDung = [];
          }

          if (
            (item.dsThoiGianSuDung || []).findIndex(
              (x) => x.stt == element.stt
            ) == -1
          ) {
            item.dsThoiGianSuDung.push({
              stt: element.stt,
              type: 1,
              soLuong: item[element.field],
              tuThoiGian: `${moment(item.thoiGianThucHien).format(
                "YYYY-MM-DD"
              )} ${element.time}:00`,
            });
          }
        });

        return item;
      });

      refdsThoiGianSuDung.current = dsThuoc.map((item) => {
        (item.dsThoiGianSuDung || []).forEach((item) => {
          item.id = stringUtils.guid();
        });
        return {
          ...item,
          id: item.id,
          loai: item.loai,
          dsThoiGianSuDung: item.dsThoiGianSuDung,
        };
      });

      const thuocsGroup = groupBy(dsThuoc, "thoiGianThucHien2");
      let dsNgayYLenh = Object.keys(thuocsGroup)
        .map((ngayYLenh) => ngayYLenh)
        .sort(
          (a, b) =>
            new Date(a.split("/").reverse().join("/")).getTime() -
            new Date(b.split("/").reverse().join("/")).getTime()
        );
      if (dsNgayYLenh.length < 3) {
        dsNgayYLenh.push(...new Array(3 - dsNgayYLenh.length).fill(""));
      }

      dsThuoc = dsThuoc.map((item) => ({
        ...item,
        dsThoiGianSuDung: (item.dsThoiGianSuDung || []).map((x) => ({
          ...x,
          type: getTypeTime(x.tuThoiGian, x.tuThoiGian, configThoiGianBuoi),
        })),
      }));

      dsThuoc = (dsThuoc || [])
        .filter((x) => !x.dungKemId)
        .map((item) => ({
          ...item,
          dsThuocDungKem: (dsThuoc || []).filter(
            (x) => x.dungKemId === item.id
          ),
        }));

      const merged = dsThuoc.reduce(
        (r, { dichVuId, thoiGianThucHien, ...rest }) => {
          const key = `${dichVuId}`;

          r[key] = r[key] || {
            dsThuoc: [],
          };
          r[key]["dsThuoc"].push({
            dichVuId,
            thoiGianThucHien,
            thoiGianThucHien2: moment(thoiGianThucHien).format("YYYY-MM-DD"),
            ...rest,
          });
          return r;
        },
        {}
      );

      let boSung = "";

      //lấy thông tin bổ sung
      const _dsThuocTheoTgThucHien = groupBy(dsThuoc, "thoiGianThucHien");
      const _dsNgayYLenhHienThi = dsNgayYLenh.slice(0, 3);

      const _dsBoSung = Object.keys(_dsThuocTheoTgThucHien)
        .filter((key) => {
          if (key == "") return false;
          return _dsNgayYLenhHienThi.includes(moment(key).format("DD/MM/YYYY"));
        })
        .map((key) => _dsThuocTheoTgThucHien[key]);

      const _dsThuocTheoTgYLenh = groupBy(
        flatten(_dsBoSung),
        (x) => x.chiDinhTuDichVuId
      );

      boSung = Object.keys(_dsThuocTheoTgYLenh)
        .map((key) => _dsThuocTheoTgYLenh[key][0])
        .filter((x) => !!x.boSung)
        .map((x) => ({
          time: x.thoiGianYLenh,
          content: x.boSung,
        }));

      let dsThuoc2 = Object.keys(merged).map((thuoc) => {
        const ngayYLenh = groupBy(merged[thuoc].dsThuoc, "thoiGianThucHien2");
        /*
        nam.mn 13/07/2025 
        Lỗi liên quan đến việc có 2 thuốc giống nhau, mỗi thuốc có 1 ds dùng kèm riêng,
        Đang gom thuốc lại, nhưng đang không gom thuốc dùng kèm -> bị thiếu thuốc dùng kèm
        */
        // 1. tạo mảng ds các thuốc dùng kèm
        let dsThuocDungKem = [];
        Object.keys(ngayYLenh).forEach((item) => {
          const tc = ngayYLenh[item].reduce((a, b) => {
            return (a || 0) + (b.soLuong || 0);
          }, 0);
          // 2. thực hiện gom hết tất cả các thuốc dùng kèm lại
          dsThuocDungKem = [
            ...dsThuocDungKem,
            ...flatten(ngayYLenh[item].map((item2) => item2.dsThuocDungKem)),
          ];
          ngayYLenh[item].forEach((el) => {
            el.tc = tc;
          });
        });
        let tongLieu = 0;

        Object.keys(ngayYLenh).forEach((item) => {
          if (!ngayYLenh[item][0].thuocDaChiDinh) {
            tongLieu += ngayYLenh[item][0]?.tc;
          }
        });
        // 3. gom theo dichVuId và cộng dồn số lượng
        dsThuocDungKem = Object.entries(
          groupBy(dsThuocDungKem, "dichVuId")
        ).map(([k, v]) => {
          if (v.length == 1) return v[0];
          v[0].soLuong = v.reduce((a, b) => (a += b.soLuong), 0);
          return v[0];
        });

        const firstThuoc = merged[thuoc].dsThuoc[0];
        firstThuoc.dsThuocDungKem = dsThuocDungKem;
        // Lấy ds Thuốc dùng kèm đã gộp để hiển thị ra phiếu
        const tongSoLuongHuy = dsThuoc
          .filter((x) => x.dichVuId === firstThuoc.dichVuId)
          .reduce((a, b) => (a || 0) + (b.soLuongHuy || 0), 0);
        const data = {
          tenThuoc: renderThuoc({ data: firstThuoc, tongSoLuongHuy }),
          dsNgayYLenh: ngayYLenh,
          tongLieu,
          donVi: firstThuoc.tenDvtSoCap,
          lieuDung: `${firstThuoc.soLuong1Lan ? firstThuoc.soLuong1Lan : ""} ${
            firstThuoc.tenDvtSuDung ? firstThuoc?.tenDvtSuDung : ""
          }${firstThuoc.soLan1Ngay ? ` x ${firstThuoc?.soLan1Ngay}` : ""}`,
          tenDuongDung: firstThuoc.tenDuongDung,
          id: stringUtils.guid(),
          tenDonViTinh: firstThuoc.tenDonViTinh,
          loai: firstThuoc.loai,
          sttHienThi: firstThuoc.sttHienThi,
          thoiGianThucHien: firstThuoc.thoiGianThucHien,
          sttDuongDung: firstThuoc.sttDuongDung,
          dsThuocDungKem: dsThuocDungKem,
          tenDvtSuDung: firstThuoc.tenDvtSuDung,
          tenDvtSoCap: firstThuoc.tenDvtSoCap,
        };
        return data;
      });

      dsThuoc2 = sortBy(dsThuoc2, [
        "thoiGianThucHien",
        "sttHienThi",
        "sttDuongDung",
      ]);

      refDsThuoc.current = dsThuoc2;
      refDataThuoc.current = dsThuoc2;

      setState({
        dsThuoc: dsThuoc2,
        dsNgayYLenh: dsNgayYLenh.slice(0, 3),
        boSung,
      });
    }
  }, [
    Object.keys(form || {}).length,
    tuThoiGian,
    denThoiGian,
    itemProps,
    MA_DUONG_DUNG_DICH_TRUYEN,
    loadFinish,
    configThoiGianBuoi,
  ]);

  const renderThuoc = ({
    data,
    isSub = false,
    thuocKeNgoai = false,
    tongSoLuongHuy,
  }) => {
    const buoiDung = [];
    if (data.slSang) buoiDung.push({ ten: "Sáng", soLuong: data.slSang });
    if (data.slChieu) buoiDung.push({ ten: "Chiều", soLuong: data.slChieu });
    if (data.slToi) buoiDung.push({ ten: "Tối", soLuong: data.slToi });
    if (data.slDem) buoiDung.push({ ten: "Đêm", soLuong: data.slDem });
    const donViTocDoTruyen = (listDonViTocDoTruyen || []).find(
      (el) => el.id === data.donViTocDoTruyen
    );
    let textBuoiDung = buoiDung
      .map((buoi, index) => {
        return `${buoi.ten ? ` ${buoi.ten}` : ""}${
          buoi.soLuong ? ` ${buoi.soLuong}` : ""
        }${data.tocDoTruyen ? `, ${data.tocDoTruyen}` : ""}${
          donViTocDoTruyen ? `(${donViTocDoTruyen?.ten})` : ""
        }`;
      })
      .join(", ");
    if (!data.slSang && !data.slToi && !data.slDem && !data.slChieu) {
      textBuoiDung = `${data.tocDoTruyen ? `${data.tocDoTruyen}` : ""}${
        donViTocDoTruyen ? `(${donViTocDoTruyen?.ten})` : ""
      }`;
    }

    const tenHoatChat_HamLuong = () => {
      let textCustom = "";
      if (data.tenHoatChat && !data.hamLuong) {
        textCustom = ` (${data?.tenHoatChat})`;
      } else if (!data.tenHoatChat && data.hamLuong) {
        textCustom = ` (${data?.hamLuong})`;
      } else if (data.tenHoatChat && data.hamLuong) {
        textCustom = ` (${data?.tenHoatChat} - ${data?.hamLuong})`;
      }
      return textCustom;
    };

    const soLuong_Dvt = () => {
      if (!itemProps?.hienSoLuongDvt) return null;

      return (
        <>
          x{" "}
          {data.loaiDonThuoc === 20 ? (
            <span className="so-luong" style={{ margin: `0 5px` }}>
              {numberToString(data.soLuong)}
            </span>
          ) : (
            data.soLuong
          )}
          {data.tenDonViTinh ? ` (${data.tenDonViTinh})` : ""}{" "}
        </>
      );
    };

    return (
      <div className={`item-drug  ${isSub ? "drug" : ""} `}>
        <div className={` ml5 ${isSub ? "child" : ""}`}>
          <div
            className={`${!isSub ? "b" : ""}`}
            style={{ fontWeight: "bold" }}
          >
            {isSub ? "-" : ""}{" "}
            {thuocKeNgoai ? data.tenThuocChiDinhNgoai : data.tenDichVu}
            {tenHoatChat_HamLuong()}
            {data.boSung ? "(bổ sung)" : ""}{" "}
            {thuocKeNgoai
              ? `(TT)`
              : [50, 55].includes(data.loai)
              ? `(NT)`
              : " "}
            {soLuong_Dvt()}
            <i style={{ fontWeight: "normal", marginLeft: 2 }}>
              {tongSoLuongHuy || data.soLuongHuy
                ? `  (Hủy ${tongSoLuongHuy || data.soLuongHuy} ${
                    data.tenDonViTinh || ""
                  }${data.lyDoHuy ? `, ${data.lyDoHuy}` : ""})`
                : ""}
            </i>
          </div>
          {/* <div className="i " style={{ fontStyle: "italic" }}>
            <div>
              {`${data.tenLieuDung || ""} ${
                data.cachDung && itemProps.hienCachDung
                  ? `${data.cachDung}`
                  : ""
              } ${
                textBuoiDung
                  ? `(${textBuoiDung}${
                      data.thoiDiem ? ` ${data.thoiDiem}` : ""
                    })`
                  : ""
              }`}
              <span>{!!data.ghiChu && `.Ghi chú: ${data.ghiChu}`}</span>
            </div>
          </div> */}
        </div>
        {data?.dsThuocDungKem?.length ? (
          <>
            {data.dsThuocDungKem.map((e) =>
              renderThuoc({ data: e, isSub: true, thuocKeNgoai })
            )}
          </>
        ) : null}
      </div>
    );
  };

  const onChangeForm = ({ thuocId, timeId, key, value }) => {
    const _thuoc = (refdsThoiGianSuDung.current || []).find(
      (item) => item.id === thuocId
    );

    const _thoiGian = (_thuoc?.dsThoiGianSuDung || []).find(
      (el) => el.id === timeId
    );

    if (_thoiGian) {
      _thoiGian[key] = value;

      formChange["dsThuoc"]?.(refdsThoiGianSuDung.current);
    }
  };

  const onChangeTuThoiGian =
    ({ stt, ngay, indexNgay, indexGio }) =>
    (e) => {
      const value =
        e instanceof moment ? `${ngay} ${e.format("HH:mm:00")}` : null;

      refdsThoiGianSuDung.current = refdsThoiGianSuDung.current.map((item) => {
        const _thoiGianSDIdx = (item.dsThoiGianSuDung || []).findIndex(
          (x) =>
            x.stt === stt && moment(x.tuThoiGian).format("YYYY-MM-DD") == ngay
        );
        if (_thoiGianSDIdx > -1) {
          item.dsThoiGianSuDung[_thoiGianSDIdx].tuThoiGian = value;
        } else {
          if (!item.dsThoiGianSuDung) {
            item.dsThoiGianSuDung = [];
          }

          item.dsThoiGianSuDung.push({ stt, tuThoiGian: value });
        }
        item.dsThoiGianSuDung = item.dsThoiGianSuDung.filter(
          (thoiGian) => !!thoiGian.stt
        );

        return item;
      });

      state.dsThoiGian[indexNgay].data[indexGio].thoiGian = e;

      refDataThuoc.current.map((thuoc) => {
        if (thuoc.dsNgayYLenh?.[ngay]?.length > 0) {
          thuoc.dsNgayYLenh?.[ngay].map((thuocTrongNgay) => {
            //Tìm thuốc theo id và replace theo dsThoiGianSuDung
            const _updateThuoc = refdsThoiGianSuDung.current.find(
              (x) => x.id === thuocTrongNgay.id
            );
            if (_updateThuoc) {
              thuocTrongNgay.dsThoiGianSuDung = _updateThuoc.dsThoiGianSuDung;
            }
          });
        }
      });

      setState({
        dsThoiGian: cloneDeep(state.dsThoiGian),
        dsThuoc: cloneDeep(refDataThuoc.current),
      });
      formChange["dsThuoc"]?.(refdsThoiGianSuDung.current);
    };

  const onAddTuThoiGian =
    ({ indexNgay }) =>
    () => {
      state.dsThoiGian[indexNgay].data.push({
        stt: state.dsThoiGian[indexNgay].data.length + 1,
      });
      setState({ dsThoiGian: cloneDeep(state.dsThoiGian) });
    };

  const onRemoveTuThoiGian =
    ({ indexNgay, indexGio }) =>
    () => {
      state.dsThoiGian[indexNgay].data.splice(indexGio, 1);
      setState({ dsThoiGian: cloneDeep(state.dsThoiGian) });
    };

  const renderChuKy = (index, key) => {
    const idx = CHUKY_INDEX[key];

    return (
      <ImageSign
        component={{
          props: {
            fieldName: itemProps[`fieldNameKy${idx}`],
            allowReset: itemProps[`allowReset${idx}`],
            fontSize: 12,
            capKy: itemProps[`capKy${idx}`],
            loaiKy: key === "dsChuKyNguoiNha" ? 2 : 1,
            width: 45,
            height: 40,
            showCa: false,
            isMultipleSign: true,
            showPatientSign: false,
            isKyTheoCot: true,
            viTri: `${idx}${index + 1}`,
            customText: "Ký",
            contentAlign: "center",
            kyAnhVanTay: itemProps[`kyAnhVanTay${idx}`],
            dataSign: {
              id: form.id,
              soPhieu: form.soPhieu,
              lichSuKyId: form?.lichSuKy?.id,
              nbDotDieuTriId: form.nbDotDieuTriId,
            },
          },
        }}
        mode={mode}
        form={{ lichSuKy: form.lichSuKy }}
      />
    );
  };

  const renderFooter = () => {
    const colLength = flatten(
      state.dsThoiGian.map((item) => item.data || [])
    ).length;

    return (
      <>
        {Array.from(Array(3).keys()).map((key) => (
          <tr className="tr-empty">
            {new Array(colLength + 4).fill({}).map((item, index) => (
              <td key={index}></td>
            ))}
          </tr>
        ))}
        {!itemProps[`anChuKy1`] && (
          <tr className="tr-sign">
            <td colSpan={2}>Bác sĩ ba tra thuốc</td>
            <td></td>
            <td></td>
            {state.dsThoiGian.map((item, index) => {
              return item.data.map((x, idx) => {
                const indexKy = index * 100 + idx;

                return (
                  <td key={`${index}-${idx}`}>
                    {renderChuKy(indexKy, "dsChuKyBacSi")}
                  </td>
                );
              });
            })}
          </tr>
        )}
        {!itemProps[`anChuKy2`] && (
          <tr className="tr-sign">
            <td colSpan={2}>Kiểm tra kép</td>
            <td></td>
            <td></td>
            {state.dsThoiGian.map((item, index) => {
              return item.data.map((x, idx) => {
                const indexKy = index * 100 + idx;

                return (
                  <td key={`${index}-${idx}`}>
                    {renderChuKy(indexKy, "dsChuKyKiemTra")}
                  </td>
                );
              });
            })}
          </tr>
        )}
        {!itemProps[`anChuKy3`] && (
          <tr className="tr-sign">
            <td colSpan={2}>Tên điều dưỡng thực hiện</td>
            <td></td>
            <td></td>
            {state.dsThoiGian.map((item, index) => {
              return item.data.map((x, idx) => {
                const indexKy = index * 100 + idx;

                return (
                  <td key={`${index}-${idx}`}>
                    {renderChuKy(indexKy, "dsChuKyDieuDuong")}
                  </td>
                );
              });
            })}
          </tr>
        )}
        {!itemProps[`anChuKy4`] && (
          <tr className="tr-sign">
            <td colSpan={2}>Người bệnh / Người nhà ký tên</td>
            <td></td>
            <td></td>
            {state.dsThoiGian.map((item, index) => {
              return item.data.map((x, idx) => {
                const indexKy = index * 100 + idx;

                return (
                  <td key={`${index}-${idx}`}>
                    {renderChuKy(indexKy, "dsChuKyNguoiNha")}
                  </td>
                );
              });
            })}
          </tr>
        )}
      </>
    );
  };

  const renderBoSung = () => {
    const renderItem = () => {
      return (state.boSung || []).map((item) => (
        <div style={{ marginBottom: 10 }}>
          <span>
            <b>{moment(item.time).format("HH:mm - DD/MM/YYYY")}</b>
          </span>
          <br />
          <span
            dangerouslySetInnerHTML={{
              __html: (item.content || "").replaceAll("\n", "<br/>"),
            }}
          ></span>
        </div>
      ));
    };

    const colLength = flatten(
      state.dsThoiGian.map((item) => item.data || [])
    ).length;

    return (
      <tr className="tr-empty">
        <td></td>
        <td>{state.showBoSung && renderItem()}</td>
        <td></td>
        <td></td>
        {[...Array(colLength + 1).keys()].map(() => (
          <td></td>
        ))}
      </tr>
    );
  };

  const getDonVi = (item) => {
    const { tenDonViTinh, tenDvtSoCap, tenDvtSuDung, loai } = item;

    if (
      loai === 60 ||
      (tenDonViTinh === tenDvtSoCap && tenDonViTinh !== tenDvtSuDung)
    ) {
      return tenDonViTinh;
    }

    return tenDvtSuDung || "";
  };

  const renderTable = useMemo(() => {
    return (
      <ContextPhieuProvider
        value={{
          onChangeForm,
          dsThoiGian: state.dsThoiGian,
        }}
      >
        {(state?.dsThuoc || []).map((item, index) => {
          return (
            <tr>
              <td className="stt">{index + 1}</td>
              <td>{item.tenThuoc}</td>
              <td className="center">
                <div className="flex-td">{item?.donVi} </div>
                {(item.dsThuocDungKem || []).map((x) => {
                  return (
                    <div style={{ marginTop: "15px" }} className="flex-td">
                      {x.tenDvtSoCap}
                    </div>
                  );
                })}
              </td>
              <td className="center">
                <div className="flex-td">{item?.tenDuongDung} </div>
                {(item.dsThuocDungKem || []).map((x) => {
                  return (
                    <div style={{ marginTop: "15px" }} className="flex-td">
                      {x.tenDuongDung}
                    </div>
                  );
                })}
              </td>

              <GioSuDungs
                mode={mode}
                thuoc={item}
                dsThoiGian={state.dsThoiGian}
                itemProps={itemProps}
              />
              <td className={`center mw-60 wrBrW`}>
                <div>{`${item.tongLieu}`}</div>
                <div>{getDonVi(item)}</div>
                {(item.dsThuocDungKem || []).map((x) => {
                  let soLuong = 0;
                  Object.keys(item.dsNgayYLenh).forEach((key) => {
                    (item.dsNgayYLenh[key][0].dsThuocDungKem || []).forEach(
                      (x1) => {
                        if (
                          x.dichVuId === x1.dichVuId &&
                          x.thoiGianThucHien2 === x1.thoiGianThucHien2
                        ) {
                          soLuong += x1.soLuong;
                        }
                      }
                    );
                  });
                  return (
                    <div className="mg-t-30">
                      <div>{soLuong}</div>
                      <div>{getDonVi(x)}</div>
                    </div>
                  );
                })}
              </td>
            </tr>
          );
        })}
      </ContextPhieuProvider>
    );
  }, [
    state.dsThuoc,
    formChange,
    state.dsNgayYLenh,
    state.dsThoiGian,
    configThoiGianBuoi,
  ]);

  return (
    <Main
      className="phieu-thuc-hien-cong-khai-thuoc-dktd"
      mode={mode}
      itemProps={itemProps}
      data-type="phieu-thuc-hien-cong-khai-thuoc-dktd"
    >
      <GlobalStyle />
      {mode === MODE.config && (
        <>
          <div className="table-config">
            <Button
              icon={<SettingOutlined />}
              onClick={handleFocus}
              size={"small"}
            />
          </div>
        </>
      )}

      <table className="table-1">
        <thead>
          <tr className="thead">
            <td rowSpan={2} className="stt" width={40}>
              STT
              {!state.showBoSung ? (
                <EyeInvisibleOutlined
                  className="icon-eye"
                  onClick={() => {
                    setState({ showBoSung: true });
                  }}
                />
              ) : (
                <EyeOutlined
                  className="icon-eye"
                  onClick={() => {
                    setState({ showBoSung: false });
                  }}
                />
              )}
            </td>
            <td rowSpan={2} width={220} className={"mw-220"}>
              Tên thuốc, hàm lượng
            </td>
            <td rowSpan={2}>Đơn vị</td>
            <td rowSpan={2}>Đường dùng</td>
            {(state.dsThoiGian || []).map((ngay, index) => (
              <td className="ngay" colSpan={ngay.data.length}>
                Ngày{" "}
                {moment(ngay.time, "YYYY-MM-DD").format("DD/MM/YYYY") || ""}
                <Tooltip title={"Thêm thời gian"}>
                  <SVG.IcAdd
                    onClick={onAddTuThoiGian({ indexNgay: index })}
                    className="icon-add"
                  />
                </Tooltip>
              </td>
            ))}

            <td rowSpan={2} width={60}>
              Tổng liều
            </td>
          </tr>
          <tr className="thead">
            {(state.dsThoiGian || []).map((item, index) => {
              return item.data.map((x, idx) => (
                <td key={`${index}-${idx}`} className="col-time gio">
                  <TimePicker
                    format={"HH:mm"}
                    placeholder="__:__"
                    clearIcon={null}
                    value={x?.thoiGian}
                    onChange={onChangeTuThoiGian({
                      stt: x.stt,
                      ngay: item.time,
                      indexNgay: index,
                      indexGio: idx,
                    })}
                  />

                  {x.stt > 4 && (
                    <Tooltip title={"Xóa thời gian"}>
                      <SVG.IcCancel
                        onClick={onRemoveTuThoiGian({
                          indexNgay: index,
                          indexGio: idx,
                        })}
                        className="icon-cancel"
                      />
                    </Tooltip>
                  )}
                </td>
              ));
            })}
          </tr>
        </thead>
        <tbody>
          {state.showBoSung && renderBoSung()}
          {renderTable}
          {renderFooter()}
        </tbody>
      </table>
    </Main>
  );
});

export default memo(ThucHienVaCongKhaiThuocBVDKKVTD);
