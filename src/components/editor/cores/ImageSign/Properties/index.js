import React, {
  useEffect,
  useImperativeHandle,
  forwardRef,
  useState,
  useMemo,
} from "react";
import T from "prop-types";
import { Main } from "./styled";
import {
  CHU_KY_BANG_KE,
  DS_LOAI_KY,
  FIELD_NAME,
  VI_TRI_CA,
} from "../constanst";
import { Col, InputNumber, Row, Select, Checkbox, Input } from "antd";
import {
  FontSizeConfig,
  EditorTool,
  AlignConfig,
} from "components/editor/config";
import { FontColorsOutlined } from "@ant-design/icons";
import { InputTimeout } from "components";
import { useTranslation } from "react-i18next";
import { useListAll, useStore } from "hooks";

const { PickColor } = EditorTool;
const ImageSignProp = forwardRef((props, ref) => {
  const { t } = useTranslation();
  const formInfo = useStore("config.formInfo", {});
  const [state, _setState] = useState({
    fontWeight: "",
    textTransform: "",
    fieldName: "",
    fontSize: "",
    alignCa: "left",
    colorCa: "#000",
    width: 100,
    height: 70,
    contentAlign: "left",
    disableIfSigned: true,
    allowReset: true,
    showPatientSign: true,
    viTri: 1,
    width: 200,
    height: 100,
    showTenVietTat: false,
  });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  const [listAllQuyenKy] = useListAll(
    "quyenKy",
    {
      active: true,
      size: "",
      page: "",
    },
    true
  );

  useImperativeHandle(ref, () => ({
    fontSize: state.fontSize,
    fontWeight: state.fontWeight,
    textTransform: state.textTransform,
    colorCa: state.colorCa,
    width: state.width || 200,
    height: state.height || 100,
    contentColor: state.contentColor,
    contentAlign: state.contentAlign,
    viTriCa: state.viTriCa,
    api: state.api,
    disableIfSigned: state.disableIfSigned,
    allowReset: state.allowReset,
    showCa: state.showCa,
    viTriAnhCa: state.viTriAnhCa,
    widthCa: state.widthCa,
    showPatientSign: state.showPatientSign,
    capKy: state.capKy,
    loaiKy: state.loaiKy,
    // isPatient: state.isPatient,
    chuKyBangKe: state.chuKyBangKe,
    keySoPhieu: state.keySoPhieu,
    textSignPatient: state.textSignPatient,
    canLeNguoiKy: state.canLeNguoiKy,
    canLeCa: state.canLeCa,
    viTri: state.viTri,
    showTenVietTat: state.showTenVietTat,
    anThoiGianKy: state.anThoiGianKy,
    huyTrinhKy: state.huyTrinhKy,
    kyAnhVanTay: state.kyAnhVanTay,
    anNhanTenVaThoiGian: state.anNhanTenVaThoiGian,
  }));

  useEffect(() => {
    if (props.state.key) {
      setState({
        fontSize: props.state.props.fontSize || 12,
        alignCa: props.state.props.alignCa,
        fontWeight: props.state.props.fontWeight,
        textTransform: props.state.props.textTransform,
        colorCa: props.state.props.colorCa,
        width: props.state.props.width || 200,
        height: props.state.props.height || 100,
        contentColor: props.state.props.contentColor,
        contentAlign: props.state.props.contentAlign,
        viTriCa: props.state.props.viTriCa,
        api: props.state.props.api,
        disableIfSigned: props.state.props.disableIfSigned,
        allowReset: props.state.props.allowReset,
        showCa: props.state.props.showCa,
        viTriAnhCa: props.state.props.viTriAnhCa,
        widthCa: props.state.props.widthCa,
        showPatientSign:
          props.state.props.showPatientSign ||
            props.state.props.showPatientSign === false
            ? props.state.props.showPatientSign
            : true,
        capKy:
          props.state.props.capKy ||
          props.state.props.capKySo ||
          props.state.props.levelSign,
        loaiKy: props.state.props.loaiKy,
        dsQuyenKy: props.state.props.dsQuyenKy,
        // isPatient: props.state.props.isPatient,
        chuKyBangKe: props.state.props.chuKyBangKe,
        keySoPhieu: props.state.props.keySoPhieu,
        textSignPatient: props.state.props.textSignPatient,
        canLeNguoiKy: props.state.props.canLeNguoiKy || "left",
        canLeCa: props.state.props.canLeCa || "left",
        viTri: props.state.props.viTri,
        showTenVietTat: props.state.props.showTenVietTat,
        anThoiGianKy: props.state.props.anThoiGianKy,
        huyTrinhKy: props.state.props.huyTrinhKy,
        kyAnhVanTay: props.state.props.kyAnhVanTay,
        anNhanTenVaThoiGian: props.state.props.anNhanTenVaThoiGian,
      });
    }
  }, [props.state]);
  const onChangeValue = (type) => (value) => {
    setState({
      [type]: value,
    });
  };
  const onChangeInput = (type) => (e) => {
    onChangeValue(type)(e.target.value);
    setState({
      [type]: e.target.value,
    });
  };
  const changeCheckbox = (target) => (e) => {
    setState({
      [target]: e.target.checked,
    });
  };

  const filterOption = (input = "", option) => {
    return containText(option?.props.label, input);
  };

  return (
    <Main>
      <Row gutter={[12, 12]}>
        {[
          "EMR_BA134",
          "EMR_BA106",
          "EMR_BA222",
          "EMR_BA251",
          "EMR_BA381",
          "EMR_BA386",
        ].includes(formInfo?.ma) && (
          <>
            <Col span={8}>
              <span>{t("editor.chuKyBangKe")}</span>
            </Col>
            <Col span={16}>
              <Select
                showSearch
                size={"small"}
                style={{ width: "100%" }}
                value={state.chuKyBangKe}
                onSelect={onChangeValue("chuKyBangKe")}
              >
                {CHU_KY_BANG_KE.map((item, index) => (
                  <Select.Option key={index} value={item.id}>
                    {t(item.ten)}
                  </Select.Option>
                ))}
              </Select>
            </Col>
          </>
        )}
        <PropertiesSign
          state={state}
          filterOption={filterOption}
          changeCheckbox={changeCheckbox}
          onChangeInput={onChangeInput}
          onChangeValue={onChangeValue}
        ></PropertiesSign>
      </Row>
    </Main>
  );
});

export const PropertiesSign = ({
  state,
  filterOption,
  changeCheckbox,
  onChangeInput,
  onChangeValue,
}) => {
  const { t } = useTranslation();
  return (
    <>
      <Col span={8}>
        <span>{t("editor.loaiKy")}</span>
      </Col>
      <Col span={16}>
        <Select
          showSearch
          size={"small"}
          style={{ width: "100%" }}
          value={state.loaiKy}
          onSelect={onChangeValue("loaiKy")}
        >
          {DS_LOAI_KY.map((item, index) => (
            <Select.Option key={index} value={item.id}>
              {t(item.ten)}
            </Select.Option>
          ))}
        </Select>
      </Col>

      {state.loaiKy === 2 ? (
        <>
          <Col span={8}>
            <span>{t("editor.tenChanKyNb")}:</span>
          </Col>
          <Col span={16}>
            <Input
              value={state.textSignPatient}
              onChange={onChangeInput("textSignPatient")}
            />
          </Col>
        </>
      ) : (
        <></>
      )}

      <Col span={8}>
        <span>{t("editor.capKy")}</span>
      </Col>
      <Col span={16}>
        <InputNumber
          type="number"
          size={"small"}
          min={1}
          value={state.capKy}
          onChange={onChangeValue("capKy")}
        ></InputNumber>
      </Col>
      <Col span={8}>
        <span>{t("editor.viTriKy")}</span>
      </Col>
      <Col span={16}>
        <InputNumber
          type="number"
          size={"small"}
          min={1}
          value={state.viTri}
          onChange={onChangeValue("viTri")}
        ></InputNumber>
      </Col>
      <Col span={8}>
        <span>{t("editor.rong")}</span>
      </Col>
      <Col span={16}>
        <InputNumber
          type="number"
          size={"small"}
          min={1}
          value={state.width}
          onChange={onChangeValue("width")}
        />
      </Col>
      <Col span={8}>
        <span>{t("editor.cao")}</span>
      </Col>
      <Col span={16}>
        <InputNumber
          type="number"
          size={"small"}
          min={1}
          value={state.height}
          onChange={onChangeValue("height")}
        />
      </Col>
      <Col span={8}>
        <span>{t("editor.canLeChuKy")}</span>
      </Col>
      <Col span={16}>
        <AlignConfig
          changeAlign={onChangeValue("contentAlign")}
          contentAlign={state.contentAlign}
        />
      </Col>
      <Col span={8}>
        <span>{t("editor.hienTenChuKyVietTat")}</span>
      </Col>
      <Col span={16}>
        <Checkbox
          checked={state.showTenVietTat}
          onChange={changeCheckbox("showTenVietTat")}
        />
      </Col>
      <Col span={8}>
        <span>{t("editor.hienThiNguoiKy")}</span>
      </Col>
      <Col span={16}>
        <Checkbox
          checked={state.showPatientSign}
          onChange={changeCheckbox("showPatientSign")}
        />
      </Col>

      {state.showPatientSign && (
        <>
          <Col span={8}>
            <span>{t("editor.anNhanTenNguoiKyVaThoiGian")}</span>
          </Col>
          <Col span={16}>
            <Checkbox
              checked={state.anNhanTenVaThoiGian}
              onChange={changeCheckbox("anNhanTenVaThoiGian")}
            />
          </Col>
          <Col span={8}>
            <span>{t("editor.anThoiGianKy")}</span>
          </Col>
          <Col span={16}>
            <Checkbox
              checked={state.anThoiGianKy}
              onChange={changeCheckbox("anThoiGianKy")}
            />
          </Col>
          <Col span={8}>
            <span>{t("editor.viTriNguoiKy")}</span>
          </Col>
          <Col span={16}>
            <Select
              showSearch
              size={"small"}
              style={{ width: "100%" }}
              value={state.viTriCa}
              onSelect={onChangeValue("viTriCa")}
            >
              {VI_TRI_CA.map((item, index) => (
                <Select.Option key={index} value={item.id}>
                  {t(item.ten)}
                </Select.Option>
              ))}
            </Select>
          </Col>
          <Col span={8}>
            <span>{t("editor.canLeNguoiKy")}</span>
          </Col>
          <Col span={16}>
            <AlignConfig
              changeAlign={onChangeValue("canLeNguoiKy")}
              contentAlign={state.canLeNguoiKy}
            />
          </Col>
          <Col span={8}>
            <span>{t("editor.mauChuNguoiKy")}</span>
          </Col>
          <Col span={16}>
            <PickColor
              iconComponent={FontColorsOutlined}
              title={t("editor.chonMauChu")}
              dataColor={state.contentColor || "black"}
              changeColor={onChangeValue("contentColor")}
            />
          </Col>
          <Col span={8}>
            <span>{t("editor.coChuNguoiKy")}</span>
          </Col>
          <Col span={16}>
            <FontSizeConfig
              changeFont={onChangeValue("fontSize")}
              fontSize={state.fontSize}
            />
          </Col>
        </>
      )}

      <Col span={8}>
        <span>{t("editor.hienThiCA")}</span>
      </Col>
      <Col span={16}>
        <Checkbox checked={state.showCa} onChange={changeCheckbox("showCa")} />
      </Col>
      {state.showCa && (
        <>
          <Col span={8}>
            <span>{t("editor.viTriCA")}</span>
          </Col>
          <Col span={16}>
            <Select
              showSearch
              size={"small"}
              style={{ width: "100%" }}
              value={state.viTriAnhCa}
              onSelect={onChangeValue("viTriAnhCa")}
            >
              {VI_TRI_CA.map((item, index) => (
                <Select.Option key={index} value={item.id}>
                  {t(item.ten)}
                </Select.Option>
              ))}
            </Select>
          </Col>
          <Col span={8}>
            <span>{t("editor.canLeCa")}</span>
          </Col>
          <Col span={16}>
            <AlignConfig
              changeAlign={onChangeValue("canLeCa")}
              contentAlign={state.canLeCa}
            />
          </Col>
          <Col span={8}>
            <span>{t("editor.doRongCA")}</span>
          </Col>
          <Col span={16}>
            <InputNumber
              type="number"
              size={"small"}
              min={1}
              value={state.widthCa}
              onChange={onChangeValue("widthCa")}
            ></InputNumber>
          </Col>
        </>
      )}
      <Col span={8}>
        <span>{"API: "}</span>
      </Col>
      <Col span={16}>
        <InputTimeout
          style={{ width: "100%" }}
          size={"small"}
          value={state.api}
          onChange={onChangeValue("api")}
        />
      </Col>
      <Col span={8}>
        <span>{t("editor.truongSoPhieu")}</span>
      </Col>
      <Col span={16}>
        <InputTimeout
          style={{ width: "100%" }}
          size={"small"}
          value={state.keySoPhieu}
          onChange={onChangeValue("keySoPhieu")}
        />
      </Col>
      <Col span={8}>
        <span>{t("editor.khoaBieuMauSauKhiKy")}</span>
      </Col>
      <Col span={16}>
        <Checkbox
          checked={state.disableIfSigned}
          onChange={changeCheckbox("disableIfSigned")}
        />
      </Col>

      <Col span={8}>
        <span>{t("editor.choPhepHuyKhiDaKy")}</span>
      </Col>
      <Col span={16}>
        <Checkbox
          checked={state.allowReset}
          onChange={changeCheckbox("allowReset")}
        />
      </Col>
      <Col span={8}>
        <span>{t("editor.choPhepHuyTrinhKy")}</span>
      </Col>
      <Col span={16}>
        <Checkbox
          checked={state.huyTrinhKy}
          onChange={changeCheckbox("huyTrinhKy")}
        />
      </Col>
      <Col span={8}>
        <span>{t("editor.kyAnhVanTay")}</span>
      </Col>
      <Col span={16}>
        <Checkbox
          checked={state.kyAnhVanTay}
          onChange={changeCheckbox("kyAnhVanTay")}
        />
      </Col>
    </>
  );
};

ImageSignProp.propTypes = {
  state: T.shape({}),
};

export default ImageSignProp;
