import React, { useMemo } from "react";
import { Col, Row } from "antd";
import { DeboundInput } from "module_html_editor/Components";
import CheckGroups from "../../CheckGroups";
import Barcode from "../../Barcode";
import { Main } from "./styled";
import { MODE } from "utils/editor-utils";
import Image from "../../Image";
import { useTranslation } from "react-i18next";
import { useListAll } from "hooks";

const HeaderFormChamSocC2 = ({
  form,
  mode,
  formChange,
  tableIndex,
  itemProps,
  showHeader,
}) => {
  const { t } = useTranslation();
  const [listAllKhoa] = useListAll("khoa", {}, true);

  const tenKhoaChiDinh = useMemo(() => {
    return listAllKhoa.find((x) => x.id == form?.khoaChiDinhId)?.ten || "";
  }, [listAllKhoa, form?.khoaChiDinhId]);

  const renderHeaderForm = useMemo(() => {
    switch (itemProps?.templateHeaderForm) {
      case 2:
        return (
          <div className="form-header flex" style={{ lineHeight: 1 }}>
            <div span={11} style={{ flex: 1, lineHeight: 1.3 }}>
              <div className="header-left">
                <div className="logo">
                  <Image
                    component={{
                      props: {
                        isLogo: true,
                        width: 60,
                        height: 60,
                      },
                    }}
                    mode={mode}
                    form={form}
                  ></Image>
                </div>
                <div className="left-content">
                  <div className="left">
                    {mode === MODE.config ? "Tiêu đề trái 1" : form.tieuDeTrai1}
                  </div>
                  <div className="left bold green">
                    <b>
                      {mode === MODE.config
                        ? "Tiêu đề trái 2"
                        : form.tieuDeTrai2}
                    </b>
                  </div>
                  <div className="header-level-1 blue">
                    <b>
                      {mode === MODE.config
                        ? "Tên khoa chỉ định"
                        : tenKhoaChiDinh}
                    </b>
                  </div>
                </div>
              </div>
              <div className="green left">
                <i> ​(Theo MS: 38/BV02, Thông tư số 32/2023/TT-BYT)</i>
              </div>
            </div>

            <div className="header-right" span={5}>
              <Barcode
                component={{
                  props: {
                    label: "Mã HS",
                    width: 170,
                    height: 35,
                    contentAlign: "left",
                    fieldName: "maHoSo",
                    noLabel: true,
                  },
                }}
                mode={mode}
                form={form}
              ></Barcode>
              <div>Mã HS: {form.maHoSo}</div>
              <div>Mã BA: {form.maBenhAn}</div>
              <div>Mã NB: {form.maNb}</div>
            </div>
          </div>
        );
      default:
        return (
          <>
            <Row className="form-header flex">
              <Col span={11}>
                <div className="header-left">
                  <div className="logo">
                    <Image
                      component={{
                        props: {
                          isLogo: true,
                          width: 60,
                          height: 60,
                        },
                      }}
                      mode={mode}
                      form={form}
                    ></Image>
                  </div>
                  <div className="left-content">
                    <div className="left">
                      {mode === MODE.config
                        ? "Tiêu đề trái 1"
                        : form.tieuDeTrai1}
                    </div>
                    <div className="left bold green">
                      <b>
                        {mode === MODE.config
                          ? "Tiêu đề trái 2"
                          : form.tieuDeTrai2}
                      </b>
                    </div>
                    <div className="header-level-1 blue">
                      <b>
                        {mode === MODE.config
                          ? "Tên khoa chỉ định"
                          : tenKhoaChiDinh}
                      </b>
                    </div>
                  </div>
                </div>
              </Col>
              <Col span={8} className="title">
                <Barcode
                  component={{
                    props: {
                      label: "Mã HS",
                      width: 200,
                      height: 45,
                      contentAlign: "left",
                      fieldName: "maHoSo",
                    },
                  }}
                  mode={mode}
                  form={form}
                ></Barcode>
              </Col>
              <Col className="header-right" span={5}>
                <div>Mã BA: {form.maBenhAn}</div>
                <div>Mã NB: {form.maNb}</div>
              </Col>
            </Row>
            <Row>
              <Col span={24} className="green right">
                <i> ​(Theo MS: 38/BV02, Thông tư số 32/2023/TT-BYT)</i>
              </Col>
            </Row>
          </>
        );
    }
  }, [itemProps?.templateHeaderForm, form, mode, tenKhoaChiDinh]);

  return (
    <Main tableIndex={tableIndex} showHeader={showHeader}>
      {renderHeaderForm}
      <Row>
        <Col span={24} className="flex-center column bold title">
          <div>
            {itemProps?.titleForm || "PHIẾU THEO DÕI VÀ CHĂM SÓC CẤP 1"}
          </div>
        </Col>
      </Row>
      <Row>
        <Col span={24} className="flex-center">
          Tờ số: {tableIndex + 1}
        </Col>
      </Row>
      <Row>
        <Col span={12}>
          Họ và tên NB: <b className="blue">{form.tenNb}</b>
        </Col>
        <Col span={4}>
          Tuổi: <span className="blue">{form.tuoi || form.tuoi2}</span>
        </Col>
        <Col span={3}>
          <CheckGroups
            className="flex gap-16"
            component={{
              props: {
                direction: "rtl",
                type: "onlyOne",
                checkList: [
                  {
                    label: "Giới tính: Nam",
                    value: 1,
                  },
                  {
                    label: "Nữ",
                    value: 2,
                  },
                ],
                fieldName: "gioiTinh",
                readOnly: true,
              },
            }}
            mode={mode}
            form={form}
            formChange={formChange}
          ></CheckGroups>
        </Col>
        <Col span={5}>
          <div style={{ display: "flex" }}>
            <DeboundInput
              label="Cân nặng: "
              size={"small"}
              value={form?.canNang || ""}
              onChange={(value) => formChange["canNang"](value)}
              type="multipleline"
              lineHeightText={1.5}
              fontSize={12}
              minHeight={24}
              styleMain={{ width: 120 }}
              contentColor="blue"
            ></DeboundInput>
            <DeboundInput
              label="Chiều cao: "
              size={"small"}
              value={form?.chieuCao || ""}
              onChange={(value) => formChange["chieuCao"](value)}
              type="multipleline"
              lineHeightText={1.5}
              fontSize={12}
              minHeight={24}
              styleMain={{ width: 120 }}
              contentColor="blue"
            ></DeboundInput>
            <DeboundInput
              label="BMI: "
              size={"small"}
              value={form?.bmi || ""}
              type="multipleline"
              lineHeightText={1.5}
              fontSize={12}
              minHeight={24}
              styleMain={{ width: 120 }}
              contentColor="blue"
            ></DeboundInput>
          </div>
        </Col>
      </Row>
      <Row>
        <Col span={12}>
          Phòng: <span className="blue">{form?.tenPhong}</span>
        </Col>
        <Col span={12}>
          Giường: <span className="blue">{form.tenDvGiuong}</span>
        </Col>
      </Row>
      <Row>
        <Col span={24}>
          Chẩn đoán:{" "}
          <span className="blue">
            {form?.cdChinh}
            {form.moTa ? `${" "}(${form.moTa})` : ""}
          </span>
        </Col>
      </Row>
      <Row>
        <Col span={24} className="flex">
          <div>
            <span>Tiền sử dị ứng:&nbsp;</span>
          </div>
          <CheckGroups
            component={{
              props: {
                direction: "ltr",
                type: "onlyOne",
                checkList: [
                  {
                    label: "Chưa ghi nhận &nbsp;",
                    value: 1,
                  },
                  {
                    label: "Có,",
                    value: 2,
                  },
                ],
                fieldName: "diUng",
              },
            }}
            mode={mode}
            form={form}
            formChange={formChange}
            className="mr-5"
          ></CheckGroups>
          <DeboundInput
            //   disabled={disabled}
            size={"small"}
            value={form?.ghiRo || ""}
            onChange={(value) => formChange["ghiRo"](value)}
            type="multipleline"
            lineHeightText={1.5}
            fontSize={12}
            minHeight={24}
            styleMain={{ flex: 1 }}
            label="&nbsp;ghi rõ:&nbsp;"
            contentColor="blue"
          ></DeboundInput>
        </Col>
      </Row>
      <div className="flex flex-a-center gap-16">
        <span className="flex flex-a-center gap-5">
          <span
            style={{
              marginTop: "-3px",
              color: "#E74C3C",
              fontWeight: "bold",
              fontSize: 18,
              lineHeight: 1,
            }}
          >
            x
          </span>
          <span className="radio-content">
            {t("quanLyNoiTru.chiSoSong.nhipMach")}
          </span>
        </span>
        <span className="flex flex-a-center gap-5">
          <span
            style={{
              width: 10,
              height: 10,
              backgroundColor: "#3498DB",
              marginHorizontal: 5,
              borderRadius: "50%",
            }}
          ></span>
          <span className="radio-content gap-5">
            {t("quanLyNoiTru.chiSoSong.nhietDo")}
          </span>
        </span>
        <span className="flex flex-a-center gap-5">
          <span
            style={{
              color: "#5cffbe",
            }}
          >
            ♦
          </span>
          <span className="radio-content gap-5">
            {t("quanLyNoiTru.chiSoSong.spo2")}
          </span>
        </span>
        <span className="flex flex-a-center gap-5">
          <span
            style={{
              color: "#7C4DFF",
            }}
          >
            ★
          </span>
          <span className="radio-content gap-5">
            {t("quanLyNoiTru.chiSoSong.huyetApTrungBinh")}
          </span>
        </span>

        <span className="flex flex-a-center gap-5">
          <span
            style={{
              width: 10,
              height: 10,
              marginHorizontal: 5,
            }}
          />
          <img
            src={require("components/VitalSigns/images/huyet_ap.png")}
            width={10}
            style={{
              marginHorizontal: 5,
              marginRight: 5,
            }}
            alt=""
          />
          <span>{t("quanLyNoiTru.chiSoSong.huyetAp")}</span>
        </span>
      </div>
    </Main>
  );
};

HeaderFormChamSocC2.propTypes = {};

export default HeaderFormChamSocC2;
