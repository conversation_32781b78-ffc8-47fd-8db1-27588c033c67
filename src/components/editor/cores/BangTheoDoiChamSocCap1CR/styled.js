import styled, { createGlobalStyle } from "styled-components";

const Main = styled("div")`
  position: relative;
  width: 100vw;
  @media print {
    .btn-add-khung {
      display: none;
    }
    @page {
      margin: 2mm;
    }
  }
  & .table-config {
    position: absolute;
    z-index: 2;
    top: -24px;
    left: 0px;
    right: 0px;
    display: none;
  }

  &:hover {
    & .table-config {
      display: block;
    }
    & .table-bar {
      display: block;
    }
  }

  & table {
    border: solid 1px;
    & .col-selected {
      background-color: #e6f7ff;
    }

    & td {
      vertical-align: top;
      border-right: 1px solid #000;
      position: relative;
      padding: ${(props) => (props.mode === "config" ? "0" : "")};

      &:first-child {
        border-left: 1px solid #000;
      }
      .hint {
        text-align: center;

        > span {
          margin-right: 10px;
          display: inline-table;
          /* white-space: nowrap; */
        }
      }
      .hint-with-number {
        width: unset;
        max-width: unset;
        display: flex;
        flex-wrap: wrap;
        text-align: left;
        span {
          width: calc(33%);
          margin-right: 0px;
        }
      }
    }

    & tr {
      border-top: 1px solid #000;
      &:first-child {
        border-top: 0;
      }
    }
  }
  .box {
    width: 16px;
    height: 16px;
    border: 1px solid #000;
    margin-right: 5px;
  }
  .flex-center {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .controls-container {
    display: flex;
    align-items: center;
    gap: 12px;
    border-radius: 8px;
    margin: 10px 0;
    @media print {
      display: none;
    }
  }
`;

const GlobalStyle = createGlobalStyle`
    html.browser .form-content {
        width: calc(100vw - 75px) !important;
        & > div {
          max-width: 100%;
        }
      }
    html.print-pdf .form-content
    {
        width: 2370px;
        & > div {
            max-width: 100%;
            width: 100%;
            & [data-type="cham-soc-cap-1"]{
              width: 100% !important;
            }
            [data-type="block"][data-level="1"], [data-type="block"][data-level="2"]{
              width: 100% !important;
            }
        }    
    }
  `;

const PopoverStyled = styled.div`
  .active {
    background: #7b92ac;
    color: #fff;
  }
  .item {
    min-width: 100px;
    border-radius: 8px;
    text-align: left;
    margin: 5px;
    display: flex;
    padding: 2px 5px;
    :hover {
      cursor: pointer;
    }
  }
  .icon-setting {
    margin-left: 10px;
    @media print {
      display: none;
    }
  }
`;

export { Main, GlobalStyle, PopoverStyled };
