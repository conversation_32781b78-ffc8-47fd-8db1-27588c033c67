import React, {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from "react";
import { useDispatch } from "react-redux";
import T from "prop-types";
import { Button, message, Pagination } from "antd";
import { Main, GlobalStyle } from "./styled";
import BangTheoDoi from "./BangTheoDoi";
import { SettingOutlined } from "@ant-design/icons";
import ModalNhapChiSo from "../BangTheoDoiBenhNhanGMHS2/components/ModalNhapChiSo";
import { PlusOutlined } from "@ant-design/icons";
import { cloneDeep, get } from "lodash";
import { useTranslation } from "react-i18next";
import moment from "moment";
import { useQueryString } from "hooks";

import { useTableTabIndex } from "./useTableTabIndex";

const DATA_DEFAULT = new Array(24).fill({}).map((item) => {
  return cloneDeep({});
});
const PAGE_SIZE = 5; // <PERSON><PERSON> khung mỗi trang, có thể cho chỉnh nếu muốn

const BangTheoDoiChamSocCap1CR = forwardRef((props, ref) => {
  const [nbDotDieuTriId] = useQueryString("nbDotDieuTriId");

  const { component, mode, formId, form, formChange, block } = props;
  const init = useDispatch().component.init;
  const { t } = useTranslation();
  const itemProps = component.props || {};
  if (!itemProps.soLuongCot) {
    itemProps.soLuongCot = 24;
  } else {
    itemProps.soLuongCot = +itemProps.soLuongCot;
  }
  const refModalNhapChiSo = useRef();
  const refAllKhung = useRef([]);

  const arr = useMemo(() => {
    return new Array(itemProps.soLuongCot ? +itemProps.soLuongCot : 24)
      .fill({})
      .map((item) => {
        return cloneDeep({});
      });
  }, [itemProps.soLuongCot]);

  const [state, _setState] = useState({
    dsTheoDoi: [
      {
        dsChiTiet: DATA_DEFAULT,
        ngayThucHien: moment().format("YYYY-MM-DD"),
      },
    ],
    size: PAGE_SIZE,
    currentPage: 1, // Thêm state cho trang hiện tại
  });
  useTableTabIndex(state.dsTheoDoi);

  const [dataCopy, setDataCopy] = useState();

  const refValuesAll = useRef();

  const setState = (data = {}) => {
    _setState((state) => {
      return {
        ...state,
        ...data,
      };
    });
  };

  const handleFocus = () => {
    if (mode === "config") {
      init(component);
    }
  };

  useEffect(() => {
    refValuesAll.current = cloneDeep(
      form.dsTheoDoi || [
        {
          dsChiTiet: DATA_DEFAULT,
        },
      ]
    );
    refValuesAll.current.forEach((el, index) => {
      el.dsChiTiet = new Array(24).fill({}).map((chiTiet, idx) => {
        return get(el.dsChiTiet, `[${idx}]`) || cloneDeep({});
      });
      el.dat14ThongSo = new Array(14).fill({}).map((item, idx) => {
        return get(el.dat14ThongSo, `[${idx}]`, 0);
      });
      (el.dsChiTiet || []).forEach((chiTiet, idx) => {
        let isExitsIdChiSoSong = false;
        if (chiTiet?.chiSoSong && !chiTiet?.chiSoSong?.nbDotDieuTriId) {
          chiTiet.chiSoSong.nbDotDieuTriId = nbDotDieuTriId;
        }
        if (chiTiet?.chiSoSong && !chiTiet?.chiSoSong?.khoaChiDinhId) {
          chiTiet.chiSoSong.khoaChiDinhId = form.khoaChiDinhId;
        }

        if (
          chiTiet?.chiSoSong &&
          !chiTiet?.chiSoSong?.chiDinhTuLoaiDichVu &&
          !chiTiet?.chiSoSong?.id
        ) {
          chiTiet.chiSoSong.chiDinhTuLoaiDichVu = 201;
        }
        for (let x = 0; x < idx; x++) {
          if (
            chiTiet?.chiSoSong?.id &&
            chiTiet?.chiSoSong?.id === el.dsChiTiet[x]?.chiSoSong?.id
          ) {
            isExitsIdChiSoSong = true;
          }

          if (chiTiet?.chiSoSong && isExitsIdChiSoSong) {
            chiTiet.chiSoSong.id = null;
          }
        }
        if (chiTiet?.chiSoSong?.thoiGianThucHien && el?.ngayThucHien) {
          if (
            moment(chiTiet?.chiSoSong?.thoiGianThucHien).format(
              "YYYY-MM-DD"
            ) !== el?.ngayThucHien
          ) {
            const years = el?.ngayThucHien.split("-")[0];
            const months = el?.ngayThucHien.split("-")[1] - 1;
            const dates = el?.ngayThucHien.split("-")[2];
            chiTiet.chiSoSong.thoiGianThucHien = moment(
              chiTiet.chiSoSong?.thoiGianThucHien
            ).set({
              years,
              months,
              dates,
            });
          }
        }
        if (!chiTiet?.ongThongTmTrungTam) {
          chiTiet.ongThongTmTrungTam = [
            {
              ongThong: chiTiet.ongThongTm || "",
              viTri: chiTiet.viTriOngThongTm || "",
              viTriKhac: chiTiet.viTriOngThongTmKhac || "",
              tinhTrangHoatDong: chiTiet?.tinhTrangHoatDong || "",
              tinhTrangDaOngThongTm: chiTiet?.tinhTrangDaOngThongTm || "",
              ngayDat: chiTiet.ngayDatOngThongTm,
              ngayRut: chiTiet.ngayRutOngThongTm,
              ongThongKhac: chiTiet.ongThongTmKhac,
            },
          ];
        }
        if (!chiTiet?.ongThongDongMach) {
          chiTiet.ongThongDongMach = [
            {
              ongThong: chiTiet.ongThongDm,
              viTri: chiTiet.viTriOngThongDm,
              viTriKhac: chiTiet.viTriOngThongDmKhac,
              tinhTrangHoatDong: chiTiet.tinhTrangOngThongDm,
              tinhTrangDaOngThongTm: chiTiet.tinhTrangDaOngThongDm,
              ngayDat: chiTiet.ngayDatOngThongDm,
              ngayRut: chiTiet.ngayRutOngThongDm,
              ongThongKhac: chiTiet.ongThongDmKhac,
            },
          ];
        }
        if (!chiTiet?.ongThongTmNgoaiVi) {
          chiTiet.ongThongTmNgoaiVi = [
            {
              ongThong: chiTiet.ongThongTmNv,
              viTri: null,
              tinhTrangHoatDong: chiTiet.tinhTrangOngThongTmNv,
              tinhTrangHoatDongDm: chiTiet.tinhTrangDaNoi,
              danhGiaMucDoViem: chiTiet.diemVip,
              ngayDat: chiTiet.ngayDatOngThongTmNv,
              ngayRut: chiTiet.ngayRutOngThongTmNv,
              ongThongKhac: chiTiet.ongThongTmNvKhac,
            },
          ];
        }
        chiTiet.thoiGianThucHien =
          chiTiet?.chiSoSong?.thoiGianThucHien || chiTiet.thoiGianThucHien;
      });
    });
    formChange?.dsTheoDoi && formChange.dsTheoDoi(refValuesAll.current);

    refAllKhung.current = refValuesAll.current;
    const dsTheoDoi = refAllKhung.current.slice(0, state.size);
    setState({
      dsTheoDoi: dsTheoDoi,
    });
  }, [JSON.stringify(form || {}), Object.keys(formChange || {}).length]);

  const onAddKhung = useCallback(() => {
    refValuesAll.current.push({
      dsChiTiet: DATA_DEFAULT.map((el) => ({
        ...el,
        khoaChiDinhId: form.khoaChiDinhId,
        nbDotDieuTriId: form.nbDotDieuTriId,
        dat14ThongSo: new Array(14).fill(0),
      })),
    });
    setState({
      dsTheoDoi: cloneDeep(refValuesAll.current),
      currentPage: 1, // Chuyển về page đầu tiên
    });
    refAllKhung.current = refValuesAll.current;

    formChange["dsTheoDoi"](refValuesAll.current);
  }, [state.dsTheoDoi, formChange]);

  const checkSign = (position) => {
    let dsViTriKy = [];
    const chanKyBatDat = position * 24;
    const chanKyKetThuc = chanKyBatDat + 23;
    for (let index = chanKyBatDat; index < chanKyKetThuc; index++) {
      dsViTriKy.push(index);
    }
    return (form.lichSuKy?.dsChuKy || []).some((el) => {
      return dsViTriKy.includes(el.viTri);
    });
  };

  const removeTable = (index) => () => {
    if (checkSign(index)) {
      message.error("Bảng đã được ký xin vui lòng hủy ký trước khi xóa bảng!");
      return;
    }
    refConfirm.current &&
      refConfirm.current.show(
        {
          title: t("common.thongBao"),
          content: `${t("common.banCoChacMuonXoa")}`,
          cancelText: t("common.huy"),
          okText: t("common.dongY"),
          classNameOkText: "button-warning",
          showBtnOk: true,
          typeModal: "warning",
        },
        () => {
          refAllKhung.current = refAllKhung.current.filter(
            (item, idx) => idx !== index
          );
          refValuesAll.current = refAllKhung.current;
          const dsTheoDoi = refValuesAll.current.slice(0, state.size);

          setState({
            dsTheoDoi,
          });
          formChange["dsTheoDoi"](refAllKhung.current);
        }
      );
  };

  // Tính toán các khung sẽ hiển thị dựa trên trang hiện tại
  const pagedTheoDoi = useMemo(() => {
    const start = (state.currentPage - 1) * state.size;
    const end = start + state.size;
    return refAllKhung.current.slice(start, end);
  }, [state.currentPage, state.size, refAllKhung.current, state.dsTheoDoi]);

  // Khi thay đổi trang
  const handlePageChange = (page) => {
    setState({ currentPage: page });
  };

  const handleSizeChange = (page, size) => {
    setState({ size });
  };

  return (
    <Main data-type="cham-soc-cap-1" mode={mode} itemProps={itemProps}>
      <GlobalStyle />
      {mode === "config" && (
        <>
          <div className="table-config">
            <Button
              icon={<SettingOutlined />}
              onClick={handleFocus}
              size={"small"}
            />
          </div>
        </>
      )}
      {pagedTheoDoi.map((item, index) => {
        return (
          <BangTheoDoi
            key={index + (state.currentPage - 1) * state.size}
            showHeader={index == 0}
            {...{
              itemProps,
              component,
              disable: props.disable,
              mode,
              formId,
              form,
              formChange,
              block,
              values: item,
              indexTable: index + (state.currentPage - 1) * state.size,
              refValuesAll,
              refModalNhapChiSo,
              removeTable,
              dataCopy,
              setDataCopy,
              arr,
            }}
          />
        );
      })}

      <div className="controls-container">
        <Pagination
          listData={refAllKhung.current}
          current={state.currentPage}
          pageSize={state.size}
          total={refAllKhung.current.length}
          onChange={handlePageChange}
          onShowSizeChange={handleSizeChange}
          showSizeChanger={true}
          showQuickJumper
          showTotal={(total, range) =>
            `${range[0]}-${range[1]} của ${total} bảng`
          }
          pageSizeOptions={[5, 10, 20, 50, 100]}
        />
      </div>

      <ModalNhapChiSo ref={refModalNhapChiSo} />

      <Button
        icon={<PlusOutlined />}
        size="small"
        onClick={onAddKhung}
        className="btn-add-khung"
      />
    </Main>
  );
});

BangTheoDoiChamSocCap1CR.defaultProps = {
  component: {},
  form: {},
};

BangTheoDoiChamSocCap1CR.propTypes = {
  component: T.shape({}),
  form: T.shape({}),
};

export default BangTheoDoiChamSocCap1CR;
