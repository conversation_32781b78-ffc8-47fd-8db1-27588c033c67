import React, {
  forwardRef,
  memo,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { useDispatch } from "react-redux";
import T from "prop-types";
import { Button } from "antd";
import { SettingOutlined } from "@ant-design/icons";
import { MODE } from "utils/editor-utils";
import { get, groupBy } from "lodash";
import { Main } from "./styled";
import { DeboundInput } from "components/editor/config";

const LIST_KEY = [
  {
    key: "tim",
  },
  {
    key: "tho",
  },
  {
    key: "mauSac",
  },
  {
    key: "truongLuc",
  },
  {
    key: "phanXa",
  },
];

const BangApgar = forwardRef((props, ref) => {
  const { component, mode, form, formChange } = props;
  const itemProps = component.props || {};
  const init = useDispatch().component.init;
  const refData = useRef();
  const [data, setData] = useState([]);
  const [tong, setTong] = useState({
    10: 0,
    20: 0,
    30: 0,
  });

  const handleFocus = () => {
    if (mode === MODE.config) {
      init(component);
    }
  };

  useEffect(() => {
    if (mode === MODE.config) {
      setData([]);
    } else {
      const data = itemProps?.phieuTongKetKhoaDe
        ? form.thongTinSanPhu_dsThongTinCon_dsApgar
        : form.thongTinCuocSinh_dsApgar;
      const dsApgar = [{ loai: 10 }, { loai: 20 }, { loai: 30 }].map((el) => {
        const item = (data || []).find((i) => i.loai == el.loai) || {};
        return {
          ...item,
          ...el,
        };
      });
      refData.current = dsApgar;
      setTong({
        10: dsApgar[0].tongCong,
        20: dsApgar[1].tongCong,
        30: dsApgar[2]?.tongCong,
      });
      setData(dsApgar);
    }
  }, [form, formChange]);
  const onChangeValue = useCallback(() => {
    const listTong = refData.current.reduce((a, b) => {
      return {
        ...a,
        [b.loai]: b.tongCong || 0,
      };
    }, {});
    setTong(listTong);
  }, []);
  return (
    <>
      {mode === MODE.config && (
        <>
          <div className="table-config">
            <Button
              icon={<SettingOutlined />}
              onClick={handleFocus}
              size={"small"}
            />
          </div>
        </>
      )}
      <Main hienThiVienBang={itemProps?.hienThiVienBang}>
        <table>
          <thead>
            <tr>
              <th>APGAR</th>
              <th>Tim</th>
              <th>Thở</th>
              <th>Màu sắc da</th>
              <th>Trương lực cơ</th>
              <th>Phản xạ</th>
              <th>Tổng số</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>1 phút</td>
              {LIST_KEY.map((td, index) => {
                return (
                  <RenderTd
                    key={index}
                    td={td}
                    loai={10}
                    data={data}
                    formChange={formChange}
                    onChangeValue={onChangeValue}
                  ></RenderTd>
                );
              })}
              <td className="text-center">{tong[10] || 0}</td>
            </tr>
            <tr>
              <td>5 phút</td>
              {LIST_KEY.map((td, index) => {
                return (
                  <RenderTd
                    key={index}
                    td={td}
                    loai={20}
                    data={data}
                    formChange={formChange}
                    onChangeValue={onChangeValue}
                  ></RenderTd>
                );
              })}
              <td className="text-center">{tong[20] || 0}</td>
            </tr>
            <tr>
              <td>10 phút</td>
              {LIST_KEY.map((td, index) => {
                return (
                  <RenderTd
                    key={index}
                    td={td}
                    loai={30}
                    data={data}
                    formChange={formChange}
                    onChangeValue={onChangeValue}
                  ></RenderTd>
                );
              })}
              <td className="text-center">{tong[30] || 0}</td>
            </tr>
          </tbody>
        </table>
      </Main>
    </>
  );
});

const RenderTd = memo(({ td, loai, data, formChange, onChangeValue }) => {
  const dataGroupBy = useMemo(() => {
    const res = groupBy(data, "loai");
    Object.keys(res).forEach((key) => {
      res[key] = res[key][0];
    });
    return res;
  }, [data]);

  const onChange = (idx, key) => (value) => {
    dataGroupBy[idx][key] = value;
    const tongCong = Object.keys(dataGroupBy[idx] || {}).reduce((a, b) => {
      return ["tongCong", "loai"].includes(b)
        ? a + 0
        : a + (dataGroupBy[idx][b] ? +dataGroupBy[idx][b] : 0);
    }, 0);
    dataGroupBy[idx]["tongCong"] = tongCong;
    formChange["thongTinCuocSinh_dsApgar"] &&
      formChange["thongTinCuocSinh_dsApgar"](data);
    onChangeValue();
  };

  return (
    <td width={90}>
      <DeboundInput
        label=""
        value={get(dataGroupBy, `[${loai}].${td.key}`)}
        onChange={onChange(loai, td.key)}
        type="number"
        lineHeightText={1.5}
        fontSize={9}
        minHeight={9 + 6}
        min={0}
        max={2}
        markSpanRow={false}
        inputNumber={true}
        contentAlign="center"
      />
    </td>
  );
});

BangApgar.defaultProps = {
  component: {},
  form: {},
};

BangApgar.propTypes = {
  component: T.shape({}),
  form: T.shape({}),
};

export default memo(BangApgar);
