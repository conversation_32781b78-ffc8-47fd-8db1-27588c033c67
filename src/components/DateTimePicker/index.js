import React, {
  memo,
  useState,
  useRef,
  useEffect,
  forwardRef,
  useImperativeHandle,
  useMemo,
} from "react";
import moment from "moment";
import { Main } from "./styled";
import { Input } from "antd";
import { DatePicker } from "components";
import { CalendarOutlined } from "@ant-design/icons";
import { useTranslation } from "react-i18next";
const { RangePicker } = DatePicker;

const range = (start, end) => {
  return Array(end - start + 1)
    .fill()
    .map((_, i) => start + i);
};

const DateTimePicker = memo(
  forwardRef(({ dropdownClassName, popupClassName, ...props }, ref) => {
    const { t } = useTranslation();
    const refDatepicker = useRef(null);
    const {
      value,
      disabled,
      placeholder = t("common.chonThoiGian"),
      className,
      allowClear,
      onChange,
      onSelect,
      showIcon = true,
      bottom = <></>,
      format = "DD/MM/YYYY HH:mm:ss",
      showTime = true,
      disabledDate,

      // disabled giờ theo ngày. disable các chọn giờ trước hoặc sau nếu chọn ngày = ngày của pointTime
      // kết hợp với compareTime để disable các ngày ko thoả mãn
      pointTime,
      compareTime = () => true,
      style,
      isDateCheckInput,
      defaultValue,
      onOk,
      showInput = true,
      isCss,
      formatDefaultTime,
      isBefore = true,
      disableOnblur,
      tabIndex,
      afterOpenChange,
      refElement,
      checkIsSameDate,
      disabledTimeDate,
      ...rest
    } = props;
    useEffect(() => {
      if (dropdownClassName) {
        console.log(
          "[antd: DatePicker] `dropdownClassName` is deprecated which will be removed in next major version. Please use `popupClassName` instead."
        );
      }
    }, [dropdownClassName]);

    const [state, _setState] = useState({
      focus: false,
    });
    const refOldDate = useRef(null);
    const refValue = useRef(null);
    const refTimeout = useRef(null);
    const setState = (data = {}) => {
      _setState((state) => {
        return { ...state, ...data };
      });
    };
    useImperativeHandle(ref, () => ({
      show: () => {
        setState({ focus: true });
      },
      hide: () => {
        setState({ focus: false });
      },
    }));
    const onBlur = () => {
      if (refElement?.current) {
        refElement.current.querySelector(".input-content").style.display =
          "none";
        refElement.current.querySelector(".value-display").style.display =
          "block";
      }

      if (disableOnblur) {
        return;
      }
      setState({ focus: false });
      try {
        const date = moment(refValue.current, format)._d;
        if (
          isDateCheckInput &&
          ((isBefore && moment(date).isBefore(isDateCheckInput)) ||
            (!isBefore && moment(date).isAfter(isDateCheckInput)))
        ) {
          setState({
            date: null,
            value: "",
          });
        } else {
          if (date.isValidDate()) {
            refValue.current = moment(date).format(format);
            setState({
              date,
              value: refValue.current,
            });
            refOldDate.current = date;
            onChange && onChange(moment(date));
          } else {
            if (date && moment(date).format(format) != "Invalid date") {
              refValue.current = moment(date).format(format);
              setState({
                value: refValue.current,
              });
              refOldDate.current = date;
              onChange && onChange(moment(date));
            } else {
              refValue.current = "";
              refOldDate.current = null;
              setState({
                date: null,
                value: refValue.current,
              });
              onChange && onChange(null);
            }
          }
        }
      } catch (error) {}
    };

    const onFocus = () => {
      setState({ focus: true });
    };

    useEffect(() => {
      afterOpenChange?.(state.focus);
    }, [state.focus]);

    useEffect(() => {
      if (value && value?._d?.isValidDate()) {
        refOldDate.current = value._d;
        const date = moment(value?._d || new Date())?._d;
        refValue.current = moment(date).format(format);
        setState({
          value: refValue.current,
          date,
        });
      } else {
        setState({
          date: null,
          value: "",
        });
        refValue.current = null;
      }
    }, [value, format]);

    const onChangeDate = (value) => {
      let e = moment(value);
      if (formatDefaultTime) {
        const { hour, minute, second } = formatDefaultTime || {};
        e = moment(value)
          .set("hour", +hour)
          .set("minute", +minute)
          .set("second", +second);
      }
      const careTime = moment(pointTime);
      if (pointTime && compareTime(e, careTime)) {
        e.set("hour", careTime.get("hour"))
          .set("minute", careTime.get("minute"))
          .set("second", careTime.get("second"));
      }
      const newState = {
        date: e,
        value: moment(e).format(format),
        focus: false,
      };
      refOldDate.current = e;
      refValue.current = e.format(format);
      onChange && onChange(moment(e));
      setState(newState);
      setTimeout(() => {
        if (refElement?.current) {
          refElement.current.querySelector(".input-content").style.display =
            "none";
          refElement.current.querySelector(".value-display").style.display =
            "block";
        }
      }, 300);
    };

    const onChangeInput = (e) => {
      const value = e?.target?.value;
      refValue.current = value;
      setState({
        value,
      });

      if (refTimeout.current) {
        clearTimeout(refTimeout.current);
      }
      refTimeout.current = setTimeout((value) => {}, 1000, e.target.value);
      if (allowClear && !value) {
        e.stopPropagation();
        onChange && onChange(null);
        onOk && onOk(null);
      }
    };

    const onKeyDown = (e) => {
      if (e.keyCode === 13 || e.keyCode === 9) {
        setState({ focus: false });
        onBlur();
      }
    };

    const disabledTime = pointTime
      ? (d) => {
          const date = moment(d);
          const careTime = moment(pointTime);
          const isSameDate = checkIsSameDate
            ? date.startOf("day").isSame(careTime.startOf("day"))
            : date.get("date") === careTime.get("date");
          return {
            disabledHours: () =>
              range(0, 23).filter((i) =>
                isSameDate ? compareTime(i, careTime.get("hour")) : false
              ),
            disabledMinutes: () =>
              range(0, 59).filter((i) =>
                isSameDate && date.get("hour") === careTime.get("hour")
                  ? compareTime(i, careTime.get("minute"))
                  : false
              ),
            disabledSeconds: () =>
              range(0, 59).filter((i) =>
                isSameDate &&
                date.get("hour") === careTime.get("hour") &&
                date.get("minute") === careTime.get("minute")
                  ? compareTime(i, careTime.get("second"))
                  : false
              ),
          };
        }
      : disabledTimeDate
      ? disabledTimeDate
      : undefined;

    const _disabledDate = pointTime
      ? (d) => {
          const careTime = moment(pointTime);
          return (
            compareTime(d, careTime) && d.get("date") !== careTime.get("date")
          );
        }
      : disabledDate;

    const _onSelect = pointTime
      ? (d) => {
          const careTime = moment(pointTime);
          return (
            compareTime(d, careTime) && d.get("date") !== careTime.get("date")
          );
        }
      : onSelect;

    const value1 = useMemo(() => {
      return state?.date
        ? moment.isMoment(state?.date)
          ? state?.date
          : moment(state?.date)
        : null;
    }, [state?.date]);

    return (
      <Main
        className="input-content"
        style={style}
        $isCss={isCss}
        $showInput={showInput}
        $showIcon={showIcon}
      >
        {showInput && (
          <Input
            onClick={onFocus}
            type="text"
            onBlur={onBlur}
            onFocus={onFocus}
            value={state?.value || defaultValue}
            placeholder={placeholder}
            className={`${className || ""} input-date`}
            disabled={disabled}
            allowClear={allowClear}
            onChange={onChangeInput}
            onKeyDown={onKeyDown}
            {...(tabIndex && {
              tabIndex,
            })}
          />
        )}

        <DatePicker
          value={value1}
          format={format}
          open={state.focus}
          ref={refDatepicker}
          onFocus={refDatepicker.current?.blur}
          onChange={onChangeDate}
          onSelect={_onSelect}
          onOk={onOk}
          showTime={showTime}
          disabledDate={_disabledDate}
          disabledTime={disabledTime}
          placeholder={placeholder}
          {...rest}
          popupClassName={popupClassName || dropdownClassName}
        />
        <div className="calendar-icon">
          {showIcon && <CalendarOutlined size={20} />}
        </div>
        {bottom}
      </Main>
    );
  })
);
export default DateTimePicker;

DateTimePicker.RangePicker = RangePicker;
