import styled, { css } from "styled-components";

const Main = styled("div")`
  display: flex;
  align-items: center;
  position: relative;
  & .input-date {
    z-index: 1;
  }
  .input-date {
    opacity: ${(props) => (props.$showInput ? 1 : 0)};
  }

  .ant-picker-input {
    opacity: ${(props) => (props.$showInput ? 0 : 1)};
  }

  .ant-picker-suffix {
    opacity: ${(props) => (props.$showIcon ? 0 : 1)};
  }

  & .calendar-icon {
    position: absolute;
    right: 12px;
    margin-top: 5px;
    z-index: 2;
    svg {
      font-size: 20px !important;
      fill: #7a869a;
    }
  }
  & .date-display {
    position: absolute;
    left: 6px;
    z-index: 1;
  }

  & .date-hide {
    visibility: hidden;
  }

  & .ant-picker {
    border: 0 !important;
    box-shadow: none !important;
    position: absolute;
    ${(props) =>
      props.$isCss &&
      css`
        top: 0;
        opacity: 0;
        width: 0;
        height: 0;
      `}
    & .ant-picker-input {
      outline: none;
      pointer-events: none;
      &:focus {
        outline: none !important;
      }
    }
  }
  .anticon-close-circle {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    right: 0px;
    z-index: 2;
    & svg {
      width: 1.5em;
      height: 1.5em;
    }
  }
`;

export { Main };
