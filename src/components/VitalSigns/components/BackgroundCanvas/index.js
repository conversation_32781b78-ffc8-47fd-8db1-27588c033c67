import React, { memo, forwardRef, useEffect, useState, useMemo } from "react";
import { usePrevious } from "hooks";
import { SIZE } from "utils/vital-signs/constants";
import {
  drawLine,
  drawDate,
  calculateHeightByKey,
} from "utils/vital-signs/canvas-utils";
import { connect } from "react-redux";

const BackgroundCanvas = memo(
  forwardRef(
    (
      {
        canvasWidth,
        values,
        canvasHeight,
        moreValueIds,
        dataMA_CSS_VONG_CANH_TAY,
        dataHIEN_THI_TRUONG_SINH_HIEU,
        listChiSoSongMacDinh,
      },
      ref
    ) => {
      const [canvasBg, setCanvasBg] = useState(null);
      const prevValues = usePrevious(values, []);
      const isNoiTru = window.location.pathname.includes("quan-ly-noi-tru");
      let heightTenDvkham = calculateHeightByKey(values, isNoiTru, "tenDvKham");
      let heightTenKhoaChiDinh = calculateHeightByKey(
        values,
        isNoiTru,
        "tenKhoaChiDinh"
      );

      const bottomHeight = useMemo(() => {
        return (
          (isNoiTru ? SIZE.bottomHeightNoiTru : SIZE.bottomHeightNgoaiTru) +
          (dataMA_CSS_VONG_CANH_TAY?.eval() ? 45 : 0) +
          (dataHIEN_THI_TRUONG_SINH_HIEU?.eval() && isNoiTru ? 45 * 3 : 0) +
          listChiSoSongMacDinh.length * 45
        );
      }, [
        dataMA_CSS_VONG_CANH_TAY,
        dataHIEN_THI_TRUONG_SINH_HIEU,
        isNoiTru,
        listChiSoSongMacDinh,
      ]);

      useEffect(() => {
        if (canvasBg) {
          canvasBg.height = canvasHeight;
          canvasBg.width = canvasWidth;
          drawBackground(
            canvasBg,
            bottomHeight,
            dataMA_CSS_VONG_CANH_TAY,
            dataHIEN_THI_TRUONG_SINH_HIEU
          );
        }
      }, [
        canvasWidth,
        canvasHeight,
        canvasBg,
        bottomHeight,
        dataMA_CSS_VONG_CANH_TAY,
        dataHIEN_THI_TRUONG_SINH_HIEU,
      ]);

      useEffect(() => {
        try {
          redrawDataDate(bottomHeight);
        } catch (error) {}
      }, [values, bottomHeight]);

      const redrawDataDate = (bottomHeight) => {
        if (
          !prevValues ||
          !values ||
          JSON.stringify(
            prevValues.map((item) => ({
              thoiGianThucHien: item.thoiGianThucHien,
              ptTtId: item.ptTtId,
            }))
          ) !==
            JSON.stringify(
              values.map((item) => ({
                thoiGianThucHien: item.thoiGianThucHien,
                ptTtId: item.ptTtId,
              }))
            )
        ) {
          if (canvasBg) {
            const context2d = canvasBg.getContext("2d");
            context2d.clearRect(0, 0, canvasWidth, canvasHeight);
            drawBackground(canvasBg, bottomHeight);
          }
        }
      };

      const handleCanvas = (bgCanvas) => {
        if (!bgCanvas || canvasBg) {
          return;
        }
        setCanvasBg(bgCanvas);
        bgCanvas.width = canvasWidth;
        bgCanvas.height = canvasHeight;
      };

      const drawBackground = (
        bgCanvas,
        bottomHeight,
        dataMA_CSS_VONG_CANH_TAY,
        dataHIEN_THI_TRUONG_SINH_HIEU
      ) => {
        if (!bgCanvas) {
          return;
        }
        const context2d = bgCanvas.getContext("2d");
        drawLineRow(context2d);
        drawLineColumn(context2d);
        // drawLeftColumn(context2d);
        drawHeader(context2d);
        drawFooter(
          context2d,
          bottomHeight,
          dataMA_CSS_VONG_CANH_TAY,
          dataHIEN_THI_TRUONG_SINH_HIEU
        );
      };
      const drawLineRow = (ctx) => {
        for (
          let i = 0;
          i <
          canvasHeight -
            bottomHeight -
            SIZE.headerHeight -
            moreValueIds.length * 45 -
            heightTenDvkham -
            heightTenKhoaChiDinh;
          i = i + SIZE.rowHeight
        ) {
          if (i % (SIZE.rowHeight * 10) === 0) {
            drawLine(
              ctx,
              { x: SIZE.leftColumnWidth, y: i + SIZE.headerHeight },
              { x: canvasWidth, y: i + SIZE.headerHeight },
              1.5
            );
          } else {
            drawLine(
              ctx,
              { x: SIZE.leftColumnWidth, y: i + SIZE.headerHeight },
              { x: canvasWidth, y: i + SIZE.headerHeight },
              0.5,
              [10, 2]
            );
          }
        }
      };

      const drawLineColumn = (ctx) => {
        for (let i = 0; i < values.length + 1; i++) {
          drawLine(
            ctx,
            { x: i * SIZE.columnWidth + SIZE.leftColumnWidth, y: 0 },
            {
              x: i * SIZE.columnWidth + SIZE.leftColumnWidth,
              y: canvasHeight,
            },
            0.5
          );
        }
        drawLine(
          ctx,
          { x: canvasWidth, y: 0 },
          {
            x: canvasWidth,
            y: canvasHeight,
          },
          0.5
        );
      };

      const drawFooter = (ctx, bottomHeight) => {
        let bottom =
          bottomHeight +
          moreValueIds.length * 45 +
          heightTenDvkham +
          heightTenKhoaChiDinh;
        let plusHeightNgoaiTru =
          heightTenDvkham >= 45 ? heightTenDvkham - 45 : 0;
        let plusHeight =
          heightTenKhoaChiDinh >= 45 ? heightTenKhoaChiDinh - 45 : 0;
        let x =
          (bottomHeight +
            45 +
            (heightTenDvkham ? 45 : 0) +
            moreValueIds.length * 45) /
          45;
        const bmiVct = dataMA_CSS_VONG_CANH_TAY?.eval();
        const otherField = dataHIEN_THI_TRUONG_SINH_HIEU?.eval();
        for (let i = 0; i <= x; i++) {
          const isLastItems = i >= Math.floor(x); // Items cuối cùng cho tenKhoaChiDinh
          if (!isNoiTru) {
            // Ngoại trú: chỉ áp dụng plusHeightNgoaiTru cho tenDvKham (>= index), plusHeight cho tenKhoaChiDinh (items cuối)
            let idx = bmiVct ? 13 : 12;
            const isTenDvKhamArea = i >= idx; // Vùng tenDvKham

            let extraHeight = 0;
            if (isTenDvKhamArea && isLastItems) {
              extraHeight = plusHeightNgoaiTru + plusHeight; // Cả tenDvKham và tenKhoaChiDinh
            } else if (isTenDvKhamArea) {
              extraHeight = plusHeightNgoaiTru; // Chỉ tenDvKham
            } else if (isLastItems) {
              extraHeight = plusHeight; // Chỉ tenKhoaChiDinh
            }

            drawLine(
              ctx,
              {
                x: SIZE.leftColumnWidth,
                y: canvasHeight - bottom + i * 45 + extraHeight,
              },
              {
                x: canvasWidth,
                y: canvasHeight - bottom + i * 45 + extraHeight,
              },
              0.5,
              []
            );
          } else if (isNoiTru) {
            // Nội trú: chỉ áp dụng plusHeight cho tenKhoaChiDinh (items cuối)
            drawLine(
              ctx,
              {
                x: SIZE.leftColumnWidth,
                y:
                  canvasHeight -
                  bottom +
                  i * 45 +
                  (isLastItems ? plusHeight : 0),
              },
              {
                x: canvasWidth,
                y:
                  canvasHeight -
                  bottom +
                  i * 45 +
                  (isLastItems ? plusHeight : 0),
              },
              0.5,
              []
            );
          } else {
            drawLine(
              ctx,
              {
                x: SIZE.leftColumnWidth,
                y: canvasHeight - bottom + i * 45,
              },
              {
                x: canvasWidth,
                y: canvasHeight - bottom + i * 45,
              },
              0.5,
              []
            );
          }
        }
      };

      const drawHeader = (ctx) => {
        //line header 1
        drawLine(
          ctx,
          { x: SIZE.leftColumnWidth, y: 0 },
          { x: canvasWidth, y: 0 },
          1,
          []
        );
        //line header 2
        drawLine(
          ctx,
          { x: SIZE.leftColumnWidth, y: 30 },
          { x: canvasWidth, y: 30 },
          0.5,
          []
        );
        //line header 3
        drawLine(
          ctx,
          { x: SIZE.leftColumnWidth, y: 80 },
          { x: canvasWidth, y: 80 },
          0.5,
          []
        );
        drawDate(ctx, values, SIZE);
      };

      return <canvas ref={handleCanvas} style={styles.canvas} />;
    }
  )
);
const styles = {
  canvas: {
    position: "absolute",
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
    zIndex: 10,
  },
};
BackgroundCanvas.displayName = "BackgroundCanvas";

export default connect((state) => {
  return {
    values: state.vitalSigns.values || [],
    moreValueIds: state.vitalSigns.moreValueIds || [],
  };
})(BackgroundCanvas);
