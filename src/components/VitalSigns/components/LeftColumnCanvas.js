import React, { memo, forwardRef, useEffect, useState, useMemo } from "react";
import { isEmpty, cloneDeep } from "lodash";
import { usePrevious } from "hooks";
import { SIZE, BLOOD_PRESSURE_1 } from "utils/vital-signs/constants";
import {
  drawText,
  handleBloodPressure,
  drawLeftColumnFooter,
  drawLeftColumnBloodPressure,
  drawLeftColumnBackground,
  calculateHeightByKey,
} from "utils/vital-signs/canvas-utils";
import { connect } from "react-redux";
import { t } from "i18next";
import useListChiSoSong from "components/VitalSigns/hooks/useListChiSoSong";

const LeftColumnCanvas = memo(
  forwardRef(
    (
      {
        canvasWidth,
        values,
        canvasHeight,
        updateData,
        rangeBloodPressure,
        moreValueIds,
        dataMA_CSS_VONG_CANH_TAY,
        dataHIEN_THI_TRUONG_SINH_HIEU,
      },
      ref
    ) => {
      const isNoiTru = window.location.pathname.includes("quan-ly-noi-tru");
      const [canvasBg, setCanvasBg] = useState(null);
      const preValues = usePrevious(values);
      const { leftColumnWidth } = SIZE;
      const sizeLeftItem = leftColumnWidth / 3;
      let heightTenDvkham = calculateHeightByKey(values, isNoiTru, "tenDvKham");
      let heightTenKhoaChiDinh = calculateHeightByKey(
        values,
        isNoiTru,
        "tenKhoaChiDinh"
      );
      const { listAllChiSoSong, listChiSoSongMacDinh } = useListChiSoSong({
        isNoiTru,
      });

      let bottomHeight = useMemo(() => {
        return (
          (isNoiTru ? SIZE.bottomHeightNoiTru : SIZE.bottomHeightNgoaiTru) +
          (dataMA_CSS_VONG_CANH_TAY?.eval() ? 45 : 0) +
          (dataHIEN_THI_TRUONG_SINH_HIEU?.eval() && isNoiTru ? 45 * 3 : 0) +
          moreValueIds.length * 45 +
          heightTenDvkham +
          heightTenKhoaChiDinh +
          listChiSoSongMacDinh.length * 45
        );
      }, [
        isNoiTru,
        dataMA_CSS_VONG_CANH_TAY,
        dataHIEN_THI_TRUONG_SINH_HIEU,
        heightTenDvkham,
        heightTenKhoaChiDinh,
        moreValueIds,
        listChiSoSongMacDinh,
      ]);

      useEffect(() => {
        if (canvasBg) {
          canvasBg.height = canvasHeight;
          canvasBg.width = canvasWidth;
          const context2d = canvasBg.getContext("2d");
          context2d.clearRect(0, 0, canvasWidth, canvasHeight);
          context2d.beginPath();
          context2d.rect(0, 0, canvasWidth, canvasHeight);
          context2d.fillStyle = "#FFF";
          context2d.fill();

          drawBackground(canvasBg, bottomHeight);
          drawLeftColumnBloodPressure(context2d, rangeBloodPressure, SIZE);
        }
      }, [
        canvasWidth,
        canvasHeight,
        canvasBg,
        rangeBloodPressure,
        bottomHeight,
      ]);

      useEffect(() => {
        if (!preValues || JSON.stringify(preValues) !== JSON.stringify(values))
          getRangeBloodPressure();
      }, [values]);

      const getRangeBloodPressure = () => {
        const cloneValues = cloneDeep(values);
        const indexOfLastItemHasValue =
          cloneValues.length -
          1 -
          cloneValues.reverse().findIndex((item) => !!item.huyetApTamTruong);
        const newValue = handleBloodPressure(
          values[indexOfLastItemHasValue] &&
            values[indexOfLastItemHasValue].huyetap
        );

        const listShow =
          BLOOD_PRESSURE_1.find(
            (item) =>
              item.min <= newValue.huyetApTamTruong &&
              newValue.huyetApTamTruong <= item.max
          ) &&
          BLOOD_PRESSURE_1.find(
            (item) =>
              item.min <= newValue.huyetApTamTruong &&
              newValue.huyetApTamTruong <= item.max
          ).listShow;
        if (!isEmpty(listShow)) {
          updateData({
            rangeBloodPressure: listShow || [],
          });
        } else {
          updateData({
            rangeBloodPressure: BLOOD_PRESSURE_1[0].listShow,
          });
        }
      };

      const handleCanvas = (bgCanvas) => {
        if (!bgCanvas || canvasBg) {
          return;
        }
        setCanvasBg(bgCanvas);
        bgCanvas.width = canvasWidth;
        bgCanvas.height = canvasHeight;
      };

      const drawBackground = (bgCanvas, bottomHeight) => {
        if (!bgCanvas) {
          return;
        }
        const context2d = bgCanvas.getContext("2d");
        drawLeftColumnBackground({
          ctx: context2d,
          canvasWidth,
          canvasHeight,
          sizeLeftItem,
          bottomHeight,
          SIZE,
        });
        drawHeader(context2d);
        drawLeftColumnFooter({
          ctx: context2d,
          moreValueIds: moreValueIds,
          vitalSignsCategories: listAllChiSoSong,
          canvasWidth: canvasWidth,
          canvasHeight: canvasHeight,
          bottomHeight,
          isPrint: false,
          isNoiTru,
          heightTenDvkham,
          heightTenKhoaChiDinh,
          dataMA_CSS_VONG_CANH_TAY,
          dataHIEN_THI_TRUONG_SINH_HIEU,
          listChiSoSongMacDinh,
        });
      };

      const drawHeader = (ctx) => {
        drawText(ctx, t("quanLyNoiTru.chiSoSong.ngayThang"), { x: 10, y: 20 });
        drawText(ctx, t("quanLyNoiTru.chiSoSong.huyet"), { x: 10, y: 50 });
        drawText(ctx, t("quanLyNoiTru.chiSoSong.ap"), { x: 17, y: 70 });
        drawText(ctx, t("quanLyNoiTru.chiSoSong.mach"), {
          x: sizeLeftItem + 5,
          y: 50,
        });
        drawText(ctx, t("quanLyNoiTru.chiSoSong.lPh"), {
          x: sizeLeftItem + 5,
          y: 70,
        });
        drawText(ctx, t("quanLyNoiTru.chiSoSong.nhiet"), {
          x: sizeLeftItem * 2 + 5,
          y: 50,
        });
        drawText(ctx, t("sinhHieu.doC"), {
          x: sizeLeftItem * 2 + 5,
          y: 70,
        });
      };

      return <canvas ref={handleCanvas} style={styles.canvas} />;
    }
  )
);
const styles = {
  canvas: {
    position: "absolute",
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
    zIndex: 102,
  },
};
LeftColumnCanvas.displayName = "LeftColumnCanvas";

const mapDispatch = ({ vitalSigns: { updateData } }) => ({
  updateData,
});

export default connect((state) => {
  return {
    values: state.vitalSigns.values || [],
    moreValueIds: state.vitalSigns.moreValueIds || [],
    rangeBloodPressure: state.vitalSigns.rangeBloodPressure || [],
    vitalSignsCategories: state.chiSoSong.listAllChiSoSong,
  };
}, mapDispatch)(LeftColumnCanvas);
