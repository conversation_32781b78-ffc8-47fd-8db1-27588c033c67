import React, {
  useState,
  forwardRef,
  useImperativeHandle,
  useRef,
  useEffect,
} from "react";
import { Row, Col } from "antd";
import { Main } from "./styled";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import { Button } from "components";
import { SVG } from "assets";
import { HOTKEY } from "constants/index";
import { useGuid } from "hooks";
import DOMPurify from "dompurify";

const ModalConfirm = forwardRef((props, ref) => {
  const { t } = useTranslation();
  const refOk = useRef(null);
  const refCancel = useRef(null);
  const refHide = useRef(null);
  const refIsValid = useRef(null);
  const layerId = useGuid();
  const { onAddLayer, onRemoveLayer, onRegisterHotkey } = useDispatch().phimTat;
  const [state, _setState] = useState({
    show: false,
  });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };
  useImperativeHandle(ref, () => ({
    show: (options = {}, onOk, onCancel, onHide) => {
      console.log("[SHOW CONFIRM]", options);
      const {
        title,
        typeModal,
        content,
        detail,
        classNameCancelText,
        cancelText,
        okText,
        classNameOkText,
        showBtnOk,
        showBtnCancel = true,
        showImg,
        rightCancelButton,
        subContent,
        isScroll,
        isContentElement,
        width,
        showIconContent = true,
        closable = false,
        mainClassName,
        isValid, // hàm sử dụng để bắt điều kiện trước khi nhấn ok
        maskClosable = true,
        zIndex,
      } = options;
      setState({
        show: true,
        title,
        typeModal,
        content,
        detail,
        classNameCancelText,
        classNameOkText,
        cancelText,
        okText,
        showBtnOk,
        showBtnCancel,
        showImg,
        rightCancelButton,
        subContent,
        isScroll,
        isContentElement,
        width,
        showIconContent,
        closable,
        mainClassName,
        maskClosable,
        zIndex,
      });
      refOk.current = onOk;
      refCancel.current = onCancel;
      refHide.current = onHide;
      refIsValid.current = isValid;

      onAddLayer({ layerId: layerId });
      onRegisterHotkey({
        layerId: layerId,
        hotKeys: [
          {
            keyCode: HOTKEY.ESC, //ESC
            onEvent: () => {
              setState({
                show: false,
              });
            },
          },
          {
            keyCode: HOTKEY.ENTER, //Enter
            onEvent: () => {
              showBtnOk && onOk();
              setState({
                show: false,
              });
            },
          },
        ],
      });
    },
    hide: () => {
      setState({ show: false });
    },
  }));
  const onCancel = () => {
    setState({ show: false });
    if (refCancel.current) refCancel.current();
  };
  const onOk = () => {
    if (refIsValid.current && !refIsValid.current()) {
      return;
    }

    setState({ show: false });
    if (refOk.current) refOk.current();
  };
  useEffect(() => {
    if (!state.show) {
      onRemoveLayer({ layerId: layerId });

      //gọi func khi modal được đóng
      if (refHide.current) refHide.current();
    }
  }, [state.show]);
  useEffect(() => {
    return () => {
      onRemoveLayer({ layerId: layerId });
    };
  }, []);
  const handleCancel = () => {
    setState({
      show: false,
    });
  };
  if (!state.show) {
    // remove popup khi đóng,
    // resolved vấn đề 2 popup đè lên nhau, và bị che mất
    return <></>;
  }
  return (
    <Main
      width={state.width}
      open={state.show}
      closable={state.closable}
      typeModal={state.typeModal}
      maskClosable={state.maskClosable}
      mask={true}
      onCancel={handleCancel}
      isScroll={state.isScroll}
      className={state.mainClassName}
      {...(state.zIndex && { zIndex: state.zIndex })}
    >
      <Row className="container">
        <Col>
          <Row className={`header header-2 ${state.typeModal || "error"}`}>
            <h3 className="title title-2">
              {state.title || t("common.thongBao")}
            </h3>
          </Row>
          <div className="modal-content modal-content-2">
            {state.showIconContent && (
              <>
                {state.typeModal == "warning" ? (
                  <SVG.IcWarning />
                ) : (
                  <SVG.IcError />
                )}
              </>
            )}

            {state.isContentElement ? (
              state.content
            ) : (
              <div
                className="content content-2"
                dangerouslySetInnerHTML={{
                  __html: DOMPurify.sanitize(state.content),
                }}
              />
            )}

            <div
              className="detail"
              dangerouslySetInnerHTML={{
                __html: DOMPurify.sanitize(state.detail),
              }}
            />
          </div>
          {state.subContent && (
            <div
              className="sub-content"
              dangerouslySetInnerHTML={{
                __html: DOMPurify.sanitize(state.subContent),
              }}
            />
          )}

          <Row
            className={`button-bottom ${
              state.rightCancelButton ? "right" : ""
            }`}
          >
            {state.showBtnCancel && (
              <Button onClick={onCancel} type="default" minWidth={100}>
                {state.cancelText || t("common.huy")}
              </Button>
            )}
            {state.showBtnOk && (
              <Button
                minWidth={100}
                onClick={onOk}
                type={state.typeModal || "primary"}
                iconHeight={20}
                rightIcon={
                  state.showImg &&
                  (state.showIconSucces ? <SVG.IcSuccess /> : <SVG.IcEdit />)
                }
              >
                <span>{state.okText || t("common.dongY")}</span>
              </Button>
            )}
          </Row>
        </Col>
      </Row>
    </Main>
  );
});

export default ModalConfirm;
