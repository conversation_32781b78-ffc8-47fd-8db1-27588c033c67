import React, { useState, useEffect, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Col, Input, Form, Menu } from "antd";
import { getBackendUrl } from "client/request";
import BaseDm3 from "pages/danhMuc/BaseDm3";
import {
  TableWrapper,
  HeaderSearch,
  Pagination,
  Checkbox,
  Select,
  Button,
} from "components";

import {
  ADD_LAYOUT_COLLAPSE,
  TABLE_LAYOUT_COLLAPSE,
  HIEU_LUC,
  TABLE_LAYOUT,
  ADD_LAYOUT,
  ROLES,
  ENUM,
  HOTKEY,
} from "constants/index";
import { checkRole } from "lib-utils/role-utils";
import { YES_NO } from "constants/index";
import MultiLevelTab from "components/MultiLevelTab";
import BaoCaoChiTiet from "./BaoCaoChiTiet";
import ThietLapChanKy from "./ThietLapChanKy";
import ModalImport from "components/DanhMuc/ModalImport";
import { t } from "i18next";
import { useEnum, useStore, useListAll, useGuid, useLoading } from "hooks";
import { SVG } from "assets";
import TLDKHoanThanhKyKhiChuyenKhoaRaVien from "./ThietLapDKHoanThanhKyKhiChuyenKhoaRaVien";

const getUploadedFileName = (url = "") => {
  const indexSlash = url?.lastIndexOf("/");
  let updatedName = url?.slice(indexSlash + 1);

  return `${updatedName}`;
};

const BaoCao = () => {
  const [form] = Form.useForm();
  const { showLoading, hideLoading } = useLoading();
  const {
    baoCao: {
      onSearch,
      onSizeChange,
      onSortChange,
      onChangeInputSearch,
      updateData,
      getBaoCaoChanKyById,
      exportData,
      onImport,
      onImportThietLapChanKy,
    },
  } = useDispatch();

  const {
    totalElements,
    page,
    size,
    dataSortColumn,
    dataEditDefault,
    dataSearch = {},
  } = useSelector((state) => state.baoCao);

  const listBaoCao = useStore("baoCao.listBaoCao", []);

  const [listHuongGiay] = useEnum(ENUM.HUONG_GIAY);
  const [listKhoGiay] = useEnum(ENUM.KHO_GIAY);
  const [listDinhDangBaoCao] = useEnum(ENUM.DINH_DANG_BAO_CAO);
  const [listLoaiIn] = useEnum(ENUM.LOAI_IN, []);
  const [listHinhThucIn] = useEnum(ENUM.HINH_THUC_IN);
  const [listNgonNgu] = useEnum(ENUM.NGON_NGU, []);
  const [listAllLoaiBenhAn] = useListAll("loaiBenhAn", {}, true);
  const [collapseStatus, setCollapseStatus] = useState(false);

  const [state, _setState] = useState({
    mauBaoCao: null,
    editStatus: false,
    defaultFileList: [],
    isSelected: false,
    showFullTable: false,
    activeKeyTab: "1",
  });
  const layerId = useGuid();
  const refClickBtnAdd = useRef();
  const refClickBtnSave = useRef();
  const refSave1 = useRef();
  const refSave2 = useRef();
  const refSave3 = useRef();
  const refSelectRow = useRef();
  const refModalImport = useRef(null);
  const refModalImportThietLapChanKy = useRef(null);
  const { onAddLayer, onRemoveLayer, onRegisterHotkey } = useDispatch().phimTat;

  useEffect(() => {
    onAddLayer({ layerId: layerId });
    onRegisterHotkey({
      layerId: layerId,
      hotKeys: [
        {
          keyCode: HOTKEY.F1, //F1
          onEvent: () => {
            refClickBtnAdd.current && refClickBtnAdd.current();
          },
        },
        {
          keyCode: HOTKEY.F4, //F4
          onEvent: (e) => {
            refClickBtnSave.current && refClickBtnSave.current(e);
          },
        },
        {
          keyCode: HOTKEY.UP, //up
          onEvent: (e) => {
            const focusedElement = document.activeElement;
            const isAntdSelect =
              focusedElement?.closest(".ant-select") !== null;

            if (!isAntdSelect) {
              refSelectRow.current && refSelectRow.current(-1);
            }
          },
        },
        {
          keyCode: HOTKEY.DOWN, //down
          onEvent: (e) => {
            const focusedElement = document.activeElement;
            const isAntdSelect =
              focusedElement?.closest(".ant-select") !== null;

            if (!isAntdSelect) {
              refSelectRow.current && refSelectRow.current(1);
            }
          },
        },
      ],
    });
    return () => {
      onRemoveLayer({ layerId: layerId });
    };
  }, []);

  refSelectRow.current = (index) => {
    const indexNextItem =
      (listBaoCao?.findIndex((item) => item.id === dataEditDefault?.id) || 0) +
      index;
    if (-1 < indexNextItem && indexNextItem < listBaoCao.length) {
      updateData({ dataEditDefault: listBaoCao[indexNextItem] });
      getBaoCaoChanKyById(listBaoCao[indexNextItem].id);
      setState({
        mauBaoCao: listBaoCao[indexNextItem].mauBaoCao,
        editStatus: true,
        isSelected: true,
        defaultFileList: listBaoCao[indexNextItem]?.mauBaoCao
          ? [
              {
                uid: "1",
                name: getUploadedFileName(listBaoCao[indexNextItem].mauBaoCao),
                url: `${getBackendUrl()}/api/his/v1/files/${
                  listBaoCao[indexNextItem]?.mauBaoCao
                }`,
              },
            ]
          : [],
      });
      form.setFieldsValue(listBaoCao[indexNextItem]);
      document
        .getElementsByClassName("row-id-" + listBaoCao[indexNextItem]?.id)[0]
        .scrollIntoView({ block: "end", behavior: "smooth" });
    }
  };

  refClickBtnSave.current = (e) => {
    const { activeKeyTab } = state;
    if (activeKeyTab === "1" && refSave1.current) refSave1.current(e);
    if (activeKeyTab === "6" && refSave2.current) refSave2.current(e);
    if (activeKeyTab === "5" && refSave2.current) refSave3.current(e);
  };
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  useEffect(() => {
    onSizeChange({ size: 10 });
  }, []);

  const handleClickedBtnAdded = () => {
    setState({
      editStatus: false,
      mauBaoCao: null,
      defaultFileList: [],
      invalidMauBaoCao: false,
      isSelected: true,
    });
    form.resetFields();
  };
  refClickBtnAdd.current = handleClickedBtnAdded;

  const onShowAndHandleUpdate = (data = {}) => {
    updateData({ dataEditDefault: data });
    getBaoCaoChanKyById(data.id);
    setState({
      mauBaoCao: data.mauBaoCao,
      editStatus: true,
      isSelected: true,
      defaultFileList: data?.mauBaoCao
        ? [
            {
              uid: "1",
              name: getUploadedFileName(data.mauBaoCao),
              url: `${getBackendUrl()}/api/his/v1/files/${data?.mauBaoCao}`,
            },
          ]
        : [],
    });
    form.setFieldsValue(data);
  };

  const onRow = (record, index) => {
    return {
      onClick: (event) => {
        onShowAndHandleUpdate(record);
      },
    };
  };

  const onClickSort = (key, value) => {
    onSortChange({
      [key]: value,
    });
  };

  const refTimeOut = useRef(null);
  const onSearchInput = (key) => (e) => {
    if (refTimeOut.current) {
      clearTimeout(refTimeOut.current);
      refTimeOut.current = null;
    }
    refTimeOut.current = setTimeout(
      (key, s) => {
        let value = "";
        if (s) {
          if (s?.hasOwnProperty("checked")) value = s?.checked;
          else value = s?.value;
        } else value = e;
        onChangeInputSearch({
          [key]: value,
        });
      },
      500,
      key,
      e?.target
    );
  };

  const onChangePage = (page) => {
    onSearch({ page: page - 1 });
  };

  const columns = [
    {
      title: <HeaderSearch title={t("common.stt")} />,
      width: "70px",
      dataIndex: "index",
      key: "index",
      align: "center",
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.maBaoCao")}
          sort_key="ma"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["ma"] || 0}
          search={
            <Input
              placeholder={t("danhMuc.timMaBaoCao")}
              onChange={onSearchInput("ma")}
              defaultValue={dataSearch["ma"]}
            />
          }
        />
      ),
      width: "120px",
      dataIndex: "ma",
      key: "ma",
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.tenBaoCao")}
          sort_key="ten"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["ten"] || 0}
          search={
            <Input
              placeholder={t("danhMuc.timTenBaoCao")}
              onChange={onSearchInput("ten")}
              defaultValue={dataSearch["ten"]}
            />
          }
        />
      ),
      width: "250px",
      dataIndex: "ten",
      key: "ten",
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.khoGiay")}
          sort_key="khoGiay"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["khoGiay"] || 0}
          searchSelect={
            <Select
              defaultValue={dataSearch["khoGiay"]}
              data={listKhoGiay}
              placeholder={t("danhMuc.chonKhoGiay")}
              onChange={onSearchInput("khoGiay")}
              hasAllOption={true}
            />
          }
        />
      ),
      width: "100px",
      dataIndex: "khoGiay",
      key: "khoGiay",
      render: (record) => {
        const currentName = (listKhoGiay || []).find(
          (item) => item.id === record
        );
        return <span>{currentName?.ten}</span>;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.kichThuocChieuDoc")}
          sort_key="chieuDoc"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["chieuDoc"] || 0}
          search={
            <Input
              placeholder={t("danhMuc.timTheoKichThuocChieuDoc")}
              onChange={onSearchInput("chieuDoc")}
              defaultValue={dataSearch["chieuDoc"]}
            />
          }
        />
      ),
      width: "160px",
      dataIndex: "chieuDoc",
      key: "chieuDoc",
      align: "right",
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.kichThuocChieuNgang")}
          sort_key="chieuNgang"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["chieuNgang"] || 0}
          search={
            <Input
              placeholder={t("danhMuc.timTheoKichThuocChieuNgang")}
              onChange={onSearchInput("chieuNgang")}
              defaultValue={dataSearch["chieuNgang"]}
            />
          }
        />
      ),
      width: "160px",
      dataIndex: "chieuNgang",
      key: "chieuNgang",
      align: "right",
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.huongGiay")}
          sort_key="huongGiay"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["huongGiay"] || 0}
          searchSelect={
            <Select
              defaultValue={dataSearch["huongGiay"]}
              data={listHuongGiay}
              placeholder={t("danhMuc.chonHuongGiay")}
              onChange={onSearchInput("huongGiay")}
              hasAllOption={true}
            />
          }
        />
      ),
      width: "100px",
      dataIndex: "huongGiay",
      key: "huongGiay",
      render: (record) => {
        const currentName = (listHuongGiay || []).find(
          (item) => item.id === record
        );
        return <span>{currentName?.ten}</span>;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.dinhDangXuatFile")}
          sort_key="dinhDang"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dinhDang"] || 0}
          searchSelect={
            <Select
              defaultValue={dataSearch["dinhDang"]}
              data={listDinhDangBaoCao}
              placeholder={t("danhMuc.chonDinhDang")}
              onChange={onSearchInput("dinhDang")}
              hasAllOption={true}
            />
          }
        />
      ),
      width: "100px",
      dataIndex: "dinhDang",
      key: "dinhDang",
      render: (record) => {
        const dinhDangBC =
          (listDinhDangBaoCao || []).find((element) => element.id === record) ||
          {};
        return <span>{dinhDangBC.ten}</span>;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.loaiIn")}
          sort_key="loaiIn"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["loaiIn"] || 0}
          searchSelect={
            <Select
              defaultValue={dataSearch["loaiIn"]}
              data={listLoaiIn}
              placeholder={t("danhMuc.chonLoaiIn")}
              onChange={onSearchInput("loaiIn")}
            />
          }
        />
      ),
      width: "100px",
      dataIndex: "loaiIn",
      key: "loaiIn",
      align: "center",
      render: (item) => {
        return (listLoaiIn || []).find((x) => x.id == item)?.ten || "";
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.loaiBenhAn")}
          searchSelect={
            <Select
              defaultValue={dataSearch["dsLoaiBenhAnId"]}
              data={listAllLoaiBenhAn}
              placeholder={t("danhMuc.chonLoaiBenhAn")}
              onChange={onSearchInput("dsLoaiBenhAnId")}
            />
          }
        />
      ),
      width: "120px",
      dataIndex: "dsLoaiBenhAn",
      key: "dsLoaiBenhAn",
      render: (_, item) =>
        item.dsLoaiBenhAn.map((item) => item?.ten)?.join(", "),
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.hinhThucIn")}
          searchSelect={
            <Select
              defaultValue={dataSearch["hinhThucIn"]}
              data={listHinhThucIn}
              placeholder={t("danhMuc.chonHinhThucIn")}
              onChange={onSearchInput("hinhThucIn")}
            />
          }
        />
      ),
      width: "120px",
      dataIndex: "hinhThucIn",
      key: "hinhThucIn",
      render: (value) => listHinhThucIn.find((item) => value === item.id)?.ten,
    },
    {
      title: (
        <HeaderSearch
          title={t("account.ngonNgu")}
          searchSelect={
            <Select
              defaultValue={dataSearch["ngonNgu"]}
              data={listNgonNgu}
              placeholder={t("danhMuc.chonNgonNgu")}
              onChange={onSearchInput("ngonNgu")}
            />
          }
        />
      ),
      width: "120px",
      dataIndex: "ngonNgu",
      key: "ngonNgu",
      render: (item) => {
        return (listNgonNgu || []).find((x) => x.id == item)?.ten || "";
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("common.ghiChu")}
          sort_key="ghiChu"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["ghiChu"] || 0}
          search={
            <Input
              placeholder={t("common.nhapGhiChu")}
              onChange={onSearchInput("ghiChu")}
              defaultValue={dataSearch["ghiChu"]}
            />
          }
        />
      ),
      width: "250px",
      dataIndex: "ghiChu",
      key: "ghiChu",
    },
    {
      title: (
        <HeaderSearch
          title={t("common.KY_SO")}
          sort_key="kySo"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["kySo"] || 0}
          searchSelect={
            <Select
              defaultValue={dataSearch["kySo"]}
              data={YES_NO}
              placeholder={t("danhMuc.chonKySo")}
              onChange={onSearchInput("kySo")}
              hasAllOption={true}
            />
          }
        />
      ),
      width: "100px",
      dataIndex: "kySo",
      key: "kySo",
      align: "center",
      render: (item) => {
        return <Checkbox checked={item} />;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.phieuDayISC")}
          sort_key="online"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["online"] || 0}
          searchSelect={
            <Select
              defaultValue={dataSearch["online"]}
              data={YES_NO}
              placeholder={t("danhMuc.chonPhieuDayISC")}
              onChange={onSearchInput("online")}
              hasAllOption={true}
            />
          }
        />
      ),
      width: "120px",
      dataIndex: "online",
      key: "online",
      align: "center",
      render: (item) => {
        return <Checkbox checked={item} />;
      },
    },
    {
      title: (
        <HeaderSearch
          sort_key="active"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.active || 0}
          searchSelect={
            <Select
              defaultValue={dataSearch["active"]}
              data={HIEU_LUC}
              placeholder={t("danhMuc.chonHieuLuc")}
              onChange={onSearchInput("active")}
              hasAllOption={true}
            />
          }
          title={t("danhMuc.coHieuLuc")}
        />
      ),
      width: "100px",
      dataIndex: "active",
      key: "active",
      align: "center",
      render: (item) => {
        return <Checkbox checked={item} />;
      },
    },
  ];

  const handleSizeChange = (size) => {
    onSizeChange({ size });
  };

  const setRowClassName = (record) => {
    let idDiff = dataEditDefault?.id;
    return record.id === idDiff
      ? "row-actived row-id-" + record.id
      : "row-id-" + record.id;
  };
  const listPanel = [
    {
      title: t("danhMuc.thongTinDichVu"),
      key: 1,
      render: () => {
        return (
          <BaoCaoChiTiet
            stateParent={state}
            setStateParent={setState}
            refCallbackSave={refSave1}
          />
        );
      },
    },
    {
      key: 6,
      title: t("danhMuc.thietLapChanKy"),
      render: () => {
        return (
          <ThietLapChanKy
            stateParent={state}
            setStateParent={setState}
            refCallbackSave={refSave2}
          />
        );
      },
    },
    {
      key: 5,
      title: t("danhMuc.thietLapDieuKienHoanThanhKy"),
      render: () => {
        return (
          <TLDKHoanThanhKyKhiChuyenKhoaRaVien
            stateParent={state}
            setStateParent={setState}
            refCallbackSave={refSave3}
          />
        );
      },
    },
  ];
  const handleChangeshowTable = () => {
    setState({
      changeShowFullTbale: true,
      showFullTable: !state.showFullTable,
    });
    setTimeout(() => {
      setState({
        changeShowFullTbale: false,
      });
    }, 1000);
  };
  const handleCollapsePane = () => {
    setCollapseStatus(!collapseStatus);
  };

  const onImportData = () => {
    refModalImport.current &&
      refModalImport.current.show({ isModalVisible: true });
  };

  const onExportData = () => {
    showLoading();
    exportData().finally(() => {
      hideLoading();
    });
  };

  const onImportDataThietLapChanKy = () => {
    refModalImportThietLapChanKy.current &&
      refModalImportThietLapChanKy.current.show({ isModalVisible: true });
  };

  const menuMultiTab = () => (
    <Menu
      items={[
        ...(onImport
          ? [
              {
                key: 1,
                label: (
                  <a onClick={onImportData}>
                    <div className="flex icon_utilities gap-8">
                      <SVG.IcUpload />
                      <span>
                        {t("danhMuc.nhapDuLieuTitle", {
                          title: t("danhMuc.danhMucBaoCao"),
                        })}
                      </span>
                    </div>
                  </a>
                ),
              },
            ]
          : []),
        ...(exportData
          ? [
              {
                key: 2,
                label: (
                  <a onClick={onExportData}>
                    <div className="flex icon_utilities gap-8">
                      <SVG.IcDownload />
                      <span>
                        {t("danhMuc.xuatDuLieuTitle", {
                          title: t("danhMuc.danhMucBaoCao"),
                        })}
                      </span>
                    </div>
                  </a>
                ),
              },
            ]
          : []),
        ...(onImportThietLapChanKy
          ? [
              {
                key: 3,
                label: (
                  <a onClick={onImportDataThietLapChanKy}>
                    <div className="flex icon_utilities gap-8">
                      <SVG.IcUpload />
                      <span>
                        {t("danhMuc.nhapDuLieuTitle", {
                          title: t("danhMuc.thietLapChanKy"),
                        })}
                      </span>
                    </div>
                  </a>
                ),
              },
            ]
          : []),
      ]}
    />
  );

  return (
    <BaseDm3
      breadcrumb={[
        { title: t("danhMuc.danhMuc"), link: "/danh-muc" },
        {
          title: t("danhMuc.danhMucBaoCao"),
          link: "/danh-muc/bao-cao",
        },
      ]}
    >
      <Col
        {...(!state.showFullTable
          ? collapseStatus
            ? TABLE_LAYOUT_COLLAPSE
            : TABLE_LAYOUT
          : null)}
        span={state.showFullTable ? 24 : null}
        className={`pr-3 ${state.changeShowFullTbale ? "" : "transition-ease"}`}
      >
        <TableWrapper
          title={t("danhMuc.danhMucBaoCao")}
          scroll={{ x: 1000 }}
          classNameRow={"custom-header"}
          styleMain={{ marginTop: 0 }}
          menuMultiTab={menuMultiTab}
          buttonHeader={
            checkRole([ROLES["DANH_MUC"].BAO_CAO_THEM])
              ? [
                  {
                    content: (
                      <Button
                        type="success"
                        onClick={handleClickedBtnAdded}
                        rightIcon={<SVG.IcAdd />}
                      >
                        {t("common.themMoiF1")}
                      </Button>
                    ),
                  },
                  {
                    className: `btn-change-full-table ${
                      state.showFullTable ? "small" : "large"
                    }`,
                    title: state.showFullTable ? (
                      <SVG.IcShowThuNho />
                    ) : (
                      <SVG.IcShowFull />
                    ),
                    onClick: handleChangeshowTable,
                  },

                  {
                    className: "btn-collapse",
                    title: collapseStatus ? (
                      <SVG.IcExtend />
                    ) : (
                      <SVG.IcCollapse />
                    ),
                    onClick: handleCollapsePane,
                  },
                ]
              : [
                  {
                    className: `btn-change-full-table ${
                      state.showFullTable ? "small" : "large"
                    }`,
                    title: state.showFullTable ? (
                      <SVG.IcShowThuNho />
                    ) : (
                      <SVG.IcShowFull />
                    ),
                    onClick: handleChangeshowTable,
                  },
                  {
                    className: "btn-collapse",
                    title: collapseStatus ? (
                      <SVG.IcExtend />
                    ) : (
                      <SVG.IcCollapse />
                    ),
                    onClick: handleCollapsePane,
                  },
                ]
          }
          columns={columns}
          dataSource={listBaoCao}
          onRow={onRow}
          rowClassName={setRowClassName}
        />
        {!!totalElements ? (
          <Pagination
            listData={listBaoCao}
            onChange={onChangePage}
            current={page + 1}
            pageSize={size}
            total={totalElements}
            onShowSizeChange={handleSizeChange}
            style={{ flex: 1, justifyContent: "flex-end", width: "100%" }}
          />
        ) : null}
      </Col>
      {!state.showFullTable && (
        <Col
          {...(collapseStatus ? ADD_LAYOUT_COLLAPSE : ADD_LAYOUT)}
          className={`mt-3 ${
            state.changeShowFullTbale ? "" : "transition-ease"
          }`}
        >
          <MultiLevelTab
            listPanel={listPanel}
            isBoxTabs={true}
            activeKey={state.activeKeyTab}
            onChange={(activeKeyTab) => setState({ activeKeyTab })}
          ></MultiLevelTab>
        </Col>
      )}
      <ModalImport
        onImport={onImport}
        ref={refModalImport}
        isUploadFolder={true}
      />
      <ModalImport
        onImport={onImportThietLapChanKy}
        ref={refModalImportThietLapChanKy}
      />
    </BaseDm3>
  );
};

export default BaoCao;
