import React, {
  useEffect,
  useState,
  useMemo,
  useRef,
  forwardRef,
  useImperativeHandle,
} from "react";
import { useSelector, useDispatch } from "react-redux";
import { formatDecimal, firstLetterWordUpperCase, isArray, sleep } from "utils";
import { Main } from "./styled";
import { Col, Row, message } from "antd";
import {
  GIOI_TINH_BY_VALUE,
  HOTKEY,
  LOAI_PHUONG_THUC_TT,
  THIET_LAP_CHUNG,
  TRANG_THAI_HOA_DON,
} from "constants/index";
import { ModalTemplate, Button } from "components";
import { useTranslation } from "react-i18next";
import { SVG } from "assets";
import { useGuid, useLoading, useStore, useThietLap } from "hooks";
import PhuongThucThanhToan from "pages/goiDichVu/ChiTietNguoiBenhSuDungGoi/ModalThemMoiPhieuThuThanhToan/PhuongThucThanhToan";
import printProvider from "data-access/print-provider";
import { centralizedErrorHandling } from "lib-utils";

const ModalPayment = (props, ref) => {
  const { t } = useTranslation();
  const { nhaTamUng, nbDotDieuTriId, isPhatHoaDon, caLamViec } = props || {};
  const { infoPatient, nguoiBenhId } = useSelector(
    (state) => state.thuocChiTiet
  );
  const listAllPhuongThucThanhToan = useStore(
    "phuongThucTT.listAllPhuongThucThanhToan",
    []
  );
  const [dataDINH_DANG_XEM_HOA_DON] = useThietLap(
    THIET_LAP_CHUNG.DINH_DANG_XEM_HOA_DON
  );
  const [dataTU_DONG_BAT_PHIEU_THU_NHA_THUOC] = useThietLap(
    THIET_LAP_CHUNG.TU_DONG_BAT_PHIEU_THU_NHA_THUOC
  );

  const { phieuThu } = infoPatient || {};
  const { thanhTien } = phieuThu || {};
  const {
    thuocChiTiet: { postThanhToan, inPhieuThuNhaThuoc },
    phimTat: { onAddLayer, onRemoveLayer, onRegisterHotkey },
    doiTac: { getListAllDoiTacThanhToan },
    phuongThucTT: { getListAllPhuongThucThanhToan },
    thuNgan: { getThongTinPhieuThu, huyQrThanhToan },
    dsHoaDonDienTu: { inHoaDon },
    phuongThucTT: { getListPhuongThucTTQRHoaDon },
  } = useDispatch();

  const { showLoading, hideLoading } = useLoading();

  const layerId = useGuid();
  const refF4 = useRef();
  const refModal = useRef(null);
  const refTienMat = useRef(null);
  const refCallback1 = useRef(null);
  const refCallback2 = useRef(null);
  const refPhuongThucThanhToan = useRef(null);

  const [state, _setState] = useState({ infoPrice: {} });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };
  useImperativeHandle(ref, () => ({
    show: (
      { isDuyet, phuongThucTtId, isHuyQrCode, qrThanhToan },
      callback1,
      callback2
    ) => {
      setState({
        show: true,
        isDuyet,
        phuongThucTtId,
        isHuyQrCode,
        qrThanhToan,
      });
      refCallback1.current = callback1;
      refCallback2.current = callback2;
      getListAllDoiTacThanhToan({ page: "", size: "", active: true });
    },
  }));

  useEffect(() => {
    if (state.show) {
      refModal.current && refModal.current.show();
      getListAllPhuongThucThanhToan({ page: "", size: "", active: true });
      getListPhuongThucTTQRHoaDon({ active: true });
    } else {
      refModal.current && refModal.current.hide();
    }
  }, [state.show]);

  useEffect(() => {
    if (state.show) {
      onAddLayer({ layerId: layerId });
      // đăng ký phím tắt //
      onRegisterHotkey({
        layerId: layerId,
        hotKeys: [
          {
            keyCode: HOTKEY.F4, //F4
            onEvent: () => {
              refF4.current && refF4.current();
            },
          },
          {
            keyCode: HOTKEY.ESC, //ESC
            onEvent: () => {
              setState({ show: false });
            },
          },
          {
            keyCode: HOTKEY.F2, //F3
            onEvent: () => {
              refTienMat.current && refTienMat.current.focus();
            },
          },
        ],
      });
    }
    return () => {
      onRemoveLayer({ layerId: layerId });
    };
  }, [state.show]);

  const tienMatObj = useMemo(() => {
    if (state.show && listAllPhuongThucThanhToan.length && thanhTien) {
      let tienMat = listAllPhuongThucThanhToan?.find((item) => item?.tienMat);

      if (state.isDuyet) {
        let dsPtt = {};
        for (const item of listAllPhuongThucThanhToan) {
          let _dsPtt = phieuThu?.dsPhuongThucTt.find(
            (i) => i.phuongThucTtId === item.id
          );
          if (_dsPtt) {
            dsPtt = {
              ...dsPtt,
              [item.id]: {
                tongTien: _dsPtt.tongTien,
                nganHangId: _dsPtt.nganHangId,
              },
            };
          }
        }
        setState({ infoPrice: dsPtt });
      } else {
        if (state.phuongThucTtId && state.phuongThucTtId !== tienMat.id) {
          setState({
            infoPrice: {
              [state.phuongThucTtId]: {
                tongTien: thanhTien,
              },
            },
          });
        } else {
          setState({
            infoPrice: {
              [tienMat.id]: {
                tongTien: thanhTien,
              },
            },
          });
        }
      }

      return tienMat;
    }
    return null;
  }, [
    listAllPhuongThucThanhToan,
    thanhTien,
    state.isDuyet,
    phieuThu?.dsPhuongThucTt,
    state.show,
    state.phuongThucTtId,
  ]);
  //handle
  const cancelHandler = () => {
    setState({ show: false });

    refPhuongThucThanhToan.current &&
      refPhuongThucThanhToan.current.resetData();
  };
  const submitHandler = async () => {
    let tongTienPTTT = totalPatientPayment(); // Tổng tiền các PTTT
    if (tongTienPTTT >= thanhTien) {
      let isQrCode = false,
        pttQrId = "";
      const payload = Object.keys(state?.infoPrice).map((key) => {
        let tienPhuongThucTT = 0;
        if (key == tienMatObj?.id) {
          let tongTienPTTKhac = tongTienPTTT - state?.infoPrice[key].tongTien;
          tienPhuongThucTT =
            thanhTien < tongTienPTTKhac ? 0 : thanhTien - tongTienPTTKhac;
        } else {
          tienPhuongThucTT = state?.infoPrice[key].tongTien || 0;
        }
        return {
          phuongThucTtId: key,
          tongTien: tienPhuongThucTT,
          maChuanChi: state?.infoPrice[key].maChuanChi,
          nganHangId: state?.infoPrice[key].nganHangId,
        };
      });
      const filterPayload = payload.filter((itemO) => {
        return itemO.tongTien || itemO.tongTien === 0;
      });

      showLoading();
      try {
        if (state.isHuyQrCode && state.qrThanhToan?.thanhToanId) {
          await huyQrThanhToan({
            thanhToanId: state.qrThanhToan.thanhToanId,
          });
          await sleep(300);
        }

        let res = await postThanhToan({
          id: nguoiBenhId,
          dsPhuongThucTt: filterPayload,
          nhaThuNganId: nhaTamUng?.toaNhaId,
          quayId: nhaTamUng?.id,
          caLamViecId: caLamViec?.id,
        });
        setState({ show: false });
        refCallback2.current && refCallback2.current();
        if (isArray(res, true)) {
          const dsPtt = res.find(
            (i) =>
              i.phuongThucTt.loaiPhuongThucTt === LOAI_PHUONG_THUC_TT.QR_CODE
          );
          if (dsPtt) {
            isQrCode = dsPtt.tongTien > 0 && dsPtt.nganHangId;
            pttQrId = dsPtt.id;
          }
          if (isQrCode) {
            hideLoading();
            refCallback1.current &&
              refCallback1.current({
                nbDotDieuTriId,
                loai: 20,
                tuBanGhiId: pttQrId,
              });
          }
        }
        if (
          dataTU_DONG_BAT_PHIEU_THU_NHA_THUOC?.eval() &&
          (!isQrCode || state.isDuyet)
        ) {
          res = await centralizedErrorHandling(
            inPhieuThuNhaThuoc({
              id: infoPatient?.phieuXuatId || infoPatient?.phieuXuat?.id,
              isGetPdfFile: true,
              maViTri: "01701",
              maPhieuIn: "P561",
            })
          );
          if (res) {
            await printProvider.printPdf(res);
          }
        }

        if (isPhatHoaDon) {
          res = await centralizedErrorHandling(
            getThongTinPhieuThu(phieuThu?.id)
          );
          await inHoaDon({
            hoaDonId: res?.data?.dsHoaDon
              ?.filter(
                (o) => o.trangThai === TRANG_THAI_HOA_DON.HD_DA_PHAT_HANH
              )
              .map((item) => item.hoaDonId),
            dinhDang: dataDINH_DANG_XEM_HOA_DON,
          });
        }
        if (!isQrCode) {
          hideLoading();
        }
      } catch (error) {
        hideLoading();
        console.error(error);
      }
    } else {
      message.error(t("nhaThuoc.soTienNbDuaNhoHonTienPhaiTra"));
    }
  };
  refF4.current = submitHandler;

  const totalPatientPayment = () => {
    let total = 0;
    Object.keys(state?.infoPrice).forEach((ifp) => {
      total += state?.infoPrice[ifp].tongTien || 0;
    });
    return Math.round(total * 100) / 100;
  };

  const calcalatorPriceOfPatient = () => {
    return totalPatientPayment();
  };

  const onChangePhuongThucThanhToan = (infoPrice) => {
    setState({ infoPrice: { ...infoPrice } });
  };

  const calculatorPatientPay = () => {
    // return (tongTien - tienTamUng);
    return thanhTien;
  };

  const calculatorPriceReturn = () => {
    let amountReturn = 0;
    let tongTienPhuongThucTT = totalPatientPayment();

    amountReturn =
      thanhTien < tongTienPhuongThucTT ? tongTienPhuongThucTT - thanhTien : 0;
    return Math.round(amountReturn * 100) / 100;
  };

  return (
    <ModalTemplate
      title={t(
        state?.isDuyet ? "thuNgan.duyetPhieuThu" : "danhMuc.phuongThucThanhToan"
      )}
      rightTitle={
        <div style={{ height: "100%", alignItems: "center" }}>
          <span className="normal-weight">
            {firstLetterWordUpperCase(infoPatient?.nbThongTinChung?.tenNb)}
          </span>
          <span className="normal-weight">
            {" "}
            {infoPatient?.nbThongTinChung?.gioiTinh
              ? GIOI_TINH_BY_VALUE[infoPatient?.nbThongTinChung?.gioiTinh]
              : ""}
          </span>
          {infoPatient?.nbThongTinChung?.tuoi && (
            <span className="normal-weight">
              {" "}
              {infoPatient?.nbThongTinChung?.tuoi
                ? `${infoPatient?.nbThongTinChung?.tuoi} tuổi`
                : ""}
            </span>
          )}
        </div>
      }
      width={800}
      onCancel={cancelHandler}
      ref={refModal}
      actionLeft={<Button.QuayLai onClick={cancelHandler} />}
      actionRight={
        <Button
          type={"primary"}
          onClick={submitHandler}
          rightIcon={<SVG.IcThanhToan />}
        >
          {t("thuNgan.xacNhanThanhToan")}
        </Button>
      }
    >
      <Main>
        <Row className="main-body">
          <Col xs={{ span: 11 }}>
            <div className="box-right">
              <div className="info-price">
                <div className="info-price__title">
                  {t("thuNgan.tienNbDua")}
                </div>
                <div className="info-price__detail">
                  {formatDecimal(calcalatorPriceOfPatient())} đ
                </div>
              </div>
              <div className="info-price">
                <div className="info-price__title">
                  {t("thuNgan.tienMatTraLai")}
                </div>
                <div className="info-price__detail">
                  {calculatorPriceReturn() &&
                    formatDecimal(calculatorPriceReturn())}{" "}
                  đ
                </div>
              </div>
            </div>
          </Col>
          <Col xs={{ span: 11, offset: 1 }}>
            <div className="box-left">
              <div className="info-price">
                <div className="info-price__title">
                  {t("thuNgan.soTienPhieuThu")}
                </div>
                <div className="info-price__detail">
                  {/* {(tongTien || 0).formatPrice()} đ */}
                  {formatDecimal(calculatorPatientPay())} đ
                </div>
              </div>
              <div className="info-price">
                <div className="info-price__title">
                  {t("thuNgan.tienTamUng")}
                </div>
                <div className="info-price__detail">
                  {/* {!isReturn && tienTamUng ? tienTamUng.formatPrice() : 0} đ */}
                  0 đ
                </div>
              </div>
            </div>
          </Col>
          <div className="text-note">
            <b>{t("common.luuY")}:</b> {t("thuNgan.qrPayMoMoKhongTheThanhToan")}{" "}
          </div>
        </Row>

        <Row className="main-body">
          <div className="box-second" key={`${nguoiBenhId}`}>
            <PhuongThucThanhToan
              ref={refPhuongThucThanhToan}
              onChange={onChangePhuongThucThanhToan}
              dsPhuongThucTt={state?.infoPrice}
              toaNhaId={nhaTamUng?.toaNhaId}
              isDonThuoc={true}
            />
          </div>
        </Row>
      </Main>
    </ModalTemplate>
  );
};

export default forwardRef(ModalPayment);
