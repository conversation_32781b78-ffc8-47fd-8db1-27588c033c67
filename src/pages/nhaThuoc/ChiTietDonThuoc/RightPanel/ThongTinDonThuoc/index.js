import React, { memo, useEffect, useMemo, useRef, useState } from "react";
import { Col, Menu, Row, Select as SelectAntd, Space, message } from "antd";
import { SVG } from "assets";
import {
  DatePicker,
  Auth<PERSON>rapper,
  Button,
  Card,
  Dropdown,
  InputTimeout,
  Select,
  ModalSignPrint,
  Checkbox,
} from "components";
import {
  ENUM,
  HOTKEY,
  LOAI_IN,
  LOAI_KHO,
  LOAI_PHUONG_THUC_TT,
  ROLES,
  THIET_LAP_CHUNG,
  TRANG_THAI_HOA_DON,
  TRANG_THAI_HOAN,
  TRANG_THAI_PHIEU_THU_THANH_TOAN,
  TRANG_THAI_THANH_TOAN_QR,
} from "constants/index";
import printProvider, { printJS } from "data-access/print-provider";
import {
  useConfirm,
  useDelayedState,
  useEnum,
  useListAll,
  useLoading,
  useQueryString,
  useRefFunc,
  useStore,
  useThietLap,
} from "hooks";
import { checkRole } from "lib-utils/role-utils";
import moment from "moment";
import ModalSuaPTTT from "pages/thuNgan/chiTietPhieuThu/ModalSuaPTTT";
import ModalTaoQrCode from "pages/thuNgan/quanLyTamUng/chiTietQuanLyTamUng/Modal/ModalTaoQrCode";
import ModalTaoQrCodeLoi from "pages/thuNgan/quanLyTamUng/chiTietQuanLyTamUng/Modal/ModalTaoQrCodeLoi";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { useHistory, useParams } from "react-router-dom";
import { isArray, openInNewTab, parseListConfig, sleep } from "utils/index";
import ModalCanclePayment from "../ModalCanclePayment";
import ModalCancleSubmit from "../ModalCancleSubmit";
import ModalDiscount from "../ModalDiscount";
import ModalHoanThuoc from "../ModalHoanThuoc";
import ModalPayment from "../ModalPayment";
import ModalThongTinHoaDon from "../ModalThongTinHoaDon";
import { BtnActions, GlobalStyle, Main } from "./styled";
import { checkIsPhieuKySo } from "utils/phieu-utils";
import { showError } from "utils/message-utils";
import { isEmpty } from "lodash";
import ModalThongBaoThanhToanQrCode from "pages/thuNgan/chiTietPhieuThu/ModalThongBaoThanhToanQrCode";
import { centralizedErrorHandling } from "lib-utils";
import isofhToolProvider from "data-access/isofh-tool-provider";

const { Option } = SelectAntd;
const ThongTinDonThuoc = ({
  isThemMoi,
  layerId,
  className,
  nhaTamUng,
  caLamViec,
}) => {
  const { showConfirm } = useConfirm();
  const history = useHistory();
  const { id } = useParams();
  const { t } = useTranslation();
  const { showLoading, hideLoading } = useLoading();
  //ref
  const refModalPayment = useRef(null);
  const refModalDiscount = useRef(null);
  const refWarningHuyPhat = useRef(null);
  const refWarningHuyThanhToan = useRef(null);
  const refHoanThuoc = useRef(null);
  const refPhieuIn = useRef(null);
  const refKho = useRef(null);
  const refGhiChu = useRef(null);
  const refSelectRow = useRef(null);
  const refEnterInPhieu = useRef(null);
  const refTuVanDon = useRef(null);
  const refModalThongTinHoaDon = useRef(null);
  const refPrintPhieuXuatBan = useRef(null);
  const refModalTaoQrCode = useRef(null);
  const refModalTaoQrCodeLoi = useRef(null);
  const refModalSignPrint = useRef(null);
  const refF4 = useRef();
  const refF12 = useRef();
  const refModalSuaPTTT = useRef(null);
  const refPtttChanged = useRef(false);
  const refTimeoutQr = useRef(null);
  const refModalThongBaoThanhToanQrCode = useRef(null);
  const refCurrentMsg = useRef(null);
  const refBtnPhat = useRef(null);

  // get
  const listKhoUser = useStore("kho.listKhoThuocUser", []);
  const thongTinPhieuThu = useStore("thuNgan.thongTinPhieuThu", {});
  const listAllDoiTacThanhToan = useStore("doiTac.listAllDoiTacThanhToan", []);

  const [dataDINH_DANG_XEM_HOA_DON] = useThietLap(
    THIET_LAP_CHUNG.DINH_DANG_XEM_HOA_DON
  );

  const [dataCANH_BAO_DUYET_DLS_KHI_PHAT_THUOC_NHA_THUOC] = useThietLap(
    THIET_LAP_CHUNG.CANH_BAO_DUYET_DLS_KHI_PHAT_THUOC_NHA_THUOC
  );
  const [dataCANH_BAO_SL0_LUU_TU_VAN_DON_NT] = useThietLap(
    THIET_LAP_CHUNG.CANH_BAO_SL0_LUU_TU_VAN_DON_NT,
    "false"
  );
  const [dataHIEN_THI_PHIEU_XUAT_BAN] = useThietLap(
    THIET_LAP_CHUNG.HIEN_THI_PHIEU_XUAT_BAN,
    "false"
  );
  const [dataPHAT_THUOC_NHA_THUOC_KHONG_CAN_THANH_TOAN] = useThietLap(
    THIET_LAP_CHUNG.PHAT_THUOC_NHA_THUOC_KHONG_CAN_THANH_TOAN,
    "false"
  );
  const [dataTU_DONG_THANH_TOAN_PHIEU_THU_TIEN_MAT] = useThietLap(
    THIET_LAP_CHUNG.TU_DONG_THANH_TOAN_PHIEU_THU_TIEN_MAT
  );
  const [SUA_PTTT_CHO_PHIEU_THU_DA_XUAT_HDDT] = useThietLap(
    THIET_LAP_CHUNG.SUA_PTTT_CHO_PHIEU_THU_DA_XUAT_HDDT,
    "true"
  );
  const [dataKHONG_CHO_HUY_PHAT_THUOC_DA_THANH_TOAN] = useThietLap(
    THIET_LAP_CHUNG.KHONG_CHO_HUY_PHAT_THUOC_DA_THANH_TOAN,
    "true"
  );
  const [dataPHAT_HANH_HOA_DON_NHA_THUOC_KHI_THANH_TOAN] = useThietLap(
    THIET_LAP_CHUNG.PHAT_HANH_HOA_DON_NHA_THUOC_KHI_THANH_TOAN,
    "false"
  );
  const [dataPHUONG_THUC_THANH_TOAN_MAC_DINH] = useThietLap(
    THIET_LAP_CHUNG.PHUONG_THUC_THANH_TOAN_MAC_DINH
  );
  const [dataTU_DONG_BAT_PHIEU_THU_NHA_THUOC] = useThietLap(
    THIET_LAP_CHUNG.TU_DONG_BAT_PHIEU_THU_NHA_THUOC
  );
  const [dataMA_DOI_TAC_QRCODE_CAP_NHAT_GIAO_DICH] = useThietLap(
    THIET_LAP_CHUNG.MA_DOI_TAC_QRCODE_CAP_NHAT_GIAO_DICH
  );
  const [dataMA_PHUONG_THUC_TT_QRCODE_MAC_DINH] = useThietLap(
    THIET_LAP_CHUNG.MA_PHUONG_THUC_TT_QRCODE_MAC_DINH
  );
  const isPhatHoaDon = dataPHAT_HANH_HOA_DON_NHA_THUOC_KHI_THANH_TOAN?.eval();
  const isXuatHoaDon = !dataPHAT_HANH_HOA_DON_NHA_THUOC_KHI_THANH_TOAN?.eval();
  const { listAllPhuongThucThanhToan } = useSelector(
    (state) => state.phuongThucTT
  );

  const {
    isAdvised,
    nguoiBenhId,
    selectedDonThuoc,
    infoPatient,
    dsThuocTamThoi,
    dsThuocEdit,
  } = useSelector((state) => state.thuocChiTiet);
  const [listTrangThaiThuoc] = useEnum(ENUM.TRANG_THAI_THUOC);
  const [listTrangThaiHoan] = useEnum(ENUM.TRANG_THAI_HOAN);

  const [listTrangThaiThanhToan] = useEnum(ENUM.TRANG_THAI_THANH_TOAN);
  const [listAllNhanVien] = useListAll("nhanVien", {}, true);
  const [listAllToaNha] = useListAll("toaNha", {}, true);
  const [dsKhoId] = useQueryString("dsKhoId", "");

  const {
    kho: { getTheoTaiKhoan },
    thuocChiTiet: {
      onSaveDonThuoc,
      updateData,
      postThanhToan,
      updateGhiChuDonThuocById,
      postHuyDuyet,
      postHuyThanhToan,
      searchDonThuoc,
      updateThoiGianPhat,
      inPhieuThuNhaThuoc,
    },
    phimTat: { onRegisterHotkey },
    phieuIn: { getListPhieu, getFilePhieuIn, showFileEditor },
    thuNgan: {
      huyQrThanhToan,
      taoQrThanhToan,
      xuatHoaDonNhap,
      kiemTraTrangThaiThanhToanQr,
      getThongTinPhieuThu,
      kiemTraGiaoDich,
    },
    dsHoaDonDienTu: { inHoaDon },
    phuongThucTT: { getListAllPhuongThucThanhToan },
    doiTac: { getListAllDoiTacThanhToan },
    nbDotDieuTri: { getThongTinCoBan },
  } = useDispatch();
  const { phieuXuat, nbDotDieuTriId } = infoPatient || {};
  const [refIsChange, onChangeStateUpdate] = useDelayedState(1500);

  const [state, _setState] = useState({
    editTimeThanhToan: false,
    editTimeDuyet: false,
    editNguoiDuyet: false,
    editThuNgan: false,
    showBtnXuatHoaDonNhap: true,
  });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };
  const [reloadEffectPttt, setReloadEffectPttt] = useState(false);
  const [phuongThucTtId, setPhuongThucTtId] = useState();
  const [ptttMacDinhTheoThietLap, setPtttMacDinhTheoThietLap] = useState(); // convert từ mã PHUONG_THUC_THANH_TOAN_MAC_DINH sang id

  const tongTien = useMemo(() => {
    let data = isThemMoi ? dsThuocTamThoi : dsThuocEdit;
    return (
      data.length &&
      data.reduce(
        (total, item) => (total = total + (item?.nbDichVu?.tienNbTuTra || 0)),
        0
      )
    );
  }, [isThemMoi, dsThuocTamThoi, dsThuocEdit]);

  const isVangLai = useMemo(() => {
    return !phieuXuat?.bacSiChiDinhId;
  }, [phieuXuat]);

  const showBtnHuyPhat = useMemo(() => {
    // Nếu danh sách dịch vụ có ít nhất 1 dịch vụ có trạng thái hoàn = Đã hoàn thì ẩn button Huỷ phát
    let show = true;
    if (isArray(infoPatient?.dsThuoc, true)) {
      show = !infoPatient.dsThuoc.some(
        (item) => item?.nbDichVu?.trangThaiHoan === TRANG_THAI_HOAN.DA_HOAN
      );
    }
    return show;
  }, [infoPatient?.dsThuoc]);

  useEffect(() => {
    getTheoTaiKhoan({
      dsLoaiKho: [LOAI_KHO.NHA_THUOC, LOAI_KHO.BAN_THUOC],
      stateKey: "listKhoThuocUser",
    });
    getListAllPhuongThucThanhToan({ active: true, page: "", size: "" });
    getListAllDoiTacThanhToan({ page: "", size: "", active: true });

    // đăng ký phím tắt
    onRegisterHotkey({
      layerId,
      hotKeys: [
        {
          keyCode: HOTKEY.F3,
          onEvent: () => {
            if (refPhieuIn.current) {
              refPhieuIn.current.click();
              document.getElementById("button-in-giay-to").focus();
            }
          },
        },
        {
          keyCode: HOTKEY.F4,
          onEvent: () => {
            refF4.current && refF4.current();
          },
        },
        {
          keyCode: HOTKEY.F6,
          onEvent: () => {
            onPrintPhieuThuNhaThuoc();
          },
        },
        {
          keyCode: HOTKEY.F7,
          onEvent: () => {
            if (isThemMoi) {
              setState({ openSelectKho: true });
              refKho.current && refKho.current.focus();
            }
          },
        },
        {
          keyCode: HOTKEY.F8,
          onEvent: () => {
            refTuVanDon.current && refTuVanDon.current.click();
          },
        },
        {
          keyCode: HOTKEY.F9,
          onEvent: () => {
            refGhiChu.current && refGhiChu.current.focus();
          },
        },
        {
          keyCode: HOTKEY.F10,
          onEvent: (e) => {
            refEnterInPhieu.current && refEnterInPhieu.current();
          },
        },
        {
          keyCode: HOTKEY.F11,
          onEvent: () => {
            refPrintPhieuXuatBan.current &&
              refPrintPhieuXuatBan.current.click();
          },
        },
        {
          keyCode: HOTKEY.F12,
          onEvent: () => {
            if (refF12.current) refF12.current();
          },
        },
        {
          keyCode: HOTKEY.UP,
          onEvent: (e) => {
            refSelectRow.current && refSelectRow.current(-1);
          },
        },
        {
          keyCode: HOTKEY.DOWN,
          onEvent: (e) => {
            refSelectRow.current && refSelectRow.current(1);
          },
        },
        {
          keyCode: HOTKEY.PAGE_DOWN,
          onEvent: () => {
            refBtnPhat.current && refBtnPhat.current.click();
          },
        },
      ],
    });
  }, []);

  useEffect(() => {
    return () => {
      refPtttChanged.current = false;
      clearFunc();
    };
  }, []);

  useEffect(() => {
    if (
      dataPHUONG_THUC_THANH_TOAN_MAC_DINH &&
      isArray(listAllPhuongThucThanhToan, true)
    ) {
      let ptttId = listAllPhuongThucThanhToan.find(
        (i) => i.ma === dataPHUONG_THUC_THANH_TOAN_MAC_DINH
      )?.id;
      if (ptttId) {
        setPhuongThucTtId(+ptttId);
        setPtttMacDinhTheoThietLap(+ptttId);
      }
    }
  }, [
    dataPHUONG_THUC_THANH_TOAN_MAC_DINH,
    listAllPhuongThucThanhToan,
    reloadEffectPttt,
  ]);

  const qrThanhToan = useMemo(() => {
    let result = null,
      dsPhuongThucTt = infoPatient?.phieuThu?.dsPhuongThucTt;
    if (isArray(dsPhuongThucTt, true)) {
      result = dsPhuongThucTt.find(
        (i) => i.loaiPhuongThucTt === LOAI_PHUONG_THUC_TT.QR_CODE
      );
    }
    return result;
  }, [infoPatient?.phieuThu?.dsPhuongThucTt]);

  const hoaDonDaPhatHanhId = useMemo(() => {
    if (!isArray(infoPatient?.phieuThu?.dsHoaDon, true)) return null;
    return infoPatient.phieuThu.dsHoaDon.find(
      (i) => i.trangThai === TRANG_THAI_HOA_DON.HD_DA_PHAT_HANH
    )?.hoaDonId;
  }, [infoPatient?.phieuThu?.dsHoaDon]);

  const showBtnDuyet = useMemo(() => {
    let result = false;
    if (qrThanhToan) {
      result = [
        TRANG_THAI_THANH_TOAN_QR.MOI,
        TRANG_THAI_THANH_TOAN_QR.TAO_QR,
        TRANG_THAI_THANH_TOAN_QR.THANH_TOAN,
        TRANG_THAI_THANH_TOAN_QR.TAO_QR_LOI,
      ].includes(qrThanhToan.trangThaiThanhToan);
    }
    return result;
  }, [qrThanhToan, TRANG_THAI_THANH_TOAN_QR]);

  const showBtnCapNhatGiaoDichQr = useMemo(() => {
    let listMaDoiTac = parseListConfig(
      dataMA_DOI_TAC_QRCODE_CAP_NHAT_GIAO_DICH
    );
    if (
      !qrThanhToan ||
      !isArray(listAllDoiTacThanhToan, true) ||
      !isArray(listMaDoiTac, true)
    ) {
      return false;
    }
    let doiTac =
      listAllDoiTacThanhToan.find((x) => x.id === qrThanhToan.nganHangId) || {};

    return (
      listMaDoiTac.includes(doiTac.ma) &&
      [TRANG_THAI_THANH_TOAN_QR.MOI, TRANG_THAI_THANH_TOAN_QR.TAO_QR].includes(
        qrThanhToan.trangThaiThanhToan
      )
    );
  }, [
    qrThanhToan,
    dataMA_DOI_TAC_QRCODE_CAP_NHAT_GIAO_DICH,
    listAllDoiTacThanhToan,
  ]);

  const onThanhToanQrThanhCong = async () => {
    searchDonThuoc(id);
    if (
      dataMA_PHUONG_THUC_TT_QRCODE_MAC_DINH ===
      listAllPhuongThucThanhToan.find(
        (i) => i.id === qrThanhToan?.phuongThucTtId
      )?.ma
    ) {
      const res = await centralizedErrorHandling(
        getThongTinPhieuThu(infoPatient?.phieuThu?.id)
      );
      await inHoaDon({
        hoaDonId: res?.data?.dsHoaDon
          ?.filter((o) => o.trangThai === TRANG_THAI_HOA_DON.HD_DA_PHAT_HANH)
          .map((item) => item.hoaDonId),
        dinhDang: dataDINH_DANG_XEM_HOA_DON,
      });
    }
  };

  const kiemTraTrangThaiQrThanhToan = async () => {
    try {
      let params = {
        nbDotDieuTriId,
        loai: 20,
        tuBanGhiId: qrThanhToan?.id,
      };
      const res = await kiemTraTrangThaiThanhToanQr(params);
      const { data } = res || {};
      const message = data?.phanHoi?.message;
      if (data?.trangThai === TRANG_THAI_THANH_TOAN_QR.THANH_TOAN) {
        // Show popup thành công
        refTimeoutQr.current && clearInterval(refTimeoutQr.current);
        isofhToolProvider.putDataCenter({
          ma: "QR_DATA",
          value: { ...qrThanhToan, trangThaiThanhToan: data.trangThai },
        });

        // ẩn popup qr xem qr
        refModalTaoQrCode.current && refModalTaoQrCode.current.hide();

        // show popup thanh toán qr thành công
        refModalThongBaoThanhToanQrCode.current &&
          refModalThongBaoThanhToanQrCode.current.show(
            {
              ...data,
              type: "success",
              title: t("thuNgan.thanhToanThanhCong"),
            },
            () => {
              onThanhToanQrThanhCong();
            }
          );
      } else {
        const showWarningPopup = () => {
          refModalThongBaoThanhToanQrCode.current &&
            refModalThongBaoThanhToanQrCode.current.show({
              ...data,
              type: "warning",
              title: t("common.canhBao"),
            });
        };
        if (message === null) {
          refCurrentMsg.current = message;
        } else if (!!message) {
          if (
            refCurrentMsg.current === null &&
            refCurrentMsg.current !== message
          ) {
            // Show popup lỗi
            showWarningPopup();
          } else if (refCurrentMsg.current !== message) {
            // Show popup lỗi
            showWarningPopup();
          }
          refCurrentMsg.current = message;
        }
      }
    } catch (error) {}
  };

  useEffect(() => {
    if (
      qrThanhToan &&
      qrThanhToan.trangThaiThanhToan === TRANG_THAI_THANH_TOAN_QR.TAO_QR
    ) {
      kiemTraTrangThaiQrThanhToan();

      refTimeoutQr.current = setInterval(() => {
        kiemTraTrangThaiQrThanhToan();
      }, 3000);

      return () => {
        if (refTimeoutQr.current) clearInterval(refTimeoutQr.current);
      };
    }
  }, [qrThanhToan]);

  useEffect(() => {
    if (
      qrThanhToan &&
      [
        TRANG_THAI_THANH_TOAN_QR.MOI,
        TRANG_THAI_THANH_TOAN_QR.TAO_QR,
        TRANG_THAI_THANH_TOAN_QR.TAO_QR_LOI,
      ].includes(qrThanhToan.trangThaiThanhToan) &&
      qrThanhToan.phuongThucTtId
    ) {
      setPhuongThucTtId(qrThanhToan.phuongThucTtId);
    }
  }, [qrThanhToan]);

  useEffect(() => {
    if (
      dataPHUONG_THUC_THANH_TOAN_MAC_DINH &&
      isArray(listAllPhuongThucThanhToan, true)
    ) {
      let isTienMat = listAllPhuongThucThanhToan.find(
        (i) => i.ma === dataPHUONG_THUC_THANH_TOAN_MAC_DINH
      )?.tienMat;
      if (
        refPtttChanged.current &&
        isTienMat &&
        dataTU_DONG_THANH_TOAN_PHIEU_THU_TIEN_MAT?.eval() &&
        phuongThucTtId &&
        ptttMacDinhTheoThietLap !== phuongThucTtId &&
        (infoPatient?.phieuXuat?.trangThai === 10 ||
          infoPatient?.phieuXuat?.trangThai === 15 ||
          infoPatient?.phieuXuat?.trangThai === 20) &&
        !showBtnDuyet &&
        checkRole([ROLES["NHA_THUOC"].THANH_TOAN_DON_THUOC]) &&
        infoPatient?.phieuThu
      ) {
        onThanhToan();
        refPtttChanged.current = false;
      }
    }
  }, [
    ptttMacDinhTheoThietLap,
    phuongThucTtId,
    dataPHUONG_THUC_THANH_TOAN_MAC_DINH,
    listAllPhuongThucThanhToan,
    dataTU_DONG_THANH_TOAN_PHIEU_THU_TIEN_MAT,
    refPtttChanged,
    infoPatient,
    showBtnDuyet,
  ]);

  useEffect(() => {
    if (
      infoPatient?.phieuthu?.thanhToan !==
        TRANG_THAI_PHIEU_THU_THANH_TOAN.DA_THANH_TOAN &&
      isArray(listAllPhuongThucThanhToan, true) &&
      qrThanhToan &&
      [
        TRANG_THAI_THANH_TOAN_QR.MOI,
        TRANG_THAI_THANH_TOAN_QR.TAO_QR,
        TRANG_THAI_THANH_TOAN_QR.TAO_QR_LOI,
      ].includes(qrThanhToan.trangThaiThanhToan) &&
      refPtttChanged.current
    ) {
      let notQrcode =
        listAllPhuongThucThanhToan.find((i) => i.id === phuongThucTtId)
          ?.loaiPhuongThucTt !== LOAI_PHUONG_THUC_TT.QR_CODE;
      if (notQrcode) {
        onThanhToan(false, true);
        refPtttChanged.current = false;
      }
    }
  }, [
    phuongThucTtId,
    listAllPhuongThucThanhToan,
    refPtttChanged,
    qrThanhToan,
    infoPatient,
  ]);

  const clearFunc = () => {
    isofhToolProvider.putDataCenter({ ma: "QR_VALUE", value: null });
    isofhToolProvider.putDataCenter({ ma: "TT_NB", value: {} });
  };

  refSelectRow.current = (index) => {
    const indexNextItem =
      (state?.listPhieu?.findIndex((item) => item.ma === state?.baoCao?.ma) ||
        0) + index;
    if (-1 < indexNextItem && indexNextItem < state?.listPhieu?.length) {
      setState({
        baoCao: state?.listPhieu[indexNextItem],
      });
    }
  };

  useEffect(() => {
    if (nbDotDieuTriId && id)
      getListPhieu({
        nbDotDieuTriId: nbDotDieuTriId,
        maManHinh: "017",
        maViTri: "01701",
        chiDinhTuDichVuId: id,
      }).then((res) => {
        setState({
          listPhieu: res || [],
        });
      });
  }, [nbDotDieuTriId, id]);

  useEffect(() => {
    if (nbDotDieuTriId) {
      getThongTinCoBan(nbDotDieuTriId);
    }
  }, [nbDotDieuTriId]);

  //Đẩy thông tin người bệnh
  useEffect(() => {
    if (!isEmpty(infoPatient?.nbThongTinChung))
      isofhToolProvider.putDataCenter({
        ma: "TT_NB",
        value: infoPatient?.nbThongTinChung,
      });
  }, [infoPatient?.nbThongTinChung]);

  refF12.current = () => {
    if (
      infoPatient?.phieuThu?.thanhToan !==
        TRANG_THAI_PHIEU_THU_THANH_TOAN.DA_THANH_TOAN &&
      (infoPatient?.phieuXuat?.trangThai === 10 ||
        infoPatient?.phieuXuat?.trangThai === 15 ||
        infoPatient?.phieuXuat?.trangThai === 20) &&
      refModalPayment.current
    )
      onProcess("thanhToan", showBtnDuyet);
  };

  useEffect(() => {
    if (dsKhoId) {
      const dsKho = dsKhoId.split(",").map(Number);
      // default khoId , nếu kho chỉ có 1 giá trị
      if (dsKho.length === 1) {
        setState({ khoId: dsKho[0] });
        updateData({ khoId: dsKho[0] });
      }
    }
  }, [dsKhoId]);

  useEffect(() => {
    if (isThemMoi && dsKhoId === "" && listKhoUser.length === 1) {
      setState({ khoId: listKhoUser[0].id });
      updateData({ khoId: listKhoUser[0].id });
    }
  }, [isThemMoi, dsKhoId, listKhoUser]);

  let khoOption = useMemo(() => {
    let options = listKhoUser?.map((item, index) => (
      <Option key={index} value={item?.id}>
        {item?.ten}
      </Option>
    ));
    return options;
  }, [listKhoUser]);

  const showBtnXuatHDDT = useMemo(() => {
    return (
      infoPatient?.phieuThu?.trangThaiHoaDon ===
        TRANG_THAI_HOA_DON.HD_TAO_MOI &&
      infoPatient?.phieuThu?.thanhToan ===
        TRANG_THAI_PHIEU_THU_THANH_TOAN.DA_THANH_TOAN
    );
  }, [infoPatient]);

  const isShowXuatHoaDonNhap = useMemo(() => {
    return (
      infoPatient?.phieuThu?.trangThaiHoaDon ===
        TRANG_THAI_HOA_DON.HD_TAO_MOI &&
      infoPatient?.phieuThu?.thanhToan ===
        TRANG_THAI_PHIEU_THU_THANH_TOAN.DA_THANH_TOAN &&
      state.showBtnXuatHoaDonNhap
    );
  }, [infoPatient, state.showBtnXuatHoaDonNhap]);

  const onThanhToan = (isDuyet, isHuyQrCode) => {
    onProcess("thanhToan", isDuyet, isHuyQrCode);
  };
  const onTuVanDon = () => {
    updateData({ isAdvised: true });
  };

  const handleBlurInput = () => {
    const activeElement = document.activeElement;
    const inputs = ["input", "select", "button", "textarea"];

    if (
      activeElement &&
      inputs.indexOf(activeElement.tagName.toLowerCase()) !== -1
    ) {
      activeElement.blur();
    }
  };

  const onChangePtTtId = (value) => {
    setPhuongThucTtId(value);
    refPtttChanged.current = true;
  };

  const onSave = (onPrintCb = null) => {
    handleBlurInput();
    function onSaveContinue(boQuaChuaThanhToan) {
      showLoading();
      onSaveDonThuoc({ history, isThemMoi, isVangLai, boQuaChuaThanhToan })
        .then((res) => {
          const { phieuXuatId, code, message } = res;
          hideLoading();
          if (code == 7921) {
            showConfirm(
              {
                title: t("common.canhBao"),
                content: message,
                cancelText: t("common.huy"),
                okText: t("common.xacNhan"),
                showBtnOk: true,
                typeModal: "warning",
                classNameOkText: "button-warning",
              },
              () => {
                onSaveContinue(true);
              }
            );
          } else if (id || phieuXuatId) {
            searchDonThuoc(id || phieuXuatId);

            //gọi func callback in đơn thuốc
            if (onPrintCb) {
              onPrintCb();
            }
          }
        })
        .catch((err) => {
          hideLoading();
        });
    }
    if (
      isAdvised &&
      dataCANH_BAO_SL0_LUU_TU_VAN_DON_NT?.eval() &&
      (dsThuocEdit || []).some((item) => item.nbDichVu?.soLuong == 0)
    ) {
      const listThuocSLBangKhong = (dsThuocEdit || []).filter(
        (item) => item.nbDichVu?.soLuong == 0
      );
      showConfirm(
        {
          title: t("common.canhBao"),
          content: `${t(
            "nhaThuoc.hangHoaCoSoLuongBanBang0"
          )}: ${listThuocSLBangKhong
            .map((x) => x.nbDichVu?.tenDichVu)
            .join(", ")}`,
          cancelText: t("common.dong"),
          okText: t("common.luu"),
          showBtnOk: true,
          typeModal: "warning",
          classNameOkText: "button-warning",
        },
        () => {
          onSaveContinue();
        },
        () => {}
      );
    } else {
      onSaveContinue();
    }
  };

  refF4.current = onSave;
  const onChange = (key) => (e) => {
    const value = (e?.target && e.target.value) || e;
    setState({ [key]: value });
    if (key === "khoId") {
      updateData({ khoId: value });
    }
  };
  const onHoanThuoc = (e) => {
    const data = infoPatient?.dsThuoc?.filter(
      (itemThuoc) => itemThuoc?.nbDichVu?.trangThaiHoan === 0
    );
    refHoanThuoc.current && refHoanThuoc.current.show(data, null, (e) => {});
  };
  const onHuyHoanThuoc = (data) => {
    refHoanThuoc.current &&
      refHoanThuoc.current.show(data, null, (e) => {}, null, {
        type: "huyHoan",
      });
  };

  const confirmAndHuyPhat = (options = {}) => {
    refWarningHuyPhat.current &&
      refWarningHuyPhat.current.show({}, (e) => {
        const payload = {
          id: nguoiBenhId,
          lyDo: e.valueInput,
          ...options,
        };

        postHuyDuyet(payload).then(() => {
          window.location.href = window.location.href;
        });
      });
  };

  const onHuyPhat = () => {
    const trangThaiHoaDon = infoPatient?.phieuThu?.trangThaiHoaDon;
    const coQuyenHuyHoaDon = checkRole([ROLES["NHA_THUOC"].HUY_HOA_DON]);

    if (trangThaiHoaDon === TRANG_THAI_HOA_DON.HD_DA_PHAT_HANH) {
      if (!coQuyenHuyHoaDon) {
        showConfirm(
          {
            title: t("nhaThuoc.xacNhanHuyThanhToanPhieuThu"),
            content: t(
              "nhaThuoc.canhBaoPhieuThuDaPhatHanhHoaDonHuyThanhToanVaTaoHoaDonDieuChinh"
            ),
            okText: t("common.xacNhan"),
            cancelText: t("common.huy"),
            typeModal: "error",
            classNameOkText: "button-error",
            showBtnOk: true,
          },
          () => {
            confirmAndHuyPhat();
          },
          () => {}
        );
      } else {
        let giuLaiHoaDon = false;

        showConfirm(
          {
            title: t("nhaThuoc.xacNhanHuyThanhToanPhieuThu"),
            content: (
              <div
                style={{
                  fontWeight: 600,
                }}
              >
                {t("nhaThuoc.canhBaoPhieuThuDaPhatHanhHoaDonHuyThanhToan")}
                <Checkbox onChange={(e) => (giuLaiHoaDon = e.target.checked)}>
                  <span
                    style={{
                      color: "rgba(0, 0, 0, 0.85)",
                    }}
                  >
                    {t("nhaThuoc.giuLaiHoaDonDienTuDaPhatHanh")}
                  </span>
                </Checkbox>
              </div>
            ),
            isContentElement: true,
            showBtnOk: true,
            okText: t("common.xacNhan"),
            cancelText: t("common.huy"),
            typeModal: "error",
            classNameOkText: "button-error",
          },
          () => {
            confirmAndHuyPhat({
              dieuChinhHoaDon: !giuLaiHoaDon,
            });
          },
          () => {}
        );
      }
    } else {
      confirmAndHuyPhat();
    }
  };

  const onHuyThanhToan = () => {
    refWarningHuyThanhToan.current.show({}, (e) => {
      postHuyThanhToan({
        id: nguoiBenhId,
        lyDo: e.valueInput,
      }).then(() => {
        window.location.href = window.location.href;
      });
    });
  };
  const onConfirm = (callback) =>
    showConfirm(
      {
        title: t("common.canhBao"),
        content: `${t("kho.donThuocChuaDuocDuyetLamSang")}.\r\n${t(
          "kho.tiepTucPhatThuoc"
        )}?`,
        cancelText: t("common.dong"),
        okText: t("kho.phatThuoc.title"),
        showBtnOk: true,
        typeModal: "warning",
        classNameOkText: "button-warning",
      },
      () => {
        callback();
      },
      () => {}
    );
  const onPhat = () => {
    onProcess("phatThuoc");
  };

  const onCapNhatTrangThaiGiaoDichQr = async () => {
    try {
      showLoading();

      await kiemTraGiaoDich(qrThanhToan.thanhToanId);
      await sleep(300);

      searchDonThuoc(id);
    } catch (error) {
      console.error(error);
    } finally {
      hideLoading();
    }
  };

  const onTaoQrThanhToan = ({ nbDotDieuTriId, loai, tuBanGhiId }) => {
    showLoading({ title: t("thuNgan.dangGenQrCode"), width: 300 });
    taoQrThanhToan({ nbDotDieuTriId, loai, tuBanGhiId })
      .then((res) => {
        //get lại để lấy thông tin QR
        searchDonThuoc(id);

        const { trangThai, qr, phanHoi } = res?.data || {};
        if (phanHoi && phanHoi.code !== "00") {
          refModalTaoQrCodeLoi.current &&
            refModalTaoQrCodeLoi.current.show(
              { message: phanHoi?.message, isPhieuThu: true },
              () => {
                onTaoQrThanhToan({ nbDotDieuTriId, loai, tuBanGhiId });
              },
              () => {}
            );
        } else {
          if (
            [
              TRANG_THAI_THANH_TOAN_QR.MOI,
              TRANG_THAI_THANH_TOAN_QR.TAO_QR,
              TRANG_THAI_THANH_TOAN_QR.THANH_TOAN,
            ].includes(trangThai)
          ) {
            if (
              infoPatient?.phieuThu?.thanhToan !==
              TRANG_THAI_PHIEU_THU_THANH_TOAN.DA_THANH_TOAN
            ) {
              isofhToolProvider.putDataCenter({
                ma: "QR_DATA",
                value: res?.data,
              });
            }

            refModalTaoQrCode.current &&
              refModalTaoQrCode.current.show({ qrData: qr }, () => {
                // window.location.href = window.location.href;
              });
          } else if (TRANG_THAI_THANH_TOAN_QR.TAO_QR_LOI === trangThai) {
            refModalTaoQrCodeLoi.current &&
              refModalTaoQrCodeLoi.current.show(
                { isPhieuThu: true },
                () => {
                  onTaoQrThanhToan({ nbDotDieuTriId, loai, tuBanGhiId });
                },
                () => {}
              );
          }
        }
      })
      .catch((err) => {
        refModalTaoQrCodeLoi.current &&
          refModalTaoQrCodeLoi.current.show(
            { message: err?.message, isPhieuThu: true },
            () => {
              onTaoQrThanhToan({ nbDotDieuTriId, loai, tuBanGhiId });
            },
            () => {}
          );
      })
      .finally(() => {
        hideLoading();
      });
  };

  const onClickHoaDonChuyenDoi = async () => {
    showLoading();
    try {
      await inHoaDon({
        hoaDonId: hoaDonDaPhatHanhId,
        dinhDang: dataDINH_DANG_XEM_HOA_DON,
        chuyenDoi: true,
      });
    } catch (error) {
      console.error(error);
    } finally {
      hideLoading();
    }
  };

  const onThanhToanDonThuoc = async (isDuyet, isHuyQrCode) => {
    if (
      dataTU_DONG_THANH_TOAN_PHIEU_THU_TIEN_MAT?.eval() &&
      phuongThucTtId === ptttMacDinhTheoThietLap &&
      !isHuyQrCode
    ) {
      let thanhTien = Math.round(
        tongTien - infoPatient?.phieuThu?.tienMienGiam || 0
      );
      const dsPhuongThucTt = listAllPhuongThucThanhToan.reduce((acc, cur) => {
        if (cur.id === phuongThucTtId)
          return [{ phuongThucTtId: cur.id, tongTien: thanhTien }];
        return [...acc];
      }, []);
      try {
        let res = await postThanhToan({
          id: nguoiBenhId,
          dsPhuongThucTt,
          quayId: nhaTamUng?.id,
          caLamViecId: caLamViec?.id,
          nhaThuNganId: nhaTamUng?.toaNhaId,
        });
        if (dataTU_DONG_BAT_PHIEU_THU_NHA_THUOC?.eval()) {
          res = await centralizedErrorHandling(
            inPhieuThuNhaThuoc({
              id: infoPatient?.phieuXuatId || infoPatient?.phieuXuat?.id,
              isGetPdfFile: true,
              maViTri: "01701",
              maPhieuIn: "P561",
            })
          );
          if (res) {
            await printProvider.printPdf(res);
          }
        }

        if (isPhatHoaDon) {
          res = await centralizedErrorHandling(
            getThongTinPhieuThu(infoPatient?.phieuThu?.id)
          );
          await inHoaDon({
            hoaDonId: res?.data?.dsHoaDon
              ?.filter(
                (o) => o.trangThai === TRANG_THAI_HOA_DON.HD_DA_PHAT_HANH
              )
              .map((item) => item.hoaDonId),
            dinhDang: dataDINH_DANG_XEM_HOA_DON,
          });
        }
      } catch (error) {
        console.error(error?.message || error);
      } finally {
        hideLoading();
      }
    } else {
      refModalPayment.current.show(
        { isDuyet, phuongThucTtId, isHuyQrCode, qrThanhToan },
        ({ nbDotDieuTriId, loai, tuBanGhiId }) => {
          if (!isDuyet) {
            setTimeout(() => {
              onTaoQrThanhToan({ nbDotDieuTriId, loai, tuBanGhiId });
            }, 301);
          }
        },
        () => {
          setReloadEffectPttt(!reloadEffectPttt);
        }
      );
    }
  };

  const onProcess = (type, isDuyet, isHuyQrCode) => {
    let func;
    if (type === "phatThuoc") {
      func = async () => {
        let list = infoPatient?.phieuThu?.dsPhuongThucTt?.reduce(
          (init, item) => {
            return Object.assign(init, {
              phuongThucTtId: item.phuongThucTtId,
              tongTien: Math.round(item.tongTien),
            });
          },
          []
        );
        showLoading();
        try {
          await postThanhToan(
            {
              id: nguoiBenhId,
              ...(!dataPHAT_THUOC_NHA_THUOC_KHONG_CAN_THANH_TOAN?.eval()
                ? {
                    dsPhuongThucTt: list,
                    quayId: nhaTamUng?.id,
                    caLamViecId: caLamViec?.id,
                    nhaThuNganId: nhaTamUng?.toaNhaId,
                  }
                : {
                    phatThuocKhongCanThanhToan: true,
                  }),
            },
            state.infoPrice
          );
          await updateData({ focusSoPhieu: true });
        } catch (error) {
          console.error(error?.message || error);
        } finally {
          hideLoading();
        }
      };
    } else if (type === "thanhToan") {
      func = async () => {
        showLoading();
        try {
          await onSaveDonThuoc({ history, isThemMoi, isVangLai });
          await onThanhToanDonThuoc(isDuyet, isHuyQrCode);
        } catch (error) {
          console.error(error);
        } finally {
          hideLoading();
        }
      };
    }

    const action = () =>
      dataCANH_BAO_DUYET_DLS_KHI_PHAT_THUOC_NHA_THUOC?.eval() &&
      [null, 25].includes(phieuXuat.trangThaiDls) &&
      !infoPatient.nbThongTinChung?.ngoaiVien
        ? onConfirm(func)
        : func();
    action();
  };

  const onXemQrCode = () => {
    const { qrThanhToan: qrData } = qrThanhToan;

    if (
      infoPatient?.phieuThu?.thanhToan !==
      TRANG_THAI_PHIEU_THU_THANH_TOAN.DA_THANH_TOAN
    ) {
      isofhToolProvider.putDataCenter({ ma: "QR_DATA", value: qrThanhToan });
    }

    refModalTaoQrCode.current &&
      refModalTaoQrCode.current.show({ qrData }, () => {
        if (
          infoPatient?.phieuThu?.thanhToan !==
          TRANG_THAI_PHIEU_THU_THANH_TOAN.DA_THANH_TOAN
        ) {
          isofhToolProvider.putDataCenter({ ma: "QR_DATA", value: null });
        }
      });
  };

  const onHuyQrCode = () => {
    const { thanhToanId, phieuThuId } = qrThanhToan || {};
    showConfirm(
      {
        title: "",
        content: `${t("thuNgan.quanLyTamUng.xacNhanHuyQrCode")}`,
        cancelText: t("common.quayLai"),
        okText: t("common.xacNhan"),
        typeModal: "warning",
        showBtnOk: true,
      },
      () => {
        showLoading();
        huyQrThanhToan({ thanhToanId, phieuThuId })
          .then(() => {
            isofhToolProvider.putDataCenter({ ma: "QR_DATA", value: null });
            setTimeout(() => {
              window.location.href = window.location.href;
            }, 500);
          })
          .finally(() => {
            hideLoading();
          });
      },
      () => {}
    );
  };

  const renderButton = () => {
    if (
      //-----------------------------------------------------------------------------------------------------------------------------
      infoPatient?.phieuXuat?.trangThai <= 15 &&
      !isAdvised &&
      (infoPatient?.phieuThu?.thanhToan ===
        TRANG_THAI_PHIEU_THU_THANH_TOAN.DA_THANH_TOAN ||
        dataPHAT_THUOC_NHA_THUOC_KHONG_CAN_THANH_TOAN?.eval())
    ) {
      // TT đơn = Tạo mới & TT thanh toán = Đã thanh toán => Hiển thị button Phát
      return (
        <Space gutter={[5, 5]} wrap>
          {infoPatient?.phieuThu?.thanhToan ===
            TRANG_THAI_PHIEU_THU_THANH_TOAN.DA_THANH_TOAN &&
            !dataPHAT_THUOC_NHA_THUOC_KHONG_CAN_THANH_TOAN?.eval() && (
              <Button
                type="primary"
                onClick={onHuyThanhToan}
                rightIcon={<SVG.IcThanhToan />}
                iconHeight={15}
                minWidth={"fit-content"}
              >
                {t("nhaThuoc.huyThanhToan")}
              </Button>
            )}
          {(infoPatient?.phieuThu?.thanhToan ===
            TRANG_THAI_PHIEU_THU_THANH_TOAN.DA_THANH_TOAN ||
            dataPHAT_THUOC_NHA_THUOC_KHONG_CAN_THANH_TOAN?.eval()) && (
            <Button
              type="primary"
              onClick={onPhat}
              rightIcon={<SVG.IcSave />}
              iconHeight={15}
              minWidth={"fit-content"}
              ref={refBtnPhat}
            >
              {t("nhaThuoc.phat") + " [PgDn]"}
            </Button>
          )}
          {dataPHAT_THUOC_NHA_THUOC_KHONG_CAN_THANH_TOAN?.eval() &&
            infoPatient?.phieuThu?.thanhToan ===
              TRANG_THAI_PHIEU_THU_THANH_TOAN.CHUA_THANH_TOAN && (
              <Button
                type="primary"
                onClick={onTuVanDon}
                ref={refTuVanDon}
                rightIcon={<SVG.IcTuVan />}
                iconHeight={15}
                minWidth={"fit-content"}
              >
                {`${t("nhaThuoc.tuVanDon")} [F8]`}
              </Button>
            )}
          {qrThanhToan && (
            <>
              {[
                TRANG_THAI_THANH_TOAN_QR.MOI,
                TRANG_THAI_THANH_TOAN_QR.TAO_QR,
                TRANG_THAI_THANH_TOAN_QR.THANH_TOAN,
                TRANG_THAI_THANH_TOAN_QR.TAO_QR_LOI,
              ].includes(qrThanhToan.trangThaiThanhToan) && (
                <Button
                  type="primary"
                  onClick={onXemQrCode}
                  minWidth={"fit-content"}
                >
                  {t("thuNgan.xemQrCode")}
                </Button>
              )}
              {[
                TRANG_THAI_THANH_TOAN_QR.MOI,
                TRANG_THAI_THANH_TOAN_QR.TAO_QR,
                TRANG_THAI_THANH_TOAN_QR.TAO_QR_LOI,
              ].includes(qrThanhToan.trangThaiThanhToan) &&
                qrThanhToan.thanhToanId && (
                  <Button
                    type="primary"
                    onClick={onHuyQrCode}
                    minWidth={"fit-content"}
                  >
                    {t("thuNgan.huyQrcode")}
                  </Button>
                )}
            </>
          )}
          {hoaDonDaPhatHanhId &&
            checkRole([ROLES["NHA_THUOC"].HOA_DON_CHUYEN_DOI]) && (
              <div className="bottom-group">
                <Button
                  type="default"
                  onClick={onClickHoaDonChuyenDoi}
                  minWidth={105}
                >
                  {t("thuNgan.hoaDonChuyenDoi")}
                </Button>
              </div>
            )}
        </Space>
      );
    } else if (
      //-----------------------------------------------------------------------------------------------------------------------------
      infoPatient?.phieuThu?.thanhToan ===
        TRANG_THAI_PHIEU_THU_THANH_TOAN.DA_THANH_TOAN &&
      infoPatient?.phieuXuat?.trangThai == 30
    ) {
      // đã phát và đã thanh toán
      let listChoHoan = infoPatient?.dsThuoc?.filter(
        (itemThuoc) => itemThuoc?.nbDichVu?.trangThaiHoan === 10
      ); // chờ hoàn
      let isDaHoan = infoPatient?.dsThuoc?.every(
        (itemThuoc) => itemThuoc?.nbDichVu?.trangThaiHoan === 30
      ); // đã hoàn
      let isHienThiBtnHoanThuoc = infoPatient?.dsThuoc?.every(
        (itemThuoc) =>
          itemThuoc?.nbDichVu?.trangThaiHoan !== 10 &&
          itemThuoc?.nbDichVu?.thanhToan === 50
      ); // thường

      const renderHoanThuocButton = () => {
        if (isDaHoan) {
          // đã hoàn thành ko hiện cả 2 button hoàn và hủy hoàn
          return null;
        } else if (isHienThiBtnHoanThuoc) {
          // trạng thái hoàn = thường => hiển thị hoàn thuốc
          return (
            <Button
              type="primary"
              onClick={onHoanThuoc}
              rightIcon={<SVG.IcSave />}
              iconHeight={15}
              minWidth={"fit-content"}
            >
              {t("nhaThuoc.hoanThuoc")}
            </Button>
          );
        } else if (listChoHoan?.length > 0) {
          return (
            <Button
              type="primary"
              onClick={() => onHuyHoanThuoc(listChoHoan)}
              rightIcon={<SVG.IcSave />}
              iconHeight={15}
              minWidth={"fit-content"}
            >
              {t("nhaThuoc.huyHoanThuoc")}
            </Button>
          );
        } else {
          return null;
        }
      };
      return (
        <Space gutter={[5, 5]} wrap>
          {renderHoanThuocButton() && <Col>{renderHoanThuocButton()}</Col>}
          {!dataKHONG_CHO_HUY_PHAT_THUOC_DA_THANH_TOAN.eval() &&
            checkRole([ROLES["NHA_THUOC"].HUY_PHAT_THUOC]) &&
            showBtnHuyPhat && (
              <Button
                type="primary"
                onClick={onHuyPhat}
                rightIcon={<SVG.IcSave />}
                iconHeight={15}
                minWidth={"fit-content"}
              >
                {t("nhaThuoc.huyPhat")}
              </Button>
            )}
          {hoaDonDaPhatHanhId &&
            checkRole([ROLES["NHA_THUOC"].HOA_DON_CHUYEN_DOI]) && (
              <div className="bottom-group">
                <Button
                  type="default"
                  onClick={onClickHoaDonChuyenDoi}
                  minWidth={105}
                >
                  {t("thuNgan.hoaDonChuyenDoi")}
                </Button>
              </div>
            )}
        </Space>
      );
    } else if (
      //-----------------------------------------------------------------------------------------------------------------------------
      (infoPatient?.phieuThu?.thanhToan ===
        TRANG_THAI_PHIEU_THU_THANH_TOAN.CHUA_THANH_TOAN ||
        dataPHAT_THUOC_NHA_THUOC_KHONG_CAN_THANH_TOAN?.eval()) &&
      infoPatient?.phieuXuat?.trangThai == 30 &&
      checkRole([ROLES["NHA_THUOC"].HUY_PHAT_THUOC]) &&
      showBtnHuyPhat
    ) {
      // đã phát và chưa thanh toán
      return (
        <Space gutter={[5, 5]} wrap>
          <Button
            type="primary"
            onClick={onHuyPhat}
            rightIcon={<SVG.IcSave />}
            iconHeight={15}
            minWidth={"fit-content"}
          >
            {t("nhaThuoc.huyPhat")}
          </Button>
          {qrThanhToan && (
            <>
              {[
                TRANG_THAI_THANH_TOAN_QR.MOI,
                TRANG_THAI_THANH_TOAN_QR.TAO_QR,
                TRANG_THAI_THANH_TOAN_QR.THANH_TOAN,
                TRANG_THAI_THANH_TOAN_QR.TAO_QR_LOI,
              ].includes(qrThanhToan.trangThaiThanhToan) && (
                <Button
                  type="primary"
                  onClick={onXemQrCode}
                  minWidth={"fit-content"}
                >
                  {t("thuNgan.xemQrCode")}
                </Button>
              )}
              {[
                TRANG_THAI_THANH_TOAN_QR.MOI,
                TRANG_THAI_THANH_TOAN_QR.TAO_QR,
                TRANG_THAI_THANH_TOAN_QR.TAO_QR_LOI,
              ].includes(qrThanhToan.trangThaiThanhToan) &&
                qrThanhToan.thanhToanId && (
                  <Button
                    type="primary"
                    onClick={onHuyQrCode}
                    minWidth={"fit-content"}
                  >
                    {t("thuNgan.huyQrcode")}
                  </Button>
                )}
            </>
          )}
          {showBtnCapNhatGiaoDichQr && (
            <Button type="primary" onClick={onCapNhatTrangThaiGiaoDichQr}>
              {t("thuNgan.capNhatQR")}
            </Button>
          )}
          {hoaDonDaPhatHanhId &&
            checkRole([ROLES["NHA_THUOC"].HOA_DON_CHUYEN_DOI]) && (
              <div className="bottom-group">
                <Button
                  type="default"
                  onClick={onClickHoaDonChuyenDoi}
                  minWidth={105}
                >
                  {t("thuNgan.hoaDonChuyenDoi")}
                </Button>
              </div>
            )}
        </Space>
      );
    } else {
      // ----------------------------------------------------------------
      if (isThemMoi || isAdvised) {
        // thêm mới hoặc nhấn button tư vấn
        return (
          <Space gutter={[5, 5]} wrap>
            <Button
              type="primary"
              onClick={onSave}
              rightIcon={<SVG.IcSave />}
              iconHeight={15}
              minWidth={"fit-content"}
            >
              {t("common.luu")} [F4]
            </Button>
          </Space>
        );
      } else if (
        //-----------------------------------------------------------------------------------------------------------------------------
        infoPatient?.phieuThu?.thanhToan !==
          TRANG_THAI_PHIEU_THU_THANH_TOAN.DA_THANH_TOAN &&
        (infoPatient?.phieuXuat?.trangThai === 10 ||
          infoPatient?.phieuXuat?.trangThai === 15 ||
          infoPatient?.phieuXuat?.trangThai === 20)
      ) {
        // TT đơn = Tạo mới & TT thanh toán = chưa thanh toán => Hiển thị button Thanh toán , Tư vấn đơn
        return (
          <Space gutter={[5, 5]} wrap>
            {checkRole([ROLES["NHA_THUOC"].THANH_TOAN_DON_THUOC]) &&
              infoPatient?.phieuThu && (
                <Button
                  type="primary"
                  onClick={() => onThanhToan(showBtnDuyet)}
                  rightIcon={<SVG.IcThanhToan />}
                  iconHeight={15}
                  minWidth={"fit-content"}
                >
                  {showBtnDuyet
                    ? t("thuNgan.duyetPhieuThu")
                    : t("nhaThuoc.thanhToan")}{" "}
                  [F12]
                </Button>
              )}
            <Button
              type="primary"
              onClick={onTuVanDon}
              ref={refTuVanDon}
              rightIcon={<SVG.IcTuVan />}
              iconHeight={15}
              minWidth={"fit-content"}
            >
              {`${t("nhaThuoc.tuVanDon")} [F8]`}
            </Button>
            {qrThanhToan && (
              <>
                {[
                  TRANG_THAI_THANH_TOAN_QR.MOI,
                  TRANG_THAI_THANH_TOAN_QR.TAO_QR,
                  TRANG_THAI_THANH_TOAN_QR.THANH_TOAN,
                  TRANG_THAI_THANH_TOAN_QR.TAO_QR_LOI,
                ].includes(qrThanhToan.trangThaiThanhToan) && (
                  <Button
                    type="primary"
                    onClick={onXemQrCode}
                    minWidth={"fit-content"}
                  >
                    {t("thuNgan.xemQrCode")}
                  </Button>
                )}
                {[
                  TRANG_THAI_THANH_TOAN_QR.MOI,
                  TRANG_THAI_THANH_TOAN_QR.TAO_QR,
                  TRANG_THAI_THANH_TOAN_QR.TAO_QR_LOI,
                ].includes(qrThanhToan.trangThaiThanhToan) &&
                  qrThanhToan.thanhToanId && (
                    <Button
                      type="primary"
                      onClick={onHuyQrCode}
                      minWidth={"fit-content"}
                    >
                      {t("thuNgan.huyQrcode")}
                    </Button>
                  )}
              </>
            )}
            {showBtnCapNhatGiaoDichQr && (
              <Button type="primary" onClick={onCapNhatTrangThaiGiaoDichQr}>
                {t("thuNgan.capNhatQR")}
              </Button>
            )}
          </Space>
        );
      }
    }
  };

  const onPrintPhieuThuNhaThuoc = useRefFunc(() => {
    if (refIsChange.current) return;
    if (nbDotDieuTriId && id) {
      getListPhieu({
        nbDotDieuTriId: nbDotDieuTriId,
        maManHinh: "017",
        maViTri: "01701",
        chiDinhTuDichVuId: id,
      })
        .then((res) => {
          let phieuThuNhaThuoc = (res || []).find((x) => x.ma == "P176");
          if (phieuThuNhaThuoc) {
            onPrintPhieu(phieuThuNhaThuoc)();
          }
        })
        .finally(() => {
          onChangeStateUpdate(true);
        });
    }
  });

  const onPrintPhieu = (item) => async () => {
    if (item?.type == "editor") {
      const lichSuKyId = item?.dsSoPhieu?.length
        ? item?.dsSoPhieu[0].lichSuKyId
        : "";
      showFileEditor({
        phieu: item,
        nbDotDieuTriId: nbDotDieuTriId,
        lichSuKyId,
      });
    } else {
      async function onPrintProcess() {
        try {
          if (checkIsPhieuKySo(item)) {
            refModalSignPrint.current &&
              refModalSignPrint.current.showToSign({
                phieuKy: item,
                payload: {
                  nbDotDieuTriId: nbDotDieuTriId,
                  maManHinh: "017",
                  maViTri: "01701",
                  phieuNhapXuatId: id,
                  loaiNhapXuat: 20,
                  phieuThuId: infoPatient?.phieuThu?.id,
                  ...(item.ma != "P005" ? { chiDinhTuDichVuId: id } : {}),
                },
              });
          } else {
            showLoading();
            const { finalFile, dsPhieu } = await getFilePhieuIn({
              listPhieus: [item],
              phieuNhapXuatId: id,
              showError: true,
              loaiNhapXuat: 20,
              nbDotDieuTriId: nbDotDieuTriId,
              phieuThuId: infoPatient?.phieuThu?.id,
            });
            if ((dsPhieu || []).every((x) => x?.loaiIn == LOAI_IN.MO_TAB)) {
              openInNewTab(finalFile);
            } else {
              if (dsPhieu.length && Array.isArray(dsPhieu[0].data)) {
                let data = [];
                dsPhieu[0].data.forEach((item) => {
                  data.push(item);
                });
                printProvider.printPdf(data);
              } else {
                printProvider.printPdf(dsPhieu);
              }
            }
          }
        } catch (error) {
        } finally {
          hideLoading();
        }
      }
      //nếu in đơn thuốc hoặc In phiếu thu nhà thuốc thì thực hiện lưu đơn trước
      if (isAdvised && (item.ma === "P005" || item.ma === "P176")) {
        onSave(onPrintProcess);
        return;
      }

      if (item.ma === "P561") {
        //bắt điều kiện trạng thái đơn= Đã phát mới in ra dữ liệu trong phiếu
        if (infoPatient?.phieuXuat?.trangThai !== 30) {
          message.error(
            t("nhaThuoc.khongInDuocPhieuThuKhiDonThuocChuaDuocThanhToan")
          );
          return;
        }
      }

      onPrintProcess();
    }
  };

  const onPrintHoaDon = (dsHoaDon) => async () => {
    try {
      showLoading();
      await inHoaDon({
        hoaDonId: dsHoaDon.map((item) => item.hoaDonId),
        dinhDang: dataDINH_DANG_XEM_HOA_DON,
      });
    } catch (error) {
      showError(error?.message);
    } finally {
      hideLoading();
    }
  };

  const contentPrint = useMemo(() => {
    let itemInHoaDon = [];
    if (infoPatient?.phieuThu?.dsHoaDon?.length) {
      itemInHoaDon = [
        {
          key: "hoa-don",
          label: (
            <a
              href={() => false}
              onClick={onPrintHoaDon(infoPatient?.phieuThu?.dsHoaDon)}
            >
              {t("thuNgan.inHoaDon")}
            </a>
          ),
        },
      ];
    }

    return (
      <Menu
        selectedKeys={state?.baoCao?.ma}
        items={[
          ...itemInHoaDon,
          ...(state?.listPhieu || []).map((item, index) => ({
            key: item.ma,
            label: (
              <a href={() => false} onClick={onPrintPhieu(item)}>
                {item.ten || item.tenBaoCao}
              </a>
            ),
          })),
        ]}
      />
    );
  }, [
    state.listPhieu,
    state.baoCao,
    infoPatient?.phieuXuat?.trangThai,
    infoPatient?.phieuThu?.dsHoaDon,
    dataDINH_DANG_XEM_HOA_DON,
  ]);

  refEnterInPhieu.current = onPrintPhieu(state.baoCao);
  const onChangeThoiGian = (key) => (e) => {
    let value = "";
    if (e?._d) {
      value = e?._d;
    } else {
      value = e;
    }
    setState({ [key]: value });
  };

  const onSaveThoiGian = () => {
    updateThoiGianPhat({
      id: id,
      thoiGianThanhToan:
        state?.thoiGianThanhToan &&
        moment(state?.thoiGianThanhToan).format("DD-MM-YYYY HH:mm:ss"),
      thoiGianDuyet:
        state?.thoiGianDuyet &&
        moment(state?.thoiGianDuyet).format("DD-MM-YYYY HH:mm:ss"),
      nguoiDuyetId: state?.nguoiDuyetId,
      thuNganId: state?.thuNganId,
    }).then(() => {
      setState({
        editTimeDuyet: false,
        editTimeThanhToan: false,
        editNguoiDuyet: false,
        editThuNgan: false,
      });
      searchDonThuoc(id);
    });
  };

  const onSuaPTTT = () => {
    if (SUA_PTTT_CHO_PHIEU_THU_DA_XUAT_HDDT?.eval()) {
      refModalSuaPTTT.current &&
        refModalSuaPTTT.current.show(
          {
            dsPhuongThucTt: infoPatient?.phieuThu?.dsPhuongThucTt,
            phieuThuId: infoPatient?.phieuThu?.id,
          },
          () => {
            searchDonThuoc(id);
          }
        );
    } else {
      message.error(t("nhaThuoc.phieuThuDaXuatHddtVuiLongKhongDoiPttt"));
    }
  };
  const onPrintPhieuXuatBan = async () => {
    try {
      showLoading();
      await inPhieuThuNhaThuoc({
        id,
        maViTri: "01701",
        maPhieuIn: "P178",
      });
    } catch (err) {
      message.error(err?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
    } finally {
      hideLoading();
    }
  };

  const handleClickBtnXuatHDDT = async () => {
    const { tienHoanTra = 0, tienMienGiam = 0 } = infoPatient?.phieuThu || {};
    refModalThongTinHoaDon.current &&
      refModalThongTinHoaDon.current.show(
        {
          phieuThuId: infoPatient?.phieuThu?.id,
          nbDotDieuTriId,
          thanhTien: tongTien - tienMienGiam - tienHoanTra,
        },
        () => {
          searchDonThuoc(id);
          setState({ showBtnXuatHoaDonNhap: false });
        }
      );
  };

  const onClickXuatHoaDonNhap = async () => {
    try {
      showLoading();
      await xuatHoaDonNhap({
        dsPhieuThuId: [`${infoPatient?.phieuThu?.id}`],
      }).then((s) => {
        const blob = new Blob([new Uint8Array(s)], {
          type: "application/pdf",
        });
        const blobUrl = window.URL.createObjectURL(blob);
        printJS({ printable: blobUrl, type: "pdf" });
        searchDonThuoc(id);
      });
    } catch (err) {
      message.error(err?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
    } finally {
      hideLoading();
    }
  };

  const onRefreshGetPhieu = () => {
    getListPhieu({
      nbDotDieuTriId: nbDotDieuTriId,
      maManHinh: "017",
      maViTri: "01701",
      chiDinhTuDichVuId: id,
    }).then((res) => {
      setState({
        listPhieu: res || [],
      });
    });
  };

  const trangThaiThanhToan = (ds) =>
    (listTrangThaiThanhToan || []).find((i) => i.id === ds.trangThaiThanhToan)
      ?.ten || "";

  return (
    <Main>
      <Card className={`${className} card_info`} bottom={0}>
        <div
          className="container"
          isThemMoi={isThemMoi}
          id="nha-thuoc-chi-tiet-right-container"
        >
          <div className="info">
            <GlobalStyle />
            <div className="title">{t("nhaThuoc.thongTinDonThuoc")}</div>
            <div
              style={{
                display: !isThemMoi ? "flex" : "unset",
                alignItems: !isThemMoi ? "center" : "unset",
                justifyContent: !isThemMoi ? "space-between" : "unset",
              }}
            >
              <div
                className={
                  infoPatient?.phieuThu?.thanhToan ===
                  TRANG_THAI_PHIEU_THU_THANH_TOAN.DA_THANH_TOAN
                    ? "title-2"
                    : "title-1"
                }
              >
                {t("nhaThuoc.khoBan")}
              </div>
              <div
                className="select-row-1"
                style={{
                  marginBottom: !isThemMoi ? 0 : "unset",
                  fontWeight: "bold",
                }}
              >
                {isThemMoi ? (
                  <SelectAntd
                    onChange={onChange("khoId")}
                    value={state.khoId}
                    // value={state.loaiDichVu}
                    placeholder={t("nhaThuoc.chonKhoBanThuoc")}
                    // data={listDataNhanVienKhoCustom}
                    // value={state.khoId || listKhoUser[0]?.id}
                    ref={refKho}
                    open={state.openSelectKho}
                    onDropdownVisibleChange={(open) =>
                      setState({ openSelectKho: open })
                    }
                  >
                    {khoOption}
                  </SelectAntd>
                ) : (
                  infoPatient && infoPatient?.phieuXuat?.kho?.ten
                )}
              </div>
            </div>
            <Row className="select-row-2">
              <div className="title-item" style={{ fontSize: 20 }}>
                <b>{t("nhaThuoc.tongTien")}</b>
              </div>
              <div className="title-item" style={{ fontSize: 20 }}>
                <b>{tongTien && tongTien?.formatPrice()}</b>
              </div>
            </Row>
            <Row className="select-row-2">
              <div
                className="title-item flex flex-center"
                style={{ color: "#0762F7" }}
                onClick={() => {
                  refModalDiscount.current && refModalDiscount.current.show();
                }}
              >
                <SVG.IcChietKhau className="mr-5" /> {t("nhaThuoc.chietKhau")}
              </div>
              <div className="title-item">
                {infoPatient?.phieuThu?.tienMienGiam?.formatPrice()}
              </div>
            </Row>
            <Row className="select-row-2">
              <div className="title-item" style={{ fontSize: 20 }}>
                <b>{t("nhaThuoc.thanhTien")}</b>
              </div>
              <div className="title-item" style={{ fontSize: 20 }}>
                <b>
                  {(
                    tongTien - infoPatient?.phieuThu?.tienMienGiam || 0
                  )?.formatPrice()}
                </b>
              </div>
            </Row>
            <Row>
              <div
                xxl={24}
                className={
                  infoPatient?.phieuThu?.thanhToan ===
                  TRANG_THAI_PHIEU_THU_THANH_TOAN.DA_THANH_TOAN
                    ? "title-2"
                    : "title-1"
                }
              >
                {t("nhaThuoc.ghiChu")}
              </div>
              <InputTimeout
                isTextArea={true}
                value={infoPatient?.phieuXuat?.ghiChu}
                onChange={onChange("ghiChu")}
                ref={refGhiChu}
                onBlur={(e) => {
                  const value = e.target.value;
                  if (isThemMoi) return updateData({ ghiChu: value });
                  if (infoPatient?.phieuXuat?.ghiChu !== value) {
                    updateGhiChuDonThuocById({
                      id: nguoiBenhId,
                      phieuXuatId: nguoiBenhId,
                      phieuXuat: { ghiChu: value },
                    });
                  }
                }}
              />
            </Row>
            <div className="flex flex-center mt-5">
              <span>{t("nhaThuoc.trangThaiDon")}:</span>
              <span className="flex flex-a-center flex1 bold ml-5 flex-j-right">
                {` ${
                  infoPatient?.phieuXuat?.trangThai
                    ? listTrangThaiThuoc?.find(
                        (item) => item.id === infoPatient?.phieuXuat?.trangThai
                      )?.ten
                    : ""
                }`}
              </span>
            </div>
            {infoPatient?.phieuThu?.thanhToan ===
              TRANG_THAI_PHIEU_THU_THANH_TOAN.CHUA_THANH_TOAN && (
              <div className="flex flex-center mt-5">
                <span>{t("nhaThuoc.trangThaiThanhToan")}:</span>
                <span
                  style={{
                    color:
                      infoPatient?.phieuThu?.thanhToan ===
                      TRANG_THAI_PHIEU_THU_THANH_TOAN.CHUA_THANH_TOAN
                        ? "red"
                        : "#049254",
                  }}
                  className="flex1 bold ml-5"
                >
                  {` ${
                    infoPatient?.phieuThu?.thanhToan ===
                    TRANG_THAI_PHIEU_THU_THANH_TOAN.CHUA_THANH_TOAN
                      ? t("nhaThuoc.chuaThanhToan")
                      : t("nhaThuoc.daThanhToan")
                  }`}
                </span>
              </div>
            )}
            <div className="flex flex-center mt-5">
              <span>{t("nhaThuoc.ngayPhat")}:</span>
              <div className="flex flex-a-center flex1 bold ml-5 flex-j-right">
                {state?.editTimeDuyet ? (
                  <div style={{ display: "flex", alignItems: "center" }}>
                    <DatePicker
                      showTime
                      placeholder={t("common.chonNgay")}
                      format="DD/MM/YYYY HH:mm"
                      value={moment(state?.thoiGianDuyet)}
                      onChange={onChangeThoiGian("thoiGianDuyet")}
                    />
                    &emsp;
                    <SVG.IcSave
                      color={"var(--color-blue-primary)"}
                      onClick={onSaveThoiGian}
                    />
                  </div>
                ) : (
                  infoPatient?.phieuXuat?.thoiGianDuyet &&
                  moment(infoPatient?.phieuXuat?.thoiGianDuyet).format(
                    "DD/MM/YYYY HH:mm"
                  )
                )}
                {infoPatient?.phieuXuat?.thoiGianDuyet &&
                  !state?.editTimeDuyet && (
                    <AuthWrapper
                      accessRoles={[ROLES["NHA_THUOC"].SUA_THOI_GIAN_DUYET]}
                    >
                      &emsp;
                      <SVG.IcEdit
                        onClick={() => {
                          setState({
                            thoiGianDuyet:
                              infoPatient?.phieuXuat?.thoiGianDuyet,
                            editTimeDuyet: true,
                          });
                        }}
                      />
                    </AuthWrapper>
                  )}
              </div>
            </div>
            <div className="info-item">
              <span>{t("nhaThuoc.nguoiPhat")}:</span>
              <div className="info-item-value">
                {state?.editNguoiDuyet ? (
                  <>
                    <Select
                      value={state?.nguoiDuyetId}
                      onChange={onChangeThoiGian("nguoiDuyetId")}
                      data={listAllNhanVien}
                      className="info-item-select"
                    />
                    &emsp;
                    <SVG.IcSave
                      color={"var(--color-blue-primary)"}
                      onClick={onSaveThoiGian}
                    />
                  </>
                ) : (
                  <>
                    <div className="info-item-text">
                      {infoPatient?.phieuXuat?.nguoiDuyet?.ten}
                    </div>
                    {infoPatient?.phieuXuat?.nguoiDuyetId && (
                      <AuthWrapper
                        accessRoles={[ROLES["NHA_THUOC"].SUA_THOI_GIAN_DUYET]}
                      >
                        &emsp;
                        <SVG.IcEdit
                          onClick={() => {
                            setState({
                              nguoiDuyetId:
                                infoPatient?.phieuXuat?.nguoiDuyetId,
                              editNguoiDuyet: true,
                            });
                          }}
                        />
                      </AuthWrapper>
                    )}
                  </>
                )}
              </div>
            </div>
            <div className="flex flex-center mt-5">
              <span>{t("nhaThuoc.soPhieu")}:</span>
              <span className="flex flex-a-center flex1 bold ml-5 flex-j-right">
                {infoPatient?.phieuXuat?.soPhieu}
              </span>
            </div>
            <div className="flex flex-center mt-5">
              <span>{t("nhaThuoc.trangThaiHoan")}:&nbsp;</span>
              <span className="flex flex-a-center flex1 bold ml-5 flex-j-right">
                {
                  listTrangThaiHoan?.find(
                    (item) =>
                      item.id === selectedDonThuoc?.nbDichVu?.trangThaiHoan
                  )?.ten
                }
              </span>
            </div>
            {[
              TRANG_THAI_PHIEU_THU_THANH_TOAN.DA_THANH_TOAN,
              TRANG_THAI_PHIEU_THU_THANH_TOAN.TAO_QR,
            ].includes(infoPatient?.phieuThu?.thanhToan) && (
              <div>
                <hr className="hr" />
                <div className="flex flex-center mt-5">
                  <span>{t("nhaThuoc.trangThaiThanhToan")}:</span>
                  <span
                    className="bold flex1 ml-5 text-align-right"
                    style={{
                      color:
                        infoPatient?.phieuThu?.thanhToan ===
                        TRANG_THAI_PHIEU_THU_THANH_TOAN.DA_THANH_TOAN
                          ? "#049254"
                          : "red",
                    }}
                  >
                    {` ${
                      infoPatient?.phieuThu?.thanhToan ===
                      TRANG_THAI_PHIEU_THU_THANH_TOAN.DA_THANH_TOAN
                        ? t("nhaThuoc.daThanhToan")
                        : t("nhaThuoc.chuaThanhToan")
                    }`}
                  </span>
                </div>
                <div className="info-item">
                  <span>{t("nhaThuoc.tenThuNgan")}:</span>
                  <div className="info-item-value">
                    {state?.editThuNgan ? (
                      <>
                        <Select
                          value={state?.thuNganId}
                          onChange={onChangeThoiGian("thuNganId")}
                          data={listAllNhanVien}
                          className="info-item-select"
                        />
                        &emsp;
                        <SVG.IcSave
                          color={"var(--color-blue-primary)"}
                          onClick={onSaveThoiGian}
                        />
                      </>
                    ) : (
                      <>
                        <div className="info-item-text">
                          {infoPatient?.phieuThu?.tenThuNgan}
                        </div>
                        {infoPatient?.phieuThu?.thuNganId && (
                          <AuthWrapper
                            accessRoles={[
                              ROLES["NHA_THUOC"].SUA_THOI_GIAN_DUYET,
                            ]}
                          >
                            &emsp;
                            <SVG.IcEdit
                              onClick={() => {
                                setState({
                                  thuNganId: infoPatient?.phieuThu?.thuNganId,
                                  editThuNgan: true,
                                });
                              }}
                            />
                          </AuthWrapper>
                        )}
                      </>
                    )}
                  </div>
                </div>
                <div className="flex flex-center mt-5">
                  <span>{t("nhaThuoc.tgThanhToan")}:</span>
                  <div className="flex flex-a-center flex1 bold ml-5 flex-j-right">
                    {state?.editTimeThanhToan ? (
                      <>
                        <DatePicker
                          showTime
                          placeholder={t("common.chonNgay")}
                          format="DD/MM/YYYY HH:mm"
                          value={moment(state?.thoiGianThanhToan)}
                          onChange={onChangeThoiGian("thoiGianThanhToan")}
                        />
                        &emsp;
                        <SVG.IcSave
                          color={"var(--color-blue-primary)"}
                          onClick={onSaveThoiGian}
                        />
                      </>
                    ) : (
                      infoPatient?.phieuThu?.thoiGianThanhToan &&
                      moment(infoPatient?.phieuThu?.thoiGianThanhToan).format(
                        "DD/MM/YYYY HH:mm"
                      )
                    )}
                    {infoPatient?.phieuThu?.thoiGianThanhToan &&
                      !state?.editTimeThanhToan && (
                        <AuthWrapper
                          accessRoles={[ROLES["NHA_THUOC"].SUA_THOI_GIAN_DUYET]}
                        >
                          &emsp;
                          <SVG.IcEdit
                            onClick={() => {
                              setState({
                                thoiGianThanhToan:
                                  infoPatient?.phieuThu?.thoiGianThanhToan,
                                editTimeThanhToan: true,
                              });
                            }}
                          />
                        </AuthWrapper>
                      )}
                  </div>
                </div>
                <div className="flex flex-center mt-5">
                  <span>{t("nhaThuoc.soPhieuThu")}:</span>
                  <span className="flex flex-a-center flex1 bold ml-5 flex-j-right">
                    {infoPatient?.phieuThu?.soPhieu}
                  </span>
                </div>
                {infoPatient?.phieuThu?.dsPhuongThucTt && (
                  <div className="flex flex-center mt-5">
                    <span className="title-2">
                      <b>{t("thuNgan.chiTietPhuongThucTt")}</b>
                    </span>
                    <div className="flex flex-a-center flex1 bold ml-5 flex-j-right">
                      {checkRole([
                        ROLES["THU_NGAN"].SUA_PHUONG_THUC_THANH_TOAN,
                      ]) && <SVG.IcEdit onClick={onSuaPTTT} />}
                    </div>
                  </div>
                )}
                {infoPatient?.phieuThu?.dsPhuongThucTt?.map((item, index) => {
                  return (
                    <div key={index}>
                      <div className="flex flex-center mt-5">
                        <span
                          {...(infoPatient?.phieuThu?.thanhToan ===
                            TRANG_THAI_PHIEU_THU_THANH_TOAN.DA_THANH_TOAN && {
                            style: { display: "block", lineHeight: "unset" },
                          })}
                        >
                          <span>{item?.tenPhuongThucTt}</span>
                          <div className="maChuanChi">
                            {t("nhaThuoc.maChuanChi")}
                          </div>
                        </span>
                        <div className="flex flex-c flex-a-end flex1 bold ml-5 flex-j-right">
                          <span>{item?.tongTien?.formatPrice()} đ</span>
                          {item.maChuanChi && (
                            <div className="maChuanChi">{item.maChuanChi}</div>
                          )}
                        </div>
                      </div>

                      {item.loaiPhuongThucTt ===
                        LOAI_PHUONG_THUC_TT.QR_CODE && (
                        <>
                          {item.tenNguoiTaoQr && (
                            <div className="flex flex-center">
                              <span>
                                <div className="maChuanChi">
                                  {t("thuNgan.tenNguoiTaoQr")}
                                </div>
                              </span>
                              <div className="flex flex-c flex-a-end flex1 bold ml-5 flex-j-right">
                                <div className="maChuanChi">
                                  {item.tenNguoiTaoQr}
                                </div>
                              </div>
                            </div>
                          )}

                          {item.thoiGianTaoQr && (
                            <div className="flex flex-center">
                              <span>
                                <div className="maChuanChi">
                                  {t("thuNgan.thoiGianTaoQr")}
                                </div>
                              </span>
                              <div className="flex flex-c flex-a-end flex1 bold ml-5 flex-j-right">
                                <div className="maChuanChi">
                                  {moment(item.thoiGianTaoQr).format(
                                    "DD/MM/YYYY HH:mm:ss"
                                  )}
                                </div>
                              </div>
                            </div>
                          )}

                          {trangThaiThanhToan(item) && (
                            <div className="flex flex-center">
                              <span>
                                <div className="maChuanChi">
                                  {t("thuNgan.trangThaiTaiNganHang")}
                                </div>
                              </span>
                              <div className="flex flex-c flex-a-end flex1 bold ml-5 flex-j-right">
                                <div className="maChuanChi">
                                  {trangThaiThanhToan(item)}
                                </div>
                              </div>
                            </div>
                          )}

                          {infoPatient?.phieuThu?.tenQuay && (
                            <div className="flex flex-center">
                              <span>
                                <div className="maChuanChi">
                                  {t("danhMuc.tenQuay")}
                                </div>
                              </span>
                              <div className="flex flex-c flex-a-end flex1 bold ml-5 flex-j-right">
                                <div className="maChuanChi">
                                  {infoPatient?.phieuThu?.tenQuay}
                                </div>
                              </div>
                            </div>
                          )}

                          {infoPatient?.phieuThu?.nhaThuNganId && (
                            <div className="flex flex-center">
                              <span>
                                <div className="maChuanChi">
                                  {t("danhMuc.toaNha")}
                                </div>
                              </span>
                              <div className="flex flex-c flex-a-end flex1 bold ml-5 flex-j-right">
                                <div className="maChuanChi">
                                  {listAllToaNha.find(
                                    (item) =>
                                      item.id ==
                                      infoPatient?.phieuThu?.nhaThuNganId
                                  )?.ten || ""}
                                </div>
                              </div>
                            </div>
                          )}

                          {item.taiKhoan && (
                            <div className="flex flex-center">
                              <span>
                                <div className="maChuanChi">
                                  {t("thuNgan.tenTK")}
                                </div>
                              </span>
                              <div className="flex flex-c flex-a-end flex1 bold ml-5 flex-j-right">
                                <div className="maChuanChi">
                                  {item.taiKhoan}
                                </div>
                              </div>
                            </div>
                          )}
                        </>
                      )}
                    </div>
                  );
                })}
                {thongTinPhieuThu?.dsHoaDon?.map(
                  (item) =>
                    item.trangThai === TRANG_THAI_HOA_DON.HD_DA_PHAT_HANH && (
                      <div key={item.hoaDonId}>
                        <div className="flex flex-center mt-5">
                          <span>{t("thuNgan.soHoaDon")}:</span>
                          <span className="flex flex-a-center flex1 bold ml-5 flex-j-right">
                            {item.soHoaDon}
                          </span>
                        </div>
                        <div className="flex flex-center mt-5">
                          <span>{t("thuNgan.kyHieuHoaDon")}:</span>
                          <span className="flex flex-a-center flex1 bold ml-5 flex-j-right">
                            {item.kyHieuHoaDon}
                          </span>
                        </div>
                        <div className="flex flex-center mt-5">
                          <span>{t("nhaThuoc.trangThaiPhatHanhHoaDon")}:</span>
                          <span className="flex flex-a-center flex1 bold ml-5 flex-j-right">
                            {t("nhaThuoc.hdDaPhatHanh")}
                          </span>
                        </div>
                        <div className="flex flex-center mt-5">
                          <span>{t("nhaThuoc.ngayPhatHanhHoaDon")}:</span>
                          <span className="flex flex-a-center flex1 bold ml-5 flex-j-right">
                            {item.thoiGianPhatHanhHd
                              ? moment(item.thoiGianPhatHanhHd).format(
                                  "DD/MM/YYYY HH:mm"
                                )
                              : null}
                          </span>
                        </div>
                      </div>
                    )
                )}
              </div>
            )}
            {infoPatient?.phieuThu?.thanhToan !==
              TRANG_THAI_PHIEU_THU_THANH_TOAN.DA_THANH_TOAN &&
              dataTU_DONG_THANH_TOAN_PHIEU_THU_TIEN_MAT?.eval() && (
                <>
                  <div className="flex flex-center mt-5">
                    <span className="bold">
                      {t("thuNgan.chiTietPhuongThucTt")}:
                    </span>
                    <span className="flex flex-a-center flex1 bold ml-5 flex-j-right">
                      <Select
                        value={phuongThucTtId}
                        onChange={onChangePtTtId}
                        data={listAllPhuongThucThanhToan}
                        placeholder={t("thuNgan.chonPTTT")}
                        dropdownStyle={{ minWidth: "300px" }}
                        placement={"bottomRight"}
                      />
                    </span>
                  </div>
                </>
              )}
            <ModalDiscount ref={refModalDiscount} />
            <ModalPayment
              nhaTamUng={nhaTamUng}
              caLamViec={caLamViec}
              nbDotDieuTriId={nbDotDieuTriId}
              ref={refModalPayment}
              isPhatHoaDon={isPhatHoaDon}
            />
            <ModalCancleSubmit ref={refWarningHuyPhat} />
            <ModalCanclePayment ref={refWarningHuyThanhToan} />
            <ModalHoanThuoc ref={refHoanThuoc} />
            <ModalSuaPTTT ref={refModalSuaPTTT} />{" "}
          </div>
        </div>
      </Card>

      <BtnActions>{renderButton()}</BtnActions>

      <div className="xuatHd_print_btn">
        {!isThemMoi && (
          <>
            <Dropdown overlay={contentPrint} trigger={["click"]}>
              <Button
                id="button-in-giay-to"
                type="primary"
                iconHeight={15}
                minWidth={100}
                rightIcon={<SVG.IcPrint className="ic-print" />}
                ref={refPhieuIn}
                onClick={onRefreshGetPhieu}
              >
                <span>{t("nhaThuoc.inGiayTo")}</span>
              </Button>
            </Dropdown>
            {dataHIEN_THI_PHIEU_XUAT_BAN?.eval() && (
              <Button
                onClick={onPrintPhieuXuatBan}
                ref={refPrintPhieuXuatBan}
                type="primary"
                iconHeight={15}
                minWidth={100}
                rightIcon={<SVG.IcPrint className="ic-print" />}
              >
                <span>{t("nhaThuoc.phieuXuatBan")} [F11] </span>
              </Button>
            )}
          </>
        )}
        {checkRole([ROLES["NHA_THUOC"].XUAT_HDDT]) && showBtnXuatHDDT && (
          <Button
            iconHeight={15}
            rightIcon={<SVG.IcPrint />}
            onClick={handleClickBtnXuatHDDT}
            type="primary"
            borderColor="#FFFFFF20"
          >
            {t("thuNgan.xuatHDDT")}
          </Button>
        )}
        {isShowXuatHoaDonNhap && (
          <AuthWrapper accessRoles={[ROLES["THU_NGAN"].XUAT_HOA_DON_NHAP]}>
            <Button
              type="primary"
              onClick={onClickXuatHoaDonNhap}
              minWidth={105}
            >
              {t("thuNgan.xuatHoaDonNhap")}
            </Button>
          </AuthWrapper>
        )}
      </div>
      <ModalThongTinHoaDon
        ref={refModalThongTinHoaDon}
        listPhieu={state.listPhieu}
        onPrintPhieu={onPrintPhieu}
        isXuatHoaDon={isXuatHoaDon}
      />
      <ModalTaoQrCode nbDotDieuTriId={nbDotDieuTriId} ref={refModalTaoQrCode} />
      <ModalTaoQrCodeLoi ref={refModalTaoQrCodeLoi} />
      <ModalSignPrint ref={refModalSignPrint} />
      <ModalThongBaoThanhToanQrCode
        nbDotDieuTriId={nbDotDieuTriId}
        ref={refModalThongBaoThanhToanQrCode}
      />
    </Main>
  );
};

export default memo(ThongTinDonThuoc);
