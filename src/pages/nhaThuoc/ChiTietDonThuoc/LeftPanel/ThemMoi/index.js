import React, { memo, useState, useEffect, useRef, useMemo } from "react";
import { useSelector, useDispatch } from "react-redux";
import { Row, Form, Radio, Col } from "antd";
import { Main } from "./styled";
import { MAX_MONTH_AGE, ENUM } from "constants/index";
import { capitalize, cloneDeep, isNil } from "lodash";
import {
  Card,
  Select,
  DOBInput,
  AddressFull,
  InputTimeout,
  Checkbox,
} from "components";
import Header1 from "pages/kho/components/Header1";
import { useEnum, useStore } from "hooks";
import { containText, filterSortText, renderMessTheoMaLoi } from "utils";
import { useTranslation } from "react-i18next";
import moment from "moment";
import ModalTrungThongTin from "pages/tiepDon/components/ThongTinTiepDon/ModalTrungThongTin";
import { DEFAULT_FEILDS } from "../../components";
import { select } from "redux-store/stores";
import { selectMaTen } from "redux-store/selectors";

const ThemMoi = ({ layerId, nhaTamUng, ...props }) => {
  const [form] = Form.useForm();
  const refModalTrungThongTin = useRef(null);
  const {
    tuoi,
    thangTuoi,
    checkValidate,
    nbDotDieuTri,
    ngaySinh,
    phieuXuat,
    defaultQuocTichId,
    defaultQuocGiaId,
  } = useSelector((state) => state.themMoiThuoc);
  const { nbDiaChi, nbNguoiBaoLanh, nbTongKetRaVien, nbGiayToTuyThan } =
    nbDotDieuTri || {};
  const { bacSiChiDinhId, dsBacSiNgoaiVienId } = phieuXuat || {};
  const {
    themMoiThuoc: { updateData: updateDataThemMoiThuoc, clearData },
    address: { getAllData },
    information: { checkTrungThongTin },
  } = useDispatch();
  const listBacSi = useStore("nhanVien.listNhanVien", []);
  const { t } = useTranslation();
  const checkValidateTtNb = useStore("thuocChiTiet.checkValidateTtNb", {});
  const isBoBatBuocBsDtVangLai = useStore(
    "thuocChiTiet.isBoBatBuocBsDtVangLai",
    false
  );
  const listAllBacSiNgoaiVien = useStore(
    "bacSiNgoaiVien.listAllBacSiNgoaiVien",
    []
  );

  const listNhanVienCatKinh = useStore("thuocChiTiet.listNhanVienCatKinh", []);
  const listNhanVienKinhDoanh = useStore(
    "thuocChiTiet.listNhanVienKinhDoanh",
    []
  );

  const [listLoaiGiayTo] = useEnum(ENUM.LOAI_GIAY_TO, []);

  const layout1 = useStore(
    `thietLap.thietLapGiaoDien.nhaThuoc.chiTietDonThuoc.layout1`,
    DEFAULT_FEILDS.LAYOUT1
  );

  const refHoTen = useRef();
  const refSearchValue = useRef();

  const [state, _setState] = useState({});
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };
  const {
    diaChi,
    tenNb,
    maNb,
    // ngaySinh,
    validate,
    soDienThoai,
    soNha,
    sdtNguoiBaoLanh,
    hoTenNguoiBaoLanh,
    gioiTinh,
    tenCsKcb,
    diaChiCsKcb,
    tenCongTy,
    diaChiCongTy,
    mstCongTy,
    stkCongTy,
    loaiGiayTo,
    maSo,
    loaiDonNhaThuoc,
    maDvQhNs,
    nhanVienKinhDoanhId,
    kyThuatVienId,
  } = state;

  const listBacSiMemo = useMemo(() => {
    return (listBacSi || []).map((item) => {
      if ((item.dsTenKhoaQuanLy || []).length === 0) {
        return { id: item.id, ten: `${item.ma} - ${item.ten}` };
      }
      const _tenKhoa = (item.dsTenKhoaQuanLy || []).join("; ");

      return { id: item.id, ten: `${item.ma} - ${item.ten} - ${_tenKhoa}` };
    });
  }, [listBacSi]);

  const listAllBacSiNgoaiVienMemo = useMemo(() => {
    return (listAllBacSiNgoaiVien || []).map((item) => ({
      id: item.id,
      ten: `${item.ma} - ${item.ten}`,
    }));
  }, [listAllBacSiNgoaiVien]);

  const onCheckTrungThongTin = (payload) => {
    let _nbDotDieuTri = cloneDeep(nbDotDieuTri);
    let _ngaySinh = ngaySinh;
    if (payload?.nbDotDieuTri) {
      _nbDotDieuTri = { ..._nbDotDieuTri, ...payload?.nbDotDieuTri };
    }
    if (payload?.ngaySinh) {
      _ngaySinh = payload?.ngaySinh;
    }

    if (
      JSON.stringify({
        tenNb: _nbDotDieuTri?.tenNb,
        ngaySinh: _ngaySinh,
        maNb: _nbDotDieuTri?.maNb,
      }) ==
      JSON.stringify({
        tenNb: nbDotDieuTri?.tenNb,
        ngaySinh: ngaySinh,
        maNb: nbDotDieuTri?.maNb,
      })
    ) {
      return;
    }
    if (
      (_nbDotDieuTri.tenNb && _nbDotDieuTri.tenNb.length && _ngaySinh?.date) ||
      _nbDotDieuTri.maNb
    ) {
      let data = {
        tenNb: _nbDotDieuTri.tenNb ? _nbDotDieuTri.tenNb.toUpperCase()?.trim() : null,
        gioiTinh: _nbDotDieuTri.gioiTinh
          ? Number(_nbDotDieuTri.gioiTinh)
          : null,
        ngaySinh: _ngaySinh?.date
          ? moment(_ngaySinh?.date).format("YYYY-MM-DD")
          : null,
        tinhThanhPhoId: _nbDotDieuTri.nbDiaChi?.tinhThanhPhoId,
        xaPhuongId: _nbDotDieuTri.nbDiaChi?.xaPhuongId,
        quocGiaId: _nbDotDieuTri.nbDiaChi?.quocGiaId,
        khoaId: nhaTamUng?.khoaId,
      };
      if (_nbDotDieuTri.maNb) {
        data = {
          maNb: _nbDotDieuTri.maNb,
        };
      }
      checkTrungThongTin(data).then((s) => {
        if (s?.data?.length) {
          refModalTrungThongTin.current.show(
            {
              show: true,
              data: s?.data,
            },
            (data) => {
              if (data) {
                updateDataThemMoiThuoc({
                  nbDotDieuTri: {
                    maNb: data?.maNb,
                    tenNb: data?.tenNb,
                    soDienThoai: data?.soDienThoai,
                    gioiTinh: data?.gioiTinh,
                    nbDiaChi: data?.nbDiaChi,
                    nbNguoiBaoLanh: data?.nbNguoiBaoLanh,
                    quocTichId: data?.quocTichId,
                  },
                  ngaySinh: {
                    date: data?.ngaySinh,
                    str:
                      data?.ngaySinh &&
                      moment(data?.ngaySinh).format("DD/MM/YYYY"),
                  },
                });

                setState({
                  tenNb: data?.tenNb,
                  maNb: data?.maNb,
                  gioiTinh: data?.gioiTinh,
                  soDienThoai: data?.soDienThoai,
                  soNha: data?.nbDiaChi?.soNha,
                  diaChi: data?.nbDiaChi.diaChi
                    ? data?.nbDiaChi.diaChi
                    : data?.nbDiaChi.tinhThanhPho
                    ? `${
                        data?.nbDiaChi.xaPhuong?.ten
                          ? data?.nbDiaChi.xaPhuong?.ten
                          : ""
                      }${
                        data?.nbDiaChi.quanHuyen?.ten
                          ? `, ${data?.nbDiaChi.quanHuyen?.ten}`
                          : ""
                      }${
                        data?.nbDiaChi.tinhThanhPho?.ten
                          ? `, ${data?.nbDiaChi.tinhThanhPho?.ten}`
                          : ""
                      }`
                    : "",
                  hoTenNguoiBaoLanh: data?.nbNguoiBaoLanh?.hoTen,
                  sdtNguoiBaoLanh: data?.nbNguoiBaoLanh?.soDienThoai,
                });
              }
            }
          );
        }
      });
    }
  };

  const updateData = (payload) => {
    if (payload?.nbDotDieuTri || payload?.ngaySinh) {
      onCheckTrungThongTin(payload);
    }
    updateDataThemMoiThuoc(payload);
  };

  const update = (value, variables) => {
    setState({ [`${variables}`]: value });
    let _nbDotDieuTri = null;

    if (
      ["tenNb", "gioiTinh", "tuoi", "soDienThoai", "maNb", "maDvQhNs"].includes(
        variables
      )
    ) {
      _nbDotDieuTri = {
        ...nbDotDieuTri,
        [`${variables}`]: value,
      };
      updateData({
        nbDotDieuTri: _nbDotDieuTri,
      });
    } else if (
      ["soNha", "tenCongTy", "diaChiCongTy", "mstCongTy", "stkCongTy"].includes(
        variables
      )
    ) {
      _nbDotDieuTri = {
        ...nbDotDieuTri,
        nbDiaChi: {
          ...nbDiaChi,
          [`${variables}`]: value,
        },
      };
      updateData({
        nbDotDieuTri: _nbDotDieuTri,
      });
    } else if (["loaiGiayTo", "maSo"].includes(variables)) {
      _nbDotDieuTri = {
        ...nbDotDieuTri,
        nbGiayToTuyThan: {
          ...nbGiayToTuyThan,
          [`${variables}`]: value,
        },
      };
      updateData({
        nbDotDieuTri: _nbDotDieuTri,
      });
    } else if (["sdtNguoiBaoLanh", "hoTenNguoiBaoLanh"].includes(variables)) {
      let keyCustom =
        variables === "hoTenNguoiBaoLanh" ? "hoTen" : "soDienThoai";
      _nbDotDieuTri = {
        ...nbDotDieuTri,
        nbNguoiBaoLanh: {
          ...nbNguoiBaoLanh,
          [`${keyCustom}`]: value,
        },
      };
      updateData({
        nbDotDieuTri: _nbDotDieuTri,
      });
    } else if (
      [
        "dsBacSiNgoaiVienId",
        "bacSiChiDinhId",
        "tenCsKcb",
        "diaChiCsKcb",
        "kyThuatVienId",
        "nhanVienKinhDoanhId",
      ].includes(variables)
    ) {
      updateData({
        phieuXuat: {
          ...phieuXuat,
          [`${variables}`]: value,
        },
      });
    }
    if (["dsCdChinhId", "cdSoBo"].includes(variables)) {
      _nbDotDieuTri = {
        ...nbDotDieuTri,
        nbTongKetRaVien: {
          ...nbTongKetRaVien,
          [`${variables}`]:
            "dsCdChinhId" === variables && value ? [value] : value,
        },
      };
      updateData({
        nbDotDieuTri: _nbDotDieuTri,
      });
    } else {
      updateData({ [`${variables}`]: value });
    }
  };

  const onSelectAddress = async (data) => {
    let address = {};
    if (data?.tinhThanhPho && data?.quanHuyen) {
      address = {
        ...props.nbDiaChi,
        tinhThanhPhoId: data?.tinhThanhPho?.id,
        xaPhuongId: data?.id,
        diaChi: data?.displayText,
      };
    } else if (data?.tinhThanhPho) {
      address = {
        ...props.nbDiaChi,
        tinhThanhPhoId: data?.tinhThanhPho?.id,
        quanHuyenId:
          data?.quanHuyen === null || data?.quanHuyen === undefined
            ? null
            : data?.id,
        xaPhuongId:
          data?.quanHuyen === null || data?.quanHuyen === undefined
            ? data.id
            : null,
        diaChi: data?.displayText,
      };
    } else {
      address = {
        ...props.nbDiaChi,
        tinhThanhPhoId: data?.id,
        quanHuyenId: null,
        xaPhuongId: null,
        diaChi: data?.displayText,
      };
    }
    updateData({
      nbDotDieuTri: {
        ...nbDotDieuTri,
        nbDiaChi: {
          ...nbDiaChi,
          ...address,
        },
      },
    });
  };

  const checkGender = (value) => {
    let dataTen = value.toUpperCase();
    let genderVan = dataTen.search("VĂN");
    let genderThi = dataTen.search("THỊ");
    let valueGender = null;
    if (genderVan >= 0 && genderThi < 0) {
      // updateData({ gioiTinh: 1 });
      updateData({
        nbDotDieuTri: {
          ...nbDotDieuTri,
          gioiTinh: 1,
        },
      });
      valueGender = 1;
    } else if (genderThi >= 0) {
      updateData({
        nbDotDieuTri: {
          ...nbDotDieuTri,
          gioiTinh: 2,
        },
      });
      valueGender = 2;
    }
    if (valueGender) {
      setState({ gioiTinh: valueGender });
    }
  };

  useEffect(() => {
    refHoTen.current && refHoTen.current.focus();
    getAllData();
    return () => {
      clearData({
        nbDotDieuTri: {
          nbDiaChi: {
            quocGiaId: defaultQuocGiaId,
            quocTichId: defaultQuocTichId,
          },
        },
      });
    };
  }, []);

  useEffect(() => {
    let _nbDotDieuTri = {
      ...(nbDotDieuTri || {}),
    };
    let _initState = {};

    if (layout1.includes("hoVaTen")) {
      _nbDotDieuTri.gioiTinh = 2;
      _initState.gioiTinh = 2;
    }
    if (layout1.includes("loaiGiayTo")) {
      _nbDotDieuTri.nbGiayToTuyThan = { loaiGiayTo: 6 }; //mặc định Không có giấy tờ
      _initState.loaiGiayTo = 6;
    }

    updateDataThemMoiThuoc({
      nbDotDieuTri: _nbDotDieuTri,
    });

    setState(_initState);
  }, [layout1]);

  const onBlur = (value, variables) => {
    if (variables === "tenNb") checkGender(value);
  };

  const filterOption = (input = "", option) => {
    refSearchValue.current = input;
    return containText(option?.label, input);
  };

  const filterSort = (optionA, optionB) => {
    if (!refSearchValue.current) return 1;

    return filterSortText(optionA, optionB, refSearchValue.current);
  };

  return (
    <Card noPadding={true} style={{ overflow: "auto" }}>
      <Main className="them-moi">
        <div className="body-info">
          <Form form={form} layout="vertical">
            <Header1 title={t("nhaThuoc.thongTinKhachHang")}>
              <Row style={{ width: "50%" }}>
                {layout1.includes("thongTinKhachHang") && (
                  <Col span={12}>
                    <Form.Item
                      style={{ margin: "0 0 0 1rem" }}
                      className="form-item"
                    >
                      <div className="paddingLeft">
                        <InputTimeout
                          placeholder={t("common.nhapMaNguoiBenh")}
                          value={maNb}
                          onChange={(e) => update(e, "maNb")}
                          onBlur={() => update(maNb, "maNb")}
                        />
                      </div>
                    </Form.Item>
                  </Col>
                )}
                {layout1.includes("nhanVien") && (
                  <Col span={12}>
                    <Form.Item
                      style={{ margin: "0 0 0 1rem" }}
                      className="form-item"
                    >
                      <Checkbox
                        checked={loaiDonNhaThuoc}
                        onChange={(e) =>
                          update(
                            e?.target?.checked ? 30 : null,
                            "loaiDonNhaThuoc"
                          )
                        }
                      >
                        {t("thuNgan.nhanVien")}
                      </Checkbox>
                    </Form.Item>
                  </Col>
                )}
              </Row>
            </Header1>
            <Row style={{ padding: "0 10px" }}>
              <Row style={{ width: "100%" }} gutter={[16, 16]}>
                {layout1.includes("hoVaTen") && (
                  <Col style={{ width: "25%" }}>
                    <Form.Item className="form-item">
                      <div className="item-input paddingLeft">
                        <div>
                          <div className="label">
                            {t("common.hoVaTen")}
                            {!nbDotDieuTri?.tenNb && (
                              <span
                                style={{
                                  color: "red",
                                }}
                              >
                                {" *"}
                              </span>
                            )}
                          </div>
                          <InputTimeout
                            ref={refHoTen}
                            placeholder={t("common.nhapHoVaTen")}
                            value={tenNb}
                            style={{ textTransform: "uppercase" }}
                            onChange={(e) => update(e, "tenNb")}
                            onBlur={(e) => onBlur(e.target.value, "tenNb")}
                          />
                        </div>
                      </div>
                    </Form.Item>
                    {checkValidateTtNb?.tenNb && (
                      <span className="error">{checkValidateTtNb.tenNb}</span>
                    )}
                  </Col>
                )}
                <div style={{ width: "35%", display: "flex" }}>
                  {layout1.includes("hoVaTen") && (
                    <Col>
                      <Form.Item className="form-item">
                        <div className="paddingLeft">
                          <div className="label">{t("common.gioiTinh")}</div>
                          <Radio.Group
                            value={gioiTinh}
                            onChange={(e) => update(e.target.value, "gioiTinh")}
                          >
                            <Radio value={1}>{t("common.nam")}</Radio>
                            <Radio value={2}>{t("common.nu")}</Radio>
                          </Radio.Group>
                        </div>
                      </Form.Item>
                    </Col>
                  )}

                  {layout1.includes("SDT") && (
                    <Col>
                      <Form.Item className="form-item" style={{ flex: 1 }}>
                        <div className="paddingLeft">
                          <div className="label">{t("common.soDienThoai")}</div>
                          <InputTimeout
                            placeholder={`${t("common.nhapSoDienThoai")}`}
                            value={soDienThoai}
                            onChange={(e) => update(e, "soDienThoai")}
                            onBlur={() => onBlur(soDienThoai, "soDienThoai")}
                          />
                          {soDienThoai &&
                          !soDienThoai.replaceAll(" ", "").isPhoneNumber() ? (
                            <div className="error">
                              {t("nhaThuoc.soDienThoaiSaiDinhDang")}
                            </div>
                          ) : null}
                        </div>
                      </Form.Item>
                    </Col>
                  )}
                </div>
                {layout1.includes("hoVaTen") && (
                  <>
                    <Col style={{ width: "20%" }}>
                      <Form.Item className="form-item">
                        <div className="paddingLeft">
                          <div className="label">{t("common.ngaySinh")}</div>
                          <DOBInput
                            className="item-born"
                            value={ngaySinh || ""}
                            onBlur={(e, nofi, ageStr, chiNamSinh) => {
                              let ngaySinhTime =
                                nofi === 0 && e && e.date && e.date._d;
                              let tuoi =
                                nofi === 0 ? ngaySinhTime.getAge() : "";
                              setState({ validate: nofi });
                              updateData({
                                ngaySinh: e,
                                tuoi: tuoi,
                                thangTuoi:
                                  tuoi <= 3
                                    ? ageStr > 0
                                      ? ageStr
                                      : null
                                    : null,
                                ngaySinhTime: ngaySinhTime,
                                checkNgaySinh: nofi === 0 ? true : false,
                                chiNamSinh: chiNamSinh,
                              });
                            }}
                            // disabled={disableTiepDon}
                            onChange={(e) => update(e, "ngaySinh")}
                            placeholder={t("nhaThuoc.ngaySinhDdmmyy")}
                            showIcon={false}
                          />
                          {validate && validate !== 0 && ngaySinh?.str ? (
                            <div className="error">
                              {t("nhaThuoc.ngaySinhSaiDinhDang")}
                            </div>
                          ) : checkValidate &&
                            !props.checkNgaySinh &&
                            !ngaySinh?.str ? (
                            <div className="error">
                              {t("nhaThuoc.vuiLongNhapNgaySinh")}
                            </div>
                          ) : null}
                        </div>
                      </Form.Item>
                    </Col>
                    <Col style={{ width: "20%" }}>
                      <Form.Item className="form-item">
                        <div className="paddingLeft">
                          <div className="label">
                            {capitalize(t("common.tuoi"))}
                          </div>
                          <div className="item-input">
                            <InputTimeout
                              value={
                                !isNil(thangTuoi) && thangTuoi <= MAX_MONTH_AGE
                                  ? `${thangTuoi} ${t("common.thang")}`
                                  : tuoi
                              }
                              placeholder={capitalize(t("common.tuoi"))}
                              disabled
                            />
                          </div>
                        </div>
                      </Form.Item>
                    </Col>
                  </>
                )}
              </Row>
              <Row
                justify={"space-between"}
                style={{ width: "100%" }}
                gutter={[16, 16]}
              >
                {layout1.includes("diaChi") && (
                  <>
                    <Col style={{ width: "25%" }}>
                      <Form.Item className="form-item">
                        <div className="paddingLeft">
                          <div className="label">{t("nhaThuoc.sNThonXom")}</div>
                          <InputTimeout
                            placeholder={t("nhaThuoc.NhapSnThonXom")}
                            value={soNha}
                            onChange={(e) => update(e, "soNha")}
                            onBlur={() => onBlur(soNha, "soNha")}
                            // disabled={disableTiepDon}
                            // onChange={onChange("qrBN", true)}
                            // onKeyDown={onKeyDown}
                          />
                        </div>
                      </Form.Item>
                    </Col>
                    <Col style={{ width: "35%" }}>
                      <Form.Item className="form-item">
                        {/* <div className="item-input" style={{ marginBottom: 0 }}> */}
                        {/* <label className={!diaChi ? `label label-error` : "label"}>
                    Phường/Xã, Quận/Huyện, Tỉnh/TP
              <span style={{ color: "red" }}> *</span>
                  </label> */}
                        <div className="paddingLeft">
                          <div className="label">
                            {t("nhaThuoc.phuongXaTinhThanhPho")}
                          </div>
                          <AddressFull
                            className="form-item_address"
                            onChangeAdrressText={(e) => setState({ diaChi: e })}
                            placeholder={t("nhaThuoc.nhapPhuongXaTinhThanhPho")}
                            value={diaChi}
                            onSelectAddress={onSelectAddress}
                            // disabled={disableTiepDon}
                          ></AddressFull>
                          {/* </div> */}
                          {checkValidate && !diaChi ? (
                            <div className="error">
                              {t("nhaThuoc.vuiLongNhapPhuongXaTinhThanhPho")}
                            </div>
                          ) : null}
                        </div>
                      </Form.Item>
                    </Col>
                  </>
                )}
                {layout1.includes("nguoiBaoLanh") && (
                  <>
                    <Col style={{ width: "20%" }}>
                      <Form.Item className="form-item">
                        <div className="paddingLeft">
                          <div className="label">
                            {t("nhaThuoc.hoVaTenNguoiBaoLanh")}
                          </div>
                          <InputTimeout
                            placeholder={t("nhaThuoc.nhapHoVaTenNguoiBaoLanh")}
                            value={hoTenNguoiBaoLanh}
                            onChange={(e) => update(e, "hoTenNguoiBaoLanh")}
                            onBlur={() =>
                              update(hoTenNguoiBaoLanh, "hoTenNguoiBaoLanh")
                            }
                            // onChange={onChange("qrBN", true)}
                            // onKeyDown={onKeyDown}
                          />
                        </div>
                      </Form.Item>
                    </Col>
                    <Col style={{ width: "20%" }}>
                      <Form.Item className="form-item">
                        <div className="paddingLeft">
                          <div className="label">
                            {t("common.sdtNguoiBaoLanh")}
                          </div>
                          <InputTimeout
                            placeholder={t("common.nhapSdtNguoiBaoLanh")}
                            value={sdtNguoiBaoLanh}
                            onChange={(e) => setState({ sdtNguoiBaoLanh: e })}
                            onBlur={() =>
                              update(sdtNguoiBaoLanh, "sdtNguoiBaoLanh")
                            }
                            // onChange={onChange("qrBN", true)}
                            // onKeyDown={onKeyDown}
                          />
                          {sdtNguoiBaoLanh &&
                          !sdtNguoiBaoLanh
                            .replaceAll(" ", "")
                            .isPhoneNumber() ? (
                            <div className="error">
                              {t("nhaThuoc.sdtNguoiBaoLanhSaiDinhDang")}
                            </div>
                          ) : null}
                        </div>
                      </Form.Item>
                    </Col>
                  </>
                )}
              </Row>
              <Row style={{ width: "100%" }} gutter={[16, 16]}>
                {layout1.includes("bsChiDinh") && (
                  <>
                    <Col style={{ width: "25%" }}>
                      <Form.Item className="form-item">
                        <div className="paddingLeft">
                          <div
                            className="title last"
                            style={{ marginRight: 5 }}
                          >
                            {t("nhaThuoc.bsChiDinh")}{" "}
                            {!isBoBatBuocBsDtVangLai && (
                              <span style={{ color: "red" }}>*</span>
                            )}
                          </div>
                          <div className="detail-last">
                            <Select
                              placeholder={t("nhaThuoc.vuiLongNhapBsChiDinh")}
                              onChange={(e) => update(e, "bacSiChiDinhId")}
                              value={bacSiChiDinhId}
                              data={listBacSiMemo}
                              style={{ width: "100%" }}
                              disabled={!!dsBacSiNgoaiVienId?.length}
                              filterOption={filterOption}
                              filterSort={filterSort}
                            />
                          </div>
                        </div>
                      </Form.Item>
                    </Col>
                    <Col style={{ width: "35%" }}>
                      <Form.Item className="form-item">
                        <div className="paddingLeft">
                          <div
                            className="title last"
                            style={{ marginRight: 5 }}
                          >
                            {t("nhaThuoc.bsChiDinhNgoaiVien")}
                            {!isBoBatBuocBsDtVangLai && (
                              <span style={{ color: "red" }}> *</span>
                            )}
                          </div>
                          <div className="detail-last">
                            <Select
                              placeholder={t("nhaThuoc.vuiLongNhapBsChiDinh")}
                              onChange={(e) => update(e, "dsBacSiNgoaiVienId")}
                              value={dsBacSiNgoaiVienId}
                              data={listAllBacSiNgoaiVienMemo}
                              style={{ width: "100%" }}
                              mode="multiple"
                              disabled={bacSiChiDinhId > 0}
                              filterOption={filterOption}
                              filterSort={filterSort}
                            >
                              {/* {children} */}
                            </Select>
                          </div>
                        </div>
                      </Form.Item>
                    </Col>
                  </>
                )}
                {layout1.includes("diaChiCoSoKCB") && (
                  <Col style={{ width: "20%" }}>
                    <Form.Item className="form-item">
                      <div className="paddingLeft">
                        <div className="label">
                          {t("nhaThuoc.diaChiCoSoKCB")}
                        </div>
                        <InputTimeout
                          placeholder={t("nhaThuoc.nhapDiaChiCoSoKCB")}
                          value={diaChiCsKcb}
                          onChange={(e) => update(e, "diaChiCsKcb")}
                          onBlur={() => update(diaChiCsKcb, "diaChiCsKcb")}
                        />
                      </div>
                    </Form.Item>
                  </Col>
                )}
                {layout1.includes("tenCoSoKCB") && (
                  <Col style={{ width: "20%" }}>
                    <Form.Item className="form-item">
                      <div className="paddingLeft">
                        <div className="label">{t("nhaThuoc.tenCoSoKCB")}</div>
                        <InputTimeout
                          placeholder={t("nhaThuoc.nhapTenCoSoKCB")}
                          value={tenCsKcb}
                          onChange={(e) => update(e, "tenCsKcb")}
                          onBlur={() => update(tenCsKcb, "tenCsKcb")}
                        />
                      </div>
                    </Form.Item>
                  </Col>
                )}
              </Row>
              <Row style={{ width: "100%" }} gutter={[16, 16]}>
                {layout1.includes("tenCongTy") && (
                  <Col style={{ width: "25%" }}>
                    <Form.Item className="form-item">
                      <div className="paddingLeft">
                        <div className="label">{t("tiepDon.tenCongTy")}</div>
                        <InputTimeout
                          placeholder={t("tiepDon.nhapTenCongTy")}
                          value={tenCongTy}
                          onChange={(e) => update(e, "tenCongTy")}
                          onBlur={() => update(tenCongTy, "tenCongTy")}
                        />
                      </div>
                    </Form.Item>
                  </Col>
                )}
                {layout1.includes("diaChiCongTy") && (
                  <Col style={{ width: "35%" }}>
                    <Form.Item className="form-item">
                      <div className="paddingLeft">
                        <div className="label">{t("tiepDon.diaChiCongTy")}</div>
                        <InputTimeout
                          placeholder={t("tiepDon.nhapDiaChiCongTy")}
                          value={diaChiCongTy}
                          onChange={(e) => update(e, "diaChiCongTy")}
                          onBlur={() => update(diaChiCongTy, "diaChiCongTy")}
                        />
                      </div>
                    </Form.Item>
                  </Col>
                )}
                {layout1.includes("maSoThueCongTy") && (
                  <Col style={{ width: "20%" }}>
                    <Form.Item className="form-item">
                      <div className="paddingLeft">
                        <div className="label">
                          {t("tiepDon.maSoThueCongTy")}
                        </div>
                        <InputTimeout
                          placeholder={t("tiepDon.nhapMaSoThueCongTy")}
                          value={mstCongTy}
                          onChange={(e) => update(e, "mstCongTy")}
                          onBlur={() => update(mstCongTy, "mstCongTy")}
                        />
                      </div>
                    </Form.Item>
                  </Col>
                )}
                {layout1.includes("soTaiKhoanCongTy") && (
                  <Col style={{ width: "20%" }}>
                    <Form.Item className="form-item">
                      <div className="paddingLeft">
                        <div className="label">
                          {t("tiepDon.soTaiKhoanCongTy")}
                        </div>
                        <InputTimeout
                          placeholder={t("tiepDon.nhapSoTaiKhoanCongTy")}
                          value={stkCongTy}
                          onChange={(e) => update(e, "stkCongTy")}
                          onBlur={() => update(stkCongTy, "stkCongTy")}
                        />
                      </div>
                    </Form.Item>
                  </Col>
                )}
                {layout1.includes("chanDoanSoBo") && (
                  <Col style={{ width: "50%" }}>
                    <Form.Item className="form-item">
                      <div className="paddingLeft">
                        <div className="label">
                          {t("nhaThuoc.chanDoanSoBo")}
                        </div>
                        <InputTimeout
                          placeholder={t("nhaThuoc.nhapChanDoanSoBo")}
                          onChange={(e) => update(e, "cdSoBo")}
                        />
                      </div>
                    </Form.Item>
                  </Col>
                )}
                {layout1.includes("chanDoanChinh") && (
                  <Col style={{ width: "50%" }}>
                    <Form.Item className="form-item">
                      <div className="paddingLeft">
                        <div className="label">{t("hsba.chanDoanChinh")}</div>
                        <Select
                          className="input-filter"
                          placeholder={t("nhaThuoc.nhapChanDoanChinh")}
                          onChange={(e) => update(e, "dsCdChinhId")}
                          data={select.maBenh.listAllMaBenh}
                          getLabel={selectMaTen}
                        />
                      </div>
                    </Form.Item>
                  </Col>
                )}

                {layout1.includes("loaiGiayTo") && (
                  <Col style={{ width: "50%" }}>
                    <Form.Item className="form-item">
                      <div className="paddingLeft">
                        <div className="label">
                          {t("tiepDon.loaiGiayToTuyThan")}
                        </div>
                        <Select
                          className="input-filter"
                          placeholder={t("tiepDon.chonLoaiGiayToTuyThan")}
                          onChange={(e) => update(e, "loaiGiayTo")}
                          data={listLoaiGiayTo}
                          value={loaiGiayTo}
                        />
                      </div>
                    </Form.Item>
                  </Col>
                )}
                {layout1.includes("maSo") && (
                  <Col style={{ width: "50%" }}>
                    <Form.Item className="form-item">
                      <div className="paddingLeft">
                        <div className="label">
                          {t("tiepDon.maSoGiayToTuyThan")}
                        </div>
                        <InputTimeout
                          placeholder={t("tiepDon.nhapMaSoGiayToTuyThan")}
                          onChange={(e) => update(e, "maSo")}
                          value={maSo}
                        />
                      </div>
                    </Form.Item>

                    {checkValidateTtNb?.maSo && (
                      <span className="error">{checkValidateTtNb.maSo}</span>
                    )}
                  </Col>
                )}
                {layout1.includes("maSoDVQHNS") && (
                  <Col style={{ width: "50%" }}>
                    <Form.Item className="form-item">
                      <div className="paddingLeft">
                        <div className="label">{t("tiepDon.maSoDVQHNS")}</div>
                        <InputTimeout
                          placeholder={t("tiepDon.nhapMaSoDVQHNS")}
                          onChange={(e) => update(e, "maDvQhNs")}
                          value={maDvQhNs}
                        />
                      </div>
                    </Form.Item>
                  </Col>
                )}

                {layout1.includes("nhanVienKinhDoanh") && (
                  <Col style={{ width: "50%" }}>
                    <Form.Item className="form-item">
                      <div className="paddingLeft">
                        <div className="label">
                          {t("nhaThuoc.nhanVienKinhDoanh")}
                        </div>
                        <Select
                          className="input-filter"
                          placeholder={t("nhaThuoc.chonNhanVienKinhDoanh")}
                          onChange={(e) => update(e, "nhanVienKinhDoanhId")}
                          data={listNhanVienKinhDoanh}
                          value={nhanVienKinhDoanhId}
                        />
                      </div>
                    </Form.Item>
                  </Col>
                )}
                {layout1.includes("kyThuatVienCatKinh") && (
                  <Col style={{ width: "50%" }}>
                    <Form.Item className="form-item">
                      <div className="paddingLeft">
                        <div className="label">
                          {t("nhaThuoc.kyThuatVienCatKinh")}
                        </div>
                        <Select
                          className="input-filter"
                          placeholder={t("nhaThuoc.chonKyThuatVienCatKinh")}
                          onChange={(e) => update(e, "kyThuatVienId")}
                          data={listNhanVienCatKinh}
                          value={kyThuatVienId}
                        />
                      </div>
                    </Form.Item>
                  </Col>
                )}
              </Row>
            </Row>
          </Form>
        </div>
      </Main>

      <ModalTrungThongTin
        renderMessTheoMaLoi={renderMessTheoMaLoi}
        boQuaKiemTraThanhToan={true}
        ref={refModalTrungThongTin}
      />
    </Card>
  );
};

export default memo(ThemMoi);
