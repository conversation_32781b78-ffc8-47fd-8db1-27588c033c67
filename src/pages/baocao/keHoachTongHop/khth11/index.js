import React, { useState, useEffect, useMemo } from "react";
import { Col, message, Row } from "antd";
import { DateTimePicker, Select } from "components";
import moment from "moment";
import { useDispatch } from "react-redux";
import BaseBaoCao from "pages/baocao/BaseBaoCao";
import { useStore, useEnum, useListAll, useQueryAll, useCache } from "hooks";
import { useTranslation } from "react-i18next";
import { CACHE_KEY, ENUM, ROLES } from "constants/index";
import { upperFirst } from "lodash";
import { query } from "redux-store/stores";
import { checkRole } from "lib-utils/role-utils";

/**
 * KHTH11. Danh sách chi tiết người bệnh đang điều trị
 *
 */

const LIST_SO_SANH_TUOI = [
  {
    id: 20,
    i18n: "baoCao.tu0Den6Tuoi",
  },
  {
    id: 50,
    i18n: "baoCao.lonHon6Tuoi",
  },
];

const Index = () => {
  const { t } = useTranslation();
  const [show, setShow] = useState(true);
  const { listKhoaTheoTaiKhoan } = useStore("khoa", []);
  const { listPhong } = useStore("phong", []);
  const [listDoiTuongKcb] = useEnum(ENUM.DOI_TUONG_KCB);
  const [listDoiTuong] = useEnum(ENUM.DOI_TUONG, []);
  const [listGioiTinh] = useEnum(ENUM.GIOI_TINH);
  const [listAllLoaiDoiTuong] = useListAll("loaiDoiTuong", {}, true);
  const listAllLoaiBenhAn = useStore("loaiBenhAn.listAllLoaiBenhAn", []);
  const { data: listAllKhoa } = useQueryAll(query.khoa.queryAllKhoa);
  const [cacheNhomTheoNb, setCacheNhomTheoNb] = useCache(
    "",
    CACHE_KEY.NHOM_THEO_NB_KHTH_11,
    true,
    false
  );

  const {
    baoCaoDaIn: { getKhth11 },
    khoa: { getKhoaTheoTaiKhoan },
    phong: { getListPhongTongHop },
    loaiBenhAn: { getListAllLoaiBenhAn },
  } = useDispatch();

  useEffect(() => {
    let param = { active: true, page: "", size: "" };
    getKhoaTheoTaiKhoan(param);
    getListPhongTongHop(param);
    getListAllLoaiBenhAn(param);
  }, []);

  const listLoaiDoiTuongCustom = useMemo(() => {
    return (listAllLoaiDoiTuong || []).map((item) => ({
      id: item?.id,
      ten: `${item?.ma} - ${item?.ten}`,
    }));
  }, [listAllLoaiDoiTuong]);

  const listDataKhoa = useMemo(() => {
    if (checkRole([ROLES["BAO_CAO"].KHTH11_TAT_CA_KHOA])) {
      return listAllKhoa;
    } else {
      return listKhoaTheoTaiKhoan;
    }
  }, [listAllKhoa, listKhoaTheoTaiKhoan]);

  const customChange = (name, onChange) => (e) => {
    if (name === "dsKhoaId") {
      if (e.length > 1) {
        onChange("dsPhongId")();
        setShow(false);
      } else {
        getListPhongTongHop({ khoaId: e });
        onChange("dsPhongId")();
        setShow(true);
      }
    }
    onChange(name, true)(e);
  };

  const renderFilter = ({ onChange, _state, onKeyDownDate }) => {
    return (
      <>
        <Row>
          <Col md={8} xl={8} xxl={8}>
            <div className="item-date">
              <label className="label-filter">
                {t("common.denNgay")}
                <span className="icon-required"> *</span>
              </label>
              <DateTimePicker
                showTime={{ defaultValue: moment().startOf("day") }}
                value={_state.denNgay}
                onChange={onChange("denNgay")}
                placeholder={t("common.chonNgay")}
                format="DD/MM/YYYY HH:mm:ss"
                className="select input-filter"
                onKeyDown={onKeyDownDate("denNgay")}
              />
              {!_state.isValidData && !_state.denNgay && (
                <div className="error">{t("baoCao.chonDenNgay")}!</div>
              )}
            </div>
          </Col>
          <Col md={8} xl={8} xxl={8}>
            <div className="item-select">
              <label className="label-filter">{t("common.khoa")}</label>
              <Select
                mode="multiple"
                onChange={customChange("dsKhoaId", onChange)}
                value={_state.dsKhoaId}
                defaultValue={_state.dsKhoaId}
                className="select input-filter"
                placeholder={t("baoCao.chonKhoaNguoiBenh")}
                data={listDataKhoa}
              />
            </div>
          </Col>
          <Col md={8} xl={8} xxl={8}>
            <div className="item-select">
              <label className="label-filter">
                {t("baoCao.phongThucHien")}
              </label>
              <Select
                mode="multiple"
                className="input-filter"
                placeholder={t("baoCao.chonPhongThucHien")}
                data={listPhong}
                onChange={onChange("dsPhongId", true)}
                value={_state.dsPhongId}
                disabled={!show}
              />
            </div>
          </Col>
          <Col md={8} xl={8} xxl={8}>
            <div className="item-select">
              <label className="label-filter">{t("baoCao.doiTuongKcb")}</label>
              <Select
                mode="multiple"
                onChange={onChange("dsDoiTuongKcb", true)}
                value={_state.dsDoiTuongKcb}
                className="select"
                placeholder={t("baoCao.chonDoiTuongKcb")}
                data={listDoiTuongKcb}
              />
            </div>
          </Col>
          <Col md={8} xl={8} xxl={8}>
            <div className="item-select">
              <label className="label-filter">{t("baoCao.doiTuong")}</label>
              <Select
                className="input-filter"
                placeholder={t("baoCao.chonDoiTuong")}
                data={listDoiTuong}
                onChange={onChange("doiTuong")}
                value={_state.doiTuong}
                defaultValue={_state.doiTuong}
              />
            </div>
          </Col>
          <Col md={8} xl={8} xxl={8}>
            <div className="item-select">
              <label className="label-filter">{t("baoCao.loaiDoiTuong")}</label>
              <Select
                mode="multiple"
                className="input-filter"
                placeholder={t("hsba.chonLoaiDoiTuong")}
                data={listLoaiDoiTuongCustom}
                onChange={onChange("dsLoaiDoiTuongId", true)}
                value={_state.dsLoaiDoiTuongId}
              />
            </div>
          </Col>
          <Col md={8} xl={8} xxl={8}>
            <div className="item-select">
              <label className="label-filter">
                {upperFirst(t("common.tuoi"))}
              </label>
              <Select
                className="input-filter"
                placeholder={t("baoCao.chonTuoi")}
                data={LIST_SO_SANH_TUOI}
                onChange={onChange("soSanhTuoi")}
                value={_state.soSanhTuoi}
                hasAllOption
              />
            </div>
          </Col>
          <Col md={8} xl={8} xxl={8}>
            <div className="item-select">
              <label className="label-filter">{t("hsba.loaiBenhAn")}</label>
              <Select
                className="input-filter"
                placeholder={t("hsba.chonLoaiBenhAn")}
                onChange={onChange("dsLoaiBenhAnId", true)}
                value={_state.dsLoaiBenhAnId}
                defaultValue={_state.dsLoaiBenhAnId}
                data={listAllLoaiBenhAn}
                mode="multiple"
              />
            </div>
          </Col>
          <Col md={8} xl={8} xxl={8}>
            <div className="item-select">
              <label className="label-filter">{t("common.gioiTinh")}</label>
              <Select
                className="input-filter"
                placeholder={t("common.chonGioiTinh")}
                data={listGioiTinh}
                onChange={onChange("dsGioiTinh")}
                value={_state.dsGioiTinh}
                mode="multiple"
              />
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-checkbox">
              <Checkbox
                className="label-filter checkbox-header"
                checked={_state.nhomTheoNb}
                onChange={(e) => {
                  onChange("nhomTheoNb")(e);
                  setCacheNhomTheoNb(e.target.checked, false);
                }}
              >
                {t("baoCao.nhomTheoNb")}
              </Checkbox>
            </div>
          </Col>
        </Row>
      </>
    );
  };

  const handleDataSearch = ({ _state }) => ({
    tinhDenNgay: moment(_state.denNgay).format("YYYY-MM-DD HH:mm:ss"),
    dsKhoaId: _state.dsKhoaId,
    dsPhongId: _state.dsPhongId,
    dsDoiTuongKcb: _state.dsDoiTuongKcb,
    doiTuong: _state.doiTuong,
    dsLoaiDoiTuongId: _state.dsLoaiDoiTuongId,
    soSanhTuoi: _state.soSanhTuoi,
    dsLoaiBenhAnId: _state.dsLoaiBenhAnId,
    dsGioiTinh: _state.dsGioiTinh,
    nhomTheoNb: _state.nhomTheoNb,
  });

  const beforeOk =
    ({ _state, _beforeOk }) =>
    () => {
      if (!_state.denNgay) {
        message.error(t("baoCao.chonDenNgay"));
        return false;
      }
      return _beforeOk();
    };

  return (
    <BaseBaoCao
      title={t("baoCao.khth11")}
      renderFilter={renderFilter}
      getBc={getKhth11}
      handleDataSearch={handleDataSearch}
      beforeOk={beforeOk}
      initState={{
        nhomTheoNb: !!cacheNhomTheoNb,
      }}
      breadcrumb={[{ title: "KHTH11", link: "/bao-cao/khth-11" }]}
    />
  );
};

export default Index;
