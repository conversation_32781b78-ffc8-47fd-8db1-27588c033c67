import React, { useEffect, useMemo } from "react";
import { useDispatch } from "react-redux";
import { select } from "redux-store/stores";
import { Col, message, Row, InputNumber } from "antd";
import { Select, DateTimePicker } from "components";
import moment from "moment";
import BaseBaoCao from "pages/baocao/BaseBaoCao";
import { useStore, useEnum } from "hooks";
import { useTranslation } from "react-i18next";
import { concatString } from "utils";
import {
  TRANG_THAI_THANH_TOAN_PHIEU_THU,
  ENUM,
  THIET_LAP_CHUNG,
  LOAI_THOI_GIAN,
} from "constants/index";
import { Main } from "./styled";
import { selectMaTen } from "redux-store/selectors";
import { upperFirst } from "lodash";
import { FilterThoiGian } from "pages/baocao/BaseBaoCao/components";

/**
 * KHTH08.1 Danh sách NB ra viện
 *
 */

const LIST_SO_SANH_TUOI = [
  {
    id: 20,
    i18n: "baoCao.tu0Den6Tuoi",
  },
  {
    id: 50,
    i18n: "baoCao.lonHon6Tuoi",
  },
];

const Index = () => {
  const { t } = useTranslation();
  const [listLoaiThoiGian] = useEnum(ENUM.LOAI_THOI_GIAN);
  const [listDoiTuong] = useEnum(ENUM.DOI_TUONG);
  const [listDoiTuongKcb] = useEnum(ENUM.DOI_TUONG_KCB);
  const [listGioiTinh] = useEnum(ENUM.GIOI_TINH);
  const [listHuongDieuTri] = useEnum(ENUM.HUONG_DIEU_TRI);
  const [listKetQuaDieuTri] = useEnum(ENUM.KET_QUA_DIEU_TRI);
  const listAllLoaiDoiTuong = useStore("loaiDoiTuong.listAllLoaiDoiTuong", []);
  const listAllLoaiBenhAn = useStore("loaiBenhAn.listAllLoaiBenhAn", []);

  const {
    baoCaoDaIn: { getKhth08_1 },
    khoa: { getListAllKhoa },
    loaiDoiTuong: { getListAllLoaiDoiTuong },
    maBenh: { getListAllMaBenh },
    loaiBenhAn: { getListAllLoaiBenhAn },
    nhanVien: { getListAllNhanVien },
    thietLap: { getThietLap },
  } = useDispatch();

  useEffect(() => {
    getListAllKhoa({ page: "", size: "", active: true });
    getListAllLoaiBenhAn({ page: "", size: "", active: true });
    getListAllMaBenh({ page: "", size: "", active: true });
    getListAllLoaiDoiTuong({ page: "", size: "", active: true });
    getThietLap({ ma: THIET_LAP_CHUNG.MA_THU_NGAN }).then((dsMaVaiTro) => {
      getListAllNhanVien({ dsMaVaiTro }).then((res) => { });
    });
  }, []);

  const listLoaiThoiGianCustom = useMemo(() => {
    return listLoaiThoiGian.filter((loaiThoiGian) =>
      [40, 70].some((item) => item === loaiThoiGian.id)
    );
  }, [listLoaiThoiGian]);

  const renderFilter = ({ onChange, _state, onKeyDownDate }) => {
    return (
      <>
        <Row>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter pointer">
                {t("baoCao.theoThoiGian")}
                <span className="icon-required"> *</span>
              </label>
              <Select
                className="input-filter"
                placeholder={t("baoCao.chonLoaiThoiGian")}
                onChange={onChange("loaiThoiGian")}
                value={_state.loaiThoiGian}
                data={listLoaiThoiGianCustom}
              />
              {!_state.isValidData && !_state.loaiThoiGian && (
                <div className="error">
                  {t("baoCao.vuiLongChonLoaiThoiGian")}
                </div>
              )}
            </div>
          </Col>
          <FilterThoiGian
            t={t}
            onChange={onChange}
            _state={_state}
            onKeyDownDate={onKeyDownDate}
          />
          <Col md={6} xl={6} xxl={6}>
            <div className="item-date">
              <label className="label-filter">{t("baoCao.sinhTuNgay")}</label>
              <DateTimePicker
                value={_state.tuNgaySinh}
                onChange={onChange("tuNgaySinh")}
                placeholder={t("common.chonNgay")}
                format="DD/MM/YYYY HH:mm:ss"
                className="input-filter"
                onKeyDown={onKeyDownDate("tuNgaySinh")}
                allowClear
              />
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-date">
              <label className="label-filter">{t("baoCao.sinhDenNgay")}</label>
              <DateTimePicker
                value={_state.denNgaySinh}
                onChange={onChange("denNgaySinh")}
                placeholder={t("common.chonNgay")}
                format="DD/MM/YYYY HH:mm:ss"
                className="input-filter"
                onKeyDown={onKeyDownDate("denNgaySinh")}
                allowClear
              />
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">
                {t("baoCao.trangThaiThanhToan")}
              </label>
              <Select
                className="input-filter"
                placeholder={t("baoCao.chonTrangThaiThanhToan")}
                onChange={onChange("thanhToan")}
                value={_state.thanhToan}
                data={TRANG_THAI_THANH_TOAN_PHIEU_THU}
                hasAllOption
              />
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">{t("baoCao.loaiDoiTuong")}</label>
              <Select
                className="input-filter"
                placeholder={t("baoCao.loaiDoiTuong")}
                onChange={onChange("dsLoaiDoiTuongId", true)}
                value={_state.dsLoaiDoiTuongId}
                data={listAllLoaiDoiTuong}
                mode="multiple"
              />
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">{t("baoCao.doiTuong")}</label>
              <Select
                className="input-filter"
                placeholder={t("baoCao.chonDoiTuong")}
                onChange={onChange("doiTuong")}
                value={_state.doiTuong}
                data={listDoiTuong}
                hasAllOption={true}
              />
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">{t("baoCao.doiTuongKcb")}</label>
              <Select
                className="input-filter"
                placeholder={t("baoCao.chonDoiTuongKcb")}
                onChange={onChange("dsDoiTuongKcb", true)}
                value={_state.dsDoiTuongKcb}
                data={listDoiTuongKcb}
                mode="multiple"
              />
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">{t("baoCao.tenBenhICD10")}</label>
              <Select
                className="input-filter"
                placeholder={t("baoCao.chonTenBenh")}
                onChange={onChange("dsCdChinhId", true)}
                value={_state.dsCdChinhId}
                defaultValue={_state.dsCdChinhId}
                data={select.maBenh.listAllMaBenh}
                getLabel={selectMaTen}
                mode="multiple"
              />
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">
                {t("baoCao.khoaNguoiBenh")}
              </label>
              <Select
                className="input-filter"
                placeholder={t("baoCao.chonKhoaNguoiBenh")}
                onChange={onChange("dsKhoaNbId", true)}
                value={_state.dsKhoaNbId}
                defaultValue={_state.dsKhoaNbId}
                data={select.khoa.listAllKhoa}
                getLabel={selectMaTen}
                mode="multiple"
              />
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">{t("baoCao.benhAn")}</label>
              <Select
                className="input-filter"
                placeholder={concatString(t("common.chon"), t("baoCao.benhAn"))}
                onChange={onChange("dsLoaiBenhAnId", true)}
                value={_state.dsLoaiBenhAnId}
                defaultValue={_state.dsLoaiBenhAnId}
                data={listAllLoaiBenhAn}
                mode="multiple"
              />
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">{t("baoCao.thuNgan")}</label>
              <Select
                className="input-filter"
                placeholder={t("baoCao.chonThuNgan")}
                onChange={onChange("dsThuNganId", true)}
                value={_state.dsThuNganId}
                data={select.nhanVien.listAllNhanVien}
                getLabel={(item) =>
                  `${item.taiKhoan || ""} - ${item.ma} - ${item.ten}`
                }
                mode="multiple"
              />
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">
                {upperFirst(t("common.tuoi"))}
              </label>
              <Select
                className="input-filter"
                placeholder={t("baoCao.chonTuoi")}
                data={LIST_SO_SANH_TUOI}
                onChange={onChange("soSanhTuoi")}
                value={_state.soSanhTuoi}
                hasAllOption
              />
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">{t("common.gioiTinh")}</label>
              <Select
                className="input-filter"
                placeholder={t("common.chonGioiTinh")}
                data={listGioiTinh}
                onChange={onChange("dsGioiTinh")}
                value={_state.dsGioiTinh}
                mode="multiple"
              />
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6} className="filter-col">
            <div className="filter-row">
              <Col md={12} xl={12} xxl={12}>
                <div className="item-input">
                  <label className="label-filter">
                    {t("baoCao.tuCanNang")}
                  </label>
                  <InputNumber
                    className="input-filter"
                    placeholder={t("baoCao.tuCanNang")}
                    value={_state.tuCanNang}
                    onChange={onChange("tuCanNang")}
                    type="number"
                    min={1}
                  />
                </div>
              </Col>
              <Col md={12} xl={12} xxl={12}>
                <div className="item-input">
                  <label className="label-filter">
                    {t("baoCao.denCanNang")}
                  </label>
                  <InputNumber
                    className="input-filter"
                    placeholder={t("baoCao.denCanNang")}
                    value={_state.denCanNang}
                    onChange={onChange("denCanNang")}
                    type="number"
                    min={1}
                  />
                </div>
              </Col>
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6} className="filter-col">
            <div className="filter-row">
              <Col md={12} xl={12} xxl={12}>
                <div className="item-input">
                  <label className="label-filter">
                    {t("baoCao.tuTuanThai")}
                  </label>
                  <InputNumber
                    className="input-filter"
                    placeholder={t("baoCao.tuTuanThai")}
                    value={_state.tuTuanThai}
                    onChange={onChange("tuTuanThai")}
                    type="number"
                    min={1}
                  />
                </div>
              </Col>
              <Col md={12} xl={12} xxl={12}>
                <div className="item-input">
                  <label className="label-filter">
                    {t("baoCao.denTuanThai")}
                  </label>
                  <InputNumber
                    className="input-filter"
                    placeholder={t("baoCao.denTuanThai")}
                    value={_state.denTuanThai}
                    onChange={onChange("denTuanThai")}
                    type="number"
                    min={1}
                  />
                </div>
              </Col>
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">{t("hsba.huongDieuTri")}</label>
              <Select
                className="input-filter"
                placeholder={t("baoCao.chonHuongDieuTri")}
                onChange={onChange("dsHuongDieuTri", true)}
                value={_state.dsHuongDieuTri}
                defaultValue={_state.dsHuongDieuTri}
                data={listHuongDieuTri}
                mode="multiple"
              />
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">{t("hsba.ketQuaDieuTri")}</label>
              <Select
                className="input-filter"
                placeholder={t("baoCao.chonKetQuaDieuTri")}
                onChange={onChange("dsKetQuaDieuTri", true)}
                value={_state.dsKetQuaDieuTri}
                defaultValue={_state.dsKetQuaDieuTri}
                data={listKetQuaDieuTri}
                mode="multiple"
              />
            </div>
          </Col>
        </Row>
      </>
    );
  };

  const handleDataSearch = ({ _state }) => ({
    loaiThoiGian: _state.loaiThoiGian,
    tuThoiGian: moment(_state.tuNgay).format("YYYY-MM-DD HH:mm:ss"),
    denThoiGian: moment(_state.denNgay).format("YYYY-MM-DD HH:mm:ss"),
    tuNgaySinh:
      _state.tuNgaySinh &&
      moment(_state.tuNgaySinh).format("YYYY-MM-DD HH:mm:ss"),
    denNgaySinh:
      _state.denNgaySinh &&
      moment(_state.denNgaySinh).format("YYYY-MM-DD HH:mm:ss"),
    thanhToan: _state.thanhToan,
    dsLoaiDoiTuongId: _state.dsLoaiDoiTuongId,
    doiTuong: _state.doiTuong,
    dsDoiTuongKcb: _state.dsDoiTuongKcb,
    dsKhoaNbId: _state.dsKhoaNbId,
    dsLoaiBenhAnId: _state.dsLoaiBenhAnId,
    dsCdChinhId: _state.dsCdChinhId,
    dsThuNganId: _state.dsThuNganId,
    soSanhTuoi: _state.soSanhTuoi,
    dsGioiTinh: _state.dsGioiTinh,
    tuCanNang: _state.tuCanNang,
    denCanNang: _state.denCanNang,
    tuTuanThai: _state.tuTuanThai,
    denTuanThai: _state.denTuanThai,
    dsHuongDieuTri: _state.dsHuongDieuTri,
    dsKetQuaDieuTri: _state.dsKetQuaDieuTri
  });

  const beforeOk =
    ({ _state, _beforeOk }) =>
      () => {
        if (!_state.loaiThoiGian) {
          message.error(t("baoCao.vuiLongChonLoaiThoiGian"));
          return false;
        }
        return _beforeOk();
      };

  return (
    <Main>
      <BaseBaoCao
        title={t("baoCao.khth08_1")}
        renderFilter={renderFilter}
        getBc={getKhth08_1}
        handleDataSearch={handleDataSearch}
        initState={{
          loaiThoiGian: LOAI_THOI_GIAN.THEO_THOI_GIAO_RA_VIEN,
          thanhToan: [""],
          doiTuong: [""],
          dsThuNganId: [""],
        }}
        beforeOk={beforeOk}
        breadcrumb={[{ title: "KHTH08.1", link: "/bao-cao/khth-08_1" }]}
      />
    </Main>
  );
};

export default Index;
