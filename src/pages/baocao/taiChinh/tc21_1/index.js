import React, { useState, useEffect, useMemo } from "react";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import { Col, Row, message } from "antd";
import moment from "moment";
import { query } from "redux-store/stores";

import {
  useLazyKVMap,
  useQueryAll,
  useStore,
  useThietLap,
  useEnum,
  useCache,
} from "hooks";

import {
  DateTimePicker,
  Select,
  SelectLoadMore,
  Checkbox,
  InputTimeout,
} from "components";
import BaseBaoCao from "../../BaseBaoCao";
import dichVuProvider from "data-access/categories/dm-dich-vu-provider";
import {
  DS_DOI_TUONG_BAO_HIEM,
  THIET_LAP_CHUNG,
  ENUM,
  PHAN_LOAI_DOI_TUONG_KCB,
} from "constants/index";
import { select } from "redux-store/stores";
import { Main, GlobalStyle, InputWrapper } from "./styled";
import { selectMaTen } from "redux-store/selectors";
import {
  LOAI_PHIEU_THU_TRONG_VIEN,
  parseSoHoaDonParams,
} from "pages/baocao/utils";
import { isArray, openInNewTab } from "utils/index";
import ChonNhomBaoCao from "pages/baocao/BaseBaoCao/components/ChonNhomBaoCao";
import { CACHE_KEY } from "constants/index";

/**
 * TC21.1. Báo cáo chi tiền dịch vụ
 *
 */

const MUC_HUONG = [
  {
    id: "true",
    i18n: "baoCao.dvDuocHuongBh",
  },
  {
    id: "false",
    i18n: "baoCao.dvKhongDuocHuongBh",
  },
];

const Index = () => {
  const [state, _setState] = useState({
    addParamDv: {},
  });

  const setState = (data) => {
    _setState((pre) => ({ ...pre, ...data }));
  };
  const { t } = useTranslation();

  const {
    baoCaoDaIn: { getTc21_1 },
    khoa: { getListAllKhoa },
    nhomDichVuCap1: { getAllTongHopDichVuCap1 },
    phong: { getListAllPhong },
    toaNha: { getListAllToaNha },
    quayTiepDon: { getListAllQuayTiepDon },
    loaiHinhThanhToan: { getListAllLoaiHinhThanhToan },
    phuongThucTT: { getListAllPhuongThucThanhToan },
    nhomDichVuCap2: { getAllTongHopDichVuCap2 },
    nhomDichVuCap3: { getAllTongHopDichVuCap3 },
  } = useDispatch();

  const auth = useStore("auth.auth");
  const listAllPhong = useStore("phong.listAllPhong", []);
  const listAllToaNha = useStore("toaNha.listAllToaNha", []);
  const listAllNhomDichVuCap1 = useStore(
    "nhomDichVuCap1.listAllNhomDichVuCap1",
    []
  );
  const listAllNhomDichVuCap2 = useStore(
    "nhomDichVuCap2.listAllNhomDichVuCap2",
    []
  );
  const listAllNhomDichVuCap3 = useStore(
    "nhomDichVuCap3.listAllNhomDichVuCap3",
    []
  );
  const listAllQuayTiepDon = useStore("quayTiepDon.listAllQuayTiepDon", []);
  const listAllLoaiHinhThanhToan = useStore(
    "loaiHinhThanhToan.listAllLoaiHinhThanhToan",
    []
  );
  const listAllPhuongThucThanhToan = useStore(
    "phuongThucTT.listAllPhuongThucThanhToan",
    []
  );
  const [getPhanLoaiDoiTuongKcb] = useLazyKVMap(PHAN_LOAI_DOI_TUONG_KCB);
  const [listLoaiPhieuThu] = useEnum(ENUM.LOAI_PHIEU_THU);
  const [dataMA_THU_NGAN, finishGetMaThuNgan] = useThietLap(
    THIET_LAP_CHUNG.MA_THU_NGAN
  );
  const [cacheDoiTuongKcb, setCacheDoiTuongKcb] = useCache(
    "",
    CACHE_KEY.DOI_TUONG_KCB_TC21_1,
    [""],
    false
  );
  const [cacheLoaiHinhThanhToan, setCacheLoaiHinhThanhToan] = useCache(
    "",
    CACHE_KEY.LOAI_HINH_THANH_TOAN_TC21_1,
    [""],
    false
  );
  const [cacheNhomDichVu, setCacheNhomDichVu] = useCache(
    "",
    CACHE_KEY.NHOM_DICH_VU_TC21_1,
    [""],
    false
  );
  const [cacheNhomDichVuCap2, setCacheNhomDichVuCap2] = useCache(
    "",
    CACHE_KEY.NHOM_DV_CAP_2_TC21_1,
    [""],
    false
  );
  const [cacheTheoKhoaThucHien, setCacheTheoKhoaThucHien] = useCache(
    "",
    CACHE_KEY.THEO_KHOA_THUC_HIEN_TC21_1,
    false,
    false
  );

  const { data: listThuNgan } = useQueryAll(
    query.nhanVien.queryAllNhanVien({
      params: {
        dsMaVaiTro: dataMA_THU_NGAN,
      },
      enabled: finishGetMaThuNgan,
    })
  );

  const listEnumLoaiPhieuThuTrongVien = useMemo(() => {
    return listLoaiPhieuThu.map((x) => x.id).filter((x) => ![6].includes(x));
  }, [listLoaiPhieuThu]);

  const listLoaiPhieuThuEnumCustom = useMemo(() => {
    return [
      { id: 6, ten: t("baoCao.thuNhaThuoc") },
      {
        id: LOAI_PHIEU_THU_TRONG_VIEN,
        ten: t("baoCao.thuTrongVien"),
      },
    ];
  }, [listLoaiPhieuThu]);

  useEffect(() => {
    const param = { active: true, page: "", size: "" };
    getListAllKhoa(param);
    getListAllPhong(param);
    getListAllToaNha(param);
    getAllTongHopDichVuCap1(param);
    getListAllQuayTiepDon({ ...param, dsLoai: 20 });
    getListAllLoaiHinhThanhToan(param);
    getListAllPhuongThucThanhToan(param);
    getAllTongHopDichVuCap2(param);
    getAllTongHopDichVuCap3(param);
  }, []);

  const handleChange = (key, onChange, _state) => (value) => {
    if (key === "dsNhomDichVuCap1Id") {
      setState({
        addParamDv: { ...state.addParamDv, dsNhomDichVuCap1Id: value },
      });
      onChange(key, true)(value);
      setCacheNhomDichVu(value, false);
    }
  };

  const renderFilter = ({ onChange, _state }) => {
    return (
      <>
        <GlobalStyle />
        <Row>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-date">
              <label className="label-filter">
                {t("common.tuNgay")} <span className="icon-required">*</span>
              </label>
              <DateTimePicker
                showTime={{ defaultValue: moment().startOf("day") }}
                value={_state.tuNgay}
                onChange={onChange("tuNgay")}
                placeholder={t("common.chonNgay")}
                format="DD/MM/YYYY HH:mm:ss"
                className="input-filter"
              />
              {!_state.isValidData && !_state.tuNgay && (
                <div className="error">{t("baoCao.chonTuNgay")}!</div>
              )}
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-date">
              <label className="label-filter">
                {t("common.denNgay")} <span className="icon-required">*</span>
              </label>
              <DateTimePicker
                showTime={{ defaultValue: moment().endOf("day") }}
                value={_state.denNgay}
                onChange={onChange("denNgay")}
                placeholder={t("common.chonNgay")}
                format="DD/MM/YYYY HH:mm:ss"
                className="input-filter"
              />
              {!_state.isValidData && !_state.denNgay && (
                <div className="error">{t("baoCao.chonDenNgay")}!</div>
              )}
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label
                className="label-filter cursor-pointer"
                onClick={() => openInNewTab("/danh-muc/khoa")}
              >
                {t("baoCao.khoaThucHien")}
              </label>
              <Select
                mode="multiple"
                onChange={onChange("dsKhoaThucHienId", true)}
                className="input-filter"
                value={_state.dsKhoaThucHienId}
                placeholder={t("baoCao.chonKhoaThucHien")}
                data={select.khoa.listAllKhoa}
                getLabel={selectMaTen}
              />
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label
                className="label-filter cursor-pointer"
                onClick={() => openInNewTab("/danh-muc/phong")}
              >
                {t("baoCao.phongThucHien")}
              </label>
              <Select
                className="input-filter"
                placeholder={t("baoCao.chonPhongThucHien")}
                value={_state.dsPhongThucHienId}
                onChange={onChange("dsPhongThucHienId")}
                data={listAllPhong}
                mode="multiple"
              />
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label
                className="label-filter cursor-pointer"
                onClick={() => openInNewTab("/danh-muc/khoa")}
              >
                {t("baoCao.khoaChiDinh")}
              </label>
              <Select
                onChange={onChange("dsKhoaChiDinhId", true)}
                className="input-filter"
                value={_state.dsKhoaChiDinhId}
                placeholder={t("baoCao.chonKhoaChiDinh")}
                data={select.khoa.listAllKhoa}
                getLabel={selectMaTen}
                mode="multiple"
              />
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label
                className="label-filter cursor-pointer"
                onClick={() => openInNewTab("/danh-muc/phong")}
              >
                {t("baoCao.phongChiDinh")}
              </label>
              <Select
                className="input-filter"
                placeholder={t("baoCao.chonPhongChiDinh")}
                onChange={onChange("dsPhongChiDinhId")}
                value={_state.dsPhongChiDinhId}
                data={listAllPhong}
                hasAllOption={true}
              />
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label
                className="label-filter cursor-pointer"
                onClick={() => openInNewTab("/quan-tri/nhan-vien")}
              >
                {t("baoCao.bacSiChiDinh")}
              </label>
              <Select
                onChange={onChange("bacSiChiDinhId")}
                value={_state.bacSiChiDinhId}
                className="input-filter"
                placeholder={t("baoCao.chonBacSi")}
                data={select.nhanVien.listAllNhanVien}
                getLabel={(item) =>
                  `${item.taiKhoan || ""} - ${item.ma} - ${item.ten}`
                }
                hasAllOption={true}
              />
            </div>
          </Col>

          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">{t("baoCao.nhaThuNgan")}</label>
              <Select
                className="input-filter"
                placeholder={t("baoCao.chonNhaThuNgan")}
                onChange={onChange("dsNhaThuNganId")}
                value={_state.dsNhaThuNganId}
                data={listAllToaNha}
              />
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">{t("baoCao.thuNgan")}</label>
              <Select
                onChange={onChange("dsThuNganId", true)}
                value={_state.dsThuNganId}
                className="input-filter"
                placeholder={t("baoCao.chonThuNgan")}
                data={listThuNgan}
                getLabel={(item) =>
                  `${item.taiKhoan || ""} - ${item.ma} - ${item.ten}`
                }
                mode="multiple"
              />
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">
                {t("baoCao.loaiDoiTuongKCB")}
              </label>
              <Select
                mode="multiple"
                onChange={(e) => {
                  onChange("dsDoiTuongKcb", true)(e);
                  setCacheDoiTuongKcb(e, false);
                }}
                value={_state.dsDoiTuongKcb}
                className="input-filter"
                placeholder={t("baoCao.chonDoiTuongKCB")}
                data={PHAN_LOAI_DOI_TUONG_KCB}
              />
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">{t("baoCao.doiTuong")}</label>
              <Select
                className="input-filter"
                placeholder={t("baoCao.doiTuong")}
                data={DS_DOI_TUONG_BAO_HIEM}
                onChange={onChange("doiTuong")}
                value={_state.doiTuong}
                hasAllOption={true}
              />
            </div>
          </Col>

          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">{t("baoCao.nhomDichVu")}</label>
              <Select
                onChange={handleChange("dsNhomDichVuCap1Id", onChange, _state)}
                value={_state.dsNhomDichVuCap1Id}
                className="input-filter"
                placeholder={t("baoCao.chonNhomDichVu")}
                data={listAllNhomDichVuCap1}
                mode="multiple"
              />
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">{t("baoCao.tenDichVu")}</label>
              <SelectLoadMore
                api={dichVuProvider.searchAll}
                mapData={(i) => ({
                  value: i.id,
                  label: i.ten,
                })}
                onChange={onChange("dsDichVuId", true)}
                value={_state.dsDichVuId}
                keySearch={"ten"}
                placeholder={t("baoCao.chonDichVu")}
                className="input-filter"
                addParam={state.addParamDv}
                blurReset={true}
                mode={"multiple"}
                hasAll={true}
              />
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">{t("baoCao.quayThu")}</label>
              <Select
                className="input-filter"
                placeholder={t("danhMuc.chonLoaiQuay")}
                onChange={onChange("dsQuayId", true)}
                value={_state.dsQuayId}
                data={listAllQuayTiepDon}
                mode="multiple"
              />
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label
                className="label-filter cursor-pointer"
                onClick={() => openInNewTab("/danh-muc/loai-hinh-thanh-toan")}
              >
                {t("baoCao.loaiHinhThanhToan")}
              </label>
              <Select
                className="input-filter"
                placeholder={t("baoCao.chonLoaiHinhThanhToan")}
                data={listAllLoaiHinhThanhToan}
                onChange={(e) => {
                  onChange("dsLoaiHinhThanhToanId", true)(e);
                  setCacheLoaiHinhThanhToan(e, false);
                }}
                value={_state.dsLoaiHinhThanhToanId}
                mode="multiple"
              />
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <InputWrapper>
              <label className="label-filter">{t("thuNgan.soHoaDon")}</label>
              <InputTimeout
                className="input-filter"
                value={_state.soHoaDon}
                placeholder={t("baoCao.nhapSoHoaDonFormat")}
                onChange={onChange("soHoaDon")}
              />
            </InputWrapper>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">
                {t("danhMuc.phuongThucThanhToan")}
              </label>
              <Select
                className="input-filter"
                placeholder={t("baoCao.chonPhuongThucThanhToan")}
                onChange={onChange("dsPhuongThucTtId")}
                value={_state.dsPhuongThucTtId}
                data={listAllPhuongThucThanhToan}
                mode="multiple"
              />
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter cursor-pointer">
                {t("tenTruong.mucHuong")}
              </label>
              <Select
                className="input-filter"
                placeholder={t("baoCao.chonMucHuong")}
                data={MUC_HUONG}
                onChange={onChange("huongBaoHiem")}
                value={_state.huongBaoHiem}
                hasAllOption={true}
              />
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">
                {t("baoCao.nhomDichVuCap2")}
              </label>
              <Select
                onChange={(e) => {
                  onChange("dsNhomDichVuCap2Id", true)(e);
                  setCacheNhomDichVuCap2(e, false);
                }}
                value={_state.dsNhomDichVuCap2Id}
                className="input-filter"
                mode="multiple"
                placeholder={t("danhMuc.chonNhomDichVuCap2")}
                data={listAllNhomDichVuCap2}
              />
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">
                {t("baoCao.nhomDichVuCap3")}
              </label>
              <Select
                className="input-filter"
                placeholder={t("danhMuc.chonNhomDichVuCap3")}
                onChange={onChange("dsNhomDichVuCap3Id", true)}
                value={_state.dsNhomDichVuCap3Id}
                data={listAllNhomDichVuCap3}
                mode="multiple"
              />
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">{t("baoCao.loaiPhieuThu")}</label>
              <Select
                className="input-filter"
                placeholder={t("baoCao.chonLoaiPhieuThu")}
                onChange={(e) => {
                  onChange("dsLoaiPhieuThu")(e);
                  onChange("dsKhoId")();
                }}
                value={_state.dsLoaiPhieuThu}
                data={listLoaiPhieuThuEnumCustom}
                mode="multiple"
              />
            </div>
          </Col>
          <ChonNhomBaoCao onChange={onChange} _state={_state} />
          <Col md={6} xl={6} xxl={6}>
            <div className="item-checkbox">
              <Checkbox
                className="label-filter checkbox-header"
                checked={_state.theoKhoaThucHien}
                onChange={(e) => {
                  onChange("theoKhoaThucHien")(e);
                  setCacheTheoKhoaThucHien(e.target.checked, false);
                }}
              >
                {t("baoCao.theoKhoaThucHien")}
              </Checkbox>
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-checkbox">
              <Checkbox
                className="label-filter checkbox-header"
                checked={_state.doiTra}
                onChange={onChange("doiTra")}
              >
                {t("baoCao.tongHopThuChi")}
              </Checkbox>
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-checkbox">
              <Checkbox
                className="label-filter checkbox-header"
                checked={_state.theoYeuCau}
                onChange={onChange("theoYeuCau")}
              >
                {t("baoCao.theoYeuCau")}
              </Checkbox>
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-checkbox">
              <Checkbox
                className="label-filter checkbox-header"
                checked={_state.tachGia}
                onChange={onChange("tachGia")}
              >
                {t("baoCao.mauBaCotGia")}
              </Checkbox>
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-checkbox">
              <Checkbox
                className="label-filter checkbox-header"
                checked={_state.nhomTheoNhomDichVuCap3}
                onChange={onChange("nhomTheoNhomDichVuCap3")}
              >
                {t("baoCao.inTheoNhomDichVuCap3")}
              </Checkbox>
            </div>
          </Col>
        </Row>
      </>
    );
  };

  const convertDSLoaiPhieuThu = (data) => {
    if (isArray(data) && data.includes(LOAI_PHIEU_THU_TRONG_VIEN)) {
      if (data.length === listLoaiPhieuThuEnumCustom.length) {
        return null;
      }

      let _dsLoaiPhieuThu = data.filter((x) => x !== LOAI_PHIEU_THU_TRONG_VIEN);
      return [..._dsLoaiPhieuThu, ...listEnumLoaiPhieuThuTrongVien];
    }

    return data;
  };

  const handleDataSearch = ({ _state }) => {
    const { tuSoHoaDon, denSoHoaDon, dsSoHoaDon } = parseSoHoaDonParams(
      _state.soHoaDon
    );

    return {
      tuThoiGian: moment(_state.tuNgay).format("DD-MM-YYYY HH:mm:ss"),
      denThoiGian: moment(_state.denNgay).format("DD-MM-YYYY HH:mm:ss"),
      dsKhoaThucHienId: _state.dsKhoaThucHienId,
      dsPhongThucHienId: _state.dsPhongThucHienId,
      dsKhoaChiDinhId: _state.dsKhoaChiDinhId,
      dsPhongChiDinhId: _state.dsPhongChiDinhId,
      bacSiChiDinhId: _state.bacSiChiDinhId,
      dsNhaThuNganId: _state.dsNhaThuNganId,
      dsThuNganId: _state.dsThuNganId,
      dsDoiTuongKcb:
        _state.dsDoiTuongKcb && _state.dsDoiTuongKcb[0] !== ""
          ? _state.dsDoiTuongKcb.flatMap(
              (id) => getPhanLoaiDoiTuongKcb(id)?.referIds
            )
          : null,
      doiTuong: _state.doiTuong,
      dsNhomDichVuCap1Id: _state.dsNhomDichVuCap1Id,
      dsDichVuId: _state.dsDichVuId,
      dsQuayId: _state.dsQuayId,
      dsLoaiHinhThanhToanId: _state.dsLoaiHinhThanhToanId,
      theoKhoaThucHien: _state.theoKhoaThucHien,
      tuSoHoaDon,
      denSoHoaDon,
      dsSoHoaDon,
      dsPhuongThucTtId: _state.dsPhuongThucTtId,
      huongBaoHiem: _state.huongBaoHiem,
      dsNhomDichVuCap2Id: _state.dsNhomDichVuCap2Id,
      dsNhomDichVuCap3Id: _state.dsNhomDichVuCap3Id,
      dsLoaiPhieuThu: convertDSLoaiPhieuThu(_state.dsLoaiPhieuThu),
      doiTra: _state.doiTra,
      theoYeuCau: _state.theoYeuCau,
      tachGia: _state.tachGia,
      maNhomBaoCao: _state.maNhomBaoCao,
      nhomTheoNhomDichVuCap3: _state.nhomTheoNhomDichVuCap3,
    };
  };

  const beforeOk =
    ({ _state, _beforeOk }) =>
    () => {
      if (!_state.tuNgay) {
        message.error(t("baoCao.chonTuNgay"));
        return false;
      }
      if (!_state.denNgay) {
        message.error(t("baoCao.chonDenNgay"));
        return false;
      }
      return _beforeOk();
    };

  return (
    <Main>
      <BaseBaoCao
        title={t("baoCao.tc21_1")}
        breadcrumb={[{ title: "TC21.1", link: "/bao-cao/tc21_1" }]}
        renderFilter={renderFilter}
        getBc={getTc21_1}
        initState={{
          dsKhoaThucHienId: [""],
          dsPhongThucHienId: [""],
          dsKhoaChiDinhId: [""],
          dsPhongChiDinhId: [""],
          bacSiChiDinhId: [""],
          dsDoiTuongKcb: cacheDoiTuongKcb,
          dsNhomDichVuCap1Id: cacheNhomDichVu,
          dsQuayId: [""],
          dsLoaiHinhThanhToanId: cacheLoaiHinhThanhToan,
          dsPhuongThucTtId: [""],
          dsNhomDichVuCap2Id: cacheNhomDichVuCap2,
          dsNhomDichVuCap3Id: [""],
          doiTra: false,
          theoYeuCau: false,
          tachGia: false,
          nhomTheoNhomDichVuCap3: false,
          theoKhoaThucHien: cacheTheoKhoaThucHien,
        }}
        handleDataSearch={handleDataSearch}
        beforeOk={beforeOk}
      />
    </Main>
  );
};

export default Index;
