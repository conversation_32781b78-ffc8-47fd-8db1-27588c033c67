import React, { useEffect } from "react";
import { Col, message, Row } from "antd";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import moment from "moment";
import { useEnum, useLazyKVMap, useStore } from "hooks";
import { Select, DateTimePicker, Checkbox } from "components";
import BaseBaoCao from "../../BaseBaoCao";
import {
  THIET_LAP_CHUNG,
  CHENH_LECH,
  DS_DOI_TUONG_BAO_HIEM,
  ENUM,
  PHAN_LOAI_DOI_TUONG_KCB,
} from "constants/index";
import { select } from "redux-store/stores";
import { selectMaTen } from "redux-store/selectors";
import { isArray } from "utils/index";
import { ChonCoSoChiNhanh } from "../../BaseBaoCao/components";
/**
 * TC29.1. <PERSON>h sách ngư<PERSON>i bệnh đã thanh toán
 *
 */

const Index = () => {
  const { t } = useTranslation();
  const [listDoiTuongKcb] = useEnum(ENUM.DOI_TUONG_KCB);
  const listAllQuayTiepDon = useStore("quayTiepDon.listAllQuayTiepDon", []);
  const listAllCaLamViec = useStore("caLamViec.listAllCaLamViec", []);
  const auth = useStore("auth.auth", []);
  const [getPhanLoaiDoiTuongKcb] = useLazyKVMap(PHAN_LOAI_DOI_TUONG_KCB);


  const {
    baoCaoDaIn: { getTc29_1 },
    khoa: { getListAllKhoa },
    caLamViec: { getListAllCaLamViec },
    quayTiepDon: { getListAllQuayTiepDon },
    nhanVien: { getListAllNhanVien },
    thietLap: { getThietLap },
  } = useDispatch();

  useEffect(() => {
    getListAllKhoa({ page: "", size: "", active: true });
    getListAllQuayTiepDon({ page: "", size: "", active: true, dsLoai: 20 });
    getListAllCaLamViec({ page: "", size: "", active: true });
    getThietLap({ ma: THIET_LAP_CHUNG.MA_THU_NGAN }).then((dsMaVaiTro) => {
      getListAllNhanVien({ dsMaVaiTro, page: "", size: "", active: true });
    });
  }, []);

  const renderFilter = ({ onChange, _state, onKeyDownDate }) => {
    return (
      <>
        <Row>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-date">
              <label className="label-filter">
                {t("common.tuNgay")} <span className="icon-required">*</span>
              </label>
              <DateTimePicker
                showTime={{ defaultValue: moment().startOf("day") }}
                value={_state.tuNgay}
                onChange={onChange("tuNgay")}
                placeholder={t("common.chonNgay")}
                format="DD/MM/YYYY HH:mm:ss"
                className="input-filter"
                onKeyDown={onKeyDownDate("tuNgay")}
              />
              {!_state.isValidData && !_state.tuNgay && (
                <div className="error">{t("baoCao.chonTuNgay")}!</div>
              )}
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-date">
              <label className="label-filter">
                {t("common.denNgay")} <span className="icon-required">*</span>
              </label>
              <DateTimePicker
                showTime={{ defaultValue: moment().startOf("day") }}
                value={_state.denNgay}
                onChange={onChange("denNgay")}
                placeholder={t("common.chonNgay")}
                format="DD/MM/YYYY HH:mm:ss"
                className="input-filter"
                onKeyDown={onKeyDownDate("denNgay")}
              />
              {!_state.isValidData && !_state.denNgay && (
                <div className="error">{t("baoCao.chonDenNgay")}!</div>
              )}
            </div>
          </Col>
          <ChonCoSoChiNhanh onChange={onChange} _state={_state} />
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">
                {t("baoCao.doiTuongNguoiBenh")}
              </label>
              <Select
                className="input-filter"
                placeholder={t("baoCao.chonDoiTuongNguoiBenh")}
                value={_state.dsDoiTuong}
                onChange={onChange("dsDoiTuong", true)}
                data={DS_DOI_TUONG_BAO_HIEM}
                mode="multiple"
              />
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">
                {t("baoCao.khoaNguoiBenh")}
              </label>
              <Select
                className="input-filter"
                placeholder={t("baoCao.chonKhoaNguoiBenh")}
                onChange={onChange("dsKhoaNbId", true)}
                value={_state.dsKhoaNbId}
                data={select.khoa.listAllKhoa}
                getLabel={selectMaTen}
                mode="multiple"
              />
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">{t("baoCao.chenhLech")}</label>
              <Select
                className="input-filter"
                placeholder={t("baoCao.chonGiaTri")}
                onChange={onChange("chenhLech")}
                value={_state.chenhLech}
                data={CHENH_LECH}
              />
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">{t("baoCao.quayThu")}</label>
              <Select
                className="input-filter"
                placeholder={t("danhMuc.chonLoaiQuay")}
                onChange={onChange("dsQuayId", true)}
                value={_state.dsQuayId}
                data={listAllQuayTiepDon}
                mode="multiple"
              />
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">{t("danhMuc.caLamViec")}</label>
              <Select
                className="input-filter"
                placeholder={t("danhMuc.chonLoaiQuay")}
                onChange={onChange("dsCaLamViecId", true)}
                value={_state.dsCaLamViecId}
                data={listAllCaLamViec}
                mode="multiple"
              />
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">{t("thuNgan.tenThuNgan")}</label>
              <Select
                className="input-filter"
                placeholder={t("baoCao.chonThuNgan")}
                onChange={onChange("dsThuNganId", true)}
                value={_state.dsThuNganId}
                data={select.nhanVien.listAllNhanVien}
                getLabel={(item) =>
                  `${item.taiKhoan || ""} - ${item.ma} - ${item.ten}`
                }
                mode="multiple"
              />
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">{t("baoCao.doiTuongKcb")}</label>
              <Select
                className="input-filter"
                placeholder={t("baoCao.chonDoiTuongKcb")}
                onChange={onChange("dsDoiTuongKcb", true)}
                value={_state.dsDoiTuongKcb}
                data={PHAN_LOAI_DOI_TUONG_KCB}
                mode="multiple"
              />
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select checkbox-pl">
              <Checkbox
                checked={_state.loaiLienKet}
                onChange={onChange("loaiLienKet")}
              >
                {t("baoCao.chiLocHoSoCon")}
              </Checkbox>
            </div>
          </Col>
        </Row>
      </>
    );
  };

  const handleDataSearch = ({ _state }) => {
    return {
      tuThoiGian: moment(_state.tuNgay).format("DD-MM-YYYY HH:mm:ss"),
      denThoiGian: moment(_state.denNgay).format("DD-MM-YYYY HH:mm:ss"),
      dsCoSoKcbId: _state.dsCoSoKcbId,
      dsDoiTuong: _state.dsDoiTuong,
      dsKhoaNbId: _state.dsKhoaNbId,
      chenhLech: _state.chenhLech,
      dsQuayId: _state.dsQuayId,
      dsCaLamViecId: _state.dsCaLamViecId,
      dsThuNganId: _state.dsThuNganId,
      dsDoiTuongKcb:
        _state.dsDoiTuongKcb && _state.dsDoiTuongKcb[0] !== ""
          ? _state.dsDoiTuongKcb.flatMap(
            (id) => getPhanLoaiDoiTuongKcb(id)?.referIds
          )
          : null,
      ...(_state.loaiLienKet ? { loaiLienKet: 10 } : {}),
    };
  };

  const beforeOk =
    ({ _state, _beforeOk }) =>
      () => {
        if (!isArray(_state.dsCoSoKcbId, 1)) {
          message.error(
            t("danhMuc.vuiLongChonTitle", {
              title: t("baoCao.coSoChiNhanh"),
            })
          );
          return false;
        }
        return _beforeOk();
      };

  return (
    <BaseBaoCao
      title={t("baoCao.tc29_1")}
      breadcrumb={[{ title: "TC29.1", link: "/bao-cao/tc29_1" }]}
      renderFilter={renderFilter}
      getBc={getTc29_1}
      initState={{
        dsDoiTuongKcb: [""],
        dsKhoaNbId: [""],
        dsDoiTuong: [""],
        chenhLech: [""],
        dsQuayId: [""],
        dsCaLamViecId: [""],
        dsThuNganId: [auth?.nhanVienId || ""],
      }}
      handleDataSearch={handleDataSearch}
      beforeOk={beforeOk}
    />
  );
};

export default Index;
