import React, { useMemo } from "react";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import moment from "moment";
import { Col, message, Row } from "antd";
import { useListAll, useCache, useThietLap, useLazyKVMap } from "hooks";
import { Select, DateTimePicker, Checkbox } from "components";
import BaseBaoCao from "pages/baocao/BaseBaoCao";
import {
  CACHE_KEY,
  PHAN_LOAI_DOI_TUONG_KCB,
  LOAI_THU_NHAP_VAO,
  THIET_LAP_CHUNG,
} from "constants/index";

const MAU_BAO_CAO = [
  {
    id: 10,
    ten: "Mẫu 01: TỔNG HỢP DANH SÁCH THU DỊCH VỤ KHÁM BỆNH, CHỮA BỆNH",
    i18n: "baoCao.mau01_TC82",
  },
  {
    id: 20,
    ten: "Mẫu 02: TỔNG HỢP THU DỊCH VỤ KHÁM BỆNH, CHỮA BỆNH",
    i18n: "baoCao.mau02_TC82",
  },
];

const THEO_KHOA_CHI_DINH = [
  {
    id: true,
    i18n: "baoCao.khoaChiDinh",
  },
  {
    id: false,
    i18n: "baoCao.khoaThucHien",
  },
];

const NHOM_CHI_PHI = [
  { id: "KHAM", i18n: "khamBenh.khamBenh" },
  { id: "GIUONG", i18n: "kpis.ngayGiuong" },
  { id: "XET_NGHIEM", i18n: "danhMuc.xetNghiem" },
  { id: "CDHA_TDCN_PHCN", i18n: "baoCao.CDHA_TDCN" },
  { id: "TT_PT", i18n: "baoCao.TT_PT" },
  { id: "MAU", i18n: "editor.mau" },
  { id: "THUOC", i18n: "baoCao.thuocDich" },
  { id: "VTYT", i18n: "kho.vtyt" },
  { id: "TIEN_AN", i18n: "baoCao.tienAn" },
  { id: "KHAC", i18n: "common.khac" },
];

const TC82 = () => {
  const { t } = useTranslation();
  const {
    baoCaoDaIn: { getTc82 },
  } = useDispatch();

  const [listAllKhoa] = useListAll("khoa", {}, true);
  const [listAllNhomDichVuCap1] = useListAll("nhomDichVuCap1", {}, true);
  const [getPhanLoaiDoiTuongKcb] = useLazyKVMap(PHAN_LOAI_DOI_TUONG_KCB);
  const [cacheDoiTuongKcb, setCacheDoiTuongKcb] = useCache(
    "",
    CACHE_KEY.DATA_DOI_TUONG_KCB_TC82,
    "",
    false
  );

  const [dataMA_NHOM_DICH_VU_CAP1_KHAM] = useThietLap(
    THIET_LAP_CHUNG.MA_NHOM_DICH_VU_CAP1_KHAM,
    ""
  );
  const [dataMA_NHOM_DICH_VU_CAP1_GIUONG] = useThietLap(
    THIET_LAP_CHUNG.MA_NHOM_DICH_VU_CAP1_GIUONG,
    ""
  );
  const [dataMA_NHOM_DICH_VU_CAP1_XET_NGHIEM] = useThietLap(
    THIET_LAP_CHUNG.MA_NHOM_DICH_VU_CAP1_XET_NGHIEM,
    ""
  );
  const [dataMA_NHOM_DICH_VU_CAP1_CDHA] = useThietLap(
    THIET_LAP_CHUNG.MA_NHOM_DICH_VU_CAP1_CDHA,
    ""
  );
  const [dataMA_NHOM_DICH_VU_CAP1_TDCN] = useThietLap(
    THIET_LAP_CHUNG.MA_NHOM_DICH_VU_CAP1_TDCN,
    ""
  );
  const [dataMA_NHOM_DICH_VU_CAP1_PHCN] = useThietLap(
    THIET_LAP_CHUNG.MA_NHOM_DICH_VU_CAP1_PHCN,
    ""
  );
  const [dataMA_NHOM_DICH_VU_CAP1_TT] = useThietLap(
    THIET_LAP_CHUNG.MA_NHOM_DICH_VU_CAP1_TT,
    ""
  );
  const [dataMA_NHOM_DICH_VU_CAP1_PT] = useThietLap(
    THIET_LAP_CHUNG.MA_NHOM_DICH_VU_CAP1_PT,
    ""
  );
  const [dataMA_NHOM_DICH_VU_CAP1_MAU] = useThietLap(
    THIET_LAP_CHUNG.MA_NHOM_DICH_VU_CAP1_MAU,
    ""
  );
  const [dataMA_NHOM_DICH_VU_CAP1_THUOC] = useThietLap(
    THIET_LAP_CHUNG.MA_NHOM_DICH_VU_CAP1_THUOC,
    ""
  );
  const [dataMA_NHOM_DICH_VU_CAP1_VAT_TU] = useThietLap(
    THIET_LAP_CHUNG.MA_NHOM_DICH_VU_CAP1_VAT_TU,
    ""
  );
  const [dataMA_NHOM_DICH_VU_CAP1_SUAT_AN] = useThietLap(
    THIET_LAP_CHUNG.MA_NHOM_DICH_VU_CAP1_SUAT_AN,
    ""
  );
  const [dataMA_NHOM_DICH_VU_CAP1_CPDD] = useThietLap(
    THIET_LAP_CHUNG.MA_NHOM_DICH_VU_CAP1_CPDD,
    ""
  );
  const [dataMA_NHOM_DICH_VU_CAP1_KHAC] = useThietLap(
    THIET_LAP_CHUNG.MA_NHOM_DICH_VU_CAP1_KHAC,
    ""
  );
  const [dataMA_NHOM_DICH_VU_CAP1_TS] = useThietLap(
    THIET_LAP_CHUNG.MA_NHOM_DICH_VU_CAP1_TS,
    ""
  );
  const [dataMA_NHOM_DICH_VU_CAP1_HC] = useThietLap(
    THIET_LAP_CHUNG.MA_NHOM_DICH_VU_CAP1_HC,
    ""
  );
  const [dataMA_NHOM_DICH_VU_CAP1_VACCIN] = useThietLap(
    THIET_LAP_CHUNG.MA_NHOM_DICH_VU_CAP1_VACCIN,
    ""
  );

  const nhomChiPhiConfig = useMemo(
    () => ({
      KHAM: [dataMA_NHOM_DICH_VU_CAP1_KHAM],
      GIUONG: [dataMA_NHOM_DICH_VU_CAP1_GIUONG],
      XET_NGHIEM: [dataMA_NHOM_DICH_VU_CAP1_XET_NGHIEM],
      CDHA_TDCN_PHCN: [
        dataMA_NHOM_DICH_VU_CAP1_CDHA,
        dataMA_NHOM_DICH_VU_CAP1_TDCN,
        dataMA_NHOM_DICH_VU_CAP1_PHCN,
      ],
      TT_PT: [dataMA_NHOM_DICH_VU_CAP1_TT, dataMA_NHOM_DICH_VU_CAP1_PT],
      MAU: [dataMA_NHOM_DICH_VU_CAP1_MAU],
      THUOC: [dataMA_NHOM_DICH_VU_CAP1_THUOC],
      VTYT: [dataMA_NHOM_DICH_VU_CAP1_VAT_TU],
      TIEN_AN: [
        dataMA_NHOM_DICH_VU_CAP1_SUAT_AN,
        dataMA_NHOM_DICH_VU_CAP1_CPDD,
      ],
      KHAC: [
        dataMA_NHOM_DICH_VU_CAP1_KHAC,
        dataMA_NHOM_DICH_VU_CAP1_TS,
        dataMA_NHOM_DICH_VU_CAP1_HC,
        dataMA_NHOM_DICH_VU_CAP1_VACCIN,
      ],
    }),
    [
      dataMA_NHOM_DICH_VU_CAP1_KHAM,
      dataMA_NHOM_DICH_VU_CAP1_GIUONG,
      dataMA_NHOM_DICH_VU_CAP1_XET_NGHIEM,
      dataMA_NHOM_DICH_VU_CAP1_CDHA,
      dataMA_NHOM_DICH_VU_CAP1_TDCN,
      dataMA_NHOM_DICH_VU_CAP1_PHCN,
      dataMA_NHOM_DICH_VU_CAP1_TT,
      dataMA_NHOM_DICH_VU_CAP1_PT,
      dataMA_NHOM_DICH_VU_CAP1_MAU,
      dataMA_NHOM_DICH_VU_CAP1_THUOC,
      dataMA_NHOM_DICH_VU_CAP1_VAT_TU,
      dataMA_NHOM_DICH_VU_CAP1_SUAT_AN,
      dataMA_NHOM_DICH_VU_CAP1_CPDD,
      dataMA_NHOM_DICH_VU_CAP1_KHAC,
      dataMA_NHOM_DICH_VU_CAP1_TS,
      dataMA_NHOM_DICH_VU_CAP1_HC,
      dataMA_NHOM_DICH_VU_CAP1_VACCIN,
    ]
  );

  const renderFilter = ({ onChange, _state }) => {
    return (
      <Row>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-date">
            <label className="label-filter">
              {t("common.tuNgay")}
              <span className="icon-required">*</span>
            </label>
            <DateTimePicker
              className="input-filter"
              placeholder={t("common.chonNgay")}
              onChange={onChange("tuNgay")}
              value={_state.tuNgay}
              format="DD/MM/YYYY HH:mm:ss"
              showTime={{ defaultValue: moment().startOf("day") }}
            />
            {!_state.isValidData && !_state.tuNgay && (
              <div className="error">{t("baoCao.chonTuNgay")}</div>
            )}
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-date">
            <label className="label-filter">
              {t("common.denNgay")}
              <span className="icon-required">*</span>
            </label>
            <DateTimePicker
              className="input-filter"
              placeholder={t("common.chonNgay")}
              value={_state.denNgay}
              onChange={onChange("denNgay")}
              format="DD/MM/YYYY HH:mm:ss"
              showTime={{ defaultValue: moment().endOf("day") }}
            />
            {!_state.isValidData && !_state.denNgay && (
              <div className="error">{t("baoCao.chonDenNgay")}</div>
            )}
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">
              {t("baoCao.doiTuongKcb")} <span className="icon-required">*</span>
            </label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonDoiTuongKcb")}
              onChange={(e) => {
                setCacheDoiTuongKcb(e, false);
                onChange("dsDoiTuongKcb")(e);
              }}
              value={_state.dsDoiTuongKcb}
              data={PHAN_LOAI_DOI_TUONG_KCB}
              hasAllOption
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">
              {t("baoCao.layTheoKhoa")} <span className="icon-required">*</span>
            </label>
            <Select
              className="input-filter"
              placeholder={t("danhMuc.chonTitle", {
                title: t("baoCao.layTheoKhoa").toLowerCase(),
              })}
              data={THEO_KHOA_CHI_DINH}
              onChange={onChange("theoKhoaChiDinh")}
              value={_state.theoKhoaChiDinh}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("danhMuc.tenKhoa")}</label>
            <Select
              className="input-filter"
              placeholder={t("danhMuc.chonTitle", {
                title: t("danhMuc.tenKhoa").toLowerCase(),
              })}
              onChange={onChange("dsKhoaChiDinhId")}
              value={_state.dsKhoaChiDinhId}
              data={listAllKhoa}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.loaiThu")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonLoaiThu")}
              onChange={onChange("dsNhomLoaiThu")}
              value={_state.dsNhomLoaiThu}
              data={LOAI_THU_NHAP_VAO}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.mauBaoCao")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonMauBaoCao")}
              onChange={onChange("mauBaoCao")}
              value={_state.mauBaoCao}
              data={MAU_BAO_CAO}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("danhMuc.nhomChiPhi")}</label>
            <Select
              className="input-filter"
              placeholder={t("danhMuc.chonNhomChiPhi")}
              onChange={onChange("dsNhomChiPhi")}
              value={_state.dsNhomChiPhi}
              data={NHOM_CHI_PHI}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select checkbox-pl">
            <Checkbox
              checked={_state.hienThiNhomCap2}
              onChange={onChange("hienThiNhomCap2")}
            >
              {t("baoCao.nhomDichVuCap2")}
            </Checkbox>
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select checkbox-pl">
            <Checkbox
              checked={_state.hienThiPhong}
              onChange={onChange("hienThiPhong")}
            >
              {t("baoCao.hienThiPhongTheoKhoa")}
            </Checkbox>
          </div>
        </Col>
      </Row>
    );
  };

  const beforeOk =
    ({ _state, _beforeOk }) =>
    () => {
      if (!_state.tuNgay) {
        message.error(t("baoCao.chonTuNgay"));
        return false;
      }
      if (!_state.denNgay) {
        message.error(t("baoCao.chonDenNgay"));
        return false;
      }
      if (_state.dsDoiTuongKcb == null) {
        message.error(t("khamBenh.vuiLongChonDoiTuongKcb"));
        return false;
      }
      if (_state.theoKhoaChiDinh == null) {
        message.error(t("baoCao.vuiLongChonLayTheoKhoa"));
        return false;
      }
      return _beforeOk();
    };

  const handleDataSearch = ({ _state }) => {
    const filterList = (list, fullList) =>
      list?.length !== fullList?.length ? list : null;

    const dsNhomDichVuCap1Id = (_state.dsNhomChiPhi || []).flatMap(
      (nhomKey) => {
        const maList = nhomChiPhiConfig[nhomKey] || [];
        return listAllNhomDichVuCap1
          .filter((item) => maList.includes(item.ma))
          .map((item) => item.id);
      }
    );

    return {
      tuThoiGian: moment(_state.tuNgay).format("DD-MM-YYYY HH:mm:ss"),
      denThoiGian: moment(_state.denNgay).format("DD-MM-YYYY HH:mm:ss"),
      dsDoiTuongKcb: _state.dsDoiTuongKcb
        ? getPhanLoaiDoiTuongKcb(_state.dsDoiTuongKcb).referIds
        : null,
      theoKhoaChiDinh: _state.theoKhoaChiDinh,
      ...(_state.theoKhoaChiDinh
        ? { dsKhoaChiDinhId: filterList(_state.dsKhoaChiDinhId, listAllKhoa) }
        : {
            dsKhoaThucHienId: filterList(_state.dsKhoaChiDinhId, listAllKhoa),
          }),
      mauBaoCao: _state.mauBaoCao,
      dsNhomLoaiThu: _state.dsNhomLoaiThu,
      dsNhomDichVuCap1Id,
      hienThiNhomCap2: _state.hienThiNhomCap2,
      hienThiPhong: _state.hienThiPhong,
    };
  };

  return (
    <BaseBaoCao
      title={t("baoCao.tc82")}
      breadcrumb={[{ title: "TC82", link: "/bao-cao/tc82" }]}
      renderFilter={renderFilter}
      getBc={getTc82}
      initState={{
        dsDoiTuongKcb: cacheDoiTuongKcb,
        layTheoKhoa: 2,
        theoKhoaChiDinh: true,
        dsKhoaChiDinhId: [""],
        dsNhomLoaiThu: [""],
        dsNhomChiPhi: [""],
        hienThiNhomCap2: false,
        hienThiPhong: false,
      }}
      handleDataSearch={handleDataSearch}
      beforeOk={beforeOk}
    />
  );
};

export default TC82;
