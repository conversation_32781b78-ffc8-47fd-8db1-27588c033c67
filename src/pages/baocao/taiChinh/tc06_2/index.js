import { Col, Row } from "antd";
import { DateTimePicker, Checkbox, Select } from "components";
import moment from "moment";
import { t } from "i18next";
import React, { useEffect, useMemo } from "react";
import { useEnum, useStore, useThietLap, useLazyKVMap } from "hooks";
import BaseBaoCao from "pages/baocao/BaseBaoCao";
import { Main } from "./styled";
import {
  TRANG_THAI_THANH_TOAN_PHIEU_THU,
  ENUM,
  THIET_LAP_CHUNG,
  PHAN_LOAI_DOI_TUONG_KCB,
  LOAI_THOI_GIAN,
} from "constants/index";
import { select } from "redux-store/stores";
import { useDispatch } from "react-redux";
import { selectMaTen } from "redux-store/selectors";
import { lowerFirst } from "lodash";

/**
 * TC06.2 Tổng hợp chi phí khám b<PERSON>nh, chữ<PERSON> bệnh của người tham gia bảo hiểm y tế
 *
 */

const Index = () => {
  const listAllQuayTiepDon = useStore("quayTiepDon.listAllQuayTiepDon", []);
  const listAllToaNha = useStore("toaNha.listAllToaNha", []);
  const listAllTheBaoHiem = useStore("theBaoHiem.listAllTheBaoHiem", []);
  const [getPhanLoaiDoiTuongKcb] = useLazyKVMap(PHAN_LOAI_DOI_TUONG_KCB);

  const [dataTONG_KET_THANH_TOAN] = useThietLap(
    THIET_LAP_CHUNG.TONG_KET_THANH_TOAN
  );

  const [listTrangThaiTongKetThanhToan] = useEnum(
    ENUM.TRANG_THAI_TONG_KET_THANH_TOAN
  );

  const {
    baoCaoDaIn: { getTc06_2 },
    khoa: { getListAllKhoa },
    toaNha: { getListAllToaNha },
    benhVien: { getListAllBenhVien },
    theBaoHiem: { getListAllTheBaoHiem },
    quayTiepDon: { getListAllQuayTiepDon },
    nhanVien: { getListAllNhanVien },
    thietLap: { getThietLap },
  } = useDispatch();

  useEffect(() => {
    getListAllKhoa({ page: "", size: "", active: true });
    getListAllToaNha({ page: "", size: "", active: true });
    getListAllBenhVien({ page: "", size: "", active: true });
    getListAllTheBaoHiem({ page: "", size: "", active: true });
    getListAllQuayTiepDon({ page: "", size: "", active: true, dsLoai: 20 });
    getThietLap({ ma: THIET_LAP_CHUNG.MA_THU_NGAN }).then((dsMaVaiTro) => {
      getListAllNhanVien({ page: "", size: "", active: true, dsMaVaiTro });
    });
  }, []);

  const listLoaiThoiGian = useMemo(() => {
    return [
      {
        id: LOAI_THOI_GIAN.THEO_THOI_GIAN_THANH_TOAN,
        i18n: "baoCao.theoThoiGianThanhToan",
      },
      {
        id: LOAI_THOI_GIAN.THEO_THOI_GIAO_RA_VIEN,
        i18n: "baoCao.theoThoiGianRaVien",
      },
      {
        id: LOAI_THOI_GIAN.THEO_THOI_GIAO_TAO_HO_SO_GIAM_DINH_XML,
        i18n: "baoCao.thoiGianQuyetToan",
      },
      {
        id: LOAI_THOI_GIAN.THEO_THOI_GIAN_TONG_KET_THANH_TOAN,
        i18n: "baoCao.theoThoiGianTongKetThanhToan",
        show: !!dataTONG_KET_THANH_TOAN?.eval(),
      },
    ].filter((item) => item.show !== false);
  }, [dataTONG_KET_THANH_TOAN]);

  const renderFilter = ({ onChange, _state }) => {
    return (
      <Row>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-date">
            <label className="label-filter">
              {t("baoCao.tuNgay")}
              <span className="icon-required">*</span>
            </label>
            <DateTimePicker
              showTime={{ defaultValue: moment().startOf("day") }}
              value={_state.tuNgay}
              onChange={onChange("tuNgay")}
              placeholder={t("baoCao.chonNgay")}
              format="DD/MM/YYYY HH:mm:ss"
              className="input-filter"
            />
            {!_state.isValidData && !_state.tuNgay && (
              <div className="error">{t("baoCao.chonTuNgay")}</div>
            )}
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-date">
            <label className="label-filter">
              {t("baoCao.denNgay")}
              <span className="icon-required">*</span>
            </label>
            <DateTimePicker
              showTime={{ defaultValue: moment().endOf("day") }}
              value={_state.denNgay}
              onChange={onChange("denNgay")}
              placeholder={t("baoCao.chonNgay")}
              format="DD/MM/YYYY HH:mm:ss"
              className="input-filter"
            />
            {!_state.isValidData && !_state.denNgay && (
              <div className="error">{t("baoCao.chonDenNgay")}</div>
            )}
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">
              {t("baoCao.trangThaiThanhToan")}
            </label>
            <Select
              onChange={onChange("thanhToan")}
              value={_state.thanhToan}
              className="input-filter"
              placeholder={t("baoCao.chonTrangThaiThanhToan")}
              data={TRANG_THAI_THANH_TOAN_PHIEU_THU}
              hasAllOption={true}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.nhaThuNgan")}</label>
            <Select
              onChange={onChange("toaNhaId")}
              value={_state.toaNhaId}
              className="input-filter"
              placeholder={t("baoCao.chonNhaThuNgan")}
              data={listAllToaNha}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.thuNgan")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonThuNgan")}
              onChange={onChange("dsThuNganId")}
              value={_state.dsThuNganId}
              data={select.nhanVien.listAllNhanVien}
              getLabel={(item) =>
                `${item.taiKhoan || ""} - ${item.ma} - ${item.ten}`
              }
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.loaiThe")}</label>
            <Select
              onChange={onChange("dsMaThe")}
              value={_state.dsMaThe}
              className="input-filter"
              mode="multiple"
              placeholder={t("baoCao.chonLoaiThe")}
              data={listAllTheBaoHiem}
              getValue={(item) => item.ma}
              getLabel={selectMaTen}
            />
          </div>
        </Col>

        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.doiTuongKcb")}</label>
            <Select
              onChange={onChange("dsDoiTuongKcb")}
              value={_state.dsDoiTuongKcb}
              className="input-filter"
              mode="multiple"
              placeholder={t("baoCao.chonDoiTuongKcb")}
              data={PHAN_LOAI_DOI_TUONG_KCB}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("common.khoa")}</label>
            <Select
              onChange={onChange("khoaId")}
              value={_state.khoaId}
              className="input-filter"
              placeholder={t("common.chonKhoa")}
              data={select.khoa.listAllKhoa}
              getLabel={selectMaTen}
              hasAllOption={true}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.noiDangKyBD")}</label>
            <Select
              onChange={onChange("noiDangKyId")}
              value={_state.noiDangKyId}
              className="input-filter"
              placeholder={t("baoCao.chonNoiDangKyBD")}
              data={select.benhVien.listAllBenhVien}
              getLabel={selectMaTen}
              hasAllOption={true}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.loaiThoiGian")}</label>
            <Select
              onChange={onChange("loaiThoiGian")}
              value={_state.loaiThoiGian}
              className="input-filter"
              placeholder={t("baoCao.chonLoaiThoiGian")}
              data={listLoaiThoiGian}
            />
          </div>
        </Col>
        {dataTONG_KET_THANH_TOAN?.eval() && (
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">
                {t("baoCao.trangThaiTongKetThanhToan")}
              </label>
              <Select
                onChange={onChange("dsTrangThaiTktt")}
                value={_state.dsTrangThaiTktt}
                className="input-filter"
                placeholder={t("danhMuc.chonTitle", {
                  title: lowerFirst(t("baoCao.trangThaiTongKetThanhToan")),
                })}
                hasAllOption={true}
                data={listTrangThaiTongKetThanhToan}
              />
            </div>
          </Col>
        )}
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.quayThu")}</label>
            <Select
              className="input-filter"
              placeholder={t("danhMuc.chonLoaiQuay")}
              onChange={onChange("dsQuayId", true)}
              value={_state.dsQuayId}
              data={listAllQuayTiepDon}
              mode="multiple"
            />
          </div>
        </Col>
      </Row>
    );
  };

  const handleDataSearch = ({ _state }) => {
    const payload = {
      tuThoiGian: moment(_state.tuNgay).format("DD-MM-YYYY HH:mm:ss"),
      denThoiGian: moment(_state.denNgay).format("DD-MM-YYYY HH:mm:ss"),
      dsDoiTuongKcb: _state.dsDoiTuongKcb,
      khoaId: _state.khoaId,
      thanhToan: _state.thanhToan,
      dsMaThe: _state.dsMaThe[0] != "" ? _state.dsMaThe : "",
      noiDangKyId: _state.noiDangKyId,
      toaNhaId: _state.toaNhaId,
      loaiThoiGian: _state.loaiThoiGian,
      dsQuayId: _state.dsQuayId,
      dsTrangThaiTktt: _state.dsTrangThaiTktt,
      dsDoiTuongKcb:
        _state.dsDoiTuongKcb && _state.dsDoiTuongKcb[0] !== ""
          ? _state.dsDoiTuongKcb.flatMap(
              (id) => getPhanLoaiDoiTuongKcb(id)?.referIds
            )
          : null,
      dsThuNganId: _state.dsThuNganId,
    };
    return payload;
  };

  return (
    <Main>
      <BaseBaoCao
        title={t("baoCao.tc06_2")}
        renderFilter={renderFilter}
        getBc={getTc06_2}
        handleDataSearch={handleDataSearch}
        initState={{
          thanhToan: 50,
          dsMaThe: [""],
          dsDoiTuongKcb: [""],
          loaiThoiGian: 80,
          dsQuayId: [""],
          dsTrangThaiTktt: [""],
          dsThuNganId: [""],
        }}
        breadcrumb={[{ title: "TC06.2", link: "/bao-cao/tc06-2" }]}
      />
    </Main>
  );
};

export default Index;
