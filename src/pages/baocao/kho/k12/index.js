import { Col, Row, message } from "antd";
import { Select, SelectLoadMore, Checkbox } from "components";
import moment from "moment";
import React, { useEffect, useMemo, useState } from "react";
import { useDispatch } from "react-redux";
import BaseBaoCao from "pages/baocao/BaseBaoCao";
import { Main } from "./styled";
import khoTonKhoProvider from "data-access/kho/kho-ton-kho-provider";
import { useTranslation } from "react-i18next";
import { useEnum, useStore, useListAll } from "hooks";
import { select } from "redux-store/stores";
import { ENUM, ROLES, LOAI_DICH_VU } from "constants/index";
import { checkRole } from "lib-utils/role-utils";
import { selectMaTen } from "redux-store/selectors";
import { FilterThoiGian } from "pages/baocao/BaseBaoCao/components";
import dichVuProvider from "data-access/categories/dm-dich-vu-provider";

/**
 * K12. <PERSON><PERSON>o cáo chi tiết sử dụng hàng hóa thông thường
 *
 */
const LIST_SO_SANH_TUOI = [
  {
    id: 20,
    i18n: "baoCao.tu0Den6Tuoi",
  },
  {
    id: 50,
    i18n: "baoCao.lonHon6Tuoi",
  },
];

const K12 = () => {
  const { t } = useTranslation();
  const [state, _setState] = useState({});
  const setState = (data = {}) => {
    _setState((state) => ({ ...state, ...data }));
  };

  const [listLoaiThoiGianBaoCaoNhapXuatKho] = useEnum(
    ENUM.LOAI_THOI_GIAN_BAO_CAO_NHAP_XUAT_KHO
  );
  const listAllKho = useStore("kho.listAllKho", []);
  const listAllKhoa = useStore("khoa.listAllKhoa", []);
  const [listAllKhoaTheoTaiKhoan] = useListAll(
    "khoa",
    {},
    true,
    "KhoaTheoTaiKhoan"
  );
  const isHienThiTatCaKhoaChiDinh = checkRole([
    ROLES["BAO_CAO"].HIEN_THI_TAT_CA_KHOA_CHI_DINH,
  ]);

  const {
    baoCaoDaIn: { getK12 },
    kho: { getListAllKho },
    khoa: { getListAllKhoa },
    quyetDinhThau: { getListAllQuyetDinhThau },
  } = useDispatch();

  useEffect(() => {
    getListAllKho({ page: "", size: "", active: true, kyThuatCao: false });
    getListAllKhoa({ page: "", size: "", active: true });
    getListAllQuyetDinhThau({ page: "", size: "", active: true });
  }, []);

  const listLoaiThoiGianBaoCaoMemo = useMemo(() => {
    return listLoaiThoiGianBaoCaoNhapXuatKho.filter(
      (i) => i.id === 10 || i.id === 20
    );
  }, [listLoaiThoiGianBaoCaoNhapXuatKho]);

  const dsKhoaChiDinh = useMemo(
    () =>
      isHienThiTatCaKhoaChiDinh ? listAllKhoa : listAllKhoaTheoTaiKhoan || [],
    [isHienThiTatCaKhoaChiDinh, listAllKhoa, listAllKhoaTheoTaiKhoan]
  );

  const customChange = (name, onChange) => (e) => {
    if (name === "khoId") {
      setState({
        paramHangHoa: { ...state.paramHangHoa, khoId: e },
      });
      onChange("dichVuId")();
    }
    if (name === "dsKhoaChiDinhId ") {
      setState({
        paramHangHoa: { ...state.paramHangHoa, dsKhoaId: e },
      });
      onChange("dichVuId")();
    }
    onChange(name)(e);
  };

  const renderFilter = ({ onChange, _state, onKeyDownDate }) => {
    return (
      <Row>
        <FilterThoiGian
          t={t}
          onChange={onChange}
          _state={_state}
          onKeyDownDate={onKeyDownDate}
        />
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">
              {t("baoCao.kho")} <span className="icon-required"> *</span>
            </label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonKho")}
              data={listAllKho || []}
              onChange={customChange("khoId", onChange)}
              value={_state.khoId}
            />
            {!_state.isValidData && !_state.khoId && (
              <div className="error">{t("baoCao.vuiLongChonKho")}</div>
            )}
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("common.khoa")}</label>
            <Select
              className="input-filter"
              placeholder={t("common.chonKhoa")}
              data={dsKhoaChiDinh}
              onChange={customChange("dsKhoaChiDinhId", onChange)}
              value={_state.dsKhoaChiDinhId}
              getLabel={selectMaTen}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.tenHangHoa")}</label>
            <SelectLoadMore
              api={khoTonKhoProvider.searchAll}
              mapData={(i) => {
                let label = i.ten;
                if (i.tenHoatChat) {
                  label = `${label} - ${i.tenHoatChat}`;
                }
                return {
                  value: `${i.dichVuId}-${i.khoId}-${i.ten}`,
                  label,
                };
              }}
              onChange={onChange("dichVuId")}
              keySearch={"timKiem"}
              value={_state.dichVuId}
              className="input-filter"
              placeholder={t("baoCao.chonHangHoa")}
              addParam={state?.paramHangHoa}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.theoThoiGian")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonLoaiThoiGian")}
              onChange={onChange("loaiThoiGian")}
              value={_state.loaiThoiGian}
              data={listLoaiThoiGianBaoCaoMemo}
              allowClear={true}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-checkbox checkbox-pl">
            <Checkbox
              checked={_state.hienThiQuyetDinhThau}
              onChange={onChange("hienThiQuyetDinhThau")}
            >
              {t("baoCao.hienThiQuyetDinhThau")}
            </Checkbox>
          </div>
        </Col>
        {_state.hienThiQuyetDinhThau && (
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">
                {t("baoCao.quyetDinhThau")}
              </label>
              <Select
                className="input-filter"
                placeholder={t("baoCao.chonQuyetDinhThau")}
                onChange={onChange("dsQuyetDinhThauId", true)}
                value={_state.dsQuyetDinhThauId}
                data={select.quyetDinhThau.listAllQuyetDinhThau}
                getLabel={(item) => item.quyetDinhThau}
                mode="multiple"
              />
            </div>
          </Col>
        )}
        <Col md={6} xl={6} xxl={6}>
          <div className="item-checkbox checkbox-pl">
            <Checkbox
              checked={_state.nhomTheoHoatChat}
              onChange={onChange("nhomTheoHoatChat")}
            >
              {t("baoCao.nhomTheoTenHoatChat")}
            </Checkbox>
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">
              {t("quanLyDinhDuong.sangLoc.doTuoi")}
            </label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonDoTuoi")}
              onChange={onChange("soSanhTuoi")}
              value={_state.soSanhTuoi}
              data={LIST_SO_SANH_TUOI}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.thuocChuaKe")}</label>
            <SelectLoadMore
              api={dichVuProvider.searchAll}
              mapData={(i) => ({
                value: i.id,
                label: `${i.ma}-${i.ten}`,
              })}
              onChange={onChange("dsDichVuIdKhongKe", true)}
              value={_state.dsDichVuIdKhongKe}
              keySearch={"timKiem"}
              placeholder={t("baoCao.chonThuocChuaKe")}
              className="input-filter"
              addParam={{
                dsLoaiDichVu: [
                  LOAI_DICH_VU.THUOC,
                  LOAI_DICH_VU.VAC_XIN,
                  LOAI_DICH_VU.VAT_TU,
                  LOAI_DICH_VU.HOA_CHAT,
                ],
              }}
              blurReset={true}
              mode="multiple"
              dropdownMatchSelectWidth={500}
            />
          </div>
        </Col>
      </Row>
    );
  };

  const handleDataSearch = ({ _state }) => {
    let dichVuId = _state?.dichVuId?.split("-")[0] || null;
    return {
      tuNgay: moment(_state.tuNgay).format("DD-MM-YYYY HH:mm:ss"),
      denNgay: moment(_state.denNgay).format("DD-MM-YYYY HH:mm:ss"),
      khoId: _state.khoId,
      dsKhoaChiDinhId: _state.dsKhoaChiDinhId,
      dichVuId: dichVuId,
      hienThiQuyetDinhThau: _state.hienThiQuyetDinhThau,
      dsQuyetDinhThauId: _state.hienThiQuyetDinhThau
        ? _state.dsQuyetDinhThauId
        : null,
      nhomTheoHoatChat: _state.nhomTheoHoatChat,
      loaiThoiGian: _state.loaiThoiGian,
      soSanhTuoi: _state.soSanhTuoi,
      dsDichVuIdKhongKe: _state.dsDichVuIdKhongKe,
    };
  };

  const beforeOk =
    ({ _state, _beforeOk }) =>
    () => {
      if (!_state.tuNgay) {
        message.error(t("baoCao.chonTuNgay"));
        return false;
      }
      if (!_state.denNgay) {
        message.error(t("baoCao.chonDenNgay"));
        return false;
      }
      if (!_state.khoId) {
        message.error(t("baoCao.vuiLongChonKho"));
        return false;
      }

      return _beforeOk();
    };

  return (
    <Main>
      <BaseBaoCao
        title={t("baoCao.k12")}
        renderFilter={renderFilter}
        getBc={getK12}
        handleDataSearch={handleDataSearch}
        initState={{
          hienThiQuyetDinhThau: false,
          loaiThoiGian: 10,
        }}
        beforeOk={beforeOk}
        breadcrumb={[{ title: "K12", link: "/bao-cao/k12" }]}
      />
    </Main>
  );
};

export default K12;
