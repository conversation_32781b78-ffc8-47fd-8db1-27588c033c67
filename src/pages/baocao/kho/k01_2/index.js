import { Col, message, Row } from "antd";
import { Select } from "components";
import moment from "moment";
import { useTranslation } from "react-i18next";
import React, { useEffect, useMemo } from "react";
import { useDispatch } from "react-redux";
import BaseBaoCao from "pages/baocao/BaseBaoCao";
import { Main } from "./styled";
import {
  LOAI_KHO,
  HINH_THUC_TRONG_NGOAI_THAU,
  ENUM,
  LOAI_NHAP_XUAT,
} from "constants/index";
import { useEnum, useStore } from "hooks";
import { select } from "redux-store/stores";
import { FilterThoiGian } from "pages/baocao/BaseBaoCao/components";

/**
 * K01.2. Báo cáo nhập theo hàng hóa
 *
 */

const Index = () => {
  const { t } = useTranslation();
  const listAllNhaCungCap = useStore("doiTac.listAllNhaCungCap", []);
  const listKhoUser = useStore("kho.listKhoUser", []);
  const listAllHangHoa = useStore("tonKho.listAllHangHoa", []);
  const [listLoaiNhapXuat] = useEnum(ENUM.LOAI_NHAP_XUAT);

  const {
    baoCaoDaIn: { getk01_2 },
    kho: { getTheoTaiKhoan: getKhoTheoTaiKhoan },
    tonKho: { onSearchAllDichVuTonKho },
    doiTac: { getListAllNhaCungCap },
  } = useDispatch();

  useEffect(() => {
    getKhoTheoTaiKhoan({
      page: "",
      size: "",
      active: true,
      dsLoaiKho: LOAI_KHO.NHAP_TU_NCC,
    });
    onSearchAllDichVuTonKho({
      page: 0,
      size: 9999,
      dataSearch: {
        active: true,
      },
    });
    getListAllNhaCungCap({ page: "", size: "", active: true });
  }, []);

  const listDataLoaiNhapXuat = useMemo(() => {
    return (listLoaiNhapXuat || []).filter((i) =>
      [LOAI_NHAP_XUAT.NHAP_TU_NCC, LOAI_NHAP_XUAT.NHAP_KHAC].includes(i.id)
    );
  }, [listLoaiNhapXuat]);

  const handleChange = (key, onChange) => (value) => {
    if (key === "dsKhoId") {
      onSearchAllDichVuTonKho({
        page: 0,
        size: 9999,
        dataSearch: {
          active: true,
          dsKhoId: value,
        },
      });
      onChange("dichVuId")();
    }
    onChange(key)(value);
  };

  const renderFilter = ({ onChange, _state, onKeyDownDate }) => {
    return (
      <>
        <Row>
          <FilterThoiGian
            t={t}
            onChange={onChange}
            _state={_state}
            onKeyDownDate={onKeyDownDate}
          />
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">
                {t("baoCao.kho")} <span className="icon-required">*</span>
              </label>
              <Select
                className="input-filter"
                placeholder={t("baoCao.chonKho")}
                data={listKhoUser || []}
                onChange={handleChange("dsKhoId", onChange)}
                value={_state.dsKhoId}
                mode="multiple"
              />
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">{t("danhMuc.nhaCungCap")}</label>
              <Select
                mode="multiple"
                showArrow
                onChange={handleChange("nhaCungCapIds", onChange)}
                value={_state.nhaCungCapIds}
                className="input-filter"
                placeholder={t("danhMuc.chonNhaCungCap")}
                data={listAllNhaCungCap}
              />
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">
                {t("baoCao.hinhThucTrongNgoaiThau")}
              </label>
              <Select
                onChange={onChange("hinhThucThau")}
                value={_state.hinhThucThau}
                className="input-filter"
                placeholder={t("baoCao.chonHinhThucTrongNgoaiThau")}
                data={HINH_THUC_TRONG_NGOAI_THAU}
                hasAllOption={true}
              />
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">
                {t("baoCao.quyetDinhThau")}
              </label>
              <Select
                onChange={onChange("quyetDinhThauId")}
                value={_state.quyetDinhThauId}
                className="input-filter"
                placeholder={t("baoCao.chonQuyetDinhThau")}
                data={select.quyetDinhThau.listAllQuyetDinhThau}
                getLabel={(item) => item.quyetDinhThau}
                hasAllOption={true}
              />
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">{t("baoCao.tenHangHoa")}</label>
              <Select
                onChange={onChange("dichVuId")}
                value={_state.dichVuId}
                className="input-filter"
                placeholder={t("baoCao.chonHangHoa")}
                data={listAllHangHoa}
                hasAllOption={true}
              />
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">{t("baoCao.loaiHoaDon")}</label>
              <Select
                className="input-filter"
                placeholder={t("baoCao.chonLoaiHoaDon")}
                data={listDataLoaiNhapXuat}
                onChange={onChange("dsLoaiNhapXuat")}
                value={_state.dsLoaiNhapXuat}
                mode="multiple"
              />
            </div>
          </Col>
        </Row>
      </>
    );
  };

  const handleDataSearch = ({ _state }) => ({
    loaiThoiGian: _state.loaiThoiGian,
    tuNgay: moment(_state.tuNgay).format("DD-MM-YYYY HH:mm:ss"),
    denNgay: moment(_state.denNgay).format("DD-MM-YYYY HH:mm:ss"),
    dsKhoId: _state.dsKhoId,
    nhaCungCapIds: _state.nhaCungCapIds,
    trongThau: _state.hinhThucThau,
    quyetDinhThauId: _state.quyetDinhThauId,
    dichVuId: _state.dichVuId,
    dsLoaiNhapXuat: _state.dsLoaiNhapXuat,
  });

  const beforeOk =
    ({ _state, _beforeOk }) =>
    () => {
      if (!_state.dsKhoId?.length) {
        message.error(t("baoCao.vuiLongChonKho"));
        return false;
      }
      return _beforeOk();
    };

  return (
    <Main>
      <BaseBaoCao
        title={t("baoCao.k01_2")}
        renderFilter={renderFilter}
        beforeOk={beforeOk}
        getBc={getk01_2}
        handleDataSearch={handleDataSearch}
        initState={{
          dsLoaiNhapXuat: [LOAI_NHAP_XUAT.NHAP_TU_NCC],
        }}
        breadcrumb={[{ title: "K01.2", link: "/bao-cao/k01_2" }]}
      />
    </Main>
  );
};

export default Index;
