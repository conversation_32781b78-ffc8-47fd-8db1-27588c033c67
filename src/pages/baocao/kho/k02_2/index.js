import { Col, message, Row } from "antd";
import { useTranslation } from "react-i18next";
import React, { useState, useEffect } from "react";
import { useDispatch } from "react-redux";
import moment from "moment";

import { useCache, useListAll, useStore } from "hooks";

import { Select, SelectLoadMore, Checkbox, DateTimePicker } from "components";
import BaseBaoCao from "pages/baocao/BaseBaoCao";
import khoTonKhoProvider from "data-access/kho/kho-ton-kho-provider";
import { CACHE_KEY, LOAI_DICH_VU, THEO_NGAY_HOA_DON } from "constants/index";

const LIST_MAU_BAO_CAO = [
  {
    id: 1,
    i18n: "baoCao.baoCaoXuatNhapTonKhoChiTiet",
  },
  {
    id: 2,
    i18n: "baoCao.baoCaoTonKho",
  },
  {
    id: 3,
    i18n: "baoCao.bienBanKiemKeThucTe",
  },
];

const K02_2 = () => {
  const { t } = useTranslation();
  const [state, _setState] = useState({});
  const setState = (data = {}) => {
    _setState((state) => ({ ...state, ...data }));
  };
  const listKhoUser = useStore("kho.listKhoUser", []);
  const listAllNhaSanXuat = useStore("doiTac.listAllNhaSanXuat", []);
  const listAllPhanLoaiThuoc = useStore(
    "phanLoaiThuoc.listAllPhanLoaiThuoc",
    []
  );
  const listAllNhomDichVuKho = useStore(
    "phanNhomDichVuKho.listAllNhomDichVuKho",
    []
  );
  const [listAllKhoa] = useListAll("khoa", {}, true);
  const listNhomDichVuKhoCap1 = useStore(
    "nhomDichVuKho.listAllNhomDichVuKhoCap1",
    []
  );
  const [listAllMaPhieuLinh] = useListAll("maPhieuLinh", {}, true);
  const [listAllHoiDongKiemKe] = useListAll("hoiDongKiemKe", { loai: 20 });
  const [cacheHienThiSoLo, setCacheHienThiSoLo] = useCache(
    "",
    CACHE_KEY.HIEN_THI_SO_LO_HSD_K02_2,
    true,
    false
  );

  const {
    baoCaoDaIn: { getK02_2 },
    kho: { getTheoTaiKhoan: getKhoTheoTaiKhoan },
    doiTac: { getListAllNhaSanXuat },
    phanLoaiThuoc: { getListAllPhanLoaiThuoc },
    phanNhomDichVuKho: { getListAllNhomDichVuKho },
    nhomDichVuKho: { getListAllNhomDichVuKhoCap1 },
  } = useDispatch();

  useEffect(() => {
    getListAllPhanLoaiThuoc({ page: "", size: "", active: true });
    getKhoTheoTaiKhoan({ page: "", size: "", active: true });
    getListAllNhaSanXuat({
      page: "",
      size: "",
      active: true,
      dsLoaiDoiTac: [10],
    });
    getListAllNhomDichVuKho({
      page: "",
      size: "",
      active: true,
      loaiDichVu: LOAI_DICH_VU.THUOC,
    });
    getListAllNhomDichVuKhoCap1({
      active: true,
      page: "",
      size: "",
      dsLoaiDichVu: [
        LOAI_DICH_VU.THUOC,
        LOAI_DICH_VU.VAT_TU,
        LOAI_DICH_VU.HOA_CHAT,
      ],
    });
  }, []);

  const customChange = (name, onChange) => (e) => {
    if (name === "dsKhoId") {
      let paramHangHoa = { active: true };
      if (Array.isArray(e) && e.length > 0 && e[0] != "") {
        paramHangHoa = { ...paramHangHoa, dsKhoId: e };
      }
      setState({
        paramHangHoa,
      });
      onChange("dichVuId")();
    }
    onChange(name, true)(e);
  };

  const renderFilter = ({ onChange, _state, onKeyDownDate }) => {
    return (
      <Row>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-date">
            <label className="label-filter">
              {t("common.tuNgay")} <span className="icon-required">*</span>
            </label>
            <DateTimePicker
              showTime={{ defaultValue: moment().startOf("day") }}
              value={_state.tuNgay}
              onChange={onChange("tuNgay")}
              placeholder={t("common.chonNgay")}
              format="DD/MM/YYYY HH:mm:ss"
              className="input-filter"
              onKeyDown={onKeyDownDate("tuNgay")}
            />
            {!_state.isValidData && !_state.tuNgay && (
              <div className="error">{t("baoCao.chonTuNgay")}!</div>
            )}
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-date">
            <label className="label-filter">
              {t("common.denNgay")} <span className="icon-required">*</span>
            </label>
            <DateTimePicker
              showTime={{ defaultValue: moment().startOf("day") }}
              value={_state.denNgay}
              onChange={onChange("denNgay")}
              placeholder={t("common.chonNgay")}
              format="DD/MM/YYYY HH:mm:ss"
              className="input-filter"
              onKeyDown={onKeyDownDate("denNgay")}
            />
            {!_state.isValidData && !_state.denNgay && (
              <div className="error">{t("baoCao.chonDenNgay")}!</div>
            )}
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">
              {t("baoCao.kho")} <span className="icon-required">*</span>
            </label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonKho")}
              data={listKhoUser}
              onChange={customChange("dsKhoId", onChange)}
              value={_state.dsKhoId}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.hangSanXuat")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonHangSanXuat")}
              data={listAllNhaSanXuat || []}
              onChange={onChange("nhaSanXuatId")}
              value={_state.nhaSanXuatId}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">
              {t("baoCao.phanLoaiHangHoa")}
            </label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonPhanLoaiHangHoa")}
              onChange={onChange("dsPhanLoaiDvKhoId", true)}
              value={_state.dsPhanLoaiDvKhoId}
              data={listAllPhanLoaiThuoc}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("danhMuc.phanNhomThuoc")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonPhanLoaiHangHoa")}
              onChange={onChange("dsPhanNhomDvKhoId", true)}
              value={_state.dsPhanNhomDvKhoId}
              data={listAllNhomDichVuKho}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.tenHangHoa")}</label>
            <SelectLoadMore
              api={khoTonKhoProvider.searchAll}
              mapData={(i) => ({
                value: `${i.dichVuId}-${i.khoId}-${i.ten}`,
                label: `${i.ma}-${i.ten}`,
              })}
              onChange={onChange("dichVuId")}
              keySearch={"timKiem"}
              value={_state.dichVuId}
              className="input-filter"
              placeholder={t("baoCao.chonHangHoa")}
              addParam={state?.paramHangHoa}
              hasAll={true}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("danhMuc.maPhieuLinh")}</label>
            <Select
              className="input-filter"
              placeholder={t("quanLyNoiTru.chonMaPhieuLinh")}
              onChange={onChange("dsPhieuLinhId")}
              value={_state.dsPhieuLinhId}
              mode={"multiple"}
              data={listAllMaPhieuLinh}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.theoThoiGian")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.vuiLongChonTheoThoiGian")}
              onChange={onChange("theoThoiGianDuyet")}
              value={_state.theoThoiGianDuyet}
              data={THEO_NGAY_HOA_DON}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.mauBaoCao")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonMauBaoCao")}
              onChange={onChange("mauBaoCao")}
              value={_state.mauBaoCao}
              data={LIST_MAU_BAO_CAO}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select checkbox-pl">
            <Checkbox
              checked={_state.hienThiNhomDvKhoCap1}
              onChange={onChange("hienThiNhomDvKhoCap1")}
            >
              {t("baoCao.hienThiNhomHangHoaCap1")}
            </Checkbox>
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select checkbox-pl">
            <Checkbox
              checked={_state.khoTaiKhoa}
              onChange={onChange("khoTaiKhoa")}
            >
              {t("baoCao.khoTaiKhoa")}
            </Checkbox>
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select checkbox-pl">
            <Checkbox
              checked={_state.hienThiTenTrungThau}
              onChange={onChange("hienThiTenTrungThau")}
            >
              {t("baoCao.hienThiTenTrungThau")}
            </Checkbox>
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select checkbox-pl">
            <Checkbox
              checked={_state.hienThiDonGia}
              onChange={onChange("hienThiDonGia")}
            >
              {t("baoCao.hienThiGiaNhapThanhTien")}
            </Checkbox>
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div
            className="item-select"
            style={_state.hienThiNhomDvKhoCap1 ? {} : { display: "none" }}
          >
            <label className="label-filter">
              {t("baoCao.nhomHangHoaCap1")}
            </label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonNhomHangHoaCap1")}
              value={_state.dsNhomDvKhoCap1Id}
              data={listNhomDichVuKhoCap1}
              onChange={onChange("dsNhomDvKhoCap1Id")}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div
            className="item-select"
            style={_state.khoTaiKhoa ? {} : { display: "none" }}
          >
            <label className="label-filter">{t("baoCao.tenKhoTaiKhoa")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonKhoa")}
              data={(listAllKhoa || []).map((item) => ({
                ...item,
                ten: `${item.ma} - ${item.ten}`,
              }))}
              onChange={onChange("dsKhoTaiKhoaId")}
              value={_state.dsKhoTaiKhoaId}
              mode="multiple"
              defaultValue={[]}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.hoiDong")}</label>
            <Select
              onChange={onChange("hoiDongKiemKeId")}
              value={_state.hoiDongKiemKeId}
              className="input-filter"
              placeholder={t("baoCao.chonHoiDong")}
              data={listAllHoiDongKiemKe}
              hasAllOption={true}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select checkbox-pl">
            <Checkbox
              checked={_state.sapXepTheoMaDichVu}
              onChange={onChange("sapXepTheoMaDichVu")}
            >
              {t("baoCao.sapXepTheoMaHangHoa")}
            </Checkbox>
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select checkbox-pl">
            <Checkbox
              checked={_state.sapXepTheoTenMoiThau}
              onChange={onChange("sapXepTheoTenMoiThau")}
            >
              {t("baoCao.sapXepTheoTenMoiThau")}
            </Checkbox>
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select checkbox-pl">
            <Checkbox
              checked={_state.sapXepTheoDvt}
              onChange={onChange("sapXepTheoDvt")}
            >
              {t("baoCao.sapXepTheoDvt")}
            </Checkbox>
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select checkbox-pl">
            <Checkbox
              checked={_state.hienThiSoLo}
              onChange={(e) => {
                setCacheHienThiSoLo(e.target.checked, false);
                onChange("hienThiSoLo")(e);
              }}
            >
              {t("baoCao.hienThiSoLoHSD")}
            </Checkbox>
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select checkbox-pl">
            <Checkbox
              checked={_state.theoSlThuCap}
              onChange={onChange("theoSlThuCap")}
            >
              {t("baoCao.theoSlThuCap")}
            </Checkbox>
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select checkbox-pl">
            <Checkbox
              checked={_state.theoNgayHoaDon}
              onChange={onChange("theoNgayHoaDon")}
            >
              {t("baoCao.theoNgayHoaDon")}
            </Checkbox>
          </div>
        </Col>
      </Row>
    );
  };

  const handleDataSearch = ({ _state }) => {
    let dichVuId = _state?.dichVuId?.split("-")[0] || null;
    const dsKhoId =
      _state.dsKhoId[0] == ""
        ? listKhoUser.map((item) => item.id)
        : _state.dsKhoId;
    return {
      tuNgay: moment(_state.tuNgay).format("DD-MM-YYYY HH:mm:ss"),
      denNgay: moment(_state.denNgay).format("DD-MM-YYYY HH:mm:ss"),
      dsKhoId: dsKhoId,
      dichVuId: dichVuId,
      nhaSanXuatId: _state.nhaSanXuatId,
      dsPhanLoaiDvKhoId: _state.dsPhanLoaiDvKhoId,
      dsPhanNhomDvKhoId: _state.dsPhanNhomDvKhoId,
      hienThiDonGia: _state.hienThiDonGia,
      theoThoiGianDuyet: _state.theoThoiGianDuyet,
      khoTaiKhoa: _state.khoTaiKhoa,
      dsKhoTaiKhoaId: _state.khoTaiKhoa ? _state.dsKhoTaiKhoaId : [],
      hienThiNhomDvKhoCap1: _state.hienThiNhomDvKhoCap1,
      dsNhomDvKhoCap1Id: _state.hienThiNhomDvKhoCap1
        ? _state.dsNhomDvKhoCap1Id
        : [],
      hienThiTenTrungThau: _state.hienThiTenTrungThau,
      sapXepTheoMaDichVu: _state.sapXepTheoMaDichVu,
      sapXepTheoTenMoiThau: _state.sapXepTheoTenMoiThau,
      sapXepTheoDvt: _state.sapXepTheoDvt,
      dsPhieuLinhId: _state.dsPhieuLinhId,
      hienThiSoLo: _state.hienThiSoLo,
      mauBaoCao: _state.mauBaoCao,
      hoiDongKiemKeId: _state.hoiDongKiemKeId,
      theoSlThuCap: _state.theoSlThuCap,
      theoNgayHoaDon: _state.theoNgayHoaDon,
    };
  };

  const beforeOk =
    ({ _state, _beforeOk }) =>
    () => {
      if (!_state.dsKhoId || _state.dsKhoId.length === 0) {
        message.error(t("baoCao.vuiLongChonKho"));
        return false;
      }
      if (!_state.tuNgay || !_state.denNgay) {
        message.error(t("baoCao.vuiLongChonThangNam"));
        return false;
      }
      return _beforeOk();
    };

  return (
    <BaseBaoCao
      title={t("baoCao.k02_2")}
      renderFilter={renderFilter}
      beforeOk={beforeOk}
      getBc={getK02_2}
      handleDataSearch={handleDataSearch}
      initState={{
        dsKhoId: [""],
        dsPhanLoaiDvKhoId: [""],
        dsPhanNhomDvKhoId: [""],
        dsPhieuLinhId: [""],
        hienThiNhomDvKhoCap1: false,
        hienThiTenTrungThau: false,
        sapXepTheoMaDichVu: false,
        sapXepTheoTenMoiThau: false,
        hienThiSoLo: cacheHienThiSoLo,
        theoNgayHoaDon: false,
      }}
      breadcrumb={[{ title: "K02.2", link: "/bao-cao/k02_2" }]}
    />
  );
};

export default K02_2;
