import React, { useEffect, useRef, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import { Col } from "antd";
import { useStore, useGuid } from "hooks";
import { checkRole } from "lib-utils/role-utils";
import { HomeWrapper } from "components";
import DanhSachMauKetQua from "./components/DanhSachMauKetQua";
import ThongTinChiTiet from "./components/ThongTinChiTiet";
import MultiLevelTab from "components/MultiLevelTab";
import ProtocolPtttTab from "./components/ProtocolPtttTab";
import {
  ADD_LAYOUT_COLLAPSE,
  TABLE_LAYOUT_COLLAPSE,
  ADD_LAYOUT,
  TABLE_LAYOUT,
  HOTKEY,
  ROLES,
} from "constants/index";
import { SVG } from "assets";
import { Main } from "./styled";

const MauKetQuaPttt = () => {
  const { t } = useTranslation();
  const refTab = useRef();
  const layerId = useGuid();
  const refClickBtnAdd = useRef();
  const refClickBtnSave = useRef();
  const refSave1 = useRef();
  const [collapseStatus, setCollapseStatus] = useState(false);
  const [editStatus, setEditStatus] = useState(false);
  const [state, _setState] = useState({
    showFullTable: false,
    activeKeyTab: "1",
  });

  const nhanVienId = useStore("auth.auth.nhanVienId", null);
  const nameScreen = t("danhMuc.mauKetQuaPttt");
  const currentItem = useSelector((state) => state.mauKetQuaPTTT.currentItem);

  const {
    phimTat: { onAddLayer, onRemoveLayer, onRegisterHotkey },
    mauKetQuaPTTT: { updateData },
  } = useDispatch();

  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  // register layerId
  useEffect(() => {
    onAddLayer({ layerId: layerId });
    onRegisterHotkey({
      layerId: layerId,
      hotKeys: [
        {
          keyCode: HOTKEY.F1,
          onEvent: () => {
            refClickBtnAdd.current && refClickBtnAdd.current();
          },
        },
        {
          keyCode: HOTKEY.F4,
          onEvent: (e) => {
            refClickBtnSave.current && refClickBtnSave.current(e);
          },
        },
      ],
    });
    return () => {
      onRemoveLayer({ layerId: layerId });
      updateData({ currentItem: {} });
    };
  }, []);

  refClickBtnSave.current = (e) => {
    const { activeKeyTab } = state;
    if (activeKeyTab === "1" && refSave1.current) refSave1.current(e);
  };

  const handleChangeshowTable = () => {
    setState({
      changeShowFullTbale: true,
      showFullTable: !state.showFullTable,
    });
    setTimeout(() => {
      setState({ changeShowFullTbale: false });
    }, 1000);
  };

  const handleClickedBtnAdded = () => {
    setEditStatus(false);
    updateData({ currentItem: {} });
  };
  refClickBtnAdd.current = handleClickedBtnAdded;

  const handleCollapsePane = () => {
    setCollapseStatus(!collapseStatus);
  };

  const listPanel = [
    {
      title: t("danhMuc.thongTinChiTiet"),
      key: 1,
      render: () => {
        return (
          <ThongTinChiTiet
            refCallbackSave={refSave1}
            currentItem={currentItem}
            roleSave={[ROLES["DANH_MUC"].MAU_KQ_PT_TT_THEM]}
            roleEdit={[ROLES["DANH_MUC"].MAU_KQ_PT_TT_SUA]}
            editStatus={
              editStatus
                ? !checkRole([ROLES["DANH_MUC"].MAU_KQ_PT_TT_SUA])
                : !checkRole([ROLES["DANH_MUC"].MAU_KQ_PT_TT_THEM])
            }
            nhanVienId={nhanVienId}
            layerId={layerId}
          />
        );
      },
    },
    {
      key: 2,
      title: t("danhMuc.mauKetQuaProtocolPTTT"),
      render: () => {
        return (
          <ProtocolPtttTab
            currentItem={currentItem}
            roleSave={[ROLES["DANH_MUC"].MAU_KQ_PT_TT_THEM]}
            roleEdit={[ROLES["DANH_MUC"].MAU_KQ_PT_TT_SUA]}
            editStatus={
              editStatus
                ? !checkRole([ROLES["DANH_MUC"].MAU_KQ_PT_TT_SUA])
                : !checkRole([ROLES["DANH_MUC"].MAU_KQ_PT_TT_THEM])
            }
            layerId={layerId}
          />
        );
      },
    },
  ];

  return (
    <Main>
      <HomeWrapper
        title={nameScreen}
        listLink={[
          {
            title: t("danhMuc.title"),
            link: "/danh-muc",
          },
          {
            title: t("danhMuc.mauKetQuaPhauThuatThuThuat"),
            link: "/danh-muc/mau-kq-pt-tt",
          },
        ]}
      >
        <Col
          {...(!state.showFullTable
            ? collapseStatus
              ? TABLE_LAYOUT_COLLAPSE
              : TABLE_LAYOUT
            : null)}
          span={state.showFullTable ? 24 : null}
          className={`pr-3 ${
            state.changeShowFullTbale ? "" : "transition-ease"
          }`}
        >
          <DanhSachMauKetQua
            layerId={layerId}
            setEditStatus={setEditStatus}
            title={t("danhMuc.danhMucMauKetQuaPhauThuatThuThuat")}
            classNameRow={"custom-header"}
            styleMain={{ marginTop: 0 }}
            styleContainerButtonHeader={{
              display: "flex",
              width: "100%",
              justifyContent: "flex-end",
              alignItems: "center",
              paddingRight: 35,
            }}
            nhanVienId={nhanVienId}
            buttonHeader={[
              {
                className: `btn-change-full-table ${
                  state.showFullTable ? "small" : "large"
                }`,
                title: state.showFullTable ? (
                  <SVG.IcShowThuNho />
                ) : (
                  <SVG.IcShowFull />
                ),
                onClick: handleChangeshowTable,
              },
              {
                className: "btn-collapse",
                title: collapseStatus ? <SVG.IcExtend /> : <SVG.IcCollapse />,
                onClick: handleCollapsePane,
              },
            ]}
          />
        </Col>
        {!state.showFullTable && (
          <Col
            {...(collapseStatus ? ADD_LAYOUT_COLLAPSE : ADD_LAYOUT)}
            className={`mt-3 ${
              state.changeShowFullTbale ? "" : "transition-ease"
            }`}
            style={
              state.isSelected
                ? { border: "2px solid #c1d8fd", borderRadius: 20 }
                : {}
            }
          >
            <MultiLevelTab
              ref={refTab}
              listPanel={listPanel}
              isBoxTabs={true}
              activeKey={state.activeKeyTab}
              onChange={(activeKeyTab) => setState({ activeKeyTab })}
            />
          </Col>
        )}
      </HomeWrapper>
    </Main>
  );
};

export default MauKetQuaPttt;
