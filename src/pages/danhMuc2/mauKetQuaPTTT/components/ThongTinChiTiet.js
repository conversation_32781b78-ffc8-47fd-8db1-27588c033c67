import React, { forwardRef, useState, useRef, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import { Form, Input, message } from "antd";
import { Checkbox, ImageEdit, Select, Tooltip } from "components";
import EditWrapper from "components/MultiLevelTab/EditWrapper";
import { useQueryAll } from "hooks";
import { query } from "redux-store/stores";
import { selectMaTen } from "redux-store/selectors";
import { checkRole } from "lib-utils/role-utils";
import { ROLES } from "constants/index";
import { safeConvertToArray } from "utils";
import { SVG } from "assets";

function ThongTinChiTiet(props, ref) {
  const {
    refCallbackSave = {},
    currentItem,
    roleSave,
    roleEdit,
    editStatus,
    nhanVienId,
    layerId,
  } = props;
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const refAutoFocus = useRef(null);
  const refDsLuocDo = useRef([]);
  const refUpload = useRef(null);
  const [data, setData] = useState({});

  const {
    mauKetQuaPTTT: { createOrEdit, onSearch, updateData },
  } = useDispatch();

  const [state, _setState] = useState({
    dsLuocDo: [],
  });

  const setState = (data) => {
    _setState({
      ...state,
      ...data,
    });
  };

  const isMacDinhKhoaChiDinh = !checkRole([
    ROLES["DANH_MUC"].MAU_KQ_PT_TT_HIEN_THI_MAU_THEO_KHOA_CHI_DINH,
  ]);

  const { data: listAllKhoa } = useQueryAll(query.khoa.queryAllKhoa);
  const { data: listAllNhanVien } = useQueryAll(
    query.nhanVien.queryAllNhanVien
  );
  const { data: listAllPhuongPhapVoCam } = useQueryAll(
    query.phuongPhapVoCam.queryAllPhuongPhapVoCam
  );
  const { data: listAllProtocol } = useQueryAll(
    query.protocol.queryAllProtocol
  );

  useEffect(() => {
    if (refAutoFocus.current) {
      refAutoFocus.current.focus();
    }
    form.resetFields();
    loadCurrentItem(currentItem);
  }, [currentItem]);

  const loadCurrentItem = (item) => {
    if (item?.id) {
      const itemData = {
        ...item,
        dsBacSiChiDinhId:
          item.dsBacSiChiDinhId || safeConvertToArray(nhanVienId),
        protocolId: item?.dsProtocol?.[0]?.protocolChiTiet?.protocolId ?? null,
      };
      setData(itemData);
      form.setFieldsValue(itemData);
      setState({
        dsLuocDo: item.dsLuocDo || [],
      });
      refDsLuocDo.current = item.dsLuocDo || [];
    } else {
      const defaultData = {
        dsBacSiChiDinhId: safeConvertToArray(nhanVienId),
        active: true,
      };
      setData(defaultData);
      form.setFieldsValue(defaultData);
      setState({
        dsLuocDo: [],
      });
      refDsLuocDo.current = [];
    }
  };

  const onChange = (form) => (e) => {
    setState({ dsLuocDo: e });
    refDsLuocDo.current = e;
  };

  const onAddNewRow = () => {
    loadCurrentItem({});
  };

  const onCancel = () => {
    if (currentItem?.id) {
      loadCurrentItem({ ...currentItem });
    } else {
      loadCurrentItem({});
      form.resetFields();
    }
  };

  const onSave = (e) => {
    e.preventDefault();
    form.submit();
  };

  const onHandleSubmit = async (values) => {
    try {
      const params = {
        id: data?.id,
        ...values,
        dsLuocDo: state.dsLuocDo,
        loaiDichVu: 40,
        ma: values?.ma?.trim(),
        ten: values?.ten?.trim(),
      };

      const res = await createOrEdit(params);
      updateData({
        currentItem: res?.data || {},
      });
      await onSearch({});
      message.success(
        data?.id
          ? t("common.capNhatThanhCong")
          : t("common.themMoiThanhCongDuLieu")
      );
    } catch (error) {
      message.error(t("common.xayRaLoiVuiLongThuLaiSau"));
    }
  };

  refCallbackSave.current = onSave;

  return (
    <EditWrapper
      title={t("danhMuc.thongTinChiTiet")}
      onCancel={onCancel}
      onSave={onSave}
      onAddNewRow={onAddNewRow}
      roleSave={roleSave}
      roleEdit={roleEdit}
      editStatus={editStatus}
      forceShowButtonSave={checkRole(roleEdit) && true}
      forceShowButtonCancel={checkRole(roleEdit) && true}
      isHiddenButtonAdd={true}
      layerId={layerId}
    >
      <fieldset disabled={editStatus}>
        <Form
          form={form}
          layout="vertical"
          style={{ width: "100%" }}
          className="form-custom"
          onFinish={onHandleSubmit}
        >
          <Form.Item
            label={t("danhMuc.maMauKetQuaPttt")}
            name="ma"
            rules={[
              {
                required: true,
                message: t("danhMuc.vuiLongNhapMaMauKetQuaPttt") + "!",
              },
              {
                max: 20,
                message:
                  t("danhMuc.vuiLongNhapMaMauKetQuaPtttKhongQua20KyTu") + "!",
              },
              {
                whitespace: true,
                message: t("danhMuc.vuiLongNhapMaMauKetQuaPttt") + "!",
              },
            ]}
          >
            <Input
              ref={refAutoFocus}
              autoFocus
              className="input-option"
              placeholder={t("danhMuc.vuiLongNhapMaMauKetQuaPttt")}
            />
          </Form.Item>

          <Form.Item
            label={t("danhMuc.tenMauKetQuaPttt")}
            name="ten"
            rules={[
              {
                required: true,
                message: t("danhMuc.vuiLongNhapTenMauKetQuaPttt") + "!",
              },
              {
                max: 1000,
                message:
                  t("danhMuc.vuiLongNhapTenMauKetQuaPtttKhongQua1000KyTu") +
                  "!",
              },
              {
                whitespace: true,
                message: t("danhMuc.vuiLongNhapTenMauKetQuaPttt") + "!",
              },
            ]}
          >
            <Input
              className="input-option"
              placeholder={t("danhMuc.vuiLongNhapTenMauKetQuaPttt")}
            />
          </Form.Item>

          <Form.Item label={t("danhMuc.mauProtocol")} name="protocolId">
            <Select
              className="input-option"
              placeholder={t("danhMuc.vuiLongChonMauProtocol")}
              data={listAllProtocol}
              getLabel={selectMaTen}
              disabled={true}
            />
          </Form.Item>

          <Form.Item
            label={t("danhMuc.phuongPhapVoCam")}
            name="phuongPhapVoCamId"
          >
            <Select
              data={listAllPhuongPhapVoCam}
              className="input-option"
              placeholder={t("danhMuc.vuiLongChonPhuongPhap")}
            />
          </Form.Item>

          <Form.Item label={t("danhMuc.ketLuan")} name="ketLuan">
            <Input
              className="input-option"
              placeholder={t("danhMuc.vuiLongNhapKetLuan")}
            />
          </Form.Item>

          <Form.Item label={t("danhMuc.cachThucPttt")} name="cachThuc">
            <Input.TextArea
              rows={3}
              className="input-option"
              placeholder={t("danhMuc.vuiLongNhapCachThuc")}
            />
          </Form.Item>

          <Form.Item label={t("danhMuc.phuongPhapPttt")} name="phuongThuc">
            <Input.TextArea
              rows={3}
              className="input-option"
              placeholder={t("danhMuc.vuiLongNhapPhuongPhap")}
            />
          </Form.Item>

          <Form.Item label={t("danhMuc.chanDoanSauPttt")} name="chanDoan">
            <Input
              className="input-option"
              placeholder={t("danhMuc.vuiLongNhapChanDoan")}
            />
          </Form.Item>

          <Form.Item label={t("danhMuc.khoaChiDinh")} name="dsKhoaId">
            <Select
              className="input-option"
              data={listAllKhoa}
              placeholder={
                !isMacDinhKhoaChiDinh ? t("danhMuc.vuiLongChonKhoaChiDinh") : ""
              }
              mode="multiple"
              disabled={isMacDinhKhoaChiDinh}
            />
          </Form.Item>

          <Form.Item label={t("pttt.bacSiChiDinh")} name="dsBacSiChiDinhId">
            <Select
              className="input-option"
              data={listAllNhanVien}
              getLabel={selectMaTen}
              placeholder={t("danhMuc.vuiLongChonBacSiChiDinh")}
              mode="multiple"
              disabled={isMacDinhKhoaChiDinh}
            />
          </Form.Item>

          <Form.Item
            label={
              <div style={{ display: "flex", alignItems: "center" }}>
                <span style={{ marginRight: 10 }}>
                  {t("danhMuc.anhLuocDoPhauThuatThuThuat")}
                </span>
                <Tooltip title={t("pttt.nhanDeTaiAnhLuocDoPttt")}>
                  <SVG.IcCamera
                    style={{ marginRight: 10 }}
                    color={"var(--color-blue-primary)"}
                    onClick={() => {
                      refUpload.current?.openSelectFile();
                    }}
                  />
                </Tooltip>
              </div>
            }
            name="dsLuocDo"
          >
            <div className="image-list-scroll">
              {(state.dsLuocDo || []).map((item, index) => (
                <div key={index} className={`contain-img`}>
                  <ImageEdit
                    typeApi="dmMauPtttLuocDo"
                    src={item}
                    afterSave={(s) => {
                      let _dsLuocDo = [...(state.dsLuocDo || [])];
                      if (!s) {
                        _dsLuocDo.splice(index, 1);
                      } else {
                        _dsLuocDo[index] = s;
                      }
                      onChange(form)(_dsLuocDo);
                    }}
                    placeholder={t("pttt.nhanDeTaiAnhLuocDoPttt")}
                  />
                </div>
              ))}

              <div className={`contain-img bg-gray`}>
                <ImageEdit
                  typeApi="dmMauPtttLuocDo"
                  src={null}
                  ref={refUpload}
                  multiple={true}
                  afterSave={(s) => {
                    let _dsLuocDo = [...(refDsLuocDo.current || [])];
                    if (s) {
                      _dsLuocDo.push(s);
                    }
                    onChange(form)(_dsLuocDo);
                  }}
                  placeholder={t("pttt.nhanDeTaiAnhLuocDoPttt")}
                />
              </div>
            </div>
          </Form.Item>

          {data?.id && (
            <Form.Item label=" " name="active" valuePropName="checked">
              <Checkbox>{t("danhMuc.coHieuLuc")}</Checkbox>
            </Form.Item>
          )}
        </Form>
      </fieldset>
    </EditWrapper>
  );
}

export default forwardRef(ThongTinChiTiet);
