import React, { useState, useEffect, useRef } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import { Form, message } from "antd";
import { Select, Card } from "components";
import { useQueryAll } from "hooks";
import { query } from "redux-store/stores";
import { selectMaTen } from "redux-store/selectors";
import EditWrapper from "components/MultiLevelTab/EditWrapper";
import { checkRole } from "lib-utils/role-utils";
import { isArray } from "utils";
import ProtocolForm from "./ProtocolForm";

const ProtocolPtttTab = React.forwardRef((props, ref) => {
  const {
    refCallbackSave = {},
    currentItem,
    roleSave,
    roleEdit,
    editStatus,
    layerId,
  } = props;

  const { t } = useTranslation();
  const [selectedProtocolId, setSelectedProtocolId] = useState(null);
  const [form] = Form.useForm();

  const {
    mauKetQuaPTTT: { createOrEdit, onSearch, updateData },
  } = useDispatch();

  const { data: listAllProtocol } = useQueryAll(
    query.protocol.queryAllProtocol
  );

  useEffect(() => {
    if (currentItem?.id) {
      if (isArray(currentItem.dsProtocol, 1)) {
        const protocolData = currentItem.dsProtocol[0];
        if (protocolData) {
          setSelectedProtocolId(
            protocolData.protocolChiTiet?.protocolId ?? null
          );
        }
      } else {
        setSelectedProtocolId(null);
      }
    } else {
      setSelectedProtocolId(null);
    }
  }, [currentItem, listAllProtocol]);

  const onCancel = () => {
    form.resetFields();
    setSelectedProtocolId(null);
  };

  const onAddNewRow = () => {
    form.resetFields();
    setSelectedProtocolId(null);
  };

  const onSave = (e) => {
    e.preventDefault();
    form.submit();
  };

  const onHandleSubmit = async (values, protocolData) => {
    try {
      if (!selectedProtocolId) {
        message.error(t("danhMuc.vuiLongChonMauProtocol"));
        return;
      }

      const params = {
        ...currentItem,
        dsProtocol: protocolData || [],
      };

      const res = await createOrEdit(params);
      updateData({
        currentItem: res?.data || {},
      });
      await onSearch({});
      message.success(
        currentItem?.id
          ? t("common.capNhatThanhCong")
          : t("common.themMoiThanhCongDuLieu")
      );
    } catch (error) {
      message.error(t("common.xayRaLoiVuiLongThuLaiSau"));
    }
  };

  refCallbackSave.current = onSave;

  return (
    <EditWrapper
      title={t("danhMuc.mauKetQuaProtocolPTTT")}
      onCancel={onCancel}
      onSave={onSave}
      onAddNewRow={onAddNewRow}
      roleSave={roleSave}
      roleEdit={roleEdit}
      editStatus={editStatus}
      forceShowButtonSave={checkRole(roleEdit) && true}
      forceShowButtonCancel={checkRole(roleEdit) && true}
      isHiddenButtonAdd={true}
      layerId={layerId}
    >
      <fieldset disabled={editStatus}>
        <div style={{ padding: "16px" }}>
          <div style={{ marginBottom: "24px" }}>
            <label
              style={{
                display: "block",
                marginBottom: "8px",
                fontWeight: "500",
              }}
            >
              {t("danhMuc.chonMauProtocol")}:
            </label>
            <Select
              className="input-option"
              placeholder={t("danhMuc.vuiLongChonMauProtocol")}
              data={listAllProtocol}
              getLabel={selectMaTen}
              value={selectedProtocolId}
              onChange={setSelectedProtocolId}
              style={{ width: "100%", maxWidth: "400px" }}
            />
          </div>

          {selectedProtocolId && (
            <Card
              title={t("danhMuc.mauKetQuaProtocolPTTT")}
              style={{ marginTop: "16px" }}
            >
              <ProtocolForm
                form={form}
                protocolId={selectedProtocolId}
                currentData={currentItem?.dsProtocol}
                disabled={editStatus}
                onFinish={onHandleSubmit}
              />
            </Card>
          )}
        </div>
      </fieldset>
    </EditWrapper>
  );
});

export default ProtocolPtttTab;
