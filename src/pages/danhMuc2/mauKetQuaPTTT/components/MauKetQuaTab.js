import { Form, Input } from "antd";
import { Checkbox, ImageEdit, Select, Tooltip } from "components";
import { ROLES } from "constants/index";
import { useLazyKVMap, useQueryAll, useStore } from "hooks";
import React, { useState, useRef, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import { checkRole } from "lib-utils/role-utils";
import { query } from "redux-store/stores";
import { selectMaTen } from "redux-store/selectors";
import { safeConvertToArray } from "utils";
import { SVG } from "assets";

const MauKetQuaTab = ({ 
  form, 
  editStatus, 
  refAutoFocus, 
  autoFocus, 
  checkChangeField,
  nhanVienId 
}) => {
  const { t } = useTranslation();
  const refDsLuocDo = useRef([]);
  const refUpload = useRef(null);

  const isMacDinhKhoaChiDinh = !checkRole([
    ROLES["DANH_MUC"].MAU_KQ_PT_TT_HIEN_THI_MAU_THEO_KHOA_CHI_DINH,
  ]);
  
  const { data: listAllKhoa } = useQueryAll(query.khoa.queryAllKhoa);
  const { data: listAllNhanVien } = useQueryAll(
    query.nhanVien.queryAllNhanVien
  );
  const { data: listAllPhuongPhapVoCam } = useQueryAll(
    query.phuongPhapVoCam.queryAllPhuongPhapVoCam
  );
  const { data: listAllProtocol } = useQueryAll(
    query.protocol.queryAllProtocol
  );

  const [state, _setState] = useState({});
  const setState = (data) => {
    _setState({
      ...state,
      ...data,
    });
  };

  const onChange = (form) => (e) => {
    setState({ dsLuocDo: e });
    refDsLuocDo.current = e;
  };

  useEffect(() => {
    form.setFieldsValue({
      dsBacSiChiDinhId: safeConvertToArray(nhanVienId),
    });
  }, [nhanVienId]);

  return (
    <>
      <Form.Item
        label={t("danhMuc.maMauKetQuaPttt")}
        name="ma"
        rules={[
          {
            required: true,
            message: t("danhMuc.vuiLongNhapMaMauKetQuaPttt") + "!",
          },
          {
            max: 20,
            message:
              t("danhMuc.vuiLongNhapMaMauKetQuaPtttKhongQua20KyTu") + "!",
          },
          {
            whitespace: true,
            message: t("danhMuc.vuiLongNhapMaMauKetQuaPttt") + "!",
          },
        ]}
      >
        <Input
          autoFocus={autoFocus}
          className="input-option"
          placeholder={t("danhMuc.vuiLongNhapMaMauKetQuaPttt")}
          ref={refAutoFocus}
        />
      </Form.Item>
      <Form.Item
        label={t("danhMuc.tenMauKetQuaPttt")}
        name="ten"
        rules={[
          {
            required: true,
            message: t("danhMuc.vuiLongNhapTenMauKetQuaPttt") + "!",
          },
          {
            max: 1000,
            message:
              t("danhMuc.vuiLongNhapTenMauKetQuaPtttKhongQua1000KyTu") + "!",
          },
          {
            whitespace: true,
            message: t("danhMuc.vuiLongNhapTenMauKetQuaPttt") + "!",
          },
        ]}
      >
        <Input
          className="input-option"
          placeholder={t("danhMuc.vuiLongNhapTenMauKetQuaPttt")}
        />
      </Form.Item>
      <Form.Item label={t("danhMuc.mauProtocol")} name="protocolId">
        <Select
          className="input-option"
          placeholder={t("danhMuc.vuiLongChonMauProtocol")}
          data={listAllProtocol}
          getLabel={selectMaTen}
        />
      </Form.Item>
      <Form.Item
        label={t("danhMuc.phuongPhapVoCam")}
        name="phuongPhapVoCamId"
      >
        <Select
          data={listAllPhuongPhapVoCam}
          className="input-option"
          placeholder={t("danhMuc.vuiLongChonPhuongPhap")}
        />
      </Form.Item>
      <Form.Item label={t("danhMuc.ketLuan")} name="ketLuan">
        <Input
          className="input-option"
          placeholder={t("danhMuc.vuiLongNhapKetLuan")}
        />
      </Form.Item>
      <Form.Item
        label={t("danhMuc.cachThucPttt")}
        className="w100"
        name="cachThuc"
      >
        <Input.TextArea
          rows={3}
          className="input-option"
          placeholder={t("danhMuc.vuiLongNhapCachThuc")}
        />
      </Form.Item>
      <Form.Item
        label={t("danhMuc.phuongPhapPttt")}
        className="w100"
        name="phuongThuc"
      >
        <Input.TextArea
          rows={3}
          className="input-option"
          placeholder={t("danhMuc.vuiLongNhapPhuongPhap")}
        />
      </Form.Item>
      <Form.Item label={t("danhMuc.chanDoanSauPttt")} name="chanDoan">
        <Input
          className="input-option"
          placeholder={t("danhMuc.vuiLongNhapChanDoan")}
        />
      </Form.Item>
      <Form.Item label={t("danhMuc.khoaChiDinh")} name="dsKhoaId">
        <Select
          className="input-option"
          data={listAllKhoa}
          placeholder={
            !isMacDinhKhoaChiDinh ? t("danhMuc.vuiLongChonKhoaChiDinh") : ""
          }
          mode="multiple"
          disabled={isMacDinhKhoaChiDinh}
        />
      </Form.Item>
      <Form.Item label={t("pttt.bacSiChiDinh")} name="dsBacSiChiDinhId">
        <Select
          className="input-option"
          data={listAllNhanVien}
          getLabel={selectMaTen}
          placeholder={t("danhMuc.vuiLongChonBacSiChiDinh")}
          mode="multiple"
          disabled={isMacDinhKhoaChiDinh}
        />
      </Form.Item>
      <Form.Item
        label={
          <div style={{ display: "flex", alignItems: "center" }}>
            <span style={{ marginRight: 10 }}>
              {t("danhMuc.anhLuocDoPhauThuatThuThuat")}
            </span>

            <Tooltip title={t("pttt.nhanDeTaiAnhLuocDoPttt")}>
              <SVG.IcCamera
                style={{ marginRight: 10 }}
                color={"var(--color-blue-primary)"}
                onClick={() => {
                  refUpload.current?.openSelectFile();
                }}
              />
            </Tooltip>
          </div>
        }
        name="dsLuocDo"
      >
        <div className="image-list-scroll">
          {(state.dsLuocDo || []).map((item, index) => (
            <div key={index} className={`contain-img`}>
              <ImageEdit
                typeApi="dmMauPtttLuocDo"
                src={item}
                afterSave={(s) => {
                  let _dsLuocDo = [...(state.dsLuocDo || [])];

                  if (!s) {
                    _dsLuocDo.splice(index, 1);
                  } else {
                    _dsLuocDo[index] = s;
                  }

                  onChange(form)(_dsLuocDo);
                  checkChangeField("dsLuocDo")(_dsLuocDo);
                }}
                placeholder={t("pttt.nhanDeTaiAnhLuocDoPttt")}
              />
            </div>
          ))}

          <div className={`contain-img bg-gray`}>
            <ImageEdit
              typeApi="dmMauPtttLuocDo"
              src={null}
              ref={refUpload}
              multiple={true}
              afterSave={(s) => {
                let _dsLuocDo = [...(refDsLuocDo.current || [])];

                if (s) {
                  _dsLuocDo.push(s);
                }
                onChange(form)(_dsLuocDo);
                checkChangeField("dsLuocDo")(_dsLuocDo);
              }}
              placeholder={t("pttt.nhanDeTaiAnhLuocDoPttt")}
            />
          </div>
        </div>
      </Form.Item>
      {editStatus && (
        <Form.Item name="active" valuePropName="checked">
          <Checkbox>{t("danhMuc.coHieuLuc")}</Checkbox>
        </Form.Item>
      )}
    </>
  );
};

export default MauKetQuaTab;
