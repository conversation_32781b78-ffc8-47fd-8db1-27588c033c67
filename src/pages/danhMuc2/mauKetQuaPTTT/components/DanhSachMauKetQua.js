import React, { useEffect, useRef } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import {
  TableWrapper,
  HeaderSearch,
  Select,
  Button,
  Checkbox,
  InputTimeout,
} from "components";
import { useLazyKVMap, useQueryAll } from "hooks";
import { dispatch, query } from "redux-store/stores";
import { selectMaTen } from "redux-store/selectors";
import { checkRole } from "lib-utils/role-utils";
import { ROLES, HIEU_LUC } from "constants/index";
import { SVG } from "assets";
import { combineSort, safeConvertToArray } from "utils";

const DanhSachMauKetQua = ({
  layerId,
  setEditStatus,
  title,
  classNameRow,
  styleMain,
  styleContainerButtonHeader,
  buttonHeader,
  nhanVienId,
}) => {
  const { t } = useTranslation();
  const refClickBtnAdd = useRef();

  const {
    mauKetQuaPTTT: { onSearch, updateData },
  } = useDispatch();

  const {
    listData,
    totalElements,
    page,
    size,
    dataSearch,
    dataSortColumn,
    currentItem,
  } = useSelector((state) => state.mauKetQuaPTTT);

  const isMacDinhKhoaChiDinh = !checkRole([
    ROLES["DANH_MUC"].MAU_KQ_PT_TT_HIEN_THI_MAU_THEO_KHOA_CHI_DINH,
  ]);

  const { data: listAllKhoa } = useQueryAll(query.khoa.queryAllKhoa);
  const { data: listAllNhanVien } = useQueryAll(
    query.nhanVien.queryAllNhanVien
  );

  const [getKhoaById] = useLazyKVMap(listAllKhoa);
  const [getNhanVienById] = useLazyKVMap(listAllNhanVien);

  const onClickSort = (key, value) => {
    dispatch.mauKetQuaPTTT.onSearch({
      dataSortColumn: {
        ...dataSortColumn,
        [key]: value,
      },
    });
  };

  const onSearchInput = (key) => (e) => {
    let value = "";
    if (e?.target) {
      if (e.target.hasOwnProperty("checked")) value = e.target.checked;
      else value = e.target.value;
    } else if (e?._d) value = e.format("YYYY-MM-DD");
    else value = e;

    dispatch.mauKetQuaPTTT.updateData({
      dataSearch: {
        ...dataSearch,
        [key]: value,
      },
    });
  };

  const getColumns = () => [
    {
      title: <HeaderSearch title={t("common.stt")} />,
      width: 50,
      dataIndex: "index",
      key: "index",
      align: "center",
      render: (_, __, index) => {
        return page * size + index + 1;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.ma")}
          sort_key="ma"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.ma || 0}
          search={
            <InputTimeout
              placeholder={t("danhMuc.timTheoMa")}
              onChange={onSearchInput("ma")}
            />
          }
        />
      ),
      width: 120,
      dataIndex: "ma",
      key: "ma",
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.ten")}
          sort_key="ten"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.ten || 0}
          search={
            <InputTimeout
              placeholder={t("danhMuc.timTheoTen")}
              onChange={onSearchInput("ten")}
            />
          }
        />
      ),
      width: 250,
      dataIndex: "ten",
      key: "ten",
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.khoaChiDinh")}
          sort_key="dsKhoaId"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.dsKhoaId || 0}
          searchSelect={
            <Select
              defaultValue=""
              data={listAllKhoa}
              placeholder={t("danhMuc.chonKhoa")}
              onChange={onSearchInput("dsKhoaId")}
              hasAllOption={true}
            />
          }
        />
      ),
      dataIndex: "dsKhoaId",
      width: 180,
      key: "dsKhoaId",
      render: (item) => {
        if (!item?.length) return "";
        return item
          .map((id) => getKhoaById(id)?.ten)
          .filter(Boolean)
          .join(", ");
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("pttt.bacSiChiDinh")}
          sort_key="dsBacSiChiDinhId"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.dsBacSiChiDinhId || 0}
          searchSelect={
            <Select
              defaultValue=""
              data={listAllNhanVien}
              placeholder={t("pttt.chonBacSi")}
              onChange={onSearchInput("dsBacSiChiDinhId")}
              hasAllOption={true}
            />
          }
        />
      ),
      dataIndex: "dsBacSiChiDinhId",
      disabled: isMacDinhKhoaChiDinh,
      width: 180,
      key: "dsBacSiChiDinhId",
      render: (item) => {
        if (!item?.length) return "";
        return item
          .map((id) => getNhanVienById(id) && selectMaTen(getNhanVienById(id)))
          .filter(Boolean)
          .join(", ");
      },
    },
    {
      title: (
        <HeaderSearch
          sort_key="active"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.active || 0}
          searchSelect={
            <Select
              defaultValue=""
              data={HIEU_LUC}
              placeholder={t("danhMuc.chonHieuLuc")}
              onChange={onSearchInput("active")}
              hasAllOption={true}
            />
          }
          title={t("danhMuc.coHieuLuc")}
        />
      ),
      width: "70px",
      dataIndex: "active",
      key: "active",
      align: "center",
      render: (item) => {
        return <Checkbox checked={item} />;
      },
    },
  ];

  const handleClickedBtnAdded = () => {
    setEditStatus(false);
    updateData({ currentItem: {} });
  };

  const onRow = (record = {}) => {
    return {
      onClick: (event) => {
        if (currentItem?.id !== record.id) {
          dispatch.mauKetQuaPTTT.updateData({ currentItem: record });
          updateData({ currentItem: record });
          setEditStatus(true);
        }
      },
    };
  };

  useEffect(() => {
    onSearch({
      dataSearch: {
        ...dataSearch,
        loaiDichVu: 40,
        dsBacSiChiDinhId: isMacDinhKhoaChiDinh
          ? safeConvertToArray(nhanVienId)
          : dataSearch.dsBacSiChiDinhId,
      },
    });
  }, []);

  const onChangePage = (page, size) => {
    dispatch.mauKetQuaPTTT.onSearch({ page: page - 1, size });
  };

  refClickBtnAdd.current = handleClickedBtnAdded;

  return (
    <TableWrapper
      title={title}
      classNameRow={classNameRow}
      styleMain={styleMain}
      styleContainerButtonHeader={styleContainerButtonHeader}
      buttonHeader={[
        ...(checkRole([ROLES["DANH_MUC"].MAU_KQ_PT_TT_THEM])
          ? [
              {
                content: (
                  <Button
                    type="success"
                    onClick={handleClickedBtnAdded}
                    rightIcon={<SVG.IcAdd />}
                  >
                    {t("common.themMoiF1")}
                  </Button>
                ),
              },
            ]
          : []),
        ...buttonHeader,
      ]}
      columns={getColumns()}
      dataSource={listData}
      onRow={onRow}
      layerId={layerId}
      page={page}
      size={size}
      totalElements={totalElements}
      onChangePage={onChangePage}
      dataEditDefault={currentItem}
    />
  );
};

export default DanhSachMauKetQua;
