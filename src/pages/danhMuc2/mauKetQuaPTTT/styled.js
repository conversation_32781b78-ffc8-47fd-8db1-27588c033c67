import styled from "styled-components";
export const Main = styled.div`
  .image-list-scroll {
    overflow-y: auto;
    overflow-x: hidden;
    height: 300px;
  }

  .contain-img {
    display: flex;
    justify-content: center;
    height: 200px;
    margin-top: 0 !important;
    margin-left: 5px;
    margin-right: 5px;
    margin-bottom: 5px;
    border: 1px solid #e0e0e0;
    &.bg-gray {
      background-color: rgb(242 242 242);
    }
    background-color: #f0f0f0;
    .img-view {
      height: 100%;
      width: 100%;
      object-fit: cover;
    }
    .img-default {
      object-fit: contain;
    }
  }
  .ant-tabs-tabpane {
    width: 100%;
  }
`;
