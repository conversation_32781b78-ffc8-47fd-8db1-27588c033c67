import React, { useEffect } from "react";
import { Form } from "antd";
import { useTranslation } from "react-i18next";
import { useEnum, useStore } from "hooks";
import { Checkbox, Select, TableWrapper } from "components";
import BaseDmWrap from "components/BaseDmWrap";
import { ENUM, ROLES } from "constants/index";
import { MainStyled } from "./styled";
import { isArray } from "utils/index";
import { useDispatch } from "react-redux";

const { ColumnSelect, ColumnCheckbox } = TableWrapper;

const KyChuyenKhoaKhiRaVien = () => {
  const { t } = useTranslation();
  const [listDoiTuongKcb] = useEnum(ENUM.DOI_TUONG_KCB);

  const {
    baoCao: { getListAllBaoCao },
  } = useDispatch();

  const listAllBaoCao = useStore(
    "baoCao.listAllBaoCao",
    []
  );

  useEffect(() => {
    getListAllBaoCao({ page: "", size: "", active: true, });
  }, []);

  const getColumns = ({ baseColumns = {}, ...rest }) => [
    baseColumns.stt,
    ColumnSelect({
      ...rest,
      title: t("danhMuc.tenPhieu"),
      dataIndex: "baoCaoId",
      dataSelect: listAllBaoCao,
      width: 200,
      render: (item) => {
        return listAllBaoCao?.find(i => i.id === item)?.ten || "";
      },
    }),
    ColumnSelect({
      ...rest,
      title: t("danhMuc.doiTuongKCB"),
      dataIndex: "dsDoiTuongKcb",
      dataSelect: listDoiTuongKcb,
      width: 200,
      render: (item) => {
        if (isArray(item, true)) {
          return item
            .reduce((acc, cur) => {
              let curItem = listDoiTuongKcb.find((i) => i.id === cur);
              if (curItem) {
                acc.push(curItem?.ten);
              }
              return acc;
            }, [])
            .join(", ");
        }
        return "";
      },
    }),
    ColumnCheckbox({
      title: t("dashboard.nhapVien"),
      dataIndex: "nhapVien",
      width: 120,
      dataSearch: [t("danhMuc.coHieuLuc"), t("danhMuc.khongHieuLuc")],
      ...rest,
    }),
    ColumnCheckbox({
      title: t("danhMuc.chuyenKhoaToiKhoaThuong"),
      dataIndex: "chuyenKhoaToiKhoaThuong",
      width: 120,
      dataSearch: [t("danhMuc.coHieuLuc"), t("danhMuc.khongHieuLuc")],
      ...rest,
    }),
    ColumnCheckbox({
      title: t("danhMuc.chuyenKhoaToiKhoaPhauThuat"),
      dataIndex: "chuyenKhoaToiKhoaPhauThuat",
      width: 120,
      dataSearch: [t("danhMuc.coHieuLuc"), t("danhMuc.khongHieuLuc")],
      ...rest,
    }),
    ColumnCheckbox({
      title: t("danhMuc.chuyenKhoaTuKhoaPhauThuat"),
      dataIndex: "chuyenKhoaTuKhoaPhauThuat",
      width: 120,
      dataSearch: [t("danhMuc.coHieuLuc"), t("danhMuc.khongHieuLuc")],
      ...rest,
    }),
    ColumnCheckbox({
      title: t("danhMuc.raVienTuKhoaThuong"),
      dataIndex: "raVienTuKhoaThuong",
      width: 120,
      dataSearch: [t("danhMuc.coHieuLuc"), t("danhMuc.khongHieuLuc")],
      ...rest,
    }),
    ColumnCheckbox({
      title: t("danhMuc.raVienTuKhoaPhauThuat"),
      dataIndex: "raVienTuKhoaPhauThuat",
      width: 120,
      dataSearch: [t("danhMuc.coHieuLuc"), t("danhMuc.khongHieuLuc")],
      ...rest,
    }),
    baseColumns.active,
  ];

  const renderForm = ({ form, editStatus }) => {
    return (
      <>
        <Form.Item label={t("danhMuc.tenPhieu")} name="baoCaoId"
          rules={[
            {
              required: true,
              message: t("danhMuc.vuiLongChonTenPhieu"),
            }
          ]}
        >
          <Select
            className="input-option"
            data={listAllBaoCao}
            showArrow={false}
            placeholder={t("danhMuc.chonTenPhieu")}
            hasAllOption={false}
          />
        </Form.Item>
        <Form.Item label={t("danhMuc.doiTuongKhamChuaBenh")} name="dsDoiTuongKcb">
          <Select
            className="input-option"
            data={listDoiTuongKcb}
            showArrow={false}
            mode="multiple"
            placeholder={t("danhMuc.chonDoiTuongKhamChuaBenh")}
          />
        </Form.Item>
        <Form.Item label={t("dashboard.nhapVien")} valuePropName="checked" name="nhapVien">
          <Checkbox>{t("danhMuc.coHieuLuc")}</Checkbox>
        </Form.Item>
        <Form.Item label={t("danhMuc.chuyenKhoaToiKhoaThuong")} valuePropName="checked" name="chuyenKhoaToiKhoaThuong">
          <Checkbox>{t("danhMuc.coHieuLuc")}</Checkbox>
        </Form.Item>
        <Form.Item label={t("danhMuc.chuyenKhoaToiKhoaPhauThuat")} valuePropName="checked" name="chuyenKhoaToiKhoaPhauThuat">
          <Checkbox>{t("danhMuc.coHieuLuc")}</Checkbox>
        </Form.Item>
        <Form.Item label={t("danhMuc.chuyenKhoaTuKhoaPhauThuat")} valuePropName="checked" name="chuyenKhoaTuKhoaPhauThuat">
          <Checkbox>{t("danhMuc.coHieuLuc")}</Checkbox>
        </Form.Item>
        <Form.Item label={t("danhMuc.raVienTuKhoaThuong")} valuePropName="checked" name="raVienTuKhoaThuong">
          <Checkbox>{t("danhMuc.coHieuLuc")}</Checkbox>
        </Form.Item>
        <Form.Item label={t("danhMuc.raVienTuKhoaPhauThuat")} valuePropName="checked" name="raVienTuKhoaPhauThuat">
          <Checkbox>{t("danhMuc.coHieuLuc")}</Checkbox>
        </Form.Item>
        <Form.Item label={t("danhMuc.hieuLuc")} valuePropName="checked" name="active">
          <Checkbox>{t("danhMuc.coHieuLuc")}</Checkbox>
        </Form.Item>
      </>
    );
  };

  return (
    <MainStyled>
      <BaseDmWrap
        getColumns={getColumns}
        renderForm={renderForm}
        titleTable={t("danhMuc.thietLapDieuKienHoanThanhKy")}
        roleName="QUYEN_KY_CHUYEN_KHOA"
        roleSave={ROLES["DANH_MUC"].QUYEN_KY_CHUYEN_KHOA_THEM}
        onImport={false}
        onExport={false}
        roleEdit={ROLES["DANH_MUC"].QUYEN_KY_CHUYEN_KHOA_SUA}
        storeName="chuyenKhoaRaVien"
        listLink={[
          {
            title: t("danhMuc.title"),
            link: "/danh-muc",
          },
          {
            title: t("danhMuc.thietLapDieuKienHoanThanhKy"),
            link: "/danh-muc/thiet-lap-ky-hoan-thanh-chuyen-khoa-ra-vien",
          },
        ]}
      />
    </MainStyled>
  );
};

export default KyChuyenKhoaKhiRaVien;
