import React from "react";
import { useTranslation } from "react-i18next";
import BaseDmWrap from "components/BaseDmWrap";

const LyDoChiDinhDichVu = ({ }) => {
  const { t } = useTranslation();
  const nameScreen = t("danhMuc.lyDoChiDinhDichVu");

  return (
    <BaseDmWrap
      titleMain={nameScreen}
      roleName="LY_DO_CHI_DINH_DICH_VU"
      storeName="lyDoChiDinhDichVu"
      listLink={[
        {
          title: t("danhMuc.title"),
          link: "/danh-muc",
        },
        {
          title: nameScreen,
          link: "/danh-muc/ly-do-chi-dinh-dich-vu",
        },
      ]}
    />
  );
};

export default LyDoChiDinhDichVu;
