import React, { memo, forwardRef, useContext } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Checkbox } from "components";
import { useTranslation } from "react-i18next";
import { SVG } from "assets";
import { useStore } from "hooks";
import { Main } from "../CheckBoxUuTien/styled";
import TiepDonContext from "pages/tiepDon/context/TiepDon";
import moment from "moment";
import { checkRole } from "lib-utils/role-utils";
import { ROLES } from "constants/index";

const CheckBoxCapCuu = ({ fromSetting, ...props }, ref) => {
  const { t } = useTranslation();
  const { disableTiepDon, modalAddCapCuu } = useContext(TiepDonContext);

  const capCuu = useSelector((state) => state.tiepDon.capCuu);
  const nbCapCuu = useStore("tiepDon.nbCapCuu");
  const tiepDonId = useStore("tiepDon.id", null);

  const {
    tiepDon: { updateData, updateThongTinNb },
  } = useDispatch();

  const onShowAddCapCuu = (item) => {
    modalAddCapCuu.current.show(
      {
        show: true,
        ...item,
        loaiCapCuuId: item?.loaiCapCuuId,
        viTriChanThuongId: item?.viTriChanThuongId,
        taiNanThuongTichId: item?.taiNanThuongTichId,
        thoiGianCapCuuId: item?.thoiGianCapCuuId,
        chuaXacDinhDanhTinh: item?.chuaXacDinhDanhTinh,
        khongCoNguoiThanDiKem: item?.khongCoNguoiThanDiKem,
        thoiGianXayRaTaiNan: nbCapCuu?.thoiGianXayRaTaiNan
          ? moment(nbCapCuu?.thoiGianXayRaTaiNan)
          : null,
      },
      (data = {}) => {
        updateThongTinNb(data, "nbCapCuu");
      }
    );
  };

  const onChange = (value, variables) => {
    updateData({ [`${variables}`]: value });
    if (value) onShowAddCapCuu();
    else updateData({ nbCapCuu: {} });
  };

  if (
    checkRole([ROLES["TIEP_DON"].CHINH_SUA_TT_NB_AN_CHECK_BOX_CAP_CUU]) &&
    tiepDonId
  )
    return null;

  return (
    <Main sm={12} md={12} xl={12} xxl={12} {...props} ref={ref}>
      <Checkbox
        className="box-item"
        onChange={(e) => {
          let value = e?.target?.checked;
          onChange(value, "capCuu");
        }}
        onKeyDown={(e) => {
          if (e.key === "Enter") {
            let value = e?.target?.checked;
            onChange(!value, "capCuu");
          }
        }}
        checked={capCuu}
        disabled={disableTiepDon}
      >
        <span>{t("tiepDon.capCuu")}</span>
      </Checkbox>
      {capCuu && nbCapCuu?.loaiCapCuuId ? (
        <span className="detail-view">
          {/* {"Chi tiết"} */}
          <SVG.IcEye onClick={() => onShowAddCapCuu(nbCapCuu)} />
        </span>
      ) : null}
    </Main>
  );
};

export default memo(forwardRef(CheckBoxCapCuu));
