import React, {
  forwardRef,
  useEffect,
  useMemo,
  useRef,
  useState,
  useImperativeHandle,
} from "react";
import moment from "moment";
import { useDispatch, useSelector } from "react-redux";
import { Radio, message, Input } from "antd";
import { useTranslation } from "react-i18next";
import { cloneDeep, groupBy, orderBy, uniqBy } from "lodash";
import {
  useCache,
  useConfirm,
  useEnum,
  useListAll,
  useLoading,
  useStore,
  useThietLap,
} from "hooks";
import { checkRole } from "lib-utils/role-utils";
import { isArray } from "utils";
import {
  Tooltip,
  Checkbox,
  Pagination,
  HeaderSearch,
  TableWrapper,
  Select,
  DateTimePicker,
} from "components";
import ModalHoanDichVu from "components/ModalHoanDichVu";
import ModalHuyHoanDichVu from "components/ModalHuyHoanDichVu";
import {
  CACHE_KEY,
  ENUM,
  LOAI_DICH_VU,
  TRANG_THAI_DICH_VU,
  DOI_TUONG_KCB,
  GIOI_TINH_BY_VALUE,
  THIET_LAP_CHUNG,
  TRANG_THAI_PHIEU_THU_THANH_TOAN,
  ROLES,
} from "constants/index";
import { SVG } from "assets";
import ModalDoiDVPhongTH from "pages/khamBenh/components/StepWrapper/ModalDoiDVPhongTH";
import { Main } from "./styled";
import ngoaiVienProvider from "data-access/ngoai-vien-provider";

const { Setting } = TableWrapper;

const DanhSachDichVu = (props, ref) => {
  const { showConfirm } = useConfirm();
  const { nbDotDieuTriId, isNBKhamSucKhoe = false, isEdit, onSetData } = props;
  const { t } = useTranslation();
  const refSettings = useRef(null);
  const refModalHoanDichVu = useRef(null);
  const refModalHuyHoanDichVu = useRef(null);
  const refDoiDVPhongTH = useRef(null);

  const { totalElements, page, size, isLoading } = useStore(
    "danhSachDichVuNbTiepDon",
    {},
    { fields: "totalElements, page, size, isLoading" }
  );
  const { gioiTinh, tuoi, thangTuoi } = useStore(
    "tiepDon",
    {},
    { fields: " gioiTinh, tuoi, thangTuoi" }
  );
  const thongTinBenhNhan = useStore("nbDotDieuTri.thongTinBenhNhan", {});
  const _listBacSi = useStore("nhanVien.listBacSi", []);
  const listDichVuTiepDon = useStore(
    "danhSachDichVuNbTiepDon.listDichVuTiepDon",
    []
  );
  const [listTrangThaiHoan] = useEnum(ENUM.TRANG_THAI_HOAN);
  const [listTrangThaiDichVu] = useEnum(ENUM.TRANG_THAI_DICH_VU);
  const { listAllKhoa } = useSelector((state) => state.khoa);
  const [dataAN_CHECKBOX_TU_TRA] = useThietLap(
    THIET_LAP_CHUNG.AN_CHECKBOX_TU_TRA
  );
  const [dataAN_CHECKBOX_KHONG_THUC_HIEN_DVKT] = useThietLap(
    THIET_LAP_CHUNG.AN_CHECKBOX_KHONG_THUC_HIEN_DVKT
  );

  const { showLoading, hideLoading } = useLoading();
  const [listhinhThucTtKsk] = useEnum(ENUM.HINH_THUC_TT_KSK);
  const [_listAllNhanVien] = useListAll("nhanVien");
  const listBacSi = useMemo(() => {
    return _listBacSi.map((item) => ({
      id: item.id,
      ten: [item.taiKhoan, item.ma, item.ten].filter(Boolean).join(" - "),
    }));
  }, [_listBacSi]);

  const [listAllNhanVien, getNhanVien] = useMemo(() => {
    const map = _listAllNhanVien.reduce((acc, item) => {
      return acc.set(item.id, {
        id: item.id,
        ten: [item.taiKhoan, item.ma, item.ten].filter(Boolean).join(" - "),
      });
    }, new Map());
    return [Array.from(map.values()), (id) => map.get(id)];
  }, [_listAllNhanVien]);

  const {
    danhSachDichVuNbTiepDon: {
      searchDichVuTiepDon,
      onSizeChange,
      onDeleteDichVu,
      onCheckInDichVu,
      updateData,
      onChangeInputSearch,
    },
    khoa: { getListAllKhoa },
    chiDinhKhamBenh: { inPhieu },
    nhanVien: { getListBacSi },
    loaiDoiTuongLoaiHinhTT: { getListLoaiDoiTuongTT },
    phongThucHien: { getListPhongTheoDichVu },
    khamBenh: { getNbDvKham },
    tiepDonDichVu: { thayDoiThongTinDichVu },
  } = useDispatch();

  const [tuyChinhHienThi, setDataTuyChinhHienThi, loadFinish] = useCache(
    "",
    CACHE_KEY.DATA_HIEN_THI_DICH_VU_KE_TIEP_DON,
    "1",
    false
  );

  const [state, _setState] = useState({
    selectedRowKeys: [],
    selectedData: [],
    renderReady: false,
  });

  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  useEffect(() => {
    if (nbDotDieuTriId && tuyChinhHienThi && loadFinish) {
      const params = { size: 50, nbDotDieuTriId };
      if (!isNBKhamSucKhoe) {
        params.dsChiDinhTuLoaiDichVu = [200, 230, 240];
      } else {
        params.dsChiDinhTuLoaiDichVu = null;
      }
      if (tuyChinhHienThi === "2") {
        params.dsDoiTuongKcb = [
          DOI_TUONG_KCB.NGOAI_TRU,
          DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_LINH_THUOC,
          DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_SU_DUNG_THUOC,
        ];
        params.dsChiDinhTuLoaiDichVu = null;
      }

      onChangeInputSearch(params);
    }
    getListAllKhoa({ active: true, page: "", size: "" });
  }, [nbDotDieuTriId, tuyChinhHienThi, loadFinish, isNBKhamSucKhoe]);

  useEffect(() => {
    let params = {
      page: "",
      size: "",
      active: true,
    };
    getListBacSi({
      dsMaThietLapVanBang: [THIET_LAP_CHUNG.BAC_SI],
      ...params,
    });
    return () => updateData({ listDichVuTiepDon: [] });
  }, []);

  useEffect(() => {
    if (!thongTinBenhNhan?.id) return;
    let dataSource = cloneDeep(listDichVuTiepDon);
    let listPhong = [];
    if (dataSource.length) {
      async function fetchData() {
        try {
          listPhong = await getListPhongTheoDichVu({
            page: "",
            size: "",
            dsDichVuId: dataSource.map((item) => item.dichVuId),
            khoaChiDinhId: thongTinBenhNhan.khoaTiepDonId,
            doiTuongKcb: thongTinBenhNhan.doiTuongKcb,
          });
        } catch (error) {
          listPhong = [];
        }
        const phongByDichVuId = groupBy(listPhong, "dichVuId");
        dataSource.forEach((dichVu) => {
          dichVu.dsPhongThucHien = phongByDichVuId[dichVu?.dichVuId];
        });
        const dataFormatDate = dataSource.map((item) => ({
          ...item,
          ngayThucHien:
            item.thoiGianThucHien &&
            moment(item.thoiGianThucHien).format("YYYY-MM-DD"),
        }));
        const dataMerge = dataFormatDate.reduce((data, current) => {
          const { khoaChiDinhId, ngayThucHien } = current;
          const key = `${khoaChiDinhId} - ${ngayThucHien}`;

          data[key] = data[key] || {
            khoaChiDinhId,
            ngayThucHien,
            dsDichVu: [],
          };
          data[key]["dsDichVu"].push(current);
          return data;
        }, {});
        const dsDichVu = [];
        await Promise.all(
          Object.keys(dataMerge).map(async (key) => {
            const data = dataMerge[key];
            const listLoaiHinh = await getListLoaiDoiTuongTT({
              active: true,
              page: "",
              size: "",
              dsDichVuId: data?.dsDichVu?.map((item) => item.dichVuId),
              loaiDoiTuongId: thongTinBenhNhan.loaiDoiTuongId,
              khoaChiDinhId: data?.dsDichVu?.[0].khoaChiDinhId,
              ngayThucHien: data?.dsDichVu?.[0].ngayThucHien,
              ngayVaoVien:
                thongTinBenhNhan.thoiGianVaoVien &&
                moment(thongTinBenhNhan.thoiGianVaoVien).format("YYYY-MM-DD"),
              ngaySinh:
                thongTinBenhNhan.ngaySinh &&
                moment(thongTinBenhNhan.ngaySinh).format("YYYY-MM-DD"),
              doiTuongKcb: thongTinBenhNhan.doiTuongKcb,
            });
            data?.dsDichVu?.forEach((item) => {
              item.dsLoaiHinhThanhToan = listLoaiHinh?.filter(
                (x) => x.dichVuId === item.dichVuId
              );
              dsDichVu.push(item);
            });
          })
        );
        setState({
          dataSource: orderBy(dsDichVu, "index", "asc"),
          renderReady: true,
        });
      }
      fetchData();
    }
    setState({ dataSource, renderReady: true });
  }, [listDichVuTiepDon, thongTinBenhNhan]);

  useImperativeHandle(ref, () => ({
    onXoaNhieuDichVu,
    onSuaNgayThucHienDichVu,
  }));

  const onChangePage = (page) => {
    searchDichVuTiepDon({
      page: page - 1,
      nbDotDieuTriId: nbDotDieuTriId,
      size: size,
    });
  };

  const handleSizeChange = (size) => {
    onSizeChange({ size: size, nbDotDieuTriId: nbDotDieuTriId });
  };

  const onPrintChiDinh = (data) => () => {
    showLoading();
    inPhieu({
      nbDotDieuTriId: data.nbDotDieuTriId,
      loaiDichVu: data.loaiDichVu,
      dsChiDinhTuLoaiDichVu: data.chiDinhTuLoaiDichVu,
      chiDinhTuDichVuId: data.chiDinhTuDichVuId,
      dsNbDichVuId: data.id,
    }).finally(() => hideLoading());
  };

  const onChageInput = (key, index) => (e) => {
    let value = "";
    if (e?.target) {
      if (e?.target?.hasOwnProperty("checked")) value = e?.target?.checked;
      else value = e?.target?.value;
    } else if (e?._d) value = e._d;
    else value = e;
    state.dataSource[index][key] = value;
    if (key === "tuTra" && value) {
      state.dataSource[index]["khongTinhTien"] = false;
    }

    if (key === "khongTinhTien" && value) {
      state.dataSource[index]["tuTra"] = false;
    }
    setState({ dataSource: [...state.dataSource] });
    onSetData(state.dataSource);
  };

  const onDelete = (data) => () => {
    showConfirm(
      {
        title: t("tiepDon.xacNhanXoaDv"),
        content: `${t("tiepDon.banCoChacChanMuonXoaDichVu")}`,
        cancelText: t("common.huy"),
        okText: t("common.xacNhan"),
        classNameOkText: "button-error",
        showBtnOk: true,
        typeModal: "error",
      },
      () => {
        showLoading();
        onDeleteDichVu({ id: data.id, loaiDichVu: data.loaiDichVu })
          .then(() => {
            const params = { size: 50, nbDotDieuTriId };
            if (!isNBKhamSucKhoe)
              params.dsChiDinhTuLoaiDichVu = [200, 230, 240];
            onSizeChange(params);
          })
          .finally(() => hideLoading());
      }
    );
  };

  const onXoaNhieuDichVu = () => {
    if (isArray(state.selectedRowKeys, true)) {
      showConfirm(
        {
          title: t("tiepDon.xacNhanXoaDv"),
          content: `${t("tiepDon.banCoChacMuonXoaDv")}`,
          cancelText: t("common.huy"),
          okText: t("common.xacNhan"),
          classNameOkText: "button-error",
          showBtnOk: true,
          typeModal: "error",
        },
        () => {
          let listDv = state.selectedRowKeys.map((item) => ({
            id: Number(item.split("-")[0]),
            loaiDichVu: Number(item.split("-")[2]),
          }));
          listDv = groupBy(listDv, "loaiDichVu");
          showLoading();
          Promise.allSettled(
            Object.entries(listDv).map(([key, value]) => {
              return onDeleteDichVu({
                loaiDichVu: key,
                listDeletingId: (value || []).map((i) => i.id),
              });
            })
          )
            .then(() => {
              const params = { size: 50, nbDotDieuTriId };
              if (!isNBKhamSucKhoe)
                params.dsChiDinhTuLoaiDichVu = [200, 230, 240];
              setState({ selectedRowKeys: [] });
              onSizeChange(params);
            })
            .finally(() => {
              hideLoading();
            });
        }
      );
    } else {
      message.error(t("tiepDon.vuiLongChonItNhatMotDvDeXoa"));
    }
  };

  const onCheckIn = (data) => () => {
    showLoading();
    onCheckInDichVu({
      maHoSo: data?.maHoSo,
      phongThucHienId: data?.phongThucHienId,
      loaiDichVu: data.loaiDichVu,
    })
      .then(() => {
        const params = { size: 50, nbDotDieuTriId };
        if (!isNBKhamSucKhoe) params.dsChiDinhTuLoaiDichVu = [200, 230, 240];
        onSizeChange(params);
      })
      .finally(() => hideLoading());
  };

  const onHoanDv = (record) => {
    let gender = gioiTinh ? GIOI_TINH_BY_VALUE[gioiTinh] : "";

    let age =
      thangTuoi > 36 || tuoi
        ? `${tuoi} ${t("common.tuoi")}`
        : `${thangTuoi} ${t("common.thang")}`;
    const data = state.dataSource;
    if (isArray(data, true)) {
      data.forEach((itemLoop) => {
        itemLoop.gioiTinh = gender;
        itemLoop.tuoi = age;
      });
      refModalHoanDichVu.current &&
        refModalHoanDichVu.current.show(
          {
            data: data,
            selectedRowKeys: [record?.id],
          },
          () => {
            const params = { size: 50, nbDotDieuTriId };
            if (!isNBKhamSucKhoe) {
              params.dsChiDinhTuLoaiDichVu = [200, 230, 240];
            }
            onSizeChange(params);
          }
        );
    } else {
      message.error(t("khamBenh.chiDinh.khongCoDichVuThoaManDieuKienDeHoan"));
    }
  };

  const onHuyHoan = (data) => {
    if (refModalHuyHoanDichVu.current)
      refModalHuyHoanDichVu.current.show(
        { data: [data], nbDotDieuTriId },
        () => {
          const params = { size: 50, nbDotDieuTriId };
          if (!isNBKhamSucKhoe) {
            params.dsChiDinhTuLoaiDichVu = [200, 230, 240];
          }
          onSizeChange(params);
        }
      );
  };

  const isCheckedAll = useMemo(() => {
    return (
      state.dataSource?.length &&
      state.dataSource
        .map((item) => `${item.id}-${item.tenNb}-${item.loaiDichVu}`)
        .every((i) => state.selectedRowKeys.includes(i))
    );
  }, [state.dataSource, state.selectedRowKeys]);

  const oncheckAll = (e) => {
    let selectedRowKeys = e.target?.checked
      ? (state.dataSource || []).map(
          (x) => `${x.id}-${x.tenNb}-${x.loaiDichVu}`
        )
      : [];
    // onSetData({ dsId: selectedRowKeys });
    setState({
      selectedRowKeys: selectedRowKeys,
      selectedData: state.dataSource,
    });
  };

  const onSelectChange = (selectedRowKeys, data) => {
    const _selectedRowKeys = [...new Set(selectedRowKeys)];
    const _selectedData = [...new Set(data)];
    // onSetData({ dsId: _selectedRowKeys });
    setState({
      selectedRowKeys: _selectedRowKeys,
      selectedData: _selectedData,
    });
  };

  const onSuaNgayThucHienDichVu = (values) => {
    const data = (state.selectedData || []).map((item) => {
      return {
        id: Number(item.id),
        nbDichVu: {
          thoiGianThucHien:
            values?.thoiGian &&
            moment(values?.thoiGian).format("YYYY-MM-DD HH:mm:ss"),
          loaiDichVu: item?.loaiDichVu,
        },
      };
    });
    showLoading();
    thayDoiThongTinDichVu({
      data: data,
    })
      .then(() => {
        onSizeChange({
          size: 10,
          nbDotDieuTriId: nbDotDieuTriId,
          dsChiDinhTuLoaiDichVu: [
            LOAI_DICH_VU.TIEP_DON,
            LOAI_DICH_VU.DAT_KHAM,
            LOAI_DICH_VU.CRM,
          ],
        });
      })
      .finally(() => hideLoading());
  };

  const onDayKetQuaSangBVE = (record) => {
    ngoaiVienProvider.guiKetQuaCdhaTdcn([record.id]).then(() => {
      message.success(t("tiepDon.guiKetQuaSangBveThanhCong"));
    });
  };

  const rowSelection = {
    columnTitle: (
      <HeaderSearch
        title={
          <Checkbox
            style={{ color: "#03317c" }}
            onChange={oncheckAll}
            checked={isCheckedAll}
          />
        }
      />
    ),
    columnWidth: 40,
    onChange: onSelectChange,
    selectedRowKeys: state.selectedRowKeys,
  };

  const onDoiDV = (record) => () => {
    getNbDvKham({
      dichVuId: record?.id,
      chuyenTrangThai: false,
    }).then(() => {
      refDoiDVPhongTH.current &&
        refDoiDVPhongTH.current.show(
          {
            dichVuCu: record?.tenDichVu || "",
            tenPhongThucHien: record?.tenPhongThucHien || "",
            phongThucHienId: record?.phongThucHienId || null,
          },
          () => {
            //reload về bệnh nhân đầu tiên của phòng khám
            setTimeout(() => {
              window.location.reload();
            }, 500);
          }
        );
    });
  };

  const columns = [
    {
      title: <HeaderSearch title={t("common.stt")} />,
      width: "40px",
      dataIndex: "index",
      key: "index",
      align: "center",
      ignore: true,
    },
    {
      title: <HeaderSearch title={t("common.maDv")} />,
      width: "100px",
      dataIndex: "maDichVu",
      key: "maDichVu",
      show: true,
      i18Name: "common.maDv",
    },
    {
      title: <HeaderSearch title={t("common.tenDichVu")} />,
      width: "200px",
      dataIndex: "tenDichVu",
      key: "tenDichVu",
      show: true,
      i18Name: "common.tenDichVu",
      render: (item, data, index) => {
        if (isEdit) {
          return (
            <Select
              data={[item]}
              value={item}
              onClick={onDoiDV(data)}
              open={false}
            />
          );
        } else {
          return item;
        }
      },
    },
    {
      title: <HeaderSearch title={t("common.trangThai")} />,
      width: "120px",
      dataIndex: "trangThai",
      key: "trangThai",
      align: "center",
      show: true,
      i18Name: "common.trangThai",
      render: (item) => {
        return (listTrangThaiDichVu || []).find((x) => x.id === item)?.ten;
      },
    },
    {
      title: <HeaderSearch title={t("tiepDon.bsKham")} />,
      width: 200,
      dataIndex: "tenBacSiKham",
      key: "tenBacSiKham",
      show: true,
      i18Name: "tiepDon.bsKham",
      render: (item, data, index) => {
        if (data.loaiDichVu === LOAI_DICH_VU.KHAM && isEdit) {
          return (
            <Select
              data={listBacSi}
              onChange={onChageInput("bacSiKhamId", index)}
              value={data.bacSiKhamId}
              dropdownMatchSelectWidth={350}
            />
          );
        } else {
          if (!data.bacSiKhamId) return null;
          const record = getNhanVien(data.bacSiKhamId);
          return record?.ten ?? null;
        }
      },
    },
    {
      title: <HeaderSearch title={t("baoCao.nguoiChiDinh")} />,
      width: 200,
      dataIndex: "tenBacSiChiDinh",
      key: "tenBacSiChiDinh",
      i18Name: "baoCao.nguoiChiDinh",
      show: true,
      render: (item, data, index) => {
        if (isEdit) {
          return (
            <Select
              data={listAllNhanVien}
              onChange={onChageInput("bacSiChiDinhId", index)}
              value={data.bacSiChiDinhId}
              dropdownMatchSelectWidth={350}
            />
          );
        } else {
          if (!data.bacSiChiDinhId) return null;
          const record = getNhanVien(data.bacSiChiDinhId);

          return record?.ten ?? null;
        }
      },
    },
    {
      title: <HeaderSearch title={t("tiepDon.loaiHinhThanhToan")} />,
      width: 150,
      dataIndex: "tenLoaiHinhThanhToan",
      key: "tenLoaiHinhThanhToan",
      show: true,
      i18Name: "tiepDon.loaiHinhThanhToan",
      render: (item, data, index) => {
        const listData = (data?.dsLoaiHinhThanhToan || []).map((item) => ({
          id: item.loaiHinhThanhToanId,
          ten: item.tenLoaiHinhThanhToan,
        }));

        //Nếu có quyền 0500120 - Chỉnh sửa liên quan đến tiền/ xóa DV khi đã tạo QR thanh toán => được chỉnh sửa nếu phiếu chưa thanh toán
        if (
          isEdit &&
          (data.thanhToan === TRANG_THAI_PHIEU_THU_THANH_TOAN.CHUA_THANH_TOAN ||
            (checkRole([ROLES["KHAM_BENH"].SUA_LOAI_HINH_THANH_TOAN]) &&
              data.thanhToan != TRANG_THAI_PHIEU_THU_THANH_TOAN.DA_THANH_TOAN))
        ) {
          return (
            <Select
              data={listData}
              onChange={onChageInput("loaiHinhThanhToanId", index)}
              value={data.loaiHinhThanhToanId}
            />
          );
        } else {
          return item;
        }
      },
    },
    {
      title: <HeaderSearch title={t("common.ngayThucHien")} />,
      width: 200,
      dataIndex: "thoiGianThucHien",
      key: "thoiGianThucHien",
      show: true,
      i18Name: "common.ngayThucHien",
      align: "center",
      render: (item, data, index) => {
        if (isEdit && data.trangThai < 150) {
          return (
            <DateTimePicker
              onChange={onChageInput("thoiGianThucHien", index)}
              value={moment(item)}
              format={"DD-MM-YYYY HH:mm:ss"}
              showTime
            ></DateTimePicker>
          );
        } else {
          return item && moment(item).format("DD-MM-YYYY HH:mm:ss");
        }
      },
    },
    {
      title: <HeaderSearch title={t("tiepDon.phongThucHien")} />,
      width: 150,
      dataIndex: "tenPhongThucHien",
      key: "tenPhongThucHien",
      show: true,
      i18Name: "tiepDon.phongThucHien",
      render: (item, data, index) => {
        if (isEdit) {
          const dsPhongThucHien = uniqBy(
            data.dsPhongThucHien || [],
            "phongId"
          ).map((item, index) => {
            return { id: item.phongId, ten: item.ten };
          });
          return (
            <Select
              data={dsPhongThucHien}
              onChange={onChageInput("phongThucHienId", index)}
              value={data.phongThucHienId}
              dropdownMatchSelectWidth={350}
            />
          );
        } else {
          return item;
        }
      },
    },
    {
      title: <HeaderSearch title={t("tiepDon.ttThanhToan")} />,
      width: "80px",
      dataIndex: "thanhToan",
      key: "thanhToan",
      align: "center",
      show: true,
      i18Name: "tiepDon.ttThanhToan",
      render: (item) => {
        return (
          <Checkbox
            checked={item === TRANG_THAI_PHIEU_THU_THANH_TOAN.DA_THANH_TOAN}
            disabled
          ></Checkbox>
        );
      },
    },
    {
      title: <HeaderSearch title={t("tiepDon.ttHoan")} />,
      width: "80px",
      dataIndex: "trangThaiHoan",
      key: "trangThaiHoan",
      show: true,
      i18Name: "tiepDon.ttHoan",
      render: (item) => {
        return (listTrangThaiHoan || []).find((x) => x.id === item)?.ten;
      },
    },
    {
      title: <HeaderSearch title={t("tiepDon.khoaChiDinh")} />,
      width: "160px",
      dataIndex: "khoaChiDinhId",
      key: "khoaChiDinhId",
      show: true,
      i18Name: "tiepDon.khoaChiDinh",
      render: (item) => {
        return (listAllKhoa || []).find((x) => x.id === item)?.ten;
      },
    },
    {
      title: <HeaderSearch title={t("common.soLuong")} />,
      width: 100,
      dataIndex: "soLuong",
      key: "soLuong",
      show: true,
      i18Name: "common.soLuong",
      align: "center",
    },
    {
      title: <HeaderSearch title={t("common.thanhTien")} />,
      width: 100,
      dataIndex: "thanhTien",
      key: "thanhTien",
      show: true,
      i18Name: "common.thanhTien",
      align: "right",
      render: (item) => {
        return item ? (item + "").formatPrice() : "";
      },
    },
    {
      title: <HeaderSearch title={t("common.tuTra")} />,
      width: 70,
      dataIndex: "tuTra",
      key: "tuTra",
      align: "center",
      show: true,
      i18Name: "common.tuTra",
      hidden: dataAN_CHECKBOX_TU_TRA?.eval() && isEdit,
      render: (item, data, index) => {
        if (
          isEdit &&
          data.thanhToan === TRANG_THAI_PHIEU_THU_THANH_TOAN.CHUA_THANH_TOAN
        ) {
          return (
            <Checkbox
              checked={item}
              onChange={onChageInput("tuTra", index)}
            ></Checkbox>
          );
        } else {
          return <Checkbox checked={item} disabled></Checkbox>;
        }
      },
    },
    {
      title: <HeaderSearch title={t("tiepDon.khongTinhTien")} />,
      width: 90,
      dataIndex: "khongTinhTien",
      key: "khongTinhTien",
      align: "center",
      show: true,
      i18Name: "tiepDon.khongTinhTien",
      render: (item, data, index) => {
        if (
          isEdit &&
          data.thanhToan === TRANG_THAI_PHIEU_THU_THANH_TOAN.CHUA_THANH_TOAN
        ) {
          return (
            <Checkbox
              checked={item}
              onChange={onChageInput("khongTinhTien", index)}
            ></Checkbox>
          );
        } else {
          return <Checkbox checked={item} disabled></Checkbox>;
        }
      },
    },
    {
      title: <HeaderSearch title={t("tiepDon.thanhToanSau")} />,
      width: 90,
      dataIndex: "thanhToanSau",
      key: "thanhToanSau",
      align: "center",
      show: true,
      i18Name: "tiepDon.thanhToanSau",
      render: (item) => {
        return <Checkbox checked={item} disabled></Checkbox>;
      },
    },
    {
      title: <HeaderSearch title={t("tiepDon.daCheckin")} />,
      width: 90,
      dataIndex: "daCheckin",
      key: "daCheckin",
      align: "center",
      show: true,
      i18Name: "tiepDon.daCheckin",
      render: (item, data) => {
        const checked = TRANG_THAI_DICH_VU.DA_CHECKIN_DICH_VU.includes(
          data.trangThai
        );
        return <Checkbox checked={checked} disabled></Checkbox>;
      },
    },
    {
      title: <HeaderSearch title={t("hsba.nbKhongThucHien")} />,
      width: 90,
      dataIndex: "khongThucHien",
      key: "khongThucHien",
      align: "center",
      show: true,
      i18Name: "hsba.nbKhongThucHien",
      hidden: dataAN_CHECKBOX_KHONG_THUC_HIEN_DVKT?.eval() && isEdit,
      render: (item, record, index) => {
        const disable = isEdit
          ? ![15, 20, 25, 35, 40, 43, 50, 63].includes(record.trangThai)
          : true;
        return (
          <Checkbox
            checked={!!item}
            disabled={disable}
            onChange={(e) => {
              if (!disable) {
                onChageInput("khongThucHien", index)(e);
              }
            }}
          ></Checkbox>
        );
      },
    },
    {
      title: <HeaderSearch title={t("hsba.lyDoNguoiBenhKhongThucHien")} />,
      width: 200,
      dataIndex: "lyDoKhongThucHien",
      key: "lyDoKhongThucHien",
      align: "center",
      show: true,
      i18Name: "hsba.lyDoNguoiBenhKhongThucHien",
      render: (item, record, index) => {
        const isEditable = isEdit ? record.khongThucHien : false;
        return !isEditable ? (
          item
        ) : (
          <Input.TextArea
            className="input-option"
            placeholder={t("khoMau.vuiLongNhapLyDo")}
            rows={2}
            value={item}
            onChange={onChageInput("lyDoKhongThucHien", index)}
          />
        );
      },
    },
    {
      title: <HeaderSearch title={t("hsba.ngayXacNhanKhongThucHien")} />,
      width: 150,
      dataIndex: "thoiGianXacNhanKhongThucHien",
      key: "thoiGianXacNhanKhongThucHien",
      align: "center",
      show: true,
      i18Name: "hsba.ngayXacNhanKhongThucHien",
      render: (item) => {
        return item && moment(item).format("DD/MM/YYYY HH:mm:ss");
      },
    },
    {
      title: <HeaderSearch title={t("tiepDon.thanhToanKsk")} />,
      width: 100,
      dataIndex: "hinhThucTtKsk",
      key: "hinhThucTtKsk",
      align: "center",
      show: true,
      i18Name: "tiepDon.thanhToanKsk",
      hidden: !isNBKhamSucKhoe,
      render: (item, data) => {
        return listhinhThucTtKsk.find((x) => x.id === item)?.ten;
      },
    },
    {
      title: <HeaderSearch title={t("tiepDon.maGoiKsk")} />,
      width: 100,
      dataIndex: "maBoChiDinh",
      key: "maBoChiDinh",
      align: "center",
      show: true,
      i18Name: "tiepDon.maGoiKsk",
    },
    {
      title: <HeaderSearch title={t("tiepDon.tenGoiKsk")} />,
      width: 120,
      dataIndex: "tenBoChiDinh",
      key: "tenBoChiDinh",
      align: "center",
      show: true,
      i18Name: "tiepDon.tenGoiKsk",
    },
    {
      title: (
        <HeaderSearch
          title={
            <>
              {t("common.khac")} <Setting refTable={refSettings} />
            </>
          }
        />
      ),
      width: 150,
      dataIndex: "action",
      key: "action",
      align: "center",
      fixed: "right",
      ignore: true,
      colSpan: 1,
      render: (_, record) => {
        return (
          <div className="col-action">
            {checkRole([
              ROLES["TIEP_DON"].HIEN_THI_BUTTON_DAY_KET_QUA_SANG_BVE,
            ]) &&
              record.trangThai >= 155 &&
              [20, 30].includes(record.loaiDichVu) && (
                <Tooltip
                  title={t("tiepDon.dayKetQuaSangBve")}
                  placement="bottom"
                >
                  <SVG.IcLogin
                    onClick={() => onDayKetQuaSangBVE(record)}
                    className="ic-action"
                  />
                </Tooltip>
              )}

            <Tooltip title={t("common.inPhieu")} placement="bottom">
              <SVG.IcPrint
                onClick={onPrintChiDinh(record)}
                className="ic-action"
              />
            </Tooltip>
            <Tooltip
              title={t("khamBenh.chiDinh.hoanDichVu")}
              placement="bottom"
            >
              <div className="btn-delete">
                {record.thanhToan ===
                  TRANG_THAI_PHIEU_THU_THANH_TOAN.DA_THANH_TOAN &&
                  record.trangThaiHoan === 0 &&
                  ![LOAI_DICH_VU.THUOC, LOAI_DICH_VU.VAT_TU].includes(
                    record.loaiDichVu
                  ) && (
                    <SVG.IcHoanDv
                      onClick={() => onHoanDv(record)}
                      className="ic-action"
                    />
                  )}
              </div>
            </Tooltip>
            <Tooltip
              title={t("khamBenh.chiDinh.huyYeuCauHoan")}
              placement="bottom"
            >
              {record.trangThaiHoan === 10 && (
                <div className="btn-huyHoan">
                  <SVG.IcHuyHoanDv
                    onClick={() => onHuyHoan(record)}
                    className="ic-action"
                  />
                </div>
              )}
            </Tooltip>
            <Tooltip title={t("tiepDon.xoaDichVu")} placement="bottom">
              <SVG.IcDelete onClick={onDelete(record)} className="ic-action" />
            </Tooltip>
            <Tooltip title={t("tiepDon.checkInDichVu")} placement="bottom">
              <SVG.IcSuccess
                color={"var(--color-green-primary)"}
                onClick={onCheckIn(record)}
                className="ic-action"
              />
            </Tooltip>
          </div>
        );
      },
    },
  ];

  const onChangeRadio = (e) => {
    //setting hiển thị dịch vụ theo khu vực. Giá trị 1: tất cả dịch vụ, 2: theo khu vực
    setDataTuyChinhHienThi(e.target.value);
  };

  const content = (
    <div style={{ display: "grid" }}>
      {t("tiepDon.caiDatHienThiDichVu")}:
      <Radio.Group
        defaultValue={tuyChinhHienThi}
        style={{ display: "grid" }}
        onChange={onChangeRadio}
      >
        <Radio value={"1"}>{t("tiepDon.hienThiDichVuDuocKeTuTiepDon")}</Radio>
        <Radio value={"2"}>{t("tiepDon.hienThiTatCaDv")}</Radio>
      </Radio.Group>
    </div>
  );

  return (
    <Main noPadding={true}>
      <TableWrapper
        columns={columns}
        dataSource={state.dataSource}
        rowKey={(record) => `${record.id}-${record.tenNb}-${record.loaiDichVu}`}
        ref={refSettings}
        tableName="TABLE_DANH_SACH_DICH_VU_DA_TIEP_DON"
        scroll={{ x: 1500 }}
        content={content}
        columnResizable={true}
        loading={isLoading || !state.renderReady}
        {...(isEdit && {
          rowSelection,
        })}
      />
      {!!totalElements ? (
        <Pagination
          onChange={onChangePage}
          current={page + 1}
          pageSize={size}
          listData={listDichVuTiepDon}
          total={totalElements}
          onShowSizeChange={handleSizeChange}
        />
      ) : null}
      <ModalHoanDichVu ref={refModalHoanDichVu} />
      <ModalHuyHoanDichVu ref={refModalHuyHoanDichVu} />
      <ModalDoiDVPhongTH ref={refDoiDVPhongTH} />
    </Main>
  );
};

export default forwardRef(DanhSachDichVu);
