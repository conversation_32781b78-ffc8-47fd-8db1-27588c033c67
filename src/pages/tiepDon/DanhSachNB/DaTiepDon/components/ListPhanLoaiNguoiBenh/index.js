import React from "react";
import { useListAll } from "hooks";

const ListPhanLoaiNguoiBenh = ({ value, listAll, allTags }) => {
  if (!Array.isArray(value) || !value.length) return null;
  const [listAllPhanLoaiNB] = useListAll("phanLoaiNB", {}, true);

  const listPhanLoaiNb = value.map((id) => {
    const item = (listAll ?? listAllPhanLoaiNB).find((x) => x.id === id);
    return (
      item || {
        id,
        ten: "",
        mauChu: "",
        mauNen: "",
      }
    );
  });

  const shouldShowTitle = allTags && allTags.length > 2;
  const titleText = shouldShowTitle
    ? allTags.map((item) => item.ten).join(", ")
    : undefined;

  return (
    <div
      style={{
        display: "inline-flex",
        flexWrap: "wrap",
        gap: 4,
        maxWidth: "100%",
        overflow: "hidden",
      }}
    >
      {listPhanLoaiNb.map((item) => (
        <span
          key={item.id}
          style={{
            color: item?.mauChu,
            backgroundColor: item?.mauNen,
            padding: "0 4px",
            margin: "0 4px",
            borderRadius: "2px",
            fontSize: "12px",
            whiteSpace: "nowrap",
            display: "inline-block",
          }}
          title={titleText}
        >
          {item.ten}
        </span>
      ))}
    </div>
  );
};

export default ListPhanLoaiNguoiBenh;
