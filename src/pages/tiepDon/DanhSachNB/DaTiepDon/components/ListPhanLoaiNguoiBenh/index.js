import React from "react";
import { Tag } from "antd";
import { useListAll } from "hooks";

const ListPhanLoaiNguoiBenh = ({ value, listAll }) => {
  if (!Array.isArray(value) || !value.length) return null;
  const [listAllPhanLoaiNB] = useListAll("phanLoaiNB", {}, true);

  const listPhanLoaiNb = value.map((id) => {
    const item = (listAll ?? listAllPhanLoaiNB).find((x) => x.id === id);
    return (
      item || {
        id,
        ten: "",
        mauChu: "",
        mauNen: "",
      }
    );
  });

  return (
    <div
      style={{
        display: "inline-flex",
        flexWrap: "wrap",
        gap: 4,
        maxWidth: "100%",
        overflow: "hidden", // Ẩn phần tràn
      }}
    >
      {listPhanLoaiNb.map((item) => (
        <Tag
          key={item.id}
          style={{
            color: item?.mau<PERSON>hu,
            backgroundColor: item?.mauNen,
            borderColor: item?.mauNen ? "transparent" : undefined,
            marginInlineEnd: 0,
            fontSize: 12,
            padding: "0 3px",
            lineHeight: "20px",
            borderRadius: "2px",
            whiteSpace: "nowrap", // Không cho text xuống dòng
            textOverflow: "ellipsis", // Hiển thị ... khi text quá dài
            maxWidth: "100px", // Giới hạn chiều rộng của mỗi tag
            overflow: "hidden",
          }}
        >
          {item.ten}
        </Tag>
      ))}
    </div>
  );
};

export default ListPhanLoaiNguoiBenh;
