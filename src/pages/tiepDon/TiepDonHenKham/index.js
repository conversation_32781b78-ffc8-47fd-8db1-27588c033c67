import React, { useEffect } from "react";
import { useDispatch } from "react-redux";
import { useParams, useHistory, useLocation } from "react-router-dom";
import { useLoading, useStore } from "hooks";
import FormInfo from "../TiepDon/FormInfo";
import nbDotDieuTriProvider from "data-access/nb-dot-dieu-tri-provider";
import { PHAN_LOAI_DOI_TUONG, LOAI_LICH_HEN_KHAM } from "constants";
import { DEFAULT_THONG_TIN_CHUNG } from "pages/application/TuyChinhGiaoDienPhamMem/TiepDon/config";
import { useKeDichVuKham } from "../KeDichVuKham";

const TiepDonHenKham = (props) => {
  const { gotoKeDichVuTiepDon } = useKeDichVuKham();
  const initTiepDonFinish = useStore("tiepDon.initTiepDonFinish", false);

  const { showLoading, hideLoading } = useLoading();
  const location = useLocation();
  const { nbDotDieuTriId } = useParams();

  const thongTinChung = useStore(
    "thietLap.thietLapGiaoDien.tiepDon.thongTinChung",
    DEFAULT_THONG_TIN_CHUNG
  );
  const {
    tiepDonDichVu: { themDichVu, tiepDonLichHenKham },
    tiepDon: { updateDetail },
    nbDotDieuTri: { getById },
  } = useDispatch();

  useEffect(() => {
    if (nbDotDieuTriId && initTiepDonFinish) {
      nbDotDieuTriProvider.getById(nbDotDieuTriId).then((s) => {
        if (s.code === 0) {
          let dataUpdate = {
            tenNb: s?.data?.tenNb,
            soDienThoai: s?.data?.soDienThoai,
            ngaySinh: s?.data?.ngaySinh,
            gioiTinh: s?.data?.gioiTinh,
            nbDiaChi: s?.data?.nbDiaChi,
            nbThongTinId: s.data?.nbThongTinId,
            quocTichId: s?.data?.quocTichId,
            dsPhanLoaiNbId: s?.data?.dsPhanLoaiNbId,
            bangLaiXeId: s?.data?.bangLaiXeId,
            email: s?.data?.email,
            nbGiayToTuyThan: s?.data?.nbGiayToTuyThan,
            danTocId: s?.data?.danTocId,
            soBaoHiemXaHoi: s?.data?.soBaoHiemXaHoi,
            ngheNghiepId: s?.data?.ngheNghiepId,
            nbNguoiBaoLanh: s?.data?.nbNguoiBaoLanh,
            chiNamSinh: s?.data?.chiNamSinh,
            anhDaiDien: s?.data?.anhDaiDien,
            doiTuongCu: s?.data?.doiTuong,
            doiTuong: s?.data?.doiTuong,
            loaiDoiTuong: s?.data?.loaiDoiTuong,
            maNb: s?.data?.maNb,
            doiTuongKcbCu: s?.data?.doiTuongKcb,
            maBenhAn: s?.data?.maBenhAn,
            ...((thongTinChung.layout1 || []).includes("phanLoaiDoiTuong")
              ? {
                  phanLoaiDoiTuong: location?.state?.record?.loai
                    ? [
                        LOAI_LICH_HEN_KHAM.HEN_DIEU_TRI_NGOAI_TRU,
                        LOAI_LICH_HEN_KHAM.HEN_KHAM_CMU,
                      ].includes(location?.state?.record?.loai)
                      ? PHAN_LOAI_DOI_TUONG.DIEU_TRI_NGOAI_TRU
                      : [LOAI_LICH_HEN_KHAM.HEN_TAI_KHAM].includes(
                          location?.state?.record?.loai
                        )
                      ? PHAN_LOAI_DOI_TUONG.TAI_KHAM
                      : null
                    : null,
                }
              : {}), //nếu có thiết lập giao diện hiển thị Phân loại đối tượng => set giá trị phanLoaiDoiTuong
            nbTheBaoHiem: {
              maThe: s?.data?.nbTheBaoHiem?.maThe,
              mucHuong: s?.data?.nbTheBaoHiem?.mucHuong,
              tuNgay: s?.data?.nbTheBaoHiem?.tuNgay,
              denNgay: s?.data?.nbTheBaoHiem?.denNgay,
              noiDangKyId: s?.data?.nbTheBaoHiem?.noiDangKyId,
              noiGioiThieuId: s?.data?.nbTheBaoHiem?.noiGioiThieuId,
              giayChuyen1Nam: s?.data?.nbTheBaoHiem?.giayChuyen1Nam,
              denNgayGiayChuyen: s?.data?.nbTheBaoHiem?.denNgayGiayChuyen,
            },
            nbNgoaiVien: { maNb: s?.data?.nbNgoaiVien?.maNb },
            nbNguonNb: {
              ghiChu: s?.data?.nbNguonNb?.ghiChu,
            },
            congTyBaoHiemId: s?.data?.congTyBaoHiemId,
            loaiDoiTuongId: s?.data?.loaiDoiTuongId,
            khoaHenKhamId:
              location?.state?.record?.loai ===
              LOAI_LICH_HEN_KHAM.HEN_DIEU_TRI_NGOAI_TRU
                ? location?.state?.record?.khoaHenKhamId
                : undefined,
            nbTongKetRaVien: {
              cdNoiGioiThieu: s?.data?.nbTongKetRaVien?.cdNoiGioiThieu,
              lyDoDenKham: s?.data?.nbTongKetRaVien?.lyDoDenKham,
            },
          };
          updateDetail(dataUpdate);
        }
      });
    }
  }, [nbDotDieuTriId, initTiepDonFinish]);

  const onTiepDonHenKham = async (id) => {
    try {
      showLoading();
      const data = location?.state?.record;
      await tiepDonLichHenKham([id]);
      if (data && (data.dsDichVu || []).length > 0) {
        //get thông tin NB để lấy các param lúc kê dịch vụ
        await getById(id);
        await themDichVu({
          dsDichVu: (data.dsDichVu || []).map((item) => ({
            ...item,
            dichVuId: item.id,
          })),
          nbDotDieuTriId: id,
        });
      }
    } finally {
      hideLoading();
      gotoKeDichVuTiepDon(id, true);
    }
  };

  if (!location?.state?.nbDotDieuTriId) {
    window.location.href = "/quan-ly-tiep-don/danh-sach-lich-hen";
  }

  return <FormInfo isCheckAge={true} onOk={onTiepDonHenKham} visible={true} />;
};

export default TiepDonHenKham;
