import React, {
  memo,
  useState,
  useEffect,
  useRef,
  useMemo,
  useCallback,
  forwardRef,
  useImperativeHandle,
} from "react";
import {
  DOI_TUONG,
  ENUM,
  GIOI_TINH_BY_VALUE,
  LOAI_DICH_VU,
  LOAI_GIA,
  ROLES,
  THIET_LAP_CHUNG,
  TRANG_THAI_DICH_VU,
  TRANG_THAI_PHIEU_THU_THANH_TOAN,
} from "constants/index";
import { Main, GlobalStyle } from "./styled";
import { useDispatch } from "react-redux";
import { InputNumber, message } from "antd";
import { useTranslation } from "react-i18next";
import { useParams } from "react-router-dom";
import moment from "moment";
import ModalDichVuBvE from "../ModalDichVuBvE";
import {
  Tooltip,
  Button,
  Pagination,
  Select,
  TableWrapper,
  HeaderSearch,
  Checkbox,
  InputTimeout,
  SelectLargeData,
  DateTimePicker,
} from "components";
import ModalHoanDichVu from "components/ModalHoanDichVu";
import ModalHuyHoanDichVu from "components/ModalHuyHoanDichVu";
import {
  useConfirm,
  useEnum,
  useListAll,
  useRefFunc,
  useStore,
  useThietLap,
} from "hooks";
import { checkRole } from "lib-utils/role-utils";
import { groupBy, cloneDeep, isEmpty, uniqBy, debounce } from "lodash";
import Box2 from "pages/tiepDon/components/Box2";
import ModalDoiDichVu from "../ModalDoiDichVu";
import { SVG } from "assets";
import stringUtils from "mainam-react-native-string-utils";
import { toSafePromise } from "lib-utils";
import { safeConvertToArray, isArray } from "utils/index";

const { Setting } = TableWrapper;

const { SelectChanDoan } = SelectLargeData;

const CustomCheckbox = forwardRef((props, ref) => {
  const [checked, setChecked] = useState(props.checked || false);

  // Cho phép parent gọi hàm refresh
  useImperativeHandle(ref, () => ({
    refresh() {
      setChecked(props.checked); // trigger re-render
    },
  }));

  useEffect(() => {
    setChecked(props.checked);
  }, [props.checked]);

  return (
    <Checkbox
      {...props}
      onChange={(e) => {
        setChecked(e?.target?.checked);
        props.onChange?.(e);
      }}
      checked={checked}
    />
  );
});

const checkChuaThanhToan = (data) => {
  return [
    TRANG_THAI_PHIEU_THU_THANH_TOAN.CHUA_THANH_TOAN,
    false,
    TRANG_THAI_PHIEU_THU_THANH_TOAN.CHUA_THANH_TOAN_TAO_QR,
  ].includes(data);
};

const checkDaThanhToan = (data) => {
  return [TRANG_THAI_PHIEU_THU_THANH_TOAN.DA_THANH_TOAN, true].includes(data);
};

const getDonGiaByLoai = (loaiHinhThanhToan) => {
  let donGia = 0;
  if (loaiHinhThanhToan.loaiGia === LOAI_GIA.GIA_BAO_HIEM) {
    donGia = loaiHinhThanhToan.giaBaoHiem;
  } else if (loaiHinhThanhToan.loaiGia === LOAI_GIA.GIA_KHONG_BAO_HIEM) {
    donGia = loaiHinhThanhToan.giaKhongBaoHiem;
  } else if (loaiHinhThanhToan.loaiGia === LOAI_GIA.GIA_PHU_THU) {
    donGia = loaiHinhThanhToan.giaPhuThu;
  }
  return donGia;
};

const getDonGia = (data, doiTuong) => {
  let giaBaoHiem = data.giaBaoHiem;
  let giaKhongBaoHiem = data.giaKhongBaoHiem;
  let loaiHinhThanhToan;
  if (isArray(data.dsLoaiHinhThanhToan, true) && data.loaiHinhThanhToanId) {
    loaiHinhThanhToan = data.dsLoaiHinhThanhToan.find(
      (x) => x.loaiHinhThanhToanId === data.loaiHinhThanhToanId
    );
    if (loaiHinhThanhToan) {
      giaBaoHiem = loaiHinhThanhToan.giaBaoHiem;
      giaKhongBaoHiem = loaiHinhThanhToan.giaKhongBaoHiem;
    }
  }
  if (loaiHinhThanhToan?.loaiGia) {
    return getDonGiaByLoai(loaiHinhThanhToan);
  }
  let donGia = doiTuong === DOI_TUONG.BAO_HIEM ? giaBaoHiem : giaKhongBaoHiem;

  return donGia;
};

const DichVuDaChon = (props) => {
  const { showConfirm } = useConfirm();
  const { id } = useParams();
  const { t } = useTranslation();
  const refModalDichVuBvE = useRef(null);
  const refModalHoanDichVu = useRef(null);
  const refModalHuyHoanDichVu = useRef(null);
  const refCustomCheckbox = useRef({});

  const [state, _setState] = useState({
    dataSource: [],
    baseData: [],
    searchBenhPhamIds: {},
    updatedBaseData: new Date(),
  });
  const refSettings = useRef(null);
  const refModalDoiDichVu = useRef(null);
  const refFocusCount = useRef(0);
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  const { totalElements, page, size, listDvDaTiepDonOriginal } = useStore(
    "tiepDonDichVu",
    {},
    { fields: "totalElements, page, size, listDvDaTiepDonOriginal" }
  );

  const thongTinBenhNhan = useStore("nbDotDieuTri.thongTinBenhNhan", {});
  const {
    doiTuong,
    nbNgoaiVien,
    nbNguonNb,
    gioiTinh,
    tuoi,
    thangTuoi,
    doiTuongKcb,
  } = thongTinBenhNhan;
  const listDvKham = useStore("tiepDonDichVu.listDvKham", []);
  const listDvChoose = useStore("tiepDonDichVu.listDvChoose", []);
  const [listAllBenhPham] = useListAll("benhPham", {}, true);
  const [listAllNguonKhacChiTra] = useListAll("nguonKhac", {}, true);
  const dichVuHen = useStore("tiepDonDichVu.dichVuHen", null);

  const [listTrangThaiDichVu] = useEnum(ENUM.TRANG_THAI_DICH_VU);
  const [TIEP_DON_KE_DICH_VU_CON] = useThietLap(
    THIET_LAP_CHUNG.TIEP_DON_KE_DICH_VU_CON
  );
  const [dataKHONG_HIEN_THI_SO_LAN_SAU_TEN_DICH_VU] = useThietLap(
    THIET_LAP_CHUNG.KHONG_HIEN_THI_SO_LAN_SAU_TEN_DICH_VU
  );
  const [dataLAY_BAC_SI_CHI_DINH_LAY_THEO_TRUONG_BS_CHI_DINH] = useThietLap(
    THIET_LAP_CHUNG.LAY_BAC_SI_CHI_DINH_LAY_THEO_TRUONG_BS_CHI_DINH
  );
  const macDinhSoLuong1 = useThietLap(
    THIET_LAP_CHUNG.TIEP_DON_MAC_DINH_SO_LUONG_DV_KHAM_BANG_1_KHONG_CHO_SUA
  )[0]?.eval();

  const [dataLOAI_DV_THG_THUC_HIEN_THEO_THG_CHON_TIEP_DON] = useThietLap(
    THIET_LAP_CHUNG.LOAI_DV_THG_THUC_HIEN_THEO_THG_CHON_TIEP_DON
  );

  const dsLoaiDichVuLayThoiGianThucHien =
    dataLOAI_DV_THG_THUC_HIEN_THEO_THG_CHON_TIEP_DON
      ?.split(",")
      .map((x) => Number(x.trim()))
      .filter((n) => !isNaN(n));

  const {
    tiepDonDichVu: {
      thayDoiThongTinDichVuDaChon,
      onDeleteDichVu,
      tamTinhTien,
      tongTien,
    },
    phongThucHien: { getListPhongTheoDichVu },
    tiepDonDichVu: { onSearchNbDv, updateData },
    nhanVien: { getListNhanVienTongHop },
    loaiDoiTuongLoaiHinhTT: { getListLoaiDoiTuongTT },
    noiLayBenhPham: { getListNoiLayMau },
    khamBenh: { updateChanDoanDvKham },
  } = useDispatch();

  const debounceUpdateChanDoan = useCallback(
    debounce(updateChanDoanDvKham, 500),
    []
  );

  const isPermisionEditDv = checkRole([ROLES["TIEP_DON"].SUA_DV]);
  const isPermisionCheckKhongTinhTien = checkRole([
    ROLES["TIEP_DON"].CHECK_KHONG_TINH_TIEN_DV_DA_CHON,
  ]);
  const listDvDaTiepDon = useStore("tiepDonDichVu.listDvDaTiepDon", []);
  const listNhanVien = useStore("nhanVien.listNhanVien", []);
  const quayTiepDonId = useStore("goiSo.quayTiepDonId", null);
  const listAllQuayTiepDonTaiKhoan = useStore(
    "quayTiepDon.listAllQuayTiepDonTaiKhoan",
    []
  );
  const quayTiepDonTaiKhoan = listAllQuayTiepDonTaiKhoan?.find(
    (x) => x.id === quayTiepDonId
  );
  useEffect(() => {
    //listDvDaTiepDonOriginal thay đổi sẽ thực hiện gọi các api thay vì dùng listDvDaTiepDon
    if (isEmpty(thongTinBenhNhan)) return;
    let dvDaTiepDon = cloneDeep(listDvDaTiepDon);

    async function fetchData() {
      const [_, listPhong] = await toSafePromise(
        getListPhongTheoDichVu({
          page: "",
          size: "",
          dsDichVuId: dvDaTiepDon.map((item) => item.dichVuId),
          khoaChiDinhId: quayTiepDonTaiKhoan?.khoaId,
          doiTuongKcb: thongTinBenhNhan.doiTuongKcb,
        })
      );
      const phongByDichVuId = groupBy(listPhong || [], "dichVuId");
      dvDaTiepDon.forEach((dichVu) => {
        dichVu.dsPhongThucHien = uniqBy(
          phongByDichVuId[dichVu?.dichVuId] || [],
          "phongId"
        );
      });
      const dataFormatDate = dvDaTiepDon.map((item) => ({
        ...item,
        ngayThucHien:
          item.thoiGianThucHien &&
          moment(item.thoiGianThucHien).format("YYYY-MM-DD"),
      }));
      const dsDichVu = [];
      const dvByNgayThucHien = groupBy(dataFormatDate, "ngayThucHien");
      await Promise.all(
        Object.keys(dvByNgayThucHien).map(async (key) => {
          const data = dvByNgayThucHien[key];
          const listLoaiHinh = await getListLoaiDoiTuongTT({
            active: true,
            page: "",
            size: "",
            dsDichVuId: data.map((item) => item.dichVuId),
            loaiDoiTuongId: thongTinBenhNhan.loaiDoiTuongId,
            khoaChiDinhId: quayTiepDonTaiKhoan?.khoaId,
            ngayThucHien: data[0].ngayThucHien,
            ngaySinh:
              thongTinBenhNhan.ngaySinh &&
              moment(thongTinBenhNhan.ngaySinh).format("YYYY-MM-DD"),
            ngayVaoVien:
              thongTinBenhNhan.thoiGianVaoVien &&
              moment(thongTinBenhNhan.thoiGianVaoVien).format("YYYY-MM-DD"),
            doiTuongKcb: thongTinBenhNhan.doiTuongKcb,
          });
          data.forEach((item) => {
            item.dsLoaiHinhThanhToan = listLoaiHinh.sort(
              (a, b) => b.uuTien - a.uuTien
            );
            dsDichVu.push(item);
          });
        })
      );

      //lấy thông tin nơi lấy mẫu
      await Promise.all(
        dsDichVu.map(async (item) => {
          if (item?.loaiDichVu === LOAI_DICH_VU.XET_NGHIEM) {
            const resNoiLayMau = await getListNoiLayMau({
              khoaChiDinhId: quayTiepDonTaiKhoan?.khoaId,
              dsDoiTuongKcb: doiTuongKcb,
              dichVuId: item.dichVuId,
              nhomDichVuCap2Id: item.nhomDichVuCap2Id,
              nhomDichVuCap3Id: item.nhomDichVuCap3Id,
              dsLoaiDoiTuongId: thongTinBenhNhan.loaiDoiTuongId,
              loaiHinhThanhToanId: item.loaiHinhThanhToanId,
              thoiGianThucHien: moment().format(),
            });

            item.dsNoiLayMau = resNoiLayMau || [];
          }
        })
      );

      setState({ dvDaTiepDon: dsDichVu });
    }
    fetchData();
  }, [
    listDvDaTiepDonOriginal,
    quayTiepDonTaiKhoan?.khoaId,
    thongTinBenhNhan.loaiDoiTuongId,
  ]);

  useEffect(() => {
    getListNhanVienTongHop({
      dsMaThietLapVanBang: THIET_LAP_CHUNG.BAC_SI,
      page: "",
      size: "",
      active: true,
    });
  }, []);

  const firstNewData = useMemo(() => {
    return state.dataSource.find((item) => item.isNew);
  }, [state.dataSource]);

  useEffect(() => {
    let listCloneDv = cloneDeep(listDvChoose);

    listCloneDv.forEach((s) => {
      if (checkChuaThanhToan(s.thanhToan)) {
        const matchingItem = (listDvKham || []).find(
          (item) => item.dichVuId === s.dichVuId && s.phongId === item.phongId
        );

        if (matchingItem) {
          s.dsLoaiHinhThanhToan = matchingItem.dsLoaiHinhThanhToan;
          s.loaiHinhThanhToanId = s.dsLoaiHinhThanhToan?.length
            ? s.dsLoaiHinhThanhToan[0].loaiHinhThanhToanId
            : null;
        }
      }
    });

    // Combine services
    const data = [
      ...(listCloneDv?.length && page === 0 ? listCloneDv : []),
      ...(state.dvDaTiepDon || []),
    ];

    setState({ baseData: data });
  }, [state.dvDaTiepDon, listDvChoose, listDvKham]);

  const refBaseDataLength = useRef(0);
  const refTotalElements = useRef(0);

  useEffect(() => {
    if (
      refBaseDataLength.current !== state.baseData?.length ||
      refTotalElements.current != totalElements
    ) {
      //render lại ds đã chọn nếu baseData length thay đổi hoặc tổng số bản ghi dv đã chọn thay đổi
      setState({ updatedBaseData: new Date() });

      refBaseDataLength.current = state.baseData?.length;
      refTotalElements.current = totalElements;
    }
  }, [state.baseData]);

  const listDvChaKeys = useMemo(() => {
    return new Set(
      state.dataSource.filter((x) => x.dichVuChaKey).map((x) => x.dichVuChaKey)
    );
  }, [state.dataSource]);

  useEffect(() => {
    const data = state.baseData
      .sort((a, b) => {
        if (a.loaiDichVu === 10 && b.loaiDichVu !== 10) return -1;
        if (a.loaiDichVu !== 10 && b.loaiDichVu === 10) return 1;
        if (a.isNew && !b.isNew) return 1;
        if (!a.isNew && b.isNew) return -1;
        return 0;
      })
      .map((item) => {
        item.key = item.key ? item.key : stringUtils.guid();
        return item;
      });
    refFocusCount.current = 0;

    const parentItems = data.filter((x) => !x.dichVuChaKey);
    const childItems = data.filter((x) => x.dichVuChaKey);

    if (childItems?.length) {
      const groupByDichVuChaKey = groupBy(childItems, "dichVuChaKey");

      const enhancedParentItems = parentItems.map((parent) => {
        const children = groupByDichVuChaKey[parent.key] || [];
        return {
          ...parent,
          children: children,
        };
      });

      const listData = [];
      enhancedParentItems.forEach((parent) => {
        listData.push(parent);
        const children = parent.children || [];
        listData.push(...children);
        delete parent.children;
      });

      setState({ dataSource: listData });
    } else {
      setState({ dataSource: data });
    }
  }, [state.updatedBaseData]);

  const onFocus = useRefFunc((el, data) => {
    if (
      firstNewData?.key &&
      data.key === firstNewData.key &&
      refFocusCount.current === 0
    ) {
      if (el && typeof el.focus === "function") {
        el.focus();
        refFocusCount.current++;
      }
    }
  });

  const onDeleteIndex = (data, index, idDichVu) => {
    const isTonTaiDichVuCon = state.dataSource.find(
      (x) => x.dichVuChaKey === data.key
    );
    if (isTonTaiDichVuCon) {
      message.error(t("tiepDon.vuiLongXoaDichVuConTruocKhiXoaDichVuCha"));
      return;
    }
    showConfirm(
      {
        title: t("common.xoaDuLieu"),
        content: t("tiepDon.banCoMuonXoaDichVu"),
        cancelText: t("common.huy"),
        okText: t("common.dongY"),
        classNameOkText: "button-error",
        showImg: true,
        showBtnOk: true,
      },
      () => {
        const currentIndex = page * size + index + 1;
        const nextPage = Math.floor((currentIndex - 2) / size); // currentIndex - 1 sẽ là index hiện tại nếu thành công thì -1 lần nữa = -2
        return onDeleteDichVu({
          id: idDichVu,
          loaiDichVu: data.loaiDichVu,
          data,
          index,
        }).then(() => {
          if (id) {
            onSearchNbDv({
              page: Math.max(nextPage, 0),
              size: size,
              nbDotDieuTriId: id,
              dsChiDinhTuLoaiDichVu: [
                LOAI_DICH_VU.TIEP_DON,
                LOAI_DICH_VU.DAT_KHAM,
                LOAI_DICH_VU.CRM,
                LOAI_DICH_VU.GOI_KSK,
              ],
              dsLoaiDichVu: [
                LOAI_DICH_VU.KHAM,
                LOAI_DICH_VU.XET_NGHIEM,
                LOAI_DICH_VU.CDHA,
                LOAI_DICH_VU.PHAU_THUAT_THU_THUAT,
                LOAI_DICH_VU.NGOAI_DIEU_TRI,
              ].join(","),
            });

            tongTien({
              nbDotDieuTriId: id,
              thanhToan: -1,
              dsChiDinhTuLoaiDichVu: LOAI_DICH_VU.TIEP_DON,
            });
          } else {
            let indexLeft =
              listDvChoose?.findIndex((x) => x?.dichVuId === data?.dichVuId) ??
              -1;
            if (indexLeft >= 0) {
              listDvChoose.splice(index, 1);
              updateData({
                listDvChoose: [...listDvChoose],
              });
            }
          }
        });
      }
    );
  };

  const updateDvDaTiepDon = (record) => {
    const _listDvDaTiepDon = cloneDeep(listDvDaTiepDon);
    const index = _listDvDaTiepDon?.findIndex(
      (item) => item.key === record.key
    );
    if (index > -1) {
      _listDvDaTiepDon[index] = cloneDeep(record);
      updateData({
        listDvDaTiepDon: _listDvDaTiepDon,
      });
    }
  };

  const save = async (data, index, tinhTienLai = false, field = null) => {
    if (!data.id) {
      let newDataSource = cloneDeep(listDvChoose);
      let _updateItem = newDataSource.find((item) => item.newId === data.newId);
      if (_updateItem) {
        _updateItem.phongId = data.phongThucHienId || _updateItem.phongId;
        _updateItem.loaiDichVu = data.loaiDichVu;
        _updateItem.bacSiKhamId = data.bacSiKhamId;
        _updateItem.loaiHinhThanhToanId = data?.loaiHinhThanhToanId;
        _updateItem.tenPhongThucHien = data.tenPhongThucHien || "";
        _updateItem.ghiChu = data.ghiChu || "";
        _updateItem.benhPhamId = data.benhPhamId;
        _updateItem.nguonKhacId = data.nguonKhacId;
        _updateItem.dsCdChinhId = data.dsCdChinhId;
        _updateItem.dsCdKemTheoId = data.dsCdKemTheoId;
        _updateItem.bacSiChiDinhId = data.bacSiChiDinhId;
        _updateItem.tuTra = data.tuTra;
        _updateItem.khongTinhTien = data.khongTinhTien;
        if (field === "dsCdChinhId" || field === "dsCdKemTheoId") {
          _updateItem.requestUpdateChanDoan = true;
        }
        if (field === "thoiGianThucHien") {
          _updateItem.thoiGianThucHien = data.thoiGianThucHien;
        }

        if (field === "loaiHinhThanhToanId") {
          let resNoiLayMau = [];
          if (data?.loaiDichVu === LOAI_DICH_VU.XET_NGHIEM) {
            resNoiLayMau = await getListNoiLayMau({
              khoaChiDinhId: quayTiepDonTaiKhoan?.khoaId,
              dsDoiTuongKcb: doiTuongKcb,
              dichVuId: data?.dichVuId,
              nhomDichVuCap2Id: data?.nhomDichVuCap2Id,
              nhomDichVuCap3Id: data?.nhomDichVuCap3Id,
              dsLoaiDoiTuongId: thongTinBenhNhan.loaiDoiTuongId,
              loaiHinhThanhToanId: data?.loaiHinhThanhToanId,
              thoiGianThucHien: moment().format(),
            });

            _updateItem.dsNoiLayMau = resNoiLayMau || [];
            if ((resNoiLayMau || []).length === 1) {
              _updateItem.phongLayMauId = resNoiLayMau[0].phongLayMauId;
            }
          }

          let resPhongThucHien = (data.dsPhongThucHien || []).filter(
            (x) =>
              !x.loaiHinhThanhToanId ||
              !loaiHinhThanhToanId ||
              x.loaiHinhThanhToanId == data?.loaiHinhThanhToanId
          );
          debugger;

          _updateItem.dsPhongThucHien = resPhongThucHien || [];
          if ((resPhongThucHien || []).length === 1) {
            _updateItem.phongThucHienId = resPhongThucHien[0].phongId;
          }
        }
      }

      updateData({
        listDvChoose: cloneDeep(newDataSource),
      });

      //khi update loaiHinhThanhToanId cần set lại dataSource để nhận giá trị phòng mới
      if (field === "loaiHinhThanhToanId") {
        let newListChoose = cloneDeep(state.dataSource);
        let idxUpdate = newListChoose.findIndex((x) => {
          return x.newId === _updateItem.newId;
        });
        if (idxUpdate > -1) {
          newListChoose[idxUpdate].dsPhongThucHien =
            _updateItem.dsPhongThucHien;
          newListChoose[idxUpdate].phongThucHienId =
            _updateItem.phongThucHienId;
          newListChoose[idxUpdate].dsNoiLayMau = _updateItem.dsNoiLayMau;
          newListChoose[idxUpdate].phongLayMauId = _updateItem.phongLayMauId;
        }
        setState({ dataSource: newListChoose });
      }

      //tính tiền lại trong trường hợp đơn giá phụ thuộc loại hình thanh toán
      if (tinhTienLai) {
        let obj = {
          id: Number(data?.id),
          nbDotDieuTriId: data?.nbDotDieuTriId || id,
          nbDichVu: {
            dichVuId: data?.dichVuId,
            soLuong: data?.soLuong,
            loaiDichVu: data?.loaiDichVu,
            khoaChiDinhId: props.khoaTiepDonId,
            loaiHinhThanhToanId: data?.loaiHinhThanhToanId,
            nguonKhacId: data?.nguonKhacId || null,
            tuTra: data?.tuTra,
            khongTinhTien: data?.khongTinhTien,
            ...(field === "thoiGianThucHien" && {
              thoiGianThucHien: data.thoiGianThucHien,
            }),
          },
        };
        tamTinhTien({
          data: [obj],
          loaiDichVu: data?.loaiDichVu,
        }).then((s) => {
          let newListDvChoose = newDataSource;
          let _updateItemChoose = newListDvChoose.find(
            (item) => item.newId === data.newId
          );
          if (_updateItemChoose) {
            _updateItemChoose.tinhTien = s.data[0]?.nbDichVu || {};
          }

          if (_updateItem) {
            _updateItem.giaKhongBaoHiem = s.data[0]?.nbDichVu?.giaKhongBaoHiem;
          }
          updateData({
            listDvChoose: newDataSource,
          });
        });
      }
    } else {
      if (field === "dsCdChinhId" || field === "dsCdKemTheoId") {
        debounceUpdateChanDoan({
          id: data?.id,
          dsCdChinhId: data?.dsCdChinhId
            ? safeConvertToArray(data?.dsCdChinhId)
            : null,
          dsCdKemTheoId: data?.dsCdKemTheoId
            ? safeConvertToArray(data?.dsCdKemTheoId)
            : null,
        });
        updateDvDaTiepDon(data);

        return;
      }

      let obj = {
        id: Number(data?.id),
        nbDotDieuTriId: data?.nbDotDieuTriId,
        bacSiKhamId: data?.bacSiKhamId,
        bacSiChiDinhId: data?.bacSiChiDinhId,
        dotKhamMoi: data?.dotKhamMoi,
        nbDvKyThuat: {
          phongThucHienId:
            state.phongThucHienId?.[data.id] || data?.phongThucHienId,
        },
        nbDichVu: {
          dichVuId: data?.dichVuId,
          loaiDichVu: data?.loaiDichVu,
          loaiHinhThanhToanId: data?.loaiHinhThanhToanId,
          ghiChu: data.ghiChu,
          nguonKhacId: data.nguonKhacId || null,
          tuTra: data.tuTra,
          khongTinhTien: data.khongTinhTien,
          ...(field === "thoiGianThucHien" && {
            thoiGianThucHien: data.thoiGianThucHien,
          }),
        },
        benhPhamId: data.benhPhamId || null,
      };
      thayDoiThongTinDichVuDaChon({
        data: obj,
        id: data?.id,
      })
        .then((s) => {
          if (s.code === 0) {
            if (field === "bacSiKhamId") {
              updateDvDaTiepDon(data);
            }
          } else {
            if (field == "tuTra" && refCustomCheckbox.current[data.key]) {
              refCustomCheckbox.current[data.key].refresh();
            }
            message.error(s?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          }
        })
        .catch((err) => {
          console.error(err);
          message.error(err?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
        });
    }
  };

  const onChangeSL = (item) => (e) => {
    let obj = {
      id: Number(item?.id),
      nbDotDieuTriId: item?.nbDotDieuTriId || id,
      nbDichVu: {
        dichVuId: item?.dichVuId,
        soLuong: e,
        loaiDichVu: item?.loaiDichVu,
        khoaChiDinhId: props.khoaTiepDonId,
        loaiHinhThanhToanId: item?.loaiHinhThanhToanId,
        nguonKhacId: item?.nguonKhacId,
      },
    };
    tamTinhTien({
      data: [obj],
      loaiDichVu: item?.loaiDichVu,
    }).then((s) => {
      let _message = (s?.data || [])
        .reduce((acc, cur) => {
          if (cur.message && !acc.includes(cur.message)) {
            acc.push(cur.message);
          }
          return acc;
        }, [])
        .join(", ");
      const updateDataDichVu = () => {
        let newListChoose = cloneDeep(state.dataSource);
        let idxUpdate = newListChoose.findIndex((x) => {
          return x.id ? x.id === item.id : x.newId === item.newId;
        });
        if (idxUpdate > -1) {
          newListChoose[idxUpdate].tinhTien = s.data[0]?.nbDichVu || {};
          newListChoose[idxUpdate].thanhToan = -1;
          newListChoose[idxUpdate].soLuong = e;
        }
        setState({ dataSource: newListChoose });
        if (item?.id) {
          thayDoiThongTinDichVuDaChon({
            data: obj,
            id: obj?.id,
          });
        } else {
          let listDichVu = newListChoose.filter((x) => !x.id);
          updateData({ listDvChoose: listDichVu });
        }
      };
      if (_message) {
        showConfirm(
          {
            title: t("common.canhBao"),
            content: _message,
            cancelText: t("common.huy"),
            showImg: false,
            typeModal: "warning",
            showBtnOk: true,
            okText: t("common.xacNhan"),
          },
          () => {
            updateDataDichVu();
          }
        );
      } else {
        updateDataDichVu();
      }
    });
  };

  const onShowDsDvBvE = () => {
    refModalDichVuBvE.current && refModalDichVuBvE.current.show();
  };

  const onHoanDv = (record) => {
    let gender = gioiTinh ? GIOI_TINH_BY_VALUE[gioiTinh] : "";

    let age =
      thangTuoi > 36 || tuoi
        ? `${tuoi} ${t("common.tuoi")}`
        : `${thangTuoi} ${t("common.thang")}`;
    const data = state.dataSource;
    if (data?.length) {
      data.forEach((itemLoop) => {
        itemLoop.gioiTinh = gender;
        itemLoop.tuoi = age;
      });

      refModalHoanDichVu.current &&
        refModalHoanDichVu.current.show(
          {
            data: data,
            selectedRowKeys: [record?.id],
          },
          () => {
            onSearchNbDv({
              nbDotDieuTriId: id,
              dsChiDinhTuLoaiDichVu: [
                LOAI_DICH_VU.TIEP_DON,
                LOAI_DICH_VU.DAT_KHAM,
                LOAI_DICH_VU.CRM,
                LOAI_DICH_VU.GOI_KSK,
              ],
              dsLoaiDichVu: [
                LOAI_DICH_VU.KHAM,
                LOAI_DICH_VU.XET_NGHIEM,
                LOAI_DICH_VU.CDHA,
                LOAI_DICH_VU.PHAU_THUAT_THU_THUAT,
                LOAI_DICH_VU.NGOAI_DIEU_TRI,
              ].join(","),
            });
          }
        );
    } else {
      message.error(t("khamBenh.chiDinh.khongCoDichVuThoaManDieuKienDeHoan"));
    }
  };

  const onHuyHoan = (data) => {
    if (refModalHuyHoanDichVu.current)
      refModalHuyHoanDichVu.current.show(
        { data: [data], nbDotDieuTriId: id },
        () => {
          onSearchNbDv({
            nbDotDieuTriId: id,
            dsChiDinhTuLoaiDichVu: [
              LOAI_DICH_VU.TIEP_DON,
              LOAI_DICH_VU.DAT_KHAM,
              LOAI_DICH_VU.CRM,
              LOAI_DICH_VU.GOI_KSK,
            ],
            dsLoaiDichVu: [
              LOAI_DICH_VU.KHAM,
              LOAI_DICH_VU.XET_NGHIEM,
              LOAI_DICH_VU.CDHA,
              LOAI_DICH_VU.PHAU_THUAT_THU_THUAT,
              LOAI_DICH_VU.NGOAI_DIEU_TRI,
            ].join(","),
          });
        }
      );
  };

  const onDoiDichVu = (data) => {
    if (refModalDoiDichVu.current)
      refModalDoiDichVu.current.show(data, () => {
        onSearchNbDv({
          nbDotDieuTriId: id,
          dsChiDinhTuLoaiDichVu: [
            LOAI_DICH_VU.TIEP_DON,
            LOAI_DICH_VU.DAT_KHAM,
            LOAI_DICH_VU.CRM,
            LOAI_DICH_VU.GOI_KSK,
          ],
          dsLoaiDichVu: [
            LOAI_DICH_VU.KHAM,
            LOAI_DICH_VU.XET_NGHIEM,
            LOAI_DICH_VU.CDHA,
            LOAI_DICH_VU.PHAU_THUAT_THU_THUAT,
            LOAI_DICH_VU.NGOAI_DIEU_TRI,
          ].join(","),
        });
      });
  };

  const onThemDichVuHen = (data) => () => {
    updateData({ dichVuHen: data });
  };

  const onKetThucDichVuHen = () => {
    updateData({ dichVuHen: null });
  };

  const columns = [
    {
      title: <HeaderSearch title={t("common.stt")} />,
      width: "50px",
      dataIndex: "index",
      key: "index",
      align: "center",
      ignore: true,
      render: (item, data, index) => {
        let _index = page * size + index + 1;
        return page === 0 || !listDvChoose?.length
          ? _index
          : _index + listDvChoose?.length;
      },
    },
    {
      title: <HeaderSearch title={t("tiepDon.dichVuHen")} />,
      width: "100px",
      dataIndex: "ma",
      hideSearch: true,
      i18Name: "tiepDon.dichVuHen",
      show: true,
      hidden: !TIEP_DON_KE_DICH_VU_CON?.eval(),
      render: (_, data) => {
        if (!data?.dichVuChaKey) {
          if (data.key === dichVuHen?.key) {
            return (
              <Button type="warning" onClick={onKetThucDichVuHen}>
                {t("common.ketThuc")}
              </Button>
            );
          }
          return (
            <Button
              type="success"
              onClick={onThemDichVuHen(data)}
              disabled={data?.loaiDichVu !== LOAI_DICH_VU.KHAM}
            >
              {t("common.them")}
            </Button>
          );
        }
      },
    },
    {
      title: <HeaderSearch title={t("hsba.chanDoanChinh")} />,
      width: 150,
      dataIndex: "dsCdChinhId",
      hideSearch: true,
      i18Name: "hsba.chanDoanChinh",
      show: true,
      hidden: !TIEP_DON_KE_DICH_VU_CON?.eval(),
      render: (item, data, index) => {
        return (
          <SelectChanDoan
            placeholder={t("danhMuc.nhapTitle", {
              title: t("hsba.chanDoanChinh").toLowerCase(),
            })}
            style={{ width: "100%" }}
            showSearch
            mode="multiple"
            allowClear
            maxItem={1}
            disabled={
              data?.loaiDichVu !== LOAI_DICH_VU.KHAM || data.dichVuChaKey
            }
            value={item ? safeConvertToArray(item).map(String) : undefined}
            onChange={(e) => {
              data.dsCdChinhId = e;
              save(data, index, false, "dsCdChinhId");
            }}
            dropdownMatchSelectWidth={300}
            className={
              thongTinBenhNhan?.doiTuong === DOI_TUONG.BAO_HIEM &&
              listDvChaKeys.has(data.key) &&
              !data.dsCdChinhId?.length
                ? "error"
                : ""
            }
          />
        );
      },
    },
    {
      title: <HeaderSearch title={t("hsba.chanDoanKemTheo")} />,
      width: 150,
      dataIndex: "dsCdKemTheoId",
      hideSearch: true,
      i18Name: "hsba.chanDoanKemTheo",
      show: true,
      hidden: !TIEP_DON_KE_DICH_VU_CON?.eval(),
      render: (item, data, index) => {
        const value = item ? safeConvertToArray(item).map(String) : [];
        return (
          <SelectChanDoan
            placeholder={t("danhMuc.nhapTitle", {
              title: t("hsba.chanDoanKemTheo").toLowerCase(),
            })}
            style={{ width: "100%" }}
            showSearch
            allowClear
            maxItem={12}
            mode="multiple"
            disabled={
              data?.loaiDichVu !== LOAI_DICH_VU.KHAM || data.dichVuChaKey
            }
            value={value}
            onChange={(e) => {
              if (e.length === 12 && item?.length === 12) {
                message.error(
                  t("tiepDon.chiHienThiToiDaBenhKemTheoChoDv", {
                    num: 12,
                    tenDichVu: data?.tenDichVu,
                  })
                );
                return;
              }
              data.dsCdKemTheoId = e;
              save(data, index, false, "dsCdKemTheoId");
            }}
            dropdownMatchSelectWidth={300}
          />
        );
      },
    },
    {
      title: <HeaderSearch title={t("common.maDv")} />,
      width: "120px",
      dataIndex: "ma",
      hideSearch: true,
      i18Name: "common.maDv",
      show: true,
      render: (item, list) => {
        return <div>{list?.ma || list?.maDichVu}</div>;
      },
    },
    {
      title: <HeaderSearch title={t("common.tenDichVu")} />,
      width: "200px",
      dataIndex: "ten",
      hideSearch: true,
      i18Name: "common.tenDichVu",
      show: true,
      render: (item, list) => {
        let data =
          state.dataSource.filter((option) => {
            let check1 =
              option?.ten && item && option?.ten === item ? true : false;
            let check2 =
              option?.tenDichVu &&
              list?.tenDichVu &&
              option?.tenDichVu === list?.tenDichVu
                ? true
                : false;
            return check1 || check2;
          }) || [];
        let index = data.findIndex((x) => x?.id === list?.id);
        const showSoLan =
          !dataKHONG_HIEN_THI_SO_LAN_SAU_TEN_DICH_VU?.eval() &&
          index &&
          index > -1;
        return (
          <div>
            {list?.ten || list?.tenDichVu}
            {showSoLan ? ` ${t("common.lan")} ${index + 1}` : ""}
          </div>
        );
      },
    },
    {
      title: <HeaderSearch title={t("tiepDon.sl")} />,
      width: "60px",
      dataIndex: "soLuong",
      hideSearch: true,
      align: "center",
      i18Name: "tiepDon.sl",
      show: true,
      render: (item, data, index) => {
        return (
          <InputNumber
            ref={(el) => onFocus(el, data)}
            style={{ width: 50 }}
            value={item ? item : macDinhSoLuong1 ? 1 : null}
            min={1}
            onChange={onChangeSL(data)}
            readOnly={checkDaThanhToan(data.thanhToan) || !isPermisionEditDv}
            disabled={data?.loaiDichVu === LOAI_DICH_VU.KHAM && macDinhSoLuong1}
          />
        );
      },
    },
    {
      title: <HeaderSearch title={t("tiepDon.donGia")} />,
      width: "80px",
      dataIndex: "giaKhongBaoHiem",
      hideSearch: true,
      align: "right",
      i18Name: "tiepDon.donGia",
      show: true,
      render: (item, list) => {
        let donGia = getDonGia(list, doiTuong);
        return <div>{donGia ? donGia.formatPrice() : ""}</div>;
      },
    },
    {
      title: <HeaderSearch title={t("tiepDon.phong")} />,
      width: "150px",
      dataIndex: "phongThucHienId",
      hideSearch: true,
      i18Name: "tiepDon.phong",
      align: "center",
      show: true,
      render: (_, item, index) => {
        return (
          <Select
            refSelect={(el) => onFocus(el, item)}
            popupClassName="tiep-don-select-1"
            style={{ width: "100%" }}
            allowClear={true}
            value={item?.phongThucHienId || item.phongId}
            disabled={!isPermisionEditDv}
            onChange={(e) => {
              setTimeout(() => {
                showConfirm(
                  {
                    title: t("common.thongBao"),
                    content: t("common.xacNhanChuyenPhong")
                      .replace("{1}", item.tenPhongThucHien || item.tenPhong)
                      .replace(
                        "{2}",
                        item?.dsPhongThucHien?.find((x) => e === x.phongId)?.ten
                      ),
                    cancelText: t("common.huy"),
                    okText: t("common.dongY"),
                    classNameOkText: "button-warning",
                    showImg: true,
                    showBtnOk: true,
                    typeModal: "warning",
                  },
                  () => {
                    item.phongThucHienId = e;
                    item.tenPhongThucHien = item?.dsPhongThucHien?.find(
                      (x) => e === x.phongId
                    )?.ten;
                    save(item, index);
                  }
                );
              }, 0);
            }}
            data={uniqBy(item?.dsPhongThucHien || [], "phongId")}
            getLabel={(item) => item?.ten}
            getValue={(item) => item?.phongId}
          />
        );
      },
    },
    {
      title: <HeaderSearch title={t("khamBenh.chiDinh.noiLayMau")} />,
      width: "150px",
      dataIndex: "phongLayMauId",
      hideSearch: true,
      i18Name: "khamBenh.chiDinh.noiLayMau",
      align: "center",
      show: true,
      render: (value, item, index) => {
        if (item.loaiDichVu !== LOAI_DICH_VU.XET_NGHIEM) return null;

        return (
          <Select
            refSelect={(el) => onFocus(el, item)}
            style={{ width: "100%" }}
            value={value}
            onChange={(e) => {
              item.phongLayMauId = e;
              save(item, index);
            }}
            data={item?.dsNoiLayMau || []}
            allowClear={true}
            getLabel={(item) =>
              `${item?.phongLayMau?.ten || ""} (${
                item?.phongLayMau?.diaDiem || ""
              })`
            }
            getValue={(item) => item?.phongLayMauId}
          />
        );
      },
    },
    {
      title: <HeaderSearch title={t("khamBenh.chiDinh.loaiHinhThanhToan")} />,
      width: "150px",
      dataIndex: "loaiHinhThanhToanId",
      hideSearch: true,
      i18Name: "khamBenh.chiDinh.loaiHinhThanhToan",
      show: true,
      align: "center",
      render: (item, data, index) => {
        if (checkChuaThanhToan(data.thanhToan)) {
          return (
            <Select
              refSelect={(el) => onFocus(el, data)}
              style={{ width: 130 }}
              value={item}
              data={data?.dsLoaiHinhThanhToan || []}
              getLabel={(item) => item.tenLoaiHinhThanhToan}
              getValue={(item) => item.loaiHinhThanhToanId}
              onChange={(e) => {
                data.loaiHinhThanhToanId = e;
                save(data, index, true, "loaiHinhThanhToanId");
              }}
              disabled={checkDaThanhToan(data?.thanhToan) || !isPermisionEditDv}
              readOnly={checkDaThanhToan(data?.thanhToan) || !isPermisionEditDv}
            />
          );
        } else {
          return data.tenLoaiHinhThanhToan;
        }
      },
    },
    {
      title: <HeaderSearch title={t("tiepDon.bacSi")} />,
      width: "230px",
      dataIndex: "bacSiKhamId",
      hideSearch: true,
      i18Name: "tiepDon.bacSi",
      align: "center",
      show: true,
      render: (item, data, index) => {
        return (
          <Select
            refSelect={(el) => onFocus(el, data)}
            allowClear={true}
            dropdownMatchSelectWidth={350}
            style={{ width: 200 }}
            value={item}
            data={listNhanVien}
            getLabel={(item) =>
              [item?.taiKhoan, item?.ma, item?.ten].filter(Boolean).join(" - ")
            }
            onChange={(e) => {
              data.bacSiKhamId = e;
              save(data, index, false, "bacSiKhamId");
            }}
            disabled={
              checkDaThanhToan(data?.thanhToan) ||
              !isPermisionEditDv ||
              data?.loaiDichVu !== LOAI_DICH_VU.KHAM
            }
          />
        );
      },
    },
    {
      title: <HeaderSearch title={t("danhMuc.bacSiChiDinh")} />,
      width: "230px",
      dataIndex: "bacSiChiDinhId",
      hideSearch: true,
      i18Name: "danhMuc.bacSiChiDinh",
      align: "center",
      show: true,
      render: (item, data, index) => {
        return (
          <Select
            refSelect={(el) => onFocus(el, data)}
            allowClear={true}
            dropdownMatchSelectWidth={350}
            style={{ width: 200 }}
            value={item}
            data={listNhanVien}
            getLabel={(item) =>
              [item?.taiKhoan, item?.ma, item?.ten].filter(Boolean).join(" - ")
            }
            onChange={(e) => {
              data.bacSiChiDinhId = e;
              save(data, index, false, "bacSiChiDinhId");
            }}
            disabled={
              checkDaThanhToan(data?.thanhToan) ||
              !isPermisionEditDv ||
              (data?.loaiDichVu !== LOAI_DICH_VU.KHAM &&
                !dataLAY_BAC_SI_CHI_DINH_LAY_THEO_TRUONG_BS_CHI_DINH?.eval())
            }
          />
        );
      },
    },
    {
      title: <HeaderSearch title={t("tiepDon.thoiGianChiDinh")} />,
      width: "150px",
      dataIndex: "thoiGianChiDinh",
      hideSearch: true,
      align: "right",
      i18Name: "tiepDon.thoiGianChiDinh",
      show: true,
      render: (item) => {
        return item && moment(item).format("DD/MM/YYYY HH:mm:ss");
      },
    },
    {
      title: <HeaderSearch title={t("tiepDon.thoiGianThucHien")} />,
      width: "200px",
      dataIndex: "thoiGianThucHien",
      hideSearch: true,
      align: "right",
      i18Name: "tiepDon.thoiGianThucHien",
      show: true,
      render: (item, data, index) => {
        if (dsLoaiDichVuLayThoiGianThucHien?.includes(data?.loaiDichVu)) {
          return (
            <DateTimePicker
              value={moment(item)}
              format={"DD-MM-YYYY HH:mm:ss"}
              showTime
              onChange={(e) => {
                data.thoiGianThucHien = e;
                save(data, index, true, "thoiGianThucHien");
              }}
            />
          );
        }
        return item && moment(item).format("DD/MM/YYYY HH:mm:ss");
      },
    },
    {
      title: <HeaderSearch title={t("common.trangThai")} />,
      width: "100px",
      dataIndex: "trangThai",
      hideSearch: true,
      align: "right",
      i18Name: "common.trangThai",
      show: true,
      render: (item) => {
        return (listTrangThaiDichVu || []).find((x) => x.id === item)?.ten;
      },
    },
    {
      title: <HeaderSearch title={t("tiepDon.daTT")} />,
      width: "80px",
      dataIndex: "thanhToan",
      hideSearch: true,
      align: "center",
      i18Name: "tiepDon.daTT",
      show: true,
      render: (item, list) => {
        return <Checkbox checked={checkDaThanhToan(item)} disabled></Checkbox>;
      },
    },
    {
      title: <HeaderSearch title={t("tiepDon.khamMoi")} />,
      width: "80px",
      dataIndex: "dotKhamMoi",
      hideSearch: true,
      align: "center",
      i18Name: "tiepDon.khamMoi",
      show: true,
      render: (item, list, index) => {
        return (
          <Checkbox
            checked={item}
            onChange={(e) => {
              list.dotKhamMoi = e?.target?.checked;
              save(list, index);
            }}
            disabled={list.loaiDichVu !== LOAI_DICH_VU.KHAM}
          ></Checkbox>
        );
      },
    },
    {
      title: <HeaderSearch title={t("common.moTaLuuy")} />,
      width: 250,
      dataIndex: "ghiChu",
      hideSearch: true,
      align: "ghiChu",
      i18Name: "common.moTaLuuy",
      show: true,
      render: (item, data, index) => {
        return (
          <InputTimeout
            ref={(el) => onFocus(el, data)}
            value={item}
            onChange={(e) => {
              data.ghiChu = e;
              save(data, index);
            }}
            readOnly={checkDaThanhToan(data.thanhToan) || !isPermisionEditDv}
          />
        );
      },
    },
    {
      title: <HeaderSearch title={t("danhMuc.nguonKhac")} />,
      width: "230px",
      dataIndex: "nguonKhacId",
      hideSearch: true,
      i18Name: "danhMuc.nguonKhac",
      align: "center",
      show: true,
      render: (item, data, index) => {
        return (
          <Select
            dropdownMatchSelectWidth={350}
            style={{ width: 200 }}
            value={item}
            data={listAllNguonKhacChiTra}
            onChange={(e) => {
              data.nguonKhacId = e;
              save(data, index, true);
            }}
            disabled={checkDaThanhToan(data?.thanhToan) || !isPermisionEditDv}
          />
        );
      },
    },
    {
      title: <HeaderSearch title={t("xetNghiem.benhPham")} />,
      width: "230px",
      dataIndex: "benhPhamId",
      hideSearch: true,
      i18Name: "xetNghiem.benhPham",
      align: "center",
      show: true,
      render: (item, data, index) => {
        const allowClear =
          typeof data.yeuCauBenhPham === "boolean" ? data.yeuCauBenhPham : true;
        return (
          <Select
            allowClear={allowClear}
            dropdownMatchSelectWidth={350}
            style={{ width: 200 }}
            value={item}
            data={
              state?.searchBenhPhamIds?.[data.id]
                ? listAllBenhPham
                : data?.dsBenhPham?.some((x) => x.id === item)
                ? data.dsBenhPham
                : listAllBenhPham
            }
            onSearch={(e) => {
              setState({ searchBenhPhamIds: { [data.id]: !!e } });
            }}
            onChange={(e) => {
              data.benhPhamId = e;
              save(data, index);
            }}
            disabled={
              checkDaThanhToan(data?.thanhToan) ||
              !isPermisionEditDv ||
              data?.loaiDichVu !== LOAI_DICH_VU.XET_NGHIEM
            }
          />
        );
      },
    },
    {
      title: <HeaderSearch title={t("common.tuTra")} />,
      width: "100px",
      dataIndex: "tuTra",
      hideSearch: true,
      i18Name: "common.tuTra",
      show: true,
      align: "center",
      render: (item, data, index) => {
        //nếu tk có quyền 0300211 => hiển thị và thao tác btn tự trả
        if (
          (!data?.dichVuChaKey &&
            !checkRole([ROLES["TIEP_DON"].THAO_TAC_TU_TRA_NB_BAO_HIEM])) ||
          thongTinBenhNhan?.doiTuong !== DOI_TUONG.BAO_HIEM
        )
          return null;

        return (
          <CustomCheckbox
            ref={(el) => (refCustomCheckbox.current[data.key] = el)}
            checked={!!item}
            onChange={(e) => {
              data.tuTra = e?.target?.checked;
              save(data, index, true, "tuTra");
            }}
          />
        );
      },
    },
    {
      title: <HeaderSearch title={t("common.khongTinhTien")} />,
      width: "100px",
      dataIndex: "khongTinhTien",
      hideSearch: true,
      i18Name: "common.khongTinhTien",
      show: true,
      align: "center",
      render: (item, data, index) => {
        return (
          <CustomCheckbox
            checked={!!item}
            onChange={(e) => {
              data.khongTinhTien = e?.target?.checked;
              save(data, index, true, "khongTinhTien");
            }}
            disabled={
              checkDaThanhToan(data?.thanhToan) ||
              !isPermisionEditDv ||
              !isPermisionCheckKhongTinhTien
            }
          />
        );
      },
    },
    {
      title: <HeaderSearch title={<Setting refTable={refSettings} />} />,
      width: "100px",
      dataIndex: "action",
      key: "action",
      fixed: "right",
      align: "center",
      hideSearch: true,
      ignore: true,
      render: (item, list, index) => {
        return (
          <>
            <Tooltip title={t("tiepDon.xoaDichVu")} placement="bottom">
              {checkChuaThanhToan(list?.thanhToan) && (
                <SVG.IcDelete
                  onClick={() => onDeleteIndex(list, index, list?.id)}
                  className="ic-action"
                />
              )}
            </Tooltip>
            <Tooltip
              title={t("khamBenh.chiDinh.hoanDichVu")}
              placement="bottom"
            >
              {checkDaThanhToan(list?.thanhToan) &&
                list.trangThaiHoan === 0 && (
                  <SVG.IcHoanDv
                    onClick={() => onHoanDv(list)}
                    className="ic-action"
                  />
                )}
            </Tooltip>
            {list.trangThaiHoan === 10 && (
              <Tooltip
                title={t("khamBenh.chiDinh.huyYeuCauHoan")}
                placement="bottom"
              >
                <SVG.IcHuyHoanDv
                  className="ic-action"
                  onClick={() => onHuyHoan(list)}
                />
              </Tooltip>
            )}
            <Tooltip title={t("tiepDon.doiDichVuKham")} placement="bottomLeft">
              {list.loaiDichVu === LOAI_DICH_VU.KHAM &&
                checkChuaThanhToan(list?.thanhToan) &&
                list.trangThai < TRANG_THAI_DICH_VU.DA_KET_LUAN && (
                  <SVG.IcChuyenDichVu
                    onClick={() => onDoiDichVu(list)}
                    className="ic-action"
                  />
                )}
            </Tooltip>
          </>
        );
      },
    },
  ];

  const onChangePage = (page) => {
    onSearchNbDv({
      page: page - 1,
      nbDotDieuTriId: id,
      dsChiDinhTuLoaiDichVu: [
        LOAI_DICH_VU.TIEP_DON,
        LOAI_DICH_VU.DAT_KHAM,
        LOAI_DICH_VU.CRM,
        LOAI_DICH_VU.GOI_KSK,
      ],
      dsLoaiDichVu: [
        LOAI_DICH_VU.KHAM,
        LOAI_DICH_VU.XET_NGHIEM,
        LOAI_DICH_VU.CDHA,
        LOAI_DICH_VU.PHAU_THUAT_THU_THUAT,
        LOAI_DICH_VU.NGOAI_DIEU_TRI,
      ].join(","),
    });
  };

  const handleSizeChange = (size) => {
    onSearchNbDv({
      size,
      nbDotDieuTriId: id,
      dsChiDinhTuLoaiDichVu: [
        LOAI_DICH_VU.TIEP_DON,
        LOAI_DICH_VU.DAT_KHAM,
        LOAI_DICH_VU.CRM,
        LOAI_DICH_VU.GOI_KSK,
      ],
      dsLoaiDichVu: [
        LOAI_DICH_VU.KHAM,
        LOAI_DICH_VU.XET_NGHIEM,
        LOAI_DICH_VU.CDHA,
        LOAI_DICH_VU.PHAU_THUAT_THU_THUAT,
        LOAI_DICH_VU.NGOAI_DIEU_TRI,
      ].join(","),
    });
  };

  console.log("state.dataSource", state.dataSource);

  return (
    <Main className="dich-vu-da-chon">
      <GlobalStyle />
      <Box2
        title={t("tiepDon.dichVuDaChon")}
        headerRight={
          nbNgoaiVien?.maHoSo && nbNguonNb?.nguonNbId === 2 ? (
            <Button.Text type="primary" onClick={onShowDsDvBvE}>
              Xem thêm
            </Button.Text>
          ) : null
        }
        noPadding={true}
      >
        <TableWrapper
          ref={refSettings}
          showHeaderTable={false}
          scroll={{ y: 400, x: 1200 }}
          columns={columns}
          dataSource={state.dataSource}
          tableName="table_TIEPDON_DICH_VU_DA_CHON"
        />

        {!!state.dataSource?.length && (
          <Pagination
            listData={state.dataSource}
            onChange={onChangePage}
            current={(page || 0) + 1}
            pageSize={size}
            total={totalElements}
            onShowSiz
            onShowSizeChange={handleSizeChange}
          />
        )}
      </Box2>
      <ModalDichVuBvE ref={refModalDichVuBvE} />
      <ModalHoanDichVu ref={refModalHoanDichVu} />
      <ModalHuyHoanDichVu ref={refModalHuyHoanDichVu} />
      <ModalDoiDichVu ref={refModalDoiDichVu} />
    </Main>
  );
};

export default memo(DichVuDaChon);
