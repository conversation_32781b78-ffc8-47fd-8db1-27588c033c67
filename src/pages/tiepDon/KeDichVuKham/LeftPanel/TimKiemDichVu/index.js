import React, { memo, useState, useEffect, useMemo, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import orderBy from "lodash/orderBy";
import {
  ENUM,
  HOTKEY,
  LOAI_DICH_VU,
  ROLES,
  DOI_TUONG_SU_DUNG,
  LOAI_PHONG,
  CACHE_KEY,
  TRANG_THAI_PHIEU_THU_THANH_TOAN,
  THIET_LAP_CHUNG,
} from "constants/index";
import Header from "components/Header";
import {
  TableWrapper,
  Select,
  HeaderSearch,
  InputTimeout,
  Checkbox,
  Pagination,
  InputSearch,
} from "components";
import { DivSetting, Main } from "./styled";
import { useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { checkRole } from "lib-utils/role-utils";
import {
  useCache,
  useConfirm,
  useEnum,
  usePersonalSettings,
  useStore,
  useThietLap,
} from "hooks";
import cacheUtils from "lib-utils/cache-utils";
import { Radio, message } from "antd";
import TableBoChiDinh from "./TableBoChiDinh";
import moment from "moment";
import ModalBoSungThongTinDichVu from "../ModalBoSungThongTinDichVu";
import useMacDinhLoaiDV from "../../useMacDinhLoaiDV";
import { isArray, isNumber } from "utils/index";
import { guid } from "mainam-react-native-string-utils";

const { Setting } = TableWrapper;
const LOAI_DV_KSK = "ksk";

const TimKiemDichVu = ({ layerId, ...props }) => {
  const refBoSungThongTin = useRef();
  const { showConfirm } = useConfirm();
  const { t } = useTranslation();
  const { id } = useParams();
  const refSettings = useRef(null);
  const refChonLoaiDV = useRef(null);
  const refInputTimKiem = useRef(null);
  const refSelectRow = useRef(null);
  const refAddService = useRef(null);
  const refKeDichVu = useRef(null);

  const [macDinhLoaiDV, setMacDinhLoaiDV, loadFinish, isTiepDonNbKskTrucTiep] =
    useMacDinhLoaiDV();
  const thongTinBenhNhan = useStore("nbDotDieuTri.thongTinBenhNhan", {});
  const listDvKham = useStore("tiepDonDichVu.listDvKham", []);
  const listDvChoose = useStore("tiepDonDichVu.listDvChoose", [], false);
  const listNbGoiDv = useStore("nbGoiDv.listNbGoiDv", []);
  const listAllPhong = useStore("phong.listAllPhong", []);
  const dichVuHen = useStore("tiepDonDichVu.dichVuHen", null);

  const {
    doiTuong,
    khamSucKhoe,
    gioiTinh,
    covid,
    tiemChung,
    ngaySinh,
    loaiDoiTuongId,
    loaiDoiTuong,
    doiTuongKcb,
  } = thongTinBenhNhan;
  const {
    totalElementsDv: totalElements,
    pageDv: page,
    sizeDv: size,
  } = useStore(
    "tiepDonDichVu",
    {},
    {
      fields: "totalElementsDv, pageDv, sizeDv",
    }
  );
  const { auth } = useSelector((state) => state.auth);
  const khuVucId = useStore("tiepDonDichVu.khuVucId", null);
  const [listLoaiDichVu] = useEnum(ENUM.LOAI_DICH_VU);
  const listDvDaTiepDon = useStore("tiepDonDichVu.listDvDaTiepDon", []);
  const quayTiepDonId = useStore("goiSo.quayTiepDonId", null);
  const listAllQuayTiepDon = useStore("quayTiepDon.listAllQuayTiepDon");
  const listAllQuayTiepDonTaiKhoan = useSelector(
    (state) => state.quayTiepDon.listAllQuayTiepDonTaiKhoan
  );

  const [dataAN_COT_DON_GIA_KHI_KE_DV_TIEP_DON] = useThietLap(
    THIET_LAP_CHUNG.AN_COT_DON_GIA_KHI_KE_DV_TIEP_DON
  );
  const [dataLOAI_DV_THG_THUC_HIEN_THEO_THG_CHON_TIEP_DON] = useThietLap(
    THIET_LAP_CHUNG.LOAI_DV_THG_THUC_HIEN_THEO_THG_CHON_TIEP_DON
  );

  const anCotDonGia = dataAN_COT_DON_GIA_KHI_KE_DV_TIEP_DON?.eval();

  const [_hienThiDichVu, setHienThiDichVu] = useCache(
    "",
    CACHE_KEY.DATA_HIEN_THI_DICH_VU,
    "2",
    false
  );

  const { personalSettings, personalSettingsMutation } = usePersonalSettings();

  const hienThiDichVu = useMemo(() => {
    return (
      personalSettings?.thietLap?.keDichVuTiepDon?.caiDatHienThiBang
        ?.hienThiDichVu || _hienThiDichVu
    );
  }, [
    personalSettings?.thietLap?.keDichVuTiepDon?.caiDatHienThiBang
      ?.hienThiDichVu,
    _hienThiDichVu,
  ]);

  const defaultHienThiDichVu = checkRole([
    ROLES["TIEP_DON"].HIEN_THI_TAT_CA_DICH_VU_KHU_VUC_KHAC,
  ])
    ? hienThiDichVu
    : "2";

  const quayTiepDon = listAllQuayTiepDon?.find((x) => x.id === quayTiepDonId);
  const quayTiepDonTaiKhoan = listAllQuayTiepDonTaiKhoan?.find(
    (x) => x.id === quayTiepDonId
  );

  const {
    tiepDonDichVu: {
      searchDvTiepDon,
      onChangeInputSearchDvTiepDon,
      onSizeChangeDvTiepDon,
      searchDvKSKTiepDon,
      getDsGoiDvChiTiet,
      themDichVu,
      xoaDichVu,
      themBo,
      xoaBo,
      updateData,
    },
    phimTat: { onRegisterHotkey },
    noiLayBenhPham: { getListNoiLayMau },
  } = useDispatch();

  const [listGoiDvChoose, setListGoiDvChoose] = useState([]);

  const [state, _setState] = useState({
    active: 1,
    textSearchDv: "",
    loaiDichVu: null,
    itemHtmlTriggerClicked: null,
    openSelectLoaiDv: false,
    isKeDichVu: false,
  });

  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  useEffect(() => {
    if (loadFinish) {
      //giá trị all cho case set loại dv = null để phân biệt với case chưa set giá trị thì mặc định = KHÁM
      setState({
        loaiDichVu: isTiepDonNbKskTrucTiep
          ? LOAI_DV_KSK
          : macDinhLoaiDV == "ALL"
          ? null
          : macDinhLoaiDV,
      });
    }
  }, [loadFinish, macDinhLoaiDV, isTiepDonNbKskTrucTiep]);

  useEffect(() => {
    onRegisterHotkey({
      layerId,
      hotKeys: [
        {
          keyCode: HOTKEY.F2,
          onEvent: () => {
            refInputTimKiem.current && refInputTimKiem.current.focus();
            setState({ isKeDichVu: false });
          },
        },
        {
          keyCode: HOTKEY.F6,
          onEvent: () => {
            refChonLoaiDV.current && refChonLoaiDV.current.focus();
            setState({
              isKeDichVu: false,
              openSelectLoaiDv: true,
              key: null,
            });
          },
        },
        {
          keyCode: HOTKEY.ENTER,
          onEvent: (e) => {
            if (refAddService.current) {
              refAddService.current(true);
            }
          },
        },
        {
          keyCode: HOTKEY.F7,
          onEvent: (e) => {
            if (refKeDichVu.current) {
              refKeDichVu.current();
            }
          },
        },
        {
          keyCode: HOTKEY.UP,
          onEvent: (e) => {
            refSelectRow.current && refSelectRow.current(-1);
          },
        },
        {
          keyCode: HOTKEY.DOWN,
          onEvent: (e) => {
            refSelectRow.current && refSelectRow.current(1);
          },
        },
      ],
    });
    return () => {
      updateData({ dataSearchDv: {} });
    };
  }, []);

  const khuVucIdMemo = useMemo(() => {
    if (isArray(listAllQuayTiepDon, true) && quayTiepDonId) {
      return listAllQuayTiepDon.find((x) => x.id === quayTiepDonId)?.khuVucId;
    }
  }, [quayTiepDonId, listAllQuayTiepDon]);
  const onChiDinhDv = () => {
    const data = listService[0];
    setState({
      isKeDichVu: true,
      openSelectLoaiDv: false,
      key: `${data?.dichVuId} - ${data.phongId}`,
    });
    refChonLoaiDV.current && refChonLoaiDV.current.blur();
    if (document.activeElement) {
      // Blur khỏi element đang focus
      document.activeElement.blur();
    }
  };
  refKeDichVu.current = onChiDinhDv;

  refSelectRow.current = (index) => {
    if (!state.isKeDichVu) return;
    const indexNextItem =
      (listService?.findIndex(
        (item) => `${item?.dichVuId} - ${item.phongId}` === state?.key
      ) || 0) + index;
    if (-1 < indexNextItem && indexNextItem < listService.length) {
      const data = listService[indexNextItem];
      setState({ key: `${data?.dichVuId} - ${data.phongId}` });
      document
        .getElementsByClassName(
          "row-id-" + `${data?.dichVuId} - ${data.phongId}`
        )[0]
        .scrollIntoView({ block: "end", behavior: "smooth" });
    }
  };

  const dataloaiDichVu = useMemo(() => {
    if (tiemChung)
      return [
        ...(checkRole([ROLES["TIEP_DON"].KE_DV_KHAM])
          ? listLoaiDichVu.filter((item) =>
              [LOAI_DICH_VU.KHAM, LOAI_DICH_VU.NGOAI_DIEU_TRI].includes(item.id)
            )
          : []),
      ];
    // if (doiTuong === 2)
    //   return listLoaiDichVu.filter((item) => item.id == 10);
    const goiDVKSK = [{ id: LOAI_DV_KSK, ten: t("tiepDon.dvTrongHopDongKSK") }];

    return [
      ...(checkRole([ROLES["TIEP_DON"].KE_DV_KHAM])
        ? listLoaiDichVu.filter((item) => item.id === LOAI_DICH_VU.KHAM)
        : []),
      ...(checkRole([ROLES["TIEP_DON"].KE_DV_XN])
        ? listLoaiDichVu.filter((item) => item.id === LOAI_DICH_VU.XET_NGHIEM)
        : []),
      ...(checkRole([ROLES["TIEP_DON"].KE_DV_CLS])
        ? listLoaiDichVu.filter((item) => item.id === LOAI_DICH_VU.CDHA)
        : []),
      ...(checkRole([ROLES["TIEP_DON"].KE_DV_CLS])
        ? listLoaiDichVu.filter(
            (item) => item.id === LOAI_DICH_VU.PHAU_THUAT_THU_THUAT
          )
        : []),
      ...(checkRole([ROLES["TIEP_DON"].KE_GOI_DV])
        ? listLoaiDichVu.filter((item) => item.id === LOAI_DICH_VU.BO_CHI_DINH)
        : []),
      ...listLoaiDichVu.filter(
        (item) => item.id === LOAI_DICH_VU.NGOAI_DIEU_TRI
      ), // Khác
      ...(khamSucKhoe ? goiDVKSK : []),
      ...listNbGoiDv.map((x) => ({ ...x, ten: x.tenGoiDv })),
    ];
  }, [listLoaiDichVu, auth, doiTuong, listNbGoiDv, khamSucKhoe, tiemChung]);

  const onChangeGroupService = (value, item) => {
    //https://conf.isofh.com.vn/pages/viewpage.action?pageId=34413804
    // if (doiTuong === 2) {
    //   if (value === 10) {
    //     setState({ loaiDichVu: value });
    //     onGetListService({ loaiDichVu: value });
    //   }
    // } else {
    setState({ loaiDichVu: value });
    onGetListService({ loaiDichVu: value });
    // }
  };

  const onGetListService = (payload = {}) => {
    const _selectedGroup = dataloaiDichVu.find(
      (x) => x.id === payload.loaiDichVu
    );
    if (_selectedGroup?.goiDvId) {
      getDsGoiDvChiTiet({
        page: "",
        size: "",
        goiDvId: _selectedGroup.goiDvId,
        nbGoiDvId: _selectedGroup.id,
        nbThongTinId: thongTinBenhNhan?.nbThongTinId,
        khoaChiDinhId: quayTiepDonTaiKhoan?.khoaId,
        dangSuDung: true,
      });
      return;
    }
    if (payload.loaiDichVu === LOAI_DV_KSK) {
      searchDvKSKTiepDon({
        nbDotDieuTriId: id,
        khoaChiDinhId: quayTiepDonTaiKhoan?.khoaId,
        hopDongKsk: true,
        hopDongKskId: thongTinBenhNhan?.nbKhamSucKhoe?.hopDongKskId,
        doiTuongKcb,
      });
    } else {
      let _khuVucId = payload.hasOwnProperty("khuVucId")
        ? payload.khuVucId
        : khuVucId;
      onChangeInputSearchDvTiepDon(
        {
          gioiTinh: gioiTinh,
          ngaySinh: ngaySinh && moment(ngaySinh).format("YYYY-MM-DD"),
          thoiGianThucHien: moment().format(),
          covid: covid ? covid : null,
          dsKhuVucId: _khuVucId,
          ...payload,
          ...(!_khuVucId &&
            isNumber(khuVucIdMemo) && {
              // case Hiển thị tất cả dịch vụ thì sort theo khuVucId nên truyền params -khuVucId để sort only ko lọc
              khuVucId: -khuVucIdMemo,
            }),
          khoaChiDinhId: quayTiepDonTaiKhoan?.khoaId,
          loaiDoiTuongId: thongTinBenhNhan?.loaiDoiTuongId,
          thoiGianVaoTien: thongTinBenhNhan?.thoiGianVaoTien,
          ...(tiemChung
            ? {
                dsDoiTuongSuDung: DOI_TUONG_SU_DUNG.NB_TIEM_CHUNG,
                ...(payload.loaiDichVu !== LOAI_DICH_VU.NGOAI_DIEU_TRI
                  ? { loaiPhong: LOAI_PHONG.PHONG_KHAM_TIEM_CHUNG }
                  : {}),
              }
            : {}),
          ...(payload.loaiDichVu === LOAI_DICH_VU.BO_CHI_DINH
            ? {
                sort: "ten,asc",
              }
            : {}),
          doiTuongKcb,
          ...(!_khuVucId && isNumber(khuVucIdMemo)
            ? { nhaChiDinhId: null }
            : { nhaChiDinhId: quayTiepDonTaiKhoan?.toaNhaId }), // case Hiển thị tất cả dịch vụ thì ko truyền thêm nhaChiDinhId
        },
        {
          searchAll: true,
        }
      );
    }
  };

  let listService = useMemo(() => {
    let datacheck = listDvChoose || [];
    if (state.loaiDichVu === LOAI_DICH_VU.BO_CHI_DINH) {
      datacheck = listGoiDvChoose;
    }

    let ten = "",
      tenText = "";

    let data = (listDvKham || [])
      .filter((item) => {
        if (state.loaiDichVu !== LOAI_DV_KSK) return true;

        ten = `${item?.ma ? item?.ma : ""} ${item?.ten ? item?.ten : ""} ${
          item?.maPhong ? item?.maPhong : ""
        } ${item?.tenPhong ? item?.tenPhong : ""}`;
        tenText = ten ? ten.trim().toLowerCase().unsignText() : "";

        return tenText.indexOf(state.textSearchDv) >= 0;
      })
      .map((item, index) => {
        let dataChecked = datacheck?.filter((option) => {
          return (
            option?.dichVuId === item?.dichVuId &&
            (item?.phongId ? option?.phongId === item?.phongId : true)
          );
        });

        let checkThanhToan = dataChecked?.find(
          (option) =>
            option.thanhToan === TRANG_THAI_PHIEU_THU_THANH_TOAN.DA_THANH_TOAN
        );
        return {
          ...item,
          checked: !!dataChecked.length,
          thanhToan: !!checkThanhToan,
          key: index,
        };
      });

    return orderBy(data, "checked", "desc");
  }, [listDvKham, listDvChoose, state.textSearchDv, state.loaiDichVu]);

  const onSearchService = (value) => {
    let textSearchDv = value ? value.trim().toLowerCase().unsignText() : "";
    setState({ textSearchDv });

    if (state.loaiDichVu !== LOAI_DV_KSK) {
      onChangeInputSearchDvTiepDon(
        {
          loaiDichVu: state.loaiDichVu,
          ...(!khuVucId &&
            isNumber(khuVucIdMemo) && {
              // case Hiển thị tất cả dịch vụ thì sort theo khuVucId nên truyền params -khuVucId để sort only ko lọc
              khuVucId: -khuVucIdMemo,
            }),
          ...(state.loaiDichVu === LOAI_DICH_VU.BO_CHI_DINH
            ? { timKiem: value, timKiem2: null }
            : {
                timKiem2: value,
                timKiem: null,
              }),
          doiTuongKcb,
          ...(!khuVucId && isNumber(khuVucIdMemo)
            ? { nhaChiDinhId: null }
            : { nhaChiDinhId: quayTiepDonTaiKhoan?.toaNhaId }), // case Hiển thị tất cả dịch vụ thì ko truyền thêm nhaChiDinhId
        },
        {
          searchAll: state.loaiDichVu === LOAI_DICH_VU.BO_CHI_DINH,
        }
      );
    }
  };
  const onSelectService = (data) => async (e) => {
    //nam.mn 2021 05 20
    /*
    - update nghiệp vụ mới, cho phép chọn nhiều dịch vụ trùng nhau, bỏ trạng thái check box khi click chọn
    - yến confirm    
    */
    const existLoaiDoiTuong = quayTiepDon?.dsLoaiDoiTuongGioiHan?.find(
      (x) => x.id === loaiDoiTuongId
    );
    //check không tồn tại loại đối tượng có trong danh sách giới hạn của quầy, không có quầy, và hiển thị khu vực theo khu vực thì chặn
    if (
      !existLoaiDoiTuong &&
      !(
        checkRole([ROLES["TIEP_DON"].HIEN_THI_TAT_CA_DICH_VU_KHU_VUC_KHAC]) &&
        hienThiDichVu === "1"
      )
    ) {
      message.error(
        t("tiepDon.quayDuocChonKhogBaoGomLoaiDuoiTuongCuaNb", {
          tenLoaiDoiTuong: loaiDoiTuong?.ten,
        })
      );
      return;
    }
    const loaiHinhThanhToanId = data?.loaiHinhThanhToanId
      ? data?.loaiHinhThanhToanId
      : (data?.dsLoaiHinhThanhToan?.sort((a, b) => b.uuTien - a.uuTien) ||
          [])[0]?.loaiHinhThanhToanId;

    let resNoiLayMau = [];
    if (data?.loaiDichVu === LOAI_DICH_VU.XET_NGHIEM) {
      resNoiLayMau = await getListNoiLayMau({
        khoaChiDinhId: quayTiepDonTaiKhoan?.khoaId,
        dsDoiTuongKcb: doiTuongKcb,
        dichVuId: data.dichVuId,
        nhomDichVuCap2Id: data.nhomDichVuCap2Id,
        nhomDichVuCap3Id: data.nhomDichVuCap3Id,
        dsLoaiDoiTuongId: thongTinBenhNhan.loaiDoiTuongId,
        loaiHinhThanhToanId: loaiHinhThanhToanId,
      });
    }

    let resPhongThucHien = (data.dsPhongThucHien || []).filter(
      (x) =>
        !x.loaiHinhThanhToanId ||
        !loaiHinhThanhToanId ||
        x.loaiHinhThanhToanId == loaiHinhThanhToanId
    );

    const dsLoaiDichVuLayThoiGianThucHien =
      dataLOAI_DV_THG_THUC_HIEN_THEO_THG_CHON_TIEP_DON
        ?.split(",")
        .map((x) => Number(x.trim()))
        .filter((n) => !isNaN(n));

    let item = {
      ...data,
      loaiHinhThanhToanId: loaiHinhThanhToanId,
      detachId: `${data.dichVuId} - ${data.phongId}`,
      benhPhamId:
        data?.yeuCauBenhPham && data?.dsBenhPhamId?.length == 1
          ? data?.dsBenhPhamId?.[0]
          : null,
      dsNoiLayMau: resNoiLayMau || [],
      phongLayMauId:
        (resNoiLayMau || []).length === 1
          ? resNoiLayMau[0].phongLayMauId
          : null,
      dsPhongThucHien: resPhongThucHien || [],
      phongThucHienId:
        (resPhongThucHien || []).length === 1
          ? resPhongThucHien[0].phongId
          : null,
      tenDichVu: data?.ten,
      key: guid(),
      ...((dsLoaiDichVuLayThoiGianThucHien || []).includes(data?.loaiDichVu)
        ? { thoiGianThucHien: moment() }
        : {}),
    };
    if (dichVuHen) {
      item = {
        ...item,
        chiDinhTuDichVuId: dichVuHen.id,
        chiDinhTuLoaiDichVu: LOAI_DICH_VU.KHAM,
        dichVuChaKey: dichVuHen.key,
      };
    }
    let value = e?.target?.checked || !data.checked;
    if (state.loaiDichVu === LOAI_DICH_VU.BO_CHI_DINH) {
      if (value) {
        themBo({
          boChiDinhId: item.dichVuId,
          nbDotDieuTriId: id,
          loaiDoiTuongId: thongTinBenhNhan.loaiDoiTuongId,
        }).then((s) => {
          if (s?.length) {
            setListGoiDvChoose([item, ...listGoiDvChoose]);
          }
        });
      } else {
        xoaBo({ boChiDinhId: item.dichVuId });
        let _listGoiDvChoose = listGoiDvChoose.filter(
          (x) => x.dichVuId !== item.dichVuId
        );
        setListGoiDvChoose(_listGoiDvChoose);
      }
      return;
    }

    let service = listDvKham.find((x) => {
      return (
        x?.dichVuId === item?.dichVuId &&
        (item?.phongId ? x?.phongId === item?.phongId : true)
      );
    });
    let checkDuplicate = false;
    let index = [...listDvChoose, ...listDvDaTiepDon].find((x) => {
      return x.dichVuId === item.dichVuId && x.phongId !== item.phongId;
    });
    if (index && value) {
      showConfirm(
        {
          title: t("tiepDon.trungDichVu"),
          content: t("tiepDon.xacNhanKeTrungDichVu").replace("{0}", item.ten),
          cancelText: t("common.quayLai"),
          okText: t("common.dongY"),
          classNameOkText: "button-error",
          showImg: true,
          showBtnOk: true,
        },
        () => {
          if (value) {
            if (!item.benhPhamId && item.yeuCauBenhPham) {
              refBoSungThongTin.current &&
                refBoSungThongTin.current.show(
                  { dsDichVu: [item] },
                  (dsDichVu) => {
                    themDichVu({ dsDichVu: dsDichVu, nbDotDieuTriId: id });
                  }
                );
            } else {
              themDichVu({ dsDichVu: [item], nbDotDieuTriId: id });
            }
          } else {
            xoaDichVu({ dsDichVu: [item] });
          }
        },
        () => {}
      );
      checkDuplicate = true;
    }
    if (service && !checkDuplicate) {
      // service.checked = value;
      if (value) {
        if (!item.benhPhamId && item.yeuCauBenhPham) {
          refBoSungThongTin.current &&
            refBoSungThongTin.current.show({ dsDichVu: [item] }, (dsDichVu) => {
              themDichVu({ dsDichVu: dsDichVu, nbDotDieuTriId: id });
            });
        } else {
          themDichVu({ dsDichVu: [item], nbDotDieuTriId: id });
        }
      } else {
        xoaDichVu({ dsDichVu: [item] });
      }
    }
  };

  const getTenPhong = (id) => {
    let phong = listAllPhong?.find((e) => e.id === id);
    if (phong) {
      return (
        phong?.ma + "-" + phong?.ten + (phong.toaNha ? "-" + phong.toaNha : "")
      );
    }
    return "";
  };

  useEffect(() => {
    if (state.itemHtmlTriggerClicked) {
      state.itemHtmlTriggerClicked.click();
    }
  }, [state.itemHtmlTriggerClicked]);

  const onRow = (record, index) => {
    return {
      onClick: (event) => {
        setState({
          itemHtmlTriggerClicked:
            event.target.parentElement.lastElementChild.firstElementChild,
        });
      },
    };
  };

  const onChangeRadio = (e) => {
    //setting hiển thị dịch vụ theo khu vực. Giá trị 1: tất cả dịch vụ, 2: theo khu vực
    setHienThiDichVu(e.target.value);

    personalSettingsMutation.mutate({
      path: "thietLap.keDichVuTiepDon.caiDatHienThiBang.hienThiDichVu",
      value: e.target.value,
    });
    cacheUtils.save("", CACHE_KEY.DATA_HIEN_THI_DICH_VU, e.target.value, false);
    let khuVucId = null;
    if (e.target.value == "2" && quayTiepDonId) {
      khuVucId = listAllQuayTiepDon.find(
        (x) => x.id === quayTiepDonId
      )?.khuVucId;
    }
    updateData({ khuVucId });
    onGetListService({
      khuVucId,
      loaiDichVu: state.loaiDichVu,
    });
  };

  const onEnterAddService = (isDeSelect) => {
    if (state.isKeDichVu) {
      let data = listService.find(
        (x) => `${x.dichVuId} - ${x.phongId}` === state?.key
      );
      if (data) onSelectService(data)();
    }
    if (isDeSelect) {
      setState({ isKeDichVu: false, key: null });
    }
  };

  refAddService.current = onEnterAddService;

  const content = (
    <div style={{ display: "grid" }}>
      {t("tiepDon.caiDatHienThiDichVu")}:
      <Radio.Group
        defaultValue={defaultHienThiDichVu}
        style={{ display: "grid" }}
        onChange={onChangeRadio}
      >
        {checkRole([
          ROLES["TIEP_DON"].HIEN_THI_TAT_CA_DICH_VU_KHU_VUC_KHAC,
        ]) && <Radio value={"1"}>{t("tiepDon.hienThiTatCaDv")}</Radio>}
        <Radio value={"2"}>{t("tiepDon.hienThiDvTheoKhuVuc")}</Radio>
      </Radio.Group>
      <DivSetting>
        <span className="label-dv">{t("tiepDon.macDinhHienThiLoaiDV")}</span>
        <Select
          defaultValue={macDinhLoaiDV == "ALL" ? null : macDinhLoaiDV}
          data={dataloaiDichVu}
          placeholder={t("tiepDon.chonNhomDichVu")}
          onChange={(value) => {
            setMacDinhLoaiDV(value ? value : "ALL");
            personalSettingsMutation.mutate({
              path: "thietLap.keDichVuTiepDon.caiDatHienThiBang.macDinhLoaiDV",
              value: value ? value : "ALL",
            });
            onChangeGroupService(value);
          }}
        />
      </DivSetting>
    </div>
  );

  const onChangePage = (page) => {
    searchDvTiepDon({ page: page - 1 });
  };

  return (
    <Main>
      <Header
        title={t("tiepDon.chiDinhDichVu") + " [F7]"}
        content={
          <>
            <div className="flex gap-8 align-items-center">
              <span style={{ color: "black" }}>[F6]</span>
              <Select
                onChange={onChangeGroupService}
                value={state.loaiDichVu}
                placeholder={t("tiepDon.chonNhomDichVu")}
                data={dataloaiDichVu}
                refSelect={refChonLoaiDV}
                open={state.openSelectLoaiDv}
                onDropdownVisibleChange={(open) =>
                  setState({ openSelectLoaiDv: open, isKeDichVu: false })
                }
              />
            </div>
            <InputSearch width={300} searchStyle={{ minHeight: "100%" }}>
              <InputTimeout
                autoFocus={true}
                ref={refInputTimKiem}
                placeholder={`${t(
                  state.loaiDichVu === LOAI_DICH_VU.BO_CHI_DINH
                    ? "danhMuc.timTenBoChiDinh"
                    : "tiepDon.timTenDichVuPhongThucHien"
                )} [F2]`}
                onChange={(e) => onSearchService(e)}
              />
            </InputSearch>
            {/* <div className="input-text">
              <img src={require("assets/images/welcome/search2.png")} alt="" /> */}

            {/* </div> */}
          </>
        }
      />
      {state.loaiDichVu === LOAI_DICH_VU.BO_CHI_DINH ? (
        <TableBoChiDinh
          listService={listService}
          onGetListService={onGetListService}
          listDvChoose={listDvChoose}
        />
      ) : (
        <>
          <TableWrapper
            className="table"
            scroll={{ y: 453, x: 1000 }}
            ref={refSettings}
            rowKey={(record, index) =>
              `${index} - ${record.dichVuId} - ${record.phongId}`
            }
            onRow={onRow}
            rowClassName={(record) =>
              `${record.dichVuId} - ${record.phongId}` === state.key
                ? "row-actived row-id-" +
                  `${record.dichVuId} - ${record.phongId}`
                : "row-id-" + `${record.dichVuId} - ${record.phongId}`
            }
            columns={[
              {
                title: (
                  <HeaderSearch
                    title={t("tiepDon.maDV")}
                    sort_key="ma"
                    // // onClickSort={onClickSort}
                    // dataSort={props.dataSortColumn && props?.dataSortColumn["tenNb"] || 0}
                  />
                ),
                width: 70,
                dataIndex: "ma",
                hideSearch: true,
                i18Name: "tiepDon.maDV",
                show: true,
              },
              {
                title: (
                  <HeaderSearch
                    title={t("common.tenDichVu")}
                    sort_key="ten"
                    // // onClickSort={onClickSort}
                    // dataSort={props.dataSortColumn && props?.dataSortColumn["tenNb"] || 0}
                  />
                ),
                width: 294,
                dataIndex: "ten",
                type: true,
                hideSearch: true,
                i18Name: "common.tenDichVu",
                show: true,
              },
              {
                title: (
                  <HeaderSearch
                    title={t("tiepDon.donGiaKhongBh")}
                    sort_key="giaKhongBaoHiem"
                    // // onClickSort={onClickSort}
                    // dataSort={props.dataSortColumn && props?.dataSortColumn["tenNb"] || 0}
                  />
                ),
                width: 90,
                dataIndex: "giaKhongBaoHiem",
                hideSearch: true,
                align: "right",
                i18Name: "tiepDon.donGiaKhongBh",
                show: true,
                hidden: anCotDonGia,
                render: (item) => {
                  return <div>{item ? item.formatPrice() : ""}</div>;
                },
              },
              {
                title: (
                  <HeaderSearch
                    title={t("tiepDon.donGiaBH")}
                    sort_key="giaBaoHiem"
                    // // onClickSort={onClickSort}
                    // dataSort={props.dataSortColumn && props?.dataSortColumn["tenNb"] || 0}
                  />
                ),
                width: 80,
                dataIndex: "giaBaoHiem",
                hideSearch: true,
                align: "right",
                i18Name: "tiepDon.donGiaBH",
                show: true,
                hidden: anCotDonGia,
                render: (item) => {
                  return <div>{item ? item.formatPrice() : ""}</div>;
                },
              },
              {
                title: (
                  <HeaderSearch
                    title={t("tiepDon.phong")}
                    sort_key="phongId"
                    // // onClickSort={onClickSort}
                    // dataSort={props.dataSortColumn && props?.dataSortColumn["tenNb"] || 0}
                  />
                ),
                width: 200,
                dataIndex: "phongId",
                hideSearch: true,
                i18Name: "tiepDon.phong",
                show: true,
                render: (item) => {
                  return getTenPhong(item);
                },
              },
              {
                title: (
                  <HeaderSearch
                    title={
                      <>
                        {t("common.chon")} <Setting refTable={refSettings} />
                      </>
                    }
                    // sort_key="ma"
                    // // onClickSort={onClickSort}
                    // dataSort={props.dataSortColumn && props?.dataSortColumn["tenNb"] || 0}
                  />
                ),
                width: 60,
                dataIndex: "checked",
                hideSearch: true,
                align: "center",
                fixed: "right",
                render: (value, item, index) => {
                  if (item?.thanhToan) {
                    return (
                      <Checkbox
                        checked={value}
                        id={"checkbox_dv_" + item.dichVuId}
                        // disabled
                        className="box-item"
                      ></Checkbox>
                    );
                  } else {
                    return (
                      <Checkbox
                        id={"checkbox_dv_" + item.dichVuId}
                        autoFocus={state.isSelect === 1 && index === 1}
                        className="box-item"
                        onChange={onSelectService(item)}
                        checked={value}
                        onKeyDown={(e) => {
                          if (e.keyCode === 13 && !state.isKeDichVu) {
                            const event = {
                              target: { checked: !e.target?.checked },
                            };
                            onSelectService(item)(event);
                            let activeEl = document.activeElement?.className;
                            if (activeEl == "ant-checkbox-input") {
                              document.activeElement.blur();
                            }
                          }
                        }}
                      />
                    );
                  }
                },
              },
            ]}
            dataSource={listService}
            showHeaderTable={false}
            tableName="TABLE_TIEP_DON_TIM_KIEM_DICH_VU"
            content={content}
            alwayGetFromCache={true}
          ></TableWrapper>
          {!!totalElements ? (
            <Pagination
              onChange={onChangePage}
              current={page + 1}
              pageSize={size}
              listData={listService}
              total={totalElements}
              onShowSizeChange={onSizeChangeDvTiepDon}
            />
          ) : null}
        </>
      )}
      <ModalBoSungThongTinDichVu ref={refBoSungThongTin} />
    </Main>
  );
};
export default memo(TimKiemDichVu);
