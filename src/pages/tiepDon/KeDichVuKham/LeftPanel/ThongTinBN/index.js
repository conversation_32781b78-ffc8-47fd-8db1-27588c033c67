import React, { memo, useEffect, useMemo, useRef, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { message, Menu, Avatar } from "antd";
import {
  ROLES,
  LENGTH_ZERO_PREFIX,
  ENUM,
  THIET_LAP_CHUNG,
} from "constants/index";
import { Main, PopoverWrapper, GlobalStyle, DropdownStyle } from "./styled";
import { Tooltip, Image, AuthWrapper, Dropdown } from "components";
import { checkData, isArray } from "utils";
import { addPrefixNumberZero } from "utils";
import fileUtils from "utils/file-utils";
import { useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import {
  useCamera,
  useConfirm,
  useEnum,
  useLoading,
  useStore,
  useThietLap,
} from "hooks";
import ModalThemMoiGoi from "pages/goiDichVu/DanhSachSuDungGoi/ModalThemMoiGoi";
import { SVG } from "assets";
import ModalChinhSuaThongTin from "pages/tiepDon/TiepDon/ModalChinhSuaThongTin";
import ModalDanhSachBieuMauScan from "pages/hoSoBenhAn/components/ModalDanhSachBieuMauScan";
import ModalHoSoBenhAn from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/Modal/ModalHoSoBenhAn";
import ModalAnhDaiDien from "components/ModalAnhDaiDien";
import ModalLichSuSinhHieu from "pages/khamBenh/KhamCoBan/components/ModalLichSuSinhHieu";
import { refConfirm } from "app";
import { UserOutlined } from "@ant-design/icons";
import fileProvider from "data-access/file-provider";
import Aborter from "utils/Aborter";
import { checkRole } from "lib-utils/role-utils";

import ThongTinNb, {
  THONG_TIN_NB_KSK,
  THONG_TIN_NB_TIEM_CHUNG,
  THONG_TIN_NB_TIEP_DON,
} from "./components";
import ModalFolderAndFile from "components/ModalFolderAndFile";
import nbDotDieuTriProvider from "data-access/nb-dot-dieu-tri-provider";
import PhanLoaiNguoiBenh from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/ThongTinBenhNhan/PhanLoaiNguoiBenh";

const ThongTinBN = (props) => {
  const { t } = useTranslation();
  const { id } = useParams();
  const refModalChinhSuaThongTin = useRef(null);
  const refModalThemMoiGoi = useRef(null);
  const refModalDanhSachBieuMauScan = useRef(null);
  const refModalHoSoBenhAn = useRef(null);
  const refContainer = useRef(null);
  const refAnhDaiDien = useRef(null);
  const refModalLichSuSinhHieu = useRef(null);
  const refDataKhuonMat = useRef(null);
  const refModalFoderAndFile = useRef(null);

  const {
    onShowCamera,
    onHideCamera,
    onGetData: onGetDataCamera,
    onTake: onTakeCamera,
    reTakePhoto,
  } = useCamera();
  const { showLoading, hideLoading } = useLoading();
  const { showConfirm } = useConfirm();
  const [dataIVIRSEKEY] = useThietLap(THIET_LAP_CHUNG.IVIRSEKEY);
  const [dataTAI_KHOAN_QUAN_TRI_FACEID] = useThietLap(
    THIET_LAP_CHUNG.TAI_KHOAN_QUAN_TRI_FACEID
  );
  const [dataMAT_KHAU_QUAN_TRI_FACEID] = useThietLap(
    THIET_LAP_CHUNG.MAT_KHAU_QUAN_TRI_FACEID
  );
  const [dataFACE_ID_IVIRSE_ON_OFF] = useThietLap(
    THIET_LAP_CHUNG.FACE_ID_IVIRSE_ON_OFF
  );

  const [isLoadingTtNb, setIsLoadingTtNb] = useState(false);
  const [isSmall, setIsSmall] = useState(false);

  const {
    hangThe,
    anhDaiDien,
    stt,
    gioiTinh,
    tenNb,
    maNb,
    nbThongTinId,
    khamSucKhoe,
    loaiDoiTuongKsk,
  } = useSelector((state) => state.nbDotDieuTri.thongTinBenhNhanTongHop || {});

  const [listGioiTinh] = useEnum(ENUM.GIOI_TINH);
  const thongTinBenhNhan = useStore("nbDotDieuTri.thongTinBenhNhan", {});
  const { nbTiemChung } = thongTinBenhNhan;
  const tiemChung = useSelector((state) => state.tiepDon.tiemChung);

  const {
    nbDotDieuTri: { getById, getThongTinCoBan, onUpdate, getByTongHopId },
    nbGoiDv: { getByNbThongTinId },
    tiepDon: { getById: getByTiepDonId },
    ketNoiIvirse: {
      dangKyKhuonMat,
      getToken,
      getKhuonMatCu,
      xoaKhuonMat,
      xacThucKhuonMat,
    },
  } = useDispatch();

  useEffect(() => {
    if (
      nbThongTinId &&
      checkRole([ROLES.GOI_DICH_VU.DANH_SACH_NB_SU_DUNG_GOI_DICH_VU])
    ) {
      getByNbThongTinId({
        nbThongTinId,
      });
    }
  }, [nbThongTinId]);

  useEffect(() => {
    if (id) {
      setIsLoadingTtNb(true);
      getThongTinCoBan(id)
        .then(() => {
          setIsLoadingTtNb(false);
        })
        .catch(() => {
          setIsLoadingTtNb(false);
        });
      getById(id);
      getByTongHopId(id);
    }
  }, [id]);

  const screenThongTinNb = useMemo(() => {
    return tiemChung
      ? THONG_TIN_NB_TIEM_CHUNG
      : khamSucKhoe || loaiDoiTuongKsk
      ? THONG_TIN_NB_KSK
      : THONG_TIN_NB_TIEP_DON;
  }, [tiemChung, khamSucKhoe, loaiDoiTuongKsk]);

  const onEdit = () => {
    refModalChinhSuaThongTin.current &&
      refModalChinhSuaThongTin.current.show({ id }, () => {
        message.success(t("common.daLuuDuLieu"));
        getById(id);
        getThongTinCoBan(id);
      });
  };

  const onShowHoSoBenhAn = () => {
    refModalHoSoBenhAn.current &&
      refModalHoSoBenhAn.current.show({
        nbThongTinId,
        nbDotDieuTriId: id,
      });
  };

  const onShowScanBieuMau = () => {
    refModalDanhSachBieuMauScan.current &&
      refModalDanhSachBieuMauScan.current.show({
        nbDotDieuTriId: id,
      });
  };

  const onShowAnhDaiDien = () => {
    if (anhDaiDien)
      refAnhDaiDien.current &&
        refAnhDaiDien.current.show({
          anhDaiDien: anhDaiDien,
          tenNb: tenNb,
          isDisplayButton: !dataFACE_ID_IVIRSE_ON_OFF?.eval(),
        });
  };

  const onShowLichSuSinhHieu = () => {
    refModalLichSuSinhHieu.current &&
      refModalLichSuSinhHieu.current.show({
        nbDotDieuTriId: id,
        isKhamBenh: false,
      });
  };

  const handleDeleteFaceid = async () => {
    try {
      if (dataFACE_ID_IVIRSE_ON_OFF?.eval()) {
        await getToken({
          username: dataTAI_KHOAN_QUAN_TRI_FACEID,
          password: dataMAT_KHAU_QUAN_TRI_FACEID,
        });
        await getKhuonMatCu({ id: maNb });
        refConfirm.current &&
          refConfirm.current.show(
            {
              title: t("common.canhBao"),
              content: t("common.banCoMuonXoaAnhChupKhuonMat"),
              cancelText: t("common.quayLai"),
              okText: t("common.dongY"),
              classNameOkText: "button-warning",
              showBtnOk: true,
              typeModal: "warning",
            },
            async () => {
              showLoading();
              try {
                await xoaKhuonMat({ id: maNb });
                await onUpdate({ id, anhDaiDien: null });
                await getByTiepDonId(id);
                message.success(t("common.xoaAnhChupKhuonMatThanhCong"));
              } catch (e) {
                e?.message && message.error(e.message);
              } finally {
                hideLoading();
              }
            }
          );
      }
    } catch (e) {
      e?.message && message.error(e.message);
    }
  };

  const menu = () => (
    <Menu
      items={[
        {
          key: 1,
          label: (
            <a onClick={onShowModalCamera("register")}>
              <span>{t("common.dangKyKhuonMat")}</span>
            </a>
          ),
        },
        {
          key: 2,
          label: (
            <a onClick={onShowModalCamera("changeFaceid")}>
              <span>{t("common.thayDoiAnhChupKhuonMat")}</span>
            </a>
          ),
        },
        {
          key: 3,
          label: (
            <a onClick={handleDeleteFaceid}>
              <span>{t("common.xoaAnhChupKhuonMat")}</span>
            </a>
          ),
        },
      ]}
    />
  );

  const handleTake = async (file) => {
    refDataKhuonMat.current = file;
  };

  const tuDongXacThucKhuonMat = () => {
    const retryInterval = 1000;
    const intervalDuration = 20000;
    let apiSucceeded = false;

    const fetchData = async (file, signal) => {
      try {
        const res = await xacThucKhuonMat({
          Ivirsekey: dataIVIRSEKEY,
          file,
          signal,
        });
        return res;
      } catch (error) {}
    };

    const startFetching = async () => {
      let attemptCount = 0;
      let lastFailedRequest;
      const intervalId = setInterval(async () => {
        if (apiSucceeded) return clearInterval(intervalId);
        const signal = Aborter.newSignal(
          `chup-anh-nb-da-tiep-don-${Date.now()}`
        );
        attemptCount++;

        try {
          const file = onGetDataCamera();
          if (!file) return clearInterval(intervalId);
          const res = await fetchData(file, signal);
          if (res?.info?.address) {
            apiSucceeded = true;
            clearInterval(intervalId);
            Aborter.abortAll("success");
            const res = await fileProvider.uploadImage({
              file: refDataKhuonMat.current,
              type: "anhDaiDien",
            });
            await onUpdate({ id, anhDaiDien: res?.data });
            await getByTiepDonId(id);
            message.success(t("common.dangKyKhuonMatThanhCong"));
            hideLoading();
            onHideCamera();
          } else {
            lastFailedRequest = res;
          }
        } catch (error) {}

        if (attemptCount >= intervalDuration / retryInterval) {
          clearInterval(intervalId);
        }
      }, retryInterval);

      setTimeout(async () => {
        if (!apiSucceeded) {
          clearInterval(intervalId);
          Aborter.abortAll("timeout");
          hideLoading();
          await xoaKhuonMat({ id: maNb });
          await onUpdate({ id, anhDaiDien: null });
          await getByTiepDonId(id);
          if (lastFailedRequest) {
            message.error(
              lastFailedRequest?.message || t("common.xayRaLoiVuiLongThuLaiSau")
            );
            console.log(`lastFailedRequest`, lastFailedRequest);
          }
          showConfirm(
            {
              title: "",
              content: t("tiepDon.xacThucThatBaiVuiLongDangKyLai"),
              cancelText: t("common.huy"),
              okText: t("common.dongY"),
              showBtnOk: true,
              typeModal: "warning",
            },
            () => {
              onTakeCamera();
            },
            () => {
              reTakePhoto();
            }
          );
        }
      }, intervalDuration);
    };

    setTimeout(() => {
      showLoading();
      startFetching();
    }, 1000);
  };

  const autoDetect = async () => {
    if (maNb && tenNb && dataFACE_ID_IVIRSE_ON_OFF?.eval()) {
      try {
        showLoading();
        const file = onGetDataCamera();
        await dangKyKhuonMat({
          id: maNb,
          username: tenNb,
          file,
          Ivirsekey: dataIVIRSEKEY,
        });
        handleTake(file);
        tuDongXacThucKhuonMat();
      } catch (error) {
        message.error(error.message);
      } finally {
        hideLoading();
      }
    }
  };

  const onShowModalCamera = (type) => async () => {
    const obj = { param: {}, isShowModal: true, uploadFunc: null };
    if (type === "register") {
      if (anhDaiDien) {
        showLoading();
        try {
          obj.isShowModal = false;
          if (dataFACE_ID_IVIRSE_ON_OFF?.eval()) {
            await dangKyKhuonMat({
              id: maNb,
              username: tenNb,
              file: anhDaiDien,
              Ivirsekey: dataIVIRSEKEY,
            });
            message.success(t("common.dangKyKhuonMatThanhCong"));
          }
        } catch (e) {
          e.message && message.error(e.message);
        } finally {
          hideLoading();
        }
      } else {
        obj.param.title = t("common.chupAnh");
        obj.param.propSelect = {
          title: t("common.dangKyKhuonMat"),
          type: "default",
          size: "middle",
          className: "btn-register",
          onClick: () => async () => {
            if (maNb && tenNb && refDataKhuonMat.current) {
              showLoading();
              try {
                if (dataFACE_ID_IVIRSE_ON_OFF?.eval()) {
                  await dangKyKhuonMat({
                    id: maNb,
                    username: tenNb,
                    file: refDataKhuonMat.current,
                    Ivirsekey: dataIVIRSEKEY,
                  });
                  reTakePhoto();
                  tuDongXacThucKhuonMat();
                }
              } catch (e) {
                e.message && message.error(e.message);
              } finally {
                hideLoading();
              }
            }
          },
        };
        obj.param.autoDetect = autoDetect;
      }
    } else if (type === "changeFaceid") {
      obj.param.title = t("common.thayDoiAnhChupKhuonMat");
      try {
        if (dataFACE_ID_IVIRSE_ON_OFF?.eval()) {
          await getToken({
            username: dataTAI_KHOAN_QUAN_TRI_FACEID,
            password: dataMAT_KHAU_QUAN_TRI_FACEID,
          });
          const res = await getKhuonMatCu({ id: maNb });
          if (res) {
            obj.param.imageCompare = res;
            obj.uploadFunc = async (file) => {
              refConfirm.current &&
                refConfirm.current.show(
                  {
                    title: t("common.canhBao"),
                    content: t("common.banCoMuonThayDoiAnhChupKhuonMat"),
                    cancelText: t("common.quayLai"),
                    okText: t("common.dongY"),
                    classNameOkText: "button-warning",
                    showBtnOk: true,
                    typeModal: "warning",
                  },
                  async () => {
                    showLoading();
                    try {
                      await xoaKhuonMat({ id: maNb });
                      await dangKyKhuonMat({
                        id: maNb,
                        username: tenNb,
                        file: refDataKhuonMat.current,
                        Ivirsekey: dataIVIRSEKEY,
                      });
                      const res = await fileProvider.uploadImage({
                        file: refDataKhuonMat.current,
                        type: "anhDaiDien",
                      });
                      await onUpdate({ id, anhDaiDien: res?.data });
                      await getByTiepDonId(id);
                      message.success(
                        t("common.thayDoiAnhChupKhuonMatThanhCong")
                      );
                      onHideCamera();
                    } catch (e) {
                      e?.message && message.error(e.message);
                    } finally {
                      hideLoading();
                    }
                  }
                );
            };
          }
        }
      } catch (e) {
        obj.isShowModal = false;
        e?.message && message.error(e.message);
      }
    }
    if (obj.isShowModal) {
      obj.param.handleTake = handleTake;
      onShowCamera(obj.param, obj.uploadFunc);
    }
  };

  const onShowFolderAndFile = () => {
    refModalFoderAndFile.current &&
      refModalFoderAndFile.current.show({
        nbThongTinId: nbThongTinId,
      });
  };

  const onDongBoGia = () => {
    const onSubmit = () => {
      showLoading();
      nbDotDieuTriProvider
        .dongBoDuLieuGoiMo({
          dsId: [id],
        })
        .then((res) => {
          if (res?.code === 0) {
            message.success(t("danhMuc.dongBoGiaThanhCong"));
          } else {
            message.error(t("danhMuc.dongBoGiaThatBai"));
          }
        })
        .catch((err) => {
          message.error(err?.message || t("danhMuc.dongBoGiaThatBai"));
        })
        .finally(() => {
          hideLoading();
        });
    };
    showConfirm(
      {
        title: t("common.xacNhan"),
        content: t("common.banCoChacMuonDongBoGia", {
          tenNb: tenNb,
        }),
        onOk: onSubmit,
        okText: t("common.dongY"),
        cancelText: t("common.huy"),
        classNameOkText: "button-warning",
        showImg: true,
        showBtnOk: true,
        typeModal: "warning",
      },
      () => {
        onSubmit();
      }
    );
  };

  useEffect(() => {
    const element = refContainer.current;
    if (!element) return;

    const resizeObserver = new ResizeObserver(([entry]) => {
      setIsSmall(entry.contentRect.width < 1050);
    });

    resizeObserver.observe(element);

    return () => resizeObserver.disconnect(); // Cleanup observer
  }, []);

  return (
    <Main ref={refContainer} className="info">
      <DropdownStyle />
      <div className="avatar-header">
        {/* <div className="order">{addPrefixNumberZero(stt, LENGTH_ZERO_PREFIX)}</div> */}
        <div className="avatar">
          {hangThe && hangThe?.icon && (
            <div className="hangTheIcon">
              <GlobalStyle />
              <PopoverWrapper
                overlayClassName="hangThe"
                openClassName="hangThe"
                content={`${hangThe?.ten}`}
                placement="right"
                trigger="hover"
              >
                <img
                  src={`${fileUtils.absoluteFileUrl(hangThe?.icon)}`}
                  alt=""
                />
              </PopoverWrapper>
            </div>
          )}
          {anhDaiDien ? (
            <Image
              src={anhDaiDien}
              defauleImage={require("assets/images/welcome/avatar.png")}
              onClick={onShowAnhDaiDien}
            />
          ) : (
            <Avatar
              icon={<UserOutlined />}
              size={96}
              shape={"square"}
              style={{
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
              }}
            />
          )}
          {!!dataFACE_ID_IVIRSE_ON_OFF?.eval() && (
            <Dropdown
              overlay={menu}
              trigger={"hover"}
              className="more-action-avatar"
            >
              <SVG.IcMore
                style={{
                  position: "absolute",
                  top: "6px",
                  right: "2px",
                  backgroundColor: "#ffffff",
                  borderRadius: "12px",
                  padding: "2px",
                }}
              />
            </Dropdown>
          )}
        </div>
      </div>
      <div className="body-info">
        <div className="title-header">
          <div className="name text-fullname">
            {tiemChung && (
              <div className="maTiemChung">
                {`${t("tiemChung.maTiemChung")}: ${
                  nbTiemChung?.maTiemChung || " "
                }`}
              </div>
            )}
            {tiemChung && " - "}
            {addPrefixNumberZero(stt, LENGTH_ZERO_PREFIX)}
            {stt && " - "}
            {tenNb}
            {gioiTinh ? (
              <span className="gender">
                ({checkData(gioiTinh, listGioiTinh).ten})
              </span>
            ) : null}
            {isArray(thongTinBenhNhan?.dsPhanLoaiNbId, true) && (
              <PhanLoaiNguoiBenh thongTinBenhNhan={thongTinBenhNhan} />
            )}
          </div>
          <div className="button">
            <AuthWrapper
              accessRoles={[
                ROLES["TIEP_DON"].XEM_LAI_TT,
                ROLES["TIEP_DON"].CHINH_SUA_TT_NB_AN_CHECK_BOX_CAP_CUU,
              ]}
            >
              <div onClick={onEdit}>
                {!isSmall && t("tiepDon.suaThongTin")}
                <Tooltip placement="top" title={t("tiepDon.suaThongTin")}>
                  <SVG.IcEdit className="icon" />
                </Tooltip>
              </div>
            </AuthWrapper>
            <AuthWrapper accessRoles={[ROLES["TIEP_DON"].XEM_LAI_TT]}>
              <>
                <div onClick={() => onShowScanBieuMau()}>
                  {!isSmall && t("common.scanBieuMauHsba")}
                  <Tooltip
                    title={t("common.scanBieuMauHsba")}
                    placement="bottom"
                  >
                    <SVG.IcScanBieuMau
                      color={"var(--color-blue-primary)"}
                      className="icon"
                      style={{ width: "22px", height: "22px" }}
                    />
                  </Tooltip>
                </div>
                <div onClick={() => onShowHoSoBenhAn()}>
                  {!isSmall && t("quanLyNoiTru.xemHoSoBenhAn")}
                  <Tooltip title={t("quanLyNoiTru.xemHoSoBenhAn")}>
                    <SVG.IcHsba
                      className="icon"
                      style={{ width: "22px", height: "22px" }}
                    />
                  </Tooltip>
                </div>
                <div onClick={() => onShowLichSuSinhHieu()}>
                  {!isSmall && t("tiepDon.xemLichSuSinhHieu")}
                  <Tooltip title={t("tiepDon.xemLichSuSinhHieu")}>
                    <SVG.IcLichSu
                      className="icon"
                      style={{ width: "22px", height: "22px" }}
                    />
                  </Tooltip>
                </div>
                <div onClick={onShowFolderAndFile}>
                  {!isSmall && t("common.thuMuc")}
                  <Tooltip title={t("common.thuMuc")}>
                    <SVG.IcFolderUpload
                      className="icon"
                      style={{ width: "22px", height: "22px" }}
                    />
                  </Tooltip>
                </div>
                <div onClick={onDongBoGia}>
                  {!isSmall && t("khamSucKhoe.dongBoGia")}
                  <Tooltip
                    title={t("khamSucKhoe.dongBoGia")}
                    placement="topLeft"
                  >
                    <SVG.IcDongBoGia
                      className="icon"
                      style={{ width: "22px", height: "22px" }}
                    />
                  </Tooltip>
                </div>
              </>
            </AuthWrapper>
          </div>
        </div>
        <ThongTinNb screen={screenThongTinNb} isLoading={isLoadingTtNb} />
      </div>
      <ModalChinhSuaThongTin ref={refModalChinhSuaThongTin} />
      <ModalThemMoiGoi ref={refModalThemMoiGoi} />
      <ModalDanhSachBieuMauScan
        ref={refModalDanhSachBieuMauScan}
      ></ModalDanhSachBieuMauScan>
      <ModalHoSoBenhAn ref={refModalHoSoBenhAn} />
      <ModalAnhDaiDien ref={refAnhDaiDien} />
      <ModalLichSuSinhHieu ref={refModalLichSuSinhHieu} />
      <ModalFolderAndFile ref={refModalFoderAndFile} />
    </Main>
  );
};

export default memo(ThongTinBN);
