import { useEffect, useState, useMemo } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import { getState } from "redux-store/stores";
import moment, { isMoment } from "moment";
import { message } from "antd";
import { isEmpty } from "lodash";
import { useListAll, useStore, useThietLap } from "hooks";
import { HUONG_DIEU_TRI_KHAM, THIET_LAP_CHUNG } from "constants/index";
import { DEFAULT_CHAN_DOAN_KSK } from "../../configs";
import { isArray } from "utils";
import { useParams } from "react-router-dom";

export const useKetLuanKham = ({
  refDataKetLuan,
  refKetLuanKham,
  handleInitRef,
}) => {
  window.refDataKetLuan = refDataKetLuan;
  const { t } = useTranslation();
  const [ketLuanKhamObject, _setKetLuanKhamObject] = useState({});
  const infoNb = useStore("khamBenh.infoNb", {});
  const thongTinKSK = useStore("nbDichVuKhamKSK.thongTinKSK", {});
  const { huongDieuTri, ketQuaDieuTri, denKhoaId } = useStore(
    "khamBenh.thongTinChiTiet.nbKetLuan",
    {}
  );
  const thongTinChiTiet = useStore("khamBenh.thongTinChiTiet", {});
  const { thoiGianKetLuan } = thongTinChiTiet?.nbKetLuan || {};
  const [listAllMaBenh] = useListAll("maBenh", {}, true);

  const BAT_BUOC_NGUOI_BAO_LANH_KHI_NHAP_VIEN = useThietLap(
    THIET_LAP_CHUNG.BAT_BUOC_NGUOI_BAO_LANH_KHI_NHAP_VIEN
  )[0]?.eval();
  const [dataHIEN_THI_SINH_HIEU_TU_TIEP_DON_VAO_KHAM_BENH] = useThietLap(
    THIET_LAP_CHUNG.HIEN_THI_SINH_HIEU_TU_TIEP_DON_VAO_KHAM_BENH
  );

  const { phongThucHienId } = useParams();

  const setKetLuanKhamObject = (payload) => {
    _setKetLuanKhamObject((prev) => ({ ...prev, ...payload }));
    refKetLuanKham.current = { ...refKetLuanKham.current, ...payload };
  };
  const {
    khamBenh: {
      ketLuanKham: onKetLuanKham,
      updateNbKhamChuyenKhoaTmh,
      updateNbKhamChuyenKhoaCmu,
      updateNbKhamChuyenKhoaRhm,
      updateNbKhamChuyenKhoaDaLieu,
      updateNbKhamChuyenKhoaKhamNgoai,
      updateNbKhamChuyenKhoaSan,
      updateNbKhamChuyenKhoaKhamNam,
    },
    danhSachLichHen: { suaLichHenKham },
    doThiLuc: { updateThongTinChungMat },
  } = useDispatch();

  const kskChanDoanIds = useMemo(() => {
    const _cdIds =
      listAllMaBenh.find((x) => x.ma == DEFAULT_CHAN_DOAN_KSK)?.id || null;

    return _cdIds ? [_cdIds] : [];
  }, [listAllMaBenh]);

  useEffect(() => {
    setKetLuanKhamObject({
      keyHuongDieuTri: huongDieuTri,
      keyKetQua: ketQuaDieuTri,
      denKhoaId: denKhoaId,
    });
  }, [huongDieuTri, ketQuaDieuTri, denKhoaId]);

  const isKsk = useMemo(() => {
    return infoNb?.khamSucKhoe || infoNb?.loaiDoiTuongKsk;
  }, [infoNb]);

  useEffect(() => {
    if (
      !infoNb.id ||
      !thongTinChiTiet?.nbDotDieuTriId ||
      thongTinChiTiet?.nbDotDieuTriId != infoNb.id
    ) {
      handleInitRef(null, true);
    } else {
      let _thongTinChiTiet = thongTinChiTiet;
      //nếu là ksk thì mặc định chẩn đoán chính là Z00
      if (isKsk && !_thongTinChiTiet.nbChanDoan?.dsCdChinhId)
        _thongTinChiTiet = {
          ..._thongTinChiTiet,
          nbChanDoan: {
            ..._thongTinChiTiet.nbChanDoan,
            dsCdChinhId: kskChanDoanIds,
          },
        };
      //nếu là ksk thì mặc định chẩn đoán chính là Z00
      if (isKsk && thongTinKSK)
        _thongTinChiTiet = {
          ..._thongTinChiTiet,
          nbKSK: thongTinKSK,
        };

      handleInitRef(_thongTinChiTiet, false);
    }
  }, [thongTinChiTiet, kskChanDoanIds, thongTinKSK, infoNb.id, isKsk]);

  const handleSetDataKetLuan = (arrKey) => (e) => {
    const value = e?.currentTarget ? e.currentTarget.innerHTML : e;
    const [key1, key2] = arrKey;
    if (key2) {
      refDataKetLuan.current = {
        ...refDataKetLuan.current,
        [key1]: refDataKetLuan.current[key1]
          ? {
              ...refDataKetLuan.current[key1],
              [key2]: value,
            }
          : { [key2]: value },
      };
    } else {
      refDataKetLuan.current = {
        ...refDataKetLuan.current,
        [key1]: value,
      };
    }
  };

  const onSaveKetLuanKham = ({
    ketThuc,
    values = {},
    dongHoSo,
    isChuyenKhoa,
    onlyCallApi,
  }) => {
    const { bacSiKetLuanId, thoiGianKetLuan, nguoiPhienDichId } = values;
    return new Promise(async (resolve, reject) => {
      const {
        nbThongTinMat,
        nbChiTietKhamTmh,
        nbThongTinCmu,
        nbThongTinRhm,
        nbThongTinDaLieu,
        nbThongTinKhamNgoai,
        nbThongTinKhamSan,
        nbThongTinChuyenKhoaNam,
      } = refDataKetLuan.current;

      try {
        const bodyKetLuan = ketThuc
          ? {
              bacSiKetLuanId,
              thoiGianKetLuan,
              id: thongTinChiTiet.id,
              nguoiPhienDichId,
            }
          : getBodyKetLuan({
              bacSiKetLuanId,
              thoiGianKetLuan,
              nguoiPhienDichId,
            });
        if (!ketThuc) {
          bodyKetLuan.huongDieuTri = ketLuanKhamObject.keyHuongDieuTri;
          bodyKetLuan.ketQuaDieuTri = ketLuanKhamObject.keyKetQua;
          bodyKetLuan.denKhoaId = denKhoaId;
        }

        if (isChuyenKhoa) {
          bodyKetLuan.denKhoaId = refKetLuanKham.current.denKhoaId;
          bodyKetLuan.thoiGianKetLuan = moment().format("DD/MM/YYYY HH:mm:ss");
          bodyKetLuan.bacSiKetLuanId = getState().auth?.auth?.nhanVienId;
        }

        if (onlyCallApi) {
          bodyKetLuan.onlyCallApi = true;
        }

        if (bodyKetLuan?.huongDieuTri === HUONG_DIEU_TRI_KHAM.NHAP_VIEN) {
          let err = null;
          let _sdtNguoiBaoLanh =
            bodyKetLuan?.nbNhapVien?.sdtNguoiBaoLanh?.replaceAll(" ", "");
          switch (true) {
            case !bodyKetLuan?.nbNhapVien?.lyDoVaoVien:
              err = t("khamBenh.ketLuanKham.nhapVien.vuiLongNhapLyDoVaoVien");
              break;
            case !bodyKetLuan?.nbNhapVien?.tenNguoiBaoLanh?.trim() &&
              BAT_BUOC_NGUOI_BAO_LANH_KHI_NHAP_VIEN:
              err = t("khamBenh.vuiLongNhapTenNguoiBaoLanh");
              break;
            case !bodyKetLuan?.nbNhapVien?.sdtNguoiBaoLanh?.trim() &&
              BAT_BUOC_NGUOI_BAO_LANH_KHI_NHAP_VIEN:
              err = t("khamBenh.vuiLongNhapSdtNguoiBaoLanh");
              break;
            case _sdtNguoiBaoLanh && !_sdtNguoiBaoLanh.isPhoneNumber():
              err = t("tiepDon.soDienThoaiSaiDinhDang");
              break;
            case !bodyKetLuan?.nbNhapVien?.khoaNhapVienId:
              err = t(
                "khamBenh.ketLuanKham.nhapVien.vuiLongNhapChoDieuTriTaiKhoa"
              );
              break;
            default:
              break;
          }
          if (err) {
            message.error(err);
            reject(err);
            return;
          }
        }

        if (
          bodyKetLuan?.huongDieuTri === HUONG_DIEU_TRI_KHAM.HEN_KHAM &&
          bodyKetLuan.thoiGianHenKham
        ) {
          const thoiGianHenKham = isMoment(bodyKetLuan.thoiGianHenKham)
            ? bodyKetLuan.thoiGianHenKham
            : moment(bodyKetLuan.thoiGianHenKham);
          if (thoiGianHenKham.isBefore(moment(thoiGianKetLuan).endOf("day"))) {
            let err = t("khamBenh.thoiGianHenKhamPhaiLonHonThoiGianKetLuan");
            message.error(err);
            reject(err);
            return;
          }
        }

        if (nbThongTinMat) {
          const { matPhaiTtChung, matTraiTtChung, ...rest } = nbThongTinMat;
          let params = {
            id: thongTinChiTiet?.id,
            ...rest,
            matPhaiTtChung: {
              ...matPhaiTtChung,
              ...(matPhaiTtChung.hasOwnProperty("dsCdChinhId") && {
                dsCdChinhId: matPhaiTtChung.dsCdChinhId
                  ? !isArray(matPhaiTtChung.dsCdChinhId, true)
                    ? [matPhaiTtChung.dsCdChinhId]
                    : matPhaiTtChung.dsCdChinhId
                  : null,
              }),
            },
            matTraiTtChung: {
              ...matTraiTtChung,
              ...(matTraiTtChung.hasOwnProperty("dsCdChinhId") && {
                dsCdChinhId: matTraiTtChung.dsCdChinhId
                  ? !isArray(matTraiTtChung.dsCdChinhId, true)
                    ? [matTraiTtChung.dsCdChinhId]
                    : matTraiTtChung.dsCdChinhId
                  : null,
              }),
            },
          };
          updateThongTinChungMat(params);
        }
        if (nbChiTietKhamTmh) {
          updateNbKhamChuyenKhoaTmh({
            id: thongTinChiTiet?.id,
            ...nbChiTietKhamTmh,
          });
        }

        if (nbThongTinCmu) {
          updateNbKhamChuyenKhoaCmu({
            id: thongTinChiTiet?.id,
            ...nbThongTinCmu,
          });
        }

        if (nbThongTinKhamSan) {
          updateNbKhamChuyenKhoaSan({
            id: thongTinChiTiet?.id,
            ...nbThongTinKhamSan,
          });
        }
        if (nbThongTinRhm) {
          updateNbKhamChuyenKhoaRhm({
            id: thongTinChiTiet?.id,
            ...nbThongTinRhm,
          });
        }
        if (nbThongTinDaLieu) {
          updateNbKhamChuyenKhoaDaLieu({
            id: thongTinChiTiet?.id,
            ...nbThongTinDaLieu,
          });
        }
        if (nbThongTinKhamNgoai) {
          updateNbKhamChuyenKhoaKhamNgoai({
            id: thongTinChiTiet?.id,
            ...nbThongTinKhamNgoai,
          });
        }
        if (nbThongTinChuyenKhoaNam) {
          updateNbKhamChuyenKhoaKhamNam({
            id: thongTinChiTiet?.id,
            ...nbThongTinChuyenKhoaNam,
          });
        }

        onKetLuanKham(bodyKetLuan, ketThuc)
          .then((s) => {
            resolve(s);
            let payload = {
              dsDichVuId: [thongTinChiTiet?.nbDichVu?.dichVuId],
              thoiGianHen: bodyKetLuan.thoiGianHenKham,
              loai: 10,
              nbDotDieuTriId: thongTinChiTiet.nbDotDieuTriId,
              huongDieuTri: bodyKetLuan.huongDieuTri,
            };
            if (!dongHoSo) suaLichHenKham(payload);
          })
          .catch((e) => {
            reject(e);
          });
      } catch (err) {
        reject(err);
      }
    });
  };

  const getBodyKetLuan = (payload = {}) => {
    const { keyHuongDieuTri, keyKetQua, ghiChu, thongTinTheoDoi } =
      refKetLuanKham.current;
    const {
      nbChuyenVien,
      nbNhapVien,
      nbKetLuan,
      nbHoiBenh,
      nbChanDoan,
      nbKhamXet,
      nbChiSoSong,
      nguoiUyQuyenId,
    } = refDataKetLuan.current;
    let bodyKetLuan = {
      id: thongTinChiTiet.id,
      huongDieuTri: keyHuongDieuTri,
      ketQuaDieuTri: keyKetQua,
      thoiGianKetLuan:
        nbKetLuan?.thoiGianKetLuan &&
        moment(nbKetLuan?.thoiGianKetLuan).format("DD/MM/YYYY HH:mm:ss"),
      phongHenKhamId: nbKetLuan?.phongHenKhamId || Number(phongThucHienId), //Xử lý nếu không có thông tin của  phongHenKhamId thì lấy thông tin của phongThucHienId
      loiDan: nbKetLuan?.loiDan,
      denKhoaId,
      dsChanDoanId: thongTinChiTiet?.nbChanDoan?.dsCdChinhId,
      nguoiUyQuyenId,
    };
    switch (keyHuongDieuTri) {
      case HUONG_DIEU_TRI_KHAM.CHO_VE:
        return bodyKetLuan;
      case HUONG_DIEU_TRI_KHAM.HEN_KHAM:
        bodyKetLuan = {
          ghiChu: nbKhamXet?.ghiChu,
          loiDan: nbKetLuan?.loiDan,
          quaTrinhBenhLy: nbHoiBenh?.quaTrinhBenhLy,
          thoiGianHenKham: nbKetLuan?.thoiGianHenKham,
          ...bodyKetLuan,
          nguoiUyQuyenId:
            bodyKetLuan?.nguoiUyQuyenId ?? nbKetLuan?.nguoiUyQuyenId,
        };
        return bodyKetLuan;
      case HUONG_DIEU_TRI_KHAM.CHUYEN_VIEN:
      case HUONG_DIEU_TRI_KHAM.CHUYEN_VIEN_THEO_YEU_CAU:
        bodyKetLuan.dsChanDoanId =
          nbChanDoan?.dsCdChinhId ||
          (nbChanDoan?.dsCdChinh || []).map((item) => item.id);
        if (nbChuyenVien) {
          bodyKetLuan.nbChuyenVien = nbChuyenVien;
        } else {
          bodyKetLuan.nbChuyenVien = {};
        }
        if (!bodyKetLuan.nbChuyenVien.hasOwnProperty("ngheNghiepId")) {
          bodyKetLuan.nbChuyenVien.ngheNghiepId = infoNb.ngheNghiepId;
        }
        if (!bodyKetLuan.nbChuyenVien.lyDoChuyenTuyen)
          bodyKetLuan.nbChuyenVien.lyDoChuyenTuyen = 1;
        if (
          bodyKetLuan.nbChuyenVien.lyDoChuyenTuyen === 1 &&
          !bodyKetLuan.nbChuyenVien.duDieuKienChuyenTuyen
        ) {
          throw "Validate failed";
        }
        bodyKetLuan = {
          ...bodyKetLuan,
          nbChuyenVien: {
            ...bodyKetLuan.nbChuyenVien,
          },
        };

        return bodyKetLuan;
      case HUONG_DIEU_TRI_KHAM.NHAP_VIEN:
        bodyKetLuan.dsChanDoanId =
          nbChanDoan?.dsCdChinhId ||
          (nbChanDoan?.dsCdChinh || []).map((item) => item.id);
        if (nbNhapVien) {
          const {
            id,
            donViYTeId,
            coSoYTeId,
            nbDotDieuTriId,
            ...newNbNhapVien
          } = nbNhapVien;
          bodyKetLuan.nbNhapVien = newNbNhapVien;
        } else {
          bodyKetLuan.nbNhapVien = {};
        }
        const {
          id,
          active,
          createdAt,
          updatedAt,
          createdBy,
          updatedBy,
          ...newNbChiSoSong
        } = nbChiSoSong || {};

        bodyKetLuan = {
          loiDan: nbNhapVien?.loiDan,
          ghiChu: nbKhamXet?.ghiChu,
          // lyDoDenKham: nbNhapVien?.loiDan || infoNb?.lyDoDenKham,
          quaTrinhBenhLy: nbHoiBenh?.quaTrinhBenhLy,
          ...bodyKetLuan,
          nbNhapVien: {
            ...bodyKetLuan.nbNhapVien,
            danTocId: nbNhapVien?.danTocId,
            cacBoPhan: nbKhamXet?.cacBoPhan,
            toanThan: nbKhamXet?.toanThan,
            tienSuGiaDinh: nbHoiBenh?.tienSuGiaDinh,
            tienSuBanThan: nbHoiBenh?.tienSuBanThan,
            nbChiSoSong: isEmpty(newNbChiSoSong)
              ? null
              : {
                  id:
                    thongTinChiTiet?.id === newNbChiSoSong?.chiDinhTuDichVuId ||
                    dataHIEN_THI_SINH_HIEU_TU_TIEP_DON_VAO_KHAM_BENH?.eval()
                      ? id
                      : null,
                  ...newNbChiSoSong,
                },
          },
        };
        return bodyKetLuan;
      case HUONG_DIEU_TRI_KHAM.KHONG_KHAM:
        bodyKetLuan.dsChanDoanId =
          nbChanDoan?.dsCdChinhId ||
          (nbChanDoan?.dsCdChinh || []).map((item) => item.id);
        return bodyKetLuan;
      case HUONG_DIEU_TRI_KHAM.CHUYEN_KHAM_CHUYEN_SAU:
        return {
          ...bodyKetLuan,
          ghiChu: ghiChu || thongTinChiTiet?.nbDichVu?.ghiChu || "",
        };
      case HUONG_DIEU_TRI_KHAM.THEO_DOI_NGUOI_BENH:
        return {
          ...bodyKetLuan,
          thongTinTheoDoi:
            thongTinTheoDoi ||
            thongTinChiTiet?.nbKetLuan?.thongTinTheoDoi ||
            "",
        };

      default:
        break;
    }
    return bodyKetLuan;
  };

  return {
    ketLuanKhamObject,
    setKetLuanKhamObject,
    onSaveKetLuanKham,
    handleSetDataKetLuan,
  };
};
