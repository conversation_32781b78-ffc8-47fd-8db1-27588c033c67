import React, {
  useImperativeHandle,
  forwardRef,
  useRef,
  useEffect,
} from "react";
import IcDot from "assets/images/khamBenh/icDot.svg";
import { NavigationWrapper, Main } from "./styled";
import { useSelector, useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import { useStore, useThietLap, useConfirm } from "hooks";
import { THIET_LAP_CHUNG } from "constants/index";
const Navigation = (
  {
    title,
    icon,
    dataChild,
    color,
    onActive,
    itemKey,
    trangThai,
    onClickChild,
    layerId,
    activeKey,
    collapse,
  },
  ref
) => {
  const { t } = useTranslation();
  const refFunOnClick = useRef(null);
  // const refLink = useRef(null);
  // const activeKey = useSelector((state) => state.khamBenh.activeKey);
  const refActiveKey = useRef(null); //lưu giá trị active key để tr<PERSON>h bị click nhiều lần
  const { showConfirm } = useConfirm();

  const [dataKHONG_CHI_DINH_DV_NEU_KHONG_SANG_LOC_DINH_DUONG_NGOAI_TRU] =
    useThietLap(
      THIET_LAP_CHUNG.KHONG_CHI_DINH_DV_NEU_KHONG_SANG_LOC_DINH_DUONG_NGOAI_TRU
    );

  const trangThaiKham = useSelector(
    (state) => state.khamBenh.thongTinChiTiet?.nbDvKyThuat?.trangThai
  );
  const infoNb = useStore("khamBenh.infoNb", {});
  const thongTinBenhNhanTongHop = useStore(
    "nbDotDieuTri.thongTinBenhNhanTongHop",
    {}
  );
  const { khamSucKhoe, trangThaiKsk } = infoNb || {};
  const { onRegisterHotkey } = useDispatch().phimTat;

  const isDisableTab =
    dataKHONG_CHI_DINH_DV_NEU_KHONG_SANG_LOC_DINH_DUONG_NGOAI_TRU?.eval() &&
    thongTinBenhNhanTongHop?.trangThaiSangLocDd === 10 &&
    ["1", "4"].includes(itemKey);

  const onSetActive = (key, element, onClick) => {
    if (itemKey != refActiveKey.current) {
      refActiveKey.current = key;

      onActive(key, onClick);
    }
  };

  useImperativeHandle(ref, () => ({
    setActive: (active) => {
      // setTimeout(() => {
      //   if (refLink.current?.parentNode?.classList) {
      //     if (!active) refLink.current.parentNode.classList.remove("active");
      //     else refLink.current.parentNode.classList.add("active");
      //   }
      // }, 500);
    },
  }));

  useEffect(() => {
    let key = null;
    switch (itemKey + "") {
      case "0":
        key = 119; // F8
        break;
      // case "1":
      //   key = 120; //F9
      //   break;
      case "2":
        key = 121; // F10
        break;
      // case "3":
      //   key = 122; //F11
      // break;
      // case "4":
      //   key = 123; //F12
      // break;
    }
    if (key) {
      onRegisterHotkey({
        layerId,
        hotKeys: [
          {
            keyCode: key,
            onEvent: () => {
              refFunOnClick.current && refFunOnClick.current();
            },
          },
        ],
      });
    }
  }, []);

  useEffect(() => {
    refActiveKey.current = activeKey;
  }, [activeKey]);

  const onClick = (e) => {
    if (khamSucKhoe && trangThaiKsk == 10) return;
    if (!trangThaiKham || trangThaiKham < trangThai) return;
    if (isDisableTab) {
      showConfirm({
        title: t("common.canhBao"),
        content: t("khamBenh.nguoiBenhChuaDuocSangLocDinhDuong"),
        cancelText: t("common.quayLai"),
        typeModal: "warning",
      });
      return;
    }
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }

    e = e?.nativeEvent ?? e;
    const target = e?.target;
    if (target) {
      console.log("[USER CLICK]", {
        target: {
          tagName: target.tagName,
          innerText: (target.innerText || "").substring(0, 200),
          value: target.value || "",
          id: target.id,
          class: target.className,
        },
        position: { x: e.x, y: e.y },
        shiftKey: e.shiftKey,
        ctrlKey: e.ctrlKey,
        altKey: e.altKey,
        isTrusted: e.isTrusted,
      });
    }

    onSetActive(itemKey, null, true);
  };
  refFunOnClick.current = onClick;

  return (
    <NavigationWrapper
      trangThai={trangThai}
      trangThaiKham={trangThaiKham}
      isDisableTab={isDisableTab}
    >
      <Main
        className={`nav-item ${activeKey == itemKey ? "active" : ""}`}
        color={color}
        onClick={onClick}
        itemKey={itemKey}
        activeKey={activeKey}
        collapse={collapse}
        isDisable={trangThaiKham < trangThai || isDisableTab}
      >
        {collapse ? (
          title
        ) : (
          <>
            {icon}
            <div className="content">
              <div className="content--title">{title}</div>
              <div className="content--child">
                {dataChild.map((item, index) => {
                  return (
                    <div
                      className="content--item"
                      key={`${index}-${item.title}`}
                      onClick={() => onClickChild(item)}
                    >
                      <IcDot />
                      {t(item.i18n)}
                    </div>
                  );
                })}
              </div>
            </div>
          </>
        )}
      </Main>
    </NavigationWrapper>
  );
};

export default forwardRef(Navigation);
