import styled, { css } from "styled-components";

export const NavigationWrapper = styled.div`
  cursor: ${(props) =>
    props.trangThaiKham < props.trangThai ? "not-allowed" : "pointer"};

  ${(props) =>
    props.trangThaiKham < props.trangThai
      ? `
        pointer-events: auto; 
        & * {
          pointer-events: none;
        }
      `
      : ""}
`;

export const Main = styled.div`
  ${(props) =>
    props?.collapse
      ? css`
          padding: 10px;
          color: #000;
          border: 1px solid rgba(23, 43, 77, 0.2);
          cursor: pointer;
          transform-origin: left center;
          white-space: nowrap;
          writing-mode: vertical-rl;
          text-align: center;
          width: 40px;
          font-weight: 600;
          font-size: 12px;
          margin-top: 16px;
          border-left: none;
        `
      : ""};
  //

  opacity: 0.5;
  min-height: 50px;
  display: flex;
  align-items: center;
  border-radius: ${(props) =>
    props.collapse ? "0px 4px 4px 0px;" : "0px 16px 16px 0px;"};
  background-color: ${(props) =>
    props.collapse ? (props.isDisable ? `#D9DEE5` : `#ffffff`) : props.color};
  &.active {
    ${(props) =>
      props?.collapse ? `background-color: #0762f7; color: #ffffff` : ""};
    opacity: 1;
    pointer-events: auto;
  }
  padding: 8px;
  ${(props) => (props ? `${props.padding - 5}px` : "15px")};

  width: 100%;
  color: ${(props) => (props.collapse ? "rgba(100, 116, 139, 1)" : "#ffffff")};
  display: flex;
  cursor: pointer;
  &:hover {
    background: ${(props) => (props.collapse ? "#0762F7" : "#ffffff")};
    color: ${(props) => (props.collapse ? "#ffffff" : " #172b4d")};
    > svg {
      fill: ${(props) => props.color};
      path {
        fill: ${(props) => props.color} !important;
      }
      &.ic-phac-do-dieu-tri {
        path:nth-child(3) {
          fill: white !important;
        }
      }
      &.ic-dieu-tri-lao {
        path {
          fill: ${(props) => props.color};
          stroke: ${(props) => props.color};
        }
        path:nth-child(4) {
          stroke: ${(props) => props.color};
        }
      }
    }
    .content {
      &--title {
        color: #172b4d;
      }
      &--item {
        > svg {
          fill: #172b4d;
        }
      }
    }
  }
  > svg {
    width: 64px;
    height: 64px;
    fill: #ffffff;
    margin-right: 10px;
    &.ic-phac-do-dieu-tri {
      path {
        fill: white;
      }
      path:nth-child(3) {
        fill: ${(props) => props.color};
      }
    }
    &.ic-dieu-tri-lao {
      path {
        fill: white;
        stroke: white;
      }
      path:nth-child(4) {
        stroke: white;
      }
    }
    @media (max-width: 1368px) {
      width: 30px;
      height: 30px;
      margin-left: 8px;
    }
  }
  .content {
    &--title {
      font-weight: 900;
      font-size: 13px;
      line-height: 20px;
      color: #fff;
    }
    &--item {
      display: flex;
      align-items: center;
      cursor: pointer;
      font-weight: 600;
      font-size: 13px;
      line-height: 20px;
      @media (max-width: 1368px) {
        line-height: 18px;
      }
      > svg {
        width: 8px;
        height: 8px;
        fill: #ffffff;
        margin-right: 8px;
      }
    }
  }
  margin-bottom: 8px;
  @media (max-width: 1368px) {
    margin-bottom: 2px;
  }
`;
