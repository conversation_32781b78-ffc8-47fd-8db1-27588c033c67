import React, { memo, forwardRef } from "react";
import { useListAll, useStore } from "hooks";
import { useTranslation } from "react-i18next";
import { Col } from "antd";
import ListPhanLoaiNguoiBenh from "pages/tiepDon/DanhSachNB/DaTiepDon/components/ListPhanLoaiNguoiBenh";

const PhanLoaiNb = ({ fromSetting, index, ...props }, ref) => {
  const { t } = useTranslation();
  const { dsPhanLoaiNbId } = useStore("khamBenh.infoNb");
  const [listAllPhanLoaiNB] = useListAll("phanLoaiNB", {}, true);

  const listPhanLoaiNb = (dsPhanLoaiNbId || []).map((id) => {
    const item = listAllPhanLoaiNB.find((x) => x.id === id);
    return item || { id, ten: "", mauChu: "", mauNen: "" };
  });

  return (
    <Col {...props} ref={ref}>
      <div className="info-profile">
        {index}. {t("khamBenh.hanhChinh.phanLoaiNguoiBenh")}:
        <ListPhanLoaiNguoiBenh
          value={dsPhanLoaiNbId?.slice(0, 2)}
          listAll={listAllPhanLoaiNB}
          allTags={listPhanLoaiNb}
        />
      </div>
    </Col>
  );
};
export default memo(forwardRef(PhanLoaiNb));
