import React, { memo, forwardRef } from "react";
import { useListAll, useStore } from "hooks";
import { useTranslation } from "react-i18next";
import { Col } from "antd";

const PhanLoaiNb = ({ fromSetting, index, ...props }, ref) => {
  const { t } = useTranslation();
  const { dsPhanLoaiNbId } = useStore("khamBenh.infoNb");
  const [listAllPhanLoaiNB] = useListAll("phanLoaiNB", {}, true);

  const listPhanLoaiNb = (dsPhanLoaiNbId || []).map((id) => {
    const item = listAllPhanLoaiNB.find((x) => x.id === id);
    return item || { id, ten: "", mauChu: "", mauNen: "" };
  });

  const displayTags = listPhanLoaiNb.slice(0, 2);
  return (
    <Col {...props} ref={ref}>
      <div className="info-profile">
        {index}. {t("khamBenh.hanhChinh.phanLoaiNguoiBenh")}:
        {displayTags.map((tag, index) => (
          <span
            key={tag.id}
            style={{
              background: tag.mauNen,
              color: tag.mauChu,
              padding: "0 3px",
              margin: "0 3px",
              borderRadius: "2px",
              fontSize: "12px",
              whiteSpace: "nowrap",
            }}
            title={listPhanLoaiNb.map((item) => item.ten).join(", ")}
          >
            {tag.ten}
          </span>
        ))}
      </div>
    </Col>
  );
};
export default memo(forwardRef(PhanLoaiNb));
