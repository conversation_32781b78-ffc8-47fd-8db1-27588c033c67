import React, { useState, useEffect, useRef, useMemo } from "react";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import { orderBy, union } from "lodash";
import { useConfirm, useStore, useEnum, useListAll, useThietLap } from "hooks";
import { TableWrapper, Tooltip, HeaderSearch, Checkbox } from "components";
import SuaThongTinThuoc from "../SuaThongTinThuoc";
import { isArray, roundNumberPoint } from "utils";
import ModalHoanThuocVTYT from "components/ModalHoanThuocVTYT";
import { CaretRightOutlined, CaretDownOutlined } from "@ant-design/icons";
import {
  DOI_TUONG,
  LOAI_DON_THUOC,
  TRANG_THAI_THUOC,
  ENUM,
  TRANG_THAI_HOAN,
  ROLES,
  LOAI_DICH_VU,
} from "constants/index";
import { SVG } from "assets";
import { TitleTable, Main } from "./styled";
import IcXemHdsd from "pages/khamBenh/components/Thuoc/IcXemHdsd";
import moment from "moment";
import ModalChiTietThuoc from "./ModalChiTietThuoc";
import { checkRole } from "lib-utils/role-utils";
import ModalNhapSoLuong from "../ModalNhapSoLuong";

const { Setting } = TableWrapper;

const TableDonThuoc = ({
  refreshData,
  expandedKeys,
  isKhamBenh = false,
  canEdit = true,
  isKeThuocRaVienTheoTungNgay = false,
  isNoiTru = false,
  modeThuoc,
  showTuNgayDenNgay = false,
  ...props
}) => {
  const { showConfirm } = useConfirm();
  const refSettings = useRef(null);
  const refModalHoanThuocVTYT = useRef(null);
  const refModalChiTietThuoc = useRef(null);
  const refModalNhapSoLuong = useRef(null);
  const [listAllKho] = useListAll("kho", {}, true);

  const { t } = useTranslation();
  const {
    chiDinhDichVuKho: { onDeleteDichVu, onDeleteDichVuNhaThuoc, onDeleteAll },
    khamBenh: { getStatisticsRoom },
    traHangHoa: { postDsDvThuocTraKho },
  } = useDispatch();

  const [state, _setState] = useState({
    visibleDelete: null,
  });
  const setState = (data) => {
    _setState({
      ...state,
      ...data,
    });
  };
  const thongTinNguoiBenh = useStore(
    "chiDinhKhamBenh.configData.thongTinNguoiBenh"
  );
  const [listTrangThaiThuoc] = useEnum(ENUM.TRANG_THAI_THUOC);

  const refSuaThongTinThuoc = useRef(null);
  const {
    listDvThuoc,
    isTuTruc,
    khoBhyt,
    thuocNhaThuoc,
    onThemThuocDungKem,
    isShowHoanThuocVoiKhoKhacTuTruc,
  } = props;

  const gomThuocDonRaVien = () => {
    let _listDvThuoc = [];

    listDvThuoc.forEach((element) => {
      const _findIdx = _listDvThuoc.findIndex(
        (item) => item.dichVuId == element.dichVuId
      );
      if (_findIdx == -1) {
        _listDvThuoc.push({
          ...element,
          tuNgay: element.thoiGianThucHien,
          denNgay: element.thoiGianThucHien,
          dsThuocNhomIds: [element.id],
        });
      } else {
        _listDvThuoc[_findIdx].soLuong =
          _listDvThuoc[_findIdx].soLuong + element.soLuong;
        _listDvThuoc[_findIdx].soLuongSoCap =
          _listDvThuoc[_findIdx].soLuongSoCap + element.soLuongSoCap;
        _listDvThuoc[_findIdx].dsThuocNhomIds = [
          ..._listDvThuoc[_findIdx].dsThuocNhomIds,
          element.id,
        ];
        if (
          moment(element.thoiGianThucHien).isBefore(
            moment(_listDvThuoc[_findIdx].tuNgay)
          )
        ) {
          _listDvThuoc[_findIdx].tuNgay = element.thoiGianThucHien;
        }
        if (
          moment(element.thoiGianThucHien).isAfter(
            moment(_listDvThuoc[_findIdx].denNgay)
          )
        ) {
          _listDvThuoc[_findIdx].denNgay = element.thoiGianThucHien;
        }
      }
    });

    return _listDvThuoc;
  };

  const dataSource = useMemo(() => {
    let _listDvThuoc = listDvThuoc;
    if (isKeThuocRaVienTheoTungNgay) {
      _listDvThuoc = gomThuocDonRaVien();
    }
    const listThuocDungKem = _listDvThuoc.filter((x) => x.dungKemId);
    const listThuocCha = _listDvThuoc.filter((x) => !x.dungKemId);

    let data = [];
    let thuocChaIdCoDungKem = [];
    (listThuocCha || []).map((item, index) => {
      let thuocDungKem = (listThuocDungKem || []).filter(
        (x) =>
          x.dungKemId === item.id ||
          (item.dsThuocGopId || []).includes(x.dungKemId)
      );
      data.push({
        ...item,
        ...(isArray(thuocDungKem, 1) && { children: thuocDungKem }),
      });
      if (thuocDungKem.length > 0) {
        thuocChaIdCoDungKem.push(item.id);
      }
    });
    (listThuocDungKem || []).map((item, index) => {
      if (
        !(listThuocCha || [])
          .map((item2) => item2?.id)
          .includes(item?.dungKemId)
      ) {
        data.push(item);
      }
    });

    setState({ expandedKeys: thuocChaIdCoDungKem });
    return data
      .toSorted((a, b) => {
        if (a.sttHienThi != null && b.sttHienThi != null) {
          return a.sttHienThi - b.sttHienThi;
        }

        if (a.sttHienThi != null) return -1;
        if (b.sttHienThi != null) return 1;

        if (a.dungChoCon !== b.dungChoCon) {
          return a.dungChoCon ? 1 : -1;
        }

        return new Date(a.thoiGianChiDinh) - new Date(b.thoiGianChiDinh);
      })
      .map((item, index) => ({ ...item, index: index + 1 }));
  }, [listDvThuoc, isKeThuocRaVienTheoTungNgay]);

  useEffect(() => {
    if (expandedKeys) {
      setState({ expandedKeys: union(expandedKeys, state.expandedKeys) });
    }
  }, [expandedKeys]);

  const onDelete = (record) => {
    showConfirm(
      {
        title: t("common.xoaDuLieu"),
        content:
          t("common.banCoChacMuonXoa") +
          (record?.tenDichVu || record?.tenDichVu || "") +
          "?",
        cancelText: t("common.quayLai"),
        okText: t("common.dongY"),
        classNameOkText: "button-error",
        showImg: true,
        showBtnOk: true,
      },
      async () => {
        if (isKeThuocRaVienTheoTungNgay) {
          await onDeleteAll(record.dsThuocNhomIds);
          refreshData();
        } else if ((record.dsThuocGop || []).length > 1) {
          debugger;
          await onDeleteAll((record.dsThuocGop || []).map((x) => x.id));
          refreshData();
        } else if (thuocNhaThuoc) {
          await onDeleteDichVuNhaThuoc(record.id);
          refreshData(thuocNhaThuoc);
        } else {
          await onDeleteDichVu(record.id);
          refreshData();
        }

        getStatisticsRoom();
      }
    );
  };

  const onEdit = (record) => () => {
    refSuaThongTinThuoc.current &&
      refSuaThongTinThuoc.current.show(
        { data: record, isTuTruc, khoBhyt, thuocNhaThuoc: thuocNhaThuoc },
        () => {
          refreshData(thuocNhaThuoc);
        }
      );
  };

  const onHuyHoan = (data) => {
    if (refModalHoanThuocVTYT.current)
      refModalHoanThuocVTYT.current.show(
        [data],
        null,
        () => {
          refreshData(false);
        },
        null,
        {
          type: "huyHoan",
        }
      );
  };

  const onHoan = (data) => {
    if (refModalHoanThuocVTYT.current)
      refModalHoanThuocVTYT.current.show([data], null, () => {
        refreshData(false);
      });
  };

  const onNgungYLenh = (record) => async (e) => {
    refModalNhapSoLuong.current &&
      refModalNhapSoLuong.current.show({}, async (value) => {
        await postDsDvThuocTraKho({
          loaiHangHoa: LOAI_DICH_VU.THUOC,
          payload: [
            {
              nbDichVuId: record.id,
              soLuong: value,
            },
          ],
        });
        refreshData(false);
      });
  };

  const onCreate = (record) => {
    onThemThuocDungKem &&
      onThemThuocDungKem({
        loaiDonThuoc: thuocNhaThuoc
          ? LOAI_DON_THUOC.NHA_THUOC
          : record.khoId
          ? LOAI_DON_THUOC.THUOC_KHO
          : "",
        khoId: record.khoId,
        dungKemId: record.id,
      });
  };

  const renderStt = (value, row, index) => {
    const obj = {
      children: value,
      props: {},
    };
    if (row.isParent) {
      obj.props.rowSpan = row.rowSpan;
      obj.children = value;
    }
    return obj;
  };

  const onViewChiTiet = (record) => () => {
    refModalChiTietThuoc.current &&
      refModalChiTietThuoc.current.show({
        dsThuocNhomIds: record.dsThuocNhomIds,
      });
  };

  const columns = [
    {
      title: <HeaderSearch title={t("common.stt")} sort_key="index" />,
      width: 50,
      onCell: () => ({
        style: {
          minWidth: 50,
        },
      }),
      dataIndex: "index",
      key: "index",
      align: "center",
      render: renderStt,
    },
    {
      title: <HeaderSearch title={t("danhMuc.maThuoc")} />,
      width: 100,
      onCell: () => ({
        style: {
          minWidth: 50,
        },
      }),
      dataIndex: "maDichVu",
      key: "maDichVu",
      show: true,
      i18Name: "danhMuc.maThuoc",
      render: (item, record) => {
        return (
          <div className="flex align-items-center gap-4">
            <IcXemHdsd record={record} />
            <span>{item}</span>
          </div>
        );
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("khamBenh.donThuoc.tenThuocHamLuong")}
          sort_key="ten"
          // dataSort={dataSortColumn["tenThuoc"] || 0}
          // onClickSort={onClickSort}
        />
      ),
      width: 300,
      onCell: () => ({
        style: {
          minWidth: 160,
        },
      }),
      dataIndex: "",
      key: "",
      colSpan: 1,
      show: true,
      i18Name: "khamBenh.donThuoc.tenThuocHamLuong",
      render: (item, record) => {
        const ten =
          record?.tenDichVu || record?.thuocChiDinhNgoai.tenDichVu || "";
        const tenLieuDung = `${
          record?.tenLieuDung || record?.lieuDung?.ten || ""
        }`;
        const tenDuongDung = `${
          record?.tenDuongDung ? " - " + record?.tenDuongDung : ""
        }`;
        const tenCachDung = `${
          record?.cachDung ? " - " + record?.cachDung : ""
        }`;
        const content1 = `${tenLieuDung}${tenDuongDung}${tenCachDung}${
          tenLieuDung || tenDuongDung || tenCachDung ? `. ` : ""
        }`;
        return (
          <div>
            <span>{`${ten} ${
              record.tenHoatChat ? " (" + record.tenHoatChat + ")" : " "
            } ${record.hamLuong ? " - " + record.hamLuong : ""}`}</span>
            <br />
            <span style={{ fontSize: "12px" }}>
              {`${content1} `}
              {record.ghiChu ? `${t("common.luuY")}: ${record.ghiChu}` : ""}
            </span>
          </div>
        );
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("common.soLuong")}
          sort_key="soLuong"
          // dataSort={dataSortColumn["Số lượng"] || 0}
          // onClickSort={onClickSort}
        />
      ),
      width: 100,
      onCell: () => ({
        style: {
          minWidth: 50,
        },
      }),
      dataIndex: "soLuong",
      key: "soLuong",
      colSpan: 1,
      show: true,
      i18Name: "common.soLuong",
      render: (item, list) => {
        let soLuong = 0;
        if (isKhamBenh) {
          soLuong = roundNumberPoint(list?.soLuongSoCap, 6);
        } else {
          soLuong =
            list?.heSoDinhMuc > 1 ? Math.ceil(item / list?.heSoDinhMuc) : item;
        }
        const tenDvt =
          list?.heSoDinhMuc > 1
            ? list?.tenDvtSoCap || ""
            : list?.tenDonViTinh || "";

        return soLuong + ` ${tenDvt}`;
      },
    },
    {
      title: <HeaderSearch title={t("khamBenh.donThuoc.slTra")} />,
      dataIndex: "soLuongTra",
      width: 100,
      onCell: () => ({
        style: {
          minWidth: 50,
        },
      }),
      show: true,
      i18Name: "khamBenh.donThuoc.slTra",
      align: "center",
      render: (item, list) => {
        const tenDvt =
          list?.heSoDinhMuc > 1
            ? list?.tenDvtSoCap || ""
            : list?.tenDonViTinh || "";
        const soLuong =
          item > 0 && list?.heSoDinhMuc > 1
            ? Math.ceil(item / list?.heSoDinhMuc)
            : item;

        return soLuong > 0 ? soLuong + ` ${tenDvt}` : "";
      },
    },
    {
      title: <HeaderSearch title={t("khamBenh.donThuoc.soPhieuLinh")} />,
      dataIndex: "soPhieuLinh",
      width: 100,
      onCell: () => ({
        style: {
          minWidth: 50,
        },
      }),
      show: false,
      i18Name: "khamBenh.donThuoc.soPhieuLinh",
      align: "left",
    },
    {
      title: <HeaderSearch title={t("common.tuTra")} />,
      hidden: !isTuTruc && !khoBhyt,
      dataIndex: "tuTra",
      width: 50,
      onCell: () => ({
        style: {
          minWidth: 50,
        },
      }),
      show: false,
      i18Name: "common.tuTra",
      align: "center",
      render: (item) => {
        return <Checkbox checked={item} />;
      },
    },
    {
      title: <HeaderSearch title={t("common.khongTinhTien")} />,
      hidden: !isTuTruc && !khoBhyt,
      width: 50,
      onCell: () => ({
        style: {
          minWidth: 50,
        },
      }),
      dataIndex: "khongTinhTien",
      show: false,
      i18Name: "common.khongTinhTien",
      align: "center",
      render: (item) => {
        return <Checkbox checked={item} />;
      },
    },
    {
      title: <HeaderSearch title={t("common.tt20")} />,
      width: 100,
      onCell: () => ({
        style: {
          minWidth: 50,
        },
      }),
      dataIndex: "tenMucDich",
      key: "tenMucDich",
      show: true,
      i18Name: "common.tt20",
    },
    ...(isKeThuocRaVienTheoTungNgay || showTuNgayDenNgay
      ? [
          {
            title: <HeaderSearch title={t("common.tuNgay")} />,
            width: 100,
            onCell: () => ({
              style: {
                minWidth: 100,
              },
            }),
            dataIndex: "tuNgay",
            key: "tuNgay",
            colSpan: 1,
            show: true,
            i18Name: "common.tuNgay",
            render: (item) =>
              item?.toDateObject().format("dd/MM/yyyy HH:mm:ss"),
          },
          {
            title: <HeaderSearch title={t("common.denNgay")} />,
            width: 100,
            onCell: () => ({
              style: {
                minWidth: 100,
              },
            }),
            dataIndex: "denNgay",
            key: "denNgay",
            colSpan: 1,
            show: true,
            i18Name: "common.denNgay",
            render: (item) =>
              item?.toDateObject().format("dd/MM/yyyy HH:mm:ss"),
          },
        ]
      : [
          {
            title: (
              <HeaderSearch
                title={t("common.thoiGianThucHien")}
                sort_key="thoiGianThucHien"
              />
            ),
            width: 120,
            onCell: () => ({
              style: {
                minWidth: 90,
              },
            }),
            dataIndex: "thoiGianThucHien",
            key: "thoiGianThucHien",
            colSpan: 1,
            show: true,
            i18Name: "common.thoiGianThucHien",
            render: (item) =>
              item?.toDateObject().format("dd/MM/yyyy HH:mm:ss"),
          },
        ]),
    {
      title: (
        <HeaderSearch
          title={t("cdha.thoiGianChiDinh")}
          sort_key="thoiGianChiDinh"
        />
      ),
      width: 120,
      onCell: () => ({
        style: {
          minWidth: 90,
        },
      }),
      dataIndex: "thoiGianChiDinh",
      key: "thoiGianChiDinh",
      colSpan: 1,
      show: true,
      i18Name: "cdha.thoiGianChiDinh",
      render: (item) => item?.toDateObject().format("dd/MM/yyyy HH:mm:ss"),
    },
    {
      title: <HeaderSearch title={t("baoCao.bacSiChiDinh")} />,
      width: 100,
      onCell: () => ({
        style: {
          minWidth: 100,
        },
      }),
      dataIndex: "tenBacSiChiDinh",
      key: "tenBacSiChiDinh",
      colSpan: 1,
      show: true,
      i18Name: "baoCao.bacSiChiDinh",
    },
    {
      title: <HeaderSearch title={t("khamBenh.yKienDuocSi")} />,
      width: 100,
      onCell: () => ({
        style: {
          minWidth: 100,
        },
      }),
      dataIndex: "ghiChuDls",
      key: "ghiChuDls",
      colSpan: 1,
      show: true,
      i18Name: "khamBenh.yKienDuocSi",
    },
    {
      title: <HeaderSearch title={t("khamBenh.yKienBacSi")} />,
      width: 100,
      onCell: () => ({
        style: {
          minWidth: 100,
        },
      }),
      dataIndex: "ghiChuBacSi",
      key: "ghiChuBacSi",
      colSpan: 1,
      show: true,
      i18Name: "khamBenh.yKienBacSi",
    },
    {
      title: <HeaderSearch title={t("khamBenh.dungChoCon")} />,
      width: 80,
      dataIndex: "dungChoCon",
      key: "dungChoCon",
      i18Name: "khamBenh.dungChoCon",
      align: "center",
      show: true,
      render: (item) => {
        return <Checkbox checked={item} />;
      },
    },
    ...(isNoiTru
      ? [
          {
            title: <HeaderSearch title={t("khamBenh.trangThaiPhat")} />,
            width: 100,
            onCell: () => ({
              style: {
                minWidth: 100,
              },
            }),
            dataIndex: "trangThai",
            key: "trangThai",
            colSpan: 1,
            show: true,
            i18Name: "khamBenh.trangThaiPhat",
            render: (item) =>
              listTrangThaiThuoc.find((x) => x.id === item)?.ten || "",
          },
        ]
      : []),
    {
      title: (
        <HeaderSearch
          title={
            <>
              {t("common.khac")} <Setting refTable={refSettings} />
            </>
          }
        />
      ),
      width: 120,
      onCell: () => ({
        style: {
          minWidth: 70,
        },
      }),
      dataIndex: "action",
      key: "action",
      align: "center",
      colSpan: 1,
      hidden: !canEdit,
      fixed: "right",
      render: (item, record, index) => {
        if (isKeThuocRaVienTheoTungNgay) {
          return (
            <div className="action-btn-ke-thuoc">
              <Tooltip title={t("common.xemChiTiet")} placement="bottom">
                <SVG.IcEye onClick={onViewChiTiet(record)} className="icon" />
              </Tooltip>

              <Tooltip
                title={t("khamBenh.donThuoc.xoaThuoc")}
                placement="bottom"
              >
                <SVG.IcDelete
                  onClick={() => onDelete(record)}
                  className="icon"
                />
              </Tooltip>
            </div>
          );
        }
        return (
          <div className="action-btn-ke-thuoc">
            {record?.children && (
              <Tooltip title={t("pttt.thuocDungKem")} placement="bottom">
                <SVG.IcAdd onClick={() => onCreate(record)} className="icon" />
              </Tooltip>
            )}

            <Tooltip
              title={t("khamBenh.donThuoc.suaThongTinThuoc")}
              placement="bottom"
            >
              <SVG.IcEdit onClick={onEdit(record)} className="icon" />
            </Tooltip>
            <Tooltip title={t("khamBenh.donThuoc.xoaThuoc")} placement="bottom">
              <SVG.IcDelete onClick={() => onDelete(record)} className="icon" />
            </Tooltip>
            {(isTuTruc || isShowHoanThuocVoiKhoKhacTuTruc) &&
              record.thanhToan === 50 &&
              record.trangThaiHoan === 0 && (
                <SVG.IcHoanDv
                  className="icon"
                  onClick={() => onHoan(record)}
                  title={t("khamBenh.chiDinh.hoanDichVu")}
                />
              )}

            {record.trangThaiHoan === 10 && (
              <SVG.IcHuyHoanDv
                className="icon"
                onClick={() => onHuyHoan(record)}
                title={t("khamBenh.chiDinh.huyYeuCauHoan")}
              />
            )}
            {record.trangThai < TRANG_THAI_THUOC.DA_PHAT.id &&
              record.trangThaiHoan === TRANG_THAI_HOAN.THUONG &&
              checkRole([ROLES["KHAM_BENH"].NGUNG_Y_LENH_KHONG_THUC_HIEN]) && (
                <>
                  <Tooltip title={t("khamBenh.ngungYLenh")} placement="bottom">
                    <SVG.IcXoaHoSo
                      className="ic-action"
                      onClick={onNgungYLenh(record)}
                      color="var(--color-red-primary)"
                    />
                  </Tooltip>
                </>
              )}
          </div>
        );
      },
    },
  ];

  const setRowClassName = (record) => {
    if (
      record?.tenMucDich &&
      thongTinNguoiBenh?.doiTuong === DOI_TUONG.BAO_HIEM
    )
      return "row-tt35";
  };
  return (
    <Main>
      <TitleTable>{props.title}</TitleTable>
      <TableWrapper
        tableLayout="auto"
        columns={columns}
        dataSource={dataSource}
        scroll={{ x: false, y: false }}
        rowClassName={setRowClassName}
        expandIconColumnIndex={1}
        expandedRowKeys={state?.expandedKeys}
        rowKey={(record) => record.id}
        ref={refSettings}
        tableName="TABLE_DON_THUOC_KHAM_BENH"
        expandable={{
          expandIcon: ({ expanded, onExpand, record }) =>
            record?.children?.length ? (
              expanded ? (
                <CaretDownOutlined
                  onClick={(e) => {
                    onExpand(record, e);
                    setState({
                      expandedKeys: state?.expandedKeys.filter(
                        (x) => x !== record.id
                      ),
                    });
                    e.stopPropagation();
                  }}
                />
              ) : (
                <CaretRightOutlined
                  onClick={(e) => {
                    onExpand(record, e);
                    setState({
                      expandedKeys: [...state?.expandedKeys, record.id],
                    });
                    e.stopPropagation();
                  }}
                />
              )
            ) : null,
        }}
      />
      <SuaThongTinThuoc modeThuoc={modeThuoc} ref={refSuaThongTinThuoc} />

      <ModalHoanThuocVTYT ref={refModalHoanThuocVTYT} />
      <ModalChiTietThuoc
        ref={refModalChiTietThuoc}
        refreshData={refreshData}
        expandedKeys={expandedKeys}
        isKhamBenh={isKhamBenh}
        canEdit={canEdit}
        {...props}
      />
      <ModalNhapSoLuong ref={refModalNhapSoLuong} />
    </Main>
  );
};

export default React.memo(TableDonThuoc);
