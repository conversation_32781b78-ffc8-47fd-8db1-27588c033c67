import React, {
  forwardRef,
  useState,
  useImperativeHandle,
  useRef,
} from "react";
import { Form, InputNumber } from "antd";
import { useTranslation } from "react-i18next";

import { Button, ModalTemplate } from "components";
import { Main } from "./styled";

const ModalNhapSoLuong = forwardRef((props, ref) => {
  const { t } = useTranslation();
  const refOk = useRef(null);
  const refModal = useRef(null);
  const [form] = Form.useForm();
  const [state, _setState] = useState({
    show: false,
  });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };
  useImperativeHandle(ref, () => ({
    show: (
      {
        title = t("common.nhapSoLuong"),
        message = t("common.soLuong"),
        buttonCancelText = t("common.huy"),
        buttonOkText = t("common.dongY"),
        value,
      },
      onOk
    ) => {
      setState({ title, message, buttonCancelText, buttonOkText, value });
      form.setFieldsValue({ soLuong: null });
      setTimeout(() => {
        form.resetFields();
      }, 500);

      refOk.current = onOk;
      refModal.current && refModal.current.show();
    },
  }));
  const onCancel = () => {
    refModal.current && refModal.current.hide();
  };

  const onOk = () => {
    form.submit();
  };

  const onHandleSubmit = () => {
    form.validateFields().then((s) => {
      const { soLuong } = s || {};
      refOk.current && refOk.current(soLuong);
      refModal.current && refModal.current.hide();
    });
  };

  return (
    <ModalTemplate
      ref={refModal}
      width={376}
      title={state.title}
      actionLeft={<Button.QuayLai className="btn-cancel" onClick={onCancel} />}
      actionRight={
        <Button className="btn-ok" onClick={onOk} type={"primary"}>
          {t("common.dongY")}
        </Button>
      }
    >
      <Main>
        <Form
          form={form}
          layout="vertical"
          style={{ width: "100%" }}
          className="form-custom"
          onFinish={onHandleSubmit}
        >
          <Form.Item
            label={state.message}
            name="soLuong"
            rules={[
              {
                required: true,
                message: t("common.vuiLongNhapSoLuong"),
              },
            ]}
            initialValue={state.value}
          >
            <InputNumber
              autoFocus
              placeholder={t("common.nhapSoLuong")}
              style={{ width: "100%" }}
              min={0}
            />
          </Form.Item>
        </Form>
      </Main>
    </ModalTemplate>
  );
});

export default ModalNhapSoLuong;
