import React, {
  useState,
  useMemo,
  useRef,
  forwardRef,
  useImperativeHandle,
  useEffect,
} from "react";
import { Form, message } from "antd";
import { CheckCircleOutlined } from "@ant-design/icons";
import { groupBy, isEmpty } from "lodash";
import {
  Popover,
  Checkbox,
  TableWrapper,
  Toolt<PERSON>,
  HeaderSearch,
} from "components";
import CustomPopover from "pages/khamBenh/components/CustomPopover";
import { PhieuChiDinhWrapper } from "./styled";
import { getColorByTrangThai } from "../../../utils";
import ModalHoanDichVu from "components/ModalHoanDichVu";
import ModalDoiDichVu from "pages/chanDoanHinhAnh/CDHATDCN/ModalDoiDichVu";
import {
  TRANG_THAI_DICH_VU,
  LOAI_DICH_VU,
  GIOI_TINH_BY_VALUE,
  ENUM,
  DOI_TUONG,
  FORMAT_DATE_TIME,
  TRANG_THAI_NB,
  DOI_TUONG_SU_DUNG,
  DOI_TUONG_KCB,
  THIET_LAP_CHUNG,
  ROLES,
  TRANG_THAI_HOAN,
} from "constants/index";
import { TRANG_THAI } from "pages/xetNghiem/configs";
import ModalHuyHoanDichVu from "components/ModalHuyHoanDichVu";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import { useConfirm, useEnum, useLoading, useStore, useThietLap } from "hooks";
import moment from "moment";
import { SVG } from "assets";
import { orderBy } from "lodash";
import useViTriChamCong, {
  KEYS_VI_TRI_CHAM_CONG,
} from "pages/khamBenh/ChiDinhDichVu/ModalChiDinhDichVu/useViTriChamCong";
import useThietLapGiaoDien from "pages/khamBenh/ChiDinhDichVu/ModalChiDinhDichVu/useThietLapGiaoDien";
import ModalNhapKetQuaDichVu from "pages/khamBenh/ChiDinhDichVu/ModalNhapKetQuaDichVu";
import useManHinh from "pages/khamBenh/components/TableDonThuoc/useManHinh";
import useCustomMemo from "./useCustomMemo";
import CustomPopoverWithRef from "pages/khamBenh/components/CustomPopoverWithRef";
import ModalKetThucKham from "pages/khamBenh/ThongTin/ModalKetThucKham";
import FormEditDichVu from "pages/khamBenh/ChiDinhDichVu/components/FormEditDichVu";
import KetQuaXetNghiem from "pages/khamBenh/ChiDinhDichVu/components/KetQuaXetNghiem";
import nbDvKhamProvider from "data-access/nb-dv-kham-provider";
import { checkRole } from "lib-utils/role-utils";
import { isBoolean, isNumber } from "utils/index";
import nhomDichVuCap1Provider from "data-access/categories/dm-nhom-dich-vu-cap1-provider";
import ModalNhapLyDo from "pages/kho/components/ModalNhapLyDo";

const { Setting } = TableWrapper;

const DanhSachDichVu = (props, ref) => {
  const {
    dataSortColumn = {},
    onDeleteDichVu,
    huyTiepNhan,
    dataSource,
    getNBSoPhieuCLS,
    soPhieuCls,
    getDsDichVu,
    themThongTinDV,
    dataPhongThucHien,
    loaiDichVu,
    onChangePhieu,
    isHiddenTyLett,
    isPhauThuat,
    tableName,
    isDisplayLoaiPttt,
    disabledAll,
    isReadonly,
    isDisplayCapCuu = false,
    isDisplayDoiDv = false,
    isDisplayUuTien = false,
    huyKetQua,
    coKetQua,
    isNbThieuTien = false,
    xemKetQua,
    huyXemKetQua,
    displayCheckbox,
  } = props;
  const { showConfirm } = useConfirm();
  const { t } = useTranslation();
  const { showLoading, hideLoading } = useLoading();

  const phongThucHien = useStore(
    "khamBenh.thongTinChiTiet.nbDvKyThuat.phongThucHien",
    null
  );

  //REF
  const refModalNhapKetQua = useRef(null);
  const refModalKetThucKham = useRef(null);
  const refModalHoanDichVu = useRef(null);
  const refModalHuyHoanDichVu = useRef(null);
  const refModalDoiDichVu = useRef(null);
  const refSettings = useRef(null);
  const refTuTraDichVuCon = useRef(false);
  const refModalNhapLyDo = useRef(null);

  const { DATA_TEN_HIEN_THI, dataThietLap } = useThietLapGiaoDien();

  const [checkTonTaiThietLap, getTenField] = useViTriChamCong({
    show: true,
  });

  const dsPhuCapPTTT = useStore("pttt.dsPhuCapPTTT", []);
  const thongTinNguoiBenh = useStore(
    "chiDinhKhamBenh.configData.thongTinNguoiBenh"
  );
  const [listtrangThaiHoan] = useEnum(ENUM.TRANG_THAI_HOAN);
  const dsDichVuKham = useStore("chiDinhKhamBenh.dsDichVuKham", []);
  const currentToDieuTri = useStore("toDieuTri.currentToDieuTri");
  const [CHAN_TRUNG_PHONG_KHAM] = useThietLap(
    THIET_LAP_CHUNG.CHAN_TRUNG_PHONG_KHAM
  );
  const {
    phongThucHien: { onSearchParams: getListPhongThucHien },
    chiDinhKhamBenh: {
      themThongTinPhieu,
      onSearchChiTietDichVu,
      khongThucHien,
      ngungYLenh,
    },
    mucDichSuDung: { getMucDichByDVId },
    loaiDoiTuongLoaiHinhTT: { getListLoaiDoiTuongTT },
    pttt: { getDsPhuCapPTTTChiTiet },
    xetNghiem: { updateKetQuaXetNghiem },
    noiLayBenhPham: { getListNoiLayMau },
    tiepNhanCDHA: { updateKetQua },
    khamBenh: {
      getChiTietNbDvKhamById,
      updateChiTietNbDvKhamById,
      dangKetLuanNbDvKhamById,
      ketThucKhamNbDvKhamById,
    },
    nbDotDieuTri: { getById },
  } = useDispatch();

  //ENUM
  const [lisTrangThaiDichVu] = useEnum(ENUM.TRANG_THAI_DICH_VU);
  const [listHinhThucTtKsk] = useEnum(ENUM.HINH_THUC_TT_KSK);

  //STORE

  const listAllNhanVien = useStore("nhanVien.listAllNhanVien", []);

  const { isNoiTruToDieuTri } = useManHinh({});
  const [form] = Form.useForm();

  const [state, _setState] = useState({
    visibleEdit: null,
    dataPhongThucHien: [],
    selectedRowKeys: [],
  });
  const setState = (data) => {
    _setState({
      ...state,
      ...data,
    });
  };

  const isXetNghiem = loaiDichVu === LOAI_DICH_VU.XET_NGHIEM;
  const isChanDoanHinhAnh = loaiDichVu === LOAI_DICH_VU.CDHA;
  const isKhamBenh = window.location.pathname.indexOf("/kham-benh") >= 0;

  const getSelectedRowKeys = () =>
    (state.selectedRowKeys || []).filter(isNumber);

  useImperativeHandle(ref, () => ({ getSelectedRowKeys }));

  const dataTable = useMemo(() => {
    const groupData = groupBy(dataSource, "tenNhomDichVuCap2");
    let formattedData = [];
    Object.keys(groupData).forEach((key, idx) => {
      formattedData.push({
        id: key,
        nameDichVu: key,
        type: "group",
        key,
        isGroup: true,
      });
      let listChild = [];
      if (isXetNghiem && state?.expanDown) {
        let data = [];
        groupData[key].map((item, index) => {
          data.push({
            ...item,
            nameDichVu: item?.tenDichVu,
            index: index + 1,
            key: `${key}-${item?.tenDichVu}-${index}`,
            keyParent: key,
            rowSpan: item.dsChiSoCon.length + 1,
            isParent: true,
          });
          const dsChiSoCon = orderBy(item.dsChiSoCon, "stt", "asc");
          data.push(
            ...dsChiSoCon.map((item) => ({
              ...item,
              isChiSoCon: true,
            }))
          );
        });
        listChild = data.map((item, index) => ({
          ...item,
        }));
      } else {
        listChild = groupData[key].map((item, index) => ({
          ...item,
          nameDichVu: item?.tenDichVu,
          index: index + 1,
          key: `${key}-${item?.tenDichVu}-${index}`,
          isParent: true,
          keyParent: key,
        }));
      }
      formattedData = [...formattedData, ...listChild];
    });
    return formattedData;
  }, [JSON.stringify(dataSource), state?.expanDown]); // https://github.com/facebook/react/issues/14476#issuecomment-471199055

  const onClickSort = () => {};

  // Tách logic chính của handleEdit để có thể gọi lại
  const executeEdit = async (record, values, extraParams = {}) => {
    let {
      ghiChu,
      capCuu,
      uuTien,
      tuTra,
      benhPhamId,
      soLuong,
      thoiGianThucHien,
      thoiGianTiepNhan,
      khongTinhTien,
      loaiPtTt,
      tyLeTtDv,
      loaiHinhThanhToanId,
      phongThucHienId,
      phongLayMauId,
      mucDichId,
      tuVanVienId,
      khongThucHien,
      lyDoKhongThucHien,
      nguonKhacId,
      bacSiChiDinhId,
      nguoiThucHienId,
      bacSiKhamId,
      ketQua,
      dsCdChinhId,
      ketLuan,
      phanTangNguyCoId,
      quaTrinhBenhLy,
      thoiGianKetLuan,
      bacSiKetLuanId,
      ...rest
    } = values;

    let obj = {
      body:
        record.loaiDichVu === LOAI_DICH_VU.NGOAI_DIEU_TRI
          ? [
              {
                id: record.id,
                nbDichVu: {
                  ghiChu,
                  tuTra,
                  soLuong,
                  khongTinhTien,
                  dichVuId: record.dichVuId,
                  phongThucHienId,
                  loaiHinhThanhToanId: loaiHinhThanhToanId || null,
                  nguonKhacId: nguonKhacId || null,
                  bacSiChiDinhId,
                },
                nbDvKyThuat: {
                  phongThucHienId,
                  tuVanVienId,
                  capCuu,
                  khongThucHien,
                  lyDoKhongThucHien,
                },
              },
            ]
          : {
              benhPhamId,
              ...(record.loaiDichVu === LOAI_DICH_VU.XET_NGHIEM
                ? {
                    phongLayMauId,
                    phanTangNguyCoId: phanTangNguyCoId || null,
                  }
                : {}),
              nbDvKyThuat: {
                phongThucHienId,
                tuVanVienId,
                capCuu,
                uuTien,
                khongThucHien,
                lyDoKhongThucHien,
                thoiGianTiepNhan,
                ...rest,
              },
              nbDichVu: {
                ghiChu,
                tuTra,
                soLuong,
                tyLeTtDv,
                thoiGianThucHien,
                khongTinhTien,
                loaiHinhThanhToanId: loaiHinhThanhToanId || null,
                mucDichId,
                nguonKhacId: nguonKhacId || null,
                bacSiChiDinhId,
                ...(isBoolean(extraParams?.tuTraDichVuCon) && {
                  tuTraDichVuCon: extraParams?.tuTraDichVuCon,
                }),
              },
              loaiPtTt: loaiPtTt,
              ...(record.loaiDichVu === LOAI_DICH_VU.KHAM
                ? { bacSiKhamId }
                : { nguoiThucHienId: nguoiThucHienId || null }),
              ...(record.loaiDichVu === LOAI_DICH_VU.PHAU_THUAT_THU_THUAT
                ? { phanLoaiPtTt: rest.phanLoaiPtTt }
                : {}),
              ...([
                LOAI_DICH_VU.PHAU_THUAT_THU_THUAT,
                LOAI_DICH_VU.CDHA,
              ].includes(record.loaiDichVu)
                ? {
                    dieuDuongId: rest.dieuDuongId || null,
                    nguoiTiepNhanId: rest.nguoiTiepNhanId || null,
                  }
                : {}),
              ...([
                LOAI_DICH_VU.PHAU_THUAT_THU_THUAT,
                LOAI_DICH_VU.CDHA,
              ].includes(record.loaiDichVu)
                ? {
                    ptTtNguoiThucHien: KEYS_VI_TRI_CHAM_CONG.filter(
                      (key) =>
                        !["nguoiTiepNhanId", "dieuDuongId"].includes(key) &&
                        dataThietLap.includes(key) &&
                        checkTonTaiThietLap(
                          key,
                          dsPhuCapPTTT,
                          record.loaiDichVu
                        )
                    ).reduce(
                      (accumulator, currentValue) => ({
                        ...accumulator,
                        ...(rest[currentValue] !== record[currentValue]
                          ? { [currentValue]: rest[currentValue] || null }
                          : {}),
                      }),
                      {}
                    ),
                  }
                : {}),
              ...(record.loaiDichVu == LOAI_DICH_VU.XET_NGHIEM
                ? {
                    phuThucHien1Id: rest?.phuPtv1Id || null,
                    phuThucHien2Id: rest?.phuPtv2Id || null,
                    phuThucHien3Id: rest?.phuPtv3Id || null,
                    thanhVienKhacId: rest?.thanhVienKhacId || null,
                    nguoiThucHien2Id: rest?.nguoiThucHien2Id || null,
                  }
                : {}),
            },
      id: record.id,
      loaiDichVu: record.loaiDichVu,
    };

    if (isEmpty(obj.body.ptTtNguoiThucHien)) {
      delete obj.body.ptTtNguoiThucHien;
    }

    if (
      thoiGianThucHien &&
      record.thoiGianCoKetQua &&
      moment(thoiGianThucHien).diff(moment(record.thoiGianCoKetQua)) >= 0
    ) {
      message.error(
        `Xử lý thất bại. Thời gian thực hiện phải nhỏ hơn thời gian có kết quả : ${moment(
          record.thoiGianCoKetQua
        ).format(FORMAT_DATE_TIME)}`
      );
      return;
    }
    if (
      CHAN_TRUNG_PHONG_KHAM?.eval() &&
      [
        DOI_TUONG_KCB.NGOAI_TRU,
        DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_LINH_THUOC,
        DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_SU_DUNG_THUOC,
      ].includes(thongTinNguoiBenh.doiTuongKcb) &&
      dsDichVuKham?.find(
        (x) =>
          x.phongThucHienId === phongThucHienId &&
          x.id !== record.id &&
          x.dichVuId === record.dichVuId
      )
    ) {
      showConfirm({
        title: t("common.thongBao"),
        content: `${t(
          "khamBenh.phongBanVuaChonDaDuocChiDinhTruocDoVuiLongChonPhongKhac",
          { tenPhong: state.phong?.ten }
        )}`,
        cancelText: t("common.huy"),
        classNameOkText: "button-warning",
        typeModal: "warning",
      });
      onClose();
      return;
    }

    let prevTuTra = record.tuTra;
    let checkTien = prevTuTra ? record.tyLeBhTt : record.tienBhThanhToan;
    if (
      !extraParams.skipTuTraWarning &&
      thongTinNguoiBenh?.doiTuong === DOI_TUONG.BAO_HIEM &&
      prevTuTra !== tuTra &&
      checkTien > 0
    ) {
      let detailNhomDvCap1 = {};
      if (record.nhomDichVuCap1Id) {
        const res = await nhomDichVuCap1Provider.getById(
          record.nhomDichVuCap1Id
        );
        detailNhomDvCap1 = res?.data || {};
      }
      if (detailNhomDvCap1.tuTraTheoDichVuChiDinh) {
        const showCheckBox = !!prevTuTra && !tuTra;
        refTuTraDichVuCon.current = true;
        showConfirm(
          {
            title: t("common.thongBao"),
            cancelText: t("common.huy"),
            classNameOkText: "button-warning",
            typeModal: "warning",
            isContentElement: true,
            zIndex: 1035,
            showBtnOk: true,
            content: (
              <>
                <div
                  className="content content-2"
                  dangerouslySetInnerHTML={{
                    __html: t(
                      "khamBenh.chiDinh.canhBaoThayDoiCheDoThanhToanDichVuCha"
                    ),
                  }}
                />
                {showCheckBox && (
                  <Checkbox
                    defaultChecked={refTuTraDichVuCon.current}
                    onChange={(e) => {
                      refTuTraDichVuCon.current = e.target.checked;
                    }}
                  >
                    {t("khamBenh.chiDinh.cheDoThanhToanDichVuConTheoDichVuCha")}
                  </Checkbox>
                )}
              </>
            ),
          },
          () => {
            // Gọi lại hàm edit với params bổ sung
            executeEdit(record, values, {
              skipTuTraWarning: true,
              tuTraDichVuCon: refTuTraDichVuCon.current,
            });
          }
        );
        return;
      }
    }

    if (values?.benhPhamId) {
      (async () => {
        try {
          await themThongTinPhieu({
            body: {
              benhPhamId: values?.benhPhamId,
            },
            id: record?.id,
            loaiDichVu: record?.loaiDichVu,
          });
        } catch (err) {
          if (err?.message) {
            message.error(err?.message);
          }
        }
      })();
    }
    let promises = [];
    if (
      (values.hasOwnProperty("ketQua") && ketQua !== record?.ketQua) ||
      (values.hasOwnProperty("ketLuan") && ketLuan !== record?.ketLuan)
    ) {
      if (record.loaiDichVu === LOAI_DICH_VU.XET_NGHIEM) {
        promises.push(
          updateKetQuaXetNghiem([
            {
              id: record.id,
              ketQua: ketQua ?? null,
              ketLuan: ketLuan ?? null,
            },
          ])
        );
      }
      if (
        [LOAI_DICH_VU.CDHA, LOAI_DICH_VU.PHAU_THUAT_THU_THUAT].includes(
          record.loaiDichVu
        )
      ) {
        promises.push(
          updateKetQua({
            id: record.id,
            ketQua: ketQua ?? null,
            ketLuan: ketLuan ?? null,
          })
        );
      }
    }
    promises.push(themThongTinDV(obj));

    const isThucHienTaiKhoa = (record.dsDoiTuongSuDung || []).includes(
      DOI_TUONG_SU_DUNG.THUC_HIEN_TAI_KHOA
    );
    if (
      isNoiTruToDieuTri &&
      record.loaiDichVu === LOAI_DICH_VU.KHAM &&
      isThucHienTaiKhoa
    ) {
      let payload = state.recordKhamChiTiet;
      payload.nbChanDoan = { ...(payload.nbChanDoan || {}), dsCdChinhId };
      payload.nbHoiBenh = { ...(payload.nbHoiBenh || {}), quaTrinhBenhLy };
      payload.nbKetLuan = { ...(payload.nbKetLuan || {}), thoiGianKetLuan };
      payload.bacSiKetLuanId = bacSiKetLuanId;

      promises.push(updateChiTietNbDvKhamById(payload));
    }

    Promise.all(promises).then((s) => {
      const resultThongTinDv = s[s.length - 1];
      if (resultThongTinDv.code === 0) {
        getDsDichVu(record.loaiDichVu);
        refTuTraDichVuCon.current = false;
        onClose();
      }
    });
  };

  const handleEdit = (record) => () => {
    form.validateFields().then((values) => {
      executeEdit(record, values);
    });
  };

  const onDelete = (record) => () => {
    showConfirm(
      {
        title: t("common.xoaDuLieu"),
        content: `${t("khamBenh.chiDinh.xacNhanXoaChiDinh")} ${
          record.nameDichVu
        }?`,
        cancelText: t("common.quayLai"),
        okText: t("common.dongY"),
        classNameOkText: "button-error",
        showBtnOk: true,
        typeModal: "error",
      },
      async () => {
        try {
          showLoading();

          const s = await onDeleteDichVu({
            id: record.id,
            loaiDichVu: record.loaiDichVuXoa || record.loaiDichVu,
          });

          if (s.code === 0) {
            getDsDichVu(record.loaiDichVuXoa || record.loaiDichVu);
          }
        } catch (error) {
          console.error(error);
        } finally {
          hideLoading();
        }
      }
    );
  };

  const onHuyTiepNhan = (record) => () => {
    showConfirm(
      {
        title: t("common.huyTiepNhan"),
        content: `${t("khamBenh.chiDinh.xacNhanHuyKetLuanName")} ${
          record.nameDichVu
        }?`,
        cancelText: t("common.quayLai"),
        okText: t("common.dongY"),
        classNameOkText: "button-error",
        showBtnOk: true,
        typeModal: "error",
      },
      async () => {
        try {
          showLoading();

          const s = await huyTiepNhan({
            listId: [record.id],
            loaiDichVu: record.loaiDichVu,
          });

          if (s.code === 0) {
            getDsDichVu(record.loaiDichVu);
          }
        } catch (error) {
          console.error(error);
        } finally {
          hideLoading();
        }
      }
    );
  };

  const onHuyKetLuan = (record) => () => {
    showConfirm(
      {
        title: t("khamBenh.chiDinh.huyKetLuanKham"),
        content: t("khamBenh.chiDinh.xacNhanHuyKetLuanName", {
          name: record.nameDichVu,
        }),
        cancelText: t("common.quayLai"),
        okText: t("common.dongY"),
        classNameOkText: "button-warning",
        showBtnOk: true,
        typeModal: "warning",
      },
      async () => {
        try {
          showLoading();

          await nbDvKhamProvider.huyKetLuan({ id: record?.id });

          getDsDichVu(record.loaiDichVu);
        } catch (error) {
          console.error(error);
        } finally {
          hideLoading();
        }
      }
    );
  };

  const onCoKetQua = (record) => async () => {
    if (!record.ketQua && record.loaiDichVu === LOAI_DICH_VU.XET_NGHIEM) {
      refModalNhapKetQua.current &&
        refModalNhapKetQua.current.show(
          {
            id: record.id,
            loaiDichVu: record.loaiDichVu,
          },
          async () => {
            const s = await coKetQua({
              payload: [record.id],
              loaiDichVu: record.loaiDichVu,
            });
            if (s.code === 0) {
              getDsDichVu(record.loaiDichVu);
            }
          }
        );
    } else {
      try {
        showLoading();
        let s = null;
        if (
          [LOAI_DICH_VU.CDHA, LOAI_DICH_VU.PHAU_THUAT_THU_THUAT]?.includes(
            record.loaiDichVu
          )
        ) {
          s = await coKetQua({
            payload: [
              {
                id: record.id,
                thoiGianCoKetQua: moment(),
              },
            ],
            loaiDichVu: record.loaiDichVu,
          });
        } else {
          s = await coKetQua({
            payload: [record.id],
            loaiDichVu: record.loaiDichVu,
          });
        }

        if (s.code === 0) {
          getDsDichVu(record.loaiDichVu);
        }
      } catch (error) {
        console.error(error);
      } finally {
        hideLoading();
      }
    }
  };

  const onHuyKetQua = (record) => async () => {
    try {
      showLoading();

      const s = await huyKetQua({
        payload: [record.id],
        loaiDichVu: record.loaiDichVu,
      });

      if (s.code === 0) {
        getDsDichVu(record.loaiDichVu);
      }
    } catch (error) {
      console.error(error);
    } finally {
      hideLoading();
    }
  };

  const onXemKetQua = (record) => async () => {
    try {
      showLoading();
      let s = await xemKetQua({
        payload: [record.id],
        loaiDichVu: record.loaiDichVu,
      });

      if (s.code === 0) {
        getDsDichVu(record.loaiDichVu);
      }
    } catch (error) {
      console.error(error);
    } finally {
      hideLoading();
    }
  };

  const onHuyXemKetQua = (record) => async () => {
    try {
      showLoading();

      const s = await huyXemKetQua({
        payload: [record.id],
        loaiDichVu: record.loaiDichVu,
      });

      if (s.code === 0) {
        getDsDichVu(record.loaiDichVu);
      }
    } catch (error) {
      console.error(error);
    } finally {
      hideLoading();
    }
  };

  const onNgungYLenh = (record) => async (e) => {
    refModalNhapLyDo.current &&
      refModalNhapLyDo.current.show(
        {
          title: t("khamBenh.lyDoNgungYLenh"),
          message: t("khamBenh.nhapLyDoNgungYLenh"),
        },
        (lyDo) => {
          ngungYLenh({
            loaiDichVu: record.loaiDichVu,
            payload: [
              {
                id: record.id,
                lyDoNgungThucHien: lyDo,
              },
            ],
          }).then(() => {
            getDsDichVu(record.loaiDichVu);
          });
        }
      );
  };

  const onKhongThucHien = (record) => async (e) => {
    showConfirm(
      {
        title: t("common.xacNhan"),
        content: t("khamBenh.xacNhanKhongThucHien"),
        cancelText: t("common.quayLai"),
        okText: t("common.dongY"),
        showBtnOk: true,
        typeModal: "warning",
      },
      async () => {
        try {
          showLoading();

          await khongThucHien({
            loaiDichVu: record.loaiDichVu,
            payload: record.id,
          });

          getDsDichVu(record.loaiDichVu);
        } catch (error) {
          console.error(error);
        } finally {
          hideLoading();
        }
      }
    );
  };

  const onHoanDv = (record) => {
    let gioiTinh = thongTinNguoiBenh.gioiTinh
      ? GIOI_TINH_BY_VALUE[thongTinNguoiBenh.gioiTinh]
      : "";

    let tuoi =
      thongTinNguoiBenh.thangTuoi > 36 || thongTinNguoiBenh.tuoi
        ? `${thongTinNguoiBenh.tuoi} ${t("common.tuoi")}`
        : `${thongTinNguoiBenh.thangTuoi} ${t("common.thang")}`;

    const data = dataTable;
    if (data?.length) {
      data.forEach((itemLoop) => {
        itemLoop.gioiTinh = gioiTinh;
        itemLoop.tuoi = tuoi;
      });

      refModalHoanDichVu.current &&
        refModalHoanDichVu.current.show(
          {
            data: data,
            selectedRowKeys: [record?.id],
          },
          () => {
            getDsDichVu(record.loaiDichVu);
          }
        );
    } else {
      message.error(t("khamBenh.chiDinh.khongCoDichVuThoaManDieuKienDeHoan"));
    }
  };

  const onHuyHoan = (data) => {
    if (refModalHuyHoanDichVu.current)
      refModalHuyHoanDichVu.current.show(
        { data: [data], nbDotDieuTriId: data.nbDotDieuTriId },
        () => {
          getDsDichVu(data.loaiDichVu);
        }
      );
  };

  const onHidden = (record, key) => {
    if (
      (record.thanhToan && record.trangThaiHoan === 0 && key === "return") ||
      (!record.thanhToan && key === "delete")
    ) {
      if (
        (record.loaiDichVu === LOAI_DICH_VU.KHAM &&
          TRANG_THAI_DICH_VU["YEU_CAU_HOAN"].includes(record.trangThai)) ||
        (record.loaiDichVu === LOAI_DICH_VU.CDHA &&
          TRANG_THAI_DICH_VU["YEU_CAU_HOAN"].includes(record.trangThai)) ||
        (record.loaiDichVu === LOAI_DICH_VU.XET_NGHIEM &&
          TRANG_THAI["YEU_CAU_HOAN"].includes(record.trangThai)) ||
        record.loaiDichVu === LOAI_DICH_VU.NGOAI_DIEU_TRI
      ) {
        return true;
      }
      return false;
    }
    return false;
  };

  const displayDoiDv = (record) => {
    if (isKhamBenh) {
      switch (record.loaiDichVu) {
        case LOAI_DICH_VU.CDHA:
        case LOAI_DICH_VU.PHAU_THUAT_THU_THUAT:
          return (
            [15, 25, 35, 43, 50].includes(record.trangThai) &&
            record.thanhToan === 50 &&
            record.trangThaiHoan === 0 &&
            isDisplayDoiDv
          );
        case LOAI_DICH_VU.XET_NGHIEM:
          return (
            [25, 35, 46, 38, 62, 80, 50].includes(record.trangThai) &&
            record.thanhToan === 50 &&
            record.trangThaiHoan === 0 &&
            isDisplayDoiDv
          );
        case LOAI_DICH_VU.KHAM:
          return (
            [20, 30, 40, 50, 130].includes(record.trangThai) &&
            record.thanhToan === 50 &&
            record.trangThaiHoan === 0 &&
            isDisplayDoiDv
          );
        default:
          return false;
      }
    } else {
      return (
        isDisplayDoiDv &&
        [
          LOAI_DICH_VU.XET_NGHIEM,
          LOAI_DICH_VU.CDHA,
          LOAI_DICH_VU.PHAU_THUAT_THU_THUAT,
          LOAI_DICH_VU.KHAM,
        ].includes(record.loaiDichVu) &&
        !record.thanhToan &&
        record.doiDichVuKhiCoKetQua &&
        ![
          TRANG_THAI_NB.DA_RA_VIEN,
          TRANG_THAI_NB.DA_THANH_TOAN_RA_VIEN,
          TRANG_THAI_NB.DA_THANH_TOAN_HEN_DIEU_TRI,
        ].includes(thongTinNguoiBenh?.trangThai)
      );
    }
  };

  const onClose = () => {
    setState({
      visibleEdit: false,
      visibleDelete: false,
    });
  };

  const onTick = (key) => (e) => {
    if (key === "tuTra" && e) {
      form.setFieldsValue({ khongTinhTien: false });
    } else if (key === "khongTinhTien" && e) {
      form.setFieldsValue({ tuTra: false });
    }
  };

  const handleVisible = (type, idx, record) => async () => {
    //tạo biến chứa các state cho dv khám tại khoa
    let _addStateKhamTaiKhoa = {};

    if (type === "edit") {
      if (record.loaiDichVu === LOAI_DICH_VU.NGOAI_DIEU_TRI) {
        form.setFieldsValue({
          ketQua: record.ketQua,
          ketLuan: record.ketLuan,
          ghiChu: record.ghiChu,
          khongTinhTien: record.khongTinhTien,
          tuTra: record.tuTra,
          capCuu: record.capCuu,
          soLuong: record.soLuong,
          phongThucHienId: record.phongThucHienId,
          loaiHinhThanhToanId: record.loaiHinhThanhToanId,
          khongThucHien: record.khongThucHien,
          nguonKhacId: record.nguonKhacId,
          ...(record.khongThucHien && {
            lyDoKhongThucHien: record.lyDoKhongThucHien,
          }),
          bacSiChiDinhId: record.bacSiChiDinhId,
        });
      } else {
        //tạo biến chứa các field cho dv khám tại khoa
        let _addParamKhamTaiKhoa = {};

        const isThucHienTaiKhoa = (record.dsDoiTuongSuDung || []).includes(
          DOI_TUONG_SU_DUNG.THUC_HIEN_TAI_KHOA
        );
        if (
          isNoiTruToDieuTri &&
          record.loaiDichVu === LOAI_DICH_VU.KHAM &&
          isThucHienTaiKhoa
        ) {
          const resDvKham = await getChiTietNbDvKhamById(record.id);

          _addParamKhamTaiKhoa = {
            dsCdChinhId:
              resDvKham?.nbChanDoan?.dsCdChinhId ||
              currentToDieuTri?.dsCdChinhId, //mặc định lấy theo cđ của tờ điều trị kê dv khám
            bacSiKhamId:
              record.bacSiKhamId ||
              resDvKham?.bacSiKhamId ||
              currentToDieuTri?.bacSiDieuTriId, //mặc định bác sỹ điều trị của tờ điều trị
            quaTrinhBenhLy:
              resDvKham?.nbHoiBenh?.quaTrinhBenhLy ||
              currentToDieuTri?.dienBienBenh,
            bacSiKetLuanId:
              resDvKham?.bacSiKetLuanId || currentToDieuTri?.bacSiDieuTriId, //mặc định bác sỹ điều trị của tờ điều trị
            thoiGianKetLuan: moment(
              resDvKham?.nbKetLuan?.thoiGianKetLuan ||
                currentToDieuTri?.thoiGianYLenh
            ), //mặc định : thời gian của tờ điều trị
          };
          _addStateKhamTaiKhoa = {
            recordKhamChiTiet: resDvKham || {},
          };
        }

        form.setFieldsValue({
          ketQua: record.ketQua,
          ketLuan: record.ketLuan,
          benhPhamId: record.benhPhamId ? record.benhPhamId : [],
          phanTangNguyCoId: record.phanTangNguyCoId,
          ghiChu: record.ghiChu,
          phongThucHienId: record.phongThucHienId,
          phongLayMauId: record.phongLayMauId,
          soPhieu: record.soPhieu,
          tuTra: record.tuTra,
          capCuu: record.capCuu,
          uuTien: record.uuTien,
          soLuong: record.soLuong,
          tyLeTtDv: record.tyLeTtDv,
          khongTinhTien: record?.khongTinhTien,
          loaiPtTt: record?.loaiPtTt,
          thoiGianThucHien: moment(record?.thoiGianThucHien),
          thoiGianTiepNhan: moment(record?.thoiGianTiepNhan),
          loaiHinhThanhToanId:
            thongTinNguoiBenh?.doiTuong === DOI_TUONG.BAO_HIEM &&
            record.tenMucDich
              ? null
              : record?.loaiHinhThanhToanId,
          mucDichId: record?.mucDichId,
          tuVanVienId: record?.tuVanVienId,
          khongThucHien: record.khongThucHien,
          nguonKhacId: record.nguonKhacId,
          ...(record.khongThucHien && {
            lyDoKhongThucHien: record.lyDoKhongThucHien,
          }),
          bacSiChiDinhId: record.bacSiChiDinhId,
          ...(record.loaiDichVu === LOAI_DICH_VU.KHAM
            ? {
                bacSiKhamId: record.bacSiKhamId,
              }
            : {
                nguoiThucHienId: record.nguoiThucHienId,
              }),
          ...(record.loaiDichVu === LOAI_DICH_VU.PHAU_THUAT_THU_THUAT
            ? { phanLoaiPtTt: record.phanLoaiPtTt }
            : {}),
          ...([LOAI_DICH_VU.PHAU_THUAT_THU_THUAT, LOAI_DICH_VU.CDHA].includes(
            record.loaiDichVu
          )
            ? KEYS_VI_TRI_CHAM_CONG.reduce(
                (accumulator, currentValue) => ({
                  ...accumulator,
                  [currentValue]: record[currentValue],
                }),
                {}
              )
            : {}),
          ...(record.loaiDichVu == LOAI_DICH_VU.XET_NGHIEM
            ? {
                phuPtv1Id: record?.phuThucHien1Id,
                phuPtv2Id: record?.phuThucHien2Id,
                phuPtv3Id: record?.phuThucHien3Id,
                thanhVienKhacId: record?.thanhVienKhacId,
                nguoiThucHien2Id: record?.nguoiThucHien2Id,
              }
            : {}),
          ..._addParamKhamTaiKhoa,
        });
        getMucDichByDVId({ page: "", size: "", dichVuId: record.dichVuId });
        if (record?.loaiDichVu === LOAI_DICH_VU.XET_NGHIEM) {
          getListNoiLayMau({
            khoaChiDinhId: record.khoaChiDinhId,
            dsDoiTuongKcb: record.doiTuongKcb,
            dichVuId: record.dichVuId,
            nhomDichVuCap2Id: record.nhomDichVuCap2Id,
            nhomDichVuCap3Id: record.nhomDichVuCap3Id,
            dsLoaiDoiTuongId: thongTinNguoiBenh.loaiDoiTuongId,
            loaiHinhThanhToanId: record.loaiHinhThanhToanId,
            phongChiDinhId: record.phongThucHienId,
            nhaChiDinhId: phongThucHien?.toaNhaId,
          });
        }

        if (
          [
            LOAI_DICH_VU.PHAU_THUAT_THU_THUAT,
            LOAI_DICH_VU.CDHA,
            LOAI_DICH_VU.XET_NGHIEM,
          ].includes(record.loaiDichVu) &&
          (record.phanLoaiPtTt || record.phanLoaiPtTt == 0)
        ) {
          getDsPhuCapPTTTChiTiet({
            dichVuId: record.dichVuId,
            nhomDichVuCap2Id: record.nhomDichVuCap2Id,
            nhomDichVuCap1Id: record.nhomDichVuCap1Id,
            phanLoai: record.phanLoaiPtTt,
            ngay: moment().format("YYYY-MM-DD"),
          });
        }
        onSearchChiTietDichVu({
          page: "",
          size: "",
          id: record.dichVuId,
          loaiDoiTuongId: thongTinNguoiBenh?.loaiDoiTuongId,
        });
      }
      getListPhongThucHien({
        dsDichVuId: [record.dichVuId],
        khoaChiDinhId: record?.khoaChiDinhId,
        doiTuongKcb: record?.doiTuongKcb,
        chiDinhTuLoaiDichVu: isKhamBenh ? LOAI_DICH_VU.KHAM : null,
        loaiHinhThanhToanId: record?.loaiHinhThanhToanId,
        nbDotDieuTriId: record?.nbDotDieuTriId,
        loaiDoiTuongId: thongTinNguoiBenh?.loaiDoiTuongId,
      });
      getNBSoPhieuCLS({ loaiDichVu: record.loaiDichVu });
      getListLoaiDoiTuongTT({
        active: true,
        page: "",
        size: "",
        dsDichVuId: record.dichVuId,
        loaiDoiTuongId: thongTinNguoiBenh.loaiDoiTuongId,
        khoaChiDinhId: record?.khoaChiDinhId,
        doiTuongKcb: record?.doiTuongKcb,
        ngayThucHien:
          record?.thoiGianThucHien &&
          moment(record?.thoiGianThucHien).format("YYYY-MM-DD"),
        ngayVaoVien:
          thongTinNguoiBenh.thoiGianVaoVien &&
          moment(thongTinNguoiBenh.thoiGianVaoVien).format("YYYY-MM-DD"),
        ngaySinh:
          thongTinNguoiBenh?.ngaySinh &&
          moment(thongTinNguoiBenh?.ngaySinh).format("YYYY-MM-DD"),
      });
    }
    const dataType = {
      edit: "visibleEdit",
      delete: "visibleDelete",
      info: "visibleInfo",
    };
    setState({
      [dataType[type]]: idx,
      ..._addStateKhamTaiKhoa,
    });
  };

  const renderContent = (typeContent) => (value, row, index) => {
    const obj = {
      children: value && typeContent === "price" ? value.formatPrice() : value,
      props: {},
    };

    if (row.type === "group") {
      obj.props.colSpan = 1;
    }
    return obj;
  };

  const renderStt = (value, row, index) => {
    const obj = {
      children: value,
      props: {},
    };
    if (row.isParent) {
      obj.props.rowSpan = row.rowSpan;
      obj.children = value;
    } else if (row.type == "group") {
      obj.props.colSpan = 2;
      obj.props.style = { textAlign: "left" };
      obj.children = <span className="group-title">{row.nameDichVu}</span>;
    } else {
      obj.props.rowSpan = 0;
    }

    return obj;
  };

  const content = (info) => {
    const {
      tenBacSiChiDinh,
      tenBenhPham,
      tenPhongThucHien,
      tenKhoaChiDinh,
      tenNguoiThucHien,
      tenNguoiDuyetKetQua,
      thoiGianCoKetQua,
      trangThai,
      diaDiemPhongThucHien,
      tenKhoaThucHien,
    } = info || {};

    return (
      <div>
        <div>
          {t("khamBenh.chiDinh.bacSiChiDinh")}:{" "}
          <b>
            {tenBacSiChiDinh} - {tenKhoaChiDinh}
          </b>
        </div>
        <div>
          {t("khamBenh.chiDinh.benhPham")}: <b>{tenBenhPham}</b>
        </div>
        <div>
          {t("common.trangThai")}:{" "}
          <b>{lisTrangThaiDichVu.find((x) => x.id === trangThai)?.ten}</b>
        </div>
        <div>
          {t("khamBenh.ketQua.coKetQuaVao")}:{" "}
          <b>
            {thoiGianCoKetQua &&
              moment(thoiGianCoKetQua).format("DD/MM/YYYY HH:mm:ss")}
          </b>
        </div>
        <div>
          {t("khamBenh.chiDinh.nguoiThucHien")}: <b>{tenNguoiThucHien}</b>
        </div>
        <div>
          {t("khamBenh.chiDinh.bacSiDocKetQua")}: <b>{tenNguoiDuyetKetQua}</b>
        </div>
        <div>
          {t("khamBenh.chiDinh.phongThucHien")}:{" "}
          <b>
            {tenPhongThucHien
              ? `${tenPhongThucHien} ${
                  diaDiemPhongThucHien ? ` - ${diaDiemPhongThucHien}` : ""
                } ${tenKhoaThucHien ? ` - ${tenKhoaThucHien}` : ""}`
              : ""}
          </b>
        </div>
      </div>
    );
  };

  const onDoiDichVu = (record) => {
    if (refModalDoiDichVu.current)
      refModalDoiDichVu.current.show(
        { data: record, khoaChiDinhId: record?.khoaChiDinhId },
        () => {
          getDsDichVu(record.loaiDichVu);
        }
      );
  };

  const onSelectChange = (record, selected) => {
    let selectedRowKeys = [...state.selectedRowKeys];

    if (selected) {
      if (record.isGroup) {
        // Chọn group: tự động chọn tất cả children
        const childrenIds = dataTable
          .filter((x) => x.keyParent === record.id)
          .map((x) => x.id);

        selectedRowKeys = [...selectedRowKeys, record.id, ...childrenIds];
      } else {
        // Chọn child: thêm child và kiểm tra có cần chọn parent không
        selectedRowKeys = [...selectedRowKeys, record.id];

        // Tìm parent của child này
        if (record.keyParent) {
          const siblings = dataTable.filter(
            (x) => x.keyParent === record.keyParent
          );
          const selectedSiblings = siblings.filter((x) =>
            selectedRowKeys.includes(x.id)
          );

          // Nếu tất cả siblings đều được chọn, tự động chọn parent
          if (selectedSiblings.length === siblings.length) {
            selectedRowKeys = [...selectedRowKeys, record.keyParent];
          }
        }
      }
    } else {
      if (record.isGroup) {
        // Bỏ chọn group: tự động bỏ chọn tất cả children
        const childrenIds = dataTable
          .filter((x) => x.keyParent === record.id)
          .map((x) => x.id);

        selectedRowKeys = selectedRowKeys.filter(
          (x) => x !== record.id && !childrenIds.includes(x)
        );
      } else {
        // Bỏ chọn child: xóa child và tự động bỏ chọn parent
        selectedRowKeys = selectedRowKeys.filter((x) => x !== record.id);

        // Tự động bỏ chọn parent nếu có
        if (record.keyParent) {
          selectedRowKeys = selectedRowKeys.filter(
            (x) => x !== record.keyParent
          );
        }
      }
    }

    setState({ selectedRowKeys });
  };

  const onCheckAll = (e) => {
    setState({
      selectedRowKeys: e.target?.checked
        ? (dataTable || []).map((x) => x.id)
        : [],
    });
  };
  const isCheckedAll = useMemo(() => {
    return (
      dataTable?.length &&
      dataTable
        .map((item) => item.id)
        .every((i) => state.selectedRowKeys.includes(i))
    );
  }, [dataTable, state.selectedRowKeys]);

  const rowSelection = {
    columnTitle: (
      <HeaderSearch
        title={<Checkbox onChange={onCheckAll} checked={isCheckedAll} />}
      />
    ),
    width: "30px",
    onSelect: onSelectChange,
    selectedRowKeys: state.selectedRowKeys,
    getCheckboxProps: (record) => {
      if (record.isChiSoCon) {
        return {
          disabled: true,
          style: { display: "none" },
        };
      }
    },
  };

  const columns = [
    {
      title: <HeaderSearch title={t("common.stt")} />,
      width: 50,
      dataIndex: "index",
      key: "index",
      align: "center",
      ignore: true,
      render: renderStt,
    },
    {
      title: (
        <HeaderSearch
          title={t("common.dichVu")}
          sort_key="dichvu"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichvu"] || 0}
        />
      ),
      width: 240,
      dataIndex: "nameDichVu",
      key: "nameDichVu",
      align: "left",
      i18Name: t("common.dichVu"),
      className: "tenDichVu",
      show: true,
      render: (text, row) => {
        if (row.type !== "group") {
          const colorStatus = getColorByTrangThai(
            row.trangThai,
            row.loaiDichVu
          );

          const additionalInfo = `${row.tuTra ? t("common.tuTuc") : ""} ${
            row.tuTra && row.loaiDichVu === LOAI_DICH_VU.KHAM ? "|" : ""
          } ${row.loaiDichVu === LOAI_DICH_VU.KHAM ? row.tenBacSiChiDinh : ""}`;
          return (
            <div className="group-row-item">
              {!!row?.dsChiSoCon?.length &&
                (state?.expanDown ? (
                  <SVG.IcDown onClick={() => setState({ expanDown: false })} />
                ) : (
                  <SVG.IcRight onClick={() => setState({ expanDown: true })} />
                ))}
              {row?.isParent && (
                <div className="group-row-item__icon">
                  <Popover content={content(row)}>
                    <CheckCircleOutlined
                      style={{ fontSize: "20px", color: colorStatus }}
                    />
                  </Popover>
                </div>
              )}
              <div
                className={`group-row-item__text ${
                  row?.isParent ? "" : "children"
                }`}
              >
                <p>{row?.isParent ? text : row?.tenChiSoCon}</p>
                <p className="add-info">{additionalInfo}</p>
              </div>
            </div>
          );
        }
        return "";
        // return {
        //   children: <span className="group-title">{text}</span>,
        //   props: {
        //     colSpan: 2,
        //   },
        // };
      },
    },
    {
      title: <HeaderSearch title={t("common.soLuong")} />,
      width: 50,
      dataIndex: "soLuong",
      key: "soLuong",
      i18Name: t("common.soLuong"),
      show: true,
    },
    {
      title: (
        <HeaderSearch title={t("quanLyNoiTru.dvNoiTru.thoiGianThucHien")} />
      ),
      width: 100,
      dataIndex: "thoiGianThucHien",
      key: "thoiGianThucHien",
      i18Name: t("quanLyNoiTru.dvNoiTru.thoiGianThucHien"),
      show: true,
      render: (item) => item && moment(item).format("DD/MM/YYYY HH:mm:ss"),
    },
    {
      title: <HeaderSearch title={t("cdha.thoiGianTiepNhan")} />,
      width: 100,
      dataIndex: "thoiGianTiepNhan",
      key: "thoiGianTiepNhan",
      i18Name: t("cdha.thoiGianTiepNhan"),
      show: true,
      render: (item) => item && moment(item).format("DD/MM/YYYY HH:mm:ss"),
    },
    {
      title: (
        <HeaderSearch title={t("quanLyNoiTru.dvNoiTru.thoiGianChiDinh")} />
      ),
      width: 100,
      dataIndex: "thoiGianChiDinh",
      key: "thoiGianChiDinh",
      i18Name: t("quanLyNoiTru.dvNoiTru.thoiGianChiDinh"),
      show: true,
      render: (item) => item && moment(item).format("DD/MM/YYYY HH:mm:ss"),
    },
    {
      title: <HeaderSearch title={t("common.tyLeTt")} />,
      width: 80,
      dataIndex: "tyLeTtDv",
      key: "tyLeTtDv",
      align: "center",
      i18Name: t("common.tyLeTt"),
      show: !isHiddenTyLett,
    },
    {
      title: <HeaderSearch title={t("common.giaBH")} />,
      width: 80,
      dataIndex: "giaBaoHiem",
      key: "giaBaoHiem",
      align: "right",
      i18Name: t("common.giaBH"),
      show: true,
      render: renderContent("price"),
    },
    {
      title: <HeaderSearch title={t("common.giaKhongBH")} />,
      width: 80,
      dataIndex: "giaKhongBaoHiem",
      key: "giaKhongBaoHiem",
      align: "right",
      i18Name: t("common.giaKhongBH"),
      show: true,
      render: renderContent("price"),
    },
    {
      title: <HeaderSearch title={t("common.phuThu")} />,
      width: 80,
      dataIndex: "giaPhuThu",
      key: "giaPhuThu",
      align: "right",
      i18Name: t("common.phuThu"),
      show: true,
      render: renderContent("price"),
    },
    {
      title: (
        <HeaderSearch
          title={t("common.thanhTien")}
          sort_key="thanhTien"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["thanhTien"] || 0}
        />
      ),
      width: 80,
      dataIndex: "thanhTien",
      key: "thanhTien",
      align: "right",
      i18Name: t("common.thanhTien"),
      show: true,
      render: renderContent("price"),
    },
    {
      title: (
        <HeaderSearch
          title={t("common.phong")}
          sort_key="tenPhongThucHien"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["tenPhongThucHien"] || 0}
        />
      ),
      width: 80,
      dataIndex: "tenPhongThucHien",
      key: "tenPhongThucHien",
      align: "right",
      hidden: LOAI_DICH_VU.KHAM !== loaiDichVu,
      i18Name: t("common.phong"),
      show: true,
    },

    {
      title: (
        <HeaderSearch
          title={
            isXetNghiem
              ? t("khamBenh.ketQua.ketQuaKetLuan")
              : t("khamBenh.ketQua.ketQua")
          }
          sort_key="dichvu"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichvu"] || 0}
        />
      ),
      width: 180,
      dataIndex: "ketQua",
      key: "ketQua",
      align: "left",
      i18Name: isXetNghiem
        ? t("khamBenh.ketQua.ketQuaKetLuan")
        : t("khamBenh.ketQua.ketQua"),
      show: isChanDoanHinhAnh || isXetNghiem,
      hidden: !isChanDoanHinhAnh && !isXetNghiem,
      render: (value, row, index) => {
        return (
          <KetQuaXetNghiem
            data={{ value, row, index }}
            isNbThieuTien={isNbThieuTien}
            isXetNghiem={isXetNghiem}
          />
        );
      },
    },
    {
      title: (
        <HeaderSearch
          title={
            isXetNghiem
              ? t("khamBenh.ketQua.giaTriThamChieu")
              : t("khamBenh.ketQua.ketLuan")
          } //Với nhóm dịch vụ != Xét nghiệm. Hiển thị tên cột = Kết luận. Lấy giá trị từ kết luận của dịch vụ để hiển thị
          sort_key="dichvu"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["dichvu"] || 0}
        />
      ),
      width: 180,
      dataIndex: "ketLuan",
      key: "ketLuan",
      align: "left",
      i18Name: isXetNghiem
        ? t("khamBenh.ketQua.giaTriThamChieu")
        : t("khamBenh.ketQua.ketLuan"),
      show: isChanDoanHinhAnh || isXetNghiem,
      hidden: !isChanDoanHinhAnh && !isXetNghiem,
      render: (value, row, index) => {
        const { ketQuaThamChieu, chiSoThap, chiSoCao, ketLuan } = row;
        let displayIsXetNghiem = "";
        if (isXetNghiem) {
          displayIsXetNghiem =
            ketQuaThamChieu ||
            (!!chiSoThap && !!chiSoCao ? `${chiSoThap} - ${chiSoCao}` : "");
        }

        const resultDiv = (
          <div
            className={isNbThieuTien && !isXetNghiem ? "blur-ket-qua" : ""}
            dangerouslySetInnerHTML={{
              __html: isXetNghiem ? displayIsXetNghiem : ketLuan,
            }}
          />
        );

        return isNbThieuTien && !isXetNghiem ? (
          <Popover
            content={
              <span style={{ color: "red" }}>
                {t("khamBenh.nguoiBenhThieuTienCanDiThanhToan")}
              </span>
            }
          >
            {resultDiv}
          </Popover>
        ) : (
          resultDiv
        );
      },
    },
    {
      title: <HeaderSearch title="TT35" />,
      width: 80,
      dataIndex: "tenMucDich",
      key: "tenMucDich",
      columnName: "TT35",
      show: true,
    },
    {
      title: <HeaderSearch title={t("khamBenh.chiDinh.tuVanVien")} />,
      width: 80,
      dataIndex: "tuVanVienId",
      key: "tuVanVienId",
      i18Name: t("khamBenh.chiDinh.tuVanVien"),
      show: true,
      render: (item) =>
        (listAllNhanVien || []).find((x) => x.id == item)?.ten || "",
    },
    {
      title: <HeaderSearch title={t("danhMuc.thoiGianDuKienCoKetQua")} />,
      width: 120,
      dataIndex: "duKienKetQua",
      key: "duKienKetQua",
      i18Name: t("danhMuc.thoiGianDuKienCoKetQua"),
      show: false,
    },
    {
      title: <HeaderSearch title={t("khamBenh.chiDinh.capCuu")} />,
      width: 80,
      dataIndex: "capCuu",
      key: "capCuu",
      align: "center",
      i18Name: t("khamBenh.chiDinh.capCuu"),
      show: true,
      hidden: !isDisplayCapCuu,
      render: (value, row, index) => {
        const obj = {
          children:
            value || row.type !== "group" ? (
              <Checkbox checked={value} disabled />
            ) : (
              ""
            ),
          props: {},
        };
        return obj;
      },
    },
    {
      title: <HeaderSearch title={t("common.uuTien")} />,
      width: 80,
      dataIndex: "uuTien",
      key: "uuTien",
      align: "center",
      i18Name: t("common.uuTien"),
      show: true,
      hidden: !isDisplayUuTien,
      render: (value, row, index) => {
        const obj = {
          children:
            value || row.type !== "group" ? (
              <Checkbox checked={value} disabled />
            ) : (
              ""
            ),
          props: {},
        };
        return obj;
      },
    },
    {
      title: <HeaderSearch title={t("hsba.nbKhongThucHien")} />,
      width: 90,
      dataIndex: "khongThucHien",
      key: "khongThucHien",
      align: "center",
      show: true,
      i18Name: "hsba.nbKhongThucHien",
      render: (item, record) => {
        const obj = {
          children:
            item || record.type !== "group" ? (
              <Checkbox checked={!!item} />
            ) : (
              ""
            ),
        };
        return obj;
      },
    },
    {
      title: <HeaderSearch title={t("khamBenh.lyDoKhongThucHienNgungYLenh")} />,
      width: 200,
      dataIndex: "lyDoKhongThucHien",
      key: "lyDoKhongThucHien",
      align: "center",
      show: true,
      i18Name: "khamBenh.lyDoKhongThucHienNgungYLenh",
    },
    {
      title: <HeaderSearch title={t("common.ghiChu")} />,
      width: 200,
      dataIndex: "ghiChu",
      key: "ghiChu",
      align: "left",
      show: true,
      i18Name: "common.ghiChu",
    },
    {
      title: <HeaderSearch title={t("khamBenh.ngayKhongThucHienNgungYLenh")} />,
      width: 150,
      dataIndex: "thoiGianXacNhanKhongThucHien",
      key: "thoiGianXacNhanKhongThucHien",
      align: "center",
      show: true,
      i18Name: "khamBenh.ngayKhongThucHienNgungYLenh",
      render: (item) => {
        return item && moment(item).format("DD/MM/YYYY HH:mm:ss");
      },
    },
    {
      title: <HeaderSearch title={t("common.giaBhyt")} isTitleCenter={true} />,
      width: 80,
      dataIndex: "giaBaoHiem",
      key: "giaBaoHiemYTe",
      align: "center",
      i18Name: "common.giaBhyt",
      show: false,
      hidden: thongTinNguoiBenh?.doiTuong !== DOI_TUONG.BAO_HIEM,
      render: (value, row, index) => {
        const obj = {
          children:
            value || row.type !== "group" ? (
              <Checkbox checked={value != 0} />
            ) : (
              ""
            ),
          props: {},
        };
        return obj;
      },
    },
    {
      title: <HeaderSearch title={t("common.tuTra")} isTitleCenter={true} />,
      dataIndex: "tuTra",
      key: "tuTra",
      width: 80,
      show: false,
      align: "center",
      i18Name: "common.tuTra",
      hidden: thongTinNguoiBenh?.doiTuong !== DOI_TUONG.BAO_HIEM,
      render: (value, row, index) => {
        const obj = {
          children:
            value || row.type !== "group" ? <Checkbox checked={value} /> : "",
          props: {},
        };
        return obj;
      },
    },
    {
      title: (
        <HeaderSearch title={t("common.khongTinhTien")} isTitleCenter={true} />
      ),
      dataIndex: "khongTinhTien",
      key: "khongTinhTien",
      width: 80,
      show: false,
      align: "center",
      i18Name: "common.khongTinhTien",
      hidden: thongTinNguoiBenh?.doiTuong !== DOI_TUONG.BAO_HIEM,
      render: (value, row, index) => {
        const obj = {
          children:
            value || row.type !== "group" ? <Checkbox checked={value} /> : "",
          props: {},
        };
        return obj;
      },
    },
    {
      title: <HeaderSearch title={t("tiepDon.thanhToanKsk")} />,
      width: 100,
      dataIndex: "hinhThucTtKsk",
      key: "hinhThucTtKsk",
      align: "center",
      i18Name: "tiepDon.thanhToanKsk",
      show: true,
      render: (item) =>
        (listHinhThucTtKsk || []).find((x) => x.id == item)?.ten || "",
    },
    {
      title: (
        <HeaderSearch
          title={t("common.nguoiXemKetQua")}
          sort_key="tenNguoiXemKetQua"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["tenNguoiXemKetQua"] || 0}
        />
      ),
      width: 130,
      dataIndex: "tenNguoiXemKetQua",
      key: "tenNguoiXemKetQua",
      hidden: LOAI_DICH_VU.KHAM === loaiDichVu,
      i18Name: t("common.nguoiXemKetQua"),
      show: true,
    },
    {
      title: <HeaderSearch title={t("common.thoiGianXemKetQua")} />,
      width: 100,
      dataIndex: "thoiGianXemKetQua",
      key: "thoiGianXemKetQua",
      i18Name: t("common.thoiGianXemKetQua"),
      show: true,
      render: (item) => item && moment(item).format("DD/MM/YYYY HH:mm:ss"),
    },

    ...([LOAI_DICH_VU.PHAU_THUAT_THU_THUAT, LOAI_DICH_VU.CDHA].includes(
      loaiDichVu
    )
      ? [
          {
            title: (
              <HeaderSearch
                title={t("khamBenh.chiDinh.ptvcTtvcNguoiTH")}
                isTitleCenter={true}
              />
            ),
            dataIndex: "tenNguoiThucHien",
            key: "tenNguoiThucHien",
            width: 160,
            show: false,
            i18Name: "khamBenh.chiDinh.ptvcTtvcNguoiTH",
          },
        ]
      : []),
    ...([
      LOAI_DICH_VU.PHAU_THUAT_THU_THUAT,
      LOAI_DICH_VU.CDHA,
      LOAI_DICH_VU.XET_NGHIEM,
    ].includes(loaiDichVu)
      ? KEYS_VI_TRI_CHAM_CONG.map((key) => ({
          title: (
            <HeaderSearch title={DATA_TEN_HIEN_THI[key]} isTitleCenter={true} />
          ),
          dataIndex: getTenField(key, loaiDichVu),
          key: key,
          width: 160,
          show: false,
          i18Name: DATA_TEN_HIEN_THI[key],
        }))
      : []),
    {
      title: (
        <HeaderSearch title={t("thuNgan.trangThaiHoan")} isTitleCenter={true} />
      ),
      width: 120,
      dataIndex: "trangThaiHoan",
      key: "trangThaiHoan",
      align: "center",
      i18Name: "thuNgan.trangThaiHoan",
      show: true,
      render: (item, list, index) => {
        return listtrangThaiHoan?.find((e) => e.id === item)?.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          title={
            <>
              {t("common.khac")} <Setting refTable={refSettings} />
            </>
          }
        />
      ),
      width: 180,
      dataIndex: "action",
      ignore: true,
      key: "action",
      align: "center",
      fixed: "right",
      render: (item, record, index) => {
        const obj = {
          props: {},
        };
        if (record.type === "group") {
          obj.props.colSpan = 0;
        } else {
          const isThucHienTaiKhoa = (record.dsDoiTuongSuDung || []).includes(
            DOI_TUONG_SU_DUNG.THUC_HIEN_TAI_KHOA
          );

          //trạng thái  khác Chờ tiếp nhận 25 (với dv cdha tdcn pttt, XN), Chờ khám 20 (với dv Khám)
          const isCanHuyTiepNhanTrangThai =
            ([
              LOAI_DICH_VU.XET_NGHIEM,
              LOAI_DICH_VU.CDHA,
              LOAI_DICH_VU.PHAU_THUAT_THU_THUAT,
            ].includes(record.loaiDichVu) &&
              ![25, 155]?.includes(record.trangThai)) ||
            (LOAI_DICH_VU.KHAM == record.loaiDichVu &&
              ![20, 150, 155]?.includes(record.trangThai));

          const isCanHuyKetLuan =
            LOAI_DICH_VU.KHAM == record.loaiDichVu && record.trangThai === 150;

          const onValidateBeforeShow = (open) => {
            if (!open) return true;

            return true;
          };

          const onKetLuanKham = async (data) => {
            try {
              showLoading();

              await dangKetLuanNbDvKhamById({
                id: record.id,
                ketThucKham: true,
                huongDieuTri: data?.keyHuongDieuTri,
                ketQuaDieuTri: data?.keyKetQua,
                ghiChu: data?.ghiChu,
              });

              getDsDichVu(record.loaiDichVu);
            } catch (error) {
              console.error(error);
            } finally {
              hideLoading();
            }
          };

          const onDongHoSo = async () => {
            await getById(record?.nbDotDieuTriId);

            refModalKetThucKham.current &&
              refModalKetThucKham.current.show({}, async (values) => {
                try {
                  showLoading();

                  await ketThucKhamNbDvKhamById({ id: record.id, ...values });
                  getDsDichVu(record.loaiDichVu);
                } catch (error) {
                  console.error(error);
                } finally {
                  hideLoading();
                }
              });
          };

          obj.children = (
            <div className="action-btn">
              {!disabledAll && (
                <>
                  {
                    // (canEditOrUpdate(record.trangThai, record.loaiDichVu) ||
                    // (!record.thanhToan &&
                    //   [155, 160].includes(record.trangThai))) && //trạng thái 155: đã có kết quả, 160: đã duyệt kết quả
                    <>
                      <Tooltip
                        title={t("quanLyNoiTru.chinhSuaDichVu")}
                        placement="bottom"
                      >
                        <div className="ic-action-1">
                          <CustomPopover
                            overlayInnerStyle={{
                              height: "fit-content",
                              padding: "0px !important",
                            }}
                            overlayClassName="popover-custom-all popover-custom-all_res"
                            iconSvg={<SVG.IcEdit />}
                            onSubmit={handleEdit(record)}
                            onCancel={onClose}
                            visible={state.visibleEdit === index}
                            handleVisible={handleVisible("edit", index, record)}
                            mask={true}
                          >
                            <FormEditDichVu
                              record={record}
                              form={form}
                              onChangePhieu={onChangePhieu}
                              isReadonly={isReadonly}
                              loaiDichVu={loaiDichVu}
                              dataPhongThucHien={dataPhongThucHien}
                              soPhieuCls={soPhieuCls}
                              isHiddenTyLett={isHiddenTyLett}
                              isPhauThuat={isPhauThuat}
                              isDisplayLoaiPttt={isDisplayLoaiPttt}
                              isDisplayCapCuu={isDisplayCapCuu}
                              isDisplayUuTien={isDisplayUuTien}
                            />
                          </CustomPopover>
                        </div>
                      </Tooltip>
                    </>
                  }

                  {!record?.chiSoConId && !isReadonly && (
                    <Tooltip
                      title={t("khamBenh.chiDinh.xoaDichVu")}
                      placement="bottom"
                    >
                      <SVG.IcDelete
                        className="ic-action-1"
                        onClick={onDelete(record)}
                      />
                    </Tooltip>
                  )}
                  {onHidden(record, "return") && (
                    <Tooltip
                      title={t("khamBenh.chiDinh.hoanDichVu")}
                      placement="bottom"
                    >
                      <SVG.IcHoanDv
                        onClick={() => onHoanDv(record)}
                        className="ic-action-1"
                      />
                    </Tooltip>
                  )}
                  {record.trangThaiHoan === 10 && (
                    <SVG.IcHuyHoanDv
                      className="ic-action-1"
                      onClick={() => onHuyHoan(record)}
                      title={t("khamBenh.chiDinh.huyYeuCauHoan")}
                    />
                  )}
                  {!isReadonly && displayDoiDv(record) && (
                    <Tooltip
                      title={t("tiepDon.doiDichVu")}
                      placement="bottomLeft"
                    >
                      <SVG.IcChuyenDichVu
                        onClick={() => onDoiDichVu(record)}
                        className="ic-action"
                      />
                    </Tooltip>
                  )}
                  {isThucHienTaiKhoa && isCanHuyTiepNhanTrangThai && (
                    <Tooltip title={t("common.huyTiepNhan")} placement="bottom">
                      <SVG.IcCancel
                        className="ic-action-1"
                        onClick={onHuyTiepNhan(record)}
                      />
                    </Tooltip>
                  )}
                  {isThucHienTaiKhoa && isCanHuyKetLuan && (
                    <Tooltip
                      title={t("khamBenh.chiDinh.huyKetLuanKham")}
                      placement="bottom"
                    >
                      <SVG.IcCloseCircle
                        color={"var(--color-red-primary)"}
                        className="ic-action"
                        onClick={onHuyKetLuan(record)}
                      />
                    </Tooltip>
                  )}
                  {isThucHienTaiKhoa &&
                    record.trangThai < TRANG_THAI_DICH_VU.DA_CO_KET_QUA &&
                    TRANG_THAI_DICH_VU.TIEP_NHAN_MAU >= record.trangThai &&
                    record.loaiDichVu != LOAI_DICH_VU.KHAM && (
                      <Tooltip title={t("common.coKetQua")} placement="bottom">
                        <SVG.IcTick
                          color={"var(--color-green-primary)"}
                          className="ic-action"
                          onClick={onCoKetQua(record)}
                        />
                      </Tooltip>
                    )}
                  {isThucHienTaiKhoa &&
                    record.trangThai >= TRANG_THAI_DICH_VU.DA_CO_KET_QUA && (
                      <Tooltip title={t("common.huyKetQua")} placement="bottom">
                        <SVG.IcCloseCircle
                          color={"var(--color-red-primary)"}
                          className="ic-action"
                          onClick={onHuyKetQua(record)}
                        />
                      </Tooltip>
                    )}

                  {isNoiTruToDieuTri &&
                    isThucHienTaiKhoa &&
                    record.loaiDichVu == LOAI_DICH_VU.KHAM && (
                      <>
                        {record?.trangThai <
                          TRANG_THAI_DICH_VU.DANG_KET_LUAN && (
                          <CustomPopoverWithRef
                            onValidateBeforeShow={onValidateBeforeShow}
                            width={300}
                            onKetLuanKham={onKetLuanKham}
                            text={
                              <Tooltip
                                title={t("khamBenh.ketThucKham")}
                                placement="bottom"
                              >
                                <SVG.IcTick
                                  color={"var(--color-green-primary)"}
                                  className="ic-action"
                                />
                              </Tooltip>
                            }
                          />
                        )}

                        {record?.trangThai ==
                          TRANG_THAI_DICH_VU.DANG_KET_LUAN && (
                          <Tooltip
                            title={t("khamBenh.dongHoSo")}
                            placement="bottom"
                          >
                            <SVG.IcTick
                              color={"var(--color-green-primary)"}
                              className="ic-action"
                              onClick={onDongHoSo}
                            />
                          </Tooltip>
                        )}
                      </>
                    )}
                  {[
                    TRANG_THAI_DICH_VU.DA_CO_KET_QUA,
                    TRANG_THAI_DICH_VU.DA_DUYET,
                  ]?.includes(record.trangThai) &&
                    ((record.loaiDichVu == LOAI_DICH_VU.XET_NGHIEM &&
                      checkRole([ROLES["KHAM_BENH"].XEM_HUY_KET_QUA_XN])) ||
                      (record.loaiDichVu == LOAI_DICH_VU.CDHA &&
                        checkRole([ROLES["KHAM_BENH"].XEM_HUY_KET_QUA_CDHA])) ||
                      (record.loaiDichVu == LOAI_DICH_VU.PHAU_THUAT_THU_THUAT &&
                        checkRole([
                          ROLES["KHAM_BENH"].XEM_HUY_KET_QUA_PTTT,
                        ]))) && (
                      <Tooltip
                        title={t("common.xacNhanXemKetQua")}
                        placement="bottom"
                      >
                        <SVG.IcTick
                          className="ic-action"
                          onClick={onXemKetQua(record)}
                        />
                      </Tooltip>
                    )}
                  {[TRANG_THAI_DICH_VU.DA_HOAN_TAT_DICH_VU]?.includes(
                    record.trangThai
                  ) &&
                    ((record.loaiDichVu == LOAI_DICH_VU.XET_NGHIEM &&
                      checkRole([ROLES["KHAM_BENH"].XEM_HUY_KET_QUA_XN])) ||
                      (record.loaiDichVu == LOAI_DICH_VU.CDHA &&
                        checkRole([ROLES["KHAM_BENH"].XEM_HUY_KET_QUA_CDHA])) ||
                      (record.loaiDichVu == LOAI_DICH_VU.PHAU_THUAT_THU_THUAT &&
                        checkRole([
                          ROLES["KHAM_BENH"].XEM_HUY_KET_QUA_PTTT,
                        ]))) && (
                      <Tooltip
                        title={t("common.huyXacNhanXemKetQua")}
                        placement="bottom"
                      >
                        <SVG.IcCloseCircle
                          color={"var(--color-red-primary)"}
                          className="ic-action"
                          onClick={onHuyXemKetQua(record)}
                        />
                      </Tooltip>
                    )}
                  {record.trangThai <= TRANG_THAI_DICH_VU.DA_CO_KET_QUA &&
                    [
                      LOAI_DICH_VU.PHAU_THUAT_THU_THUAT,
                      LOAI_DICH_VU.CDHA,
                      LOAI_DICH_VU.XET_NGHIEM,
                      LOAI_DICH_VU.KHAM,
                    ].includes(record.loaiDichVu) &&
                    record.trangThaiHoan === TRANG_THAI_HOAN.THUONG &&
                    checkRole([
                      ROLES["KHAM_BENH"].NGUNG_Y_LENH_KHONG_THUC_HIEN,
                    ]) && (
                      <>
                        <Tooltip
                          title={t("khamBenh.ngungYLenh")}
                          placement="bottom"
                        >
                          <SVG.IcXoaHoSo
                            className="ic-action"
                            onClick={onNgungYLenh(record)}
                            color="var(--color-red-primary)"
                          />
                        </Tooltip>
                        <Tooltip
                          title={t("cdha.khongThucHien")}
                          placement="bottom"
                        >
                          <SVG.IcCloseCircleFill
                            className="ic-action"
                            onClick={onKhongThucHien(record)}
                            color="var(--color-red-primary)"
                          />
                        </Tooltip>
                      </>
                    )}
                </>
              )}
            </div>
          );
        }
        return obj;
      },
    },
  ];

  const setRowClassName = (record) => {
    if (
      record?.tenMucDich &&
      thongTinNguoiBenh?.doiTuong === DOI_TUONG.BAO_HIEM
    ) {
      if (record.khongTinhTien) {
        return "row-tt35 green-color";
      }
      if (record.tuTra) {
        return "row-tt35 orange-color";
      }
      return "row-tt35";
    }

    if (record.khongTinhTien) {
      return "green-color";
    }
    if (record.tuTra) {
      return "orange-color";
    }
  };

  const onRow = (record, index) => {
    return {
      dataSource: dataTable,
      record,
      index,
    };
  };

  return (
    <PhieuChiDinhWrapper>
      <div className="form-detail">
        <TableWrapper
          styleWrap={{
            tableCell: { padding: "2px 4px" },
          }}
          columns={columns}
          dataSource={dataTable}
          tableName={tableName + "DVKT"}
          ref={refSettings}
          rowClassName={setRowClassName}
          onRow={onRow}
          {...(displayCheckbox &&
            (!disabledAll || !isReadonly) && {
              rowSelection: rowSelection,
              rowKey: (record) => record.id,
            })}
        />
      </div>
      <ModalHoanDichVu ref={refModalHoanDichVu} />
      <ModalHuyHoanDichVu ref={refModalHuyHoanDichVu} />
      <ModalDoiDichVu ref={refModalDoiDichVu} />
      <ModalNhapKetQuaDichVu ref={refModalNhapKetQua} />
      <ModalKetThucKham ref={refModalKetThucKham} />
      <ModalNhapLyDo ref={refModalNhapLyDo} />
    </PhieuChiDinhWrapper>
  );
};

export default forwardRef(DanhSachDichVu);
