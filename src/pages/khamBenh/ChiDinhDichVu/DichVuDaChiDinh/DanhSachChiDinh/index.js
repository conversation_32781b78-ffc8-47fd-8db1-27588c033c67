import React, { useEffect, useMemo, useRef, useState } from "react";
import { Collapse, message } from "antd";
import { useDispatch } from "react-redux";
import { groupBy, orderBy } from "lodash";
import Header from "./Header";
import DanhSachDichVu from "./DanhSachDichVu";
import { LOAI_DICH_VU, GIOI_TINH_BY_VALUE, ROLES } from "constants/index";
import ModalHoanDichVu from "components/ModalHoanDichVu";
import { useTranslation } from "react-i18next";
import { useConfirm, useLoading, useStore } from "hooks";
import { checkRole } from "lib-utils/role-utils";
import { combineSort, isArray } from "utils/index";

const { Panel } = Collapse;

const DanhSachChiDinh = ({
  keyStore,
  loaiDichVu,
  activeKey = [],
  onCollapsed,
  keys,
  isHiddenTyLett,
  isDisplayLoaiPttt,
  isDisplayIconHoan,
  isDisplayCapCuu,
  isDisplayDoiDv,
  disabledAll,
  isReadonly,
  isShowDvTiepDon,
  loadFinish,
  isTiepDon = false,
  ...props
}) => {
  const { showConfirm } = useConfirm();
  const { t } = useTranslation();
  const refsDanhSachDv = useRef({});
  const { showLoading, hideLoading } = useLoading();
  const dsChiDinh = useStore(keyStore, []);
  const configData = useStore("chiDinhKhamBenh.configData", null);
  const thongTinNguoiBenh = useStore(
    "chiDinhKhamBenh.configData.thongTinNguoiBenh"
  );
  const dsDichVuChiDinhXN = useStore("chiDinhKhamBenh.dsDichVuChiDinhXN");
  const dsDichVuChiDinhKham = useStore("chiDinhKhamBenh.dsDichVuChiDinhKham");
  const dsDichVuChiDinhCls = useStore("chiDinhKhamBenh.dsDichVuChiDinhCls");
  const dsDichVuNgoaiDieuTri = useStore("chiDinhKhamBenh.dsDichVuNgoaiDieuTri");
  const infoNb = useStore("khamBenh.infoNb", {});
  const { tienChuaThanhToan, tienTamUng, tienConLai } = useStore(
    "nbDotDieuTri.thongTinCoBan",
    {},
    { field: "tienChuaThanhToan,tienTamUng,tienConLai" }
  );

  const isKhamBenh = useMemo(() => {
    return window.location.pathname.includes("/kham-benh");
  }, []);

  const isNbThieuTien = useMemo(() => {
    // Nếu bệnh nhân không thiếu tiền → luôn được xem kết quả
    const isKhongThieuTien = tienChuaThanhToan < tienTamUng || tienConLai >= 0;
    if (isKhongThieuTien) return false;

    // Nếu bệnh nhân thiếu tiền và tài khoản không có quyền → không được xem kết quả
    if (isKhamBenh && !checkRole([ROLES["KHAM_BENH"].CHAN_XEM_KQ_KHAM])) {
      return true;
    }

    return false;
  }, [tienChuaThanhToan, tienTamUng, tienConLai, isKhamBenh]);

  const {
    chiDinhKhamBenh: {
      getDsDichVuChiDinhXN,
      getDsDichVuChiDinhKham,
      getDsDichVuChiDinhCLS,
      getDsDichVuNgoaiDieuTri,
      onDeleteDichVu,
      huyTiepNhan,
      huyKetQua,
      coKetQua,
      getDsDichVuKham,
      xemKetQua,
      huyXemKetQua,
    },
    nbDotDieuTri: { getThongTinCoBan },
  } = useDispatch();

  const { getUrl } = useDispatch().pacs;
  const [state, _setState] = useState({
    dsDichVuHoan: [],
  });

  const setState = (data) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };
  const refModalHoanDichVu = useRef(null);

  useEffect(() => {
    if (
      configData &&
      Object.keys(configData).length &&
      (loadFinish || isShowDvTiepDon === undefined)
    ) {
      let addParams = {
        dsChiDinhTuLoaiDichVu: [
          configData.chiDinhTuLoaiDichVu,
          LOAI_DICH_VU.TIEP_DON,
          ...(configData?.isKhamSucKhoe ? [LOAI_DICH_VU.GOI_KSK] : []),
        ],
        dsChiDinhTuDichVuId: [
          configData.nbDotDieuTriId,
          configData.chiDinhTuDichVuId,
        ],
      };

      if (isTiepDon) {
        addParams = {
          dsChiDinhTuLoaiDichVu: [LOAI_DICH_VU.TIEP_DON],
          dsChiDinhTuDichVuId: undefined,
          chiDinhTuDichVuId: undefined,
        };
      }
      const defaultSort = combineSort({
        sttDichVu: 1,
        tenDichVu: 1,
      });
      switch (loaiDichVu) {
        case LOAI_DICH_VU.KHAM:
          getDsDichVuChiDinhKham({
            isShowDvTiepDon: isShowDvTiepDon || isTiepDon,
            sort: defaultSort,
            ...addParams,
          });
          getDsDichVuKham({ nbDotDieuTriId: configData.nbDotDieuTriId });
          break;
        case LOAI_DICH_VU.XET_NGHIEM:
          getDsDichVuChiDinhXN({
            page: "",
            size: "",
            isShowDvTiepDon: isShowDvTiepDon || isTiepDon,
            sort: defaultSort,
            ...addParams,
          });
          break;
        case LOAI_DICH_VU.CDHA:
          getDsDichVuChiDinhCLS({
            isShowDvTiepDon: isShowDvTiepDon || isTiepDon,
            sort: defaultSort,
            ...addParams,
          });
          break;
        case LOAI_DICH_VU.NGOAI_DIEU_TRI:
          getDsDichVuNgoaiDieuTri({
            isShowDvTiepDon: isShowDvTiepDon || isTiepDon,
            sort: defaultSort,
            ...addParams,
          });
          break;
        default:
          break;
      }
    }
  }, [configData, loaiDichVu, isShowDvTiepDon, loadFinish]);

  useEffect(() => {
    let dsDichVuHoan = [
      ...dsDichVuChiDinhXN,
      ...dsDichVuChiDinhKham,
      ...dsDichVuChiDinhCls,
      ...dsDichVuNgoaiDieuTri,
    ];
    setState({ dsDichVuHoan: dsDichVuHoan });
  }, [
    dsDichVuChiDinhXN,
    dsDichVuChiDinhKham,
    dsDichVuChiDinhCls,
    dsDichVuNgoaiDieuTri,
  ]);

  useEffect(() => {
    if (infoNb?.id) {
      getThongTinCoBan(infoNb?.id);
    }
  }, [infoNb?.id]);

  const onDeletePhieu = (data) => () => {
    const listDichVuId = data
      // .filter((item) => canEditOrUpdate(item.trangThai, item.loaiDichVu))
      .map((dichVu) => dichVu.id);

    showConfirm(
      {
        title: t("common.xoaDuLieu"),
        content: `${t("khamBenh.chiDinh.xacNhanXoaDuLieu")} ${
          data[0].tenPhieuChiDinh
            ? data[0].tenPhieuChiDinh
            : data[0].tenNhomDichVuCap1
        }?`,
        cancelText: t("common.quayLai"),
        okText: t("common.dongY"),
        classNameOkText: "button-error",
        showBtnOk: true,
        typeModal: "error",
      },
      async () => {
        showLoading();
        try {
          const s = await onDeleteDichVu({
            listDeletingId: listDichVuId,
            loaiDichVu: data[0].loaiDichVu,
          });
          if (s.code === 0) {
            getDsDichVu(data[0].loaiDichVu);
          }
        } catch (error) {
        } finally {
          hideLoading();
        }
      }
    );
  };

  const getDsDichVu = (type) => {
    const defaultSort = combineSort({
      sttDichVu: 1,
      tenDichVu: 1,
    });
    let addParams = {
      dsChiDinhTuLoaiDichVu: [
        configData.chiDinhTuLoaiDichVu,
        LOAI_DICH_VU.TIEP_DON,
        ...(configData?.isKhamSucKhoe ? [LOAI_DICH_VU.GOI_KSK] : []),
      ],
      dsChiDinhTuDichVuId: [
        configData.nbDotDieuTriId,
        configData.chiDinhTuDichVuId,
      ],
    };
    if (isTiepDon) {
      addParams = {
        dsChiDinhTuLoaiDichVu: [LOAI_DICH_VU.TIEP_DON],
        dsChiDinhTuDichVuId: undefined,
        chiDinhTuDichVuId: undefined,
      };
    }
    switch (type) {
      case LOAI_DICH_VU.KHAM:
        getDsDichVuChiDinhKham({
          isShowDvTiepDon: isShowDvTiepDon || isTiepDon,
          sort: defaultSort,
          ...addParams,
        });
        getDsDichVuKham({ nbDotDieuTriId: configData.nbDotDieuTriId });

        break;
      case LOAI_DICH_VU.XET_NGHIEM:
        getDsDichVuChiDinhXN({
          isShowDvTiepDon: isShowDvTiepDon || isTiepDon,
          sort: defaultSort,
          ...addParams,
        });
        break;
      case LOAI_DICH_VU.CDHA:
      case LOAI_DICH_VU.PHAU_THUAT_THU_THUAT:
        getDsDichVuChiDinhCLS({
          isShowDvTiepDon: isShowDvTiepDon || isTiepDon,
          sort: defaultSort,
          ...addParams,
        });
        break;
      case LOAI_DICH_VU.NGOAI_DIEU_TRI:
        getDsDichVuNgoaiDieuTri({
          isShowDvTiepDon: isShowDvTiepDon || isTiepDon,
          sort: defaultSort,
          ...addParams,
        });
        break;
      default:
        break;
    }
  };

  const groupAndOrderItem = (items, groupkey, orderKey) => {
    const groupData = groupBy(items, "trangChiDinh");
    const data = {};
    Object.keys(groupData).forEach((trangChiDinh) => {
      data[`${trangChiDinh}-${groupkey}`] = orderBy(
        groupData[trangChiDinh],
        orderKey,
        "asc"
      );
    });
    return data;
  };

  const dataSource = useMemo(() => {
    if (loaiDichVu == LOAI_DICH_VU.KHAM)
      return groupAndOrderItem(dsChiDinh, "kham", [
        "tenNhomDichVuCap1",
        "benhPham",
        "phongThucHien",
      ]);
    if (loaiDichVu == LOAI_DICH_VU.XET_NGHIEM)
      return groupAndOrderItem(dsChiDinh, "xn", [
        "tenPhieuChiDinh",
        "benhPham",
        "phongThucHien",
      ]);
    if (loaiDichVu == LOAI_DICH_VU.CDHA)
      return groupAndOrderItem(dsChiDinh, "cls", [
        "tenPhieuChiDinh",
        "benhPham",
        "phongThucHien",
      ]);
    if (loaiDichVu == LOAI_DICH_VU.NGOAI_DIEU_TRI)
      return { ngoaiDieuTri: dsChiDinh };
  }, [dsChiDinh, loaiDichVu]);
  const onHoanDichVu =
    (record) =>
    (e = {}) => {
      e.preventDefault && e.preventDefault();
      e.stopPropagation && e.stopPropagation();
      const data = record.filter(
        (x) =>
          x.thanhToan &&
          x.trangThaiHoan === 0 &&
          [
            LOAI_DICH_VU.KHAM,
            LOAI_DICH_VU.CDHA,
            LOAI_DICH_VU.PHAU_THUAT_THU_THUAT,
            LOAI_DICH_VU.XET_NGHIEM,
            LOAI_DICH_VU.NGOAI_DIEU_TRI,
          ].includes(x.loaiDichVu)
      );

      let gioiTinh = thongTinNguoiBenh.gioiTinh
        ? GIOI_TINH_BY_VALUE[thongTinNguoiBenh.gioiTinh]
        : "";

      let tuoi =
        thongTinNguoiBenh.thangTuoi > 36 || thongTinNguoiBenh.tuoi
          ? `${thongTinNguoiBenh.tuoi} ${t("common.tuoi")}`
          : `${thongTinNguoiBenh.thangTuoi} ${t("common.thang")}`;
      if (state.dsDichVuHoan?.length) {
        state.dsDichVuHoan.forEach((itemLoop) => {
          itemLoop.gioiTinh = gioiTinh;
          itemLoop.tuoi = tuoi;
        });
        refModalHoanDichVu.current &&
          refModalHoanDichVu.current.show(
            {
              data: state.dsDichVuHoan,
              selectedRowKeys: (data || []).map((item) => {
                return item?.id;
              }),
            },
            (dsLoaiDichVu) => {
              if (dsLoaiDichVu && Object.keys(dsLoaiDichVu).length) {
                let listLoaiDichVu = Object.keys(dsLoaiDichVu);
                (listLoaiDichVu || []).map((item) => getDsDichVu(Number(item)));
              }
            }
          );
      } else {
        message.error(t("khamBenh.chiDinh.khongCoDichVuThoaManDieuKienDeHoan"));
        return;
      }
    };
  const onViewPacs = (data) => (e) => {
    e.stopPropagation();

    if (isNbThieuTien) {
      showConfirm({
        title: t("common.canhBao"),
        content: t("khamBenh.nguoiBenhThieuTienCanDiThanhToan"),
        cancelText: t("common.quayLai"),
        typeModal: "warning",
      });
      return;
    }

    getUrl({ id: data[0]?.id }).then((res) => {
      if (res) {
        window.open(res, "_blank").focus();
      }
    });
  };

  const onXemKetQua = (loaiDichVu, key) => (e) => {
    e.stopPropagation();

    let dsId = refsDanhSachDv.current?.[key]?.getListIds();

    if (!isArray(dsId, true)) {
      message.error(t("khamBenh.chiDinh.vuiLongChonDichVu"));
      return;
    }

    xemKetQua({ payload: dsId, loaiDichVu }).then(() => {
      getDsDichVu(loaiDichVu);
    });
  };

  const onHuyXemKetQua = (loaiDichVu, key) => (e) => {
    e.stopPropagation();

    let dsId = refsDanhSachDv.current?.[key]?.getListIds();

    if (!isArray(dsId, true)) {
      message.error(t("khamBenh.chiDinh.vuiLongChonDichVu"));
      return;
    }

    huyXemKetQua({ payload: dsId, loaiDichVu }).then(() => {
      getDsDichVu(loaiDichVu);
    });
  };

  const listPanel = useMemo(() => {
    const list = [];

    const checkRoleXemKetQua = (loaiDichVu) => {
      if (loaiDichVu == LOAI_DICH_VU.XET_NGHIEM) {
        return checkRole([ROLES["KHAM_BENH"].XEM_HUY_KET_QUA_XN]);
      }
      if (loaiDichVu == LOAI_DICH_VU.CDHA) {
        return checkRole([ROLES["KHAM_BENH"].XEM_HUY_KET_QUA_CDHA]);
      }
      if (loaiDichVu == LOAI_DICH_VU.PHAU_THUAT_THU_THUAT) {
        return checkRole([ROLES["KHAM_BENH"].XEM_HUY_KET_QUA_PTTT]);
      }
      return false;
    };

    Object.keys(dataSource).forEach((key) => {
      if (!dataSource[key]?.length) return;
      const { soPhieu, tenPhieuChiDinh, diaDiemPhongThucHien } =
        dataSource[key][0];
      dataSource[key].forEach((item) => {
        list.push({
          ...item,
          keyDefine: `${soPhieu}-${tenPhieuChiDinh}-${diaDiemPhongThucHien}`,
        });
      });
    });
    const grouped = groupBy(list, "keyDefine");
    return Object.keys(grouped).map((key) => {
      const { tenPhieuChiDinh, tenNhomDichVuCap1, loaiDichVu, soPhieu } =
        (grouped && grouped[key][0]) || {};
      const listTrangThai = grouped[key].map((item) => item.trangThai);
      const dsSoKetNoi = grouped[key].map((item) => item.soKetNoi);

      const setId = new Set();
      grouped[key].forEach((i) => setId.add(i.soPhieuId));

      return {
        header: (
          <Header
            title={tenPhieuChiDinh || tenNhomDichVuCap1}
            listTrangThai={listTrangThai}
            loaiDichVu={loaiDichVu}
            nbDotDieuTriId={configData?.nbDotDieuTriId}
            chiDinhTuLoaiDichVu={configData?.chiDinhTuLoaiDichVu}
            dsChiDinhTuLoaiDichVu={configData?.dsChiDinhTuLoaiDichVu}
            chiDinhTuDichVuId={configData?.chiDinhTuDichVuId}
            dsChiDinhTuDichVuId={configData?.dsChiDinhTuDichVuId}
            isCollapsed={activeKey.includes(key)}
            dataSource={grouped[key]}
            soPhieuId={grouped[key]?.[0]?.soPhieuId}
            dsSoPhieuId={Array.from(setId)}
            phieuChiDinhId={grouped[key]?.[0]?.phieuChiDinhId}
            keyDefine={key}
            onDelete={onDeletePhieu(grouped[key], key)}
            onHoanDichVu={onHoanDichVu(grouped[key])}
            dsSoKetNoi={dsSoKetNoi}
            onViewPacs={onViewPacs(grouped[key])}
            isDisplayIconHoan={isDisplayIconHoan}
            disabledAll={disabledAll}
            isTiepDon={isTiepDon}
            isReadonly={isReadonly}
            soPhieu={soPhieu}
            getDsDichVu={getDsDichVu}
            isNbThieuTien={isNbThieuTien}
            onXemKetQua={onXemKetQua(loaiDichVu, key)}
            onHuyXemKetQua={onHuyXemKetQua(loaiDichVu, key)}
            diplayIconKetQua={checkRoleXemKetQua(loaiDichVu)}
          />
        ),
        content: (
          <DanhSachDichVu
            dataGroup={grouped[key]}
            getDsDichVu={getDsDichVu}
            onDeleteDichVu={onDeleteDichVu}
            huyTiepNhan={huyTiepNhan}
            isHiddenTyLett={isHiddenTyLett}
            isDisplayLoaiPttt={isDisplayLoaiPttt}
            isDisplayCapCuu={isDisplayCapCuu}
            isDisplayUuTien={
              loaiDichVu == LOAI_DICH_VU.XET_NGHIEM ||
              loaiDichVu == LOAI_DICH_VU.CDHA
            }
            isDisplayDoiDv={isDisplayDoiDv}
            tableName={"DICH_VU_CHI_DINH_" + loaiDichVu}
            isReadonly={isReadonly}
            disabledAll={disabledAll || isTiepDon}
            huyKetQua={huyKetQua}
            coKetQua={coKetQua}
            isNbThieuTien={isNbThieuTien}
            xemKetQua={xemKetQua}
            huyXemKetQua={huyXemKetQua}
            displayCheckbox={checkRoleXemKetQua(loaiDichVu)}
            ref={(ref) => {
              if (ref) {
                refsDanhSachDv.current[key] = ref;
              } else {
                delete refsDanhSachDv.current[key];
              }
            }}
            {...props}
          />
        ),
        key,
      };
    });
  }, [dataSource, activeKey, disabledAll, state.dsDichVuHoan]);

  return (
    <>
      {listPanel.map((panel, idx) => (
        <Panel
          {...props}
          showArrow={false}
          key={panel.key}
          panelKey={panel.key}
          isActive={activeKey.includes(panel.key)}
          header={panel.header}
        >
          {panel.content}
        </Panel>
      ))}
      <ModalHoanDichVu ref={refModalHoanDichVu} />
    </>
  );
};

export default DanhSachChiDinh;
