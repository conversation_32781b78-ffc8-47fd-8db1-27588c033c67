import React, { useRef } from "react";
import { Col, Form, Input, DatePicker, Select as SelectAntd } from "antd";
import { useSelector, useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import moment from "moment";

import { checkRole, checkRoleOr } from "lib-utils/role-utils";
import { useEnum, useStore, useListAll, useThietLap } from "hooks";

import {
  DOI_TUONG_SU_DUNG,
  LOAI_DICH_VU,
  ROLES,
  ENUM,
  TY_LE_THANH_TOAN,
  DOI_TUONG,
  DOI_TUONG_KCB,
  THIET_LAP_CHUNG,
} from "constants/index";
import { Checkbox, DateTimePicker, InputTimeout, Select } from "components";
import useCustomMemo from "pages/khamBenh/ChiDinhDichVu/DichVuDaChiDinh/DanhSachChiDinh/DanhSachDichVu/useCustomMemo";
import { RowEditContent } from "./styled";
import useThietLapGiaoDien from "pages/khamBenh/ChiDinhDichVu/ModalChiDinhDichVu/useThietLapGiaoDien";
import useViTriChamCong, {
  KEYS_VI_TRI_CHAM_CONG,
} from "pages/khamBenh/ChiDinhDichVu/ModalChiDinhDichVu/useViTriChamCong";
import useManHinh from "pages/khamBenh/components/TableDonThuoc/useManHinh";
import ModalVuotDinhMuc from "pages/khamBenh/ChiDinhDichVu/ModalVuotDinhMuc";
import { uniqBy } from "lodash";

function FormEditDichVu(props) {
  const {
    record,
    form,
    onChangePhieu = () => {},
    isReadonly,
    dataPhongThucHien = [],
    soPhieuCls,
    isHiddenTyLett,
    isPhauThuat,
    isDisplayLoaiPttt,
    isDisplayCapCuu,
    isDisplayUuTien,
  } = props;

  const { t } = useTranslation();
  const listMucDichSuDung = useStore("mucDichSuDung.listDataTongHop", []);
  const dsPhuCapPTTT = useStore("pttt.dsPhuCapPTTT", []);
  const thongTinNguoiBenh = useStore(
    "chiDinhKhamBenh.configData.thongTinNguoiBenh"
  );
  const detailDvKyThuat = useStore("chiDinhKhamBenh.detailDvKyThuat", []);
  const { listAllBenhPham } = useSelector((state) => state.benhPham);
  const listNoiLayMau = useStore("noiLayBenhPham.listNoiLayMau", []);
  const [listPhanLoaiPTTT] = useEnum(ENUM.PHAN_LOAI_PTTT);
  const [listAllPhanTangNguyCo] = useListAll("phanTangNguyCo", {}, true);
  const [listAllNguonKhacChiTra] = useListAll("nguonKhac", {}, true);
  const { isNoiTruToDieuTri } = useManHinh({});
  const refModalVuotDinhMuc = useRef(null);
  const isKhamBenh = window.location.pathname.indexOf("/kham-benh") >= 0;

  const { DATA_TEN_HIEN_THI, dataThietLap } = useThietLapGiaoDien();
  const [dataMA_LOAI_HINH_THANH_TOAN_HEN_THUC_HIEN_KHI_VUOT_DINH_MUC] =
    useThietLap(
      THIET_LAP_CHUNG.MA_LOAI_HINH_THANH_TOAN_HEN_THUC_HIEN_KHI_VUOT_DINH_MUC
    );

  const [dataKHONG_HEN_NGAY_THUC_HIEN_DICH_VU_VUOT_DINH_MUC] = useThietLap(
    THIET_LAP_CHUNG.KHONG_HEN_NGAY_THUC_HIEN_DICH_VU_VUOT_DINH_MUC
  );
  const [dataAN_CHECKBOX_TU_TRA] = useThietLap(
    THIET_LAP_CHUNG.AN_CHECKBOX_TU_TRA
  );
  const [dataAN_CHECKBOX_KHONG_THUC_HIEN_DVKT] = useThietLap(
    THIET_LAP_CHUNG.AN_CHECKBOX_KHONG_THUC_HIEN_DVKT
  );

  const [renderDataListVTCC, checkTonTaiThietLap] = useViTriChamCong({
    show: true,
  });
  const _loaiHinhThanhToanId = Form.useWatch("loaiHinhThanhToanId", form);
  const _phongThucHienId = Form.useWatch("phongThucHienId", form);

  const {
    pttt: { getDsPhuCapPTTTChiTiet },
    khamBenh: { getSlDichVu },
    phongThucHien: { onSearchParams: getListPhongThucHien },
  } = useDispatch();

  const {
    listAllLoaiHinhThanhToan,
    listAllNhanVienMemo,
    listAllLoaiPttt,
    listAllMaBenhCustom,
  } = useCustomMemo();

  const disabledField =
    !record.thanhToan && [155, 160].includes(record.trangThai);
  const disabledField2 = ![15, 20, 30, 40, 25, 35, 43, 38, 46, 62].includes(
    record.trangThai
  );
  const disabledKhongTinhTien =
    listMucDichSuDung.length ||
    isReadonly ||
    ([20, 30, 40, 45, 60].includes(record.loaiDichVu)
      ? !checkRoleOr([
          ROLES["QUAN_LY_NOI_TRU"]
            .CHINH_SUA_KHONG_TINH_TIEN_POPUP_CHI_DINH_DVKT,
        ])
      : false) ||
    !checkRole([ROLES["QUAN_LY_NOI_TRU"].TICK_KHONG_TINH_TIEN]);
  const disableKhongThucHien = ![15, 20, 25, 35, 40, 43, 50, 63].includes(
    record.trangThai
  );
  const enableSoLuong =
    record?.doiDichVuKhiCoKetQua &&
    [20, 30, 40].includes(record.loaiDichVu) &&
    ![100, 120, 130].includes(record.trangThai);

  const isThucHienTaiKhoa = (record.dsDoiTuongSuDung || []).includes(
    DOI_TUONG_SU_DUNG.THUC_HIEN_TAI_KHOA
  );

  const onTick = (key) => (e) => {
    if (key === "tuTra" && e) {
      form.setFieldsValue({ khongTinhTien: false });
    } else if (key === "khongTinhTien" && e) {
      form.setFieldsValue({ tuTra: false });
    }
  };

  const checkCanhBaoDinhMuc = ({ data }) => {
    const listPhong = data?.dsPhongThucHien?.length
      ? data?.dsPhongThucHien
      : dataPhongThucHien;
    const listPhongThucHien = uniqBy(listPhong || [], "phongId").map(
      (item) => ({
        ...item,
        id: item.phongId,
        ten: `${item?.ma} - ${item?.ten}`,
        dichVuId: item.dichVuId,
      })
    );
    const phongThucHien =
      listPhongThucHien?.find((x) => x.id === data?.phongThucHienMoiId) || {};
    const { dinhMucNgoaiTru, soLuongNgoaiTru, soLuongNoiTru, dinhMucNoiTru } =
      phongThucHien;
    let soLuongDinhMucNgoaiTru = dinhMucNgoaiTru - (soLuongNgoaiTru || 0);
    let soLuongDinhMucNoiTru = dinhMucNoiTru - (soLuongNoiTru || 0);
    if (
      dinhMucNgoaiTru &&
      soLuongDinhMucNgoaiTru <= 0 &&
      data?.phongThucHienMoiId &&
      [
        DOI_TUONG_KCB.NGOAI_TRU,
        DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_LINH_THUOC,
        DOI_TUONG_KCB.NHAN_THUOC_THEO_HEN,
        DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_SU_DUNG_THUOC,
        DOI_TUONG_KCB.CAC_TRUONG_HOP_KHAC,
      ].includes(thongTinNguoiBenh.doiTuongKcb)
    ) {
      showPopupDinhMuc({
        data: data,
      });
    } else if (
      dinhMucNoiTru &&
      soLuongDinhMucNoiTru <= 0 &&
      data?.phongThucHienMoiId &&
      [
        DOI_TUONG_KCB.DIEU_TRI_NGOAI_TRU,
        DOI_TUONG_KCB.DIEU_TRI_NOI_TRU,
        DOI_TUONG_KCB.DIEU_TRI_NOI_TRU_BAN_NGAY,
        DOI_TUONG_KCB.DIEU_TRI_LUU_TRU_TAI_TRAM_Y_TE_TUYEN_XA,
        DOI_TUONG_KCB.DIEU_TRI_NOI_TRU_DUOI_BON_GIO,
      ].includes(thongTinNguoiBenh.doiTuongKcb)
    ) {
      showPopupDinhMuc({
        data: data,
      });
    } else {
      form.setFieldsValue({
        thoiGianThucHien:
          data.thoiGianThucHien && moment(data.thoiGianThucHien),
        phongThucHienId: data?.phongThucHienMoiId,
      });
    }
  };

  const onCheckLoaiHinhThanhToan = async (loaiHinhThanhToan, data) => {
    let slDichVu = null;
    if (
      loaiHinhThanhToan?.maLoaiHinhThanhToan ==
        dataMA_LOAI_HINH_THANH_TOAN_HEN_THUC_HIEN_KHI_VUOT_DINH_MUC &&
      data?.phongThucHienMoiId
    ) {
      slDichVu = await getSlDichVu({
        phongId: data?.phongThucHienMoiId,
        tuThoiGian: moment().add(1, "days").format("YYYY-MM-DD"),
        denThoiGian: moment().add(1, "months").format("YYYY-MM-DD"),
        doiTuongKcb: thongTinNguoiBenh.doiTuongKcb,
      });
      slDichVu = (slDichVu?.data || []).find(
        (x) =>
          x.dinhMucNgoaiTru > x.soLuongNgoaiTru ||
          x.dinhMucNoiTru > x.soLuongNoiTru
      );
      if (slDichVu) {
        debugger;
        setTimeout(() => {
          form.setFieldsValue({
            loaiHinhThanhToanId: loaiHinhThanhToan?.id,
            thoiGianThucHien: moment(slDichVu?.ngayThucHien),
            phongThucHienId: data?.phongThucHienMoiId,
          });
        }, 0);
      }
    } else {
      form.setFieldsValue({
        loaiHinhThanhToanId: loaiHinhThanhToan?.id,
        phongThucHienId: data?.phongThucHienId,
        thoiGianThucHien:
          data.thoiGianThucHien && moment(data.thoiGianThucHien),
      });
    }
  };
  const showPopupDinhMuc = ({ data }) => {
    refModalVuotDinhMuc.current &&
      refModalVuotDinhMuc.current.show(
        {
          dsLoaiHinhThanhToan: listAllLoaiHinhThanhToan,
          loaiHinhThanhToanId: data?.loaiHinhThanhToanId,
        },
        async (loaiHinhThanhToan) => {
          let resPhongThucHien = [];
          let phongThucHienId = null;
          resPhongThucHien = await getListPhongThucHien({
            dsDichVuId: [record.dichVuId],
            khoaChiDinhId: record?.khoaChiDinhId,
            doiTuongKcb: record?.doiTuongKcb,
            chiDinhTuLoaiDichVu: isKhamBenh ? LOAI_DICH_VU.KHAM : null,
            loaiHinhThanhToanId: loaiHinhThanhToan?.id,
            nbDotDieuTriId: record?.nbDotDieuTriId,
            loaiDoiTuongId: thongTinNguoiBenh?.loaiDoiTuongId,
          });
          if ((resPhongThucHien || []).length === 1) {
            phongThucHienId = resPhongThucHien[0].phongId;
          }
          onCheckLoaiHinhThanhToan(loaiHinhThanhToan, {
            ...data,
            phongThucHienId: phongThucHienId,
          });
          return true;
        },
        async () => {
          const loaiHinhThanhToan = listAllLoaiHinhThanhToan?.find(
            (x) => x.loaiHinhThanhToanId === _loaiHinhThanhToanId
          );
          onCheckLoaiHinhThanhToan(loaiHinhThanhToan, {
            ...data,
            phongThucHienId: _phongThucHienId,
          });

          return true;
        }
      );
  };

  const onChangeCheckDinhMuc = (key) => async (e, data) => {
    if (key === "phongThucHienId") {
      checkCanhBaoDinhMuc({ data: { ...record, phongThucHienMoiId: e } });
    } else if (key === "thoiGianThucHien") {
      const dsSlDichVuTheoPhong = await getSlDichVu({
        phongId: _phongThucHienId,
        tuThoiGian: e?._d.format("YYYY-MM-dd"),
        denThoiGian: moment().add(1, "months").format("YYYY-MM-DD"),
        doiTuongKcb: thongTinNguoiBenh.doiTuongKcb,
      });
      const day = moment(e?._d).day();
      const thongTinNgayVuotDinhMuc =
        dataKHONG_HEN_NGAY_THUC_HIEN_DICH_VU_VUOT_DINH_MUC
          ?.split(",")
          ?.map((item) => item?.toLowerCase());
      if (
        (thongTinNgayVuotDinhMuc?.includes("t7") && day === 6) ||
        (thongTinNgayVuotDinhMuc?.includes("cn") && day === 0)
      ) {
        form.setFieldsValue({
          thoiGianThucHien: moment(e?._d),
        });
        return;
      }
      const slDichVu = (dsSlDichVuTheoPhong?.data || []).find(
        (x) => x.ngayThucHien === e?._d.format("YYYY-MM-dd")
      );
      let soLuongDinhMucNgoaiTru =
        slDichVu?.dinhMucNgoaiTru - (slDichVu?.soLuongNgoaiTru || 0);
      let soLuongDinhMucNoiTru =
        slDichVu?.dinhMucNoiTru - (slDichVu?.soLuongNoiTru || 0);
      if (
        dsSlDichVuTheoPhong?.data?.length &&
        ((slDichVu?.dinhMucNgoaiTru &&
          soLuongDinhMucNgoaiTru <= 0 &&
          [
            DOI_TUONG_KCB.NGOAI_TRU,
            DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_LINH_THUOC,
            DOI_TUONG_KCB.NHAN_THUOC_THEO_HEN,
            DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_SU_DUNG_THUOC,
            DOI_TUONG_KCB.CAC_TRUONG_HOP_KHAC,
          ].includes(thongTinNguoiBenh.doiTuongKcb)) ||
          (slDichVu?.dinhMucNoiTru &&
            soLuongDinhMucNoiTru <= 0 &&
            [
              DOI_TUONG_KCB.DIEU_TRI_NGOAI_TRU,
              DOI_TUONG_KCB.DIEU_TRI_NOI_TRU,
              DOI_TUONG_KCB.DIEU_TRI_NOI_TRU_BAN_NGAY,
              DOI_TUONG_KCB.DIEU_TRI_LUU_TRU_TAI_TRAM_Y_TE_TUYEN_XA,
              DOI_TUONG_KCB.DIEU_TRI_NOI_TRU_DUOI_BON_GIO,
            ].includes(thongTinNguoiBenh.doiTuongKcb)) ||
          !slDichVu)
      ) {
        showPopupDinhMuc({
          data: {
            ...record,
            phongThucHienMoiId: _phongThucHienId,
            phongThucHienId: _phongThucHienId,
          },
        });
      }
    } else {
      let resPhongThucHien = [];
      resPhongThucHien = await getListPhongThucHien({
        dsDichVuId: [record.dichVuId],
        khoaChiDinhId: record?.khoaChiDinhId,
        doiTuongKcb: record?.doiTuongKcb,
        chiDinhTuLoaiDichVu: isKhamBenh ? LOAI_DICH_VU.KHAM : null,
        loaiHinhThanhToanId: e,
        nbDotDieuTriId: record?.nbDotDieuTriId,
        loaiDoiTuongId: thongTinNguoiBenh?.loaiDoiTuongId,
      });
      if ((resPhongThucHien || []).length === 1) {
        checkCanhBaoDinhMuc({
          data: {
            ...record,
            phongThucHienMoiId: resPhongThucHien[0].phongId,
            dsPhongThucHien: resPhongThucHien,
          },
        });
      } else {
        checkCanhBaoDinhMuc({
          data: {
            ...record,
            phongThucHienMoiId: record.phongThucHienId,
            dsPhongThucHien: resPhongThucHien,
          },
        });
      }
    }
  };
  return (
    <Form
      form={form}
      layout="vertical"
      onValuesChange={(changedValues) => {
        if (changedValues.hasOwnProperty("phanLoaiPtTt")) {
          getDsPhuCapPTTTChiTiet({
            dichVuId: record.dichVuId,
            nhomDichVuCap2Id: record.nhomDichVuCap2Id,
            nhomDichVuCap1Id: record.nhomDichVuCap1Id,
            phanLoai: changedValues.phanLoaiPtTt,
            ngay: moment().format("YYYY-MM-DD"),
          });
        }
      }}
    >
      <RowEditContent gutter={8}>
        <Col span={12}>
          <Form.Item
            label={t("common.soLuong")}
            name="soLuong"
            rules={[
              {
                required: true,
                message: t("common.vuiLongNhapSoLuong"),
              },
              {
                pattern: new RegExp(
                  /^(0*[1-9][0-9]*(\.[0-9]+)?|0+\.[0-9]*[1-9][0-9]*)$/
                ),
                message: t("pttt.yeuCauNhapSoLuongLonHon0"),
              },
            ]}
          >
            <InputTimeout
              type="number"
              style={{ width: "100%" }}
              placeholder={t("common.vuiLongNhapSoLuong")}
              min={0.01}
              step={1}
              parser={(value) => value.replaceAll(",", ".")}
              formatter={(value) => value.replaceAll(".", ",")}
              disabled={
                (!enableSoLuong &&
                  LOAI_DICH_VU.NGOAI_DIEU_TRI !== record.loaiDichVu &&
                  disabledField2 &&
                  !checkRole([ROLES["QUAN_LY_NOI_TRU"].SUA_SO_LUONG])) ||
                isReadonly
              }
            />
          </Form.Item>
        </Col>

        {![
          LOAI_DICH_VU.CDHA,
          LOAI_DICH_VU.KHAM,
          LOAI_DICH_VU.NGOAI_DIEU_TRI,
        ].includes(record.loaiDichVu) && (
          <Col span={12}>
            <Form.Item label={t("khamBenh.chiDinh.benhPham")} name="benhPhamId">
              <Select
                allowClear
                disabled={disabledField || isReadonly}
                data={listAllBenhPham}
                placeholder={t("khamBenh.chiDinh.chonTenBenhPham")}
                mode="tags"
                removeIcon={() => null}
                onChange={onChangePhieu(
                  record.soPhieuId,
                  record.loaiDichVu,
                  form
                )}
              />
            </Form.Item>
          </Col>
        )}
        <Col span={12}>
          <Form.Item
            label={t("khamBenh.chiDinh.phongThucHien")}
            name="phongThucHienId"
            rules={
              LOAI_DICH_VU.NGOAI_DIEU_TRI !== record.loaiDichVu
                ? [
                    {
                      required: true,
                      message: t(
                        "khamBenh.chiDinh.vuiLongNhapTenPhongThucHien"
                      ),
                    },
                  ]
                : []
            }
          >
            <SelectAntd
              allowClear
              disabled={
                (LOAI_DICH_VU.NGOAI_DIEU_TRI !== record.loaiDichVu
                  ? disabledField2
                  : false) || isReadonly
              }
              placeholder={t("khamBenh.chiDinh.chonTenPhongThucHien")}
              onChange={onChangeCheckDinhMuc("phongThucHienId")}
            >
              {dataPhongThucHien.map((item, index) => {
                return (
                  <SelectAntd.Option key={index} value={item?.id}>
                    {`${item?.ten}`}
                  </SelectAntd.Option>
                );
              })}
            </SelectAntd>
          </Form.Item>
        </Col>

        {record.loaiDichVu === LOAI_DICH_VU.XET_NGHIEM && (
          <Col span={12}>
            <Form.Item
              label={t("khamBenh.chiDinh.noiLayMau")}
              name="phongLayMauId"
            >
              <SelectAntd
                allowClear
                placeholder={t("khamBenh.chiDinh.chonNoiLayMau")}
              >
                {listNoiLayMau.map((item, index) => {
                  return (
                    <SelectAntd.Option key={index} value={item?.phongLayMauId}>
                      {`${item.phongLayMau?.ten || ""} (${
                        item.phongLayMau?.diaDiem || ""
                      })`}
                    </SelectAntd.Option>
                  );
                })}
              </SelectAntd>
            </Form.Item>
          </Col>
        )}

        <Col span={12}>
          <Form.Item label={t("common.ghiChu")} name="ghiChu">
            <Input
              disabled={disabledField || isReadonly}
              className="input-option"
              placeholder={t("danhMuc.vuiLongNhapGhiChu")}
            />
          </Form.Item>
        </Col>

        {![LOAI_DICH_VU.KHAM, LOAI_DICH_VU.NGOAI_DIEU_TRI].includes(
          record.loaiDichVu
        ) && (
          <Col span={12}>
            <Form.Item
              label={t("khamBenh.chiDinh.soPhieu")}
              name="soPhieu"
              rules={[
                {
                  required: true,
                  message: t("khamBenh.chiDinh.vuiLongNhapSoPhieu"),
                },
              ]}
            >
              <Select
                disabled={disabledField2 || isReadonly}
                data={soPhieuCls}
                placeholder={t("khamBenh.chiDinh.chonSoPhieu")}
              />
            </Form.Item>
          </Col>
        )}

        {!isHiddenTyLett && (
          <Col span={12}>
            <Form.Item label={t("common.tyLeTt")} name="tyLeTtDv">
              <Select
                data={TY_LE_THANH_TOAN}
                disabled={
                  record.loaiDichVu !== LOAI_DICH_VU.PHAU_THUAT_THU_THUAT ||
                  !isPhauThuat ||
                  disabledField ||
                  isReadonly
                }
              />
            </Form.Item>
          </Col>
        )}
        <Col
          span={24}
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
          {isDisplayCapCuu && (
            <Form.Item label=" " name="capCuu" valuePropName="checked">
              <Checkbox disabled={isReadonly}>
                {t("khamBenh.chiDinh.capCuu")}
              </Checkbox>
            </Form.Item>
          )}
          {isDisplayUuTien && (
            <Form.Item label=" " name="uuTien" valuePropName="checked">
              <Checkbox disabled={isReadonly}>{t("common.uuTien")}</Checkbox>
            </Form.Item>
          )}
          {!dataAN_CHECKBOX_TU_TRA?.eval() && (
            <Form.Item label=" " name="tuTra" valuePropName="checked">
              <Checkbox
                disabled={listMucDichSuDung.length || isReadonly}
                onChange={onTick("tuTra")}
              >
                {t("khamBenh.chiDinh.tuTra")}
              </Checkbox>
            </Form.Item>
          )}
          <Form.Item label=" " name="khongTinhTien" valuePropName="checked">
            <Checkbox
              disabled={disabledKhongTinhTien}
              onChange={onTick("khongTinhTien")}
            >
              {t("common.khongTinhTien")}
            </Checkbox>
          </Form.Item>
        </Col>
        <Col
          span={12}
          style={{
            display: "flex",
            alignItems: "center",
            width: "100%",
          }}
        >
          <Form.Item
            label={t("quanLyNoiTru.ngayThucHien")}
            name="thoiGianThucHien"
            style={{ width: "100%" }}
          >
            <DateTimePicker
              disabled={isReadonly}
              onOk={onChangeCheckDinhMuc("thoiGianThucHien")}
              style={{ width: "100%" }}
            />
          </Form.Item>
        </Col>
        {[
          LOAI_DICH_VU.CDHA,
          LOAI_DICH_VU.XET_NGHIEM,
          LOAI_DICH_VU.PHAU_THUAT_THU_THUAT,
        ].includes(record.loaiDichVu) && (
          <Col
            span={12}
            style={{
              display: "flex",
              alignItems: "center",
              width: "100%",
            }}
          >
            <Form.Item
              label={t("theoDoiDieuTri.ngayTiepNhan")}
              name="thoiGianTiepNhan"
              style={{ width: "100%" }}
            >
              <DateTimePicker
                disabled={isReadonly || !record?.dsDoiTuongSuDung?.includes(60)}
              />
            </Form.Item>
          </Col>
        )}
        {isDisplayLoaiPttt &&
          record?.loaiDichVu === LOAI_DICH_VU.PHAU_THUAT_THU_THUAT && (
            <Col span={12}>
              <Form.Item label={t("pttt.loaiPttt")} name="loaiPtTt">
                <Select
                  disabled={disabledField || isReadonly}
                  placeholder={t("pttt.loaiPttt")}
                  data={listAllLoaiPttt}
                />
              </Form.Item>
            </Col>
          )}
        <Col span={12}>
          <Form.Item
            label={t("khamBenh.chiDinh.loaiHinhThanhToan")}
            name="loaiHinhThanhToanId"
          >
            <Select
              data={listAllLoaiHinhThanhToan}
              placeholder={t("khamBenh.chiDinh.loaiHinhThanhToan")}
              disabled={
                !(
                  !detailDvKyThuat[0]?.mucDichSuDung ||
                  thongTinNguoiBenh?.doiTuong === DOI_TUONG.KHONG_BAO_HIEM
                ) || isReadonly
              }
              onChange={onChangeCheckDinhMuc("loaiHinhThanhToanId")}
            />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="TT35" name="mucDichId">
            <Select
              disabled={
                disabledField ||
                !(
                  detailDvKyThuat[0]?.mucDichSuDung &&
                  thongTinNguoiBenh?.doiTuong === DOI_TUONG.BAO_HIEM
                ) ||
                isReadonly
              }
              data={listMucDichSuDung}
              placeholder="TT35"
            />
          </Form.Item>
        </Col>
        {isThucHienTaiKhoa &&
          [
            LOAI_DICH_VU.XET_NGHIEM,
            LOAI_DICH_VU.CDHA,
            LOAI_DICH_VU.PHAU_THUAT_THU_THUAT,
          ].includes(record.loaiDichVu) && (
            <>
              <Col span={12}>
                <Form.Item label={t("common.ketQua")} name="ketQua">
                  <Input.TextArea />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label={t("common.ketLuan")} name="ketLuan">
                  <Input.TextArea />
                </Form.Item>
              </Col>
            </>
          )}
        <Col span={12}>
          <Form.Item label={t("hsba.tuVanVien")} name="tuVanVienId">
            <Select
              disabled={disabledField || isReadonly}
              data={listAllNhanVienMemo}
              valueNumber={true}
              placeholder={t("hsba.tuVanVien")}
            />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label={t("hsba.bsChiDinh")} name="bacSiChiDinhId">
            <Select
              disabled={disabledField || isReadonly}
              data={listAllNhanVienMemo}
              valueNumber={true}
              placeholder={t("hsba.chonBsChiDinh")}
            />
          </Form.Item>
        </Col>
        {!dataAN_CHECKBOX_KHONG_THUC_HIEN_DVKT?.eval() && (
          <Col span={12}>
            <Form.Item label=" " name="khongThucHien" valuePropName="checked">
              <Checkbox
                onChange={(e) => {
                  if (e.target.checked)
                    form.setFieldsValue({
                      lyDoKhongThucHien: record.lyDoKhongThucHien,
                    });
                }}
                disabled={disableKhongThucHien || isReadonly}
              >
                {t("hsba.nbKhongThucHien")}
              </Checkbox>
            </Form.Item>
          </Col>
        )}
        <Col span={12}>
          <Form.Item label={t("danhMuc.nguonKhac")} name="nguonKhacId">
            <Select
              data={listAllNguonKhacChiTra}
              placeholder={t("danhMuc.nguonKhac")}
              disabled={isReadonly}
            />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            label={
              isNoiTruToDieuTri &&
              record?.loaiDichVu === LOAI_DICH_VU.KHAM &&
              isThucHienTaiKhoa
                ? t("khamBenh.bacSyKham")
                : t("khamBenh.chiDinh.ptvcTtvcNguoiTH")
            }
            name={
              record?.loaiDichVu === LOAI_DICH_VU.KHAM
                ? "bacSiKhamId"
                : "nguoiThucHienId"
            }
          >
            <Select
              disabled={disabledField || isReadonly}
              data={listAllNhanVienMemo}
              valueNumber={true}
              placeholder={t("khamBenh.chiDinh.chonPtvcTtvcNguoiTH")}
            />
          </Form.Item>
        </Col>
        <Form.Item shouldUpdate noStyle>
          {() =>
            form.getFieldValue("khongThucHien") && (
              <>
                <Col
                  span={12}
                  style={{
                    display: "flex",
                    alignItems: "center",
                    width: "100%",
                  }}
                >
                  <Form.Item
                    label={t("khamBenh.ngayKhongThucHienNgungYLenh")}
                    style={{ width: "100%" }}
                  >
                    <Input
                      value={
                        record.thoiGianXacNhanKhongThucHien &&
                        moment(record.thoiGianXacNhanKhongThucHien).format(
                          "DD/MM/YYYY HH:mm:ss"
                        )
                      }
                      disabled
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label={t("khamBenh.lyDoKhongThucHienNgungYLenh")}
                    name="lyDoKhongThucHien"
                    required
                    rules={[
                      {
                        required: true,
                        message: t("khoMau.vuiLongNhapLyDo"),
                      },
                    ]}
                  >
                    <Input.TextArea
                      className="input-option"
                      placeholder={t("khoMau.vuiLongNhapLyDo")}
                      rows={2}
                      disabled={isReadonly}
                    />
                  </Form.Item>
                </Col>
              </>
            )
          }
        </Form.Item>

        {record.loaiDichVu === LOAI_DICH_VU.PHAU_THUAT_THU_THUAT &&
          dataThietLap.includes("phanLoaiPtTt") && (
            <Col span={12}>
              <Form.Item label={t("baoCao.phanLoaiPttt")} name={"phanLoaiPtTt"}>
                <Select
                  disabled={disabledField || isReadonly}
                  data={listPhanLoaiPTTT}
                  placeholder={t("baoCao.chonPhanLoaiPttt")}
                />
              </Form.Item>
            </Col>
          )}

        {[
          LOAI_DICH_VU.PHAU_THUAT_THU_THUAT,
          LOAI_DICH_VU.CDHA,
          LOAI_DICH_VU.XET_NGHIEM,
        ].includes(record.loaiDichVu) &&
          KEYS_VI_TRI_CHAM_CONG.filter(
            (key) =>
              dataThietLap.includes(key) &&
              checkTonTaiThietLap(key, dsPhuCapPTTT, record.loaiDichVu)
          ).map((key) => (
            <Col span={12} key={key}>
              <Form.Item label={DATA_TEN_HIEN_THI[key]} name={key}>
                <Select
                  disabled={disabledField || isReadonly}
                  data={renderDataListVTCC(key)}
                />
              </Form.Item>
            </Col>
          ))}
        {[LOAI_DICH_VU.XET_NGHIEM].includes(record.loaiDichVu) && (
          <Col span={12}>
            <Form.Item
              label={t("khamBenh.chiDinh.phanTangNguyCo")}
              name="phanTangNguyCoId"
            >
              <Select
                disabled={disabledField || isReadonly}
                data={listAllPhanTangNguyCo}
                placeholder={t("khamBenh.chiDinh.phanTangNguyCo")}
              />
            </Form.Item>
          </Col>
        )}

        {isNoiTruToDieuTri &&
          isThucHienTaiKhoa &&
          record.loaiDichVu == LOAI_DICH_VU.KHAM && (
            <>
              <Col span={12}>
                <Form.Item
                  label={t("khamBenh.chanDoan.chanDoanBenh")}
                  name="dsCdChinhId"
                >
                  <Select
                    data={listAllMaBenhCustom}
                    placeholder={t("khamBenh.chanDoan.chanDoanBenh")}
                    disabled={isReadonly}
                  />
                </Form.Item>
              </Col>

              <Col span={12}>
                <Form.Item
                  label={t("khamBenh.khamXet.dienBien")}
                  name="quaTrinhBenhLy"
                >
                  <Input
                    disabled={disabledField || isReadonly}
                    className="input-option"
                    placeholder={t("khamBenh.khamXet.dienBien")}
                  />
                </Form.Item>
              </Col>

              <Col span={12}>
                <Form.Item
                  label={t("khamBenh.hanhTrinhKham.bacSiKetLuan")}
                  name={"bacSiKetLuanId"}
                >
                  <Select
                    disabled={disabledField || isReadonly}
                    data={listAllNhanVienMemo}
                    valueNumber={true}
                    placeholder={t("khamBenh.hanhTrinhKham.bacSiKetLuan")}
                  />
                </Form.Item>
              </Col>

              <Col span={12}>
                <Form.Item
                  label={t("khamBenh.hanhTrinhKham.thoiGianKetLuan")}
                  name="thoiGianKetLuan"
                >
                  <DateTimePicker disabled={isReadonly} />
                </Form.Item>
              </Col>
            </>
          )}
      </RowEditContent>
      <ModalVuotDinhMuc ref={refModalVuotDinhMuc} />
    </Form>
  );
}

export default FormEditDichVu;
