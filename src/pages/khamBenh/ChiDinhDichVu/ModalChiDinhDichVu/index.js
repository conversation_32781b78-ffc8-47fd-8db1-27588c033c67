import React, {
  forwardRef,
  useImperativeHandle,
  useState,
  useEffect,
  useRef,
  useMemo,
} from "react";
import { useDispatch, useSelector } from "react-redux";
import { Main, BlankContentWrapper } from "./styled";
import { useTranslation } from "react-i18next";
import {
  Checkbox,
  Select,
  Button,
  InputTimeout,
  TableWrapper,
  Pagination,
  ModalTemplate,
  HeaderSearch,
  SplitPanel,
} from "components";
import { message, Row } from "antd";
import {
  DOI_TUONG,
  HOTKEY,
  LOAI_DICH_VU,
  THIET_LAP_CHUNG,
  TY_LE_THANH_TOAN,
  ENUM,
  TRANG_THAI_DICH_VU,
  DOI_TUONG_KCB,
  LOAI_GIA,
} from "constants/index";
import { RightOutlined } from "@ant-design/icons";
import CircleCheck from "assets/images/khamBenh/circle-check.png";
import {
  useCache,
  useConfirm,
  useEnum,
  useGuid,
  useListAll,
  useRefFunc,
  useStore,
  useThietLap,
} from "hooks";
import ModalBoSungThongTinDichVu from "../ModalBoSungThongTinDichVu";
import ModalThongTinThuoc from "pages/khamBenh/DonThuoc/ModalThongTinThuoc";
import useNbInfoTitle from "pages/khamBenh/hooks/useNbInfoTitle";
import { SVG } from "assets";
import { cloneDeep, flatten, groupBy, orderBy, uniqBy } from "lodash";
import moment from "moment";
import { combineSort, isArray } from "utils/index";
import useViTriChamCong, { KEYS_VI_TRI_CHAM_CONG } from "./useViTriChamCong";
import useThietLapGiaoDien from "./useThietLapGiaoDien";
import ModalCanhBaoDVChuaDenNgayHuongBH from "../ModalCanhBaoDVChuaDenNgayHuongBH";
import useKiemTraChungChi from "pages/chiDinhDichVu/components/useKiemTraChungChi";
import ModalCanhBaoKeTrung from "../ModalCanhBaoKeTrung";
import ModalVuotDinhMuc from "../ModalVuotDinhMuc";

const { Setting } = TableWrapper;
const MESS_CANH_BAO_HUONG_BHYT = "thanh toán BHYT".toLowerCase().unsignText();

const getPhongThucHienId = (record) => {
  let phongThucHienId = null;

  let dsPhongThucHien = record?.dsPhongThucHien;
  let dsPhongThucHienLayMau = isArray(dsPhongThucHien, true)
    ? dsPhongThucHien.filter((i) => i.phongLayMau)
    : [];
  if (isArray(dsPhongThucHienLayMau, true)) {
    phongThucHienId = dsPhongThucHienLayMau[0].phongId;
  } else if (isArray(dsPhongThucHien, true)) {
    if (
      record?.phongId &&
      dsPhongThucHien.findIndex((i) => i.phongId === record.phongId) > -1
    ) {
      phongThucHienId = record.phongId;
    } else {
      phongThucHienId = dsPhongThucHien[0].phongId;
    }
  }

  return phongThucHienId;
};

const getDonGiaByLoai = (loaiHinhThanhToan) => {
  let donGia = 0;
  if (loaiHinhThanhToan.loaiGia === LOAI_GIA.GIA_BAO_HIEM) {
    donGia = loaiHinhThanhToan.giaBaoHiem;
  } else if (loaiHinhThanhToan.loaiGia === LOAI_GIA.GIA_KHONG_BAO_HIEM) {
    donGia = loaiHinhThanhToan.giaKhongBaoHiem;
  } else if (loaiHinhThanhToan.loaiGia === LOAI_GIA.GIA_PHU_THU) {
    donGia = loaiHinhThanhToan.giaPhuThu;
  }
  return donGia;
};

const extractBenhPham = (list, dichVuId) => {
  if (!isArray(list, true)) return null;

  const filtered = list.filter((x) => x.dichVuId == dichVuId);
  const isValid = filtered.every(
    (x) => x?.benhPhamId != null && x?.benhPham != null
  );

  if (!filtered.length || !isValid) return null;

  return {
    dsBenhPhamId: filtered.map((x) => x.benhPhamId),
    dsBenhPham: filtered.map((x) => x.benhPham),
  };
};

export const ModalChiDinhDichVu = forwardRef((props, ref) => {
  const {
    isShowDvTiepDon,
    splitCacheCustomize,
    onResizeSplit = () => {},
    isKhamBenh = false,
  } = props;
  const { showConfirm } = useConfirm();
  const { t } = useTranslation();
  const nbInfoTitle = useNbInfoTitle();
  const { DATA_TEN_HIEN_THI, dataThietLap } = useThietLapGiaoDien();
  const isNoiTru =
    window.location.pathname.indexOf(
      "/quan-ly-noi-tru/chi-tiet-nguoi-benh-noi-tru"
    ) >= 0;

  const layerId = useGuid();
  const refIsSubmit = useRef(null);
  const refOption = useRef({});
  const refInput = useRef(null);
  const refModal = useRef(null);
  const refModalBoSungThongTinDichVu = useRef(null);
  const refModalThongTinThuoc = useRef(null);
  const refSelectRow = useRef(null);
  const refAddService = useRef(null);
  const refSubmit = useRef(null);
  const refCallback = useRef(null);
  const refSettings = useRef(null);
  const refSettingsLeft = useRef(null);
  const refSplitPanelLeft = useRef(null);
  const refSplitPanelRight = useRef(null);
  const refModalCanhBaoDVChuaDenNgayHuongBH = useRef(null);
  const refModalCanhBaoKeTrung = useRef(null);
  const refModalVuotDinhMuc = useRef(null);

  const [state, _setState] = useState({
    show: false,
    listSelectedDv: [],
    listDichVu: [],
    // boChiDinhSelected: {},
    tuVanVienId: null,
    showAllFilter: false,
    showLoaiHinhThanhToan: false,
    errCode: null,
    errMes: null,
    searchBenhPhamIds: {},
    loaiDichVu: "",
    dsDichVuBoQuaCheckTrung: [],
  });

  const {
    auth: { nhanVienId },
  } = useSelector((state) => state.auth);

  const [dataColumns, _, loadFinish] = useCache(
    nhanVienId,
    "DATA_CUSTOMIZE_COLUMN_table_DVKT_DichVuDaChon",
    [],
    false
  );
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  const [renderDataListVTCC, renderTitleVTCC, checkTonTaiThietLap] =
    useViTriChamCong({
      show: state.show,
    });

  const { dataTamTinhTien, listGoiDv, listDvKham, page, size, totalElements } =
    useSelector((state) => state.chiDinhKhamBenh);

  const [listPhanLoaiPTTT] = useEnum(ENUM.PHAN_LOAI_PTTT);
  const dsDichVuChiDinhKham = useStore(
    "chiDinhKhamBenh.dsDichVuChiDinhKham",
    []
  );
  const listDvTiepDon = useStore("chiDinhKhamBenh.listDvTiepDon", []);
  const dsDichVuChiDinhXN = useStore("chiDinhKhamBenh.dsDichVuChiDinhXN", []);
  const dsDichVuChiDinhCls = useStore("chiDinhKhamBenh.dsDichVuChiDinhCls", []);
  const dsDichVuNgoaiDieuTri = useStore(
    "chiDinhKhamBenh.dsDichVuNgoaiDieuTri",
    []
  );
  const dsDichVuKham = useStore("chiDinhKhamBenh.dsDichVuKham", []);
  const [listAllPhong] = useListAll("phong", {}, true);
  const [listAllNhanVien] = useListAll("nhanVien", {}, state.show);
  const [listAllBenhPham] = useListAll("benhPham", {}, true);
  const [listAllNguonKhacChiTra] = useListAll("nguonKhac", {}, true);
  const [listAllPhanTangNguyCo] = useListAll("phanTangNguyCo", {}, true);

  const listNhomDvCap2 = useStore("nhomDichVuCap2.listGroupService2", []);
  const mayTinhId = useStore("application.mayTinhId", "");
  const dsChuyenKhoa = useStore(
    "khamBenh.thongTinChiTiet.nbDichVu.dichVu.dsChuyenKhoa",
    null
  );
  const chiTietKhamSan = useStore("khamBenh.chiTietKhamSan", {});
  const id = useStore("khamBenh.thongTinChiTiet.id", null);
  const [dataThucHienCanBang] = useThietLap(
    THIET_LAP_CHUNG.THUC_HIEN_CAN_BANG_PHONG
  );
  const [TU_DONG_CHECK_ALL_DICH_VU_TRONG_BO] = useThietLap(
    THIET_LAP_CHUNG.TU_DONG_CHECK_ALL_DICH_VU_TRONG_BO
  );

  const [dataPageSize] = useThietLap(THIET_LAP_CHUNG.PAGE_SIZE);
  const [dataPHONG_CHO_MO] = useThietLap(THIET_LAP_CHUNG.PHONG_CHO_MO);
  const [dataCAN_BANG_PHONG_THUC_HIEN_THEO_TONG_THOI_GIAN_THUC_HIEN] =
    useThietLap(
      THIET_LAP_CHUNG.CAN_BANG_PHONG_THUC_HIEN_THEO_TONG_THOI_GIAN_THUC_HIEN
    );
  const [dataBAT_BUOC_NHAP_SO_THAI_TUAN_THAI_KHI_CHI_DINH] = useThietLap(
    THIET_LAP_CHUNG.BAT_BUOC_NHAP_SO_THAI_TUAN_THAI_KHI_CHI_DINH
  );

  const [dataMA_LOAI_HINH_THANH_TOAN_HEN_THUC_HIEN_KHI_VUOT_DINH_MUC] =
    useThietLap(
      THIET_LAP_CHUNG.MA_LOAI_HINH_THANH_TOAN_HEN_THUC_HIEN_KHI_VUOT_DINH_MUC
    );
  const [CHAN_TRUNG_PHONG_KHAM] = useThietLap(
    THIET_LAP_CHUNG.CHAN_TRUNG_PHONG_KHAM
  );
  const [CANH_BAO_TRUNG_CHI_DINH_TRONG_NGAY] = useThietLap(
    THIET_LAP_CHUNG.CANH_BAO_TRUNG_CHI_DINH_TRONG_NGAY
  );
  const [ckSan] = useThietLap(THIET_LAP_CHUNG.CHUYEN_KHOA_SAN_KHOA, "");
  const [dataCHO_TICK_TU_TRA_NB_KHONG_BHYT] = useThietLap(
    THIET_LAP_CHUNG.CHO_TICK_TU_TRA_NB_KHONG_BHYT,
    ""
  );
  console.log(dataCHO_TICK_TU_TRA_NB_KHONG_BHYT);

  const listLoaiHinhThanhToanCuaDoiTuong = useStore(
    "loaiDoiTuongLoaiHinhTT.listLoaiHinhThanhToanCuaDoiTuong",
    []
  );

  const [kiemTraChungChi] = useKiemTraChungChi();

  const {
    phimTat: { onAddLayer, onRemoveLayer, onRegisterHotkey },
    chiDinhKhamBenh: {
      updateData,
      onSearchDichVu,
      onSearchGoiDichVu,
      tamTinhTien,
      chiDinhDichVu,
      searchDvKSKTachPhong,
      getDsGoiDvChiTiet,
      getDsDichVuTiepDon,
    },
    nhomDichVuCap2: { searchTongHopDichVuCap2 },
    loaiDoiTuongLoaiHinhTT: { getListLoaiDoiTuongTT },
    pttt: { getDsPhuCapPTTTChiTiet },
    noiLayBenhPham: { getListNoiLayMau },
    logNguoiDung: { guiLog },
    khamBenh: { getSlDichVu },
    goiDichVuChiTiet: { getAllBoChiDinhTheoLoai },
    mucDichSuDung: { getMucDichByDVId },
  } = useDispatch();

  const dsChuyenKhoaMemo = useMemo(() => {
    return dsChuyenKhoa?.map((item) => item.ma);
  }, [dsChuyenKhoa]);

  const [isCkSan] = useMemo(() => {
    if (id && dsChuyenKhoaMemo) {
      return [ckSan].map((thietLap) => {
        const thietLapArray = thietLap.split(",").map((item) => item.trim());

        return dsChuyenKhoaMemo.some((i) => thietLapArray.includes(i));
      });
    }

    return Array(5).fill(false);
  }, [id, dsChuyenKhoaMemo, ckSan]);

  useEffect(() => {
    if (loadFinish && dataColumns?.length) {
      let columnLoaiHinhThanhToan = dataColumns.filter(
        (s) => s.i18Name === "khamBenh.chiDinh.loaiHinhThanhToan" && s?.show
      );
      if (columnLoaiHinhThanhToan.length) {
        setState({ showLoaiHinhThanhToan: true });
      }
    }
  }, [loadFinish, dataColumns]);

  useImperativeHandle(ref, () => ({
    show: (
      {
        loaiDichVu,
        dsLoaiDichVu = [
          LOAI_DICH_VU.KHAM,
          LOAI_DICH_VU.XET_NGHIEM,
          LOAI_DICH_VU.CDHA,
          LOAI_DICH_VU.PHAU_THUAT_THU_THUAT,
        ],
        dsDoiTuongSuDung,
        nbThongTinId,
        nbDotDieuTriId,
        khoaChiDinhId,
        phongChiDinhId,
        chiDinhTuDichVuId,
        chiDinhTuLoaiDichVu,
        isPhauThuat = false,
        isHiddenTyLett = true,
        disableChiDinh = false,
        listLoaiChiDinhDV = [],
        doiTuong,
        loaiDoiTuongId,
        doiTuongKcb,
        thoiGianVaoVien,
        hopDongKskId,
        ngaySinh,
        dsNhaChiDinhId,
        gioiTinh,
        thoiGianThucHien,
        showChiDinhTuDichVuId = false,
        chiDinhTuDichVuIdOptions = {},
        khoaChiDinhMHPTTTId,
        isKhoaChiDinhDvTuMHPTTT = false,
        dsMaDvKhoaThucHien = [],
      },
      onOk
    ) => {
      setState({
        show: true,
        keyword: "",
        chiDinhTuDichVuId,
        chiDinhTuLoaiDichVu,
        isHiddenTyLett,
        isPhauThuat,
        listLoaiChiDinhDV,
        disableChiDinh,
        tuVanVienId: null,
        loaiDoiTuongId,
        errCode: null,
        errMes: null,
        hopDongKskId,
        showChiDinhTuDichVuId,
      });

      refOption.current = {
        dsLoaiDichVu: dsLoaiDichVu.join(","),
        nbDotDieuTriId,
        dsDoiTuongSuDung,
        khoaChiDinhId,
        phongChiDinhId,
        nbThongTinId,
        listLoaiChiDinhDV,
        doiTuong,
        loaiDoiTuongId,
        doiTuongKcb,
        thoiGianVaoVien,
        hopDongKskId,
        ngaySinh,
        dsNhaChiDinhId,
        gioiTinh,
        thoiGianThucHien,
        chiDinhTuDichVuIdOptions,
        khoaChiDinhMHPTTTId,
        isKhoaChiDinhDvTuMHPTTT,
        dsMaDvKhoaThucHien,
      };

      refIsSubmit.current = false;
      refCallback.current = onOk;
      onSelectServiceType(loaiDichVu);
      getListLoaiDoiTuongTT({
        loaiDoiTuongId: loaiDoiTuongId,
        khoaChiDinhId: khoaChiDinhId,
        doiTuongKcb: doiTuongKcb,
        active: true,
        page: "",
        size: "",
        ngaySinh: ngaySinh && moment(ngaySinh).format("YYYY-MM-DD"),
      });
      onSearchGoiDichVu({
        page: "",
        size: "",
        timKiem: "",
        khoaChiDinhId: refOption.current.khoaChiDinhId,
        dsBacSiChiDinhId: nhanVienId,
        dsDoiTuongSuDung: refOption.current.dsDoiTuongSuDung,
        dsLoaiDichVu: refOption.current.dsLoaiDichVu,
        sort: "ten,asc",
      });

      //Get ds dv tiếp đón để phục vụ việc check trùng
      let params = {
        active: true,
        nbDotDieuTriId: nbDotDieuTriId,
        dsChiDinhTuLoaiDichVu: [200, 230, 240, 10],
      };
      if (isNoiTru && CANH_BAO_TRUNG_CHI_DINH_TRONG_NGAY?.eval()) {
        params = {
          active: true,
          nbThongTinId: refOption.current.nbThongTinId,
          dsLoaiDichVu: [
            LOAI_DICH_VU.KHAM,
            LOAI_DICH_VU.XET_NGHIEM,
            LOAI_DICH_VU.CDHA,
            LOAI_DICH_VU.PHAU_THUAT_THU_THUAT,
            LOAI_DICH_VU.NGOAI_DIEU_TRI,
          ],
          tuThoiGianThucHien: moment()
            .startOf("day")
            .format("YYYY-MM-DD HH:mm:ss"),
          denThoiGianThucHien: moment()
            .endOf("day")
            .format("YYYY-MM-DD HH:mm:ss"),
          page: 0,
          size: 200,
        };
      }
      getDsDichVuTiepDon(params);

      onAddLayer({ layerId: layerId });
      onRegisterHotkey({
        layerId: layerId,
        hotKeys: [
          {
            keyCode: HOTKEY.F2, //F2
            onEvent: (e) => {
              if (refInput.current) {
                onSearch("");
                refInput.current.focus();
              }
            },
          },
          {
            keyCode: HOTKEY.F4, //F4
            onEvent: () => {
              refSubmit.current && refSubmit.current();
            },
          },
          {
            keyCode: HOTKEY.ENTER, //key enter
            onEvent: (e) => {
              if (refAddService.current) {
                refAddService.current();
              }
            },
          },
          {
            keyCode: HOTKEY.DOWN, // down
            onEvent: () => {
              if (refSelectRow.current) refSelectRow.current(1);
            },
          },
          {
            keyCode: HOTKEY.UP, //up
            onEvent: () => {
              if (refSelectRow.current) refSelectRow.current(-1);
            },
          },
        ],
      });
    },
  }));

  refSelectRow.current = (index) => {
    const indexNextItem =
      (listDichVu?.findIndex(
        (item) => (item.id || item.dichVuId) === state?.key
      ) || 0) + index;
    if (-1 < indexNextItem && indexNextItem < listDichVu.length) {
      setState({
        key:
          listDichVu[indexNextItem]?.id || listDichVu[indexNextItem]?.dichVuId,
      });
      document
        .getElementsByClassName(
          "table-row-odd " +
            (listDichVu[indexNextItem]?.id ||
              listDichVu[indexNextItem]?.dichVuId)
        )[0]
        .scrollIntoView({ block: "end", behavior: "smooth" });
    }
  };

  const phongChoMoId = useMemo(() => {
    return listAllPhong.find((item) => item.ma == dataPHONG_CHO_MO)?.id;
  }, [dataPHONG_CHO_MO, listAllPhong]);

  const splitCacheCustomizeMemo = useMemo(() => {
    return state.loaiDichVu !== LOAI_DICH_VU.BO_CHI_DINH
      ? splitCacheCustomize?.widthCacheDichVu
      : splitCacheCustomize?.widthCacheDichVuBoChiDinh;
  }, [splitCacheCustomize, state.loaiDichVu]);

  const listAllLoaiHinhThanhToan = useMemo(() => {
    if (isArray(listLoaiHinhThanhToanCuaDoiTuong)) {
      return listLoaiHinhThanhToanCuaDoiTuong.map((item) => ({
        id: item?.loaiHinhThanhToan?.id,
        ten: item.loaiHinhThanhToan?.ten,
      }));
    } else {
      return [
        {
          id: listLoaiHinhThanhToanCuaDoiTuong?.loaiHinhThanhToan?.id,
          ten: listLoaiHinhThanhToanCuaDoiTuong.loaiHinhThanhToan?.ten,
        },
      ];
    }
  }, [listLoaiHinhThanhToanCuaDoiTuong]);

  const listAllNhanVienMemo = useMemo(() => {
    return listAllNhanVien.map((item) => ({
      ...item,
      ten: `${item.ma} - ${item.ten}`,
    }));
  }, [listAllNhanVien]);

  useEffect(() => {
    if (!state.show) {
      onRemoveLayer({ layerId: layerId });
    }
    if (state.show) {
      refModal.current && refModal.current.show({});
      setTimeout(() => {
        refInput.current.focus();
      }, 1000);
    } else {
      refModal.current && refModal.current.hide({});
    }
  }, [state.show]);

  useEffect(() => {
    return () => {
      onRemoveLayer({ layerId: layerId });
    };
  }, []);

  const listDichVuMemo = useMemo(() => {
    let _listDichVu = (listDvKham || []).map((item, index) => ({
      ...item,
      key: index,
      uniqueKey: `${item.id || "dv"}-${item.dichVuId}`,
    }));

    setState({
      listDichVu: _listDichVu,
    });

    return _listDichVu;
  }, [listDvKham]);

  useEffect(() => {
    //tách effect này ra để tránh case setState liên tục khi loaiDichVu thay đổi
    //đảm bảo listDichVu ko bị setState lại với các loại dịch vụ khác
    if (state.loaiDichVu === "KSK") {
      let _listDichVu = listDichVuMemo.filter((item) => {
        if (state.loaiDichVu != "KSK") return true;

        let ten = `${item?.ma ? item?.ma : ""} ${item?.ten ? item?.ten : ""}`;
        let tenText = ten ? ten.trim().toLowerCase().unsignText() : "";
        let keyWordText = state.keyword
          ? state.keyword.trim().toLowerCase().unsignText()
          : "";

        return tenText.indexOf(keyWordText) >= 0;
      });

      setState({
        listDichVu: _listDichVu,
      });
    }
  }, [listDichVuMemo, state.loaiDichVu, state.keyword]);

  const keyNguoiThucHien =
    state.loaiDichVu === LOAI_DICH_VU.KHAM ? "bacSiKhamId" : "nguoiThucHienId";

  const getKhoaChiDinhId = (dichVu) => {
    if (dichVu.loaiDichVu !== LOAI_DICH_VU.PHAU_THUAT_THU_THUAT) {
      return refOption.current.khoaChiDinhId;
    }

    if (state.isPhauThuat && refOption.current.isKhoaChiDinhDvTuMHPTTT) {
      if (
        (refOption.current.dsMaDvKhoaThucHien || []).length > 0 &&
        (refOption.current.dsMaDvKhoaThucHien || []).includes(dichVu.ma)
      ) {
        return refOption.current.khoaChiDinhId;
      } else {
        return refOption.current.khoaChiDinhMHPTTTId;
      }
    }

    return refOption.current.khoaChiDinhId;
  };

  const onTamTinhTien = (listSelectedDv) => {
    const payload = listSelectedDv.map((item) => ({
      nbDotDieuTriId: refOption.current.nbDotDieuTriId,
      dsPhongThucHien: item.dsPhongThucHien,
      dsLoaiHinhThanhToan: item.dsLoaiHinhThanhToan,
      nbDichVu: {
        chiDinhTuDichVuId: state.showChiDinhTuDichVuId
          ? item.chiDinhTuDichVuId
          : state.chiDinhTuDichVuId,
        chiDinhTuLoaiDichVu: state.chiDinhTuLoaiDichVu,
        dichVu: {
          ten: item.ten,
          ma: item.ma,
        },
        dichVuId: item?.dichVuId,
        boChiDinhId: item.boChiDinhId || undefined,
        soLuong: item.soLuong || 1,
        loaiDichVu: item?.loaiDichVu,
        khoaChiDinhId: getKhoaChiDinhId(item),
        nbGoiDvId: item?.nbGoiDvId || undefined,
        nbGoiDvChiTietId: item?.nbGoiDvChiTietId || undefined,
        loaiHinhThanhToanId: item?.loaiHinhThanhToanId,
        tyLeTtDv:
          !state.isHiddenTyLett &&
          item?.loaiDichVu === LOAI_DICH_VU.PHAU_THUAT_THU_THUAT &&
          state.isPhauThuat
            ? item?.tyLeTtDv || 100
            : undefined,
        tuTra: item.tuTra,
        khongTinhTien: item.khongTinhTien,
        ghiChu: item.ghiChu,
        nguonKhacId: item.nguonKhacId,
        thoiGianThucHien: item.thoiGianThucHien,
      },
      boChiDinhId: item.boChiDinhId || undefined,
      nbDvKyThuat: {
        phongThucHienId: item.phongThucHienId,
        tuVanVienId: item.tuVanVienId || state.tuVanVienId,
        capCuu: item.capCuu,
        uuTien: item.uuTien,
      },
      benhPhamId:
        !item?.benhPhamId && item?.dsBenhPhamId?.length == 1
          ? item?.dsBenhPhamId?.[0]
          : item.benhPhamId,
      ...(item?.loaiDichVu === LOAI_DICH_VU.XET_NGHIEM
        ? { phongLayMauId: item.phongLayMauId }
        : {}),
      [keyNguoiThucHien]: item?.nguoiThucHienId,
      ...([LOAI_DICH_VU.PHAU_THUAT_THU_THUAT, LOAI_DICH_VU.CDHA].includes(
        item?.loaiDichVu
      )
        ? {
            ptTtNguoiThucHien: {
              bsNgoaiVienId: item?.bsNgoaiVienId,
              bsGayMeNgoaiVienId: item?.bsGayMeNgoaiVienId,
              ptv1Id: item?.ptv1Id,
              ptv2Id: item?.ptv2Id,
              ptv3Id: item?.ptv3Id,
              gayMe1Id: item?.gayMe1Id,
              gayMe2Id: item?.gayMe2Id,
              phuGayMe1Id: item?.phuGayMe1Id,
              phuGayMe2Id: item?.phuGayMe2Id,
              chayMayChinhId: item?.chayMayChinhId,
              chayMayPhuId: item?.chayMayPhuId,
              yTaGiupViecId: item?.yTaGiupViecId,
              yTaDungCu1Id: item?.yTaDungCu1Id,
              yTaDungCu2Id: item?.yTaDungCu2Id,
              yTaDungCu3Id: item?.yTaDungCu3Id,
              yTaDungCu4Id: item?.yTaDungCu4Id,
              yTaDungCu5Id: item?.yTaDungCu5Id,
              phuPtv1Id: item?.phuPtv1Id,
              phuPtv2Id: item?.phuPtv2Id,
              phuPtv3Id: item?.phuPtv3Id,
              thanhVienKhacId: item?.thanhVienKhacId,
            },
            phanLoaiPtTt: item?.phanLoaiPtTt,
            nguoiTiepNhanId: item?.nguoiTiepNhanId,
            dieuDuongId: item?.dieuDuongId,
          }
        : {}),
      ...(item?.loaiDichVu == LOAI_DICH_VU.XET_NGHIEM
        ? {
            phuThucHien1Id: item?.phuPtv1Id,
            phuThucHien2Id: item?.phuPtv2Id,
            phuThucHien3Id: item?.phuPtv3Id,
            thanhVienKhacId: item?.thanhVienKhacId,
            nguoiThucHien2Id: item?.nguoiThucHien2Id,
            phanTangNguyCoId: item.phanTangNguyCoId,
          }
        : {}),
    }));

    tamTinhTien({
      khoaChiDinhId: refOption.current.khoaChiDinhId,
      chiDinhTuDichVuId: state.chiDinhTuDichVuId,
      showChiDinhTuDichVuId: state.showChiDinhTuDichVuId,
      nbDotDieuTriId: refOption.current.nbDotDieuTriId,
      chiDinhTuLoaiDichVu: state.chiDinhTuLoaiDichVu,
      dsDichVu: payload,
    }).then((s) => {
      if (!Array.isArray(s) && s?.code != 0) {
        if (s?.code === 7826) {
          //Lỗi chỉ định số lượng lẻ
          setState({
            errCode: s?.code,
            errMes: s?.message || t("khamBenh.chiDinh.khongDuocChiDinhSlLe"),
          });

          return;
        } else if (s?.code === 8324) {
          //Lỗi Hồ sơ đã quyết toán Bảo hiểm Y tế
          setState({
            errCode: s?.code,
            errMes: s?.message || t("common.xayRaLoiVuiLongThuLaiSau"),
          });

          return;
        }
      }

      const dsDichVuChuaDenNgayHuongBHYT = [];
      const dsCanhBaoNgayHuongBHYTIds = [];

      listSelectedDv.forEach((item, index) => {
        if (!item.loaiHinhThanhToanId && Array.isArray(s)) {
          s.forEach((x) => {
            if (item.dichVuId === x.nbDichVu.dichVuId) {
              item.donGia =
                refOption.current.doiTuong === DOI_TUONG.BAO_HIEM
                  ? x.nbDichVu.giaBaoHiem
                  : x.nbDichVu.giaKhongBaoHiem;

              if (
                refOption.current.doiTuong === DOI_TUONG.BAO_HIEM &&
                item.loaiDichVu == LOAI_DICH_VU.XET_NGHIEM &&
                !!x.message &&
                x.message
                  .toLowerCase()
                  .unsignText()
                  .indexOf(MESS_CANH_BAO_HUONG_BHYT) >= 0
              ) {
                if (!item.boQuaCanhBaoNgayHuongBH) {
                  dsDichVuChuaDenNgayHuongBHYT.push({
                    ...item,
                    index: index,
                    message: x.message,
                  });
                }
                dsCanhBaoNgayHuongBHYTIds.push(item.dichVuId);
              }
            }
          });
        }
      });

      if (dsDichVuChuaDenNgayHuongBHYT.length > 0) {
        let _messageContent = dsDichVuChuaDenNgayHuongBHYT
          .map((item) => item.message)
          .join(", ");

        refModalCanhBaoDVChuaDenNgayHuongBH.current &&
          refModalCanhBaoDVChuaDenNgayHuongBH.current.show(
            _messageContent,
            () => {
              dsDichVuChuaDenNgayHuongBHYT.forEach((item) => {
                listSelectedDv[item.index].boQuaCanhBaoNgayHuongBH = true;
              });

              setState({
                thanhTien: thanhTien,
                listSelectedDv: listSelectedDv,
                indeterminate:
                  listSelectedDv.length &&
                  listSelectedDv.length < state.listDichVu.length,
                errCode: null,
                errMes: null,
              });
            },
            () => {
              dsDichVuChuaDenNgayHuongBHYT.forEach((item) => {
                listSelectedDv[item.index].tuTra = true;
              });

              setState({
                thanhTien: thanhTien,
                listSelectedDv: listSelectedDv,
                indeterminate:
                  listSelectedDv.length &&
                  listSelectedDv.length < state.listDichVu.length,
                errCode: null,
                errMes: null,
              });
            }
          );
      } else {
        let _dsDVBoQuaCheckTrung = [];
        let _message = (Array.isArray(s) ? s : [])
          .reduce((acc, cur) => {
            const isDvDaBoQuaCheckTrung =
              cur.code == 7690 &&
              (state.dsDichVuBoQuaCheckTrung || []).includes(
                cur.nbDichVu.dichVuId
              );

            if (cur.code == 7690) {
              _dsDVBoQuaCheckTrung.push(cur.nbDichVu.dichVuId);
            }

            if (
              cur.message &&
              !acc.includes(cur.message) &&
              !dsCanhBaoNgayHuongBHYTIds.includes(cur.nbDichVu.dichVuId) &&
              !isDvDaBoQuaCheckTrung
            ) {
              acc.push(cur.message);
            }
            return acc;
          }, [])
          .join(", ");
        const thanhTien = (Array.isArray(s) ? s : []).reduce(
          (accumulator, currentValue) =>
            accumulator + currentValue.nbDichVu.thanhTien,
          0
        );

        if (_message) {
          showConfirm(
            {
              title: t("common.canhBao"),
              content: _message,
              cancelText: t("common.huy"),
              showImg: false,
              typeModal: "warning",
              showBtnOk: true,
              okText: t("common.xacNhan"),
            },
            () => {
              setState({
                thanhTien: thanhTien,
                listSelectedDv: listSelectedDv,
                indeterminate:
                  listSelectedDv.length &&
                  listSelectedDv.length < state.listDichVu.length,
                errCode: null,
                errMes: null,
                //Thêm vào đánh dấu là đã bỏ qua check trùng => lần sau ko hiển thị cảnh báo nữa
                dsDichVuBoQuaCheckTrung: [
                  ...new Set([
                    ...(state.dsDichVuBoQuaCheckTrung || []),
                    ...(_dsDVBoQuaCheckTrung || []),
                  ]),
                ],
              });
            }
          );
        } else {
          setState({
            thanhTien: thanhTien,
            listSelectedDv: listSelectedDv,
            indeterminate:
              listSelectedDv.length &&
              listSelectedDv.length < state.listDichVu.length,
            errCode: null,
            errMes: null,
          });
        }
      }
    });
  };

  const onChangeSoLuong = (_uniqueKey, key, isIgnoreCheckDinhMuc) => {
    return async (e, data) => {
      let value = e;
      if (key == "tuTra" || key == "capCuu" || key == "uuTien") {
        value = e?.target?.checked;
      }
      let _listSelectedDv = Object.assign([], listSelectedDv);
      const _findDvIndex = _listSelectedDv.findIndex(
        (x) => x.uniqueKey === _uniqueKey
      );
      if (_findDvIndex !== -1) {
        if (isIgnoreCheckDinhMuc) {
          const loaiHinhThanhToanId =
            _listSelectedDv[_findDvIndex].loaiHinhThanhToanId;
          const dsLoaiHinhThanhToan =
            _listSelectedDv[_findDvIndex]?.dsLoaiHinhThanhToan;
          _listSelectedDv[_findDvIndex].loaiHinhThanhToanId =
            value?.loaiHinhThanhToanId || loaiHinhThanhToanId;
          let slDichVu = null;
          if (
            dsLoaiHinhThanhToan?.find(
              (x) =>
                x.loaiHinhThanhToanId ===
                _listSelectedDv[_findDvIndex].loaiHinhThanhToanId
            )?.maLoaiHinhThanhToan ==
            dataMA_LOAI_HINH_THANH_TOAN_HEN_THUC_HIEN_KHI_VUOT_DINH_MUC
          ) {
            slDichVu = await getSlDichVu({
              phongId: _listSelectedDv[_findDvIndex]?.phongThucHienId,
              tuThoiGian: moment().add(1, "days").format("YYYY-MM-DD"),
              denThoiGian: moment().add(1, "months").format("YYYY-MM-DD"),
              doiTuongKcb: refOption.current.doiTuongKcb,
            });
            slDichVu = (slDichVu?.data || []).find(
              (x) =>
                x.dinhMucNgoaiTru > x.soLuongNgoaiTru ||
                x.dinhMucNoiTru > x.soLuongNoiTru
            );
            if (slDichVu) {
              _listSelectedDv[_findDvIndex].thoiGianThucHien =
                slDichVu?.ngayThucHien && moment(slDichVu?.ngayThucHien);
            }
          }
        } else {
          _listSelectedDv[_findDvIndex][key] = value;
        }

        if (key === "loaiHinhThanhToanId" || isIgnoreCheckDinhMuc) {
          const loaiHinhThanhToanId = isIgnoreCheckDinhMuc
            ? value?.loaiHinhThanhToanId
            : value;
          let giaBaoHiem = _listSelectedDv[_findDvIndex].giaBaoHiem;
          let giaKhongBaoHiem = _listSelectedDv[_findDvIndex].giaKhongBaoHiem;
          if (data) {
            giaBaoHiem = data?.giaBaoHiem;
            giaKhongBaoHiem = data?.giaKhongBaoHiem;
          }

          if (data?.loaiGia) {
            _listSelectedDv[_findDvIndex].donGia = getDonGiaByLoai(data);
          } else {
            _listSelectedDv[_findDvIndex].donGia =
              refOption.current.doiTuong === DOI_TUONG.BAO_HIEM
                ? giaBaoHiem
                : giaKhongBaoHiem;
          }

          let resNoiLayMau = [];
          if (
            _listSelectedDv[_findDvIndex].loaiDichVu === LOAI_DICH_VU.XET_NGHIEM
          ) {
            resNoiLayMau = await getListNoiLayMau({
              khoaChiDinhId: refOption.current.khoaChiDinhId,
              dsDoiTuongKcb: refOption.current.doiTuongKcb,
              dichVuId: _listSelectedDv[_findDvIndex].dichVuId,
              nhomDichVuCap2Id: _listSelectedDv[_findDvIndex].nhomDichVuCap2Id,
              nhomDichVuCap3Id: _listSelectedDv[_findDvIndex].nhomDichVuCap3Id,
              dsLoaiDoiTuongId: refOption.current.loaiDoiTuongId,
              phongChiDinhId: refOption.current.phongChiDinhId,
              loaiHinhThanhToanId: loaiHinhThanhToanId,
              nhaChiDinhId: refOption.current.dsNhaChiDinhId,
              thoiGianThucHien:
                refOption.current.thoiGianThucHien &&
                moment(refOption.current.thoiGianThucHien).format(),
            });

            _listSelectedDv[_findDvIndex].dsNoiLayMau = resNoiLayMau || [];

            if ((resNoiLayMau || []).length === 1) {
              //nếu có 1 phòng thì set giá trị đó
              _listSelectedDv[_findDvIndex].phongLayMauId =
                resNoiLayMau[0].phongLayMauId;
            } else if (
              (resNoiLayMau || []).findIndex(
                (item) =>
                  item.phongLayMauId ==
                  _listSelectedDv[_findDvIndex].phongLayMauId
              ) == -1
            ) {
              //nếu phòng đã chọn ko tồn tại trong list mới thì set về null
              _listSelectedDv[_findDvIndex].phongLayMauId = null;
            }
          }

          let resPhongThucHien = (
            _listSelectedDv[_findDvIndex]._dsPhongThucHien || []
          ).filter(
            (x) =>
              !x.loaiHinhThanhToanId ||
              !loaiHinhThanhToanId ||
              x.loaiHinhThanhToanId == loaiHinhThanhToanId
          );

          _listSelectedDv[_findDvIndex].dsPhongThucHien = uniqBy(
            resPhongThucHien || [],
            "phongId"
          );

          if ((resPhongThucHien || []).length === 1) {
            //nếu có 1 phòng thì set giá trị đó
            _listSelectedDv[_findDvIndex].phongThucHienId =
              resPhongThucHien[0].phongId;
          } else if (
            (resPhongThucHien || []).findIndex(
              (item) =>
                item.phongId == _listSelectedDv[_findDvIndex].phongThucHienId
            ) == -1
          ) {
            //nếu phòng đã chọn ko tồn tại trong list mới thì set về null
            _listSelectedDv[_findDvIndex].phongThucHienId = null;
          }
        }
        if (key === "phanLoaiPtTt") {
          if (
            [
              LOAI_DICH_VU.PHAU_THUAT_THU_THUAT,
              LOAI_DICH_VU.CDHA,
              LOAI_DICH_VU.XET_NGHIEM,
            ].includes(_listSelectedDv[_findDvIndex]?.loaiDichVu) &&
            (_listSelectedDv[_findDvIndex]?.phanLoaiPtTt ||
              _listSelectedDv[_findDvIndex]?.phanLoaiPtTt == 0)
          ) {
            let resPhuCap = await getDsPhuCapPTTTChiTiet(
              {
                dichVuId: _listSelectedDv[_findDvIndex]?.dichVuId,
                nhomDichVuCap2Id:
                  _listSelectedDv[_findDvIndex]?.nhomDichVuCap2Id,
                nhomDichVuCap1Id:
                  _listSelectedDv[_findDvIndex]?.nhomDichVuCap1Id,
                phanLoai: _listSelectedDv[_findDvIndex]?.phanLoaiPtTt,
                ngay: moment().format("YYYY-MM-DD"),
              },
              true
            );

            _listSelectedDv[_findDvIndex].dsPhuCapPtTtChiTiet =
              resPhuCap?.dsPhuCapPtTtChiTiet || [];
          }
        }
        if (
          (key === "loaiHinhThanhToanId" || key === "phongThucHienId") &&
          _listSelectedDv[_findDvIndex]?.phongThucHienId &&
          _listSelectedDv[_findDvIndex]?.loaiHinhThanhToanId &&
          !isIgnoreCheckDinhMuc
        ) {
          checkCanhBaoDinhMuc({
            listDichVu: _listSelectedDv,
            data: _listSelectedDv[_findDvIndex],
            _uniqueKey,
            key,
            onSelectContinue: onChangeSoLuong,
          });
          setState({ listSelectedDv: _listSelectedDv });
        } else {
          setState({ listSelectedDv: _listSelectedDv });

          onTamTinhTien(_listSelectedDv);
        }
      }
    };
  };

  const { listDichVu, listSelectedDv, thanhTien, keyword, loaiDichVu } = state;

  useEffect(() => {
    // if (elementKey !== 1) return;
    if (
      !state.disableChiDinh &&
      state.show &&
      state.loaiDichVu !== LOAI_DICH_VU.BO_CHI_DINH
    ) {
      onChangePage(1); //reset lại size trường hợp chuyển từ bộ chỉ định
    } else {
      updateData({
        listDvKham: [],
        disableChiDinh: state.disableChiDinh,
      });
    }
  }, [state.disableChiDinh, state.show, state.loaiDichVu]);

  useEffect(() => {
    state.show &&
      ((state.activeLink != -1 &&
        state.loaiDichVu === LOAI_DICH_VU.BO_CHI_DINH &&
        TU_DONG_CHECK_ALL_DICH_VU_TRONG_BO?.eval()) ||
        state.boChiDinhSelectedId) &&
      checkAllDichVu(true);
  }, [listDichVu]);

  const onSelectServiceType = (value = "") => {
    if (state.disableChiDinh) return;
    updateData({
      listLoaiDichVu: value ? [value] : [],
    });

    const _selectedGroup = refOption.current.listLoaiChiDinhDV.find(
      (x) => x.id === value
    );

    if (
      value != "" &&
      value != LOAI_DICH_VU.BO_CHI_DINH &&
      !_selectedGroup?.goiDvId
    ) {
      searchTongHopDichVuCap2({
        page: "",
        size: "",
        active: true,
        sort: "ten,asc",
        loaiDichVu: value == "KSK" ? LOAI_DICH_VU.GOI_KSK : value,
      });
    }
    setState({
      loaiDichVu: value,
      indeterminate: false,
      keyword: "",
      listDichVu: [],
      boChiDinhId: null,
      // listSelectedDv: [],
      dsNhomDichVuCap2Id: [],
    });
    if (value !== "KSK") {
      if (_selectedGroup?.goiDvId) {
        getDsGoiDvChiTiet({
          page: 0,
          goiDvId: _selectedGroup.goiDvId,
          nbGoiDvId: _selectedGroup.nbGoiDvId,
          // nbDotDieuTriId: id,
          nbThongTinId: refOption.current.nbThongTinId,
          dangSuDung: true,
        });
        return;
      }

      if (value !== LOAI_DICH_VU.BO_CHI_DINH) {
        onSearchDichVu2({ page: 0, keyword: "", loaiDichVu: value });
      }
    } else {
      searchDvKSKTachPhong({
        nbDotDieuTriId: refOption.current.nbDotDieuTriId,
        hopDongKsk: true,
        hopDongKskId: refOption.current.hopDongKskId,
        doiTuongKcb: refOption.current.doiTuongKcb,
      });
    }
    // getBoChiDinh({ dsLoaiDichVu: value })
  };

  const onSubmit = async () => {
    try {
      if (refIsSubmit.current) return; //nếu đang submit thì bỏ qua

      const isDaCoChungChi = await kiemTraChungChi();
      if (!isDaCoChungChi) {
        return;
      }

      const { listSelectedDv } = state;
      if (
        state.loaiDichVu == LOAI_DICH_VU.BO_CHI_DINH &&
        !listSelectedDv.every((x) => x.soLuong > 0)
      ) {
        message.error(t("khamBenh.vuiLongNhapSoLuongLon0"));
        return;
      }

      if (!listSelectedDv.length) {
        message.error(t("khamBenh.chiDinh.yeuCauNhapChiDinhDichVu"));
        return;
      }

      if (state.showChiDinhTuDichVuId) {
        //Yêu cầu bắt buộc chọn chiDinhTuDichVuId
        if (listSelectedDv.some((x) => !x.chiDinhTuDichVuId)) {
          message.error(
            t("khamBenh.chiDinh.vuiLongChonChiDinhTuDichVuId", {
              chiDinhTuDichVuId:
                refOption.current.chiDinhTuDichVuIdOptions?.title || "",
            })
          );
          return;
        }
      }

      if (state.errCode === 7826) {
        message.error(state.errMes);
        return;
      }
      setState({
        filterText: "",
      });
      let dsDichVuCanBoSung = [];
      let dsDichVuThoaDieuKien = [];

      let checkDvKham = [];
      listSelectedDv.forEach((x) => {
        if (x.loaiDichVu === LOAI_DICH_VU.KHAM) {
          dsDichVuKham.forEach((x1) => {
            if (
              x1.dichVuId === x.dichVuId &&
              x1.phongThucHienId === x.phongThucHienId
            ) {
              checkDvKham.push(x1);
            }
          });
        }
      });
      if (
        CHAN_TRUNG_PHONG_KHAM?.eval() &&
        [
          DOI_TUONG_KCB.NGOAI_TRU,
          DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_LINH_THUOC,
          DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_SU_DUNG_THUOC,
        ].includes(refOption.current.doiTuongKcb) &&
        checkDvKham?.length
      ) {
        showConfirm({
          title: t("common.thongBao"),
          content: `${t(
            "khamBenh.phongBanVuaChonDaDuocChiDinhTruocDoVuiLongChonPhongKhac",
            { tenPhong: state.phong?.ten }
          )}`,
          cancelText: t("common.huy"),
          classNameOkText: "button-warning",
          typeModal: "warning",
        });
        return;
      }
      listSelectedDv.filter((item) => {
        if (
          (item?.dsPhongThucHien?.length > 1 &&
            !item.phongThucHienId &&
            !dataThucHienCanBang?.eval()) ||
          (item?.yeuCauBenhPham && !item.benhPhamId) ||
          (item?.dsMucDich?.length &&
            refOption.current.doiTuong === DOI_TUONG.BAO_HIEM) ||
          (item.dsLoaiHinhThanhToan?.length &&
            !item.loaiHinhThanhToanId &&
            !item.boChiDinhId) ||
          (item?.yeuCauPhanTangNguyCo && !item.phanTangNguyCoId)
        ) {
          dsDichVuCanBoSung.push(item);
        } else {
          dsDichVuThoaDieuKien.push(item?.dichVuId);
        }
      });
      //lấy ra ds dịch vụ đủ điều kiện và ds dịch vụ cần bổ sung
      dsDichVuCanBoSung = dsDichVuCanBoSung.map((item, index) => ({
        yeuCauBenhPham: item?.yeuCauBenhPham,
        dsPhongThucHien: item?.dsPhongThucHien,
        dsMucDich: item?.dsMucDich,
        dsLoaiHinhThanhToan: item?.dsLoaiHinhThanhToan,
        nbDichVu: {
          chiDinhTuDichVuId: state.showChiDinhTuDichVuId
            ? item.chiDinhTuDichVuId
            : state.chiDinhTuDichVuId,
          chiDinhTuLoaiDichVu: state.chiDinhTuLoaiDichVu,
          dichVu: {
            ten: item?.ten,
          },
          dichVuId: item?.dichVuId,
          soLuong: item.soLuong || 1,
          loaiDichVu: item?.loaiDichVu,
          khoaChiDinhId: getKhoaChiDinhId(item),
          nbGoiDvId: item?.nbGoiDvId || undefined,
          nbGoiDvChiTietId: item?.nbGoiDvChiTietId || undefined,
          loaiHinhThanhToanId: item?.loaiHinhThanhToanId,
          tyLeTtDv:
            !state.isHiddenTyLett &&
            item?.loaiDichVu === LOAI_DICH_VU.PHAU_THUAT_THU_THUAT &&
            state.isPhauThuat
              ? item?.tyLeTtDv || 100
              : undefined,
          tuTra: item?.tuTra,
          khongTinhTien: item.khongTinhTien,
          nguonKhacId: item.nguonKhacId,
          thoiGianThucHien: item.thoiGianThucHien,
        },
        boChiDinhId: item.boChiDinhId || undefined,
        nbDotDieuTriId: refOption.current.nbDotDieuTriId,
        nbDvKyThuat: {
          // phongThucHienId: item.nbDvKyThuat?.phongThucHienId,
          tuVanVienId: state.tuVanVienId,
          capCuu: item.capCuu,
          uuTien: item.uuTien,
        },
        key: index,
        index: index + 1,
        phongId: item.phongId,
        benhPhamId:
          !item?.benhPhamId && item?.dsBenhPhamId?.length == 1
            ? item?.dsBenhPhamId?.[0]
            : item.benhPhamId,
        ...(item?.loaiDichVu === LOAI_DICH_VU.XET_NGHIEM
          ? { phongLayMauId: item.phongLayMauId }
          : {}),
        [keyNguoiThucHien]: item?.nguoiThucHienId,
        ...([LOAI_DICH_VU.PHAU_THUAT_THU_THUAT, LOAI_DICH_VU.CDHA].includes(
          item?.loaiDichVu
        )
          ? {
              ptTtNguoiThucHien: {
                bsNgoaiVienId: item?.bsNgoaiVienId,
                bsGayMeNgoaiVienId: item?.bsGayMeNgoaiVienId,
                ptv1Id: item?.ptv1Id,
                ptv2Id: item?.ptv2Id,
                ptv3Id: item?.ptv3Id,
                gayMe1Id: item?.gayMe1Id,
                gayMe2Id: item?.gayMe2Id,
                phuGayMe1Id: item?.phuGayMe1Id,
                phuGayMe2Id: item?.phuGayMe2Id,
                chayMayChinhId: item?.chayMayChinhId,
                chayMayPhuId: item?.chayMayPhuId,
                yTaGiupViecId: item?.yTaGiupViecId,
                yTaDungCu1Id: item?.yTaDungCu1Id,
                yTaDungCu2Id: item?.yTaDungCu2Id,
                yTaDungCu3Id: item?.yTaDungCu3Id,
                yTaDungCu4Id: item?.yTaDungCu4Id,
                yTaDungCu5Id: item?.yTaDungCu5Id,
                phuPtv1Id: item?.phuPtv1Id,
                phuPtv2Id: item?.phuPtv2Id,
                phuPtv3Id: item?.phuPtv3Id,
                thanhVienKhacId: item?.thanhVienKhacId,
              },
              phanLoaiPtTt: item?.phanLoaiPtTt,
              nguoiTiepNhanId: item?.nguoiTiepNhanId,
              dieuDuongId: item?.dieuDuongId,
            }
          : {}),
        ...(item?.loaiDichVu == LOAI_DICH_VU.XET_NGHIEM
          ? {
              phuThucHien1Id: item?.phuPtv1Id,
              phuThucHien2Id: item?.phuPtv2Id,
              phuThucHien3Id: item?.phuPtv3Id,
              thanhVienKhacId: item?.thanhVienKhacId,
              nguoiThucHien2Id: item?.nguoiThucHien2Id,
              phanTangNguyCoId: item.phanTangNguyCoId,
            }
          : {}),
      }));

      if (dsDichVuThoaDieuKien.length > 0) {
        // tạo dịch vụ nếu đủ điều kiện
        let dataTamTinhTienDichVuDuThoaDieuKien = dataTamTinhTien.filter(
          (item) =>
            dsDichVuThoaDieuKien.some(
              (dichVuId) => dichVuId == item.nbDichVu.dichVuId
            )
        );
        if (dataThucHienCanBang?.eval()) {
          const noiDungLog = dataTamTinhTien.map((x) => ({
            phongId: x.phongId,
            dsPhongThucHien: x.dsPhongThucHien,
            dichVuId: x?.nbDichVu?.dichVuId,
          }));

          const data = {
            thoiGianThucHien: moment().format("YYYY-MM-DD HH:mm:ss"),
            tenMay: `${mayTinhId || ""}|${window.tabId}`,
            url: window.location.href,
            noiDung: `nbDotDieuTriId : ${
              refOption.current.nbDotDieuTriId
            } |${JSON.stringify(noiDungLog)}`,
          };
          guiLog(data);
        }
        refIsSubmit.current = true;
        const res = await chiDinhDichVu({
          dataTamTinhTien: dataTamTinhTienDichVuDuThoaDieuKien,
          dsDichVuCanBoSung,
          isShowDvTiepDon,
        });
        setState({
          listSelectedDv: [],
          listDichVu:
            state.loaiDichVu === LOAI_DICH_VU.BO_CHI_DINH
              ? []
              : state.listDichVu,
        });
        if (res.code == 0) {
          onCancel();
          refCallback.current && refCallback.current();
        } else {
          refIsSubmit.current = false;
        }
        dsDichVuCanBoSung = res.dsDichVuCanBoSung;
        //ở đây dùng res.dsDichVuCanBoSung bởi vì đã merge với ds trả về lỗi, nếu chỉ dùng dsDichVuCanBoSung thì sẽ bị thiếu những dịch vụ kê bị lỗi
        onShowDichVuBoSung(dsDichVuCanBoSung);

        const response = res.response || [];
        if (!dsDichVuCanBoSung?.length && response?.length) {
          let item = response.filter((x) => x.data).map((x1) => x1.data);
          let messageWarning = item[item.length - 1].filter((x) => x.message);
          let content = messageWarning[messageWarning?.length - 1]?.message;
          content &&
            showConfirm(
              {
                title: t("common.canhBao"),
                content: content,
                cancelText: t("common.dong"),
                classNameOkText: "button-error",
                showImg: true,
                typeModal: "warning",
              },
              () => {}
            );
        }

        if (response.length) {
          onDichVuKemTheo(res.response);
        }
      } else {
        onCancel();
        onShowDichVuBoSung(dsDichVuCanBoSung);
      }
    } catch (error) {
      console.error("err", error);
      refIsSubmit.current = false;
    }
  };

  const onDichVuKemTheo = async (data, khoId) => {
    let item = data.filter((x) => x.data).map((x1) => x1.data);
    let newTable = [];
    item.forEach((x) => {
      x.forEach((x1) => {
        if (x1.nbDichVu.dichVu.loaiDichVu === LOAI_DICH_VU.CDHA) {
          (x1.dsDvKemTheo || []).forEach((x2) => {
            if ([7624, 8501].includes(x2.code)) newTable.push(x2);
          });
        }
      });
    });
    if (newTable.length) {
      const _dsDichVu = await Promise.all(
        newTable.map(async (item) => {
          const _dsMucDich = await getMucDichByDVId({
            page: "",
            size: "",
            dichVuId: item?.nbDichVu?.dichVuId,
          });

          console.log("_dsMucDich", _dsMucDich);

          return {
            ...item,
            dsMucDich: _dsMucDich,
          };
        })
      );

      refModalThongTinThuoc.current &&
        refModalThongTinThuoc.current.show({
          newTable: _dsDichVu,
          nbDotDieuTriId: refOption.current.nbDotDieuTriId,
          chiDinhTuDichVuId: state.chiDinhTuDichVuId,
        });
    }
  };

  const onShowDichVuBoSung = (dsDichVuCanBoSung) => {
    refModalBoSungThongTinDichVu.current &&
      dsDichVuCanBoSung?.length &&
      refModalBoSungThongTinDichVu.current.show(
        {
          dataSource: dsDichVuCanBoSung,
          isPhauThuat: state.isPhauThuat,
          isShowDvTiepDon,
        },
        () => {
          refCallback.current();
        }
      );
  };

  const onCancel = () => {
    setState({
      show: false,
      thanhTien: 0,
      listDichVu:
        state.loaiDichVu === LOAI_DICH_VU.BO_CHI_DINH ? [] : state.listDichVu,
      indeterminate: false,
      listSelectedDv: [],
      filterText: "",
      activeLink: -1,
      loaiHinhThanhToanId: null,
      boChiDinhSelectedId: null,
      dsDichVuBoQuaCheckTrung: [],
    });
    refModal.current && refModal.current.hide();
  };

  const onSearch = useRefFunc((keyword) => {
    setState({
      keyword: keyword,
    });
    if (state.loaiDichVu === LOAI_DICH_VU.BO_CHI_DINH) {
      onSearchGoiDichVu({
        timKiem: keyword,
        page: 0,
        size: 500,
        khoaChiDinhId: refOption.current.khoaChiDinhId,
        dsBacSiChiDinhId: nhanVienId,
        dsDoiTuongSuDung: refOption.current.dsDoiTuongSuDung,
        sort: "ten,asc",
        dsLoaiDichVu: refOption.current.dsLoaiDichVu,
      });
    } else {
      onSearchDichVu2({
        keyword,
        page: 0,
        size,
        loaiDichVu: state.loaiDichVu,
        dsNhomDichVuCap2Id: state.dsNhomDichVuCap2Id,
        boChiDinhId: state?.boChiDinhSelectedId,
      });
    }
  });

  const onSelectGoiDichVu = (item) => (e) => {
    setState({ activeLink: item.dichVuId });

    onSearchDichVu2({
      page: 0,
      size: 50,
      loaiDichVu: null,
      boChiDinhId: item.dichVuId,
    });
    e.preventDefault();
  };

  const listDichVuDaKe = useMemo(() => {
    return [
      ...dsDichVuChiDinhCls,
      ...dsDichVuChiDinhKham,
      ...dsDichVuChiDinhXN,
      ...dsDichVuNgoaiDieuTri,
      ...(isKhamBenh || (isNoiTru && CANH_BAO_TRUNG_CHI_DINH_TRONG_NGAY?.eval())
        ? listDvTiepDon
        : []),
    ];
  }, [
    dsDichVuChiDinhCls,
    dsDichVuChiDinhKham,
    dsDichVuChiDinhXN,
    dsDichVuNgoaiDieuTri,
    listDvTiepDon,
    isKhamBenh,
    isNoiTru,
    CANH_BAO_TRUNG_CHI_DINH_TRONG_NGAY,
  ]);

  const phanPhongDichVu = (updatedListDv) => {
    const dsPhongByLevel = getGroupPhong(updatedListDv);
    let data = updatedListDv.map((item) => {
      if (item?.dsPhongThucHien?.length === 1) {
        return {
          ...item,
          phongThucHienId: item.dsPhongThucHien[0].phongId,
        };
      } else if (item?.dsPhongThucHien?.length > 1) {
        let phongThucHien = null;

        for (let index = 0; index < dsPhongByLevel.length; index++) {
          const level = dsPhongByLevel[index];

          const phongChung = level.phong.filter((phong) =>
            item.dsPhongThucHien.find((item2) => item2.phongId == phong.phongId)
          );
          if (phongChung.length) {
            phongThucHien = phongChung[0];
            break;
          }
        }

        return {
          ...item,
          phongThucHienId: phongThucHien?.phongId,
          canBangTai: true,
        };
      }
      return item;
    });
    return data;
  };

  const getGroupPhong = (updatedListDv) => {
    let group = groupBy(
      flatten(
        updatedListDv.map((item) =>
          item.dsPhongThucHien.filter((x) => !x.boQuaCanBangTai)
        )
      ),
      (item) => item.phongId
    );

    const x1 = Object.entries(
      groupBy(Object.values(group), (item) => item.length)
    )
      .map((item) => ({
        soLuong: item[0],
        phong: item[1]
          .map((item) => item[0])
          .sort((a, b) => {
            if (b.slDichVuNbChuaHoanThanh - a.slDichVuNbChuaHoanThanh !== 0) {
              return b.slDichVuNbChuaHoanThanh - a.slDichVuNbChuaHoanThanh;
            }
            if (
              dataCAN_BANG_PHONG_THUC_HIEN_THEO_TONG_THOI_GIAN_THUC_HIEN?.eval()
            ) {
              const thoiGianCho =
                a.thoiGianChuaHoanThanh +
                a.thoiGianDaHoanThanh -
                b.thoiGianChuaHoanThanh -
                b.thoiGianDaHoanThanh;
              if (thoiGianCho === 0) {
                return (
                  a.slDichVuChuaHoanThanh +
                  a.slDichVuDaHoanThanh -
                  b.slDichVuChuaHoanThanh -
                  b.slDichVuDaHoanThanh
                );
              }
              return (
                a.thoiGianChuaHoanThanh +
                a.thoiGianDaHoanThanh -
                b.thoiGianChuaHoanThanh -
                b.thoiGianDaHoanThanh
              );
            } else if (
              a.thoiGianChuaHoanThanh - b.thoiGianChuaHoanThanh !==
              0
            ) {
              return a.thoiGianChuaHoanThanh - b.thoiGianChuaHoanThanh;
            } else return b.slDichVuNbChuaHoanThanh - a.slDichVuNbChuaHoanThanh;
          }),
      }))
      .sort((a, b) => b.soLuong - a.soLuong);
    return x1;
  };

  const checkCanhBaoDinhMuc = ({
    listDichVu,
    data,
    checked,
    onSelectContinue,
    _uniqueKey,
    key,
  }) => {
    const item = listDichVu?.find((x) => x.uniqueKey === data.uniqueKey);
    const phongThucHien =
      item?.dsPhongThucHien?.find((x) => x.phongId === item?.phongThucHienId) ||
      {};
    const { dinhMucNgoaiTru, soLuongNgoaiTru, soLuongNoiTru, dinhMucNoiTru } =
      phongThucHien;
    let soLuongDinhMucNgoaiTru = dinhMucNgoaiTru - (soLuongNgoaiTru || 0);
    let soLuongDinhMucNoiTru = dinhMucNoiTru - (soLuongNoiTru || 0);

    if (
      dinhMucNgoaiTru &&
      soLuongDinhMucNgoaiTru <= 0 &&
      item?.phongThucHienId &&
      [
        DOI_TUONG_KCB.NGOAI_TRU,
        DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_LINH_THUOC,
        DOI_TUONG_KCB.NHAN_THUOC_THEO_HEN,
        DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_SU_DUNG_THUOC,
        DOI_TUONG_KCB.CAC_TRUONG_HOP_KHAC,
      ].includes(refOption.current.doiTuongKcb)
    ) {
      showPopupDinhMuc({
        data: item,
        checked,
        onSelectContinue,
        _uniqueKey,
        key,
      });
    } else if (
      dinhMucNoiTru &&
      soLuongDinhMucNoiTru <= 0 &&
      item?.phongThucHienId &&
      [
        DOI_TUONG_KCB.DIEU_TRI_NGOAI_TRU,
        DOI_TUONG_KCB.DIEU_TRI_NOI_TRU,
        DOI_TUONG_KCB.DIEU_TRI_NOI_TRU_BAN_NGAY,
        DOI_TUONG_KCB.DIEU_TRI_LUU_TRU_TAI_TRAM_Y_TE_TUYEN_XA,
        DOI_TUONG_KCB.DIEU_TRI_NOI_TRU_DUOI_BON_GIO,
      ].includes(refOption.current.doiTuongKcb)
    ) {
      showPopupDinhMuc({
        data: item,
        checked,
        onSelectContinue,
        _uniqueKey,
        key,
      });
    } else {
      onTamTinhTien(listDichVu);
    }
  };

  const showPopupDinhMuc = ({
    data,
    checked,
    onSelectContinue,
    _uniqueKey,
    key,
  }) => {
    const dsLoaiHinhThanhToan = data?.dsLoaiHinhThanhToan?.filter((x) =>
      data?._dsPhongThucHien
        .filter(
          (item) =>
            ([
              DOI_TUONG_KCB.NGOAI_TRU,
              DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_LINH_THUOC,
              DOI_TUONG_KCB.NHAN_THUOC_THEO_HEN,
              DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_SU_DUNG_THUOC,
              DOI_TUONG_KCB.CAC_TRUONG_HOP_KHAC,
            ].includes(refOption.current.doiTuongKcb) &&
              item.dinhMucNgoaiTru > (item.soLuongNgoaiTru || 0)) ||
            ([
              DOI_TUONG_KCB.DIEU_TRI_NGOAI_TRU,
              DOI_TUONG_KCB.DIEU_TRI_NOI_TRU,
              DOI_TUONG_KCB.DIEU_TRI_NOI_TRU_BAN_NGAY,
              DOI_TUONG_KCB.DIEU_TRI_LUU_TRU_TAI_TRAM_Y_TE_TUYEN_XA,
              DOI_TUONG_KCB.DIEU_TRI_NOI_TRU_DUOI_BON_GIO,
            ].includes(refOption.current.doiTuongKcb) &&
              item.dinhMucNoiTru > (item.soLuongNoiTru || 0)) ||
            x.maLoaiHinhThanhToan ==
              dataMA_LOAI_HINH_THANH_TOAN_HEN_THUC_HIEN_KHI_VUOT_DINH_MUC
        )
        .map((x1) => x1.loaiHinhThanhToanId)
        ?.includes(x.loaiHinhThanhToanId)
    );
    refModalVuotDinhMuc.current &&
      refModalVuotDinhMuc.current.show(
        {
          dsLoaiHinhThanhToan: dsLoaiHinhThanhToan,
          loaiHinhThanhToanId: data?.loaiHinhThanhToanId,
        },
        (loaiHinhThanhToan) => {
          if (_uniqueKey) {
            onSelectContinue(_uniqueKey, key, true)(loaiHinhThanhToan);
          } else {
            onSelectContinue(checked, loaiHinhThanhToan);
          }
          return true;
        },
        () => {
          if (_uniqueKey) {
            onSelectContinue(_uniqueKey, key, true)();
          } else {
            onSelectContinue(checked);
          }
        }
      );
  };

  const onSelectDichVu = (record, isEnter, isSelected) => async (e) => {
    let isSetPhongChoMo = false;
    if (e?.target?.hasAttribute("type") || isEnter) {
      const checked = e?.target?.checked || isSelected;

      const onSelectContinue = async (checked, _loaiHinhThanhToan) => {
        const { listSelectedDv } = state;
        let updatedListDv = [];
        let data = null;
        const _loaiHinhThanhToanId = _loaiHinhThanhToan?.loaiHinhThanhToanId;
        if (checked) {
          let resPhuCap = {};
          if (
            [
              LOAI_DICH_VU.PHAU_THUAT_THU_THUAT,
              LOAI_DICH_VU.CDHA,
              LOAI_DICH_VU.XET_NGHIEM,
            ].includes(record?.loaiDichVu) &&
            (record.phanLoaiPtTt || record.phanLoaiPtTt == 0)
          ) {
            resPhuCap = await getDsPhuCapPTTTChiTiet(
              {
                dichVuId: record.dichVuId,
                nhomDichVuCap2Id: record.nhomDichVuCap2Id,
                nhomDichVuCap1Id: record.nhomDichVuCap1Id,
                phanLoai: record.phanLoaiPtTt,
                ngay: moment().format("YYYY-MM-DD"),
              },
              true
            );
          }
          let loaiHinhThanhToanId =
            _loaiHinhThanhToanId ||
            (record?.dsLoaiHinhThanhToan?.sort((a, b) => b.uuTien - a.uuTien) ||
              [])[0]?.loaiHinhThanhToanId;

          if (state.loaiHinhThanhToanId) {
            loaiHinhThanhToanId = record?.dsLoaiHinhThanhToan.find(
              (x) => x.loaiHinhThanhToanId === state.loaiHinhThanhToanId
            )?.loaiHinhThanhToanId;
          }
          const doiTuong = refOption.current.doiTuong;
          let giaKhongBaoHiem = record?.giaKhongBaoHiem;
          let giaBaoHiem = record?.giaBaoHiem;
          let loaiHinhThanhToan;

          if (record?.dsLoaiHinhThanhToan?.length && loaiHinhThanhToanId) {
            loaiHinhThanhToan = record?.dsLoaiHinhThanhToan.find(
              (x) => x.loaiHinhThanhToanId === loaiHinhThanhToanId
            );
            if (loaiHinhThanhToan) {
              giaBaoHiem = loaiHinhThanhToan.giaBaoHiem;
              giaKhongBaoHiem = loaiHinhThanhToan.giaKhongBaoHiem;
            }
          }

          let donGia =
            doiTuong === DOI_TUONG.BAO_HIEM ? giaBaoHiem : giaKhongBaoHiem;
          if (loaiHinhThanhToan?.loaiGia) {
            donGia = getDonGiaByLoai(loaiHinhThanhToan);
          }

          //field _dsPhongThucHien giữ ds phòng thực hiện ban đầu, ko bị filter theo loại hình thanh toán
          record._dsPhongThucHien =
            record._dsPhongThucHien || record.dsPhongThucHien || [];

          let resPhongThucHien = (record._dsPhongThucHien || []).filter(
            (x) =>
              !x.loaiHinhThanhToanId ||
              !loaiHinhThanhToanId ||
              x.loaiHinhThanhToanId == loaiHinhThanhToanId
          );

          record.dsPhongThucHien = uniqBy(resPhongThucHien || [], "phongId");
          let slDichVu = null;
          if (
            _loaiHinhThanhToan?.maLoaiHinhThanhToan ==
            dataMA_LOAI_HINH_THANH_TOAN_HEN_THUC_HIEN_KHI_VUOT_DINH_MUC
          ) {
            slDichVu = await getSlDichVu({
              phongId: resPhongThucHien?.[0]?.phongId,
              tuThoiGian: moment().add(1, "days").format("YYYY-MM-DD"),
              denThoiGian: moment().add(1, "months").format("YYYY-MM-DD"),
              doiTuongKcb: refOption.current.doiTuongKcb,
            });
            slDichVu = (slDichVu?.data || []).find(
              (x) =>
                x.dinhMucNgoaiTru > x.soLuongNgoaiTru ||
                x.dinhMucNoiTru > x.soLuongNoiTru
            );
          }

          let phongThucHienId = null;
          if (record.boChiDinhId) {
            phongThucHienId = getPhongThucHienId(record);
          } else if (record?.dsPhongThucHien?.length === 1) {
            phongThucHienId = record.dsPhongThucHien[0]?.phongId;
          } else if (
            record?.loaiDichVu == LOAI_DICH_VU.PHAU_THUAT_THU_THUAT &&
            state.chiDinhTuLoaiDichVu == LOAI_DICH_VU.TO_DIEU_TRI
          ) {
            const _findIdx = record?.dsPhongThucHien.findIndex(
              (x) => x.phongId == phongChoMoId
            );
            if (_findIdx > -1) {
              phongThucHienId = record.dsPhongThucHien[_findIdx]?.phongId;
              isSetPhongChoMo = true; // đánh dấu để ko thực hiện cân bằng phòng
            }
          }

          let resNoiLayMau = [],
            resBenhPham = [];
          if (record?.loaiDichVu === LOAI_DICH_VU.XET_NGHIEM) {
            resNoiLayMau = await getListNoiLayMau({
              khoaChiDinhId: refOption.current.khoaChiDinhId,
              dsDoiTuongKcb: refOption.current.doiTuongKcb,
              dichVuId: record.dichVuId,
              nhomDichVuCap2Id: record.nhomDichVuCap2Id,
              nhomDichVuCap3Id: record.nhomDichVuCap3Id,
              dsLoaiDoiTuongId: refOption.current.loaiDoiTuongId,
              phongChiDinhId: refOption.current.phongChiDinhId,
              loaiHinhThanhToanId,
              nhaChiDinhId: refOption.current.dsNhaChiDinhId,
              thoiGianThucHien:
                refOption.current.thoiGianThucHien &&
                moment(refOption.current.thoiGianThucHien).format(),
            });
            if (record.boChiDinhId) {
              resBenhPham = await getAllBoChiDinhTheoLoai({
                boChiDinhId: record.boChiDinhId,
              });
              const result = extractBenhPham(resBenhPham, record.dichVuId);
              if (result) {
                record.dsBenhPhamId = result.dsBenhPhamId;
                record.dsBenhPham = result.dsBenhPham;
              }
            }
          }

          //mặc định chiDinhTuDichVuId
          if (state.showChiDinhTuDichVuId) {
            if (
              (refOption.current.chiDinhTuDichVuIdOptions?.data || [])
                .length === 1
            ) {
              record.chiDinhTuDichVuId =
                refOption.current.chiDinhTuDichVuIdOptions.data[0].id;
            }
          }

          updatedListDv = [
            {
              ...record,
              benhPhamId:
                record?.dsBenhPhamId?.length === 1
                  ? record?.dsBenhPhamId?.[0]
                  : null,
              soLuong: record?.soLuongMacDinh || record.soLuong || 1,
              loaiHinhThanhToanId: loaiHinhThanhToanId,
              donGia,
              phongThucHienId,
              dsPhuCapPtTtChiTiet: resPhuCap?.dsPhuCapPtTtChiTiet || [],
              dsNoiLayMau: resNoiLayMau || [],
              phongLayMauId:
                (resNoiLayMau || []).length === 1
                  ? resNoiLayMau[0].phongLayMauId
                  : null,
              ...(_loaiHinhThanhToan?.maLoaiHinhThanhToan ==
              dataMA_LOAI_HINH_THANH_TOAN_HEN_THUC_HIEN_KHI_VUOT_DINH_MUC
                ? {
                    thoiGianThucHien:
                      slDichVu?.ngayThucHien && moment(slDichVu?.ngayThucHien),
                  }
                : {}),
            },
            ...listSelectedDv,
          ];

          //check và hiện thị cảnh báo nếu dịch vụ đã tồn tại
          const _searchIndex = (listSelectedDv || []).findIndex(
            (x) => x.dichVuId == record.dichVuId
          );
          if (_searchIndex != -1) {
            message.error(`Dịch vụ ${record.ten} đã tồn tại!`);
          }
        } else {
          updatedListDv = listSelectedDv.filter(
            (item) => item.uniqueKey !== record.uniqueKey
          );
          if ((state.dsDichVuBoQuaCheckTrung || []).includes(record.dichVuId)) {
            setState({
              dsDichVuBoQuaCheckTrung: (
                state.dsDichVuBoQuaCheckTrung || []
              ).filter((x) => x !== record.dichVuId),
            });
          }
        }
        if (listDichVuDaKe.length > 0 && checked && !_loaiHinhThanhToanId) {
          //kiểm tra mở popup khi dịch vụ trùng
          let objDupplicate = listDichVuDaKe.find(
            (item1) => item1.dichVuId == record.dichVuId
          );
          if (objDupplicate) {
            showConfirm(
              {
                title: t("common.canhBao"),
                content:
                  isNoiTru && CANH_BAO_TRUNG_CHI_DINH_TRONG_NGAY?.eval()
                    ? t("khamBenh.chiDinh.canhBaoKeTrungTaiKhoa", {
                        tenDichVu: objDupplicate.tenDichVu,
                        tenNb: objDupplicate.tenNb,
                        tenKhoa: objDupplicate.tenKhoaChiDinh,
                      })
                    : t("khamBenh.chiDinh.canhBaoKeTrung")
                        .replace("{0}", objDupplicate.tenDichVu)
                        .replace("{1}", objDupplicate.tenNb),
                cancelText: t("common.huy"),
                okText: t("common.xacNhan"),
                showImg: false,
                showBtnOk: true,
                typeModal: "warning",
              },
              () => {
                let listDichVu = cloneDeep(updatedListDv);
                if (dataThucHienCanBang?.eval() && !isSetPhongChoMo) {
                  listDichVu = phanPhongDichVu(updatedListDv);
                }
                checkCanhBaoDinhMuc({
                  listDichVu,
                  data: record,
                  onSelectContinue,
                  checked,
                });
              }
            );
            return false;
          }
        }
        let listDichVu = cloneDeep(updatedListDv);
        if (dataThucHienCanBang?.eval() && !isSetPhongChoMo) {
          listDichVu = phanPhongDichVu(updatedListDv);
        }
        if (!_loaiHinhThanhToanId) {
          checkCanhBaoDinhMuc({
            listDichVu,
            data: record,
            onSelectContinue,
            checked,
          });
        } else {
          onTamTinhTien(listDichVu);
        }
      };

      if (checked && record?.canhBao) {
        showConfirm(
          {
            title: t("common.canhBao"),
            content: record.canhBao,
            cancelText: t("common.huy"),
            okText: t("common.xacNhan"),
            showImg: false,
            showBtnOk: true,
            typeModal: "warning",
          },
          async () => {
            await onSelectContinue(checked);
          }
        );
        return;
      }

      const listDvCanhBao = dataBAT_BUOC_NHAP_SO_THAI_TUAN_THAI_KHI_CHI_DINH
        ?.split(",")
        .map((i) => i.trim());

      if (
        checked &&
        isCkSan &&
        !chiTietKhamSan?.tuoiThai &&
        !chiTietKhamSan?.soLuongThai &&
        isArray(listDvCanhBao, true) &&
        listDvCanhBao.includes(record.ma)
      ) {
        showConfirm(
          {
            title: t("common.canhBao"),
            content: t(
              "khamBenh.chiDinh.chiDinhDvCanNhapDuLieuCacTruongSoThaiTuoiThai",
              {
                ma: record.ma,
                ten: record.ten,
              }
            ),
            cancelText: t("common.huy"),
            okText: t("common.xacNhan"),
            showImg: false,
            showBtnOk: true,
            typeModal: "warning",
          },
          async () => {
            await onSelectContinue(checked);
          }
        );
      } else {
        await onSelectContinue(checked);
      }
    } else {
      e.currentTarget.firstElementChild.firstElementChild.firstElementChild.firstElementChild.click();
    }
  };

  const checkDinhMucDichVuBo = ({ listDichVu, onSelectContinue }) => {
    const data = listDichVu?.filter((x) => {
      const phongThucHien =
        x?.dsPhongThucHien?.find((x1) => x1.phongId === x?.phongThucHienId) ||
        {};
      const { dinhMucNgoaiTru, soLuongNgoaiTru, soLuongNoiTru, dinhMucNoiTru } =
        phongThucHien;
      let soLuongDinhMucNgoaiTru = dinhMucNgoaiTru - (soLuongNgoaiTru || 0);
      let soLuongDinhMucNoiTru = dinhMucNoiTru - (soLuongNoiTru || 0);

      if (
        (dinhMucNgoaiTru &&
          soLuongDinhMucNgoaiTru <= 0 &&
          [
            DOI_TUONG_KCB.NGOAI_TRU,
            DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_LINH_THUOC,
            DOI_TUONG_KCB.NHAN_THUOC_THEO_HEN,
            DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_SU_DUNG_THUOC,
            DOI_TUONG_KCB.CAC_TRUONG_HOP_KHAC,
          ].includes(refOption.current.doiTuongKcb)) ||
        (dinhMucNoiTru &&
          soLuongDinhMucNoiTru <= 0 &&
          [
            DOI_TUONG_KCB.DIEU_TRI_NGOAI_TRU,
            DOI_TUONG_KCB.DIEU_TRI_NOI_TRU,
            DOI_TUONG_KCB.DIEU_TRI_NOI_TRU_BAN_NGAY,
            DOI_TUONG_KCB.DIEU_TRI_LUU_TRU_TAI_TRAM_Y_TE_TUYEN_XA,
            DOI_TUONG_KCB.DIEU_TRI_NOI_TRU_DUOI_BON_GIO,
          ].includes(refOption.current.doiTuongKcb))
      )
        return x;
    });
    if (data?.length) {
      const record = data[0];
      const dsLoaiHinhThanhToan = record?.dsLoaiHinhThanhToan?.filter((x) =>
        record?._dsPhongThucHien
          .filter(
            (item) =>
              ([
                DOI_TUONG_KCB.NGOAI_TRU,
                DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_LINH_THUOC,
                DOI_TUONG_KCB.NHAN_THUOC_THEO_HEN,
                DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_SU_DUNG_THUOC,
                DOI_TUONG_KCB.CAC_TRUONG_HOP_KHAC,
              ].includes(refOption.current.doiTuongKcb) &&
                item.dinhMucNgoaiTru > (item.soLuongNgoaiTru || 0)) ||
              ([
                DOI_TUONG_KCB.DIEU_TRI_NGOAI_TRU,
                DOI_TUONG_KCB.DIEU_TRI_NOI_TRU,
                DOI_TUONG_KCB.DIEU_TRI_NOI_TRU_BAN_NGAY,
                DOI_TUONG_KCB.DIEU_TRI_LUU_TRU_TAI_TRAM_Y_TE_TUYEN_XA,
                DOI_TUONG_KCB.DIEU_TRI_NOI_TRU_DUOI_BON_GIO,
              ].includes(refOption.current.doiTuongKcb) &&
                item.dinhMucNoiTru > (item.soLuongNoiTru || 0)) ||
              x.maLoaiHinhThanhToan ==
                dataMA_LOAI_HINH_THANH_TOAN_HEN_THUC_HIEN_KHI_VUOT_DINH_MUC
          )
          .map((x1) => x1.loaiHinhThanhToanId)
          ?.includes(x.loaiHinhThanhToanId)
      );
      refModalVuotDinhMuc.current &&
        refModalVuotDinhMuc.current.show(
          {
            dsLoaiHinhThanhToan: dsLoaiHinhThanhToan,
            loaiHinhThanhToanId: record?.loaiHinhThanhToanId,
          },
          (loaiHinhThanhToan) => {
            onSelectContinue({
              dsDichVuHetDinhMucId: data.map((item) => item.dichVuId),
              _loaiHinhThanhToan: loaiHinhThanhToan,
            });
            return true;
          }
        );
    } else {
      onTamTinhTien(listDichVu);
    }
  };

  const checkAllDichVu = (e) => {
    const checked = e?.target ? e?.target?.checked : e;
    let updatedListDv = [];
    let listDichVuBo = [];

    async function autoSelectAllDv({
      dsDichVuId = null,
      _loaiHinhThanhToan,
      dsDichVuHetDinhMucId,
    }) {
      const _loaiHinhThanhToanId = _loaiHinhThanhToan?.loaiHinhThanhToanId;
      if (checked) {
        const newListDichVu = await Promise.all(
          listDichVu
            .filter(
              (x1) =>
                listSelectedDv.findIndex((x2) => x1.dichVuId == x2.dichVuId) ==
                -1
            )
            .filter((x1) => !dsDichVuId || dsDichVuId.includes(x1.dichVuId))
            .map(async (x) => {
              //nếu có loại hình thanh toán được thiết lập sẵn thì chọn luôn giá trị đó
              let loaiHinhThanhToanId =
                x.loaiHinhThanhToanId &&
                x?.dsLoaiHinhThanhToan.findIndex(
                  (loaiHinhTT) =>
                    loaiHinhTT.loaiHinhThanhToanId === x.loaiHinhThanhToanId
                ) > -1
                  ? x.loaiHinhThanhToanId
                  : (x?.dsLoaiHinhThanhToan?.sort(
                      (a, b) => b.uuTien - a.uuTien
                    ) || [])[0]?.loaiHinhThanhToanId;

              if (
                _loaiHinhThanhToanId &&
                dsDichVuHetDinhMucId.includes(x.dichVuId) &&
                x?.dsLoaiHinhThanhToan.findIndex(
                  (loaiHinhTT) =>
                    loaiHinhTT.loaiHinhThanhToanId === _loaiHinhThanhToanId
                ) > -1
              ) {
                loaiHinhThanhToanId = _loaiHinhThanhToanId;
              }

              if (state.loaiHinhThanhToanId) {
                loaiHinhThanhToanId = x?.dsLoaiHinhThanhToan.find(
                  (x) => x.loaiHinhThanhToanId === state.loaiHinhThanhToanId
                )?.loaiHinhThanhToanId;
              }
              const doiTuong = refOption.current.doiTuong;
              let giaKhongBaoHiem = x.giaKhongBaoHiem;
              let giaBaoHiem = x.giaBaoHiem;
              let loaiHinhThanhToan;

              if (x?.dsLoaiHinhThanhToan?.length && loaiHinhThanhToanId) {
                loaiHinhThanhToan = x?.dsLoaiHinhThanhToan.find(
                  (item) => item.loaiHinhThanhToanId === loaiHinhThanhToanId
                );
                if (loaiHinhThanhToan) {
                  giaBaoHiem = loaiHinhThanhToan?.giaBaoHiem;
                  giaKhongBaoHiem = loaiHinhThanhToan?.giaKhongBaoHiem;
                }
              }

              x._dsPhongThucHien =
                x._dsPhongThucHien || x.dsPhongThucHien || [];
              let resPhongThucHien = (x._dsPhongThucHien || []).filter(
                (x) =>
                  !x.loaiHinhThanhToanId ||
                  !loaiHinhThanhToanId ||
                  x.loaiHinhThanhToanId == loaiHinhThanhToanId
              );

              x.dsPhongThucHien = uniqBy(resPhongThucHien || [], "phongId");

              let donGia =
                doiTuong === DOI_TUONG.BAO_HIEM ? giaBaoHiem : giaKhongBaoHiem;
              if (loaiHinhThanhToan?.loaiGia) {
                donGia = getDonGiaByLoai(loaiHinhThanhToan);
              }

              const benhPhamId =
                x?.dsBenhPhamId?.length === 1 ? x?.dsBenhPhamId?.[0] : null;

              let phongThucHienId = null;
              if (x.boChiDinhId) {
                phongThucHienId = getPhongThucHienId(x);
              } else if (x?.dsPhongThucHien?.length === 1) {
                phongThucHienId = x.dsPhongThucHien[0]?.phongId;
              } else if (
                x?.loaiDichVu == LOAI_DICH_VU.PHAU_THUAT_THU_THUAT &&
                state.chiDinhTuLoaiDichVu == LOAI_DICH_VU.TO_DIEU_TRI
              ) {
                const _findIdx = x?.dsPhongThucHien.findIndex(
                  (x) => x.phongId == phongChoMoId
                );
                if (_findIdx > -1) {
                  phongThucHienId = x.dsPhongThucHien[_findIdx]?.phongId;
                  x.isSetPhongChoMo = true; // đánh dấu để ko thực hiện cân bằng phòng
                }
              }

              let resNoiLayMau = [],
                resBenhPham;
              if (x?.loaiDichVu === LOAI_DICH_VU.XET_NGHIEM) {
                resNoiLayMau = await getListNoiLayMau({
                  khoaChiDinhId: refOption.current.khoaChiDinhId,
                  dsDoiTuongKcb: refOption.current.doiTuongKcb,
                  dichVuId: x.dichVuId,
                  nhomDichVuCap2Id: x.nhomDichVuCap2Id,
                  nhomDichVuCap3Id: x.nhomDichVuCap3Id,
                  dsLoaiDoiTuongId: refOption.current.loaiDoiTuongId,
                  loaiHinhThanhToanId: loaiHinhThanhToanId,
                  phongChiDinhId: refOption.current.phongChiDinhId,
                  nhaChiDinhId: refOption.current.dsNhaChiDinhId,
                  thoiGianThucHien:
                    refOption.current.thoiGianThucHien &&
                    moment(refOption.current.thoiGianThucHien).format(),
                });
                if (x.boChiDinhId) {
                  resBenhPham = await getAllBoChiDinhTheoLoai({
                    boChiDinhId: x.boChiDinhId,
                  });
                  const result = extractBenhPham(resBenhPham, x.dichVuId);
                  if (result) {
                    x.dsBenhPhamId = result.dsBenhPhamId;
                    x.dsBenhPham = result.dsBenhPham;
                  }
                }
              }
              let slDichVu = null;
              if (
                _loaiHinhThanhToan?.maLoaiHinhThanhToan ==
                  dataMA_LOAI_HINH_THANH_TOAN_HEN_THUC_HIEN_KHI_VUOT_DINH_MUC &&
                resPhongThucHien?.[0]?.phongId &&
                dsDichVuHetDinhMucId.includes(x.dichVuId)
              ) {
                slDichVu = await getSlDichVu({
                  phongId: resPhongThucHien?.[0]?.phongId,
                  tuThoiGian: moment().add(1, "days").format("YYYY-MM-DD"),
                  denThoiGian: moment().add(1, "months").format("YYYY-MM-DD"),
                  doiTuongKcb: refOption.current.doiTuongKcb,
                });
                slDichVu = (slDichVu?.data || []).find(
                  (x) =>
                    x.dinhMucNgoaiTru > x.soLuongNgoaiTru ||
                    x.dinhMucNoiTru > x.soLuongNoiTru
                );
              }
              return {
                ...x,
                soLuong: x.soLuong || 1,
                phongThucHienId,
                donGia,
                loaiHinhThanhToanId,
                benhPhamId,
                dsNoiLayMau: resNoiLayMau || [],
                phongLayMauId:
                  (resNoiLayMau || []).length === 1
                    ? resNoiLayMau[0].phongLayMauId
                    : null,
                ...(_loaiHinhThanhToan?.maLoaiHinhThanhToan ==
                  dataMA_LOAI_HINH_THANH_TOAN_HEN_THUC_HIEN_KHI_VUOT_DINH_MUC &&
                resPhongThucHien?.[0]?.phongId &&
                dsDichVuHetDinhMucId.includes(x.dichVuId)
                  ? {
                      thoiGianThucHien:
                        slDichVu?.ngayThucHien &&
                        moment(slDichVu?.ngayThucHien),
                    }
                  : { thoiGianThucHien: moment() }),
              };
            })
        );

        updatedListDv = [...newListDichVu, ...listSelectedDv];
        listDichVuBo = updatedListDv;

        if (dataThucHienCanBang?.eval()) {
          listDichVuBo = phanPhongDichVu(updatedListDv);
        }
      } else {
        updatedListDv = listSelectedDv.filter(
          (x1) => listDichVu.findIndex((x2) => x1.dichVuId == x2.dichVuId) == -1
        );
        listDichVuBo = updatedListDv;
        if (dataThucHienCanBang?.eval()) {
          listDichVuBo = phanPhongDichVu(updatedListDv);
        }
      }
      if (!_loaiHinhThanhToanId) {
        checkDinhMucDichVuBo({
          listDichVu: listDichVuBo,
          onSelectContinue: autoSelectAllDv,
        });
      }
      if (_loaiHinhThanhToanId) {
        onTamTinhTien(listDichVuBo);
      }
    }

    if (listDichVuDaKe.length > 0 && checked) {
      //kiểm tra mở popup khi dịch vụ trùng
      let objDupplicate = uniqBy(
        listDichVuDaKe.filter(
          (x1) => listDichVu.findIndex((x2) => x2.dichVuId === x1.dichVuId) > -1
        ),
        "dichVuId"
      );
      const dsDVKhongTrung = uniqBy(
        listDichVu.filter(
          (x1) =>
            listDichVuDaKe.findIndex((x2) => x2.dichVuId === x1.dichVuId) == -1
        ),
        "dichVuId"
      ).map((item) => item.dichVuId);

      if (objDupplicate && objDupplicate.length > 0) {
        refModalCanhBaoKeTrung.current &&
          refModalCanhBaoKeTrung.current.show(
            {
              content:
                isNoiTru && CANH_BAO_TRUNG_CHI_DINH_TRONG_NGAY?.eval()
                  ? t("khamBenh.chiDinh.canhBaoKeTrungTaiKhoa", {
                      tenDichVu: objDupplicate
                        .map((x) => x.tenDichVu)
                        .join(", "),
                      tenNb: objDupplicate[0].tenNb,
                      tenKhoa: objDupplicate[0].tenKhoaChiDinh,
                    })
                  : t("khamBenh.chiDinh.canhBaoKeTrung")
                      .replace(
                        "{0}",
                        `${objDupplicate.map((x) => x.tenDichVu).join(", ")}`
                      )
                      .replace("{1}", objDupplicate[0].tenNb),
              showKeDVKhongTrung: dsDVKhongTrung.length > 0,
            },
            () => {
              autoSelectAllDv({});
            },
            () => {
              autoSelectAllDv({ dsDichVuId: dsDVKhongTrung });
            }
          );
      } else {
        autoSelectAllDv({});
      }
    } else {
      autoSelectAllDv({});
    }
  };

  useEffect(() => {
    if (state.loaiDichVu !== 150) {
      setState({ activeLink: -1 });
    }
  }, [state.loaiDichVu]);

  const unCheckAll = () => {
    setState({
      listSelectedDv: [],
    });
    onTamTinhTien([]);
  };

  const onRemoveItem = (value, record) => () => {
    const listUpdatedTag = listSelectedDv.filter(
      (item) => item.uniqueKey !== value
    );

    let listDichVu = listUpdatedTag;
    if (dataThucHienCanBang?.eval()) {
      listDichVu = phanPhongDichVu(listUpdatedTag);
    }
    setState({
      listSelectedDv: listDichVu,
      dsDichVuBoQuaCheckTrung: (state.dsDichVuBoQuaCheckTrung || []).filter(
        (x) => x !== record.dichVuId
      ),
    });
    onTamTinhTien(listDichVu);
  };

  const onSelectTuVanVien = (e) => {
    setState({
      tuVanVienId: e,
    });
    onTamTinhTien(listSelectedDv.map((item) => ({ ...item, tuVanVienId: e })));
  };

  const onSelectNhomDichVu = (e) => {
    onSearchDichVu2({
      page,
      size,
      keyword,
      loaiDichVu: loaiDichVu == LOAI_DICH_VU.BO_CHI_DINH ? null : loaiDichVu,
      dsNhomDichVuCap2Id: e,
      boChiDinhId: state?.boChiDinhSelectedId,
    });

    setState({ dsNhomDichVuCap2Id: e });
  };

  const onSelectLoaiHinhThanhToan = (e) => {
    const doiTuong = refOption.current.doiTuong;

    const payload = listSelectedDv.map((item) => {
      let giaKhongBaoHiem = item.giaKhongBaoHiem;
      let giaBaoHiem = item.giaBaoHiem;

      const loaiHinhThanhToan = item?.dsLoaiHinhThanhToan.find(
        (x) => x.loaiHinhThanhToanId === e
      );

      if (loaiHinhThanhToan && e) {
        giaKhongBaoHiem = loaiHinhThanhToan.giaKhongBaoHiem;
        giaBaoHiem = loaiHinhThanhToan.giaBaoHiem;
      }

      let donGia =
        doiTuong === DOI_TUONG.BAO_HIEM ? giaBaoHiem : giaKhongBaoHiem;

      if (loaiHinhThanhToan?.loaiGia) {
        donGia = getDonGiaByLoai(loaiHinhThanhToan);
      }

      return {
        ...item,
        nbDichVu: {
          ...item.nbDichVu,
        },
        loaiHinhThanhToanId: loaiHinhThanhToan?.loaiHinhThanhToanId,
        donGia,
      };
    });
    setState({ listSelectedDv: payload, loaiHinhThanhToanId: e });
    onTamTinhTien(payload);
  };

  const columnsDichVu = [
    {
      dataIndex: "ma",
      key: "ma",
      width: 120,
      show: true,
      i18Name: "common.maDv",
      render: (value, currentRow, index) => {
        return (
          <div className="row-item">
            <div className="left-box">
              <Checkbox
                checked={
                  !!listSelectedDv.find(
                    (item) => item.uniqueKey === currentRow.uniqueKey
                  )
                }
              />
            </div>
            <div className="right-box">
              <div className="name">
                <b>{currentRow?.ma}</b>
              </div>
            </div>
          </div>
        );
      },
    },
    {
      title: (
        <HeaderSearch
          isTitleCenter={!(state.loaiDichVu == LOAI_DICH_VU.BO_CHI_DINH)}
          title={
            state.loaiDichVu == LOAI_DICH_VU.BO_CHI_DINH ? (
              <div style={{ marginLeft: 6, width: "100%", textAlign: "left" }}>
                {listDichVu && listDichVu.length > 0 && (
                  <Checkbox
                    onChange={checkAllDichVu}
                    checked={listDichVu.every(
                      (x1) =>
                        listSelectedDv.findIndex(
                          (x2) => x1.dichVuId == x2.dichVuId
                        ) != -1
                    )}
                  >
                    {t("common.chonTatCa")}
                  </Checkbox>
                )}
              </div>
            ) : (
              t("khamBenh.donThuoc.tenThuocHamLuong")
            )
          }
        />
      ),
      dataIndex: "tenDichVu",
      key: "tenDichVu",
      width: 200,
      show: true,
      i18Name: "common.tenDichVu",
      render: (value, currentRow, index) => {
        return (
          <div className="row-item">
            <div className="right-box">
              <div className="name">
                <b>{currentRow?.ten}</b>
              </div>
            </div>
          </div>
        );
      },
    },
    {
      title: (
        <HeaderSearch
          isTitleCenter={!(state.loaiDichVu == LOAI_DICH_VU.BO_CHI_DINH)}
        />
      ),
      dataIndex: "donGia",
      key: "donGia",
      width: 100,
      show: true,
      i18Name: "common.donGia",
      render: (value, currentRow, index) => {
        const giaKhongBaoHiem = (currentRow.giaKhongBaoHiem || 0).formatPrice();
        const giaBaoHiem = (currentRow.giaBaoHiem || 0).formatPrice();
        const giaPhuThu = (currentRow.giaPhuThu || 0).formatPrice();
        const donGia = `${giaKhongBaoHiem} | ${t(
          "khamBenh.chiDinh.BH"
        )}: ${giaBaoHiem} | ${t("khamBenh.chiDinh.phuThu")}: ${giaPhuThu}`;
        return (
          <div className="row-item">
            <div className="right-box">
              <div className="desc">{donGia}</div>
            </div>
          </div>
        );
      },
    },
  ];

  const columnsGoiDichVu = [
    {
      title: (
        <HeaderSearch
          isTitleCenter={true}
          title={t("khamBenh.donThuoc.tenThuocHamLuong")}
        />
      ),
      dataIndex: "",
      key: "",
      width: "100%",
      render: (value, currentRow, index) => {
        return (
          <div>
            {currentRow?.ten} <RightOutlined className="arrow-icon" />
          </div>
        );
      },
    },
  ];

  const columnsChooseDv = {
    ma: {
      title: <HeaderSearch isTitleCenter={true} title={t("common.maDv")} />,
      dataIndex: "ma",
      key: "ma",
      width: 120,
      show: true,
      i18Name: "common.maDv",
    },
    ten: {
      title: (
        <HeaderSearch isTitleCenter={true} title={t("common.tenDichVu")} />
      ),
      dataIndex: "ten",
      key: "ten",
      width: 240,
      show: true,
      i18Name: "common.tenDichVu",
    },
    tyLeTt: {
      title: <HeaderSearch title={t("common.tyLeTt")} isTitleCenter={true} />,
      dataIndex: "tyLeTtDv",
      key: "tyLeTtDv",
      width: 80,
      show: true,
      i18Name: "common.tyLeTt",
      hidden: state.isHiddenTyLett,
      render: (item, list, index) => {
        const tyLeTtDv = item || 100;
        return (
          <div className="soluong" onClick={(event) => event.stopPropagation()}>
            <Select
              value={tyLeTtDv}
              data={TY_LE_THANH_TOAN}
              onChange={onChangeSoLuong(list.uniqueKey, "tyLeTtDv")}
              disabled={
                !state.isPhauThuat ||
                list.loaiDichVu !== LOAI_DICH_VU.PHAU_THUAT_THU_THUAT
              }
            />
          </div>
        );
      },
    },
    giaBhyt: {
      title: <HeaderSearch title={t("common.giaBhyt")} isTitleCenter={true} />,
      dataIndex: "giaBaoHiem",
      key: "giaBaoHiem",
      width: 80,
      show: true,
      align: "center",
      i18Name: "common.giaBhyt",
      hidden: refOption.current.doiTuong !== DOI_TUONG.BAO_HIEM,
      render: (item, list, index) => {
        return <Checkbox checked={item != 0} />;
      },
    },
    tuTra: {
      title: <HeaderSearch title={t("common.tuTra")} isTitleCenter={true} />,
      dataIndex: "tuTra",
      key: "tuTra",
      width: 80,
      show: true,
      align: "center",
      i18Name: "common.tuTra",
      hidden:
        !dataCHO_TICK_TU_TRA_NB_KHONG_BHYT.eval() &&
        refOption.current.doiTuong !== DOI_TUONG.BAO_HIEM,
      render: (item, list, index) => {
        return (
          <Checkbox
            checked={item}
            onChange={onChangeSoLuong(list.uniqueKey, "tuTra")}
          />
        );
      },
    },
    capCuu: {
      title: (
        <HeaderSearch
          title={t("khamBenh.chiDinh.capCuu")}
          isTitleCenter={true}
        />
      ),
      dataIndex: "capCuu",
      key: "capCuu",
      width: 80,
      show: true,
      align: "center",
      i18Name: "khamBenh.chiDinh.capCuu",
      render: (item, list, index) => {
        return (
          <Checkbox
            checked={item}
            onChange={onChangeSoLuong(list.uniqueKey, "capCuu")}
          />
        );
      },
    },
    uuTien: {
      title: <HeaderSearch title={t("common.uuTien")} isTitleCenter={true} />,
      dataIndex: "uuTien",
      key: "uuTien",
      width: 80,
      show: true,
      align: "center",
      i18Name: "common.uuTien",
      show:
        loaiDichVu === LOAI_DICH_VU.XET_NGHIEM ||
        loaiDichVu === LOAI_DICH_VU.CDHA ||
        !loaiDichVu,
      render: (item, list, index) => {
        return (
          <Checkbox
            checked={item}
            onChange={onChangeSoLuong(list.uniqueKey, "uuTien")}
          />
        );
      },
    },
    benhPham: {
      title: (
        <HeaderSearch title={t("xetNghiem.benhPham")} isTitleCenter={true} />
      ),
      dataIndex: "benhPhamId",
      key: "benhPhamId",
      width: 200,
      show: loaiDichVu === LOAI_DICH_VU.XET_NGHIEM || !loaiDichVu,
      align: "center",
      i18Name: "xetNghiem.benhPham",
      render: (item, list, index) => {
        const modifyList = !!list?.dsBenhPhamId?.length
          ? listAllBenhPham?.filter((i) => list?.dsBenhPhamId?.includes(i.id))
          : listAllBenhPham;
        return (
          <Select
            value={item}
            // mode="multiple"
            onSearch={(e) => {
              setState({ searchBenhPhamIds: { [list.id]: !!e } });
            }}
            defaultValue={
              list?.dsBenhPhamId?.length === 1 ? list?.dsBenhPhamId?.[0] : null
            }
            data={
              state?.searchBenhPhamIds?.[list.id] ? listAllBenhPham : modifyList
            }
            placeholder={t("khamBenh.ketQua.chonBenhPham")}
            onChange={onChangeSoLuong(list.uniqueKey, "benhPhamId")}
            dropdownMatchSelectWidth={300}
          />
        );
      },
    },
    phanTangNguyCoId: {
      title: (
        <HeaderSearch
          title={t("khamBenh.chiDinh.phanTangNguyCo")}
          isTitleCenter={true}
        />
      ),
      dataIndex: "phanTangNguyCoId",
      key: "phanTangNguyCoId",
      width: 200,
      show: true,
      align: "center",
      i18Name: "khamBenh.chiDinh.phanTangNguyCo",
      render: (item, list, index) => {
        return (
          <div onClick={(event) => event.stopPropagation()}>
            <Select
              value={item}
              data={listAllPhanTangNguyCo}
              onChange={onChangeSoLuong(list.uniqueKey, "phanTangNguyCoId")}
              dropdownMatchSelectWidth={250}
            />
          </div>
        );
      },
    },
    nguoiThucHienId: {
      title: (
        <HeaderSearch
          title={t("khamBenh.chiDinh.ptvcTtvcNguoiTH")}
          isTitleCenter={true}
        />
      ),
      dataIndex: "nguoiThucHienId",
      key: "nguoiThucHienId",
      width: 200,
      show: false,
      align: "center",
      i18Name: "khamBenh.chiDinh.ptvcTtvcNguoiTH",
      render: (item, list, index) => {
        return (
          <div onClick={(event) => event.stopPropagation()}>
            <Select
              value={item}
              data={listAllNhanVienMemo}
              onChange={onChangeSoLuong(list.uniqueKey, "nguoiThucHienId")}
              dropdownMatchSelectWidth={250}
            />
          </div>
        );
      },
    },
    soLuong: {
      title: <HeaderSearch isTitleCenter={true} title={t("common.soLuong")} />,
      dataIndex: "soLuong",
      key: "soLuong",
      width: 80,
      show: true,
      i18Name: "common.soLuong",
      render: (item, list, index) => {
        const soLuong = item || 1;

        return (
          <div className="soluong" onClick={(event) => event.stopPropagation()}>
            <InputTimeout
              type="number"
              value={soLuong}
              style={{ width: 65 }}
              min={0.01}
              step={1}
              onChange={onChangeSoLuong(list.uniqueKey, "soLuong")}
              parser={(value) => value.replaceAll(",", ".")}
              formatter={(value) => value.replaceAll(".", ",")}
            />
          </div>
        );
      },
    },
    loaiHinhThanhToan: {
      title: (
        <HeaderSearch
          title={t("khamBenh.chiDinh.loaiHinhThanhToan")}
          isTitleCenter={true}
        />
      ),
      dataIndex: "loaiHinhThanhToanId",
      key: "loaiHinhThanhToanId",
      width: 180,
      show: true,
      i18Name: "khamBenh.chiDinh.loaiHinhThanhToan",
      render: (item, list, idx) => {
        const dataSource = (list.dsLoaiHinhThanhToan || [])
          .map((item) => ({
            id: item.loaiHinhThanhToanId,
            ten: item.tenLoaiHinhThanhToan,
            uuTien: item.uuTien,
            giaBaoHiem: item.giaBaoHiem,
            giaKhongBaoHiem: item.giaKhongBaoHiem,
            loaiGia: item.loaiGia,
            giaPhuThu: item.giaPhuThu,
          }))
          .sort((a, b) => b.uuTien - a.uuTien);
        return (
          <div className="soluong" onClick={(event) => event.stopPropagation()}>
            <Select
              value={list?.loaiHinhThanhToanId}
              data={dataSource}
              onChange={onChangeSoLuong(list.uniqueKey, "loaiHinhThanhToanId")}
              style={{ margin: "0px" }}
              dropdownMatchSelectWidth={300}
            />
          </div>
        );
      },
    },
    donGia: {
      title: <HeaderSearch isTitleCenter={true} title={t("common.donGia")} />,
      dataIndex: "donGia",
      key: "donGia",
      width: 100,
      align: "right",
      show: true,
      i18Name: "common.donGia",
      render: (item) => item && item.formatPrice(),
    },
    phongThucHien: {
      title: (
        <HeaderSearch
          title={t("khamBenh.chiDinh.phongThucHien")}
          isTitleCenter={true}
        />
      ),
      dataIndex: "phongThucHienId",
      key: "phongThucHienId",
      width: 200,
      show: true,
      i18Name: "khamBenh.chiDinh.phongThucHien",
      render: (item, list, idx) => {
        const dataSource = (list?.dsPhongThucHien || []).map((item) => ({
          id: item.phongId,
          ten: item.ten,
          nbChuaHoanThanh: item.nbChuaHoanThanh,
        }));
        return (
          <div className="soluong" onClick={(event) => event.stopPropagation()}>
            <Select
              value={list?.phongThucHienId}
              data={dataSource}
              onChange={onChangeSoLuong(list.uniqueKey, "phongThucHienId")}
              style={{ margin: "0px" }}
              dropdownMatchSelectWidth={300}
            />
          </div>
        );
      },
    },
    phongLayMau: {
      title: (
        <HeaderSearch
          title={t("khamBenh.chiDinh.noiLayMau")}
          isTitleCenter={true}
        />
      ),
      dataIndex: "phongLayMauId",
      key: "phongLayMauId",
      width: 200,
      show: true,
      i18Name: "khamBenh.chiDinh.noiLayMau",
      render: (item, list, idx) => {
        //chỉ hiển thị với loại dịch vụ xét nghiệm
        if (list?.loaiDichVu !== LOAI_DICH_VU.XET_NGHIEM) {
          return null;
        }
        const dataSource = (list?.dsNoiLayMau || []).map((item) => ({
          id: item.phongLayMauId,
          ten: `${item.phongLayMau?.ten || ""} (${
            item.phongLayMau?.diaDiem || ""
          })`,
        }));
        return (
          <div className="soluong" onClick={(event) => event.stopPropagation()}>
            <Select
              value={list?.phongLayMauId}
              data={dataSource}
              onChange={onChangeSoLuong(list.uniqueKey, "phongLayMauId")}
              style={{ margin: "0px" }}
              dropdownMatchSelectWidth={300}
            />
          </div>
        );
      },
    },
    ghiChu: {
      title: <HeaderSearch isTitleCenter={true} title={t("common.moTaLuuy")} />,
      dataIndex: "ghiChu",
      key: "ghiChu",
      width: 150,
      show: true,
      i18Name: "common.moTaLuuy",
      render: (item, list, index) => {
        return (
          <InputTimeout
            value={item}
            onChange={onChangeSoLuong(list.uniqueKey, "ghiChu")}
          />
        );
      },
    },
    phanLoaiPtTt: {
      title: (
        <HeaderSearch isTitleCenter={true} title={t("baoCao.phanLoaiPttt")} />
      ),
      dataIndex: "phanLoaiPtTt",
      key: "phanLoaiPtTt",
      width: 150,
      show: true,
      i18Name: "baoCao.phanLoaiPttt",
      render: (item, list, index) => {
        return (
          <Select
            value={item}
            data={listPhanLoaiPTTT}
            onChange={onChangeSoLuong(list.uniqueKey, "phanLoaiPtTt")}
          />
        );
      },
    },
    ...KEYS_VI_TRI_CHAM_CONG.reduce((accumulator, currentValue) => {
      accumulator[currentValue] = {
        title: (
          <HeaderSearch
            title={t(renderTitleVTCC(currentValue))}
            isTitleCenter={true}
          />
        ),
        dataIndex: currentValue,
        key: currentValue,
        width: 200,
        show: true,
        align: "center",
        i18Name: renderTitleVTCC(currentValue),
        render: (item, list, index) => {
          if (
            ![
              LOAI_DICH_VU.PHAU_THUAT_THU_THUAT,
              LOAI_DICH_VU.CDHA,
              LOAI_DICH_VU.XET_NGHIEM,
            ].includes(list?.loaiDichVu) ||
            !checkTonTaiThietLap(
              currentValue,
              list?.dsPhuCapPtTtChiTiet,
              list?.loaiDichVu
            )
          )
            return null;
          return (
            <div onClick={(event) => event.stopPropagation()}>
              <Select
                value={item}
                data={renderDataListVTCC(currentValue)}
                onChange={onChangeSoLuong(list.uniqueKey, currentValue)}
                dropdownMatchSelectWidth={250}
              />
            </div>
          );
        },
      };
      return accumulator;
    }, {}),
    nguonKhacId: {
      title: (
        <HeaderSearch title={t("danhMuc.nguonKhac")} isTitleCenter={true} />
      ),
      dataIndex: "nguonKhacId",
      key: "nguonKhacId",
      width: 200,
      show: false,
      align: "center",
      i18Name: "danhMuc.nguonKhac",
      render: (item, list, index) => {
        return (
          <div onClick={(event) => event.stopPropagation()}>
            <Select
              value={item}
              data={listAllNguonKhacChiTra}
              onChange={onChangeSoLuong(list.uniqueKey, "nguonKhacId")}
            />
          </div>
        );
      },
    },
  };

  const columnsChooseDvtMemo =
    dataThietLap?.reduce(
      (a, c) =>
        columnsChooseDv[c]
          ? [
              ...a,
              {
                ...columnsChooseDv[c],
                i18Name: undefined,
                columnName: DATA_TEN_HIEN_THI[c],
                title: (
                  <HeaderSearch
                    isTitleCenter={true}
                    title={DATA_TEN_HIEN_THI[c]}
                  />
                ),
              },
            ]
          : [...a],
      [
        {
          title: (
            <HeaderSearch
              isTitleCenter={true}
              title={<Checkbox checked onChange={unCheckAll} />}
            />
          ),
          dataIndex: "index",
          key: "key",
          width: 40,
          align: "center",
          render: (item, list, index) => (
            <Checkbox checked onChange={onRemoveItem(list.uniqueKey, list)} />
          ),
        },
        {
          title: <HeaderSearch title={t("common.stt")} />,
          width: 40,
          dataIndex: "index",
          key: "index",
          align: "center",
        },
        ...(state.showChiDinhTuDichVuId
          ? [
              {
                title: (
                  <HeaderSearch
                    title={
                      refOption.current?.chiDinhTuDichVuIdOptions?.title || ""
                    }
                  />
                ),
                width: 160,
                dataIndex: "chiDinhTuDichVuId",
                key: "chiDinhTuDichVuId",
                render: (item, list, index) => {
                  return (
                    <div onClick={(event) => event.stopPropagation()}>
                      <Select
                        value={item}
                        data={
                          refOption.current?.chiDinhTuDichVuIdOptions?.data ||
                          []
                        }
                        onChange={onChangeSoLuong(
                          list.uniqueKey,
                          "chiDinhTuDichVuId"
                        )}
                        dropdownMatchSelectWidth={250}
                      />
                    </div>
                  );
                },
              },
            ]
          : []),
      ]
    ) || Object.values(columnsChooseDv || {});

  const renderEmptyTextLeftTable = () => {
    return (
      <div style={{ marginTop: 130 }}>
        <div style={{ color: "#c3c3c3", fontSize: 14 }}>
          {t("common.khongCoDuLieuPhuHop")}
        </div>
      </div>
    );
  };

  const onChangePage = (page, newSize = null) => {
    onSearchDichVu2({
      page: page - 1,
      size: newSize ? newSize : size,
      keyword,
      loaiDichVu: loaiDichVu == LOAI_DICH_VU.BO_CHI_DINH ? null : loaiDichVu,
      dsNhomDichVuCap2Id: state.dsNhomDichVuCap2Id,
      boChiDinhId: state?.boChiDinhSelectedId,
    });
  };

  const onSizeChange = (value) => {
    onSearchDichVu2({
      page: 0,
      size: value,
      keyword: keyword,
      loaiDichVu: loaiDichVu == LOAI_DICH_VU.BO_CHI_DINH ? null : loaiDichVu,
      dsNhomDichVuCap2Id: state.dsNhomDichVuCap2Id,
      boChiDinhId: state?.boChiDinhSelectedId,
    });
  };

  const onSearchDichVu2 = ({
    keyword,
    page = 0,
    size = dataPageSize || 10,
    loaiDichVu,
    boChiDinhId,
    dsNhomDichVuCap2Id = null,
  }) => {
    //nếu không phải là loại dịch vụ thì ko search (case loaiDichVu = id gói dv)
    if (![...Object.values(LOAI_DICH_VU), null, ""].includes(loaiDichVu))
      return;

    onSearchDichVu({
      timKiem: keyword,
      page: page || 0,
      size: size || 10,
      khoaChiDinhId: refOption.current.khoaChiDinhId,
      bacSiChiDinhId: nhanVienId,
      // boChiDinhId: state.boChiDinhSelected?.id,
      boChiDinhId: boChiDinhId,
      ...(loaiDichVu
        ? { loaiDichVu: loaiDichVu }
        : {
            dsLoaiDichVu: refOption.current.dsLoaiDichVu,
          }),
      dsDoiTuongSuDung: refOption.current.dsDoiTuongSuDung,
      doiTuongKcb: refOption.current.doiTuongKcb,
      nbDotDieuTriId: refOption.current.nbDotDieuTriId,
      dsNhomDichVuCap2Id,
      loaiDoiTuongId: refOption.current.loaiDoiTuongId,
      thoiGianVaoVien:
        refOption.current.thoiGianVaoVien &&
        moment(refOption.current.thoiGianVaoVien).format("YYYY-MM-DD"),
      ngaySinh:
        refOption.current.ngaySinh &&
        moment(refOption.current.ngaySinh).format("YYYY-MM-DD"),
      thoiGianThucHien:
        refOption.current.thoiGianThucHien &&
        moment(refOption.current.thoiGianThucHien).format(),
      ...(!boChiDinhId
        ? {
            sort:
              loaiDichVu && !keyword
                ? combineSort({ stt: 1, ten: 1 })
                : !keyword
                ? combineSort({ ten: 1 })
                : null,
          }
        : null),
      dsNhaChiDinhId: refOption.current.dsNhaChiDinhId,
      ...(isKhamBenh
        ? {
            chiDinhTuLoaiDichVu: LOAI_DICH_VU.KHAM,
            phongChiDinhId: refOption.current.phongChiDinhId,
          }
        : {}),
    });
  };

  const onEnterAddService = () => {
    let data = listDichVu.find((x) => (x.id || x.dichVuId) === state?.key);
    let isSelected = true;

    if (listSelectedDv.find((item) => item.uniqueKey === data.uniqueKey)) {
      isSelected = false;
    }
    onSelectDichVu(data, true, isSelected)();
  };

  refAddService.current = onEnterAddService;
  refSubmit.current = onSubmit;

  const onCallBackOk = (data) => {
    let columnLoaiHinhThanhToan = data.filter(
      (s) => s.i18Name === "khamBenh.chiDinh.loaiHinhThanhToan" && s?.show
    );
    if (columnLoaiHinhThanhToan.length) {
      setState({ showLoaiHinhThanhToan: true });
    } else {
      setState({ showLoaiHinhThanhToan: false });
    }
  };

  const handleResizeSplit = async () => {
    const widthSplitLeft =
      refSplitPanelLeft?.current?.getBoundingClientRect()?.width;
    const widthSplitRight =
      refSplitPanelRight?.current?.getBoundingClientRect()?.width;

    const key =
      state.loaiDichVu !== LOAI_DICH_VU.BO_CHI_DINH
        ? "widthCacheDichVu"
        : "widthCacheDichVuBoChiDinh";
    const keyCache =
      state.loaiDichVu !== LOAI_DICH_VU.BO_CHI_DINH
        ? "DATA_CUSTOMIZE_COLUMN_split_KHAMBENH_KeDichVu_DichVu"
        : "DATA_CUSTOMIZE_COLUMN_split_KHAMBENH_KeDichVu_DichVuBoChiDinh";

    onResizeSplit(key, keyCache, [widthSplitLeft, widthSplitRight]);
  };

  const onChangeBoChiDinh = (value, data) => {
    setState({ boChiDinhSelectedId: data?.dichVuId });
    onSearchDichVu2({
      page: 0,
      size: 50,
      loaiDichVu: state.loaiDichVu,
      boChiDinhId: data?.dichVuId,
    });
  };
  return (
    <ModalTemplate
      ref={refModal}
      width={1447}
      layerId={layerId}
      title={t("khamBenh.chiDinh.chiDinhDichVuKyThuat")}
      onCancel={onCancel}
      rightTitle={nbInfoTitle}
      actionRight={
        <>
          <Button
            minWidth={100}
            type="default"
            onClick={onCancel}
            iconHeight={15}
          >
            {t("common.huy")}
          </Button>
          <Button minWidth={100} type="primary" onClick={onSubmit}>
            {t("common.dongY")}
          </Button>
        </>
      }
      maskClosable={false}
    >
      <Main loaiDichVu={state.loaiDichVu}>
        <div className="filter-box">
          <Select
            defaultValue=""
            value={state.loaiDichVu}
            className="filter-item"
            data={state.listLoaiChiDinhDV}
            placeholder={t("khamBenh.chiDinh.chonLoaiDV")}
            onChange={onSelectServiceType}
          ></Select>
          {state.showAllFilter &&
            state.loaiDichVu != "" &&
            state.loaiDichVu != LOAI_DICH_VU.BO_CHI_DINH && (
              <Select
                value={state.dsNhomDichVuCap2Id}
                mode="multiple"
                className="filter-item"
                data={listNhomDvCap2}
                placeholder={t("khamBenh.chiDinh.chonNhomDichVu")}
                onChange={onSelectNhomDichVu}
                maxTagCount="responsive"
              ></Select>
            )}
          <InputTimeout
            className="filter-item"
            ref={refInput}
            placeholder={t("khamBenh.chiDinh.chonDichVu")}
            onChange={onSearch}
            value={state.keyword}
          />
          {state.showAllFilter && state.showLoaiHinhThanhToan && (
            <Select
              value={state.loaiHinhThanhToanId}
              className="filter-item"
              data={listAllLoaiHinhThanhToan}
              placeholder={t("khamBenh.chiDinh.chonLoaiHinhThanhToan")}
              onChange={onSelectLoaiHinhThanhToan}
            ></Select>
          )}
          {state.showAllFilter && (
            <Select
              value={state.tuVanVienId}
              className="filter-item"
              data={listAllNhanVienMemo}
              placeholder={t("khamBenh.chiDinh.chonTuVanVien")}
              onChange={onSelectTuVanVien}
            ></Select>
          )}
          {state.showAllFilter ? (
            <SVG.IcCollapse
              onClick={() => setState({ showAllFilter: false })}
            />
          ) : (
            <SVG.IcExtend onClick={() => setState({ showAllFilter: true })} />
          )}
        </div>
        <div className="list-services">
          {state.loaiDichVu == LOAI_DICH_VU.BO_CHI_DINH && (
            <div className="content-equal-w bundle-services">
              <div className="danh-sach-goi-dich-vu">
                <TableWrapper
                  rowKey={(record) => {
                    return record.ma;
                  }}
                  columns={columnsGoiDichVu}
                  dataSource={listGoiDv}
                  rowClassName={(record, index) => {
                    return `goi-dich-vu-item ${
                      record.dichVuId == state.activeLink
                        ? "selected-goi-dich-vu"
                        : ""
                    }`;
                  }}
                  showHeader={false}
                  onRow={(record, rowIndex) => {
                    return {
                      onClick: onSelectGoiDichVu(record),
                    };
                  }}
                  locale={{
                    emptyText: renderEmptyTextLeftTable(),
                  }}
                  scroll={{ y: 350 }}
                />
              </div>
            </div>
          )}

          <SplitPanel onDragEnd={() => handleResizeSplit()}>
            <div
              className="content-equal-w content-left"
              ref={refSplitPanelLeft}
              style={
                splitCacheCustomizeMemo && {
                  width: splitCacheCustomizeMemo[0],
                }
              }
            >
              {state.loaiDichVu != LOAI_DICH_VU.BO_CHI_DINH ? (
                <Row
                  justify="space-between"
                  align="middle"
                  className="bo-chi-dinh"
                >
                  <Select
                    placeholder={t("khamBenh.donThuoc.chonBoChiDinh")}
                    data={listGoiDv}
                    onChange={onChangeBoChiDinh}
                    style={{ width: "100%" }}
                  ></Select>
                  <Setting
                    refTable={refSettingsLeft}
                    className="setting-table-inside-select"
                  />
                </Row>
              ) : (
                <div className="title-table">
                  <div className="title-table__left">{t("common.dichVu")}</div>
                  <div className="title-table__right">
                    <Setting refTable={refSettingsLeft} />
                  </div>
                </div>
              )}

              <div className="danh-sach-dich-vu">
                <TableWrapper
                  rowKey={(record) => {
                    return record.id || record.dichVuId;
                  }}
                  columns={columnsDichVu}
                  dataSource={listDichVu}
                  rowClassName={(record, index) =>
                    state?.key === (record.id || record.dichVuId)
                      ? "row-actived table-row-odd " +
                        (record.id || record.dichVuId)
                      : "table-row-odd " + (record.id || record.dichVuId)
                  }
                  showHeader={
                    state.loaiDichVu == LOAI_DICH_VU.BO_CHI_DINH &&
                    listDichVu &&
                    listDichVu.length > 0
                  }
                  onRow={(record, rowIndex) => {
                    return {
                      onClick: onSelectDichVu(record),
                    };
                  }}
                  locale={{
                    emptyText: renderEmptyTextLeftTable(),
                  }}
                  scroll={{ y: 350 }}
                  ref={refSettingsLeft}
                  tableName="table_DVKT_ChiDinhDichVu"
                />
                {state.loaiDichVu != "KSK" && !!listDichVu.length && (
                  <Pagination
                    listData={listDichVu}
                    onChange={onChangePage}
                    current={page + 1}
                    pageSize={size}
                    total={totalElements}
                    onShowSizeChange={onSizeChange}
                    stylePagination={{ justifyContent: "flex-start" }}
                  />
                )}
              </div>
            </div>
            <div
              className="content-equal-w content-right"
              ref={refSplitPanelRight}
              style={
                splitCacheCustomizeMemo && {
                  width: splitCacheCustomizeMemo[1],
                }
              }
            >
              <div className="title">
                <div className="title__left">
                  <img src={CircleCheck} alt="" /> {t("common.daChon")}
                </div>
                <div className="title__right">
                  {t("khamBenh.chiDinh.tongTien")}:{" "}
                  {(thanhTien || 0).formatPrice()} đ
                  {!!listSelectedDv.length && (
                    <Setting
                      refTable={refSettings}
                      onCallBackOk={onCallBackOk}
                    />
                  )}
                </div>
              </div>
              <div className="content-body">
                {!listSelectedDv.length || listSelectedDv.length === 0 ? (
                  <BlankContentWrapper>
                    <div>
                      {!state.disableChiDinh
                        ? t("khamBenh.chiDinh.yeuCauNhapChiDinhDichVu")
                        : t(
                            "khamBenh.chiDinh.yeuCauNhapChanDoanTruocChiDinhDichVu"
                          )}
                    </div>
                  </BlankContentWrapper>
                ) : (
                  <>
                    <TableWrapper
                      rowKey={(record) => {
                        return record.uniqueKey;
                      }}
                      columns={
                        columnsChooseDvtMemo ||
                        Object.values(columnsChooseDv || {})
                      }
                      dataSource={(listSelectedDv || []).map((x, idx) => ({
                        ...x,
                        index: idx + 1,
                      }))}
                      scroll={{ x: 800 }}
                      ref={refSettings}
                      tableName="table_DVKT_DichVuDaChon"
                      columnResizable={true}
                    />
                  </>
                )}
              </div>
            </div>
          </SplitPanel>
        </div>
      </Main>
      <ModalBoSungThongTinDichVu
        ref={refModalBoSungThongTinDichVu}
        onDichVuKemTheo={onDichVuKemTheo}
      />
      <ModalThongTinThuoc ref={refModalThongTinThuoc} />
      <ModalCanhBaoDVChuaDenNgayHuongBH
        ref={refModalCanhBaoDVChuaDenNgayHuongBH}
      />
      <ModalCanhBaoKeTrung ref={refModalCanhBaoKeTrung} />
      <ModalVuotDinhMuc ref={refModalVuotDinhMuc} />
    </ModalTemplate>
  );
});

export default ModalChiDinhDichVu;
