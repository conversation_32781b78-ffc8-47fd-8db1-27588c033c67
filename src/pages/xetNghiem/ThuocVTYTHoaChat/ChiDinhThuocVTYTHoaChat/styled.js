import styled from "styled-components";

export const Main = styled.div`
  display: flex;
  align-items: baseline;
  & .search-area {
    display: flex;
    align-items: center;
    & .ant-select {
      width: 200px;
      margin-right: 10px;
    }
    .select-custom {
      width: clamp(350px, 100%, 500px) !important;
      margin-left: 10px;
    }
    & .search-text {
      position: relative;
      cursor: pointer;
      & img {
        position: absolute;
        top: 10px;
        left: 5px;
        z-index: 1;
      }
      & input {
        text-indent: 10px;
        cursor: pointer;
        width: 200px;
      }
    }
  }
  & .require-loai-kho {
    height: 18px;
    color: red;
    visibility: inherit;
  }
  & .require-kho {
    height: 18px;
    color: red;
    margin-left: 205px;
    visibility: inherit;
  }
  .ant-form-item-explain-error {
    font-size: 14px;
    color: #ff4d4f;
    font-weight: 500;
    text-align: left;
    padding: 4px 8px;
    background-color: #fff2f0;
    border-radius: 4px;
    display: inline-block;
  }
`;
