import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { Input } from "antd";
import { useParams } from "react-router-dom";
import moment from "moment";
import { useQueryClient } from "@tanstack/react-query";
import { uniqBy } from "lodash";

import { useCache, useEnum, useQueryAll, useStore, useThietLap } from "hooks";

import { Checkbox, HotKeyTrigger, Select } from "components";
import ModalChiDinhThuoc from "pages/khamBenh/DonThuoc/ModalChiDinhThuoc";
import ChiDinhDichVuVatTu from "pages/chiDinhDichVu/DichVuVatTu";
import ChiDinhDichVuHoatChat from "pages/chiDinhDichVu/DichVuHoaChat";
import {
  ENUM,
  THIET_LAP_CHUNG,
  LOAI_DICH_VU,
  DOI_TUONG_KCB,
  LOAI_DON_THUOC,
  CACHE_KEY,
} from "constants/index";
import imgSearch from "assets/images/template/icSearch.png";
import { Main } from "./styled";
import ThemChiDinhSpan from "pages/chiDinhDichVu/components/ThemChiDinhTxtSpan";
import { useXetNghiem } from "pages/xetNghiem/contexts";
import { isArray } from "utils/index";
import { query } from "redux-store/stores";

function ChiDinhThuocVTYTHoaChat({ layerId }) {
  const { t } = useTranslation();

  const [state, _setState] = useState({
    isTuTruc: false,
    loaiDichVu: LOAI_DICH_VU.THUOC,
    dvKemTheo: true,
  });

  const setState = (data) => {
    _setState((prevState) => ({
      ...prevState,
      ...data,
    }));
  };

  const { nbDotDieuTriId } = useParams();
  const [listloaiDichVu] = useEnum(ENUM.LOAI_DICH_VU);
  const [listTrangThaiDichVu] = useEnum(ENUM.TRANG_THAI_DICH_VU);
  const listThietLapChonKhoThuoc = useSelector(
    (state) => state.thietLapChonKho.listThietLapChonKhoThuoc
  );
  const listThietLapChonKhoVatTu = useSelector(
    (state) => state.thietLapChonKho.listThietLapChonKhoVatTu
  );
  const listThietLapChonKhoHoaChat = useSelector(
    (state) => state.thietLapChonKho.listThietLapChonKhoHoaChat
  );
  const thongTinCoBan = useStore("nbDotDieuTri.thongTinCoBan");
  const listToDieuTri = useStore("toDieuTri.listToDieuTri", []);
  const configData = useStore("chiDinhKhamBenh.configData", null);
  const refModalChiDinhThuoc = useRef(null);
  const refModalChiDinhVatTu = useRef(null);
  const refModalChiDinhHoaChat = useRef(null);
  const [dataHIEN_THI_CHECKBOX_TU_TRUC] = useThietLap(
    THIET_LAP_CHUNG.HIEN_THI_CHECKBOX_TU_TRUC
  );
  const [dataKHONG_HIEN_THI_NGUOI_BENH_CHUA_THANH_TOAN] = useThietLap(
    THIET_LAP_CHUNG.KHONG_HIEN_THI_NGUOI_BENH_CHUA_THANH_TOAN
  );
  const { xetNghiemState } = useXetNghiem();
  const { phongThucHienId, tuThoiGian, denThoiGian, finishedLoadState } =
    xetNghiemState;

  const { data: listAllPhong } = useQueryAll(query.phong.queryAllPhong);
  const { listServices = [] } = useSelector((state) => state.layMauXN);
  const [loaiChiDinhKemXn, setLoaiChiDinhKemXn, loadCacheFinish] = useCache(
    "",
    CACHE_KEY.LOAI_DICH_VU_CHI_DINH_KEM_XET_NGHIEM,
    LOAI_DICH_VU.THUOC,
    false,
    true
  );
  const queryClient = useQueryClient();

  const {
    thietLapChonKho: { getListThietLapChonKhoTheoTaiKhoan },
    toDieuTri: { getToDieuTri },
    chiDinhKhamBenh: { updateConfigData },
    layMauXN: { onSearch, clearData },
    chiDinhHoaChat: { updateData: updateDataChiDinhHoaChat },
  } = useDispatch();

  const listLoaiDichVuMemo = useMemo(() => {
    return listloaiDichVu.filter((x) =>
      [LOAI_DICH_VU.THUOC, LOAI_DICH_VU.VAT_TU, LOAI_DICH_VU.HOA_CHAT].includes(
        x.id
      )
    );
  }, [listloaiDichVu]);

  const getListDvXetNghiem = useCallback(
    (dvKemTheo) => {
      if (!nbDotDieuTriId || !finishedLoadState) return;
      onSearch({
        dataSearch: {
          nbDotDieuTriId,
          thoiGianThucHienTu: tuThoiGian
            ? moment(tuThoiGian).format("YYYY-MM-DD 00:00:00")
            : null,
          thoiGianThucHienDen: denThoiGian
            ? moment(denThoiGian).format("YYYY-MM-DD 23:59:59")
            : null,
          khongThucHien: false,
          dsTrangThaiHoan: null,
          dsTrangThaiHoan: [0, 10, 20],
          ...(dataKHONG_HIEN_THI_NGUOI_BENH_CHUA_THANH_TOAN?.eval()
            ? { dieuKienThanhToan: true }
            : {}),
          dvKemTheo: dvKemTheo || undefined,
        },
      });
    },
    [
      nbDotDieuTriId,
      phongThucHienId,
      tuThoiGian,
      denThoiGian,
      finishedLoadState,
      dataKHONG_HIEN_THI_NGUOI_BENH_CHUA_THANH_TOAN,
    ]
  );

  useEffect(() => {
    getListDvXetNghiem(true);
  }, [getListDvXetNghiem]);

  useEffect(() => {
    if (loadCacheFinish && loaiChiDinhKemXn) {
      setState({ loaiDichVu: loaiChiDinhKemXn });
    }
  }, [loadCacheFinish, loaiChiDinhKemXn]);

  useEffect(() => {
    return () => {
      clearData();
      updateConfigData({
        configData: {},
      });
    };
  }, []);

  const danhSachDichVuXetNghiem = useMemo(() => {
    if (
      !nbDotDieuTriId ||
      !isArray(listServices, true) ||
      !isArray(listTrangThaiDichVu, true)
    )
      return [];
    return listServices.map((item) => {
      return {
        ...item,
        label: [
          item.maDichVu,
          item.tenDichVu,
          item.thoiGianThucHien
            ? moment(item.thoiGianThucHien).format("DD/MM/YYYY HH:mm:ss")
            : "",
          listTrangThaiDichVu.find((i) => i.id === item.trangThai)?.ten,
        ]
          .filter(Boolean)
          .join(" - "),
      };
    });
  }, [listServices, nbDotDieuTriId, listTrangThaiDichVu]);

  useEffect(() => {
    if (
      isArray(danhSachDichVuXetNghiem, true) &&
      thongTinCoBan?.id &&
      isArray(listAllPhong, true)
    ) {
      let currentItem = danhSachDichVuXetNghiem[0];
      let khoaChiDinhId = listAllPhong.find(
        (i) => i.id === currentItem.phongThucHienId
      )?.khoaId;
      const baseParams = {
        khoaNbId: thongTinCoBan?.khoaNbId,
        khoaChiDinhId,
        doiTuong: thongTinCoBan?.doiTuong,
        loaiDoiTuongId: thongTinCoBan?.loaiDoiTuongId,
        capCuu: thongTinCoBan?.capCuu,
        phongId: currentItem.phongThucHienId,
        noiTru: [
          DOI_TUONG_KCB.NGOAI_TRU,
          DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_LINH_THUOC,
          DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_SU_DUNG_THUOC,
        ].includes(thongTinCoBan?.doiTuongKcb)
          ? false
          : true,
        canLamSang: false,
      };

      getListThietLapChonKhoTheoTaiKhoan({
        ...baseParams,
        loaiDichVu: LOAI_DICH_VU.VAT_TU,
      });
      getListThietLapChonKhoTheoTaiKhoan({
        ...baseParams,
        loaiDichVu: LOAI_DICH_VU.THUOC,
      });
      getListThietLapChonKhoTheoTaiKhoan({
        ...baseParams,
        loaiDichVu: LOAI_DICH_VU.HOA_CHAT,
      });
      getToDieuTri({
        nbDotDieuTriId: thongTinCoBan.id,
        dsKhoaChiDinhId: thongTinCoBan.khoaNbId,
      });
      updateConfigData({
        configData: {
          chiDinhTuDichVuId: currentItem.id,
          dsChiDinhTuDichVuId: currentItem.id,
          thoiGianThucHien: currentItem.thoiGianThucHien,
          nbDotDieuTriId: thongTinCoBan.id,
          nbThongTinId: thongTinCoBan.nbThongTinId,
          chiDinhTuLoaiDichVu: LOAI_DICH_VU.XET_NGHIEM,
          dsChiDinhTuLoaiDichVu: LOAI_DICH_VU.XET_NGHIEM,
          khoaChiDinhId,
          thongTinNguoiBenh: thongTinCoBan,
          phongThucHienId: currentItem.phongThucHienId,
          doiTuongKcb: thongTinCoBan.doiTuongKcb,
          noiTru: [
            DOI_TUONG_KCB.NGOAI_TRU,
            DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_LINH_THUOC,
            DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_SU_DUNG_THUOC,
          ].includes(thongTinCoBan?.doiTuongKcb)
            ? false
            : true,
          canLamSang: false,
          baseParams,
        },
      });
    }
  }, [danhSachDichVuXetNghiem, thongTinCoBan, listAllPhong]);

  const dataKhoTheoLoai = useMemo(() => {
    if (state.loaiDichVu === LOAI_DICH_VU.THUOC) {
      return listThietLapChonKhoThuoc;
    } else if (state.loaiDichVu === LOAI_DICH_VU.VAT_TU) {
      return listThietLapChonKhoVatTu;
    } else if (state.loaiDichVu === LOAI_DICH_VU.HOA_CHAT) {
      return listThietLapChonKhoHoaChat;
    }
    return [];
  }, [
    state.loaiDichVu,
    listThietLapChonKhoThuoc,
    listThietLapChonKhoVatTu,
    listThietLapChonKhoHoaChat,
  ]);

  const dataKho = useMemo(() => {
    if (
      dataHIEN_THI_CHECKBOX_TU_TRUC?.eval() &&
      state.loaiDichVu !== LOAI_DICH_VU.HOA_CHAT
    ) {
      return uniqBy(dataKhoTheoLoai || [], "id").filter((item) =>
        state.isTuTruc
          ? item.dsCoCheDuyetPhat?.includes(20)
          : !item.dsCoCheDuyetPhat?.includes(20)
      );
    }

    return uniqBy(dataKhoTheoLoai || [], "id");
  }, [
    dataKhoTheoLoai,
    state.isTuTruc,
    dataHIEN_THI_CHECKBOX_TU_TRUC,
    state.loaiDichVu,
  ]);

  const dataToDieuTri = useMemo(() => {
    return listToDieuTri.map((item) => ({
      id: item.id,
      ten: `${moment(item.thoiGianYLenh).format("DD/MM/YYYY HH:mm:ss")} - ${
        item.tenBacSiDieuTri || ""
      }`,
    }));
  }, [listToDieuTri]);

  const onShowPopupChiDinh = () => {
    if (!nbDotDieuTriId) return;
    if (state.loaiDichVu === LOAI_DICH_VU.THUOC) {
      const options = {
        loaiDonThuoc: LOAI_DON_THUOC.THUOC_KHO,
        khoId: state.khoId,
        isTuTruc: state.isTuTruc,
        dataKho,
        dataToDieuTri: dataToDieuTri,
        isExam: false,
        danhSachDichVuXetNghiem,
        listAllPhong,
      };
      refModalChiDinhThuoc.current &&
        refModalChiDinhThuoc.current.show(options, () => {
          queryClient.invalidateQueries({
            queryKey: ["danhSachChiDinhThuocVTYTHoaChat", nbDotDieuTriId],
          });
        });
    } else if (state.loaiDichVu === LOAI_DICH_VU.VAT_TU) {
      refModalChiDinhVatTu.current?.show(
        {
          dataKho,
          danhSachDichVuXetNghiem,
          khoId: state?.khoId,
          isTuTruc: state.isTuTruc,
          listAllPhong,
        },
        () => {
          queryClient.invalidateQueries({
            queryKey: ["danhSachChiDinhThuocVTYTHoaChat", nbDotDieuTriId],
          });
        }
      );
    } else {
      // loại hoát chất
      refModalChiDinhHoaChat.current &&
        refModalChiDinhHoaChat.current.show(
          {
            dataKho,
            danhSachDichVuXetNghiem,
            khoId: state?.khoId,
            listAllPhong,
          },
          () => {
            queryClient.invalidateQueries({
              queryKey: ["danhSachChiDinhThuocVTYTHoaChat", nbDotDieuTriId],
            });
          }
        );
    }
  };

  const onChangeXetNghiem = (value) => {
    if (value !== configData.chiDinhTuDichVuId) {
      let currentItem = (danhSachDichVuXetNghiem || []).find(
        (i) => i.id === value
      );
      let khoaChiDinhId = (listAllPhong || []).find(
        (i) => i.id === currentItem.phongThucHienId
      )?.khoaId;
      getListThietLapChonKhoTheoTaiKhoan({
        ...configData.baseParams,
        khoaChiDinhId,
        loaiDichVu: state.loaiDichVu,
      });
      updateConfigData({
        configData: {
          ...configData,
          chiDinhTuDichVuId: value,
          dsChiDinhTuDichVuId: value,
        },
      });
    }
  };

  const onChangeLoaiDv = (value) => {
    setState({ loaiDichVu: value });
    setLoaiChiDinhKemXn(value);
  };

  const onSelectKho = (value, data) => {
    setState({
      khoId: value,
      kho: data,
    });
    if (state.loaiDichVu === LOAI_DICH_VU.HOA_CHAT) {
      updateDataChiDinhHoaChat({
        dataSearch: { khoId: value },
      });
    }
  };

  const disabledChiDinh = !isArray(danhSachDichVuXetNghiem, true);

  if (disabledChiDinh) {
    return (
      <Main>
        <div className="ant-form-item-explain-error">
          {t(
            "xetNghiem.nguoiBenhKhongTonTaiXetNghiemDeChiDinhThemDichVuKemTheo"
          )}
        </div>
        <Checkbox
          checked={state.dvKemTheo}
          onChange={(e) => {
            const value = e?.target?.checked;
            setState({ dvKemTheo: value });
            getListDvXetNghiem(value);
          }}
          style={{ marginLeft: "10px" }}
        >
          {t("xetNghiem.chiHienThiXNCoDVKemTheo")}
        </Checkbox>
      </Main>
    );
  }

  return (
    <Main className="fadeIn">
      <ThemChiDinhSpan />
      <div>
        <div className="search-area">
          <Select
            data={listLoaiDichVuMemo}
            style={{ width: "150px" }}
            placeholder={t("danhMuc.chonLoaiDichVu")}
            onChange={onChangeLoaiDv}
            value={state.loaiDichVu}
            allowClear={true}
            disabled={disabledChiDinh}
          />
          <Select
            data={dataKho}
            style={{ width: "200px" }}
            placeholder={t("khamBenh.donThuoc.vuiLongChonKho")}
            onChange={onSelectKho}
            value={state.khoId}
            disabled={disabledChiDinh}
          />
          <HotKeyTrigger
            layerIds={[layerId]}
            hotKey="F2"
            triggerEvent={onShowPopupChiDinh}
          >
            <div className="search-text">
              <img src={imgSearch} alt="imgSearch" />
              <Input
                id={"inputChiDinhThuocId"}
                placeholder={`${t("common.timKiem")} [F2]`}
                onKeyPress={(e) => {
                  if (e.key === "Enter") {
                    onShowPopupChiDinh();
                  }
                  e.preventDefault();
                  e.target.value = "";
                  return null;
                }}
                readOnly={true}
                disabeld={disabledChiDinh}
                onClick={onShowPopupChiDinh}
              />
            </div>
          </HotKeyTrigger>
          {dataHIEN_THI_CHECKBOX_TU_TRUC?.eval() &&
            [LOAI_DICH_VU.THUOC, LOAI_DICH_VU.VAT_TU].includes(
              state.loaiDichVu
            ) && (
              <Checkbox
                checked={state.isTuTruc}
                onChange={(e) => {
                  const value = e?.target?.checked;

                  setState({
                    isTuTruc: value,
                    ...(value ? { khoId: null } : {}),
                  });
                }}
                style={{ marginLeft: "10px" }}
              >
                {t("kho.tuTruc")}
              </Checkbox>
            )}
          <Select
            className="select-custom"
            placeholder={t("xetNghiem.chonDichVuXetNghiem")}
            data={danhSachDichVuXetNghiem}
            value={configData?.chiDinhTuDichVuId}
            onChange={onChangeXetNghiem}
            allowClear={true}
            getLabel={(item) => item.label}
            dropdownMatchSelectWidth={600}
            disabeld={disabledChiDinh}
          />
          {!disabledChiDinh && (
            <Checkbox
              checked={state.dvKemTheo}
              onChange={(e) => {
                const value = e?.target?.checked;
                setState({ dvKemTheo: value });
                getListDvXetNghiem(value);
              }}
              style={{ marginLeft: "10px" }}
            >
              {t("xetNghiem.chiHienThiXNCoDVKemTheo")}
            </Checkbox>
          )}
        </div>
        <ModalChiDinhThuoc ref={refModalChiDinhThuoc} isXetNghiem={true} />
        <ChiDinhDichVuVatTu ref={refModalChiDinhVatTu} />
        <ChiDinhDichVuHoatChat
          ref={refModalChiDinhHoaChat}
          configData={configData}
          dataNb={thongTinCoBan}
          chiDinhTuLoaiDichVu={configData?.chiDinhTuLoaiDichVu}
        />
      </div>
    </Main>
  );
}

export default ChiDinhThuocVTYTHoaChat;
