import React, { useEffect, useState, useRef, useMemo } from "react";
import { useTranslation } from "react-i18next";
import { useLocation, useParams, useHistory } from "react-router-dom";
import { useDispatch } from "react-redux";
import { getState } from "redux-store/stores";
import { get, uniqBy } from "lodash";
import { Input, Row } from "antd";
import ThongTinChiTiet from "./ThongTinChiTiet";
import { checkRole } from "lib-utils/role-utils";
import { ENUM, HOTKEY, ROLES } from "constants/index";
import { useEnum, useStore, useFillMaHoSo } from "hooks";
import nbDichVuXN from "data-access/nb-dv-xet-nghiem-provider";
import { SVG } from "assets";
import { Main, InputSearch } from "./styled";
import nbDotDieuTriProvider from "data-access/nb-dot-dieu-tri-provider";
import { SelectLoadMore } from "components";

function ThongTinBN({
  layerId,
  hideSoPhieu = false,
  isThuocVTYTHoaChat = false,
}) {
  const { t } = useTranslation();

  const refInputSearch = useRef(null);
  const location = useLocation();
  const history = useHistory();
  const { nbDotDieuTriId } = useParams();
  const thongTinCoBan = useStore("nbDotDieuTri.thongTinCoBan");
  const {
    nbDotDieuTri: { getThongTinCoBan, clearData },
    phimTat: { onRegisterHotkey },
  } = useDispatch();
  const [listGioiTinh] = useEnum(ENUM.GIOI_TINH);

  const { formatMaHoSo, testMaHoSo } = useFillMaHoSo();

  const [state, _setState] = useState({ show: false, nbThongTinId: null });
  const [searchChange, setSearchChange] = useState(false);
  const setState = (data) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  useEffect(() => {
    onRegisterHotkey({
      layerId: layerId,
      hotKeys: [
        {
          keyCode: HOTKEY.F6, //F6
          onEvent: () => {
            refInputSearch.current.focus();
          },
        },
        {
          keyCode: HOTKEY.F1, //F1
          onEvent: () => {
            onToggleModal()();
          },
        },
      ],
    });
  }, []);

  useEffect(() => {
    if (nbDotDieuTriId) {
      getThongTinCoBan(nbDotDieuTriId);
    }
    if (!nbDotDieuTriId) {
      clearData();
    }
  }, [nbDotDieuTriId]);

  useEffect(() => {
    return () => {
      clearData();
    };
  }, []);

  const updateNbDieuTriId = (nbDotDieuTriId, param) => {
    const { pathname } = location;
    if (pathname.includes("/lay-mau")) {
      history.push(
        `/xet-nghiem/lay-mau${
          nbDotDieuTriId
            ? `/${nbDotDieuTriId}${param.soPhieu ? `/${param.soPhieu}` : ``}`
            : ``
        }${window.location.search}`
      );
    }
    if (pathname.includes("/sinh-hoa-huyet-hoc")) {
      history.push(
        `/xet-nghiem/sinh-hoa-huyet-hoc${
          nbDotDieuTriId
            ? `/${nbDotDieuTriId}${param.soPhieu ? `/${param.soPhieu}` : ``}`
            : ``
        }${window.location.search}`
      );
    }
    if (pathname.includes("/giai-phau-benh-vi-ky-sinh")) {
      history.push(
        `/xet-nghiem/giai-phau-benh-vi-ky-sinh${
          nbDotDieuTriId ? `/${nbDotDieuTriId}` : ``
        }${window.location.search}`
      );
    }
    if (pathname.includes("/thuoc-vat-tu-hoa-chat")) {
      history.push(
        `/xet-nghiem/thuoc-vat-tu-hoa-chat${
          nbDotDieuTriId ? `/${nbDotDieuTriId}` : ``
        }${window.location.search}`
      );
    }
  };

  const onToggleModal = (key) => () => {
    if (key === "hidden") {
      if (state.show) {
        setState({
          show: false,
        });
      }
    } else {
      setState({
        show: !state.show,
      });
    }
  };
  const handleSearchBN = (value) => {
    const { qrBN = "", soPhieu = "" } = state;
    let str = qrBN.trim() || value || "";
    let param = {};

    //quét mã QR
    if (str.indexOf("NB|") === 0 && str.indexOf("$") === str.length - 1) {
      let array = str.split("|");
      let maHoSo = array[3] || "";
      let soPhieuSearch = array[24]?.endsWith("$")
        ? array[24].substring(0, array[24]?.length - 1)
        : array[24] || "";

      param = param = { maHoSo: maHoSo };
      param.soPhieu = soPhieuSearch;
    } else {
      if (testMaHoSo(str)) {
        param = { maHoSo: formatMaHoSo(str) };
      } else if (/^[0-9]+$/.test(str)) {
        //có 10 số => mã hồ sơ
        if (/^[0-9]{10}$/.test(str)) {
          param = { maHoSo: formatMaHoSo(str) };
        }
      } else if (/^[a-zA-Z]+[0-9]+$/.test(str)) {
        //bắt đầu chữ, kết thúc số
        param = { maNb: str };
      } else if (/^[0-9A-F]{8}$/.test(str)) {
        //số và chữ có 8 ký tự
        param = { maThe: str };
      }
      param.soPhieu = soPhieu.trim();
    }

    //đánh dấu để ko update lại list
    param.isQrSearch = true;

    const lastestDataSearch = get(getState(), "xetNghiem.lastestDataSearch");

    if (param?.maHoSo || param?.soPhieu || param?.maThe || param?.maNb) {
      nbDichVuXN
        .getBNXetNghiem({
          ...lastestDataSearch,
          ...param,
          tuThoiGianThucHien: null,
          denThoiGianThucHien: null,
          page: null,
          size: null,
        })
        .then((s) => {
          updateNbDieuTriId(s?.data?.[0]?.id, param);
        });
    } else {
      nbDichVuXN.getBNXetNghiem({
        ...lastestDataSearch,
        ...param,
        page: null,
        size: null,
      });
      updateNbDieuTriId(null, param);
    }
  };

  const onChange = (key) => (e, item) => {
    let value = "";
    if (e?.target) {
      if (e.target.hasOwnProperty("checked")) value = e.target.checked;
      else value = e.target.value;
    } else value = e;

    setState({ [key]: value });
  };

  const onKeyDown = (e) => {
    if (e.key === "Enter") {
      handleSearchBN();
    }
  };

  const gioiTinh =
    listGioiTinh.find((item) => item.id === thongTinCoBan?.gioiTinh) || {};

  const onBlur = () => {
    setState({ focusInput: false });
  };

  const customFilterAndMapData = (data) => {
    const listData = (data || []).map((i) => ({
      value: i.id,
      label: `${i.maHoSo} - ${i.tenNb}`,
    }));
    return uniqBy(listData, "value");
  };

  return (
    <Main bottom={0}>
      <Row align="middle">
        {!isThuocVTYTHoaChat && (
          <>
            <InputSearch focusInput={state.focusInput}>
              <SVG.IcSearch className="icon-search" />
              <Input
                placeholder={t("xetNghiem.timMaNbQrNbMaHoSo")}
                autoFocus
                onChange={onChange("qrBN")}
                onKeyDown={onKeyDown}
                onFocus={() =>
                  setState({
                    focusInput: true,
                  })
                }
                onBlur={onBlur}
              />
              <SVG.IcQrCode className="qr-search" />
            </InputSearch>
            {!hideSoPhieu && (
              <InputSearch>
                <SVG.IcSearch className="icon-search" />
                <Input
                  placeholder={t("xetNghiem.timKiemTheoSoPhieu")}
                  onChange={onChange("soPhieu")}
                  onKeyDown={onKeyDown}
                />
                <SVG.IcQrCode className="qr-search" />
              </InputSearch>
            )}
          </>
        )}
        {isThuocVTYTHoaChat && (
          <SelectLoadMore
            api={(params) => {
              const { value, ...searchParams } = params || {};
              let _value = value?.trim();
              switch (true) {
                case testMaHoSo(_value):
                  Object.assign(searchParams, { maHoSo: formatMaHoSo(_value) });
                  break;
                case /^[0-9]{10}$/.test(_value):
                  Object.assign(searchParams, { maHoSo: formatMaHoSo(_value) });
                  break;
                case /^[0-9]{7}$/.test(_value):
                  Object.assign(searchParams, { maBenhAn: _value });
                  break;
                case /^[a-zA-Z]+[0-9]+$/.test(_value):
                  Object.assign(searchParams, { maNb: _value });
                  break;
                case /^[0-9A-F]{8}$/.test(_value):
                  Object.assign(searchParams, { maThe: _value });
                  break;
                case !/^[0-9]+$/.test(_value) &&
                  !/^[0-9A-F]{8}$/.test(_value) &&
                  !/^[a-zA-Z]+[0-9]+$/.test(_value):
                  Object.assign(searchParams, { tenNb: _value });
                  break;
                default:
                  break;
              }
              setSearchChange(true);
              return nbDotDieuTriProvider.searchNBDotDieuTriTongHop(
                searchParams
              );
            }}
            customFilterAndMapData={customFilterAndMapData}
            onChange={(e) => {
              updateNbDieuTriId(e, {});
              setState({ nbThongTinId: e });
            }}
            value={
              state.nbThongTinId
                ? state.nbThongTinId
                : nbDotDieuTriId
                ? +nbDotDieuTriId
                : null
            }
            clearPreviousData
            keySearch={"value"}
            placeholder={t("xetNghiem.timMaNbQrNbMaHoSo")}
            className="select-custom"
            blurReset={true}
            firstLoadData={false}
            haveLoading
            addValue={
              thongTinCoBan?.id && !searchChange && !state.nbThongTinId
                ? [
                    {
                      value: thongTinCoBan.id,
                      label: `${thongTinCoBan.maHoSo} - ${thongTinCoBan.tenNb}`,
                    },
                  ]
                : []
            }
          />
        )}
      </Row>
      {!!thongTinCoBan.id && (
        <Row className="info-partinent">
          {/* <div className="info-partinent__index">BNS-0004</div> */}
          <div className="info-partinent__name">
            <span>{thongTinCoBan?.tenNb}</span>
            {` (${gioiTinh?.ten} - ${thongTinCoBan.tuoi2})`}
          </div>
          {checkRole([ROLES["XET_NGHIEM"].THONG_TIN_NB]) &&
            thongTinCoBan?.id && (
              <div className="info-partinent__detail" onClick={onToggleModal()}>
                {t("xetNghiem.xemThongTinDayDu")}
                <SVG.IcInfo />
                {state.show && <SVG.IcArrowUp className="icon-info" />}
              </div>
            )}
        </Row>
      )}
      {state.show && (
        <ThongTinChiTiet
          nbDotDieuTriId={nbDotDieuTriId}
          key={state.show + "-thong-tin-benh-nhan"}
          onToggleModal={onToggleModal}
        />
      )}
    </Main>
  );
}

export default ThongTinBN;
