import React, { useEffect, useRef, useState } from "react";
import { CloseOutlined, SaveOutlined } from "@ant-design/icons";
import { Form, Input, message } from "antd";
import { <PERSON><PERSON>, Page } from "components";
import { Main } from "./styled";
import { useTranslation } from "react-i18next";
import nbThongTinProvider from "data-access/nb-thong-tin-provider";
import nbDotDieuTriProvider from "data-access/nb-dot-dieu-tri-provider";
import { useDispatch } from "react-redux";
import { HOTKEY } from "constants/index";
import { throttle } from "lodash";
import { useGuid } from "hooks";

const ThietLapDoiMaNguoiBenh = ({}) => {
  const { t } = useTranslation();
  const refSubmit = useRef(null);
  const layerId = useGuid();
  const [error, setError] = useState("");
  const [form] = Form.useForm();
  const maHoSo = Form.useWatch("maHoSo", form);
  const maNb = Form.useWatch("maNb", form);
  const {
    phimTat: { onAddLayer, onRemoveLayer, onRegisterHotkey },
  } = useDispatch();

  const onHandleSubmit = async () => {
    try {
      const { maHoSo, maNb } = await form.validateFields();
      const responses = await Promise.all([
        nbDotDieuTriProvider.getNbDotDieuTri({
          maHoSo: maHoSo.trim(),
        }),
      ]);
      const oldInfo = responses[0]?.data;
      if (!oldInfo) {
        setError(
          t("kiosk.khongTonTaiMaHoSo{{maHoSo}}", {
            maHoSo,
          })
        );
      }
      if (oldInfo ) {
        nbDotDieuTriProvider
          .doiMaNguoiBenh({
            nbDotDieuTriId: oldInfo.id,
            maNb,
          })
          .then(() => {
            message.success(
              t("thietLap.doiMaNbChoHsXXXThanhCong", {
                maHoSo,
              })
            );
            setError("");
          })
          .catch((err) => {
            setError(err?.message);
          });
      }
    } catch (err) {
      setError(err?.message);
    }
  };
  const onCancel = () => {
    form.resetFields();
  };

  useEffect(() => {
    if (!maHoSo && !maNb) setError("");
  }, [maHoSo, maNb]);

  refSubmit.current = onHandleSubmit;

  useEffect(() => {
    onAddLayer({ layerId: layerId });
    onRegisterHotkey({
      layerId: layerId,
      hotKeys: [
        {
          keyCode: HOTKEY.ESC, //F1
          onEvent: onCancel,
        },
        {
          keyCode: HOTKEY.F4, //F4
          onEvent: (e) => {
            refSubmit.current && refSubmit.current();
          },
        },
      ],
    });
    return () => {
      onRemoveLayer({ layerId: layerId });
    };
  }, []);

  return (
    <Page
      breadcrumb={[
        { title: t("thietLap.thietLap"), link: "/thiet-lap" },
        {
          title: t("thietLap.thietLapDoiMaNguoiBenh"),
          link: "/thiet-lap/doi-ma-nguoi-benh",
        },
      ]}
    >
      <Main title={t("thietLap.thietLapDoiMaNguoiBenh")}>
        <div className="main">
          <Form
            form={form}
            layout="vertical"
            style={{ width: "100%" }}
            className="form-custom"
            onFinish={onHandleSubmit}
            initialValues={{
              maHoSo: "",
              maNb: "",
            }}
          >
            <div className="form-item">
              <div className="label text-bold ">
                {t("thietLap.maHoSoCanThayDoi")}
                <span className="text-danger">&nbsp;*</span>
              </div>
              <Form.Item
                name="maHoSo"
                rules={[
                  {
                    required: true,
                    whitespace: true,
                    message: t("thietLap.vuiLongNhapMaHoSo"),
                  },
                ]}
              >
                <Input
                  autoFocus={true}
                  className="input-option"
                  placeholder={t("common.maHs")}
                  autoComplete="off"
                />
              </Form.Item>
            </div>
            <div className="form-item">
              <div className="label text-bold ">
                {t("thietLap.maNguoiBenhMoi")}
                <span className="text-danger">&nbsp;*</span>
              </div>
              <Form.Item
                name="maNb"
                rules={[
                  {
                    required: true,
                    whitespace: true,
                    message: t("thietLap.vuiLongNhapMaNguoiBenhMoi"),
                  },
                ]}
              >
                <Input
                  className="input-option"
                  placeholder={t("common.maNb")}
                  autoComplete="off"
                />
              </Form.Item>
            </div>
          </Form>
          <div className=" text flex flex-center text-danger">
            {error || <>&nbsp;</>}
          </div>
          <div className="action">
            <Button
              className="button-cancel"
              rightIcon={<CloseOutlined />}
              onClick={onCancel}
              iconHeight={15}
              minWidth={100}
            >
              {t("common.huy")}
            </Button>
            <Button
              type="primary"
              rightIcon={<SaveOutlined />}
              className="button-ok"
              onClick={throttle(form.submit, 1000)}
              iconHeight={18}
              minWidth={100}
              disabled={!(maHoSo?.trim() && maNb?.trim())}
            >
              {t("common.luu")}
            </Button>
          </div>
        </div>
      </Main>
    </Page>
  );
};

export default ThietLapDoiMaNguoiBenh;
