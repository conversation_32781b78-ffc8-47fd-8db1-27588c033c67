import React, { useEffect, useMemo, useState } from "react";
import { useDispatch } from "react-redux";
import { t } from "i18next";
import { useTranslation } from "react-i18next";
import { Input } from "antd";
import { Checkbox, Page, Select, TableWrapper, Button } from "components";
import { useEnum, useLoading, useStore } from "hooks";
import { DOI_TUONG_KCB, ENUM } from "constants/index";
import { SVG } from "assets";
import { Main } from "./styled";
import { checkRole } from "lib-utils/role-utils";
import { ROLES } from "constants/index";

const { Column } = TableWrapper;

const dataThietLap = [
  {
    id: "phieuLinhThuoc",
    ten: t("kho.trangThaiThuoc"),
  },
  {
    id: "phieuLinhVatTuHoaChat",
    ten: t("thietLap.phieuLinhVatTuHoaChatCpdd"),
  },
  {
    id: "phieuTra",
    ten: t("thietLap.phieuTraVatTuHoaChatCpdd"),
  },
  {
    id: "phieuLinhMau",
    ten: t("thietLap.phieuLinhMau"),
  },
  {
    id: "phanGiuong",
    ten: t("thietLap.batBuocPhanGiuong"),
  },
  {
    id: "trangThaiHoanThanhDv",
    ten: t("thietLap.trangThaiHoanThanhDichVuKyThuat"),
  },
  {
    id: "hoanThanhKy",
    ten: t("thietLap.hoanThanhKyCacGiayTo"),
  },
  {
    id: "phieuLinhSuatAn",
    ten: t("thietLap.phieuLinhSuatAn"),
  },
  {
    id: "phieuTraSuatAn",
    ten: t("thietLap.phieuTraSuatAn"),
  },
  {
    id: "maDoiTuongKcb",
    ten: t("thietLap.khaiBaoDayDuThongTinDoiTuongKcb"),
  },
  {
    id: "hoiChanThuocDauSao",
    ten: t("thietLap.batBuocHoiChanThuocDauSao"),
  },
  {
    id: "thoiGianThucHienThuocVatTuToDieuTri",
    ten: t("thietLap.khongKiemTraThoiGianThucHienThuocVatTu"),
  },
  {
    id: "thoiGianThucHienDvkt",
    ten: t("thietLap.khongKiemTraThoiGianThucHienDvkt"),
  },
  {
    id: "phieuLinhChiDinhTuCls",
    ten: t("thietLap.trangThaiThuocChiDinhTuCls"),
  },
];

const LIST_DOI_TUONG_KCB = [
  DOI_TUONG_KCB.DIEU_TRI_NGOAI_TRU,
  DOI_TUONG_KCB.DIEU_TRI_NOI_TRU,
  DOI_TUONG_KCB.DIEU_TRI_NOI_TRU_BAN_NGAY,
];

const listCheckbox = [
  "phanGiuong",
  "trangThaiHoanThanhDv",
  "hoanThanhKy",
  "maDoiTuongKcb",
  "hoiChanThuocDauSao",
  "thoiGianThucHienThuocVatTuToDieuTri",
  "thoiGianThucHienDvkt",
];

const ThietLapDieuKienChuyenKhoaRaVien = ({}) => {
  const { t } = useTranslation();
  const { showLoading, hideLoading } = useLoading();
  const [state, _setState] = useState({ currentIndex: -1, currentItem: null });
  const {
    thietLap: { getThietLapChuyenKhoaRaVien, postThietLapChuyenKhoaRaVien },
  } = useDispatch();

  const chuyenKhoaRaVien = useStore("thietLap.chuyenKhoaRaVien", {});
  const [listTrangThaiThuoc] = useEnum(ENUM.TRANG_THAI_THUOC);
  const [listTrangThaiPhieuNhapXuat] = useEnum(ENUM.TRANG_THAI_PHIEU_NHAP_XUAT);
  const [listTrangThaiPhieuLinhSuatAn] = useEnum(
    ENUM.TRANG_THAI_PHIEU_LINH_SUAT_AN
  );
  const [listDoiTuongKcb] = useEnum(ENUM.DOI_TUONG_KCB);

  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  useEffect(() => {
    getThietLapChuyenKhoaRaVien();
  }, []);

  const dataSource = useMemo(() => {
    let data = [];
    Object.keys(chuyenKhoaRaVien).map((key) => {
      data.push({
        id: key,
        tenThietLap: dataThietLap.find((x) => x.id == key)?.ten,
        ...chuyenKhoaRaVien[key],
      });
    });
    data = data?.filter(item => item?.id !== "hoanThanhKy");
    return data;
  }, [chuyenKhoaRaVien]);

  const onChange = (key, index) => (e) => {
    let value = "";
    if (e?.target) {
      if (e.target.hasOwnProperty("checked")) value = e.target.checked;
      else value = e.target.value;
    } else value = e;
    let item = dataSource[index];
    item[key] = value;
    setState({ currentItem: item });
  };

  const onChangeCheckbox = (key, item, index) => (e) => {
    e.stopPropagation();
    const value = e.target.checked;
    let item = dataSource[index];
    item[key] = value;
    const data = {
      currentItem: item,
      ...(state.currentIndex !== index && {
        currentIndex: index,
      }),
    };
    setState(data);
  };

  const renderData = (item, data, index, key) => {
    if (key === "ghiChu") {
      if (index === state.currentIndex) {
        return (
          <Input
            placeholder={t("thietLap.ghiChu")}
            value={state?.currentItem[key]}
            onChange={onChange("ghiChu", index)}
          />
        );
      } else return item;
    } else if (key === "dsDoiTuongKcb") {
      if (!listCheckbox.includes(data?.id)) return null;
      if (index === state.currentIndex) {
        return (
          <Select
            placeholder={t("thietLap.doiTuongApDung")}
            value={state?.currentItem[key]}
            onChange={onChange("dsDoiTuongKcb", index)}
            data={listDoiTuongKcb.filter((i) =>
              LIST_DOI_TUONG_KCB.includes(i.id)
            )}
            mode="multiple"
          />
        );
      } else {
        return item?.length
          ? listDoiTuongKcb
              .filter((x) => item.includes(x.id))
              .map((x) => x.ten)
              .join(", ")
          : "";
      }
    } else {
      if (
        data?.id === "phieuLinhThuoc" ||
        data?.id === "phieuLinhChiDinhTuCls"
      ) {
        if (index === state.currentIndex) {
          return (
            <Select
              value={state?.currentItem[key]}
              onChange={onChange([key], index)}
              data={listTrangThaiThuoc}
            />
          );
        } else {
          return listTrangThaiThuoc.find((x) => x.id === item)?.ten;
        }
      } else if (
        data?.id === "phieuLinhVatTuHoaChat" ||
        data?.id === "phieuTra" ||
        data?.id === "phieuLinhMau"
      ) {
        if (index === state.currentIndex) {
          return (
            <Select
              value={
                state?.currentItem[key] || t("thietLap.khongBatBuocTaoPhieu")
              }
              onChange={onChange([key], index)}
              data={[
                { id: null, ten: t("thietLap.khongBatBuocTaoPhieu") },
                ...listTrangThaiPhieuNhapXuat,
              ]}
            />
          );
        } else {
          return [
            { id: null, ten: t("thietLap.khongBatBuocTaoPhieu") },
            ...listTrangThaiPhieuNhapXuat,
          ].find((x) => x.id === item)?.ten;
        }
      } else if (
        data?.id === "phieuLinhSuatAn" ||
        data?.id === "phieuTraSuatAn"
      ) {
        const listTrangThai =
          data?.id === "phieuLinhSuatAn"
            ? listTrangThaiPhieuLinhSuatAn.filter((i) =>
                [10, 20].includes(i.id)
              )
            : listTrangThaiPhieuLinhSuatAn.filter((i) =>
                [30, 40].includes(i.id)
              );
        if (index === state.currentIndex) {
          return (
            <Select
              value={
                state?.currentItem[key] || t("thietLap.khongBatBuocTaoPhieu")
              }
              onChange={onChange([key], index)}
              data={[
                { id: null, ten: t("thietLap.khongBatBuocTaoPhieu") },
                ...listTrangThai,
              ]}
            />
          );
        } else {
          return [
            { id: null, ten: t("thietLap.khongBatBuocTaoPhieu") },
            ...listTrangThai,
          ].find((x) => x.id === item)?.ten;
        }
      } else if (listCheckbox.includes(data?.id) && item !== undefined) {
        return (
          <Checkbox
            checked={
              index == state.currentIndex ? state?.currentItem[key] : item
            }
            onChange={onChangeCheckbox(key, data, index)}
          />
        );
      }
    }
  };

  const columns = [
    Column({
      title: t("common.stt"),
      width: 50,
      dataIndex: "index",
      key: "index",
      align: "center",
      render: (_, data, index) => index + 1,
    }),
    Column({
      title: t("thietLap.tenThietLap"),
      dataIndex: "tenThietLap",
      key: "tenThietLap",
      width: 150,
      align: "center",
      render: (item, data, index) => {
        return item;
      },
    }),
    Column({
      title: t("thietLap.chuyenKhoaToiKhoaThuong"),
      dataIndex: "chuyenKhoaToiKhoaThuong",
      key: "chuyenKhoaToiKhoaThuong",
      width: 100,
      align: "center",
      render: (item, data, index) =>
        renderData(item, data, index, "chuyenKhoaToiKhoaThuong"),
    }),
    Column({
      title: t("thietLap.chuyenKhoaToiKhoaPhauThuat"),
      dataIndex: "chuyenKhoaToiKhoaPhauThuat",
      key: "chuyenKhoaToiKhoaPhauThuat",
      width: 100,
      align: "center",
      render: (item, data, index) =>
        renderData(item, data, index, "chuyenKhoaToiKhoaPhauThuat"),
    }),
    Column({
      title: t("thietLap.chuyenKhoaTuKhoaPhauThuat"),
      dataIndex: "chuyenKhoaTuKhoaPhauThuat",
      key: "chuyenKhoaTuKhoaPhauThuat",
      width: 100,
      align: "center",
      render: (item, data, index) =>
        renderData(item, data, index, "chuyenKhoaTuKhoaPhauThuat"),
    }),
    Column({
      title: t("thietLap.raVienTuKhoaThuong"),
      dataIndex: "raVienTuKhoaThuong",
      key: "raVienTuKhoaThuong",
      width: 100,
      align: "center",
      render: (item, data, index) =>
        renderData(item, data, index, "raVienTuKhoaThuong"),
    }),
    Column({
      title: t("thietLap.raVienTuKhoaPhauThuat"),
      dataIndex: "raVienTuKhoaPhauThuat",
      key: "raVienTuKhoaPhauThuat",
      width: 100,
      align: "center",
      render: (item, data, index) =>
        renderData(item, data, index, "raVienTuKhoaPhauThuat"),
    }),
    Column({
      title: t("thietLap.doiTuongApDung"),
      dataIndex: "dsDoiTuongKcb",
      key: "dsDoiTuongKcb",
      width: 100,
      render: (item, data, index) =>
        renderData(item, data, index, "dsDoiTuongKcb"),
    }),
    Column({
      title: t("thietLap.ghiChu"),
      dataIndex: "ghiChu",
      key: "ghiChu",
      width: 100,
      render: (item, data, index) => renderData(item, data, index, "ghiChu"),
    }),
  ];

  const onRow = (record, index) => {
    return {
      onClick: (event) => {
        setState({ currentIndex: index, currentItem: record });
      },
    };
  };

  const onSave = () => {
    if (dataSource.length) {
      const payload = dataSource.reduce((data, item) => {
        const { id, key, tenThietLap, ...rest } = item;
        data[id] = rest;
        return data;
      }, {});
      showLoading();
      postThietLapChuyenKhoaRaVien(payload)
        .then((s) => {
          setState({ currentIndex: -1, currentItem: null });
        })
        .catch((error) => {
          console.error(error);
        })
        .finally(() => {
          hideLoading();
        });
    }
  };
  return (
    <Page
      breadcrumb={[
        { title: t("thietLap.thietLap"), link: "/thiet-lap" },
        {
          title: t("thietLap.thietLapDieuKienChuyenKhoaRaVien"),
          link: "/thiet-lap//thiet-lap/thiet-lap-dieu-kien-chuyen-khoa-ra-vien",
        },
      ]}
      title={t("thietLap.thietLapDieuKienChuyenKhoaRaVien")}
      actionRight={
        checkRole([ROLES["DANH_MUC"].DIEU_KIEN_CHUYEN_KHOA_RA_VIEN_SUA]) && (
          <Button
            type="primary"
            minWidth={100}
            rightIcon={<SVG.IcSave />}
            onClick={onSave}
          >
            {t("common.luu")}
          </Button>
        )
      }
    >
      <Main>
        <TableWrapper columns={columns} dataSource={dataSource} onRow={onRow} />
      </Main>
    </Page>
  );
};

export default ThietLapDieuKienChuyenKhoaRaVien;
