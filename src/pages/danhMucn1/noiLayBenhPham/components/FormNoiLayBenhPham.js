import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from "react";
import { Checkbox, CreatedWrapper, Select } from "components";
import { Form, Input } from "antd";
import { useTranslation } from "react-i18next";
import { useEnum, useListAll, useStore } from "hooks";
import {
  ENUM,
  ROLES,
  HOTKEY,
  DOI_TUONG_KCB,
  LOAI_PHONG,
} from "constants/index";
import { checkRole } from "lib-utils/role-utils";
import { useDispatch } from "react-redux";
import { combineSort, safeConvertToArray } from "utils/index";
import { selectMaTen } from "redux-store/selectors";

const FormNoiLayBenhPham = (
  { handleSubmit, onCancel, editStatus, layerId },
  ref
) => {
  const { t } = useTranslation();

  const [form] = Form.useForm();
  const formRef = React.useRef();
  const [dataEdit, setdataEdit] = useState(null);

  const [state, _setState] = useState({ isRequireKhoaChiDinh: false });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  const refClickBtnAdd = useRef();
  const refClickBtnSave = useRef();
  const refAutoFocus = useRef(null);

  const { onRegisterHotkey } = useDispatch().phimTat;

  const [listAllKhoa] = useListAll("khoa", {}, true);
  const [listAllToaNha] = useListAll("toaNha", {}, true);
  const [listAllLoaiDoiTuong] = useListAll("loaiDoiTuong", {}, true);
  const [listAllLoaiHinhThanhToan] = useListAll("loaiHinhThanhToan", {}, true);
  const [listAllDichVu] = useListAll("dichVu", {}, true);

  const [listdoiTuongKcb] = useEnum(ENUM.DOI_TUONG_KCB);
  const [listAllPhong] = useListAll(
    "phong",
    {
      loaiPhong: LOAI_PHONG.LAY_MAU_BENH_PHAM,
      sort: combineSort({ active: 2, ten: 1 }),
    },
    true
  );

  const listAllNhomDichVuCap2 = useStore(
    "nhomDichVuCap2.listAllNhomDichVuCap2",
    []
  );
  const listAllNhomDichVuCap3 = useStore(
    "nhomDichVuCap3.listAllNhomDichVuCap3",
    []
  );

  const nhomDichVuCap1 = useStore("thietLap.nhomDichVuCap1");
  const listAllPhongTheoKhoa = useStore("phong.listAllPhongTheoKhoa", []);
  const listAllCauHinh = useStore("thietLapCauHinh.listAllCauHinh", []);

  const {
    loaiHinhThanhToan: { getListAllLoaiHinhThanhToan },
    thietLapCauHinh: { getListAllCauHinh },
    phong: { getListAllPhong },
    thietLap: { getNhomDichVuCap1 },
    nhomDichVuCap2: { getAllDichVuCap2, searchTongHopDichVuCap2 },
    nhomDichVuCap3: { getAllTongHopDichVuCap3, searchTongHopDichVuCap3 },
  } = useDispatch();

  useEffect(() => {
    getNhomDichVuCap1({
      path: "/nhom-dich-vu-cap1",
      loaiDichVu: 20,
    }).then((s) => {
      if (s && s?.payload) {
        let xetNghiem = s.payload.nhomDichVuCap1;
        if (xetNghiem) {
          searchTongHopDichVuCap2({
            page: "",
            size: "",
            active: true,
            nhomDichVuCap1Id: xetNghiem.id,
          }).then((res) => {
            let listNhomDvCap2 = res?.payload?.listGroupService2;
            searchTongHopDichVuCap3({
              page: "",
              size: "",
              active: true,
              dsNhomDichVuCap2Id: listNhomDvCap2?.map((i) => i.id),
            });
          });
        }
      } else {
        searchTongHopDichVuCap2({
          page: "",
          size: "",
          active: true,
        }).then((res) => {
          let listNhomDvCap2 = res?.payload?.listGroupService2;
          searchTongHopDichVuCap3({
            page: "",
            size: "",
            active: true,
            dsNhomDichVuCap2Id: listNhomDvCap2?.map((i) => i.id),
          });
        });
      }
    });
  }, []);

  useEffect(() => {
    getListAllCauHinh({ page: 0, size: 500 });
  }, []);

  useEffect(() => {
    if (refAutoFocus.current) {
      refAutoFocus.current.focus();
    }
  }, [dataEdit]);

  // register layerId
  useEffect(() => {
    onRegisterHotkey({
      layerId,
      hotKeys: [
        {
          keyCode: HOTKEY.F1, //F1
          onEvent: () => {
            refClickBtnAdd.current && refClickBtnAdd.current();
          },
        },
        {
          keyCode: HOTKEY.F4, //F4
          onEvent: (e) => {
            refClickBtnSave.current && refClickBtnSave.current(e);
          },
        },
      ],
    });
  }, []);

  useImperativeHandle(
    ref,
    () => ({
      setfields: (data) => {
        if (data?.editNoiLayBenhPhamId) {
          form.setFieldsValue(data?.info);
          setdataEdit(data?.info?.id);

          if (!data.nhomDichVuCap1Id) {
            getNhomDichVuCap1({
              path: "/nhom-dich-vu-cap1",
              loaiDichVu: 20,
            }).then((s) => {
              if (s && s?.payload) {
                let xetNghiem = s.payload.nhomDichVuCap1;
                if (xetNghiem) {
                  form.setFieldsValue({
                    tenNhomDichVuCap1: xetNghiem.ten,
                    nhomDichVuCap1Id: xetNghiem.id,
                  });
                  setState({ nhomDichVuCap1Id: xetNghiem.id });
                  getAllDichVuCap2({
                    page: "",
                    size: "",
                    nhomDichVuCap1Id: xetNghiem.id,
                  }).then((res) => {
                    let listNhomDvCap2 = res?.payload?.listAllNhomDichVuCap2;
                    getAllTongHopDichVuCap3({
                      page: "",
                      size: "",
                      active: true,
                      dsNhomDichVuCap2Id: listNhomDvCap2?.map((i) => i.id),
                    });
                  });
                }
              } else {
                getAllDichVuCap2({
                  page: "",
                  size: "",
                }).then((res) => {
                  let listNhomDvCap2 = res?.payload?.listAllNhomDichVuCap2;
                  getAllTongHopDichVuCap3({
                    page: "",
                    size: "",
                    active: true,
                    dsNhomDichVuCap2Id: listNhomDvCap2?.map((i) => i.id),
                  });
                });
              }
            });
          } else {
            form.setFieldsValue({
              tenNhomDichVuCap1: data?.nhomDichVuCap1?.ten,
            });
            getAllDichVuCap2({
              page: "",
              size: "",
              nhomDichVuCap1Id: data.nhomDichVuCap1Id,
            }).then((res) => {
              let listNhomDvCap2 = res?.payload?.listAllNhomDichVuCap2;
              getAllTongHopDichVuCap3({
                page: "",
                size: "",
                active: true,
                dsNhomDichVuCap2Id: listNhomDvCap2?.map((i) => i.id),
              });
            });
          }
        } else {
          form.resetFields();
          setdataEdit(null);
        }
      },
      resetFields: (data) => {
        form.resetFields();
        setdataEdit(null);

        getNhomDichVuCap1({
          path: "/nhom-dich-vu-cap1",
          loaiDichVu: 20,
        }).then((s) => {
          if (s && s?.payload) {
            let xetNghiem = s.payload.nhomDichVuCap1;
            form.setFieldsValue({
              tenNhomDichVuCap1: xetNghiem.ten,
            });
            setState({ nhomDichVuCap1Id: xetNghiem?.id });
            getAllDichVuCap2({
              page: "",
              size: "",
              nhomDichVuCap1Id: xetNghiem.id,
            }).then((res) => {
              let listNhomDvCap2 = res?.payload?.listAllNhomDichVuCap2;
              getAllTongHopDichVuCap3({
                page: "",
                size: "",
                active: true,
                dsNhomDichVuCap2Id: listNhomDvCap2?.map((i) => i.id),
              });
            });
          } else {
            getAllDichVuCap2({
              page: "",
              size: "",
            }).then((res) => {
              let listNhomDvCap2 = res?.payload?.listAllNhomDichVuCap2;
              getAllTongHopDichVuCap3({
                page: "",
                size: "",
                active: true,
                dsNhomDichVuCap2Id: listNhomDvCap2?.map((i) => i.id),
              });
            });
          }
        });
      },
    }),
    []
  );

  const handleAddNew = () => {
    form
      .validateFields()
      .then((values) => {
        const { dsDichVuId, ...rest } = values;
        handleSubmit({
          ...rest,
          dsDichVuId: safeConvertToArray(dsDichVuId),
        });
      })
      .catch(() => {});
  };

  const onRequireKhoaChiDinh = (e) => {
    let checkKhoaChiDinh = (e || []).find((x) =>
      [DOI_TUONG_KCB.DIEU_TRI_NGOAI_TRU, DOI_TUONG_KCB.NGOAI_TRU].includes(x)
    );
    if (checkKhoaChiDinh) {
      setState({ isRequireKhoaChiDinh: true });
    } else {
      setState({ isRequireKhoaChiDinh: false });
    }
  };

  return (
    <CreatedWrapper
      title={t("danhMuc.thongTinChiTiet")}
      onCancel={onCancel}
      cancelText={t("common.huy")}
      onOk={handleAddNew}
      okText={t("common.luuF4")}
      roleSave={[ROLES["DANH_MUC"].NOI_LAY_BENH_PHAM_THEM]}
      roleEdit={[ROLES["DANH_MUC"].NOI_LAY_BENH_PHAM_SUA]}
      editStatus={editStatus}
    >
      <fieldset
        disabled={
          !checkRole([ROLES["DANH_MUC"].NOI_LAY_BENH_PHAM_THEM]) && !editStatus
        }
      >
        <Form
          ref={formRef}
          form={form}
          layout="vertical"
          style={{ width: "100%" }}
          className="form-custom"
        >
          <Form.Item
            label={
              <div
                className="pointer"
                onClick={() => openInNewTab("/danh-muc/khoa")}
              >
                {t("danhMuc.doiTuongKCB")}
              </div>
            }
            name="dsDoiTuongKcb"
          >
            <Select
              data={listdoiTuongKcb}
              placeholder={t("common.chonDoiTuongKCB")}
              onChange={onRequireKhoaChiDinh}
              mode="multiple"
              showArrow
            />
          </Form.Item>
          <Form.Item
            label={
              <div
                className="pointer"
                onClick={() => openInNewTab("/danh-muc/toa-nha")}
              >
                {t("danhMuc.nhaChiDinh")}
              </div>
            }
            name="nhaChiDinhId"
          >
            <Select
              data={listAllToaNha}
              placeholder={t("danhMuc.chonNhaChiDinh")}
            />
          </Form.Item>
          <Form.Item
            label={
              <div
                className="pointer"
                onClick={() => openInNewTab("/danh-muc/khoa")}
              >
                {t("danhMuc.khoaChiDinh")}
              </div>
            }
            name="khoaChiDinhId"
            rules={[
              {
                required: state?.isRequireKhoaChiDinh,
                message: t("danhMuc.vuiLongNhapKhoaChiDinh"),
              },
            ]}
          >
            <Select
              data={listAllKhoa}
              placeholder={t("danhMuc.chonKhoaChiDinh")}
              onChange={(khoaId, list) => {
                form.setFieldsValue({
                  nhaChiDinhId: list?.toaNhaId,
                  phongChiDinhId: null,
                });
                getListAllPhong(
                  {
                    ...params,
                    ...(khoaId ? { khoaId } : {}),
                  },
                  { storeKey: "listAllPhongTheoKhoa" }
                );
              }}
            />
          </Form.Item>
          <Form.Item label={t("baoCao.phongChiDinh")} name="phongChiDinhId">
            <Select
              className="input-option"
              placeholder={t("baoCao.chonPhongChiDinh")}
              data={listAllPhongTheoKhoa}
            />
          </Form.Item>
          <Form.Item
            label={
              <div
                className="pointer"
                onClick={() => openInNewTab("/danh-muc/nhom-dich-vu?level=1")}
              >
                {t("danhMuc.nhomDVCap1")}
              </div>
            }
            name="tenNhomDichVuCap1"
            rules={[
              {
                required: true,
                message: t("danhMuc.vuiLongNhapNhomChiDinhCap1"),
              },
            ]}
          >
            <Input
              className="input-option"
              value={nhomDichVuCap1?.ten}
              placeholder={t("danhMuc.nhapNhomDVCap1")}
              disabled
            />
          </Form.Item>
          <Form.Item
            label={
              <div
                className="pointer"
                onClick={() => openInNewTab("/danh-muc/nhom-dich-vu?level=2")}
              >
                {t("danhMuc.nhomDVCap2")}
              </div>
            }
            name="dsNhomDichVuCap2Id"
          >
            <Select
              mode="multiple"
              data={listAllNhomDichVuCap2}
              placeholder={t("danhMuc.chonNhomDVCap2")}
            />
          </Form.Item>
          <Form.Item
            label={
              <div
                className="pointer"
                onClick={() => openInNewTab("/danh-muc/nhom-dich-vu?level=3")}
              >
                {t("danhMuc.nhomDVCap3")}
              </div>
            }
            name="dsNhomDichVuCap3Id"
          >
            <Select
              mode="multiple"
              data={listAllNhomDichVuCap3}
              placeholder={t("danhMuc.chonNhomDVCap3")}
            />
          </Form.Item>
          <Form.Item label={t("common.tenDichVu")} name="dsDichVuId">
            <Select
              className="input-option"
              placeholder={t("baoCao.chonDichVu")}
              data={listAllDichVu}
              getLabel={selectMaTen}
            />
          </Form.Item>
          <Form.Item
            label={
              <div
                className="pointer"
                onClick={() => openInNewTab("/danh-muc/phong")}
              >
                {t("danhMuc.phongLayMau")}
              </div>
            }
            name="phongLayMauId"
            rules={[
              {
                required: true,
                message: t("danhMuc.vuiLongChonPhongLayMau"),
              },
            ]}
          >
            <Select
              data={listAllPhong}
              placeholder={t("danhMuc.chonPhongLayMau")}
              onChange={(e, list) => {
                form.setFieldsValue({ diaDiem: list?.diaDiem });
              }}
            />
          </Form.Item>
          <Form.Item label={t("danhMuc.diaDiem")} name="diaDiem">
            <Input
              className="input-option"
              placeholder={t("danhMuc.nhapDiaDiem")}
              disabled
            />
          </Form.Item>
          <Form.Item label={t("common.loaiDoiTuong")} name="dsLoaiDoiTuongId">
            <Select
              className="input-option"
              placeholder={t("danhMuc.chonLoaiDoiTuong")}
              data={listAllLoaiDoiTuong}
              mode="multiple"
              onChange={(dsLoaiDoiTuongId) => {
                form.setFieldsValue({ loaiHinhThanhToanId: null });
                getListAllLoaiHinhThanhToan({
                  ...params,
                  ...(dsLoaiDoiTuongId ? { dsLoaiDoiTuongId } : {}),
                });
              }}
            />
          </Form.Item>
          <Form.Item
            label={t("baoCao.loaiHinhThanhToan")}
            name="loaiHinhThanhToanId"
          >
            <Select
              className="input-option"
              placeholder={t("baoCao.chonLoaiHinhThanhToan")}
              data={listAllLoaiHinhThanhToan}
            />
          </Form.Item>
          <Form.Item label={t("danhMuc.slHangDoi")} name="slBanLayBenhPham">
            <Input
              className="input-option"
              placeholder={t("danhMuc.nhapSlHangDoi")}
            />
          </Form.Item>
          <Form.Item label={t("thietLap.cauHinh")} name="thoiGianHoatDongId">
            <Select
              className="input-option"
              placeholder={t("thietLap.chonCauHinh")}
              data={listAllCauHinh}
            />
          </Form.Item>
          {editStatus && (
            <Form.Item name="active" valuePropName="checked">
              <Checkbox>{t("danhMuc.coHieuLuc")}</Checkbox>
            </Form.Item>
          )}
        </Form>
      </fieldset>
    </CreatedWrapper>
  );
};

export default forwardRef(FormNoiLayBenhPham);
