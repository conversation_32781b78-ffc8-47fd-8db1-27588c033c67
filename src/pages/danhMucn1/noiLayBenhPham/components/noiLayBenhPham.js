import React, { useEffect, useRef, useState } from "react";
import { SVG } from "assets";
import {
  Checkbox,
  Pagination,
  TableWrapper,
  Select,
  HeaderSearch,
  Button,
  Dropdown,
} from "components";
import { useTranslation } from "react-i18next";
import { useEnum, useListAll, useLoading, useStore } from "hooks";
import { ENUM, HIEU_LUC, HOTKEY, LOAI_PHONG } from "constants/index";
import { useDispatch } from "react-redux";
import { isArray, combineSort } from "utils/index";
import { Menu } from "antd";

let timer = null;

const { ColumnSelect, ColumnInput, Column, ModalImport } = TableWrapper;
const params = { page: "", size: "", active: true };

const NoiLayBenhPham = ({
  getListNoiLayBenhPham,
  handleChangeshowTable,
  showFullTable,
  collapseStatus,
  handleCollapsePane,
  setEditStatus,
  onReset,
  layerId,
  total,
  page,
  size,
  dataSearch,
  updateData,
  onEditNoiLayBenhPham,
  listNoiLayBenhPham,
  sortData,
  onPageChange,
  onSizeChange,
}) => {
  const { t } = useTranslation();
  const { showLoading, hideLoading } = useLoading();

  const [dataEditDefault, setDataEditDefault] = useState(null);

  const refModalImport = useRef(null);
  const refSelectRow = useRef();

  const { onRegisterHotkey } = useDispatch().phimTat;

  const [listAllKhoa] = useListAll("khoa", {}, true);
  const [listAllToaNha] = useListAll("toaNha", {}, true);
  const [listAllLoaiDoiTuong] = useListAll("loaiDoiTuong", {}, true);
  const [listAllLoaiHinhThanhToan] = useListAll("loaiHinhThanhToan", {}, true);

  const [listdoiTuongKcb] = useEnum(ENUM.DOI_TUONG_KCB);
  const [listAllPhong] = useListAll(
    "phong",
    {
      loaiPhong: LOAI_PHONG.LAY_MAU_BENH_PHAM,
      sort: combineSort({ active: 2, ten: 1 }),
    },
    true
  );

  const listAllCauHinh = useStore("thietLapCauHinh.listAllCauHinh", []);
  const listNhomDvCap2 = useStore("nhomDichVuCap2.listGroupService2", []);
  const listNhomDvCap3 = useStore("nhomDichVuCap3.listGroupService3", []);
  const [listAllDichVu] = useListAll("dichVu", {}, true);
  const {
    phong: { getListAllPhong },
    loaiHinhThanhToan: { getListAllLoaiHinhThanhToan },
    noiLayBenhPham: { onImport, onExport },
  } = useDispatch();

  // register layerId
  useEffect(() => {
    onRegisterHotkey({
      layerId,
      hotKeys: [
        {
          keyCode: HOTKEY.UP, //up
          onEvent: (e) => {
            refSelectRow.current && refSelectRow.current(-1);
          },
        },
        {
          keyCode: HOTKEY.DOWN, //DOWN
          onEvent: (e) => {
            refSelectRow.current && refSelectRow.current(1);
          },
        },
      ],
    });
    return () => {
      onRemoveLayer({ layerId });
    };
  }, []);

  useEffect(() => {
    getListNoiLayBenhPham({
      page: 0,
      size: 10,
      sort: combineSort(sortData),
    });
  }, []);

  const onSearchInput = (key) => (e) => {
    let value = "";
    if (e?.target) {
      if (e.target.hasOwnProperty("checked")) value = e.target.checked;
      else value = e.target.value;
    } else if (e?._d) value = e.format("YYYY-MM-DD");
    else value = e;

    clearTimeout(timer);
    timer = setTimeout(() => {
      updateData({
        dataSearch: { ...dataSearch, [key]: value },
      });
      getListNoiLayBenhPham({
        ...dataSearch,
        page: 0,
        size: size,
        [key]: value,
        sort: combineSort(sortData),
      });
    }, 500);
  };

  const columns = [
    Column({
      title: t("common.stt"),
      width: "50px",
      dataIndex: "stt",
      key: "stt",
      align: "center",
    }),
    Column({
      title: t("thietLap.cauHinh"),
      dataIndex: "thoiGianHoatDongId",
      width: 100,
      render: (item) => {
        return (listAllCauHinh || []).find((x) => x.id === item)?.ten || "";
      },
    }),
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.doiTuongKCB")}
          searchSelect={
            <Select
              data={listdoiTuongKcb}
              placeholder={t("common.chonDoiTuongKCB")}
              mode="multiple"
              onChange={onSearchInput("dsDoiTuongKcb")}
            />
          }
        />
      ),
      width: 150,
      dataIndex: "dsDoiTuongKcb",
      key: "dsDoiTuongKcb",
      render: (item) => {
        const ten = item?.length
          ? (listdoiTuongKcb || [])
              .filter((x) => item.includes(x?.id))
              .map((x1) => x1?.ten)
              .join(",")
          : "";
        return <span>{ten}</span>;
      },
    },
    ColumnSelect({
      title: t("danhMuc.khoaChiDinh"),
      dataIndex: "khoaChiDinh",
      sort_key: "khoaChiDinhId",
      searchKey: "khoaChiDinhId",
      dataSelect: listAllKhoa,
      hasAllOption: true,
      // onClickSort,
      // dataSortColumn,
      onSearchInput,
      width: 150,
      render: (item) => item?.ten,
    }),
    ColumnSelect({
      title: t("baoCao.phongChiDinh"),
      dataIndex: "phongChiDinh",
      sort_key: "phongChiDinhId",
      searchKey: "phongChiDinhId",
      dataSelect: listAllPhong,
      hasAllOption: true,
      // onClickSort,
      // dataSortColumn,
      onSearchInput,
      width: 180,
      render: (item) => item?.ten,
    }),
    ColumnSelect({
      title: t("danhMuc.nhaChiDinh"),
      dataIndex: "nhaChiDinh",
      sort_key: "nhaChiDinhId",
      searchKey: "nhaChiDinhId",
      dataSelect: listAllToaNha,
      hasAllOption: true,
      // onClickSort,
      // dataSortColumn,
      onSearchInput,
      width: 150,
      render: (item) => item?.ten,
    }),
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.nhomDVCap2")}
          searchSelect={
            <Select
              data={listNhomDvCap2}
              placeholder={t("danhMuc.chonNhomDVCap2")}
              defaultValue=""
              onChange={onSearchInput("nhomDichVuCap2Id")}
              hasAllOption={true}
            />
          }
        />
      ),
      width: 150,
      dataIndex: "dsNhomDichVuCap2",
      key: "dsNhomDichVuCap2",
      render: (item) => {
        const ten = item?.length ? item.map((ten) => ten.ten).join(",") : "";
        return item?.length ? <span>{ten}</span> : "";
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.nhomDVCap3")}
          searchSelect={
            <Select
              data={listNhomDvCap3}
              placeholder={t("danhMuc.chonNhomDVCap3")}
              defaultValue=""
              onChange={onSearchInput("nhomDichVuCap3Id")}
              hasAllOption={true}
            />
          }
        />
      ),
      width: 150,
      dataIndex: "dsNhomDichVuCap3",
      key: "dsNhomDichVuCap3",
      render: (item) => {
        const ten = item?.length ? item.map((ten) => ten.ten).join(",") : "";
        return item?.length ? <span>{ten}</span> : "";
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("common.tenDichVu")}
          searchSelect={
            <Select
              data={listAllDichVu}
              placeholder={t("baoCao.chonDichVu")}
              defaultValue=""
              onChange={onSearchInput("dichVuId")}
              hasAllOption={true}
            />
          }
        />
      ),
      width: 150,
      dataIndex: "dsDichVu",
      key: "dsDichVu",
      render: (item) => {
        const ten = item?.length ? item.map((ten) => ten.ten).join(",") : "";
        return item?.length ? <span>{ten}</span> : "";
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("danhMuc.phongLayMau")}
          sort_key="phongLayMauId"
          // onClickSort={onClickSort}
          // dataSort={dataSortColumn["phongLayMauId"] || 0}
          searchSelect={
            <Select
              data={listAllPhong}
              placeholder={t("danhMuc.chonPhongLayMau")}
              defaultValue=""
              onChange={onSearchInput("phongLayMauId")}
              hasAllOption={true}
            />
          }
        />
      ),
      width: 150,
      dataIndex: "phongLayMau",
      key: "phongLayMau",
      render: (item) => {
        return item && <span>{item?.ten}</span>;
      },
    },
    ColumnInput({
      title: t("danhMuc.diaDiem"),
      dataIndex: "diaDiem",
      // onClickSort,
      // dataSortColumn,
      onSearchInput,
    }),
    ColumnSelect({
      title: t("danhMuc.loaiDoiTuong"),
      dataIndex: "dsLoaiDoiTuong",
      sort_key: "dsLoaiDoiTuongId",
      searchKey: "dsLoaiDoiTuongId",
      dataSelect: listAllLoaiDoiTuong,
      hasAllOption: true,
      // onClickSort,
      // dataSortColumn,
      onSearchInput,
      width: 180,
      render: (item) => {
        if (isArray(item, true)) {
          return item.map((i) => i.ten).join(", ");
        }
        return null;
      },
    }),
    ColumnSelect({
      title: t("danhMuc.loaiHinhThanhToan"),
      dataIndex: "loaiHinhThanhToan",
      sort_key: "loaiHinhThanhToanId",
      searchKey: "loaiHinhThanhToanId",
      dataSelect: listAllLoaiHinhThanhToan,
      hasAllOption: true,
      // onClickSort,
      // dataSortColumn,
      onSearchInput,
      width: 150,
      render: (item) => item?.ten,
    }),
    Column({
      title: t("danhMuc.coHieuLuc"),
      dataIndex: "active",
      selectSearch: true,
      renderSearch: (
        <Select
          defaultValue=""
          data={HIEU_LUC}
          placeholder={t("danhMuc.chonHieuLuc")}
          onChange={(value) => {
            onSearchInput(value, "active");
          }}
          hasAllOption={true}
        />
      ),
      width: 90,
      align: "center",
      render: (item) => {
        return <Checkbox checked={item} onClick={() => {}} />;
      },
    }),
  ];

  const onRow = (record) => ({
    onClick: () => {
      setEditStatus(true);
      setDataEditDefault(record.action);
      onEditNoiLayBenhPham(record.action);

      getListAllPhong(
        {
          ...params,
          ...(record?.khoaChiDinhId ? { khoaId: record?.khoaChiDinhId } : {}),
        },
        { storeKey: "listAllPhongTheoKhoa" }
      );
      getListAllLoaiHinhThanhToan({
        ...params,
        ...(record?.dsLoaiDoiTuongId
          ? { dsLoaiDoiTuongId: record?.dsLoaiDoiTuongId }
          : {}),
      });
    },
  });

  const setRowClassName = (record) => {
    let idDiff = dataEditDefault?.id;
    return record.id === idDiff
      ? "row-actived row-id-" + record.id
      : "row-id-" + record.id;
  };

  const data = listNoiLayBenhPham.map((item, index) => {
    return {
      ...item,
      action: item,
      stt: page * size + index + 1,
    };
  });

  const onClickImport = () => {
    refModalImport.current &&
      refModalImport.current.show({ isModalVisible: true });
  };
  const onClickExport = () => {
    if (onExport) {
      showLoading();
      onExport().finally(() => hideLoading());
    }
  };

  const menu = () => (
    <Menu
      items={[
        {
          key: 1,
          label: (
            <a onClick={onClickImport}>
              <div
                className="flex icon_utilities gap-8"
                style={{ alignItems: "center" }}
              >
                <SVG.IcUpload />
                <span>
                  {t("danhMuc.nhapDuLieuTitle", {
                    title: t("danhMuc.noiLayMauBenhPham"),
                  })}
                </span>
              </div>
            </a>
          ),
        },
        {
          key: 2,
          label: (
            <a onClick={onClickExport}>
              <div className="flex icon_utilities gap-8">
                <SVG.IcDownload />
                <span>
                  {t("danhMuc.xuatDuLieuTitle", {
                    title: t("danhMuc.noiLayMauBenhPham"),
                  })}
                </span>
              </div>
            </a>
          ),
        },
      ]}
    />
  );

  return (
    <div>
      <TableWrapper
        classNameRow={"custom-header phong-thuc-hien"}
        styleMain={{ marginTop: 0 }}
        styleContainerButtonHeader={{
          display: "flex",
          width: "100%",
          justifyContent: "flex-end",
          alignItems: "center",
          paddingRight: 35,
        }}
        buttonHeader={[
          {
            content: (
              <>
                <Select
                  value={dataSearch?.thoiGianHoatDongId || ""}
                  data={[
                    { id: "", ten: t("thietLap.tatCaCauHinh") },
                    ...listAllCauHinh,
                  ]}
                  placeholder={t("thietLap.chonCauHinh")}
                  onChange={onSearchInput("thoiGianHoatDongId")}
                />
              </>
            ),
          },
          {
            content: (
              <Dropdown
                overlay={menu}
                trigger={"click"}
                placement="bottomLeft"
                overlayClassName="danh-muc-dropdown-tien-ich"
              >
                <Button rightIcon={<SVG.IcMore />} height={28}>
                  {t("common.tienIch")}
                </Button>
              </Dropdown>
            ),
          },
          {
            content: (
              <Button
                type="success"
                onClick={onReset}
                rightIcon={<SVG.IcAdd />}
              >
                {t("common.themMoiF1")}
              </Button>
            ),
          },
          {
            className: `btn-change-full-table ${
              showFullTable ? "small" : "large"
            }`,
            title: showFullTable ? <SVG.IcShowThuNho /> : <SVG.IcShowFull />,
            onClick: handleChangeshowTable,
          },

          {
            className: "btn-collapse",
            title: collapseStatus ? <SVG.IcExtend /> : <SVG.IcCollapse />,
            onClick: handleCollapsePane,
          },
        ]}
        columns={columns}
        dataSource={data}
        onRow={onRow}
        rowClassName={setRowClassName}
      />
      {!!total && (
        <Pagination
          onChange={onPageChange}
          current={page + 1}
          pageSize={size}
          total={total}
          listData={data}
          onShowSizeChange={onSizeChange}
        />
      )}

      <ModalImport ref={refModalImport} onImport={onImport} />
    </div>
  );
};

export default NoiLayBenhPham;
