import React, { useMemo, useRef } from "react";
import { Popover, Button } from "components";
import { useHistory } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import ModalTaoPhieuLinh from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/PhieuLinh/ModalTaoPhieuLinh";
import ModalTaoPhieuTra from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/PhieuTra/ModalTaoPhieuTra";
import ModalTaoPhieuSuatAn from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/PhieuLinhSuatAn/ModalTaoPhieu";
import ModalTaoPhieuTraSuatAn from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/PhieuTraSuatAn/ModalTaoPhieu";
import { useStore } from "hooks";
import { WrapperPopover } from "./styled";
import { useTranslation } from "react-i18next";
import ModalSuaSoLuongLe from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/PhieuLinh/ModalSuaSoLuongLe";
import { SVG } from "assets";

const ButtonTaoPhieuLinhTra = ({ khoaLamViec }) => {
  const history = useHistory();
  const { t } = useTranslation();

  const refModalPhieuLinh = useRef();
  const refModalPhieuTra = useRef();
  const refModalTaoPhieuSuatAn = useRef();
  const refModalTaoPhieuTraSuatAn = useRef();
  const refSuaSoLuongLe = useRef();

  const { auth } = useSelector((state) => state.auth);

  const listThietLapChonKhoTongHop = useStore(
    "thietLapChonKho.listThietLapChonKhoTongHop",
    []
  );

  const {
    thietLapChonKho: { getListThietLapChonKhoTongHop },
  } = useDispatch();

  const initState = useMemo(
    () => ({
      loaiDichVu: 90,
      noiTru: true,
      tuTruc: false,
      active: true,
      page: "",
      size: "",
      loaiNhapXuat: 85,
      loaiTra: 70,
      khoDoiUngId: null,
      khoId:
        listThietLapChonKhoTongHop.length === 1
          ? listThietLapChonKhoTongHop[0]?.id
          : null,
    }),
    []
  );

  const clickTaoPhieuLinh = (e) => {
    if (
      refModalPhieuLinh.current &&
      (["svg", "SPAN"].every((i) => i !== e?.target?.nodeName) ||
        e?.target?.className == "label-btn")
    ) {
      getListThietLapChonKhoTongHop({
        ...initState,
        dsKhoaChiDinhId:
          auth?.dsKhoaPhuTrachId.length < 2
            ? auth?.dsKhoaPhuTrachId[0]
            : khoaLamViec?.id,
      });
      refModalPhieuLinh.current.show(
        {},
        () => {},
        (data) => {
          if (data?.dsDichVu.length == 1 && data?.code != 1032) {
            history.push(
              `/quan-ly-noi-tru/chi-tiet-phieu-linh/${data?.dsDichVu[0]?.phieuNhapXuatId}`
            );
          } else {
            refSuaSoLuongLe.current && refSuaSoLuongLe.current.show(data);
          }
        }
      );
    }
  };

  const clickTaoPhieuTra = (e) => {
    if (
      refModalPhieuTra.current &&
      (["svg", "SPAN"].every((i) => i !== e?.target?.nodeName) ||
        e?.target?.className == "label-btn")
    ) {
      getListThietLapChonKhoTongHop({
        ...initState,
        khoaChiDinhId:
          auth?.dsKhoaPhuTrachId.length < 2
            ? auth?.dsKhoaPhuTrachId[0]
            : khoaLamViec?.id,
        canLamSang: false,
      });
      refModalPhieuTra.current.show();
    }
  };

  const onClickDsNbTraDv = () => {
    history.push("/quan-ly-noi-tru/danh-sach-nguoi-benh-noi-tru-tra-dich-vu");
  };

  return (
    <>
      <Button
        className="btn_new"
        // type={"success"}
        onClick={clickTaoPhieuTra}
        rightIcon={
          <Popover
            trigger={"click"}
            content={
              <WrapperPopover width={250}>
                <div
                  className="item-popover"
                  onClick={() => {
                    history.push(
                      "/quan-ly-noi-tru/danh-sach-phieu-tra-suat-an"
                    );
                  }}
                >
                  {t("quanLyNoiTru.danhSachPhieuTraSuatAn")}
                </div>
                <div
                  className="item-popover"
                  onClick={() => {
                    history.push("/quan-ly-noi-tru/danh-sach-phieu-tra");
                  }}
                >
                  {t("quanLyNoiTru.danhSachPhieuTra")}
                </div>
                <div className="item-popover" onClick={clickTaoPhieuTra}>
                  {t("quanLyNoiTru.taoPhieuTra")}
                </div>
                <div className="item-popover" onClick={onClickDsNbTraDv}>
                  {t("quanLyNoiTru.danhSachNbChuaHoanTra")}
                </div>
              </WrapperPopover>
            }
          >
            <SVG.IcMore className={"icon-more"} />
          </Popover>
        }
        iconHeight={20}
      >
        <span className="label-btn">{t("quanLyNoiTru.taoPhieuTra")}</span>
      </Button>
      <Button
        className="btn_new"
        // type={"success"}
        onClick={clickTaoPhieuLinh}
        rightIcon={
          <Popover
            trigger={"click"}
            content={
              <WrapperPopover width={200}>
                <div
                  className="item-popover"
                  onClick={() => {
                    history.push(
                      "/quan-ly-noi-tru/danh-sach-phieu-linh-suat-an"
                    );
                  }}
                >
                  {t("quanLyNoiTru.danhSachPhieuLinhSuatAn")}
                </div>
                <div
                  className="item-popover"
                  onClick={() => {
                    history.push("/quan-ly-noi-tru/danh-sach-phieu-linh");
                  }}
                >
                  {t("quanLyNoiTru.danhSachPhieuLinh")}
                </div>
                <div className="item-popover" onClick={clickTaoPhieuLinh}>
                  {t("quanLyNoiTru.taoPhieuLinh")}
                </div>
                <div
                  className="item-popover"
                  onClick={() => {
                    history.push(
                      "/quan-ly-noi-tru/danh-sach-nb-chua-tao-duyet-hoan-tra"
                    );
                  }}
                >
                  {t("quanLyNoiTru.danhSachNbChuaTaoduyetLinhHangHoa")}
                </div>
                <div
                  className="item-popover"
                  onClick={() => {
                    history.push("/pha-che-thuoc/danh-sach-phieu-chot-pha-che");
                  }}
                >
                  {t("phaCheThuoc.taoDonPhaChe")}
                </div>
              </WrapperPopover>
            }
          >
            <SVG.IcMore className="icon-more" />
          </Popover>
        }
        iconHeight={20}
      >
        <span className="label-btn">{t("quanLyNoiTru.taoPhieuLinh")}</span>
      </Button>

      <ModalTaoPhieuLinh
        khoaLamViecId={khoaLamViec?.id}
        ref={refModalPhieuLinh}
        refModalTaoPhieuSuatAn={refModalTaoPhieuSuatAn}
        initState={initState}
      />
      <ModalTaoPhieuTra
        initState={initState}
        ref={refModalPhieuTra}
        khoaLamViec={khoaLamViec}
        refModalTaoPhieuTraSuatAn={refModalTaoPhieuTraSuatAn}
      />
      <ModalTaoPhieuSuatAn
        ref={refModalTaoPhieuSuatAn}
        khoaLamViecId={khoaLamViec?.id}
      />
      <ModalTaoPhieuTraSuatAn
        ref={refModalTaoPhieuTraSuatAn}
        khoaLamViecId={khoaLamViec?.id}
      />
      <ModalSuaSoLuongLe ref={refSuaSoLuongLe} />
    </>
  );
};

export default ButtonTaoPhieuLinhTra;
