import { message } from "antd";
import { ModalTemplate, Button } from "components";
import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useState,
  useRef,
} from "react";
import ElementFilter from "components/common/ElementFilter";
import { Main } from "./styled";
import { useTranslation } from "react-i18next";
import { SVG } from "assets";
import { useConfirm } from "hooks";

const ModalCreate = (
  {
    width,
    title,
    initState,
    renderFilter,
    onSubmit = () => {},
    afterSubmit = () => {},
  },
  ref
) => {
  const { showConfirm } = useConfirm();
  const { t } = useTranslation();

  const refModal = useRef(null);
  const refElementFilter = useRef();

  useImperativeHandle(ref, () => ({
    show: ({} = {}) => {
      setState({ show: true });
    },
    close: ({} = {}) => {
      setState({ show: false });
    },
  }));

  const [state, _setState] = useState({ show: false });
  const setState = (data) => {
    _setState((pre) => ({ ...pre, ...data }));
  };

  useEffect(() => {
    if (state.show) {
      refModal.current && refModal.current.show();
    } else {
      refModal.current && refModal.current.hide();
    }
  }, [state.show]);

  const onOk = (isOk) => () => {
    if (isOk) {
      refElementFilter.current &&
        refElementFilter.current.submit(async (data) => {
          try {
            setState({ isLoading: true });
            const res = await onSubmit(data);
            if (res && res.code === 0) {
              message.success(t("quanLyNoiTru.taoMoiPhieuThanhCong"));
              afterSubmit(res.data);
            } else if (res) {
              if (res.message) message.error(res.message);
              setState({ show: false });
            }
          } catch (error) {
            showConfirm(
              {
                title: t("common.canhBao"),
                content: error?.message,
                cancelText: t("common.xong"),
                showBtnOk: false,
              },
              () => {},
              () => {}
            );
          } finally {
            setState({ isLoading: false });
          }
        });
    } else {
      setState({
        show: false,
      });
    }
  };

  return (
    <ModalTemplate
      ref={refModal}
      width={width || 800}
      title={title || t("quanLyNoiTru.taoPhieuTra")}
      wrapClassName="modal-phieu-linh"
      onCancel={onOk(false)}
      actionRight={
        <>
          <Button
            type={"default"}
            onClick={onOk(false)}
            loading={state.isLoading}
            minWidth={100}
          >
            {t("common.huy")}
          </Button>
          <Button
            type={"primary"}
            onClick={onOk(true)}
            loading={state.isLoading}
            iconHeight={15}
            rightIcon={<SVG.IcSuccess />}
            minWidth={100}
          >
            {t("common.xacNhan")}
          </Button>
        </>
      }
    >
      <Main>
        {state.show && (
          <ElementFilter
            ref={refElementFilter}
            renderFilter={renderFilter}
            initState={initState}
          />
        )}
      </Main>
    </ModalTemplate>
  );
};

export default forwardRef(ModalCreate);
