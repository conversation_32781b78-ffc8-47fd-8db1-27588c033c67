import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from "react";
import { Form, Input, InputNumber } from "antd";
import { Main } from "./styled";
import { useDispatch } from "react-redux";
import { firstLetterWordUpperCase, getDsMoTa, getMoTaChanDoan } from "utils";
import {
  Button,
  Checkbox,
  ModalTemplate,
  Select,
  SelectLargeData,
} from "components";
import {
  DOI_TUONG,
  DOI_TUONG_KCB,
  DOI_TUONG_KCB_NOI_TRU,
  DS_TINH_CHAT_KHOA,
  ENUM,
  ROLES,
  THIET_LAP_CHUNG,
} from "constants/index";
import { useEnum, useStore, useThietLap, useLoading, useConfirm } from "hooks";
import { useTranslation } from "react-i18next";
import { SVG } from "assets";
import ModalTimKiemNB from "pages/khamBenh/components/ModalTimKiemNB";
import useIsKhoaDe from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/ThongTinVaoVien/hooks/useIsKhoaDe";
import CustomTag from "pages/khamBenh/KhamCoBan/ChanDoan/ChanDoanBenh/CustomTag";
import { checkRole } from "lib-utils/role-utils";

const { SelectChanDoan } = SelectLargeData;

const ModalLapBenhAn = (props, ref) => {
  const [state, _setState] = useState({});
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };
  const [, isKhoaDe] = useIsKhoaDe();

  const [form] = Form.useForm();
  const soConSinhLanNay = Form.useWatch("soConSinhLanNay", form);
  const { id, title } = props;
  const { t } = useTranslation();
  const refModal = useRef(null);
  const refModalTimKiemNB = useRef(null);
  const refCallback = useRef(null);
  const refBoQuaChuaThanhToan = useRef(false);
  const { showConfirm } = useConfirm();

  const listDataTongHop = useStore("khoa.listDataTongHop", []);
  const listKhoaTheoTaiKhoan = useStore("khoa.listKhoaTheoTaiKhoan", []);
  const listAllLoaiBenhAn = useStore("loaiBenhAn.listAllLoaiBenhAn", []);
  const thongTinCoBan = useStore("nbDotDieuTri.thongTinCoBan");
  const nbLapBenhAn = useStore("quanLyNoiTru.nbLapBenhAn", {});
  const nbThongTinKham = useStore("dieuTriDaiHan.nbThongTinKham", {});
  const [LOAI_DOI_TUONG_KBH_NOI_TRU_MAC_DINH] = useThietLap(
    THIET_LAP_CHUNG.LOAI_DOI_TUONG_KBH_NOI_TRU_MAC_DINH
  );
  const [LOAI_DOI_TUONG_BH_NOI_TRU_MAC_DINH, isLoadFinish] = useThietLap(
    THIET_LAP_CHUNG.LOAI_DOI_TUONG_BH_NOI_TRU_MAC_DINH
  );
  const [KHONG_HIEN_THI_DOI_TUONG_KCB, isLoadFinish2] = useThietLap(
    THIET_LAP_CHUNG.KHONG_HIEN_THI_DOI_TUONG_KCB
  );
  const [dataCHO_PHEP_NHAP_MO_TA_CHAN_DOAN] = useThietLap(
    THIET_LAP_CHUNG.CHO_PHEP_NHAP_MO_TA_CHAN_DOAN,
    "TRUE"
  );
  const [dataTU_DONG_DIEN_CD_MO_TA_THEO_CAC_CD] = useThietLap(
    THIET_LAP_CHUNG.TU_DONG_DIEN_CD_MO_TA_THEO_CAC_CD,
    "FALSE"
  );
  const [dataSINH_MA_BENH_NHAN_MOI_KHI_SUA_BA] = useThietLap(
    THIET_LAP_CHUNG.SINH_MA_BENH_NHAN_MOI_KHI_SUA_BA,
    "FALSE"
  );

  const listAllLoaiDoiTuong = useStore("loaiDoiTuong.listAllLoaiDoiTuong", []);
  const listAllNbDotDieuTri = useStore("nbDotDieuTri.listAllNbDotDieuTri", []);

  const {
    quanLyNoiTru: { postLapBenhAn, putLapBenhAn, getNbLapBenhAnById },
    dieuTriDaiHan: { postLapBenhAnDaiHan, putBenhAnDaiHan },
    loaiBenhAn: { getListAllLoaiBenhAn },
    khoa: { getListKhoaTongHop },
    loaiDoiTuong: { getListAllLoaiDoiTuong },
    nbDotDieuTri: { kiemTraSuaKhoaDieuTri, searchNBDotDieuTriTongHop },
  } = useDispatch();

  const [listdoiTuongKcb] = useEnum(ENUM.DOI_TUONG_KCB);
  const [listgioiTinh] = useEnum(ENUM.GIOI_TINH);
  const { showLoading, hideLoading } = useLoading();

  useImperativeHandle(ref, () => ({
    show: (isEdit = false, onOk) => {
      setState({ show: true, isEdit, isEditKhoaDieuTri: false });
      refCallback.current = onOk;
      refBoQuaChuaThanhToan.current = false;
    },
  }));
  useEffect(() => {
    if (state?.show) {
      refModal.current && refModal.current.show();
      getListAllLoaiDoiTuong({ active: true, page: "", size: "" });
    } else {
      refModal.current && refModal.current.hide();
    }
  }, [state?.show, nbLapBenhAn?.doiTuongKcb]);

  useEffect(() => {
    if (state.show && id && title) {
      getNbLapBenhAnById(id);
    }
  }, [state.show, id, title]);

  useEffect(() => {
    if (state.show && thongTinCoBan.nbThongTinId) {
      searchNBDotDieuTriTongHop({
        nbThongTinId: thongTinCoBan.nbThongTinId,
        hoSoDuSinh: true,
      });
    }
  }, [state.show, thongTinCoBan]);

  useEffect(() => {
    if (state.show) {
      const locBenhAnDaiHan = [
        DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_LINH_THUOC,
        DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_SU_DUNG_THUOC,
      ].includes(nbLapBenhAn?.doiTuongKcb);
      const locBenhAnKhongDaiHan = [
        DOI_TUONG_KCB.NGOAI_TRU,
        DOI_TUONG_KCB.DIEU_TRI_NGOAI_TRU,
        DOI_TUONG_KCB.DIEU_TRI_NOI_TRU,
      ].includes(nbLapBenhAn?.doiTuongKcb);
      const defaultParams = {
        page: "",
        size: "",
        active: true,
      };
      const paramKhoa = {
        ...defaultParams,
        dsTinhChatKhoa: locBenhAnDaiHan
          ? [DS_TINH_CHAT_KHOA.DIEU_TRI_NGOAI_TRU]
          : undefined,
      };
      const paramsLoaiBenhAn = {
        ...defaultParams,
        benhAnDaiHan: locBenhAnDaiHan
          ? true
          : locBenhAnKhongDaiHan
          ? false
          : undefined,
      };

      getListAllLoaiBenhAn(paramsLoaiBenhAn, {
        saveCache: false,
      });
      getListKhoaTongHop(paramKhoa);
    }
  }, [state.show, nbLapBenhAn?.doiTuongKcb]);

  useEffect(() => {
    if (listAllLoaiDoiTuong?.length && state?.show) {
      let loaiDoiTuong;
      if (thongTinCoBan.doiTuong === DOI_TUONG.BAO_HIEM) {
        if (
          ![
            DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_LINH_THUOC,
            DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_SU_DUNG_THUOC,
          ]?.includes(thongTinCoBan?.doiTuongKcb)
        ) {
          loaiDoiTuong = listAllLoaiDoiTuong.find(
            (x) => x.ma === LOAI_DOI_TUONG_BH_NOI_TRU_MAC_DINH
          );
        }
      } else {
        loaiDoiTuong = listAllLoaiDoiTuong.find(
          (x) => x.ma === LOAI_DOI_TUONG_KBH_NOI_TRU_MAC_DINH
        );
      }

      if (!loaiDoiTuong || state.isEdit) {
        form.setFieldsValue({ loaiDoiTuongId: thongTinCoBan.loaiDoiTuongId });
      } else {
        form.setFieldsValue({ loaiDoiTuongId: loaiDoiTuong.id });
      }
    }
  }, [
    LOAI_DOI_TUONG_KBH_NOI_TRU_MAC_DINH,
    listAllLoaiDoiTuong,
    thongTinCoBan,
    state?.show,
    LOAI_DOI_TUONG_BH_NOI_TRU_MAC_DINH,
    isLoadFinish,
    state.isEdit,
  ]);

  useEffect(() => {
    if (state.show) {
      if (isKhoaDe) {
        form.setFieldsValue({
          nbLienKetId: nbLapBenhAn.nbLienKetId,
        });
      } else {
        form.setFieldsValue({
          nbLienKetId: state.thongTinNbLienKet?.maNb,
        });
      }
    }
  }, [state.show, state.thongTinNbLienKet?.idNb, isKhoaDe, nbLapBenhAn]);

  useEffect(() => {
    if (id && state.isEdit && state.show) {
      kiemTraSuaKhoaDieuTri(id)
        .then((res) => {
          setState({ isEditKhoaDieuTri: !!res });
        })
        .catch(() => {
          setState({ isEditKhoaDieuTri: true });
        });
    } else {
      setState({ isEditKhoaDieuTri: true });
    }
  }, [id, state.isEdit, state.show]);

  const gioiTinh =
    listgioiTinh.find((item) => item.id === thongTinCoBan?.gioiTinh) || {};

  const danhSachKhongHienLoaiBenhAn = useMemo(() => {
    if (!isLoadFinish2) return [];
    const raw = KHONG_HIEN_THI_DOI_TUONG_KCB?.toString().trim();

    if (!raw || raw.toLowerCase() === "false") return [];

    return raw
      .split(",")
      .map((s) => s.trim()) // loại bỏ khoảng trắng
      .filter((s) => /^\d+$/.test(s)) // chỉ giữ chuỗi toàn số
      .map((s) => parseInt(s, 10)); // parse thành số nguyên
  }, [isLoadFinish2, KHONG_HIEN_THI_DOI_TUONG_KCB]);

  const listDoiTuongKhamChuaBenh = useMemo(() => {
    if (
      [
        DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_LINH_THUOC,
        DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_SU_DUNG_THUOC,
      ].includes(nbLapBenhAn?.doiTuongKcb)
    ) {
      return listdoiTuongKcb.filter((item) =>
        [
          DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_LINH_THUOC,
          DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_SU_DUNG_THUOC,
        ].includes(item.id)
      );
    }
    return listdoiTuongKcb.filter(
      (item) =>
        [
          DOI_TUONG_KCB.DIEU_TRI_NGOAI_TRU,
          DOI_TUONG_KCB.DIEU_TRI_NOI_TRU,
          DOI_TUONG_KCB.DIEU_TRI_NOI_TRU_BAN_NGAY,
        ].includes(item.id) && !danhSachKhongHienLoaiBenhAn.includes(item.id)
    );
  }, [listdoiTuongKcb, nbLapBenhAn?.doiTuongKcb]);

  useEffect(() => {
    if (nbLapBenhAn) {
      let loaiBenhAnId = nbLapBenhAn?.loaiBenhAnId;
      let doiTuongKcb =
        state?.isEdit ||
        [
          DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_LINH_THUOC,
          DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_SU_DUNG_THUOC,
        ].includes(nbLapBenhAn?.doiTuongKcb)
          ? nbLapBenhAn?.doiTuongKcb
          : DOI_TUONG_KCB_NOI_TRU.DIEU_TRI_NOI_TRU;

      let khoaNhapVienId = [
        DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_LINH_THUOC,
        DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_SU_DUNG_THUOC,
      ].includes(nbLapBenhAn?.doiTuongKcb)
        ? thongTinCoBan?.khoaNbId
        : nbLapBenhAn?.khoaNhapVienId;

      if (listDataTongHop && listAllLoaiBenhAn && !state?.isEdit) {
        const selectedKhoa = (listDataTongHop || []).find(
          (x) => x?.id == nbLapBenhAn?.khoaNhapVienId
        );
        if (selectedKhoa) {
          const selectedLoaiBenhAn = (listAllLoaiBenhAn || []).find(
            (x) => x?.ten == selectedKhoa?.tenLoaiBenhAn
          );
          loaiBenhAnId = selectedLoaiBenhAn?.id || null;
        }
      }
      let dsCdPhanBietId = [
        DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_LINH_THUOC,
        DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_SU_DUNG_THUOC,
      ].includes(nbLapBenhAn?.doiTuongKcb)
        ? null
        : nbLapBenhAn?.dsCdPhanBietId?.map(String);

      if (state?.show) {
        form.setFieldsValue({
          loaiBenhAnId: loaiBenhAnId,
          doiTuongKcb: doiTuongKcb,
          khoaNhapVienId: khoaNhapVienId,
          dsCdPhanBietId,
          soConSinhLanNay: nbLapBenhAn?.soConSinhLanNay,
          dsThongTinCon: (nbLapBenhAn?.dsThongTinCon || []).map(
            (item) => item.tenNb
          ),
          dsCdVaoVienId: nbLapBenhAn?.dsCdVaoVienId,
          dsCdVaoVienKemTheoId: nbLapBenhAn?.dsCdVaoVienKemTheoId,
          moTaVaoVien: nbLapBenhAn?.moTaVaoVien,
          dsCdYhctChinhId: nbLapBenhAn?.dsCdYhctChinhId?.map(String),
          dsCdYhctKemTheoId: nbLapBenhAn?.dsCdYhctKemTheoId?.map(String),
        });
        setState({
          dsMoTaChinh: getDsMoTa(nbLapBenhAn, "dsCdVaoVien", "dsCdChinh"),
          dsMoTaKemTheo: getDsMoTa(
            nbLapBenhAn,
            "dsCdVaoVienKemTheo",
            "dsCdKemTheo"
          ),
        });
      }
    }
  }, [
    nbLapBenhAn,
    listDataTongHop,
    listAllLoaiBenhAn,
    state?.show,
    thongTinCoBan,
  ]);

  const handleClickBack = () => {
    setState({ show: false, thongTinNbLienKet: {} });
    form.resetFields();
  };

  const onSave = () => {
    form.submit();
  };

  const handleClickMaNBLienKet = () => {
    refModalTimKiemNB.current &&
      refModalTimKiemNB.current.show(onHandleChoosePatient);
  };

  const onHandleChoosePatient = (values) => {
    setState({ thongTinNbLienKet: { ...values } });
  };

  const withLoading = async (fn) => {
    showLoading();
    try {
      return await fn();
    } finally {
      hideLoading();
    }
  };

  const handlePostLapBenhAn = async (payload) => {
    showLoading();
    try {
      const res = await postLapBenhAn(payload);

      if (res?.code === 7930) {
        hideLoading(); // phải tắt trước khi show confirm
        return new Promise((resolve) => {
          showConfirm(
            {
              title: t("common.thongBao"),
              cancelText: t("common.huy"),
              okText: t("common.dongY"),
              classNameOkText: "button-warning",
              showBtnOk: checkRole([
                ROLES["KHAM_BENH"].LAP_BENH_AN_BO_QUA_THANH_TOAN_DV,
              ]),
              typeModal: "warning",
              isContentElement: true,
              content: (
                <div className="flex flex-col gap-4 flex-center">
                  <div className="content content-2">{res.message}</div>
                  {checkRole([
                    ROLES["KHAM_BENH"].LAP_BENH_AN_BO_QUA_THANH_TOAN_DV,
                  ]) && (
                    <Checkbox
                      onChange={(e) =>
                        (refBoQuaChuaThanhToan.current = e.target.checked)
                      }
                    >
                      {t("quanLyNoiTru.lapBenhAnBoQuaThanhToanDichVu")}
                    </Checkbox>
                  )}
                </div>
              ),
            },
            async () => {
              const reRes = await handlePostLapBenhAn({
                ...payload,
                boQuaChuaThanhToan: refBoQuaChuaThanhToan.current,
              });
              resolve(reRes);
            }
          );
        });
      }

      return res;
    } finally {
      hideLoading();
    }
  };

  const onHandleSubmit = async (values) => {
    const { soConSinhLanNay, dsThongTinCon, ...restValues } = values;
    const payload = {
      ...restValues,
      moTaChanDoan: {
        dsCdChinh: state.dsMoTaChinh,
        dsCdKemTheo: state.dsMoTaKemTheo,
      },
      ...(values.nbLienKetId ? { loaiLienKet: 30 } : {}),
      id,
      ...(isKhoaDe && soConSinhLanNay
        ? {
            nbThongTinSanPhu: {
              soConSinhLanNay: soConSinhLanNay,
              dsThongTinCon: (dsThongTinCon || []).map((item, index) => ({
                conThu: index + 1,
                hoTen: item,
              })),
            },
          }
        : {}),
    };

    const isDieuTriDaiHan = [
      DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_LINH_THUOC,
      DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_SU_DUNG_THUOC,
    ].includes(nbLapBenhAn?.doiTuongKcb);

    const paramsBADaiHan = {
      doiTuongKcb: values.doiTuongKcb,
      khoaId: values.khoaNhapVienId,
      loaiBenhAnId: values.loaiBenhAnId,
      nbLienKetId: state.thongTinNbLienKet?.idNb,
      nbDotDieuTriId: id,
      nbDvKhamId: nbThongTinKham?.id,
    };
    const isChangeLoaiBA =
      state.isEdit &&
      nbLapBenhAn?.loaiBenhAnId &&
      nbLapBenhAn?.loaiBenhAnId !== values.loaiBenhAnId &&
      dataSINH_MA_BENH_NHAN_MOI_KHI_SUA_BA?.eval();

    const onSubmit = async (extraParams = {}) => {
      try {
        if (isDieuTriDaiHan) {
          await withLoading(() =>
            state.isEdit
              ? putBenhAnDaiHan(paramsBADaiHan)
              : postLapBenhAnDaiHan(paramsBADaiHan)
          );
        } else {
          if (state.isEdit) {
            await withLoading(() =>
              putLapBenhAn({ ...payload, ...extraParams })
            );
          } else {
            await handlePostLapBenhAn(payload);
          }
        }
        handleClickBack();
        refCallback.current && refCallback.current();
      } catch (error) {
        console.log("error", error);
      }
    };

    const confirmChangeLoaiBA = () => {
      const title1 =
        listAllLoaiBenhAn.find((x) => x.id === nbLapBenhAn?.loaiBenhAnId)
          ?.ten || "";
      const title2 =
        listAllLoaiBenhAn.find((x) => x.id === values.loaiBenhAnId)?.ten || "";

      showConfirm(
        {
          title: t("common.thongBao"),
          content: (
            <div style={{ textAlign: "center" }}>
              <div style={{ fontWeight: 600, marginBottom: 8 }}>
                {t("quanLyNoiTru.xacNhanThayDoiLoaiBenhAn")}
              </div>
              <div style={{ margin: "8px 0", fontWeight: 600 }}>
                <span style={{ color: "red" }}>{title1}</span> ➝{" "}
                <span style={{ color: "red" }}>{title2}</span>
              </div>
              <div style={{ fontStyle: "italic" }}>
                {t("quanLyNoiTru.seDuocCapMaBenhAnMoi")}
              </div>
            </div>
          ),
          cancelText: t("common.quayLai"),
          okText: t("common.dongY"),
          classNameOkText: "button-warning",
          showImg: true,
          showBtnOk: true,
          typeModal: "warning",
          isContentElement: true,
        },
        () => onSubmit({ sinhMaBenhAn: true })
      );
    };

    isChangeLoaiBA ? confirmChangeLoaiBA() : onSubmit();
  };

  const listKhoa = useMemo(() => {
    return (listDataTongHop || [])
      .sort((a, b) => a.ten.localeCompare(b.ten))
      .map((item) => {
        return { ...item, ten: `${item.ma} - ${item.ten}` };
      });
  }, [listDataTongHop]);

  const onChangeChanDoan = (type) => (value, record) => {
    let moTa;
    if (type == "dsCdChinhId") {
      let dsCd = null;
      if (record.length) {
        dsCd = [record[record.length - 1]];
      } else dsCd = [];

      const dsMoTaChinh = getDsMoTa(
        {
          dsCdChinh: dsCd,
          moTaChanDoan: {
            dsCdChinh: state.dsMoTaChinh,
          },
        },
        "dsCdChinh"
      );
      moTa = getMoTaChanDoan([dsMoTaChinh, state.dsMoTaKemTheo]);
      setState({
        dsMoTaChinh,
      });
    } else {
      const dsMoTaKemTheo = getDsMoTa(
        {
          dsCdKemTheo: record,
          moTaChanDoan: {
            dsCdKemTheo: state.dsMoTaKemTheo,
          },
        },
        "dsCdKemTheo"
      );
      moTa = getMoTaChanDoan([state.dsMoTaChinh, dsMoTaKemTheo]);
      setState({
        dsMoTaKemTheo,
      });
    }
    form.setFieldValue("moTaVaoVien", moTa);
  };
  const onChangeMoTa = (type) => (id, value) => {
    const dsMoTa = state[type] || [];
    const item = dsMoTa.find((item) => item?.id == id);
    if (!item) {
      dsMoTa.push({ id, moTa: value });
    } else {
      item.moTa = value;
    }
    const moTa = getMoTaChanDoan([state.dsMoTaChinh, state.dsMoTaKemTheo]);
    setState({ [type]: [...dsMoTa] });
    form.setFieldValue("moTaVaoVien", moTa);
  };

  return (
    <ModalTemplate
      width={640}
      ref={refModal}
      title={
        title ||
        (state.isEdit
          ? t("lapBenhAn.suaThongTin")
          : t("quanLyNoiTru.lapBenhAn.title"))
      }
      onCancel={handleClickBack}
      rightTitle={
        <>
          <span className="font-color">
            {firstLetterWordUpperCase(thongTinCoBan?.tenNb)}
          </span>
          {gioiTinh.ten && (
            <span className="normal-weight"> - {gioiTinh.ten} </span>
          )}

          {thongTinCoBan.tuoi && (
            <span className="normal-weight">
              - {thongTinCoBan?.tuoi} {t("common.tuoi")}
            </span>
          )}
        </>
      }
      actionLeft={<Button.QuayLai onClick={handleClickBack} />}
      actionRight={
        <Button
          type="primary"
          minWidth={100}
          rightIcon={<SVG.IcSave />}
          onClick={onSave}
        >
          {t("common.luu")}
        </Button>
      }
    >
      <Main>
        <Form
          form={form}
          layout="horizontal"
          autoComplete="off"
          onFinish={onHandleSubmit}
          labelAlign="left"
          labelCol={{ span: 24 }}
          wrapperCol={{ span: 24 }}
        >
          <Form.Item
            label={t("quanLyNoiTru.loaiBenhAn")}
            name="loaiBenhAnId"
            rules={[
              {
                required: "true",
                message: t("quanLyNoiTru.lapBenhAn.vuiLongChonLoaiBenhAn"),
              },
            ]}
          >
            <Select
              data={listAllLoaiBenhAn}
              placeholder={t("quanLyNoiTru.lapBenhAn.vuiLongChonLoaiBenhAn")}
            />
          </Form.Item>
          <Form.Item label={t("quanLyNoiTru.doiTuongKcb")} name="doiTuongKcb">
            <Select data={listDoiTuongKhamChuaBenh} />
          </Form.Item>
          <Form.Item
            label={t("khamBenh.khoaDieuTri")}
            name="khoaNhapVienId"
            rules={[
              {
                required: "true",
                message: t("lapBenhAn.vuiLongChonKhoaNhapVien"),
              },
            ]}
          >
            <Select
              disabled={!state.isEditKhoaDieuTri}
              data={listKhoa}
              placeholder={t("khamBenh.khoaDieuTri")}
            />
          </Form.Item>
          {![
            DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_LINH_THUOC,
            DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_SU_DUNG_THUOC,
          ].includes(nbLapBenhAn?.doiTuongKcb) && (
            <Form.Item label={t("tiepDon.loaiDoiTuong")} name="loaiDoiTuongId">
              <Select data={listAllLoaiDoiTuong} disabled />
            </Form.Item>
          )}
          {[
            DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_LINH_THUOC,
            DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_SU_DUNG_THUOC,
          ].includes(nbLapBenhAn?.doiTuongKcb) && (
            <Form.Item label={t("khamBenh.maNBLienKet")} name="nbLienKetId">
              <Input
                className="input-option"
                placeholder={t("dieuTriDaiHan.nhapMaNBLienKet")}
                onClick={handleClickMaNBLienKet}
              />
            </Form.Item>
          )}

          <Form.Item
            label={t("quanLyNoiTru.chanDoanVaoVien")}
            name="dsCdVaoVienId"
          >
            <SelectChanDoan
              placeholder={t("quanLyNoiTru.chonChanDoanVaoVien")}
              mode="multiple"
              maxItem={1}
              showArrow
              onChange={onChangeChanDoan("dsCdChinhId")}
              tagRender={CustomTag(
                onChangeMoTa("dsMoTaChinh"),
                state.dsMoTaChinh
              )}
            />
          </Form.Item>
          <Form.Item
            label={t("quanLyNoiTru.chanDoanKemTheo")}
            name="dsCdVaoVienKemTheoId"
          >
            <SelectChanDoan
              placeholder={t("quanLyNoiTru.chonChanDoanKemTheo")}
              mode="multiple"
              showArrow
              onChange={onChangeChanDoan("dsCdKemTheoId")}
              tagRender={CustomTag(
                onChangeMoTa("dsMoTaKemTheo"),
                state.dsMoTaKemTheo
              )}
            />
          </Form.Item>
          {![
            DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_LINH_THUOC,
            DOI_TUONG_KCB.DIEU_TRI_DAI_HAN_CO_KHAM_SU_DUNG_THUOC,
          ].includes(nbLapBenhAn?.doiTuongKcb) && (
            <Form.Item
              label={t("quanLyNoiTru.chanDoanPhanBiet")}
              name="dsCdPhanBietId"
            >
              <SelectChanDoan
                mode="multiple"
                placeholder={t("quanLyNoiTru.chonChanDoanPhanBiet")}
                showArrow
              />
            </Form.Item>
          )}
          <Form.Item
            label={t("khamBenh.chanDoan.moTaChiTiet")}
            name="moTaVaoVien"
          >
            <Input.TextArea
              placeholder={t("khamBenh.chanDoan.moTaChiTiet")}
              disabled={!dataCHO_PHEP_NHAP_MO_TA_CHAN_DOAN?.eval()}
            />
          </Form.Item>
          {listKhoaTheoTaiKhoan
            ?.find((x) => x.id == nbLapBenhAn?.khoaNbId)
            ?.dsTinhChatKhoa?.includes(DS_TINH_CHAT_KHOA.Y_HOC_CO_TRUYEN) && (
            <>
              <Form.Item
                label={t("quanLyNoiTru.chanDoanBenhYhct")}
                name="dsCdYhctChinhId"
              >
                <SelectChanDoan
                  placeholder={t("quanLyNoiTru.chonChanDoanBenhYhct")}
                  mode="multiple"
                  maxItem={1}
                  showArrow
                  isYhct={true}
                />
              </Form.Item>
              <Form.Item
                label={t("quanLyNoiTru.chanDoanKemTheoYhct")}
                name="dsCdYhctKemTheoId"
              >
                <SelectChanDoan
                  placeholder={t("quanLyNoiTru.chonChanDoanKemTheoYhct")}
                  mode="multiple"
                  maxItem={1}
                  showArrow
                  isYhct={true}
                />
              </Form.Item>
            </>
          )}

          {isKhoaDe && (
            <>
              <Form.Item
                label={t("quanLyNoiTru.soConDuSinh")}
                name="soConSinhLanNay"
                labelCol={{ span: 8 }}
                wrapperCol={{ span: 16 }}
              >
                <InputNumber />
              </Form.Item>

              {Array.from({ length: soConSinhLanNay }, (_, i) => (
                <Form.Item
                  key={i}
                  label={t("quanLyNoiTru.tenDuKienDatCon", {
                    conThu: i + 1,
                  })}
                  name={["dsThongTinCon", i]}
                  labelCol={{ span: 8 }}
                  wrapperCol={{ span: 16 }}
                >
                  <Input />
                </Form.Item>
              ))}
              <Form.Item
                label={t("quanLyNoiTru.lienKetHoSoDuSinh")}
                name="nbLienKetId"
                labelCol={{ span: 8 }}
                wrapperCol={{ span: 16 }}
              >
                <Select
                  data={listAllNbDotDieuTri}
                  placeholder={t("quanLyNoiTru.lienKetHoSoDuSinh")}
                  ten="maHoSo"
                />
              </Form.Item>
            </>
          )}
        </Form>
      </Main>
      <ModalTimKiemNB ref={refModalTimKiemNB} />
    </ModalTemplate>
  );
};

export default forwardRef(ModalLapBenhAn);
