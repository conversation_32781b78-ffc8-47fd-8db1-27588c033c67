import React from 'react'
import { InputTimeout } from 'components'
import FieldItem from 'pages/quanLyNoiTru/dieuDuong/PhieuChamSocCap1CR/components/common/FieldItem'
import { Main } from './styled'
import { get } from 'lodash'

const GhiChu = ({ onChange, data, readOnly, ...props }) => {
    return (
        <Main>
            <FieldItem title={`Tiêu đề ghi chú`}>
                <InputTimeout
                    disabled={readOnly}
                    isTextArea={true}
                    className="flex1"
                    value={get(data, "ghiChu2")}
                    onChange={e => onChange(["ghiChu2"], e)}
                >
                </InputTimeout>
            </FieldItem>
            <FieldItem title={`Nội dung ghi chú`}>
                <InputTimeout
                    disabled={readOnly}
                    isTextArea={true}
                    className="flex1"
                    value={get(data, "ghiChu")}
                    onChange={e => onChange(["ghiChu"], e)}
                >
                </InputTimeout>
            </FieldItem>
        </Main >
    )
}
export default G<PERSON><PERSON>hu;