import React, { useEffect, useState, useRef } from "react";
import {
  Tooltip,
  Checkbox,
  Button,
  HeaderSearch,
  InputTimeout,
  Pagination,
  TableWrapper,
} from "components";
import moment from "moment";
import { message } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { Main } from "./styled";
import { useLoading, useStore, useThietLap } from "hooks";
import { useTranslation } from "react-i18next";
import { SVG } from "assets";
import { roundToDigits, isArray, openInNewTab } from "utils";
import { evalString } from "utils/chi-dinh-thuoc-utils";
import ModalCanhBaoCoSoTuTruc from "../ModalCanhBaoCoSoTuTruc/index";
import { uniqBy } from "lodash";
import { transformObjToQueryString } from "hooks/useQueryString/queryString";
import { LOAI_DICH_VU, THIET_LAP_CHUNG } from "constants/index";
import { getI18nKey } from "../";

const { Setting } = TableWrapper;

const DanhSachHangHoa = ({ id, isEdit }) => {
  const { t } = useTranslation();
  const { showLoading, hideLoading } = useLoading();
  const refSettings = useRef(null);
  const refModalCanhBaoCoSoTuTruc = useRef(null);
  const refSoLuong1Lan = useRef({});
  const refSoLan1Ngay = useRef({});
  const dataSearch = useSelector((state) => state.traHangHoa.dataSearch);
  const loaiHangHoa = useSelector((state) => state.traHangHoa.loaiHangHoa);
  const [dataDE_XUAT_TRA_HANG_HOA_LAM_TANG_CO_SO] = useThietLap(
    THIET_LAP_CHUNG.DE_XUAT_TRA_HANG_HOA_LAM_TANG_CO_SO
  );
  const [dataKHONG_NHAP_TRA_THUOC_TT_HTGN_CHUA_LINH] = useThietLap(
    THIET_LAP_CHUNG.KHONG_NHAP_TRA_THUOC_TT_HTGN_CHUA_LINH,
    ""
  );

  let _loaiDv = dataSearch?.loaiDichVu ?? loaiHangHoa;

  const listHangHoaTra = useStore("traHangHoa.listHangHoaTra", []);
  const dataSortColumn = useStore("traHangHoa.dataSortColumn", {});
  const {
    traHangHoa: {
      getListHangHoa,
      postDsDvThuocTraKho,
      putDsDvThuocTraKho,
      deleteDsDvThuocTraKho,
      postDsDvThuocTraKhoTatCa,
    },
  } = useDispatch();

  const [state, _setState] = useState({
    addNew: null,
    dataSource: [],
    newDataDsTra: [],
    editDataDsTra: [],
    currentIndex: { hs1: null, hs2: null },

    totalElements: 0,
    page: 0,
    size: 10,
  });

  const setState = (data) => {
    _setState((pre) => ({ ...pre, ...data }));
  };

  useEffect(() => {
    const fromIndex = state.page * state.size;
    const toIndex = Math.min(
      (state.page + 1) * state.size,
      listHangHoaTra.length
    );
    setState({
      dataSource: [...listHangHoaTra].slice(fromIndex, toIndex),
      totalElements: listHangHoaTra?.length || 0,
      page: state.page,
      size: state.size,
    });
  }, [listHangHoaTra, state.page, state.size]);

  const { dataSource, newDataDsTra, editDataDsTra } = state;

  const onChange =
    ({ key, hs1, hs2, data, list }) =>
    (val, { callback }) => {
      let value = /^[+]?\d+(\.\d+)?(\/\d+(\.\d+)?)?$/.test(val)
        ? +val || evalString(val)
        : 0;
      if (key === "soLuong1Lan") {
        refSoLuong1Lan.current[`${hs1}_${hs2}`] = val;
        refSoLan1Ngay.current[`${hs1}_${hs2}`] = null;
        value = list.soNgay * list.soLan1Ngay * val;
      } else if (key === "soLan1Ngay") {
        refSoLan1Ngay.current[`${hs1}_${hs2}`] = val;
        refSoLuong1Lan.current[`${hs1}_${hs2}`] = null;
        value = list.soNgay * list.soLuong1Lan * val;
      } else if (key === "soLuongTra") {
        refSoLan1Ngay.current[`${hs1}_${hs2}`] = null;
        refSoLuong1Lan.current[`${hs1}_${hs2}`] = null;
        dataSource[hs1].dsTra[hs2].soLuongYeuCauTra =
          Math.round((value / data.heSoDinhMuc) * 1000000) / 1000000;
      } else if (key === "soLuongYeuCauTra") {
        refSoLan1Ngay.current[`${hs1}_${hs2}`] = null;
        refSoLuong1Lan.current[`${hs1}_${hs2}`] = null;
        dataSource[hs1].dsTra[hs2].soLuongYeuCauTra = value;
        value = value * data.heSoDinhMuc;
      }
      dataSource[hs1].dsTra[hs2].soLuongTra = value || 0;
      let arrEditDataDsTra = [...editDataDsTra];
      if (data.id) {
        // edit
        const indexResult = editDataDsTra.findIndex(
          (itemDataDsTra) => itemDataDsTra.id === data.id
        );

        if (indexResult >= 0) {
          editDataDsTra[indexResult] = {
            ...editDataDsTra[indexResult],
            soLuongTra: value,
          };
          arrEditDataDsTra = [...editDataDsTra];
        } else {
          arrEditDataDsTra = [
            ...editDataDsTra,
            {
              ...data,
              hs1,
              hs2,
              key: `key${hs1}-${dataSource[hs2]?.dsTra.length - 1}`,
              soLuongTra: value,
            },
          ];
        }
      } else {
        // new
        const indexResult = newDataDsTra.findIndex(
          (itemDataDsTra) => itemDataDsTra.timestamp === data.timestamp
        );

        if (indexResult > -1) {
          newDataDsTra[indexResult] = {
            ...newDataDsTra[indexResult],
            soLuongTra: value,
          };
        }
      }
      callback(value || 0);
      setState({
        dataSource,
        newDataDsTra,
        editDataDsTra: arrEditDataDsTra,
      });
    };

  const onChangeLoaiTra =
    ({ hs1, hs2, data }) =>
    (e) => {
      const loaiTra = e.target.checked ? 10 : 0;

      let arrEditDataDsTra = [...editDataDsTra];
      dataSource[hs1].dsTra[hs2].loaiTra = loaiTra;
      if (data.id) {
        // edit
        const indexResult = editDataDsTra.findIndex(
          (itemDataDsTra) => itemDataDsTra.id === data.id
        );

        if (indexResult >= 0) {
          editDataDsTra[indexResult] = {
            ...editDataDsTra[indexResult],
            loaiTra,
          };
          arrEditDataDsTra = [...editDataDsTra];
        } else {
          arrEditDataDsTra = [
            ...editDataDsTra,
            {
              ...data,
              hs1,
              hs2,
              key: `key${hs1}-${dataSource[hs2]?.dsTra.length - 1}`,
              loaiTra,
            },
          ];
        }
      } else {
        // new
        const indexResult = newDataDsTra.findIndex(
          (itemDataDsTra) => itemDataDsTra.timestamp === data.timestamp
        );

        if (indexResult > -1) {
          newDataDsTra[indexResult] = {
            ...newDataDsTra[indexResult],
            loaiTra,
          };
        }
      }
      setState({
        dataSource,
        newDataDsTra,
        editDataDsTra: arrEditDataDsTra,
      });
    };

  useEffect(() => {
    if (state.addNew) {
      setTimeout(() => {
        onAddNewRow(state.addNew)();
        setState({ addNew: null });
      }, 500);
    }
  }, [dataSource, state.addNew]);

  const onAddNewRow =
    ({ item, index }) =>
    (e) => {
      const newData = {
        timestamp: moment().unix(),
        hs1: index,
        hs2: dataSource[index].dsTra.length - 1,
        nbDichVuId: item.id,
        key: `key${index}-${dataSource[index].dsTra.length - 1}`,
        heSoDinhMuc: item.heSoDinhMuc,
      };
      const prevItem = newDataDsTra.find((x) => x.nbDichVuId == item.id);
      if (prevItem) {
        if (!prevItem.soLuongTra) {
          message.error(t("quanLyNoiTru.vuiLongNhapSoLuongTra"));
          return;
        } else {
          setState({
            newDataDsTra: [],
          });

          postDsDvThuocTraKho(
            newDataDsTra.map((x) => ({
              nbDichVuId: x.nbDichVuId,
              soLuong: x.soLuongTra,
            }))
          ).then(() => {
            getListHangHoa({ nbDotDieuTriId: id, ...dataSearch }).then(() => {
              setState({ addNew: { item, index } });
            });
          });

          return;
        }
      }
      dataSource[index].dsTra.push(newData);
      const _newDataDsTra = [
        ...newDataDsTra,
        {
          timestamp: moment().unix(),
          hs1: index,
          hs2: dataSource[index].dsTra.length - 1,
          nbDichVuId: item.id,
          key: `key${index}-${dataSource[index].dsTra.length - 1}`,
        },
      ];
      setState({
        dataSource,
        // -1 index for current status
        newDataDsTra: _newDataDsTra,
        currentIndex: { hs1: index, hs2: dataSource[index].dsTra.length - 1 },
      });
    };

  const onRemoveItemDsTra =
    ({ hs1, hs2, data }) =>
    () => {
      if (data.id) {
        // note
        const indexResult1 = editDataDsTra.findIndex(
          (item) => item.id === data.id
        );
        if (indexResult1 >= 0) {
          editDataDsTra.splice(indexResult1, 1);
        } else {
          handleRemove(data.id);
        }
      } else {
        const indexResult1 = dataSource[hs1].dsTra.findIndex(
          (item) => item.timestamp === data.timestamp
        );
        const indexResult2 = newDataDsTra.findIndex(
          (item) => item.timestamp === data.timestamp
        );
        if (indexResult1 >= 0) dataSource[hs1].dsTra.splice(indexResult1, 1);
        if (indexResult2 >= 0) newDataDsTra.splice(indexResult2, 1);
      }

      setState({ dataSource, editDataDsTra, newDataDsTra });
    };

  const handleCheckVuotCoSo = (_data) => {
    let data = listHangHoaTra.filter((item) =>
      _data.some((i) => i.nbDichVuId === item.id)
    );
    data = data.reduce((acc, cur) => {
      const slTonSoCap = (cur.soLuongTon || 0) / (cur.heSoDinhMuc || 1);
      if (slTonSoCap - (cur.coSoSoCap || 0) > 0) {
        acc.push(cur);
      }
      return acc;
    }, []);
    data = uniqBy(data, "id");
    if (
      isArray(data, true) &&
      (dataDE_XUAT_TRA_HANG_HOA_LAM_TANG_CO_SO || "").toLowerCase() === "true"
    ) {
      refModalCanhBaoCoSoTuTruc.current &&
        refModalCanhBaoCoSoTuTruc.current.show(data, (rest) => {
          const { khoId, listData } = rest || {};
          let listSelect = listData.map((i) => i.dichVuId).join(",");
          let url = `/kho/xuat-kho/them-moi?type=30&khoId=${khoId}&from=noiTru&dichVuId=${listSelect}`;
          let params = transformObjToQueryString({
            nbDotDieuTriId: id,
            ...dataSearch,
          });
          params = params.replace("?", "");
          url += `&${params}`;
          openInNewTab(url);
        });
    }
  };

  const handleSubmit = async () => {
    showLoading();
    try {
      let listData = [];
      if (dataSearch?.traTatCa) {
        await postDsDvThuocTraKhoTatCa({
          nbDotDieuTriId: id,
          khoId: dataSearch.dsKhoId,
          dichVuId: null,
          tuThoiGianThucHien: dataSearch.tuThoiGianThucHien
            ? moment(dataSearch.tuThoiGianThucHien).format(
                "DD-MM-YYYY HH:mm:ss"
              )
            : undefined,
          denThoiGianThucHien: dataSearch.denThoiGianThucHien
            ? moment(dataSearch.denThoiGianThucHien).format(
                "DD-MM-YYYY HH:mm:ss"
              )
            : undefined,
        });
        setState({
          newDataDsTra: [],
          editDataDsTra: [],
        });
        listData = state.dataSource;
      } else {
        if (newDataDsTra.length) {
          listData = [...listData, ...newDataDsTra];
          await Promise.allSettled(
            newDataDsTra.map((itemDsTra) => {
              if (itemDsTra.nbDichVuId && itemDsTra.soLuongTra) {
                return new Promise((resolve, reject) => {
                  postDsDvThuocTraKho([
                    {
                      nbDichVuId: itemDsTra.nbDichVuId,
                      soLuong: itemDsTra.soLuongTra,
                      loaiTra: itemDsTra.loaiTra,
                    },
                  ])
                    .then((s) => {
                      if (s.code === 0) resolve(s?.data);
                      else reject(s?.data);
                      setState({
                        newDataDsTra: [],
                      });
                    })
                    .catch((e) => reject(e));
                });
              }
            })
          );
        }
        if (editDataDsTra.length) {
          listData = [...listData, ...editDataDsTra];
          await Promise.allSettled(
            editDataDsTra.map((itemDsTra) => {
              if (itemDsTra.nbDichVuId && itemDsTra.soLuongTra) {
                return new Promise((resolve, reject) => {
                  const {
                    id,
                    nbDotDieuTriId,
                    tenNb,
                    maNb,
                    maHoSo,
                    nbDichVuId,
                    khoaChiDinhId,
                    loaiDichVu,
                    thoiGianThucHien,
                    phieuLinhId,
                    khoId,
                    phieuTraId,
                    maDichVu,
                    tenDichVu,
                    soPhieuLinh,
                    soPhieuTra,
                    loaiTra,
                  } = itemDsTra;
                  putDsDvThuocTraKho([
                    {
                      id,
                      nbDotDieuTriId,
                      tenNb,
                      maNb,
                      maHoSo,
                      nbDichVuId,
                      khoaChiDinhId,
                      loaiDichVu,
                      khoId,
                      thoiGianThucHien,
                      phieuLinhId,
                      phieuTraId,
                      maDichVu,
                      tenDichVu,
                      soLuong: itemDsTra.soLuongTra,
                      soPhieuLinh,
                      soPhieuTra,
                      loaiTra,
                    },
                  ])
                    .then((s) => {
                      if (s.code === 0) resolve(s?.data);
                      else reject(s?.data);
                      setState({
                        editDataDsTra: [],
                      });
                    })
                    .catch((e) => reject(e));
                });
              }
            })
          );
        }
      }
      await getListHangHoa({ nbDotDieuTriId: id, ...dataSearch });
      hideLoading();
      handleCheckVuotCoSo(listData);
    } catch (error) {
      hideLoading();
    }
  };

  const handleRemove = (idData) => {
    showLoading();
    return new Promise((resolve, reject) => {
      deleteDsDvThuocTraKho([idData])
        .then((s) => {
          if (s.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e))
        .finally(async () => {
          await getListHangHoa({ nbDotDieuTriId: id, ...dataSearch });
          hideLoading();
        });
    });
  };

  const handleCancel = () => {
    setState({
      newDataDsTra: [],
      editDataDsTra: [],
    });
    getListHangHoa({ nbDotDieuTriId: id, ...dataSearch });
  };

  const onClickSort = (key, value) => {
    getListHangHoa({
      nbDotDieuTriId: id,
      ...dataSearch,
      dataSortColumn: { ...dataSortColumn, [key]: value },
    });
  };

  const columns = [
    {
      title: <HeaderSearch title={t("common.stt")} />,
      width: "50px",
      dataIndex: "index",
      key: "index",
      align: "center",
      ignore: true,
      render: (_, __, index) => state.page * state.size + index + 1,
    },
    {
      title: <HeaderSearch title={t("quanLyNoiTru.dvNoiTru.kho")} />,
      width: "160px",
      dataIndex: "tenKho",
      key: "tenKho",
      i18Name: "kho.kho",
      show: true,
      render: (item, list, index) => {
        return item;
      },
    },
    {
      title: <HeaderSearch title={t("kho.khoTaiKhoa")} />,
      width: "160px",
      dataIndex: "tenKhoTaiKhoa",
      key: "tenKhoTaiKhoa",
      i18Name: "kho.khoTaiKhoa",
      show: true,
    },
    {
      title: <HeaderSearch title={t("kho.tenHangHoa")} />,
      width: "160px",
      dataIndex: "tenDichVu",
      key: "tenDichVu",
      i18Name: "kho.tenHangHoa",
      className: "tenDichVu",
      show: true,
      render: (item, list, index) => item,
    },
    {
      title: <HeaderSearch title={t("kho.hoatChat")} />,
      width: "120px",
      key: "hoatChat",
      dataIndex: "tenHoatChat",
      i18Name: "kho.hoatChat",
      show: true,
    },
    {
      title: <HeaderSearch title={t(getI18nKey("lanTra", _loaiDv))} />,
      width: "100px",
      i18Name: getI18nKey("lanTra", _loaiDv),
      show: true,
      render: (item, list, index) => {
        //KHONG_NHAP_TRA_THUOC_TT_HTGN_CHUA_LINH = TRUE
        //Thuốc có Loại=20 (Thuốc tủ trực)
        //phieuLinhId= Null
        //loaiDonThuoc có value=20,30
        const hideAddBtn =
          dataKHONG_NHAP_TRA_THUOC_TT_HTGN_CHUA_LINH.toLowerCase() === "true" &&
          list?.loai === 20 &&
          list?.phieuLinhId === null &&
          [20, 30].includes(list?.loaiDonThuoc);
        return (
          <div className="cell">
            <div className="cell-total">
              <div className="cell-total-add">
                {!hideAddBtn && isEdit && (
                  <Tooltip title={t(getI18nKey("themLanTra", _loaiDv))}>
                    <Button
                      type="success"
                      iconHeight={15}
                      height={26}
                      onClick={onAddNewRow({ item, list, index })}
                      rightIcon={<SVG.IcAdd />}
                    >
                      {t("common.them")}
                    </Button>
                  </Tooltip>
                )}
              </div>
            </div>
            <div className="cell-list">
              {list.dsTra?.map((itm, idx) => (
                <div className={`cell-item cell-action`} key={itm.id}>
                  {!!itm.id ? (
                    <div>
                      {t("common.lan")} {idx + 1}
                    </div>
                  ) : (
                    <div></div>
                  )}
                  {isEdit && (
                    <Tooltip title={t("common.xoa")}>
                      <SVG.IcDelete
                        className="icon-action"
                        onClick={onRemoveItemDsTra({
                          hs1: index,
                          hs2: idx,
                          data: itm,
                        })}
                      />
                    </Tooltip>
                  )}
                </div>
              ))}
            </div>
          </div>
        );
      },
    },
    {
      title: <HeaderSearch title={t(getI18nKey("slYeuCauTra", _loaiDv))} />,
      width: "80px",
      i18Name: getI18nKey("slYeuCauTra", _loaiDv),
      show: true,
      render: (_, list, index) => (
        <div className="cell">
          <div className="cell-total">
            <div className="cell-total-number" style={{ minHeight: "24px" }}>
              {!!list.dsTra?.length && (
                <span>
                  {t("common.tong")}:{" "}
                  {roundToDigits(
                    list.dsTra?.reduce((a, b) => a + (+b.soLuongTra || 0), 0),
                    3
                  )}
                </span>
              )}
            </div>
          </div>
          <div className="cell-list">
            {list.dsTra?.map((itm, idx) => {
              return (
                <div key={itm.id} className={`cell-item `}>
                  <InputTimeout
                    controls={false}
                    disabled={!isEdit}
                    value={itm.soLuongTra}
                    onChange={onChange({
                      key: "soLuongTra",
                      hs1: index,
                      hs2: idx,
                      data: itm,
                    })}
                    className="cell-item-input"
                    autoFocus={
                      !!(
                        state.currentIndex.hs1 === index &&
                        state.currentIndex.hs2 === idx
                      )
                    }
                  />
                </div>
              );
            })}
          </div>
        </div>
      ),
    },
    {
      title: (
        <HeaderSearch title={t(getI18nKey("slSoCapYeuCauTra", _loaiDv))} />
      ),
      width: "80px",
      i18Name: getI18nKey("slSoCapYeuCauTra", _loaiDv),
      show: true,
      render: (_, list, index) => (
        <div className="cell">
          <div className="cell-total">
            <div className="cell-total-number" style={{ minHeight: "24px" }}>
              {!!list.dsTra?.length && (
                <span>
                  {t("common.tong")}:{" "}
                  {roundToDigits(
                    list.dsTra?.reduce(
                      (a, b) => a + (b.soLuongYeuCauTra || 0),
                      0
                    ),
                    3
                  )}
                </span>
              )}
            </div>
          </div>
          <div className="cell-list">
            {list.dsTra?.map((itm, idx) => {
              return (
                <div key={itm.id} className={`cell-item `}>
                  <InputTimeout
                    controls={false}
                    disabled={!isEdit}
                    value={itm.soLuongYeuCauTra}
                    onChange={onChange({
                      key: "soLuongYeuCauTra",
                      hs1: index,
                      hs2: idx,
                      data: itm,
                    })}
                    className="cell-item-input"
                    autoFocus={
                      !!(
                        state.currentIndex.hs1 === index &&
                        state.currentIndex.hs2 === idx
                      )
                    }
                  />
                </div>
              );
            })}
          </div>
        </div>
      ),
    },
    {
      title: <HeaderSearch title={t("quanLyNoiTru.dvNoiTru.dotXuat")} />,
      width: "60px",
      align: "right",
      key: "dotXuat",
      i18Name: "quanLyNoiTru.dvNoiTru.dotXuat",
      show: true,
      isShow: dataSearch.loaiHangHoa
        ? [
            LOAI_DICH_VU.THUOC,
            LOAI_DICH_VU.VAT_TU,
            LOAI_DICH_VU.CHE_PHAM_DINH_DUONG,
          ].includes(dataSearch.loaiHangHoa)
        : true, // mac dinh la thuoc
      render: (_, list, index) => {
        return (
          <div className="flex flex-col">
            <div style={{ height: 24 }} />
            <div className="flex flex-col ">
              {list.dsTra?.map((itm, idx) => {
                const soPhieuTra = dataSource[index]?.dsTra?.[idx]?.soPhieuTra;
                return (
                  <div
                    style={{ height: 32 }}
                    key={itm.id}
                    className="flex-center"
                  >
                    <Checkbox
                      disabled={!!soPhieuTra || !isEdit}
                      checked={itm.loaiTra === 10}
                      onChange={onChangeLoaiTra({
                        hs1: index,
                        hs2: idx,
                        data: itm,
                      })}
                    />
                  </div>
                );
              })}
            </div>
          </div>
        );
      },
    },
    {
      title: <HeaderSearch title={t("quanLyNoiTru.dvNoiTru.soLuongKe")} />,
      width: "60px",
      align: "right",
      key: "soLuongYeuCau",
      i18Name: "quanLyNoiTru.dvNoiTru.soLuongKe",
      show: true,
      dataIndex: "soLuongYeuCau",
    },
    {
      title: <HeaderSearch title={t("goiDichVu.soLuongConLai")} />,
      width: "60px",
      align: "right",
      i18Name: "goiDichVu.soLuongConLai",
      show: true,
      render: (item, list, index) =>
        roundToDigits(
          (list?.soLuongYeuCau || 0) - (list?.soLuongYeuCauTra || 0),
          3
        ),
    },
    {
      title: <HeaderSearch title={t("goiDichVu.soLuong1Lan")} />,
      width: "60px",
      align: "right",
      i18Name: "goiDichVu.soLuong1Lan",
      show: true,
      render: (_, list, index) => (
        <div className="cell">
          <div className="cell-total">
            <div className="cell-total-number" style={{ minHeight: "24px" }}>
              {list?.soLuong1Lan}
            </div>
          </div>
          <div className="cell-list">
            {list.dsTra?.map((itm, idx) => {
              return (
                <div
                  key={itm.id}
                  className={`cell-item`}
                  style={{ minHeight: "38px" }}
                >
                  {itm.timestamp && (
                    <InputTimeout
                      controls={false}
                      disabled={!isEdit}
                      type="number"
                      value={refSoLuong1Lan.current?.[`${index}_${idx}`]}
                      onChange={onChange({
                        key: "soLuong1Lan",
                        hs1: index,
                        hs2: idx,
                        data: itm,
                        list,
                      })}
                      min={0}
                      className="cell-item-input"
                    />
                  )}
                </div>
              );
            })}
          </div>
        </div>
      ),
    },
    {
      title: <HeaderSearch title={t("goiDichVu.soLan1Ngay")} />,
      width: "60px",
      align: "right",
      i18Name: "goiDichVu.soLan1Ngay",
      show: true,
      render: (_, list, index) => (
        <div className="cell">
          <div className="cell-total">
            <div className="cell-total-number" style={{ minHeight: "24px" }}>
              {list?.soLan1Ngay}
            </div>
          </div>
          <div className="cell-list">
            {list.dsTra?.map((itm, idx) => {
              return (
                <div
                  key={itm.id}
                  className={`cell-item`}
                  style={{ minHeight: "38px" }}
                >
                  {itm.timestamp && (
                    <InputTimeout
                      disabled={!isEdit}
                      controls={false}
                      type="number"
                      value={refSoLan1Ngay.current?.[`${index}_${idx}`]}
                      onChange={onChange({
                        key: "soLan1Ngay",
                        hs1: index,
                        hs2: idx,
                        data: itm,
                        list,
                      })}
                      min={0}
                      className="cell-item-input"
                    />
                  )}
                </div>
              );
            })}
          </div>
        </div>
      ),
    },
    {
      title: <HeaderSearch title={t("goiDichVu.soNgay")} />,
      width: "60px",
      align: "right",
      i18Name: "goiDichVu.soNgay",
      show: false,
      render: (_, list) => {
        return list?.soNgay;
      },
    },
    {
      title: <HeaderSearch title={t(getI18nKey("soPhieuTra", _loaiDv))} />,
      width: 100,
      i18Name: getI18nKey("soPhieuTra", _loaiDv),
      show: true,
      render: (_, data, idx) => (
        <div className="cell">
          <div className="cell-total"></div>
          <div className="cell-list">
            {data.dsTra?.map((item, index) => (
              <div key={item.id} className="cell-item">
                {item.soPhieuTra}
              </div>
            ))}
          </div>
        </div>
      ),
    },
    {
      title: <HeaderSearch title={t(getI18nKey("ngayTra", _loaiDv))} />,
      width: 90,
      i18Name: getI18nKey("ngayTra", _loaiDv),
      show: true,
      render: (_, list, idx) => (
        <div className="cell">
          <div className="cell-total"></div>
          <div className="cell-list">
            {list.dsTra?.map((item, index) => (
              <div key={item.id} className="cell-item">
                {item?.thoiGianThucHien
                  ? moment(item.thoiGianThucHien).format("DD/MM/YYYY")
                  : ""}
              </div>
            ))}
          </div>
        </div>
      ),
    },
    {
      title: (
        <HeaderSearch
          sort_key="thoiGianThucHien"
          dataSort={dataSortColumn["thoiGianThucHien"] || ""}
          onClickSort={onClickSort}
          title={t("quanLyNoiTru.toDieuTri.ngayYLenh")}
        />
      ),
      width: 90,
      dataIndex: "thoiGianThucHien",
      i18Name: "quanLyNoiTru.toDieuTri.ngayYLenh",
      show: true,
      render: (item) => (
        <div className="cell">
          <div className="cell-total">{moment(item).format("DD/MM/YYYY")}</div>
        </div>
      ),
    },
    {
      title: <HeaderSearch title={t("common.daPhat")} />,
      width: 60,
      dataIndex: "phat",
      align: "center",
      i18Name: "common.daPhat",
      show: true,
      render: (item) => <Checkbox checked={item} />,
    },
    {
      title: <HeaderSearch title={t("pttt.khoaChiDinh")} />,
      width: 120,
      dataIndex: "tenKhoaChiDinh",
      i18Name: "pttt.khoaChiDinh",
      show: true,
    },
    {
      title: <HeaderSearch title={t(getI18nKey("nguoiTra", _loaiDv))} />,
      width: 120,
      dataIndex: "tenNguoiTra",
      i18Name: getI18nKey("nguoiTra", _loaiDv),
      show: true,
      render: (item, data) => {
        return [
          ...new Set(
            data?.dsTra?.map((item) => item.tenNguoiTra).filter(Boolean)
          ),
        ].join(", ");
      },
    },
    {
      title: (
        <HeaderSearch
          title={
            <>
              {t("common.thaoTac")} <Setting refTable={refSettings} />
            </>
          }
        />
      ),
      width: 80,
      dataIndex: "",
      key: "",
      align: "center",
      fixed: "right",
      ignore: true,
    },
  ].filter((i) => i.isShow !== false);

  const onChangePage = (page) => {
    setState({
      page: page - 1,
    });
  };

  const onSizeChange = (size) => {
    setState({
      size: size,
    });
  };

  const setRowClassName = (record) => {
    if (record.khongTinhTien) {
      return "green-color";
    }
    if (record.tuTra) {
      return "orange-color";
    }
  };

  return (
    <>
      <Main noPadding={true} top={8}>
        <TableWrapper
          columns={columns}
          dataSource={(dataSource || []).map((o) => ({
            ...o,
            dsTra: o.dsTra?.map((p) => ({
              ...p,
              soLuongYeuCauTra:
                p.soLuongYeuCauTra ||
                (p.soLuongTra && p.heSoDinhMuc
                  ? Math.round((p.soLuongTra / p.heSoDinhMuc) * 1000000) /
                    1000000
                  : null),
            })),
          }))}
          scroll={{ x: 1800 }}
          tableName="table_QLNT_DanhSachHangHoaTra"
          ref={refSettings}
          rowClassName={setRowClassName}
        />

        {!!state.totalElements && (
          <Pagination
            onChange={onChangePage}
            onShowSizeChange={onSizeChange}
            current={state.page + 1}
            pageSize={state.size}
            total={state.totalElements}
            listData={dataSource}
          />
        )}
      </Main>

      {isEdit && (
        <div
          style={{
            display: "flex",
            justifyContent: "flex-end",
            marginBottom: "2px",
          }}
        >
          <Button
            type={"default"}
            onClick={handleCancel}
            iconHeight={15}
            rightIcon={<SVG.IcCancel />}
            minWidth={100}
          >
            {t("common.huy")}
          </Button>
          <Button
            type={"primary"}
            onClick={handleSubmit}
            minWidth={100}
            rightIcon={<SVG.IcSave />}
            iconHeight={15}
          >
            {t("common.luu")}
          </Button>
        </div>
      )}
      <ModalCanhBaoCoSoTuTruc ref={refModalCanhBaoCoSoTuTruc} />
    </>
  );
};

DanhSachHangHoa.propTypes = {};

export default DanhSachHangHoa;
