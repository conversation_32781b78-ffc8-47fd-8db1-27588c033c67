import styled from "styled-components";
import { Card } from "components";

export const Main = styled(Card)`
  display: flex;
  flex-direction: column;
  flex: 1;
  .pagination {
    height: 20px;
    .ant-pagination-item {
      min-height: 20px;
      min-width: 20px;
      line-height: 20px;
      height: 20px;
    }
  }
  .cell {
    .cell-total-add {
      margin-top: -5px;
    }
    .cell-action {
      display: flex;
      padding-bottom: 16px;
      > div {
        margin-top: 2px;
      }
      &:last-child {
        padding-bottom: 0;
      }
    }

    .cell-item-input {
      border-radius: 8px !important;
    }
  }
  table {
    .green-color > .tenDichVu {
      background-color: #81c8a9 !important;
    }
    .orange-color > .tenDichVu {
      background-color: #ffd6c2 !important;
    }
  }
`;
