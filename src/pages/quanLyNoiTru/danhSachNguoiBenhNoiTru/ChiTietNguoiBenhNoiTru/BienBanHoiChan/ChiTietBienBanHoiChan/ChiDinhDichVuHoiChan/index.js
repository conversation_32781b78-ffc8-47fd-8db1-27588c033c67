import React, {
  useRef,
  useState,
  useEffect,
  forwardRef,
  useImperative<PERSON>andle,
  useMemo,
} from "react";
import { InputNumber, message } from "antd";
import CircleCheck from "assets/images/khamBenh/circle-check.png";
import { Main } from "./styled";
import { useTranslation } from "react-i18next";
import imgSearch from "assets/images/template/icSearch.png";
import { useDispatch, useSelector } from "react-redux";
import {
  HeaderSearch,
  Checkbox,
  Button,
  ModalTemplate,
  Pagination,
  InputTimeout,
  TableWrapper,
  Select,
} from "components";
import ThemChiDinhSpan from "pages/chiDinhDichVu/components/ThemChiDinhTxtSpan";
import { useStore, useThietLap } from "hooks";
import { THIET_LAP_CHUNG, LOAI_DICH_VU } from "constants/index";
import { combineSort } from "utils";
import { LOAI_HOI_CHAN } from "../..";
import { uniqBy } from "lodash";

const DichVuKyThuat = (props, ref) => {
  const { dataNb, listLoaiChiDinh, dsDoiTuongSuDung, dsLoaiDichVu } = props;
  const { t } = useTranslation();
  const refIsSubmit = useRef(null);
  const refInput = useRef(null);
  const refModal = useRef(null);
  const refCallback = useRef(null);

  const [dataPageSize] = useThietLap(THIET_LAP_CHUNG.PAGE_SIZE, 10);
  const [soLuongDvKyThuatToiDa1LanChiDinh] = useThietLap(
    THIET_LAP_CHUNG.SO_LUONG_DV_KY_THUAT_TOI_DA_1LAN_CHI_DINH
  );
  const soLuongToiDa = (() => {
    const n = Number(soLuongDvKyThuatToiDa1LanChiDinh);
    return Number.isInteger(n) && n > 0 ? n : 100;
  })();

  const [state, _setState] = useState({
    show: false,
    listSelectedDv: [],
  });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  const {
    chiDinhKhamBenh: { onSearchDichVu },
    nbBienBanHoiChan: { tuVanDichVu },
    noiLayBenhPham: { getListNoiLayMau },
  } = useDispatch();

  const chiTietNguoiBenhNoiTru = useStore(
    "danhSachNguoiBenhNoiTru.chiTietNguoiBenhNoiTru"
  );
  const { listDvKham, page, size, totalElements } = useSelector(
    (state) => state.chiDinhKhamBenh
  );
  const {
    auth: { nhanVienId },
  } = useSelector((state) => state.auth);

  useImperativeHandle(ref, () => ({
    show: (listNguoiTuVan = [], callback) => {
      setState({
        show: true,
        keyword: "",
        listNguoiTuVan: listNguoiTuVan,
      });
      refIsSubmit.current = false;
      refCallback.current = callback;

      onSearchDichVu2({
        page,
        size: dataPageSize,
        keyword: "",
      });
    },
  }));

  useEffect(() => {
    if (state.show) {
      refModal.current && refModal.current.show({});
      setTimeout(() => {
        refInput.current && refInput.current.focus();
      }, 1000);
    } else {
      refModal.current && refModal.current.hide({});
    }
  }, [state.show]);

  const onSelect = (listSelectedDv) => {
    setState({
      listSelectedDv: listSelectedDv,
      selectedRowKeys: (listSelectedDv || []).map((item) => item.dichVuId),
    });
  };

  const { listSelectedDv, keyword, loaiDichVu } = state;

  const onSelectServiceType = (value = "") => {
    setState({
      loaiDichVu: value,
      keyword: "",
    });
    onSearchDichVu2({
      page,
      size: dataPageSize,
      keyword: "",
      loaiDichVu: value,
    });
  };

  const isThemField = useMemo(() => {
    return [LOAI_HOI_CHAN.THUONG, LOAI_HOI_CHAN.THUOC].includes(
      dataNb?.loaiHoiChan
    );
  }, [dataNb?.loaiHoiChan]);

  const onSubmit = async () => {
    try {
      if (refIsSubmit.current) return; //nếu đang submit thì bỏ qua
      const { listSelectedDv } = state;
      if (!listSelectedDv.every((x) => x.soLuong > 0)) {
        message.error(t("cdha.vuiLongNhapSoLuongLon0"));
        return;
      }
      setState({
        filterText: "",
      });
      let payload = listSelectedDv.map((item) => ({
        nbDotDieuTriId: dataNb?.nbDotDieuTriId,
        bienBanHoiChanId: dataNb?.id,
        loaiDichVu: item?.loaiDichVu,
        dichVuId: item?.dichVuId,
        nguoiTuVanId: item?.nguoiTuVanId,
        soLuong: item?.soLuong,
        boSung: {
          dsMucDich: item?.dsMucDich,
          dsPhongThucHien: item?.dsPhongThucHien,
          nbDvKyThuat: {
            phongThucHienId: item?.phongThucHienId,
            tenPhongThucHien: item?.tenPhongThucHien,
            phongLayMauId: item?.phongLayMauId,
            tenPhongLayMau: item?.tenPhongLayMau,
            loaiHinhThanhToanId: item.loaiHinhThanhToanId,
          },
        },
      }));
      refIsSubmit.current = true;
      tuVanDichVu(payload)
        .then(() => {
          refIsSubmit.current = false;
          refCallback.current && refCallback.current();
          onCancel();
        })
        .catch((e) => {
          refIsSubmit.current = false;
        });
    } catch (error) {
      refIsSubmit.current = false;
    }
  };

  const onCancel = () => {
    setState({
      show: false,
      listSelectedDv: [],
      selectedRowKeys: [],
    });
  };

  const onSearch = (keyword) => {
    setState({
      keyword: keyword,
    });
    onSearchDichVu2({ keyword, page: 0, size, loaiDichVu: state.loaiDichVu });
  };

  const columnsDichVu = [
    {
      title: <HeaderSearch isTitleCenter={true} />,
      dataIndex: "ten",
      key: "ten",
      width: "100%",
      render: (value) => {
        return <b>{value}</b>;
      },
    },
    {
      title: <HeaderSearch isTitleCenter={true} />,
      dataIndex: "",
      key: "",
      width: "100%",
      render: (value, currentRow, index) => {
        const giaKhongBaoHiem = (currentRow.giaKhongBaoHiem || 0).formatPrice();
        const giaBaoHiem = (currentRow.giaBaoHiem || 0).formatPrice();
        const giaPhuThu = (currentRow.giaPhuThu || 0).formatPrice();
        const donGia = `${giaKhongBaoHiem} | ${t(
          "khamBenh.chiDinh.BH"
        )}: ${giaBaoHiem} | ${t("khamBenh.chiDinh.phuThu")}: ${giaPhuThu}`;
        return <div className="desc">{donGia}</div>;
      },
    },
  ];

  const onSelectChangeRight = (selectedRowKeys, data, item) => {
    setState({
      listSelectedDv: data,
      selectedRowKeys: selectedRowKeys,
    });
  };

  const rowSelectionRight = {
    columnTitle: <HeaderSearch title={t("common.chon")} />,
    columnWidth: 50,
    onChange: onSelectChangeRight,
    selectedRowKeys: state.selectedRowKeys,
    preserveSelectedRowKeys: true,
  };

  const onChange = (data, key) => (e, list) => {
    let _listSelectedDv = Object.assign([], listSelectedDv);
    const _findDvIndex = _listSelectedDv.findIndex(
      (x) => x.dichVuId === data?.dichVuId
    );
    if (_findDvIndex !== -1) {
      _listSelectedDv[_findDvIndex][key] = e;
      if (key === "phongThucHienId") {
        _listSelectedDv[_findDvIndex]["tenPhongThucHien"] = list?.ten || "";
      }
      if (key === "phongLayMauId") {
        _listSelectedDv[_findDvIndex]["tenPhongLayMau"] = list?.ten || "";
      }

      setState({ listSelectedDv: _listSelectedDv });
    }
  };

  const columnsTableRight = [
    {
      title: <HeaderSearch isTitleCenter={true} title={t("common.stt")} />,
      key: "index",
      width: 50,
      align: "center",
      render: (item, data, index) => {
        return <div clickcheckbox="true">{index + 1}</div>;
      },
    },
    {
      title: (
        <HeaderSearch
          isTitleCenter={true}
          title={<div className="pointer">{t("common.tenDichVu")}</div>}
        />
      ),
      dataIndex: "ten",
      key: "ten",
      width: 240,
      render: (item, data) => {
        return <div clickcheckbox="true">{item}</div>;
      },
    },
    {
      title: (
        <HeaderSearch
          isTitleCenter={true}
          title={<div className="pointer">{t("common.soLuong")}</div>}
        />
      ),
      dataIndex: "soLuong",
      key: "soLuong",
      width: 80,
      render: (item, data) => {
        const value = item ?? 1;
        return (
          <InputNumber
            min={1}
            max={soLuongToiDa}
            value={value}
            onChange={onChange(data, "soLuong")}
          />
        );
      },
    },
    {
      title: (
        <HeaderSearch
          isTitleCenter={true}
          title={<div className="pointer">{t("quanLyNoiTru.nguoiTuVan")}</div>}
        />
      ),
      dataIndex: "",
      key: "nguoiTuVanId",
      width: 120,
      render: (item, data) => {
        return (
          <Select
            data={state?.listNguoiTuVan}
            onChange={onChange(data, "nguoiTuVanId")}
          />
        );
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("khamBenh.chiDinh.loaiHinhThanhToan")}
          isTitleCenter={true}
        />
      ),
      dataIndex: "loaiHinhThanhToanId",
      key: "loaiHinhThanhToanId",
      width: 180,
      show: true,
      i18Name: "khamBenh.chiDinh.loaiHinhThanhToan",
      render: (item, data) => {
        const dataSource = (data.dsLoaiHinhThanhToan || [])
          .map((item) => ({
            id: item.loaiHinhThanhToanId,
            ten: item.tenLoaiHinhThanhToan,
            uuTien: item.uuTien,
            giaBaoHiem: item.giaBaoHiem,
            giaKhongBaoHiem: item.giaKhongBaoHiem,
          }))
          .sort((a, b) => b.uuTien - a.uuTien);
        return (
          <div className="soluong" onClick={(event) => event.stopPropagation()}>
            <Select
              value={data?.loaiHinhThanhToanId}
              data={dataSource}
              onChange={onChange(data, "loaiHinhThanhToanId")}
              style={{ margin: "0px" }}
              dropdownMatchSelectWidth={300}
            />
          </div>
        );
      },
    },
    ...(isThemField
      ? [
          {
            title: (
              <HeaderSearch
                title={t("khamBenh.chiDinh.phongThucHien")}
                isTitleCenter={true}
              />
            ),
            dataIndex: "phongThucHienId",
            key: "phongThucHienId",
            width: 200,
            show: true,
            i18Name: "khamBenh.chiDinh.phongThucHien",
            render: (item, data) => {
              const dataSource = uniqBy(
                data?.dsPhongThucHien || [],
                "phongId"
              ).map((item) => ({
                id: item.phongId,
                ten: item.ten,
              }));

              return (
                <Select
                  value={data?.phongThucHienId}
                  data={dataSource}
                  onChange={onChange(data, "phongThucHienId")}
                />
              );
            },
          },
          {
            title: (
              <HeaderSearch
                title={t("khamBenh.chiDinh.noiLayMau")}
                isTitleCenter={true}
              />
            ),
            dataIndex: "phongLayMauId",
            key: "phongLayMauId",
            width: 200,
            show: true,
            i18Name: "khamBenh.chiDinh.noiLayMau",
            render: (item, data) => {
              //chỉ hiển thị với loại dịch vụ xét nghiệm
              if (data?.loaiDichVu !== LOAI_DICH_VU.XET_NGHIEM) {
                return null;
              }
              const dataSource = uniqBy(
                data?.dsNoiLayMau || [],
                "phongLayMauId"
              ).map((item) => ({
                id: item.phongLayMauId,
                ten: `${item.phongLayMau?.ten || ""} (${
                  item.phongLayMau?.diaDiem || ""
                })`,
              }));

              return (
                <Select
                  value={data?.phongLayMauId}
                  data={dataSource}
                  onChange={onChange(data, "phongLayMauId")}
                />
              );
            },
          },
        ]
      : []),
  ];

  const onSelectChangeLeft = async (selectedRowKeys, data) => {
    const updateData = await Promise.all(
      data.map(async (item1) => {
        const existedItem = (state.listSelectedDv || []).find(
          (item2) =>
            item1.dichVuId &&
            item2.dichVuId &&
            item1.dichVuId === item2.dichVuId
        );

        if (existedItem) {
          // Giữ lại số lượng đã chọn trước đó
          item1.soLuong = existedItem.soLuong;
        } else {
          // Nếu chưa có thì mặc định 1
          item1.soLuong = 1;

          let resNoiLayMau = [];
          if (item1?.loaiDichVu === LOAI_DICH_VU.XET_NGHIEM) {
            resNoiLayMau = await getListNoiLayMau({
              khoaChiDinhId: dataNb?.khoaChiDinhId,
              dichVuId: item1.dichVuId,
              nhomDichVuCap2Id: item1.nhomDichVuCap2Id,
              nhomDichVuCap3Id: item1.nhomDichVuCap3Id,
              dsDoiTuongKcb: chiTietNguoiBenhNoiTru?.doiTuongKcb,
              dsLoaiDoiTuongId: chiTietNguoiBenhNoiTru?.loaiDoiTuongId,
            });
          }

          item1.dsNoiLayMau = resNoiLayMau || [];
        }

        // Đảm bảo không vượt quá max
        if (item1.soLuong > soLuongToiDa) {
          item1.soLuong = soLuongToiDa;
        }

        return item1;
      })
    );

    onSelect(updateData);
  };

  const checkAllDichVu = (e) => {
    const checked = e.target.checked;

    let updatedListDv = [];

    if (checked) {
      updatedListDv = [
        ...listDvKham
          .filter(
            (x1) =>
              listSelectedDv.findIndex((x2) => x1.dichVuId === x2.dichVuId) ===
              -1
          )
          .map((x) => ({ ...x, soLuong: x.soLuong || 1 })),
        ...listSelectedDv,
      ];
    } else {
      updatedListDv = listSelectedDv.filter(
        (x1) => listDvKham.findIndex((x2) => x1.dichVuId === x2.dichVuId) === -1
      );
    }

    onSelect(updatedListDv);
  };

  const rowSelectionLeft = {
    columnTitle: (
      <HeaderSearch
        title={
          <Checkbox
            onChange={checkAllDichVu}
            checked={listDvKham.every(
              (x1) =>
                listSelectedDv.findIndex(
                  (x2) => x1.dichVuId === x2.dichVuId
                ) !== -1
            )}
          />
        }
      />
    ),
    columnWidth: 50,
    onChange: onSelectChangeLeft,
    selectedRowKeys: state.selectedRowKeys,
    preserveSelectedRowKeys: true,
  };

  const renderEmptyTextLeftTable = () => {
    return (
      <div style={{ marginTop: 130 }}>
        <div style={{ color: "#c3c3c3", fontSize: 14 }}>
          {t("common.khongCoDuLieuPhuHop")}
        </div>
      </div>
    );
  };

  const onChangePage = (page) => {
    onSearchDichVu2({
      page: page - 1,
      size,
      keyword,
      loaiDichVu: loaiDichVu === 150 ? null : loaiDichVu,
    });
  };

  const onSizeChange = (value) => {
    onSearchDichVu2({
      page: 0,
      size: value,
      keyword: keyword,
      loaiDichVu: loaiDichVu === 150 ? null : loaiDichVu,
    });
  };

  const onSearchDichVu2 = ({ keyword, page = 0, size, loaiDichVu }) => {
    onSearchDichVu({
      ten: keyword,
      page: page || 0,
      size: size,
      khoaChiDinhId: dataNb?.khoaChiDinhId,
      doiTuongKcb: chiTietNguoiBenhNoiTru?.doiTuongKcb,
      loaiDoiTuongId: chiTietNguoiBenhNoiTru?.loaiDoiTuongId,
      bacSiChiDinhId: nhanVienId,
      dsDoiTuongSuDung: dsDoiTuongSuDung,
      ...(loaiDichVu
        ? { loaiDichVu: loaiDichVu }
        : {
            dsLoaiDichVu: dsLoaiDichVu,
          }),
      nbDotDieuTriId: dataNb?.nbDotDieuTriId,
      sort:
        loaiDichVu && !keyword
          ? combineSort({ stt: 1, ten: 1 })
          : !keyword
          ? combineSort({ ten: 1 })
          : null,
    });
  };

  return (
    <ModalTemplate
      width={1366}
      ref={refModal}
      title={t("quanLyNoiTru.toDieuTri.chiDinhDichVuKyThuat")}
      onCancel={onCancel}
      actionLeft={<Button.QuayLai onClick={onCancel} />}
      actionRight={
        <Button type="primary" minWidth={100} onClick={onSubmit}>
          {t("common.dongY")}
        </Button>
      }
    >
      <Main>
        <div className="content-title">
          <ThemChiDinhSpan />
          <Select
            placeholder={t("danhMuc.chonLoaiDichVu")}
            data={listLoaiChiDinh}
            value={state?.loaiDichVu}
            onChange={onSelectServiceType}
          ></Select>
          <div className="input-box">
            <img src={imgSearch} alt="imgSearch" />
            <InputTimeout
              placeholder={t("common.timKiem")}
              onChange={onSearch}
              value={state.keyword}
              ref={refInput}
            />
          </div>
        </div>
        <div className="content">
          <div className="content-equal-w">
            <div className="title-table">{t("common.dichVu")}</div>
            <div className="danh-sach-dich-vu">
              <TableWrapper
                rowKey={(record) => {
                  return record.dichVuId;
                }}
                columns={columnsDichVu}
                dataSource={listDvKham}
                rowSelection={rowSelectionLeft}
                rowClassName={(record, index) => {
                  return index % 2 === 0 ? "table-row-even" : "table-row-odd";
                }}
                showHeader={listDvKham && listDvKham.length > 0}
                onRow={() => {
                  return {
                    onClick: (row) => {
                      row.currentTarget.firstChild.firstElementChild.firstElementChild.firstElementChild.click();
                    },
                  };
                }}
                locale={{
                  emptyText: renderEmptyTextLeftTable(),
                }}
                scroll={{ y: 350 }}
              />
              {!!listDvKham.length && (
                <Pagination
                  listData={listDvKham}
                  onChange={onChangePage}
                  current={page + 1}
                  pageSize={size}
                  total={totalElements}
                  onShowSizeChange={onSizeChange}
                  stylePagination={{ justifyContent: "flex-start" }}
                />
              )}
            </div>
          </div>
          <div className="content-equal-w">
            <div className="title">
              <div className="title__left">
                <img src={CircleCheck} alt="" /> {t("common.daChon")}
              </div>
            </div>
            <div className="content-body">
              <TableWrapper
                rowKey={(record) => {
                  return record.dichVuId;
                }}
                rowSelection={rowSelectionRight}
                className="table-right"
                columns={columnsTableRight}
                dataSource={listSelectedDv}
                scroll={{ x: 800, y: 380 }}
                styleWrap={{ height: 380 }}
                rowClassName={(record, index) => {
                  return index % 2 === 0
                    ? `table-row-even ${
                        index === state.listServiceSelected?.length - 1
                          ? "add-border"
                          : ""
                      }`
                    : `table-row-odd ${
                        index === state.listServiceSelected?.length - 1
                          ? "add-border"
                          : ""
                      }`;
                }}
                onRow={() => {
                  return {
                    onClick: (row) => {
                      if (
                        row?.target?.firstElementChild?.hasAttribute(
                          "clickcheckbox"
                        ) ||
                        row?.target?.hasAttribute("clickcheckbox")
                      ) {
                        row.currentTarget.firstChild.firstElementChild.firstElementChild.firstElementChild.click();
                      }
                    },
                  };
                }}
                locale={{
                  emptyText: (
                    <div style={{ height: 297 }}>
                      <div style={{ color: "#c3c3c3", lineHeight: "297px" }}>
                        {t("khamBenh.donThuoc.khongCoDuLieuThuocDaChon")}
                      </div>
                    </div>
                  ),
                }}
              />
            </div>
          </div>
        </div>
      </Main>
    </ModalTemplate>
  );
};

export default forwardRef(DichVuKyThuat);
