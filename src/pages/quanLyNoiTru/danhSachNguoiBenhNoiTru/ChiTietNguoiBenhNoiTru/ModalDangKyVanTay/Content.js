import React, { useEffect, useMemo, useRef, useState } from "react";
import { useDispatch } from "react-redux";
import { Button, ListImage, ModalTemplate } from "components";
import { useTranslation } from "react-i18next";
import { SVG } from "assets";
import { useFingerprint, useLoading, useStore, useThietLap } from "hooks";
import { Main } from "./styled";
import styled from "styled-components";
import isofhToolProvider from "data-access/isofh-tool-provider";
import fileProvider from "data-access/file-provider";
import { showError } from "utils/message-utils";
import { message, Modal } from "antd";
import FingerprintScanner from './FingerprintScanner';
import fileUtils from "utils/file-utils";
import stringUtils from "mainam-react-native-string-utils";

const FingerprintGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
`;

const FingerprintCard = styled.div`
  display: flex;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 0.75rem;
  padding: 1rem;
  transition: all 0.2s;
  overflow: hidden;

  &:hover {
    border-color: #3b82f6;
    box-shadow: 0 4px 6px rgba(59, 130, 246, 0.1);
  }
`;

const FingerprintHeader = styled.div`
  display: flex;
  flex: 1;
  align-items: center;
  gap: 0.75rem;
`;

const FingerprintIcon = styled.div`
  width: 40px;
  height: 40px;
  background: #f0f9ff;
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #3b82f6;
  font-size: 1.25rem;
`;

const FingerprintInfo = styled.div`
  flex: 1;
`;

const FingerprintName = styled.div`
  font-weight: 500;
  color: #1a365d;
`;

const FingerprintId = styled.div`
  font-size: 0.75rem;
  color: #64748b;
  font-family: monospace;
`;

const AddFingerprintButton = styled(Button)`
  width: 100%;
  background: #10b981;
  justify-content: center;
  margin-top: 1rem;

  &:hover {
    background: #059669;
  }
`;

const FingerprintButton = styled.div`
  flex: 1;
  display: flex
`;

const Content = (props) => {
  const refFingerprintScanner = useRef(null);
  const refFileInput = useRef(null);
  const { showLoading, hideLoading } = useLoading();
  const { t } = useTranslation();

  const {
    showModal,
    options: { submitCallback },
  } = ModalTemplate.useModal();
  const {
    nbDotDieuTri: {
      getById,
      onUpdate
    },
  } = useDispatch();

  const { getVanTay } = useFingerprint();

  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewSrc, setPreviewSrc] = useState("");

  const [fingerprints, setFingerprints] = useState("");
  const chiTietNguoiBenhNoiTru = useStore("nbDotDieuTri.thongTinBenhNhan");

  useEffect(() => {
    if (chiTietNguoiBenhNoiTru && chiTietNguoiBenhNoiTru?.anhVanTay) {
      setFingerprints(chiTietNguoiBenhNoiTru?.anhVanTay);
    }
  }, [showModal, chiTietNguoiBenhNoiTru]);

  const onClose = () => {
    props.onCancel(true);
  }

  const onOK = async () => {
    try {
      showLoading();
      const res = await onUpdate({
        id: props.nbDotDieuTriId,
        anhVanTay: fingerprints
      });
      await getById(props.nbDotDieuTriId);
      message.success(t("common.capNhatThanhCong"));
      onClose()
    } catch (error) {
      showError(error?.message);
    } finally {
      hideLoading();
    }
  };

  const chooseImage = () => {
    if (refFileInput.current) {
      refFileInput.current.value = "";
      refFileInput.current.click();
    }
  }

  const taiFile = async (file) => {
    const s = await fileProvider.uploadImage({ file, type: "anhVanTayNb", nbDotDieuTriId: props.nbDotDieuTriId });
    setFingerprints(s?.data);
    message.success(t("common.taiLenThanhCong"));
  }

  const handleFileChange = async (e) => {
    const file = e?.target?.files?.[0];
    if (!file) return;
    try {
      showLoading();
      taiFile(file);
    } catch (error) {
      showError(error?.message);
    } finally {
      hideLoading();
    }
  };

  const onAddVanTay = async () => {
    getVanTay({ api: isofhToolProvider.getVanTay }, async (e, data) => {
      if (e) {
        showError(e?.message);
      }
      else {
        if (data?.dataVanTay) {
          let file = fileUtils.base64ToFile(data?.dataVanTay, stringUtils.guid() + ".png")
          taiFile(file);
        }
      }
    });
  };

  const removeFingerprint = () => {
    setFingerprints("");
  };

  const onPreview = async (item) => {
    let itemParse = JSON.parse(JSON.stringify(item));
    if (itemParse) {
      const absoluteUrl = await fileUtils.urlToBase64(fileUtils.absoluteFileUrl(itemParse));
      setPreviewSrc(absoluteUrl);
    }
    setPreviewVisible(true);
  };

  return (
    <ModalTemplate.Container>
      <ModalTemplate.Content>
        <Main>
          <input
            ref={refFileInput}
            type="file"
            accept="image/*"
            style={{ display: "none" }}
            onChange={handleFileChange}
          />
          <FingerprintGrid>
            {fingerprints && (
              <FingerprintCard className="pointer" onClick={() => onPreview(fingerprints)}>
                <FingerprintHeader>
                  <FingerprintIcon><SVG.IcFingerPrint /></FingerprintIcon>
                  <FingerprintInfo>
                    <FingerprintName>Vân tay</FingerprintName>
                    <FingerprintId>ID: {fingerprints.substring(0, 50)}...</FingerprintId>
                  </FingerprintInfo>
                </FingerprintHeader>
                <SVG.IcDelete className="pointer" onClick={(e) => { e.stopPropagation(); removeFingerprint(); }} />
              </FingerprintCard>
            )}
          </FingerprintGrid>
          <FingerprintButton>
            <AddFingerprintButton onClick={chooseImage} leftIcon={<SVG.IcUpload />}>
              Tải ảnh vân tay
            </AddFingerprintButton>
            {!fingerprints ? (
              <AddFingerprintButton onClick={onAddVanTay} leftIcon={<SVG.IcAdd />}>
                Thêm vân tay mới
              </AddFingerprintButton>
            ) : (
              <AddFingerprintButton onClick={onAddVanTay} leftIcon={<SVG.IcRefresh />}>
                Quét lại vân tay
              </AddFingerprintButton>
            )}
          </FingerprintButton>
        </Main>
      </ModalTemplate.Content>
      <Modal
        open={previewVisible}
        onCancel={() => setPreviewVisible(false)}
        footer={null}
        centered
        width={600}
      >
        <img src={previewSrc} alt="anhVanTay" style={{ width: "100%" }} />
      </Modal>
      <ModalTemplate.Footer>
        <ModalTemplate.FooterLeft>
          <Button.QuayLai onClick={onClose} />
        </ModalTemplate.FooterLeft>
        <ModalTemplate.FooterRight>
          <Button
            type="primary"
            minWidth={100}
            rightIcon={<SVG.IcSave />}
            onClick={onOK}
          >
            {t("common.luuThongTin")}
          </Button>
        </ModalTemplate.FooterRight>
      </ModalTemplate.Footer>
      <FingerprintScanner ref={refFingerprintScanner} />
    </ModalTemplate.Container>
  );
};

export default Content;
