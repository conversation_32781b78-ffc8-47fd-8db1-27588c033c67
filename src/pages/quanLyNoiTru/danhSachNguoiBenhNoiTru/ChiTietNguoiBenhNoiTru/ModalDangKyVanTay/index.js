import React, { forwardRef, useImperativeHandle, useRef, useState, useEffect } from "react";
import { ModalTemplate, withSuspense } from "components";
import { useTranslation } from "react-i18next";

const Content = withSuspense(() => import("./Content"));

const ModalQuanLyVanTay = (props, ref) => {
  const { t } = useTranslation();
  const refModal = useRef(null);
  const [state, _setState] = useState({
    show: false,
    nbDotDieuTriId: null
  });

  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  useImperativeHandle(ref, () => ({
    show: ({ nbDotDieuTriId }) => {
      setState({ show: true, nbDotDieuTriId });
    },
  }));

   useEffect(() => {
    if (state.show) {
      refModal.current && refModal.current.show();
    } else {
      refModal.current && refModal.current.hide();
    }
  }, [state.show]);

  const onClose = () => {
    setState({ show: false });
  }

  return (
    <ModalTemplate
      ref={refModal}
      closable={false}
      onCancel={onClose}
      width={"450px"}
      title={t("common.dangKyVanTay")}
    >
      <Content nbDotDieuTriId={state?.nbDotDieuTriId} onCancel={onClose}/>
    </ModalTemplate>
  );
};

export default forwardRef(ModalQuanLyVanTay);
