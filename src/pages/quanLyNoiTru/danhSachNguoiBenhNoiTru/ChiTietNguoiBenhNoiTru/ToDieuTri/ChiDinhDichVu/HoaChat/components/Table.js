import React, { useRef } from "react";
import { Tooltip, Checkbox, HeaderSearch, TableWrapper } from "components";
import { Main } from "../styled";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import Chinh<PERSON>uaHoaChat from "pages/chiDinhDichVu/DichVuHoaChat/ChinhSuaHoaChat";
import { useConfirm, useStore } from "hooks";
import moment from "moment";
import { SVG } from "assets";
import { LOAI_DICH_VU } from "constants";
import { TRANG_THAI_THUOC } from "constants";
import { TRANG_THAI_HOAN } from "constants";
import { checkRole } from "lib-utils/role-utils";
import { ROLES } from "constants";
import ModalNhapSoLuong from "pages/khamBenh/DonThuoc/ModalNhapSoLuong";
const { Setting } = TableWrapper;

function Table(props) {
  const { showConfirm } = useConfirm();
  const {
    chiDinhHoaChat: { onDeleteDichVu, getListDichVuHoaChat },
    traHangHoa: { postDsDvThuocTraKho },
  } = useDispatch();
  const currentToDieuTri = useStore("toDieuTri.currentToDieuTri", {});

  const refSettings = useRef(null);
  const refModalNhapSoLuong = useRef(null);

  const { listDvHoaChat, isReadonly, nbDotDieuTriId, chiDinhTuDichVuId } =
    props;
  const { t } = useTranslation();

  const refSuaThongTin = useRef(null);

  const onDelete = (record) => {
    showConfirm(
      {
        title: t("common.xoaDuLieu"),
        content: t("common.banCoChacMuonXoa") + (record?.tenDichVu || "") + "?",
        cancelText: t("common.quayLai"),
        okText: t("common.dongY"),
        classNameOkText: "button-error",
        showBtnOk: true,
        typeModal: "error",
      },
      () => {
        onDeleteDichVu(record.id).then((s) =>
          getListDichVuHoaChat({
            nbDotDieuTriId: nbDotDieuTriId || currentToDieuTri.nbDotDieuTriId,
            chiDinhTuDichVuId: chiDinhTuDichVuId || currentToDieuTri?.id,
            dsTrangThaiHoan: [0, 10, 20],
            noClearListData: true,
          })
        );
      }
    );
  };

  const onEdit = (record) => () => {
    refSuaThongTin.current && refSuaThongTin.current.show(record);
  };

  const onNgungYLenh = (record) => async (e) => {
    refModalNhapSoLuong.current &&
      refModalNhapSoLuong.current.show({}, async (value) => {
        await postDsDvThuocTraKho({
          loaiHangHoa: LOAI_DICH_VU.HOA_CHAT,
          payload: [
            {
              nbDichVuId: record.id,
              soLuong: value,
            },
          ],
        });
        refreshData();
      });
  };

  const columns = [
    {
      title: <HeaderSearch title={t("common.stt")} sort_key="index" />,
      width: 60,
      dataIndex: "index",
      key: "index",
      align: "center",
      ignore: true,
      render: (item, row, index) => {
        return index + 1;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("quanLyNoiTru.toDieuTri.tenHoaChat")}
          sort_key="tenDichVu"
        />
      ),
      width: 150,
      dataIndex: "tenDichVu",
      key: "tenDichVu",
      i18Name: t("quanLyNoiTru.toDieuTri.tenHoaChat"),
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("khamBenh.donThuoc.slChiDinh")}
          sort_key="soLuong"
        />
      ),
      width: 120,
      dataIndex: "soLuong",
      key: "soLuong",
      align: "center",
      i18Name: t("khamBenh.donThuoc.slChiDinh"),
      show: true,
      render: (item, list) => {
        return `${list?.soLuongYeuCau} ${list?.tenDonViTinh}`;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("khamBenh.donThuoc.slTra")}
          sort_key="soLuongTra"
        />
      ),
      width: 80,
      dataIndex: "soLuongTra",
      key: "soLuongTra",
      align: "center",
      i18Name: t("khamBenh.donThuoc.slTra"),
      show: true,
    },
    {
      title: <HeaderSearch title={t("khamBenh.donThuoc.slThucDung")} />,
      width: 120,
      dataIndex: "soLuong",
      key: "soLuong",
      align: "center",
      i18Name: t("khamBenh.donThuoc.slThucDung"),
      show: true,
    },
    {
      title: (
        <HeaderSearch title={t("quanLyNoiTru.dvNoiTru.thoiGianThucHien")} />
      ),
      width: 180,
      dataIndex: "thoiGianThucHien",
      key: "thoiGianThucHien",
      i18Name: t("quanLyNoiTru.dvNoiTru.thoiGianThucHien"),
      show: true,
      render: (item) => item && moment(item).format("DD/MM/YYYY HH:mm:ss"),
    },
    {
      title: (
        <HeaderSearch title={t("quanLyNoiTru.dvNoiTru.thoiGianChiDinh")} />
      ),
      width: 180,
      dataIndex: "thoiGianChiDinh",
      key: "thoiGianChiDinh",
      i18Name: t("quanLyNoiTru.dvNoiTru.thoiGianChiDinh"),
      show: true,
      render: (item) => item && moment(item).format("DD/MM/YYYY HH:mm:ss"),
    },
    {
      title: (
        <HeaderSearch title={t("khamBenh.donThuoc.kho")} sort_key="tenKho" />
      ),
      width: 120,
      dataIndex: "tenKho",
      key: "tenKho",
      i18Name: t("khamBenh.donThuoc.kho"),
      show: true,
      render: (item, list) => {
        return item;
      },
    },
    {
      title: (
        <HeaderSearch title={t("hsba.bsChiDinh")} sort_key="tenBacSiChiDinh" />
      ),
      width: 150,
      dataIndex: "tenBacSiChiDinh",
      key: "tenBacSiChiDinh",
      align: "center",
      i18Name: "hsba.bsChiDinh",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("quanLyNoiTru.dvNoiTru.soPhieuLinh")}
          sort_key="soPhieuLinh"
        />
      ),
      width: 120,
      dataIndex: "soPhieuLinh",
      key: "soPhieuLinh",
      align: "center",
      i18Name: "quanLyNoiTru.dvNoiTru.soPhieuLinh",
      show: true,
    },
    {
      title: <HeaderSearch title={t("common.daPhat")} sort_key="phat" />,
      width: 100,
      dataIndex: "phat",
      key: "phat",
      align: "center",
      i18Name: "common.daPhat",
      show: true,
      render: (item) => <Checkbox checked={item} />,
    },
    {
      title: (
        <HeaderSearch
          title={t("quanLyNoiTru.dvNoiTru.tuTra")}
          sort_key="tuTra"
        />
      ),
      width: 100,
      dataIndex: "tuTra",
      key: "tuTra",
      align: "center",
      i18Name: "quanLyNoiTru.dvNoiTru.tuTra",
      show: true,
      render: (item) => <Checkbox checked={item} />,
    },
    {
      title: (
        <HeaderSearch
          title={t("quanLyNoiTru.dvNoiTru.khongTinhTien")}
          sort_key="khongTinhTien"
        />
      ),
      width: 150,
      dataIndex: "khongTinhTien",
      key: "khongTinhTien",
      align: "center",
      i18Name: "quanLyNoiTru.dvNoiTru.khongTinhTien",
      show: true,
      render: (item) => <Checkbox checked={item} />,
    },
    {
      title: (
        <HeaderSearch
          title={
            <>
              {t("common.khac")}
              <Setting refTable={refSettings} />
            </>
          }
        />
      ),
      width: 130,
      dataIndex: "action",
      key: "action",
      align: "center",
      fixed: "right",
      colSpan: 1,
      ignore: true,
      render: (item, record, index) => {
        return (
          <div className="action-btn">
            <Tooltip title={t("tiepDon.suaThongTin")} placement="bottom">
              <SVG.IcEdit onClick={onEdit(record)} className="icon" />
            </Tooltip>
            {!isReadonly && (
              <Tooltip title={t("tiepDon.xoaDichVu")} placement="bottom">
                <SVG.IcDelete
                  onClick={() => onDelete(record)}
                  className="icon"
                />
              </Tooltip>
            )}
            {record.trangThai < TRANG_THAI_THUOC.DA_PHAT.id &&
              record.trangThaiHoan === TRANG_THAI_HOAN.THUONG &&
              checkRole([ROLES["KHAM_BENH"].NGUNG_Y_LENH_KHONG_THUC_HIEN]) && (
                <>
                  <Tooltip title={t("khamBenh.ngungYLenh")} placement="bottom">
                    <SVG.IcXoaHoSo
                      className="ic-action"
                      onClick={onNgungYLenh(record)}
                      color="var(--color-red-primary)"
                    />
                  </Tooltip>
                </>
              )}
          </div>
        );
      },
    },
  ];
  return (
    <Main>
      <TableWrapper
        columns={columns}
        dataSource={listDvHoaChat}
        scroll={{ x: false, y: false }}
        tableName="table_ChiDinhDichVuToDieuTri_HoaChat"
        ref={refSettings}
      />
      <ChinhSuaHoaChat ref={refSuaThongTin} isReadonlyDvNoiTru={isReadonly} />
      <ModalNhapSoLuong ref={refModalNhapSoLuong} />
    </Main>
  );
}

export default React.memo(Table);
