import React, {
  useEffect,
  useState,
  forwardRef,
  useImperativeHandle,
  useRef,
  useMemo,
} from "react";
import { Col, Form, Row, TreeSelect } from "antd";
import { Main } from "./styled";
import {
  Button,
  ModalTemplate,
  Select,
  Checkbox,
  AlertMessage,
  SelectLoadMore,
} from "components";
import { useStore, useEnum, useLoading, useThietLap } from "hooks";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import FormWraper from "components/FormWraper";
import moment from "moment";
import { cloneDeep, orderBy } from "lodash";
import { useHistory } from "react-router-dom";
import {
  DOI_TUONG_KCB,
  ENUM,
  LOAI_DICH_VU,
  THIET_LAP_CHUNG,
} from "constants/index";
import { combineSort, isArray } from "utils/index";
import dvKyThuatProvider from "data-access/nb-dv-ky-thuat-provider";
import {
  parseDsLoaiDichVu,
  DS_TRANG_THAI_HOAN,
  generateTreeDatas,
} from "utils/sao-chep-to-dieu-tri-utils";
import nbDotDieuTriProvider from "data-access/nb-dot-dieu-tri-provider";
import nbDichVuProvider from "data-access/nb-dich-vu-provider";

const { SHOW_PARENT } = TreeSelect;

const initialState = {
  show: false,
  toDieuTriId: null,
  dsMaHoSo: [],
  dsKhoThuoc: [],
  dsKhoVatTu: [],
  disabledKho: false,
  isKhoInteracted: false,
  readOnlyLoaiDichVu: false,
  isMaHSCu: false,
  dsDvKyThuat: [],
};

const ModalSaoChepThuoc = (props, ref) => {
  const { t } = useTranslation();
  const { hideLoading, showLoading } = useLoading();
  const history = useHistory();
  const [form] = Form.useForm();
  const refModal = useRef(null);
  const refIsSubmit = useRef(null);

  const chiTietNguoiBenhNoiTru = useStore(
    "danhSachNguoiBenhNoiTru.chiTietNguoiBenhNoiTru"
  );

  const dsLoaiDichVu = Form.useWatch("dsLoaiDichVu", form) || [];
  const tuToDieuTriId = Form.useWatch("tuToDieuTriId", form);
  const saoChepDichVuTuNbTrongKhoa = Form.useWatch(
    "saoChepDichVuTuNbTrongKhoa",
    form
  );
  const hienThiDv = Form.useWatch("hienThiDv", form);

  const {
    toDieuTri: { getToDieuTri, saoChepThuoc },
    thietLapChonKho: { getListThietLapChonKhoTheoTaiKhoan },
    nbDotDieuTri: { searchNBDotDieuTriTongHop },
  } = useDispatch();
  const { listToDieuTri, currentToDieuTri } = useSelector(
    (state) => state.toDieuTri
  );
  const [listLoaiToDieuTri] = useEnum(ENUM.LOAI_TO_DIEU_TRI);
  const [listLoaiChiDinh] = useEnum(ENUM.LOAI_CHI_DINH);

  const [dataMAC_DINH_KHO_THUOC_NOI_TRU_KHI_SAO_CHEP] = useThietLap(
    THIET_LAP_CHUNG.MAC_DINH_KHO_THUOC_NOI_TRU_KHI_SAO_CHEP
  );
  const [dataMAC_DINH_KHO_VAT_TU_NOI_TRU_KHI_KE] = useThietLap(
    THIET_LAP_CHUNG.MAC_DINH_KHO_VAT_TU_NOI_TRU_KHI_KE
  );
  const [dataSAO_CHEP_DV_THEO_MHS_CU] = useThietLap(
    THIET_LAP_CHUNG.SAO_CHEP_DV_THEO_MHS_CU
  );
  const [dataHIEN_THI_CHECKBOX_TU_TRUC] = useThietLap(
    THIET_LAP_CHUNG.HIEN_THI_CHECKBOX_TU_TRUC
  );

  const { id: nbDotDieuTriId } = chiTietNguoiBenhNoiTru || {};

  const [state, _setState] = useState(cloneDeep(initialState));
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  useEffect(() => {
    if (state.show) {
      refModal.current && refModal.current.show();
    } else {
      refModal.current && refModal.current.hide();
    }
  }, [state.show]);

  useEffect(() => {
    form.setFieldsValue({});
  }, [listToDieuTri]);

  const dsSaoChepDvTheoMaHsCu = useMemo(() => {
    if (dataSAO_CHEP_DV_THEO_MHS_CU) {
      const _dsLoaiDv = dataSAO_CHEP_DV_THEO_MHS_CU.split(",");
      return _dsLoaiDv.map((x) => Number(x.trim()));
    }

    return [];
  }, [dataSAO_CHEP_DV_THEO_MHS_CU]);

  const dsKhoMemo = useMemo(() => {
    let _dsKho = [];

    if (dsLoaiDichVu.includes(90)) _dsKho = [..._dsKho, ...state.dsKhoThuoc];

    if (dsLoaiDichVu.includes(100)) _dsKho = [..._dsKho, ...state.dsKhoVatTu];
    if (dsLoaiDichVu.includes(125)) _dsKho = [..._dsKho, ...state.dsKhoCpdd];
    return _dsKho;
  }, [dsLoaiDichVu, state.dsKhoThuoc, state.dsKhoVatTu, state.dsKhoCpdd]);

  useEffect(() => {
    const dsKhoId = [];

    if (state.isKhoInteracted && dsKhoMemo.length) {
      form.getFieldValue("dsKhoId")?.forEach((i) => {
        if (dsKhoMemo.some((x) => x.id == i)) dsKhoId.push(i);
      });
      form.setFieldValue("dsKhoId", dsKhoId);
      return;
    }

    const addKho = (thietLap) => {
      const khoThietLap = thietLap.split(",").map((i) => i.trim());
      const kho = khoThietLap
        .map((i) => dsKhoMemo.find((x) => x.ma === i)?.id)
        .filter(Boolean);
      dsKhoId.push(...kho);
    };

    if (
      dataMAC_DINH_KHO_THUOC_NOI_TRU_KHI_SAO_CHEP &&
      dsLoaiDichVu.includes(90)
    ) {
      addKho(dataMAC_DINH_KHO_THUOC_NOI_TRU_KHI_SAO_CHEP);
    }
    if (dataMAC_DINH_KHO_VAT_TU_NOI_TRU_KHI_KE && dsLoaiDichVu.includes(100)) {
      addKho(dataMAC_DINH_KHO_VAT_TU_NOI_TRU_KHI_KE);
    }

    if (dsKhoId.length > 0) {
      form.setFieldValue("dsKhoId", dsKhoId);
    } else if (
      // nếu chỉ có 1 kho và loại dịch vụ là thuốc, vật tự, cpdd thì tự động chọn kho
      dsKhoMemo.length === 1 &&
      dsLoaiDichVu.some((x) => [90, 100, 125].includes(x))
    ) {
      form.setFieldValue("dsKhoId", [dsKhoMemo[0].id]);
    }
  }, [dsKhoMemo, dsLoaiDichVu, state.isKhoInteracted]);

  const listToDieuTriMemo = useMemo(() => {
    if (!state.toDieuTriId) return [];

    const listToDieuTriOrder = orderBy(
      listToDieuTri || [],
      "thoiGianYLenh",
      "desc"
    )
      .filter(
        (x) =>
          x.id != state.toDieuTriId &&
          x.khoaChiDinhId == currentToDieuTri?.khoaChiDinhId
      )
      .map((x) => ({
        id: x.id,
        ten: moment(x.thoiGianYLenh).isValid
          ? moment(x.thoiGianYLenh).format("DD/MM/YYYY HH:mm")
          : "",
      }));

    if (listToDieuTriOrder && listToDieuTriOrder.length > 0)
      form.setFieldsValue({
        tuToDieuTriId: listToDieuTriOrder[0].id,
      });

    return listToDieuTriOrder;
  }, [listToDieuTri, state.toDieuTriId]);

  const treeDataLoaiDVMemo = useMemo(() => {
    let parseTreeDatas = (treeDatas, tuToDieuTriId) => {
      let result = cloneDeep(treeDatas);
      let idx = result.findIndex((i) => i.value == "0");
      if (idx > -1) {
        let dvKyThuat = result[idx].children.reduce((acc, cur) => {
          if (isArray(cur.children, true)) {
            let listChild = cur.children.filter((item) => {
              let chiDinhTuDichVuId = item.value.split("_")[2];
              chiDinhTuDichVuId = !isNaN(+chiDinhTuDichVuId)
                ? +chiDinhTuDichVuId
                : null;
              return tuToDieuTriId ? chiDinhTuDichVuId === tuToDieuTriId : true;
            });
            acc.push({
              ...cur,
              children: listChild,
            });
          } else {
            acc.push(cur);
          }
          return acc;
        }, []);
        result[idx].children = dvKyThuat;
      }
      return result;
    };

    if (state.isMaHSCu && isArray(state.treeDatas, true)) {
      let treeDatas = parseTreeDatas(state.treeDatas, tuToDieuTriId);
      return treeDatas
        .map((item) => {
          if (item.value == 0) {
            return {
              ...item,
              children: item.children.filter((x) =>
                dsSaoChepDvTheoMaHsCu.includes(x.value)
              ),
            };
          } else {
            return item;
          }
        })
        .filter((item) => {
          if (item.value == 0) {
            return item.children.length > 0;
          } else {
            return dsSaoChepDvTheoMaHsCu.includes(item.value);
          }
        });
    } else if (tuToDieuTriId && isArray(state.treeDatas, true)) {
      return parseTreeDatas(state.treeDatas, tuToDieuTriId);
    } else {
      return state.treeDatas;
    }
  }, [dsSaoChepDvTheoMaHsCu, state.isMaHSCu, state.treeDatas, tuToDieuTriId]);

  useEffect(() => {
    if (
      state.isMaHSCu &&
      dsLoaiDichVu.some((item) => !dsSaoChepDvTheoMaHsCu.includes(item))
    ) {
      //nếu chọn mã hs cũ => kiểm tra thiết lập ds loại dịch vụ được sao chép để reset lại dsLoaiDichVu
      const _dsLoaiDichVu = dsLoaiDichVu.filter((item) =>
        dsSaoChepDvTheoMaHsCu.includes(item)
      );

      form.setFieldsValue({ dsLoaiDichVu: _dsLoaiDichVu });
    }
  }, [state.isMaHSCu, dsLoaiDichVu, dsSaoChepDvTheoMaHsCu]);

  const dsMaHoSoMemo = useMemo(() => {
    return (state.dsMaHoSo || []).map((item) => ({
      id: item.id,
      ten: item.maHoSo,
    }));
  }, [state.dsMaHoSo]);

  useImperativeHandle(ref, () => ({
    show: async ({ toDieuTriId, khoaNbId, loai, treeConfig }) => {
      showLoading();
      refIsSubmit.current = false;

      try {
        async function fetchData() {
          getToDieuTri({
            nbDotDieuTriId,
            loai: loai || listLoaiToDieuTri[0].id,
          });
          let payload = {
            khoaNbId: chiTietNguoiBenhNoiTru?.khoaNbId,
            khoaChiDinhId: currentToDieuTri?.khoaChiDinhId,
            doiTuong: chiTietNguoiBenhNoiTru?.doiTuong,
            loaiDoiTuongId: chiTietNguoiBenhNoiTru?.loaiDoiTuongId,
            capCuu: chiTietNguoiBenhNoiTru?.capCuu,
            phongId: chiTietNguoiBenhNoiTru?.phongId,
            noiTru: true,
            canLamSang: false,
          };
          let dsMaHoSo = {};

          if (chiTietNguoiBenhNoiTru?.nbThongTinId) {
            dsMaHoSo = await searchNBDotDieuTriTongHop({
              nbThongTinId: chiTietNguoiBenhNoiTru?.nbThongTinId,
              sort: combineSort({ thoiGianVaoVien: 2 }),
              active: true,
            });
          }
          dsMaHoSo = dsMaHoSo.data || [];

          let dsKhoThuoc = await getListThietLapChonKhoTheoTaiKhoan({
            ...payload,
            loaiDichVu: 90,
          });
          dsKhoThuoc = dsKhoThuoc?.payload?.listThietLapChonKho || [];

          let dsKhoVatTu = await getListThietLapChonKhoTheoTaiKhoan({
            ...payload,
            loaiDichVu: 100,
          });
          dsKhoVatTu = dsKhoVatTu?.payload?.listThietLapChonKho || [];
          let dsKhoCpdd = await getListThietLapChonKhoTheoTaiKhoan({
            ...payload,
            loaiDichVu: 125,
          });
          dsKhoCpdd = dsKhoCpdd?.payload?.listThietLapChonKho || [];

          let _listToDieuTri = (listToDieuTri || []).filter(
            (x) =>
              x.id != toDieuTriId &&
              x.khoaChiDinhId == currentToDieuTri?.khoaChiDinhId
          );
          const treeDatas = treeConfig?.length ? treeConfig : generateTreeDatas(t, [], true);
          let _isKhoInteracted = false;
          let _dsKhoId = [];

          if (dataHIEN_THI_CHECKBOX_TU_TRUC?.eval()) {
            _dsKhoId = dsKhoThuoc.map((x) => x.id);
            _isKhoInteracted = true;
          }

          setState({
            dsMaHoSo,
            dsKhoThuoc,
            dsKhoVatTu,
            dsKhoCpdd,
            show: true,
            toDieuTriId,
            readOnlyLoaiDichVu: false,
            treeDatas,
            khoaNbId,
            isKhoInteracted: _isKhoInteracted,
            dsChiDinhTuDichVuId: _listToDieuTri.map((i) => i.id),
            loai
          });

          form.setFieldsValue({
            dsLoaiDichVu: [90],
            tuNbDotDieuTriId: null,
            denToDieuTriId: toDieuTriId,
            disabledKho: false,
            thuocKeNgoai: true,
            thuocNhaThuoc: true,
            soLuongHuy: false,
            loaiChiDinh: 0, //mặc định loại = 0(Thường)
            vatTuXetNghiem: false,
            ...(_isKhoInteracted ? { dsKhoId: _dsKhoId } : {}),
          });
        }

        await fetchData();
      } catch (error) {
        console.error(error);
      } finally {
        hideLoading();
      }
    },
  }));

  const onClose = () => {
    form.resetFields();
    _setState(cloneDeep(initialState));
  };

  const onSaoChepThuocVT = () => {
    if (refIsSubmit.current) return;

    form.validateFields().then((values) => {
      const {
        tuNbDotDieuTriId,
        tuToDieuTriId,
        denToDieuTriId,
        dsLoaiDichVu,
        dsKhoId,
        thuocKeNgoai,
        thuocNhaThuoc,
        soLuongHuy,
        loaiChiDinh,
        vatTuXetNghiem,
        dsNbDichVuId,
      } = values;

      refIsSubmit.current = true;
      showLoading();

      let { dsLoaiDv: _dsLoaiDichVu } = parseDsLoaiDichVu(
        dsLoaiDichVu,
        treeDataLoaiDVMemo
      );

      saoChepThuoc({
        tuNbDotDieuTriId: tuNbDotDieuTriId ? tuNbDotDieuTriId : nbDotDieuTriId,
        denNbDotDieuTriId: nbDotDieuTriId,
        tuToDieuTriId,
        denToDieuTriId,
        dsLoaiDichVu: _dsLoaiDichVu,
        dsKhoId,
        nbDotDieuTriId,
        thuocKeNgoai,
        thuocNhaThuoc,
        soLuongHuy,
        loaiChiDinh,
        vatTuXetNghiem,
        ...(isArray(dsNbDichVuId, true) && { dsNbDichVuId }),
      })
        .then(() => {
          setTimeout(() => {
            history.go();
          }, 300);
        })
        .catch((e) => {
          //nếu có dv sao chép thành công => phải reload lại trang
          if (e?.dsSuccess?.length > 0) {
            setTimeout(() => {
              history.go();
            }, 1000);
          } else {
            refIsSubmit.current = false;
          }
        })
        .finally(() => {
          hideLoading();
        });
    });
  };

  const handleValuesChange = async (changedValues, allValues) => {
    if (changedValues.dsLoaiDichVu) {
      const readOnlyLoaiDichVu = !changedValues.dsLoaiDichVu.some((x) =>
        [LOAI_DICH_VU.THUOC, LOAI_DICH_VU.VAT_TU].includes(x)
      );
      setState({
        disabledKho: !changedValues.dsLoaiDichVu.some((x) =>
          [90, 100, 125].includes(x)
        ),
        readOnlyLoaiDichVu,
      });
      form.setFieldsValue({
        thuocKeNgoai: (changedValues.dsLoaiDichVu || []).includes(
          LOAI_DICH_VU.THUOC
        ),
        thuocNhaThuoc: (changedValues.dsLoaiDichVu || []).includes(
          LOAI_DICH_VU.THUOC
        ),
        loaiChiDinh: !readOnlyLoaiDichVu ? 0 : null, //mặc định loại = 0(Thường)
      });
    } else if (changedValues.hasOwnProperty("dsKhoId")) {
      setState({
        isKhoInteracted: true,
      });
    } else if (changedValues.hasOwnProperty("tuNbDotDieuTriId")) {
      setState({
        isMaHSCu:
          changedValues.tuNbDotDieuTriId &&
          changedValues.tuNbDotDieuTriId !== nbDotDieuTriId,
      });

      getToDieuTri({
        nbDotDieuTriId: changedValues.tuNbDotDieuTriId || nbDotDieuTriId,
        loai: state.loai || listLoaiToDieuTri[0].id,
      });
    } else if (changedValues.hasOwnProperty("saoChepDichVuTuNbTrongKhoa")) {
      form.setFieldsValue({
        tuNbDotDieuTriId: null,
      });
    }
  };

  return (
    <ModalTemplate
      width={500}
      closable={true}
      ref={refModal}
      title={t("quanLyNoiTru.toDieuTri.saoChepDichVu")}
      onCancel={onClose}
      actionLeft={
        <Button.QuayLai
          onClick={() => {
            onClose();
          }}
        />
      }
      actionRight={
        <Button type="primary" onClick={onSaoChepThuocVT}>
          {t("common.xacNhan")}
        </Button>
      }
    >
      <Main>
        <AlertMessage
          keyThietLap={THIET_LAP_CHUNG.TEN_CANH_BAO_POPUP_SAO_CHEP_DV}
        />
        <div className="content">
          <FormWraper
            form={form}
            style={{ width: "100%" }}
            labelAlign={"left"}
            layout="vertical"
            onValuesChange={handleValuesChange}
            initialValues={{
              dsLoaiDichVu: [90],
            }}
          >
            <Form.Item label={t("danhMuc.loaiDv")} name="dsLoaiDichVu">
              <TreeSelect
                treeData={treeDataLoaiDVMemo}
                showCheckedStrategy={SHOW_PARENT}
                treeCheckable={true}
                placeholder={t("khamBenh.chiDinh.chonLoaiDV")}
                style={{
                  width: "100%",
                }}
              />
            </Form.Item>
            {saoChepDichVuTuNbTrongKhoa ? (
              <Form.Item
                label={t("quanLyNoiTru.thongTinNb")}
                name="tuNbDotDieuTriId"
              >
                <SelectLoadMore
                  placeholder={t("baoCao.chonMaHoSo")}
                  api={nbDotDieuTriProvider.getNbNoiTru}
                  mapData={(i) => ({
                    value: i.id,
                    label: i.maHoSo,
                    ten: i.maHoSo,
                  })}
                  addParam={{
                    trangThaiTaiKhoa: 10,
                    dsTrangThai: 30,
                    dsDoiTuongKcb: [
                      DOI_TUONG_KCB.DIEU_TRI_NGOAI_TRU,
                      DOI_TUONG_KCB.DIEU_TRI_NOI_TRU,
                      DOI_TUONG_KCB.DIEU_TRI_NOI_TRU_BAN_NGAY,
                    ],
                    dsKhoaNbId: state?.khoaNbId,
                    active: true,
                  }}
                  keySearch={"maHoSo"}
                />
              </Form.Item>
            ) : (
              <Form.Item label={t("common.maHoSoCu")} name="tuNbDotDieuTriId">
                <Select
                  data={dsMaHoSoMemo}
                  placeholder={t("baoCao.chonMaHoSo")}
                />
              </Form.Item>
            )}
            <Form.Item
              label={t("quanLyNoiTru.toDieuTri.saoChepTuToDieuTriTuNgay")}
              name="tuToDieuTriId"
              rules={[
                {
                  required: true,
                  message: t("quanLyNoiTru.toDieuTri.vuiLongChonToTieuTri"),
                },
              ]}
            >
              <Select
                data={listToDieuTriMemo}
                placeholder={t("quanLyNoiTru.toDieuTri.chonToTieuTri")}
              />
            </Form.Item>

            <Form.Item
              label={t("quanLyNoiTru.toDieuTri.denToDieuTriNgay")}
              name="denToDieuTriId"
              rules={[
                {
                  required: true,
                  message: t("quanLyNoiTru.toDieuTri.vuiLongChonToTieuTri"),
                },
              ]}
            >
              <Select
                disabled={true}
                data={listToDieuTri.map((x) => ({
                  id: x.id,
                  ten: moment(x.thoiGianYLenh).isValid
                    ? moment(x.thoiGianYLenh).format("DD/MM/YYYY HH:mm")
                    : "",
                }))}
                placeholder={t("quanLyNoiTru.toDieuTri.chonToTieuTri")}
                valueNumber={true}
              />
            </Form.Item>

            <Form.Item
              label={t("kho.kho")}
              name="dsKhoId"
              rules={[
                {
                  required: !state.disabledKho,
                  message: t("quanLyNoiTru.toDieuTri.vuiLongChonKho"),
                },
              ]}
            >
              <Select
                mode="multiple"
                data={dsKhoMemo}
                disabled={state.disabledKho}
                placeholder={t("quanLyNoiTru.chonKho")}
              />
            </Form.Item>

            <Form.Item
              label={t("common.loaiChiDinh")}
              name="loaiChiDinh"
              rules={[
                {
                  required: !state.readOnlyLoaiDichVu,
                  message: t("quanLyNoiTru.cpdd.vuiLongChonLoaiChiDinh"),
                },
              ]}
            >
              <Select
                data={listLoaiChiDinh}
                disabled={state.readOnlyLoaiDichVu}
                placeholder={t("quanLyNoiTru.cpdd.chonLoaiChiDinh")}
                showArrow
              />
            </Form.Item>
            {hienThiDv && (
              <Form.Item label={t("common.dichVu")} name="dsNbDichVuId">
                <SelectLoadMore
                  api={nbDichVuProvider.searchAll}
                  mapData={(i) => ({
                    value: i.id,
                    label: `${i.maDichVu}-${i.tenDichVu}`,
                  })}
                  keySearch={"tenDichVu"}
                  placeholder={t("baoCao.chonDichVu")}
                  className="input-filter"
                  addParam={{
                    chiDinhTuLoaiDichVu: LOAI_DICH_VU.TO_DIEU_TRI,
                    dsTrangThaiHoan: DS_TRANG_THAI_HOAN,
                    nbDotDieuTriId: chiTietNguoiBenhNoiTru?.id,
                    dsChiDinhTuDichVuId: state?.dsChiDinhTuDichVuId,
                  }}
                  mode="multiple"
                />
              </Form.Item>
            )}
            <Row></Row>
            <Row>
              <Col span={12}>
                <Form.Item valuePropName="checked" name="thuocKeNgoai">
                  <Checkbox>{t("quanLyNoiTru.thuocKeNgoai")} </Checkbox>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="thuocNhaThuoc" valuePropName="checked">
                  <Checkbox>{t("quanLyNoiTru.thuocNhaThuoc")} </Checkbox>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item valuePropName="checked" name="soLuongHuy">
                  <Checkbox>{t("quanLyNoiTru.saoChepSLHuy")} </Checkbox>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  valuePropName="checked"
                  name="saoChepDichVuTuNbTrongKhoa"
                >
                  <Checkbox>
                    {t("quanLyNoiTru.saoChepDichVuTuNbTrongKhoa")}{" "}
                  </Checkbox>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item valuePropName="checked" name="hienThiDv">
                  <Checkbox>{t("quanLyNoiTru.hienThiDv")} </Checkbox>
                </Form.Item>
              </Col>
              {dsLoaiDichVu.includes(LOAI_DICH_VU.VAT_TU) && (
                <Col span={12}>
                  <Form.Item valuePropName="checked" name="vatTuXetNghiem">
                    <Checkbox>{t("common.vatTuTheoXN")} </Checkbox>
                  </Form.Item>
                </Col>
              )}
            </Row>
          </FormWraper>
        </div>
      </Main>
    </ModalTemplate>
  );
};

export default forwardRef(ModalSaoChepThuoc);
