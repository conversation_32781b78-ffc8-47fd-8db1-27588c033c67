import React, {
  forwardRef,
  memo,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from "react";
import { Row, Col, Tooltip } from "antd";
import { MainToDieuTri, SelectGroup, TextFieldGroup } from "./styled";
import { useDispatch } from "react-redux";
import moment from "moment";
import { useHistory } from "react-router-dom";
import {
  SelectLargeData,
  Select,
  TextField,
  DateTimePicker,
  Button,
  CheckField,
} from "components";
import { orderBy } from "lodash";
import {
  useCache,
  useQueryAll,
  useQueryString,
  useStore,
  useThietLap,
  useEnum,
} from "hooks";
import { useTranslation } from "react-i18next";
import { checkRole } from "lib-utils/role-utils";
import {
  CACHE_KEY,
  DS_TINH_CHAT_KHOA,
  ROLES,
  THIET_LAP_CHUNG,
  ENUM,
} from "constants/index";
import { SVG } from "assets";
import classNames from "classnames";
import { query } from "redux-store/stores";
import nbToDieuTriProvider from "data-access/nb-to-dieu-tri-provider";
import { useQuery } from "@tanstack/react-query";
import CustomTag from "pages/khamBenh/KhamCoBan/ChanDoan/ChanDoanBenh/CustomTag";
import { getDsMoTa, getMoTaChanDoan } from "utils";

const { SelectChanDoan } = SelectLargeData;

const ThongTinToDieuTri = (props, ref) => {
  const { currentToDieuTri, isReadonly, isDisableThoiGianDotDieuTri, phcnId } =
    props;
  const [dataTU_DONG_DIEN_CD_MO_TA_THEO_CAC_CD] = useThietLap(
    THIET_LAP_CHUNG.TU_DONG_DIEN_CD_MO_TA_THEO_CAC_CD,
    "FALSE"
  );

  const history = useHistory();
  const refDienBien = useRef(null);
  const isSelectNgayYLenh = useRef(false);

  const { t } = useTranslation();
  const chiTietNguoiBenhNoiTru = useStore(
    "danhSachNguoiBenhNoiTru.chiTietNguoiBenhNoiTru"
  );

  const { nhanVienId, id } = useStore("auth.auth", {});
  const [loaiToDieuTri] = useQueryString("loai", null);
  const [dataBAT_BUOC_NHAP_CHE_DO_CHAM_SOC] = useThietLap(
    THIET_LAP_CHUNG.BAT_BUOC_NHAP_CHE_DO_CHAM_SOC,
    "false"
  );
  const [dataMAC_DINH_GIO_CUA_TO_DIEU_TRI_08H] = useThietLap(
    THIET_LAP_CHUNG.MAC_DINH_GIO_CUA_TO_DIEU_TRI_08H,
    "false"
  );
  const [dataTO_DIEU_TRI_TU_LAY_CHAN_DOAN_KHI_CHUYEN_KHOA, loadFinish] =
    useThietLap(THIET_LAP_CHUNG.TO_DIEU_TRI_TU_LAY_CHAN_DOAN_KHI_CHUYEN_KHOA);
  const [dataCHO_PHEP_NHAP_MO_TA_CHAN_DOAN] = useThietLap(
    THIET_LAP_CHUNG.CHO_PHEP_NHAP_MO_TA_CHAN_DOAN,
    "TRUE"
  );

  const [dataHIEN_THI_BIEN_CHUNG_PHAU_THUAT_TO_DIEU_TRI] = useThietLap(
    THIET_LAP_CHUNG.HIEN_THI_BIEN_CHUNG_PHAU_THUAT_TO_DIEU_TRI
  );
  const [dataCHI_DINH_LOAI_CHE_DO_AN] = useThietLap(
    THIET_LAP_CHUNG.CHI_DINH_LOAI_CHE_DO_AN,
    "FALSE"
  );

  const listKhoaTheoTaiKhoan = useStore("khoa.listKhoaTheoTaiKhoan", []);
  const [listLoaiCheDoAn] = useEnum(ENUM.LOAI_CHE_DO_AN, []);

  const [khoaLamViec, _, isLoadFinish] = useCache(
    id + DS_TINH_CHAT_KHOA.NOI_TRU,
    CACHE_KEY.DATA_KHOA_LAM_VIEC,
    null,
    false
  );

  const [khoaLamViecPhcn] = useCache(
    id + DS_TINH_CHAT_KHOA.NOI_TRU,
    CACHE_KEY.DATA_KHOA_LAM_VIEC_PHCN,
    null,
    false
  );

  const [isShowFullDienBien, setShowFullDienBien] = useCache(
    id,
    CACHE_KEY.SHOW_FULL_DIEN_BIEN_TO_DIEU_TRI,
    true,
    false
  );

  const {
    toDieuTri: { updateData: updataDataToDieuTri },
  } = useDispatch();

  const [state, _setState] = useState({
    thoiGianYLenh: moment(),
    thoiGianKham: moment(),
    bacSiDieuTriId: null,
    bacSiTrucId: nhanVienId,
    dsMoTaChinh: [],
    dsMoTaKemTheo: [],
    dienBienChuyenKhoa: {
      saLechIol: null,
      rachMongMat: null,
      rotIol: null,
      rachBaoSau: null,
    },
  });

  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  const { data: listAllCheDoChamSoc } = useQueryAll(
    query.cheDoChamSoc.queryAllCheDoChamSoc
  );

  const { data: listAllMauDienBien } = useQueryAll(
    query.mauDienBien.queryAllMauDienBien({
      params: {
        dsKhoaChiDinhId: currentToDieuTri?.khoaChiDinhId || state.khoaChiDinhId,
        dsPhongId: chiTietNguoiBenhNoiTru?.phongId,
        dsBacSiChiDinhId: state.bacSiDieuTriId,
      },
    })
  );

  const { data: listBacSi } = useQueryAll(
    query.nhanVien.queryAllNhanVien({
      params: {
        dsMaThietLapVanBang: [THIET_LAP_CHUNG.BAC_SI],
      },
    })
  );

  const { data: listAllNhanVien } = useQueryAll(
    query.nhanVien.queryAllNhanVien({
      params: {
        khoaId: khoaLamViec?.id,
      },
      enabled: khoaLamViec?.id && isLoadFinish,
    })
  );

  const { data: listToDieuTri } = useQuery({
    queryKey: ["listToDieuTri", chiTietNguoiBenhNoiTru?.id],
    queryFn: () =>
      nbToDieuTriProvider.getNbToDieuTri({
        nbDotDieuTriId: chiTietNguoiBenhNoiTru?.id,
        dsKhoaChiDinhId:
          dataTO_DIEU_TRI_TU_LAY_CHAN_DOAN_KHI_CHUYEN_KHOA?.eval()
            ? undefined
            : khoaLamViec?.id,
        loai: loaiToDieuTri ?? 10,
      }),
    enabled: loadFinish && !!chiTietNguoiBenhNoiTru?.id,
    select: (res) => res?.data,
  });

  useImperativeHandle(ref, () => ({
    getData: () => {
      return state;
    },
  }));

  useEffect(() => {
    const clearHtml = (html) => {
      if (html) {
        const div = document.createElement("div");
        div.innerHTML = html;
        return div.textContent || div.innerText || "";
      }
      return html;
    };

    const getMoTaTuToDieuTri = (toDieuTri) => {
      if (toDieuTri.moTa) return toDieuTri.moTa;
      if (toDieuTri.moTaChanDoan) {
        return getMoTaChanDoan([
          getDsMoTa(toDieuTri, "dsCdChinh"),
          getDsMoTa(toDieuTri, "dsCdKemTheo"),
        ]);
      }
    };

    const getMoTaTuNbNoiTru = (chiTietNguoiBenhNoiTru) => {
      return getMoTaTuToDieuTri({
        dsCdChinh: chiTietNguoiBenhNoiTru.dsCdVaoVien,
        dsCdKemTheo: chiTietNguoiBenhNoiTru.dsCdVaoVienKemTheo,
        moTaChanDoan: {
          dsCdChinh: getDsMoTa(chiTietNguoiBenhNoiTru, "dsCdVaoVien"),
          dsCdKemTheo: getDsMoTa(chiTietNguoiBenhNoiTru, "dsCdVaoVienKemTheo"),
        },
      });
    };
    if (currentToDieuTri) {
      setState({
        cheDoAn: clearHtml(currentToDieuTri?.cheDoAn),
        bacSiTrucId: currentToDieuTri?.bacSiTrucId,
        bacSiDieuTriId: currentToDieuTri?.bacSiDieuTriId,
        boSung: currentToDieuTri?.boSung,
        ghiChu: currentToDieuTri?.ghiChu,
        dsCdKemTheoId: currentToDieuTri?.dsCdKemTheoId,
        dienBienBenh: clearHtml(currentToDieuTri?.dienBienBenh),
        moTa: currentToDieuTri?.moTa,
        dsCdChinhId: currentToDieuTri?.dsCdChinhId,
        thoiGianYLenh: moment(currentToDieuTri?.thoiGianYLenh),
        thoiGianKham: moment(currentToDieuTri?.thoiGianKham),
        cheDoChamSocId: currentToDieuTri?.cheDoChamSocId,
        tenKhoaChiDinh: currentToDieuTri?.tenKhoaChiDinh,
        giaiDoanBenh: currentToDieuTri?.giaiDoanBenh,
        cheDoTheoDoi: clearHtml(currentToDieuTri?.cheDoTheoDoi),
        dsCdPhanBietId: currentToDieuTri?.dsCdPhanBietId,
        dsCdYhctChinhId: currentToDieuTri?.dsCdYhctChinhId,
        dsCdYhctKemTheoId: currentToDieuTri?.dsCdYhctKemTheoId,
        dsMoTaChinh: getDsMoTa(currentToDieuTri, "dsCdChinh"),
        dsMoTaKemTheo: getDsMoTa(currentToDieuTri, "dsCdKemTheo"),
        dienBienChuyenKhoa: currentToDieuTri?.dienBienChuyenKhoa || {
          saLechIol: null,
          rachMongMat: null,
          rotIol: null,
          rachBaoSau: null,
        },
        ...(dataCHI_DINH_LOAI_CHE_DO_AN?.eval() && {
          loaiCheDoAn: currentToDieuTri?.loaiCheDoAn,
        }),
      });
    } else {
      let toDieuTri = orderBy(listToDieuTri, "thoiGianYLenh", "desc");
      const layGiaTriChanDoanToDieuTri =
        dataTO_DIEU_TRI_TU_LAY_CHAN_DOAN_KHI_CHUYEN_KHOA?.eval();
      if (
        toDieuTri.length &&
        (toDieuTri[0].khoaChiDinhId === chiTietNguoiBenhNoiTru?.khoaNbId ||
          layGiaTriChanDoanToDieuTri)
      ) {
        const isSameKhoa =
          toDieuTri[0].khoaChiDinhId === chiTietNguoiBenhNoiTru?.khoaNbId;
        let obj = {
          dsCdKemTheoId: toDieuTri[0]?.dsCdKemTheoId,
          dsCdChinhId: toDieuTri[0]?.dsCdChinhId,
          dsMoTaChinh: getDsMoTa(toDieuTri[0], "dsCdChinh"),
          dsMoTaKemTheo: getDsMoTa(toDieuTri[0], "dsCdKemTheo"),
          moTa: getMoTaTuToDieuTri(toDieuTri[0]),
          dsCdPhanBietId: toDieuTri[0]?.dsCdPhanBietId,
          ...(isSameKhoa && {
            dienBienBenh: clearHtml(toDieuTri[0]?.dienBienBenh),
            khoaChiDinhId: toDieuTri[0]?.khoaChiDinhId,
            bacSiDieuTriId: listBacSi
              .map((item) => item.id)
              .includes(nhanVienId)
              ? nhanVienId
              : toDieuTri[0]?.bacSiDieuTriId,
            dsCdYhctKemTheoId: toDieuTri[0]?.dsCdYhctKemTheoId,
            dsCdYhctChinhId: toDieuTri[0]?.dsCdYhctChinhId,
          }),
          ...(layGiaTriChanDoanToDieuTri &&
            !isSameKhoa && {
              bacSiDieuTriId: listBacSi
                .map((item) => item.id)
                .includes(nhanVienId)
                ? nhanVienId
                : null,
              khoaChiDinhId: chiTietNguoiBenhNoiTru?.khoaChiDinhId,
            }),
        };
        setState(obj);
      } else {
        setState({
          dsCdKemTheoId: chiTietNguoiBenhNoiTru?.dsCdVaoVienKemTheoId,
          dsCdChinhId: chiTietNguoiBenhNoiTru?.dsCdVaoVienId,
          dsMoTaChinh: getDsMoTa(chiTietNguoiBenhNoiTru, "dsCdVaoVien"),
          dsMoTaKemTheo: getDsMoTa(
            chiTietNguoiBenhNoiTru,
            "dsCdVaoVienKemTheo"
          ),
          moTa: getMoTaTuNbNoiTru(chiTietNguoiBenhNoiTru),
          bacSiDieuTriId: listBacSi.map((item) => item.id).includes(nhanVienId)
            ? nhanVienId
            : null,
          khoaChiDinhId: chiTietNguoiBenhNoiTru?.khoaChiDinhId,
          dsCdPhanBietId: chiTietNguoiBenhNoiTru?.dsCdPhanBietId,
        });
      }
    }
  }, [listToDieuTri, chiTietNguoiBenhNoiTru, listBacSi, currentToDieuTri]);
  const onClose = () => {
    if (phcnId) {
      history.push(
        `/phuc-hoi-chuc-nang/dieu-tri-phcn/${chiTietNguoiBenhNoiTru?.id}/${phcnId}?tab=1`
      );
    } else {
      history.push(
        `/quan-ly-noi-tru/chi-tiet-nguoi-benh-noi-tru/${chiTietNguoiBenhNoiTru?.id}`
      );
    }
    if (currentToDieuTri)
      updataDataToDieuTri({ toDieuTriId: currentToDieuTri?.id });
  };
  const onChangeInput = (key) => (e, dataItems) => {
    let value = "";
    if (e?.target) {
      if (e.target.hasOwnProperty("checked")) value = e.target.checked;
      else value = e.target.value;
    } else value = e;

    let addParams = {};

    if (
      dataMAC_DINH_GIO_CUA_TO_DIEU_TRI_08H?.eval() &&
      key == "thoiGianYLenh"
    ) {
      if (
        value instanceof moment &&
        value.isAfter(moment(), "day") &&
        isSelectNgayYLenh.current
      ) {
        value = value.set({
          hour: 8,
          minute: 0,
          second: 0,
        });
      }
      addParams = { thoiGianKham: value };
      // Reset flag
      isSelectNgayYLenh.current = false;
    }
    if (key == "dsCdChinhId" || key == "dsCdKemTheoId") {
      const keyMoTa = key == "dsCdChinhId" ? "dsMoTaChinh" : "dsMoTaKemTheo";
      const dsMoTa = value.map((id, index) => ({
        id: id,
        tenChanDoan: dataItems?.find((item2) => item2.id == id)?.label,
        moTa: (state[keyMoTa] || [])?.find((item) => item.id == id)?.moTa || "",
      }));
      const moTa = getMoTaChanDoan(
        key == "dsCdChinhId"
          ? [dsMoTa, state.dsMoTaKemTheo]
          : [state.dsMoTaChinh, dsMoTa]
      );
      addParams[keyMoTa] = dsMoTa;
      if (dataTU_DONG_DIEN_CD_MO_TA_THEO_CAC_CD?.eval()) addParams.moTa = moTa;
    }
    if (key === "loaiCheDoAn") {
      let cheDoAnUpdated = state?.cheDoAn;

      if ([10, 20].includes(state?.loaiCheDoAn) && [30, 40].includes(value)) {
        cheDoAnUpdated = "";
      }

      addParams.cheDoAn = cheDoAnUpdated;
    }

    setState({ [key]: value, ...addParams });
  };

  const onChangeDienBienChuyenKhoa = (field) => (e) => {
    const newValue = e.target.checked;
    setState({
      dienBienChuyenKhoa: {
        ...state.dienBienChuyenKhoa,
        [field]: newValue,
      },
    });
  };

  const onToggleButtonMoRong = () => {
    setShowFullDienBien(!isShowFullDienBien);
  };

  useEffect(() => {
    const textarea = refDienBien.current?.resizableTextArea?.textArea;
    if (!textarea) return;

    const observer = new ResizeObserver(([entry]) => {
      if (entry.target.scrollHeight > 200) {
        if (!state.isShowButtonMoRong) setState({ isShowButtonMoRong: true });
      } else {
        if (state.isShowButtonMoRong) {
          setState({ isShowButtonMoRong: false });
        }
      }
    });
    observer.observe(textarea);
    return () => observer.disconnect();
  }, [state.isShowButtonMoRong]);

  const handleSelect = () => {
    isSelectNgayYLenh.current = true;
  };

  const onChangeMoTa = (type) => (id, value) => {
    const dsMoTa = state[type] || [];
    const item = dsMoTa.find((item) => item?.id == id);
    if (!item) {
      dsMoTa.push({ id, moTa: value });
    } else {
      item.moTa = value;
    }
    if (dataTU_DONG_DIEN_CD_MO_TA_THEO_CAC_CD.eval()) {
      const moTa = getMoTaChanDoan([state.dsMoTaChinh, state.dsMoTaKemTheo]);
      setState({ [type]: [...dsMoTa], moTa });
    } else {
      setState({ [type]: [...dsMoTa] });
    }
  };

  return (
    <fieldset disabled={isReadonly} style={{ width: "100%" }}>
      <MainToDieuTri>
        <Col span={12} className="mr8">
          <Row>
            <h1
              className="to-dieu-tri-title"
              style={{ paddingRight: 20, marginBottom: 0, lineHeight: "unset" }}
            >
              {t("quanLyNoiTru.toDieuTri.title")}
            </h1>
            <div className="date">
              <span
                style={{ paddingRight: 10 }}
                className={`title ${state?.thoiGianYLenh ? "" : "validate"} `}
              >
                {t("quanLyNoiTru.toDieuTri.ngayYLenh")}{" "}
                <span style={{ color: "red", paddingLeft: "3px" }}>*</span> :
              </span>
              <DateTimePicker
                // showTime={{ format: "HH:mm:ss" }}
                showTime={false}
                format={"DD/MM/YYYY HH:mm:ss"}
                value={state?.thoiGianYLenh}
                onChange={onChangeInput("thoiGianYLenh")}
                onSelect={handleSelect}
                placeholder={t("common.chonThoiGian")}
                disabled={isDisableThoiGianDotDieuTri}
              />
            </div>
          </Row>
          <Col>
            <SelectGroup>
              <span
                className={`title ${
                  state?.dsCdChinhId?.length ? "" : "validate"
                }`}
              >
                {t("quanLyNoiTru.chanDoanBenh")}{" "}
                <span style={{ color: "red" }}> *</span>:{" "}
              </span>
              <div className="select-box-chan-doan">
                <SelectChanDoan
                  mode="multiple"
                  maxItem={1}
                  value={(state?.dsCdChinhId || []).map((item) => item + "")}
                  onChange={onChangeInput("dsCdChinhId")}
                  style={{
                    width: "100%",
                  }}
                  disabled={isReadonly}
                  tagRender={CustomTag(
                    onChangeMoTa("dsMoTaChinh"),
                    state.dsMoTaChinh
                  )}
                />
              </div>
            </SelectGroup>
          </Col>
          <Col>
            <SelectGroup>
              <span>{t("quanLyNoiTru.chanDoanKemTheo")}: </span>
              <div className="select-box-chan-doan">
                <SelectChanDoan
                  mode="multiple"
                  value={(state?.dsCdKemTheoId || []).map((item) => item + "")}
                  onChange={onChangeInput("dsCdKemTheoId")}
                  style={{
                    width: "100%",
                  }}
                  disabled={isReadonly}
                  tagRender={CustomTag(
                    onChangeMoTa("dsMoTaKemTheo"),
                    state.dsMoTaKemTheo
                  )}
                />
              </div>
            </SelectGroup>
          </Col>
          <Col>
            <SelectGroup>
              <span>{t("quanLyNoiTru.chanDoanPhanBiet")}: </span>
              <div className="select-box-chan-doan">
                <SelectChanDoan
                  mode="multiple"
                  value={(state?.dsCdPhanBietId || []).map((item) => item + "")}
                  onChange={onChangeInput("dsCdPhanBietId")}
                  style={{
                    width: "100%",
                  }}
                  disabled={isReadonly}
                />
              </div>
            </SelectGroup>
          </Col>
          <Col>
            <TextFieldGroup>
              <TextField
                label={t("quanLyNoiTru.chanDoanMoTaChiTiet")}
                className="input_custom"
                onChange={onChangeInput("moTa")}
                html={state?.moTa}
                disabled={!dataCHO_PHEP_NHAP_MO_TA_CHAN_DOAN?.eval()}
                maxLine={1}
              />
            </TextFieldGroup>
          </Col>
          <Col>
            <span className={`title ${state?.dienBienBenh ? "" : "validate"}`}>
              {t("quanLyNoiTru.dienBienBenh")}{" "}
              <span style={{ color: "red" }}> *</span>:
            </span>
            <Select
              style={{ minWidth: "200px", paddingLeft: "5px" }}
              data={listAllMauDienBien}
              onChange={(e) => {
                const mauDienBien = (listAllMauDienBien || []).find(
                  (x) => x.id === e
                );
                setState({
                  dienBienBenh: mauDienBien?.dienBien,
                  cheDoChamSocId: mauDienBien?.cheDoChamSocId,
                  cheDoAn: mauDienBien?.cheDoAn,
                });
              }}
              placeholder={t("quanLyNoiTru.chonDienBienBenh")}
              className="select-box-chan-doan"
              disabled={isReadonly}
            />
            <TextFieldGroup className="flex flex-c dien-bien-container">
              <TextField
                ref={refDienBien}
                className="input_custom dien-bien"
                onChange={onChangeInput("dienBienBenh")}
                html={state?.dienBienBenh}
                autoHeight={!!isShowFullDienBien}
              />
              {state.isShowButtonMoRong && (
                <Button.Text
                  onClick={onToggleButtonMoRong}
                  className="align-self-end btn-show-full-dien-bien"
                  type="primary"
                  leftIcon={
                    isShowFullDienBien ? <SVG.IcArrowUp /> : <SVG.IcArrowDown />
                  }
                >
                  {!isShowFullDienBien
                    ? t("common.moRong")
                    : t("common.thuNho")}
                </Button.Text>
              )}
            </TextFieldGroup>
          </Col>
        </Col>
        {/* ---------------------------------------------------------------------------------------- Right  */}
        <Col span={12} className="ml8">
          <Row>
            <Row style={{ flex: 1 }}>
              <div className="date">
                <span
                  style={{ paddingRight: 10 }}
                  className={`title ${state?.thoiGianKham ? "" : "validate"}`}
                >
                  {t("quanLyNoiTru.toDieuTri.ngayKham")}{" "}
                  <span style={{ color: "red", paddingLeft: "3px" }}>*</span>:
                </span>
                <DateTimePicker
                  // showTime={{ format: "HH:mm:ss" }}
                  showTime={false}
                  format={"DD/MM/YYYY HH:mm:ss"}
                  value={state?.thoiGianKham}
                  onChange={onChangeInput("thoiGianKham")}
                  placeholder={t("common.chonThoiGian")}
                  disabled={isReadonly}
                />
              </div>
            </Row>
            <Tooltip placement="top" title={t("common.quayLai")}>
              <SVG.IcCancel className="pointer" onClick={() => onClose()} />
            </Tooltip>
          </Row>
          {listKhoaTheoTaiKhoan
            ?.find(
              (x) =>
                x.id ==
                (loaiToDieuTri === "12" ? khoaLamViecPhcn?.id : khoaLamViec?.id)
            )
            ?.dsTinhChatKhoa?.includes(DS_TINH_CHAT_KHOA.Y_HOC_CO_TRUYEN) && (
            <Col>
              <SelectGroup>
                <span>{t("quanLyNoiTru.chanDoanBenhYhct")}: </span>
                <div className="select-box-chan-doan">
                  <SelectChanDoan
                    mode="multiple"
                    maxItem={1}
                    value={(state?.dsCdYhctChinhId || []).map(
                      (item) => item + ""
                    )}
                    onChange={onChangeInput("dsCdYhctChinhId")}
                    style={{
                      width: "100%",
                    }}
                    disabled={isReadonly}
                    isYhct={true}
                  />
                </div>
              </SelectGroup>
            </Col>
          )}
          {listKhoaTheoTaiKhoan
            ?.find(
              (x) =>
                x.id ==
                (loaiToDieuTri === "12" ? khoaLamViecPhcn?.id : khoaLamViec?.id)
            )
            ?.dsTinhChatKhoa?.includes(DS_TINH_CHAT_KHOA.Y_HOC_CO_TRUYEN) && (
            <Col>
              <SelectGroup>
                <span>{t("quanLyNoiTru.chanDoanKemTheoYhct")}: </span>
                <div className="select-box-chan-doan">
                  <SelectChanDoan
                    mode="multiple"
                    value={(state?.dsCdYhctKemTheoId || []).map(
                      (item) => item + ""
                    )}
                    onChange={onChangeInput("dsCdYhctKemTheoId")}
                    style={{
                      width: "100%",
                    }}
                    disabled={isReadonly}
                    isYhct={true}
                  />
                </div>
              </SelectGroup>
            </Col>
          )}
          <Col>
            <TextFieldGroup>
              <TextField
                label={t("tiepDon.thongTinBoSung")}
                className="input_custom"
                onChange={onChangeInput("boSung")}
                html={state?.boSung || ""}
                maxLine={1}
              />
            </TextFieldGroup>
          </Col>
          <Col>
            <TextFieldGroup>
              <TextField
                label={t("quanLyNoiTru.toDieuTri.ghiChu")}
                className="input_custom"
                onChange={onChangeInput("ghiChu")}
                html={state?.ghiChu}
                maxLine={1}
              />
            </TextFieldGroup>
          </Col>
          <Col>
            <SelectGroup>
              <span
                className={`title ${state?.bacSiDieuTriId ? "" : "validate"}`}
              >
                {t("quanLyNoiTru.bacSiDieuTri")}{" "}
                <span style={{ color: "red" }}> *</span>:{" "}
              </span>
              <Select
                value={state?.bacSiDieuTriId}
                onChange={onChangeInput("bacSiDieuTriId")}
                name="bacSiDieuTriId"
                className="select-box-chan-doan"
                data={listBacSi}
                getLabel={(item) => `${item.ma} - ${item.ten}`}
                disabled={
                  isReadonly ||
                  !checkRole([
                    ROLES["QUAN_LY_NOI_TRU"].SUA_THONG_TIN_BAC_SI_DIEU_TRI,
                  ])
                }
              />
            </SelectGroup>
          </Col>
          <Col>
            <SelectGroup>
              <span>{t("quanLyNoiTru.toDieuTri.bacSiTruc")}: </span>
              <Select
                value={state?.bacSiTrucId}
                onChange={onChangeInput("bacSiTrucId")}
                name="bacSiTrucId"
                className="select-box-chan-doan"
                data={listAllNhanVien}
                getLabel={(item) => `${item.ma} - ${item.ten}`}
                disabled={isReadonly}
              />
            </SelectGroup>
          </Col>
          <Col>
            <TextFieldGroup>
              <TextField
                label={t("quanLyNoiTru.toDieuTri.cheDoAn")}
                className="input_custom"
                onChange={onChangeInput("cheDoAn")}
                html={state?.cheDoAn}
                maxLine={1}
              />
            </TextFieldGroup>
          </Col>
          <Col>
            <TextFieldGroup>
              <TextField
                label={t("hoiChan.giaiDoanBenh")}
                className="input_custom"
                onChange={onChangeInput("giaiDoanBenh")}
                html={state?.giaiDoanBenh}
                maxLine={1}
              />
            </TextFieldGroup>
          </Col>
          <Col>
            <SelectGroup>
              <span
                className={classNames("title", {
                  validate:
                    dataBAT_BUOC_NHAP_CHE_DO_CHAM_SOC?.eval() &&
                    !state?.cheDoChamSocId,
                })}
              >
                {t("quanLyNoiTru.toDieuTri.cheDoChamSoc")}{" "}
                {dataBAT_BUOC_NHAP_CHE_DO_CHAM_SOC?.eval() && (
                  <span style={{ color: "red" }}>*</span>
                )}
                :
              </span>

              <Select
                value={state?.cheDoChamSocId}
                onChange={onChangeInput("cheDoChamSocId")}
                className="select-box-chan-doan"
                data={listAllCheDoChamSoc}
                disabled={isReadonly}
              />
            </SelectGroup>
          </Col>
          <Col>
            <TextFieldGroup>
              <TextField
                label={t("quanLyNoiTru.toDieuTri.cheDoTheoDoi")}
                className="input_custom"
                onChange={onChangeInput("cheDoTheoDoi")}
                html={state?.cheDoTheoDoi}
                maxLine={1}
                maxLength={1000}
              />
            </TextFieldGroup>
          </Col>
          {!!dataHIEN_THI_BIEN_CHUNG_PHAU_THUAT_TO_DIEU_TRI?.eval() && (
            <Col>
              <div
                style={{
                  display: "flex",
                  flexWrap: "wrap",
                  gap: "16px 24px",
                }}
              >
                <div
                  style={{
                    display: "flex",
                    alignItems: "center",
                    gap: "8px",
                  }}
                >
                  <span>{t("quanLyNoiTru.toDieuTri.saLechIol")}: </span>
                  <CheckField
                    checked={state?.dienBienChuyenKhoa?.saLechIol}
                    onChange={onChangeDienBienChuyenKhoa("saLechIol")}
                    disabled={isReadonly}
                  />
                </div>
                <div className="flex-center gap-8">
                  <span>{t("quanLyNoiTru.toDieuTri.rotIol")}: </span>
                  <CheckField
                    checked={state?.dienBienChuyenKhoa?.rotIol}
                    onChange={onChangeDienBienChuyenKhoa("rotIol")}
                    disabled={isReadonly}
                  />
                </div>
                <div className="flex-center gap-8">
                  <span>{t("quanLyNoiTru.toDieuTri.rachMongMat")}:</span>
                  <CheckField
                    checked={state?.dienBienChuyenKhoa?.rachMongMat}
                    onChange={onChangeDienBienChuyenKhoa("rachMongMat")}
                    disabled={isReadonly}
                  />
                </div>

                <div className="flex-center gap-8">
                  <span>{t("quanLyNoiTru.toDieuTri.rachBaoSau")}: </span>
                  <CheckField
                    checked={state?.dienBienChuyenKhoa?.rachBaoSau}
                    onChange={onChangeDienBienChuyenKhoa("rachBaoSau")}
                    disabled={isReadonly}
                  />
                </div>
              </div>
            </Col>
          )}

          <Col style={{ marginTop: 5 }}>
            <span>
              {t("common.khoa")}: {state?.tenKhoaChiDinh}
            </span>
          </Col>
          {dataCHI_DINH_LOAI_CHE_DO_AN?.eval() && (
            <Col>
              <SelectGroup>
                <span
                  className={classNames("title", {
                    validate: !state?.loaiCheDoAn,
                  })}
                >
                  {t("quanLyNoiTru.toDieuTri.chiDinhLoaiCheDoAn")}{" "}
                  <span style={{ color: "red" }}>*</span>:
                </span>

                <Select
                  value={state?.loaiCheDoAn}
                  onChange={onChangeInput("loaiCheDoAn")}
                  className="select-box-chan-doan"
                  data={listLoaiCheDoAn}
                />
              </SelectGroup>
            </Col>
          )}
        </Col>
      </MainToDieuTri>
    </fieldset>
  );
};
export default memo(forwardRef(ThongTinToDieuTri));
