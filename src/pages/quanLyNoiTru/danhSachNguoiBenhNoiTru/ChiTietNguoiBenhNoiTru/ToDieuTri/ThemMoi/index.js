import React, { useEffect, memo, useState, useRef, useMemo } from "react";
import { message } from "antd";
import { MainPage, Main } from "./styled";
import { useDispatch } from "react-redux";
import { useLocation, useParams, useHistory } from "react-router-dom";
import ThongTinToDieuTri from "../../ToDieuTri/ThongTinToDieuTri";
import moment from "moment";
import { Button, Card, KhoaThucHien, ThongTinBenhNhan } from "components";
import {
  CACHE_KEY,
  DATA_MODE_DS_NB,
  DS_TINH_CHAT_KHOA,
  LOAI_DANG_KY,
  THIET_LAP_CHUNG,
} from "constants/index";
import { useTranslation } from "react-i18next";
import {
  useCache,
  useConfirm,
  useLoading,
  useQueryString,
  useStore,
  useThietLap,
} from "hooks";
import { transformObjToQueryString } from "hooks/useQueryString/queryString";
import DanhSach<PERSON><PERSON>h<PERSON>han from "../../DanhSachBenhNhan";
import DanhSachBenhNhanSidePanel from "../../DanhSachBenhNhanSidePanel";
import { useCallback } from "react";
import useListDieuTriKetHop, {
  DIEU_TRI_KET_HOP_OPTIONS,
} from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/hooks/useListDieuTriKetHop";

const ThemMoiToDieuTri = (props) => {
  const { showConfirm } = useConfirm();
  const { t } = useTranslation();
  const history = useHistory();
  const [loaiPhcnId] = useQueryString("loaiPhcnId", null);
  const [loaiPhcn] = useQueryString("loaiPhcn", null);
  const [phcnId] = useQueryString("phcnId", null);
  const { state: locationState } = useLocation();
  const { nbDotDieuTriId } = useParams();
  const refThongTinToDieuTri = useRef(null);
  const refDanhSachBenhNhanSidePanel = useRef(null);

  const authId = useStore("auth.auth.id");
  const [loaiToDieuTri] = useQueryString("loai", null);

  const [modeDsNb, setModeDsNb] = useCache(
    authId,
    CACHE_KEY.DATA_MODE_DS_NB,
    DATA_MODE_DS_NB.DRAWER,
    false
  );
  const khoaId = useStore(
    "danhSachNguoiBenhNoiTru.chiTietNguoiBenhNoiTru.khoaNbId"
  );
  const dsToDieuTri = useStore("toDieuTri.listToDieuTri", []);

  const dataBAT_BUOC_NHAP_CHE_DO_CHAM_SOC =
    useThietLap(
      THIET_LAP_CHUNG.BAT_BUOC_NHAP_CHE_DO_CHAM_SOC
    )[0].toLowerCase() === "true";
  const [dataMAC_DINH_GIO_CUA_TO_DIEU_TRI_08H] = useThietLap(
    THIET_LAP_CHUNG.MAC_DINH_GIO_CUA_TO_DIEU_TRI_08H,
    "false"
  );
  const [dataCHI_DINH_LOAI_CHE_DO_AN] = useThietLap(
    THIET_LAP_CHUNG.CHI_DINH_LOAI_CHE_DO_AN,
    "FALSE"
  );

  const {
    danhSachNguoiBenhNoiTru: { clearData: clearDataNoiTru },
    toDieuTri: { getToDieuTri, createOrEdit, checkTonTaiToDieuTri },
    nbDotDieuTri: { clearData: clearDataNb },
    danhSachNguoiBenhNoiTru: { getNbNoiTruById },
  } = useDispatch();
  const { showLoading, hideLoading } = useLoading();

  const [hienThiToDieuTriTheoKhoa] = useCache(
    "",
    CACHE_KEY.DATA_HIEN_THI_TO_DIEU_TRI_THEO_KHOA,
    "1",
    false
  );

  const [state, _setState] = useState({
    khoaLamViec: {},
  });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  const { listAllDieuTriKetHop, listLoaiPhcnEnums } = useListDieuTriKetHop({
    manHinh: "NOI_TRU",
    khoaId: state.khoaLamViec?.id,
    options: DIEU_TRI_KET_HOP_OPTIONS.THEM_MOI,
  });

  useEffect(() => {
    if (nbDotDieuTriId) {
      getNbNoiTruById(nbDotDieuTriId);
    }
  }, [nbDotDieuTriId]);

  useEffect(() => {
    if (hienThiToDieuTriTheoKhoa && state.khoaLamViec?.id) {
      if (hienThiToDieuTriTheoKhoa == 1) {
        getToDieuTri({
          nbDotDieuTriId: nbDotDieuTriId,
          dsKhoaChiDinhId: state.khoaLamViec?.id,
        });
      } else {
        getToDieuTri({
          nbDotDieuTriId: nbDotDieuTriId,
        });
      }
    }
  }, [hienThiToDieuTriTheoKhoa, state.khoaLamViec?.id]);

  useEffect(() => {
    return () => {
      clearDataNb();
      clearDataNoiTru();
    };
  }, []);

  const tenLoaiPhcn = useMemo(() => {
    if (
      loaiPhcn &&
      (+loaiPhcn === LOAI_DANG_KY.PHUC_HOI_CHUC_NANG ||
        +loaiPhcn === LOAI_DANG_KY.Y_HOC_CO_TRUYEN)
    ) {
      return listLoaiPhcnEnums.find((x) => x.id === +loaiPhcn)?.ten;
    }
    if (loaiPhcnId) {
      return listAllDieuTriKetHop?.find((x) => x.id === +loaiPhcnId)?.ten;
    }
    return "";
  }, [listAllDieuTriKetHop, loaiPhcnId, loaiPhcn]);

  useEffect(() => {
    if (loaiToDieuTri === "12" && tenLoaiPhcn) {
      document.title = tenLoaiPhcn ? tenLoaiPhcn : "";
    }
  }, [loaiToDieuTri, tenLoaiPhcn]);

  const onSave = () => {
    const data = refThongTinToDieuTri.current
      ? refThongTinToDieuTri.current.getData()
      : {};

    if (!data?.thoiGianYLenh) {
      message.error(t("quanLyNoiTru.vuiLongNhapThoiGianYLenh"));
      return;
    }
    if (!data?.thoiGianKham) {
      message.error(t("quanLyNoiTru.vuiLongNhapNgayKham"));
      return;
    }
    if (!data?.dsCdChinhId?.length) {
      message.error(t("quanLyNoiTru.vuiLongNhapChanDoanBenh"));
      return;
    }
    if (!data?.bacSiDieuTriId) {
      message.error(t("quanLyNoiTru.vuiLongNhapBacSiDieuTri"));
      return;
    }
    if (!data.cheDoChamSocId && dataBAT_BUOC_NHAP_CHE_DO_CHAM_SOC) {
      message.error(t("quanLyNoiTru.vuiLongNhapCheDoChamSoc"));
      return;
    }
    if (dataMAC_DINH_GIO_CUA_TO_DIEU_TRI_08H?.eval()) {
      const _thoiGianYLenh = moment(data?.thoiGianYLenh).format(
        "YYYY-MM-DD HH:mm:ss"
      );

      if (
        dsToDieuTri.some(
          (item) =>
            moment(item.thoiGianYLenh).format("YYYY-MM-DD HH:mm:ss") ===
            _thoiGianYLenh
        )
      ) {
        message.error(
          t(
            "quanLyNoiTru.khongDuocPhepThemMoiToDieuTriCungThoiGianYLenhTrongMotKhoa"
          )
        );
        return;
      }
    }
    if (!data.dienBienBenh) {
      message.error(t("quanLyNoiTru.vuiLongNhapDienBienBenh"));
      return;
    }
    if (!data.loaiCheDoAn && dataCHI_DINH_LOAI_CHE_DO_AN?.eval()) {
      message.error(t("quanLyNoiTru.vuiLongNhapChiDinhLoaiCheDoAn"));
      return;
    }

    const payload = {
      ...data,
      nbDotDieuTriId: nbDotDieuTriId,
      khoaChiDinhId: state.khoaLamViec?.id,
      loai: loaiToDieuTri || 10, //10: Trong viện
      thoiGianYLenh:
        data?.thoiGianYLenh &&
        moment(data?.thoiGianYLenh).format("YYYY-MM-DD HH:mm:ss"),
      thoiGianKham:
        data?.thoiGianKham &&
        moment(data?.thoiGianKham).format("YYYY-MM-DD HH:mm:ss"),
    };

    const createOrEditFunc = () => {
      showLoading();
      createOrEdit({
        ...payload,
      })
        .then((s) => {
          const params = {
            loaiPhcnId,
            loaiPhcn,
          };

          const filteredParams = Object.entries(params).reduce(
            (acc, [key, val]) => {
              if (val) acc[key] = val;
              return acc;
            },
            {}
          );

          const queryParams = new URLSearchParams(filteredParams).toString();
          const queryString = queryParams
            ? `?${queryParams}&phcnId=${phcnId}`
            : "";

          history.push({
            pathname: `/quan-ly-noi-tru/chi-tiet-nguoi-benh-noi-tru/to-dieu-tri/${s?.id}${queryString}`,
            state: { isThemMoi: true },
          });
        })
        .catch(() => {})
        .finally(() => {
          hideLoading();
        });
    };

    checkTonTaiToDieuTri({
      nbDotDieuTriId,
      khoaChiDinhId: khoaId,
      tuThoiGian:
        data?.thoiGianYLenh &&
        moment(data?.thoiGianYLenh).format("YYYY-MM-DD HH:mm:ss"),
    }).then((res) => {
      if (res) {
        showConfirm(
          {
            title: t("common.canhBao"),
            content: `${t(
              "quanLyNoiTru.daCoToDieuTriXacNhanTaoTrungToDieuTri",
              {
                title: moment(data?.thoiGianYLenh).format("DD-MM-YYYY"),
              }
            )}`,
            cancelText: t("common.huy"),
            okText: t("common.xacNhan"),
            classNameOkText: "button-confirm",
            showImg: true,
            showBtnOk: true,
            typeModal: "error",
          },
          () => {
            createOrEditFunc();
          }
        );
      } else {
        createOrEditFunc();
      }
    });
  };

  const onChangeKhoaThucHien = (khoaLamViec) => {
    setState({ khoaLamViec });
  };
  const onSelectItem = useCallback((item) => {
    history.push(
      `/quan-ly-noi-tru/chi-tiet-nguoi-benh-noi-tru/${item.id}/to-dieu-tri/them-moi`
    );
  }, []);

  const openDrawerDsNb = useCallback(() => {
    refDanhSachBenhNhanSidePanel.current &&
      refDanhSachBenhNhanSidePanel.current.showDrawer({
        khoaLamViec: state.khoaLamViec,
      });
  }, [state.khoaLamViec]);

  const getBreadcrumb = ({
    tenLoaiPhcn,
    nbDotDieuTriId,
    locationState,
    pathname,
  }) => {
    if (tenLoaiPhcn) {
      return [
        {
          link: "/phuc-hoi-chuc-nang",
          title: tenLoaiPhcn ? tenLoaiPhcn : "",
        },
        {
          link: "/phuc-hoi-chuc-nang/ds-dieu-tri-phcn",
          title: tenLoaiPhcn
            ? t("phcn.dieuTriPhcnTen", { ten: tenLoaiPhcn })
            : "",
        },
        {
          link: pathname,
          title: tenLoaiPhcn
            ? t("phcn.chiTietDieuTriPhcnTen", { ten: tenLoaiPhcn })
            : "",
        },
      ];
    }

    return [
      { link: "/quan-ly-noi-tru", title: t("quanLyNoiTru.quanLyNoiTru") },
      {
        link:
          "/quan-ly-noi-tru/danh-sach-nguoi-benh-noi-tru" +
          transformObjToQueryString(locationState),
        title: t("quanLyNoiTru.danhSachNguoiBenhNoiTru"),
      },
      {
        link: `/quan-ly-noi-tru/chi-tiet-nguoi-benh-noi-tru/${nbDotDieuTriId}`,
        title: t("quanLyNoiTru.dieuTriNoiTru"),
      },
    ];
  };

  return (
    <MainPage
      breadcrumb={getBreadcrumb({
        tenLoaiPhcn,
        nbDotDieuTriId,
        locationState,
        pathname: window.location.pathname,
      })}
      title={t("quanLyNoiTru.toDieuTri.themMoiToDieuTri")}
      titleRight={
        <KhoaThucHien
          {...(loaiToDieuTri === "12" && {
            cacheKey: CACHE_KEY.DATA_KHOA_LAM_VIEC_PHCN,
          })}
          dsTinhChatKhoa={DS_TINH_CHAT_KHOA.NOI_TRU}
          allowReselectKhoa={false}
          onChange={onChangeKhoaThucHien}
          type={2}
        />
      }
      actionRight={
        <>
          <Button
            minWidth={100}
            onClick={() => {
              history.push(
                `/quan-ly-noi-tru/chi-tiet-nguoi-benh-noi-tru/${nbDotDieuTriId}`
              );
            }}
          >
            {t("common.quayLai")}
          </Button>
          <Button type="primary" minWidth={100} onClick={onSave}>
            {t("common.luu")}
          </Button>
        </>
      }
      contentRight={
        modeDsNb === DATA_MODE_DS_NB.MODULE && (
          <Card className="right-content" noPadding={true} bottom={0} top={10}>
            <DanhSachBenhNhan
              onSelectItem={onSelectItem}
              khoaLamViec={state.khoaLamViec}
              modeDsNb={modeDsNb}
              changeModeDsNb={setModeDsNb}
            />
          </Card>
        )
      }
    >
      <Main>
        <ThongTinBenhNhan
          nbDotDieuTriId={nbDotDieuTriId}
          openDrawerDsNb={openDrawerDsNb}
          modeDsNb={modeDsNb}
        />
        <Card className="content" bottom={0} top={8}>
          <ThongTinToDieuTri ref={refThongTinToDieuTri} phcnId={phcnId} />
        </Card>
      </Main>
      <DanhSachBenhNhanSidePanel
        modeDsNb={modeDsNb}
        ref={refDanhSachBenhNhanSidePanel}
        onChangeMode={setModeDsNb}
        onSelectItem={onSelectItem}
      />
    </MainPage>
  );
};

export default memo(ThemMoiToDieuTri);
