import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { Col, Menu, message, Radio, Row, Space } from "antd";
import ThongTinToDieuTri from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/ToDieuTri/ThongTinToDieuTri";
import { MainPage, Main } from "./styled";
import ChiDinhDichVu from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/ToDieuTri/ChiDinhDichVu";
import ModalInPhieu from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/Modal/ModalInPhieuTomTatBenhAn";
import {
  Popover,
  Dropdown,
  Button,
  Card,
  NextPrevController,
  ModalSignPrint,
  ThongTin<PERSON><PERSON><PERSON><PERSON><PERSON>,
  Auth<PERSON>rapper,
} from "components";
import { useParams, useHistory, useLocation } from "react-router-dom";
import { useDispatch } from "react-redux";
import moment from "moment";
import { useTranslation } from "react-i18next";
import {
  useCache,
  useConfirm,
  useGuid,
  useLoading,
  useStore,
  useThietLap,
  useQueryString,
  useQueryAll,
  useEnum,
} from "hooks";
import {
  CACHE_KEY,
  DATA_MODE_DS_NB,
  LIST_PHIEU_CHON_TIEU_CHI,
  LOAI_DICH_VU,
  MA_BIEU_MAU_EDITOR,
  ROLES,
  THIET_LAP_CHUNG,
  TRANG_THAI_NB,
  LIST_PHIEU_CHON_TIEU_CHI_IN_THEO_SO_PHIEU,
  LOAI_DANG_KY,
  DS_TINH_CHAT_KHOA,
  DOI_TUONG_KCB,
  ENUM,
} from "constants/index";
import { checkRole } from "lib-utils/role-utils";
import { ModalSaoChepThuoc } from "../../modals";
import printProvider, { printJS } from "data-access/print-provider";
import { debounce, flatten, orderBy } from "lodash";
import nbDvKyThuatProvider from "data-access/nb-dv-ky-thuat-provider";
import { SVG } from "assets";
import { isNumber, openInNewTab } from "utils";
import ModalXemKetQuaPDF from "../../modals/ModalXemKetQuaPDF";
import DanhSachBenhNhan from "../../../DanhSachBenhNhan";
import { transformObjToQueryString } from "hooks/useQueryString/queryString";
import ModalChonKhoaPhieuTruyenDich from "../../modals/ModalChonKhoaPhieuTruyenDich";
import ModalChonTieuChi from "../../../ModalChonTieuChi";
import ModalChonKetQuaXetNghiem from "../../modals/ModalChonKetQuaXetNghiem";
import ModalChonPhieuCongKhaiThuoc from "../../modals/ModalChonPhieuCongKhaiThuoc";
import { checkIsPhieuKySo } from "utils/phieu-utils";
import ModalChiDinhPhacDo from "pages/chiDinhDichVu/PhacDoDieuTri/ModalDanhSachPhacDo";
import ModalChonLoaiPHCN from "../../../Modal/ModalChonLoaiPHCN";
import { TRANG_THAI_PHIEU_NUMBER } from "pages/quanLyBaoCaoAdr/config";
import DanhSachBenhNhanSidePanel from "../../../DanhSachBenhNhanSidePanel";
import ModalChonPhieuCanThiepDuoc from "pages/kho/DuyetDuocLamSang/ChiTietDuyetDuocLamSang/modal/ModalChonPhieuCanThiepDuoc";
import { createUniqueText } from "lib-utils";
import ModalChonPhieuThuPhanUngThuoc from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/Modal/ModalChonPhieuThuPhanUngThuoc";
import ModalChonLoaiDangKyKetHopDieuTri from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/Modal/ModalChonLoaiDangKyKetHopDieuTri";
import ModalXoaToDieuTri from "../../modals/ModalXoaToDieuTri";
import ModalInChiDinhTheoDV from "pages/khamBenh/components/StepWrapper/ModalInChiDinhTheoDV";
import { showError } from "utils/message-utils";
import useListDieuTriKetHop, {
  DIEU_TRI_KET_HOP_OPTIONS,
} from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/hooks/useListDieuTriKetHop";
import { useKiemTraChongChiDinh } from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/hooks/useKiemTraChongChiDinh";

const ChiTietToDieuTri = () => {
  const { id } = useParams();
  const history = useHistory();
  const { state: locationState = {} } = useLocation();

  const { t } = useTranslation();
  const refModalSaoChepThuoc = useRef(null);
  const refModalInChiDinhTheoDV = useRef(null);
  const refModalXemKetQuaPDF = useRef(null);
  const refModalChonPhieuTruyenDich = useRef(null);
  const refModalChonTieuChi = useRef(null);
  const refModalChonKetQuaXetNghiem = useRef(null);
  const refModalChonPhieuCongKhaiThuoc = useRef(null);
  const refHeaderLeft = useRef(null);
  const refModalSignPrint = useRef(null);
  const refChiDinhPhacDo = useRef(null);
  const refModalChonLoaiPHCN = useRef(null);
  const refDanhSachBenhNhanSidePanel = useRef(null);
  const refModalChonPhieuCanThiepDuoc = useRef(null);
  const refModalPhieuThuPhanUngThuoc = useRef(null);
  const refModalDangKyKetHop = useRef(null);
  const refModalInPhieu = useRef(null);
  const refModalXoaToDieuTri = useRef(null);
  const layerId = useGuid();
  const { showConfirm } = useConfirm();

  const { showLoading, hideLoading } = useLoading();
  const { isThemMoi, ...location } = locationState;
  const {
    danhSachNguoiBenhNoiTru: { getNbNoiTruById, getDanhSachMuonNb },
    maBenh: { getListAllMaBenh },
    toDieuTri: {
      getToDieuTri,
      getToDieuTriById,
      patch,
      updateData,
      onDelete,
      checkTonTaiToDieuTri,
    },
    khamBenh: { getTatCaGiayChiDinh },
    chiDinhVatTu: { getListDichVuVatTu },
    chiDinhHoaChat: { getListDichVuHoaChat },
    phieuIn: {
      getFilePhieuIn,
      getListPhieu,
      showFileEditor,
      getDataDanhSachPhieu,
    },
    chiDinhSuatAn: { getDsSuatAn },
    chiDinhMau: { getListChePhamMau },
    chiDinhDichVuKho: { getListDichVuThuoc },
    chiDinhKhamBenh: {
      getDsDichVuChiDinhXN,
      getDsDichVuChiDinhKham,
      getDsDichVuChiDinhCLS,
      getDsDichVuNgoaiDieuTri,
    },
    quanLyNoiTru: { getTieuChi },
    nbPhacDoDieuTri: { onChangeInputSearch },
    phacDoDieuTri: { onChangeInputSearch: onSearchPhacDo },
    dieuTriPHCN: { getListDieuTriPHCNByNb, huyDangKyDotPHCN, dangKyDotPHCN },
    quanLyBaoCaoAdr: { taoMoiBaoCaoAdr, searchBaoCaoByMaHoSo },
    phimTat: { onAddLayer, onRemoveLayer },
    dsPhieuThuChotDotDieuTri: { getListAllPhieuThuChotDotDieuTri },
  } = useDispatch();

  const authId = useStore("auth.auth.id");
  const nhanVienId = useStore("auth.auth.nhanVienId");
  const refreshListPhieu = useStore("phieuIn.refreshListPhieu");
  const currentToDieuTri = useStore("toDieuTri.currentToDieuTri");
  const _listToDieuTri = useStore("toDieuTri.listToDieuTri", []);
  const listToDieuTri = _listToDieuTri.filter((i) => i.loai === 10);
  const listDataByNb = useStore("dieuTriPHCN.listDataByNb", []);
  const chiTietNguoiBenhNoiTru = useStore(
    "danhSachNguoiBenhNoiTru.chiTietNguoiBenhNoiTru"
  );
  const listAllPhieuThuChotDot = useStore(
    "dsPhieuThuChotDotDieuTri.listAllPhieuThuChotDot",
    []
  );
  const listDvSuatAn = useStore("chiDinhSuatAn.listDvSuatAn", []);
  const listDvCPDD = useStore("chiDinhChePhamDinhDuong.listDvCPDD", []);

  const [listLoaiPhucHoiCn] = useEnum(ENUM.LOAI_PHUC_HOI_CN);

  const [GOI_Y_PHAC_DO_TO_DIEU_TRI_SUA_CDCHINH] = useThietLap(
    THIET_LAP_CHUNG.GOI_Y_PHAC_DO_TO_DIEU_TRI_SUA_CDCHINH
  );
  const [GOI_Y_PHAC_DO_TO_DIEU_TRI_LUU_MOI_CDCHINH, loadFinish] = useThietLap(
    THIET_LAP_CHUNG.GOI_Y_PHAC_DO_TO_DIEU_TRI_LUU_MOI_CDCHINH
  );

  const [dataBAT_BUOC_NHAP_CHE_DO_CHAM_SOC] = useThietLap(
    THIET_LAP_CHUNG.BAT_BUOC_NHAP_CHE_DO_CHAM_SOC
  );
  const [dataBO_BAT_BUOC_CHON_PHAN_LOAI_KHI_DANG_KY_PHCN] = useThietLap(
    THIET_LAP_CHUNG.BO_BAT_BUOC_CHON_PHAN_LOAI_KHI_DANG_KY_PHCN,
    ""
  );
  const [dataMAC_DINH_IN_DV_CHUA_THANH_TOAN_PHIEU_CHI_DINH_TONG_HOP] =
    useThietLap(
      THIET_LAP_CHUNG.MAC_DINH_IN_DV_CHUA_THANH_TOAN_PHIEU_CHI_DINH_TONG_HOP,
      "FALSE"
    );
  const [dataCHI_DINH_LOAI_CHE_DO_AN] = useThietLap(
    THIET_LAP_CHUNG.CHI_DINH_LOAI_CHE_DO_AN,
    "FALSE"
  );

  const [khoaLamViec] = useCache(
    authId + DS_TINH_CHAT_KHOA.NOI_TRU,
    CACHE_KEY.DATA_KHOA_LAM_VIEC,
    null,
    false
  );

  const {
    listAllDieuTriKetHopTheoLoai,
    listAllDieuTriKetHopTheoKhoa,
    listLoaiPhcnEnums,
  } = useListDieuTriKetHop({
    manHinh: "NOI_TRU",
    khoaId: khoaLamViec?.id,
    enabled: !!currentToDieuTri?.nbDotDieuTriId,
    options: DIEU_TRI_KET_HOP_OPTIONS.CHI_TIET_TO_DIEU_TRI,
  });

  const [hienThiToDieuTriTheoKhoa] = useCache(
    "",
    CACHE_KEY.DATA_HIEN_THI_TO_DIEU_TRI_THEO_KHOA,
    "1",
    false
  );
  const [valuePhieuChiDinh, setValuePhieuChiDinh] = useCache(
    "",
    CACHE_KEY.DATA_TO_DIEU_TRI_OPTION_IN_PHIEU_CHI_DINH,
    dataMAC_DINH_IN_DV_CHUA_THANH_TOAN_PHIEU_CHI_DINH_TONG_HOP?.eval() ? 4 : 1
  );
  const [valuePhieuChiDinhTH, setValuePhieuChiDinhTH] = useCache(
    "",
    CACHE_KEY.DATA_TO_DIEU_TRI_OPTION_IN_PHIEU_CHI_DINH_TONG_HOP,
    dataMAC_DINH_IN_DV_CHUA_THANH_TOAN_PHIEU_CHI_DINH_TONG_HOP?.eval() ? 4 : 2,
    false
  );

  const [modeDsNb, setModeDsNb] = useCache(
    authId,
    CACHE_KEY.DATA_MODE_DS_NB,
    DATA_MODE_DS_NB.DRAWER,
    false
  );
  const [dataNHOM_GIAI_PHAU_BENH] = useThietLap(
    THIET_LAP_CHUNG.NHOM_GIAI_PHAU_BENH
  );

  const [loaiPhcnId] = useQueryString("loaiPhcnId", null);
  const [loaiPhcn] = useQueryString("loaiPhcn", null);
  const [phcnId] = useQueryString("phcnId", null);

  const [title, setTitle] = useState(t("quanLyNoiTru.toDieuTri.title"));
  const [state, _setState] = useState({
    popoverVisible: false,
    nbPhieuAdr: null,
  });

  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };
  const refThongTinToDieuTri = useRef(null);

  const { listDataPHCN, listDataYHCT } = useMemo(() => {
    let listDataPHCN = [];
    let listDataYHCT = [];
    if (listDataByNb.length > 0) {
      listDataPHCN = listDataByNb.filter(
        (x) => x.loai === LOAI_DANG_KY.PHUC_HOI_CHUC_NANG
      );
      listDataYHCT = listDataByNb.filter(
        (x) => x.loai === LOAI_DANG_KY.Y_HOC_CO_TRUYEN
      );
    }
    return {
      listDataPHCN,
      listDataYHCT,
    };
  }, [listDataByNb]);

  const { checkChongChiDinh } = useKiemTraChongChiDinh(chiTietNguoiBenhNoiTru);

  useEffect(() => {
    getListAllMaBenh({ active: true, page: "", size: "" });
  }, []);

  useEffect(() => {
    onAddLayer({ layerId });
    return () => {
      onRemoveLayer({ layerId });
    };
  }, []);

  useEffect(() => {
    if (
      isThemMoi &&
      loadFinish &&
      currentToDieuTri?.nbDotDieuTriId &&
      GOI_Y_PHAC_DO_TO_DIEU_TRI_LUU_MOI_CDCHINH?.eval()
    ) {
      onSearchPhacDo({
        maBenhId: currentToDieuTri.dsCdChinhId[0],
        active: true,
      }).then((s) => {
        if (s?.length) {
          refChiDinhPhacDo.current &&
            refChiDinhPhacDo.current.show(
              {
                maBenhId:
                  currentToDieuTri.dsCdChinhId[0] &&
                  Number(currentToDieuTri.dsCdChinhId[0]),
                nbDotDieuTriId: currentToDieuTri?.nbDotDieuTriId,
                chiDinhTuLoaiDichVu: LOAI_DICH_VU.TO_DIEU_TRI,
                chiDinhTuDichVuId: currentToDieuTri?.id,
              },
              () => {
                onChangeInputSearch({
                  nbDotDieuTriId: currentToDieuTri?.nbDotDieuTriId,
                });
              }
            );
        }
      });
    }
  }, [loadFinish, currentToDieuTri]);

  useEffect(() => {
    if (id) {
      getToDieuTriById(id);
    }
  }, [id]);
  useEffect(() => {
    if (currentToDieuTri?.nbDotDieuTriId) {
      getNbNoiTruById(currentToDieuTri?.nbDotDieuTriId);
      getDanhSachMuonNb({
        khoaId: currentToDieuTri?.khoaChiDinhId,
        nbDotDieuTriId: currentToDieuTri?.nbDotDieuTriId,
      });
      getListAllPhieuThuChotDotDieuTri({
        page: "",
        size: "",
        nbDotDieuTriId: currentToDieuTri?.nbDotDieuTriId,
        chotDotDieuTri: true,
      });
    }
  }, [currentToDieuTri]);

  useEffect(() => {
    inGiayTo();
  }, [refreshListPhieu]);

  useEffect(() => {
    if (currentToDieuTri?.nbDotDieuTriId) {
      if (hienThiToDieuTriTheoKhoa == 1 && khoaLamViec?.id) {
        getToDieuTri({
          nbDotDieuTriId: currentToDieuTri?.nbDotDieuTriId,
          dsKhoaChiDinhId: khoaLamViec?.id,
          loai: 10,
        });
      } else {
        getToDieuTri({
          nbDotDieuTriId: currentToDieuTri?.nbDotDieuTriId,
          loai: 10,
        });
      }
    }
  }, [hienThiToDieuTriTheoKhoa, currentToDieuTri, khoaLamViec]);

  useEffect(() => {
    if (chiTietNguoiBenhNoiTru?.maHoSo) {
      getListDieuTriPHCNByNb({
        page: "",
        size: "",
        maHoSo: chiTietNguoiBenhNoiTru.maHoSo,
      });
    }
  }, [chiTietNguoiBenhNoiTru?.maHoSo]);

  useEffect(() => {
    if (currentToDieuTri?.id && chiTietNguoiBenhNoiTru?.maHoSo) {
      searchBaoCaoByMaHoSo({
        maHoSo: chiTietNguoiBenhNoiTru?.maHoSo,
        chiDinhTuDichVuId: currentToDieuTri?.id,
      }).then((res) => {
        if (res.length > 0) {
          setState({ nbPhieuAdr: res[0] });
        } else {
          setState({ nbPhieuAdr: null });
        }
      });
    }
  }, [chiTietNguoiBenhNoiTru?.maHoSo, currentToDieuTri?.id]);

  const tenLoaiPhcn = useMemo(() => {
    if (
      loaiPhcn &&
      (+loaiPhcn === LOAI_DANG_KY.PHUC_HOI_CHUC_NANG ||
        +loaiPhcn === LOAI_DANG_KY.Y_HOC_CO_TRUYEN)
    ) {
      return listLoaiPhcnEnums.find((x) => x.id === +loaiPhcn)?.ten;
    }
    if (loaiPhcnId) {
      return listAllDieuTriKetHopTheoLoai?.find((x) => x.id === +loaiPhcnId)
        ?.ten;
    }
    return "";
  }, [listAllDieuTriKetHopTheoLoai, loaiPhcnId, loaiPhcn]);

  useEffect(() => {
    if (tenLoaiPhcn) {
      document.title = tenLoaiPhcn ?? "";
    }
  }, [tenLoaiPhcn]);

  const getListAllDichVu = () => {
    getListDichVuVatTu({
      nbDotDieuTriId: currentToDieuTri?.nbDotDieuTriId,
      chiDinhTuDichVuId: currentToDieuTri?.id,
      chiDinhTuLoaiDichVu: LOAI_DICH_VU.TO_DIEU_TRI,
    });
    getListDichVuHoaChat({
      nbDotDieuTriId: currentToDieuTri?.nbDotDieuTriId,
      chiDinhTuDichVuId: currentToDieuTri?.id,
      chiDinhTuLoaiDichVu: LOAI_DICH_VU.TO_DIEU_TRI,
    });
    getDsSuatAn({
      nbDotDieuTriId: currentToDieuTri?.nbDotDieuTriId,
      chiDinhTuDichVuId: currentToDieuTri?.id,
      chiDinhTuLoaiDichVu: LOAI_DICH_VU.TO_DIEU_TRI,
    });
    getListChePhamMau({
      nbDotDieuTriId: currentToDieuTri.nbDotDieuTriId,
      chiDinhTuDichVuId: currentToDieuTri?.id,
      chiDinhTuLoaiDichVu: LOAI_DICH_VU.TO_DIEU_TRI,
      dsTrangThaiHoan: [0, 10, 20],
    });
    getListDichVuThuoc({
      nbDotDieuTriId: currentToDieuTri.nbDotDieuTriId,
      chiDinhTuDichVuId: currentToDieuTri?.id,
      chiDinhTuLoaiDichVu: LOAI_DICH_VU.TO_DIEU_TRI,
      dsTrangThaiHoan: [0, 10, 20],
    });
    getDsDichVuChiDinhXN();
    getDsDichVuChiDinhKham();
    getDsDichVuChiDinhCLS();
    getDsDichVuNgoaiDieuTri();
  };

  const currentIndex = useMemo(() => {
    return (
      (listToDieuTri.findIndex((item) => {
        return item?.id == currentToDieuTri?.id;
      }) || 0) + 1
    );
  }, [currentToDieuTri, listToDieuTri]);

  const { dieuKienPHCN, dieuKienYHCT } = useMemo(() => {
    const checkLoaiData = (listData) => {
      let dangKy = true,
        huyDangKy = false;
      let idHuyDangKy = null;

      //đã có đợt phcn => ẩn nút đăng ký
      if (listData.length > 0) {
        //update 5/4/2023: NB chưa có bản ghi PHCN nào hoặc tồn tại các bản ghi PHCN và trạng thái tất cả bản ghi = Đã hủy (20) (Ngoài các TH trên thì ko hiển thị button Đăng ký)
        //bổ sung thêm: trạng thái != hoàn thành (40)
        if (listData.some((x) => x.trangThai != 20 && x.trangThai != 40)) {
          dangKy = false;
        }

        //update 5/4/2023: Truyền id bản ghi PHCN có stt nhỏ nhất , trạng thái # Đã hủy (20)
        //bổ sung thêm: trạng thái != hoàn thành (40)
        const _listDangKy = orderBy(
          listData.filter((x) => x.trangThai != 20 && x.trangThai != 40) || [],
          "stt",
          "asc"
        );
        if (_listDangKy.length > 0) {
          huyDangKy = true;
          idHuyDangKy = _listDangKy[0].id;
        }
      }

      if (
        chiTietNguoiBenhNoiTru.trangThai < 5 ||
        chiTietNguoiBenhNoiTru.trangThai > 50
      ) {
        dangKy = false;
        huyDangKy = false;
        idHuyDangKy = null;
      }
      return {
        dangKy,
        huyDangKy,
        idHuyDangKy,
      };
    };

    const dieuKienPHCN = checkLoaiData(listDataPHCN);
    const dieuKienYHCT = checkLoaiData(listDataYHCT);
    return {
      dieuKienPHCN,
      dieuKienYHCT,
    };
  }, [listDataPHCN, listDataYHCT, chiTietNguoiBenhNoiTru.trangThai]);
  const onChangeToDieuTri = (index) => {
    const toDieuTri = listToDieuTri[index - 1];
    history.push(
      "/quan-ly-noi-tru/chi-tiet-nguoi-benh-noi-tru/to-dieu-tri/" +
        toDieuTri.id +
        history.location.search
    );
  };

  const onSave = () => {
    const data = refThongTinToDieuTri.current
      ? refThongTinToDieuTri.current.getData()
      : {};
    if (!data?.dsCdChinhId?.length) {
      message.error(t("quanLyNoiTru.vuiLongNhapChanDoanBenh"));
      return;
    }
    if (!data?.thoiGianYLenh) {
      message.error(t("quanLyNoiTru.vuiLongNhapThoiGianYLenh"));
      return;
    }
    if (!data?.thoiGianKham) {
      message.error(t("quanLyNoiTru.vuiLongNhapNgayKham"));
      return;
    }
    if (!data?.bacSiDieuTriId) {
      message.error(t("quanLyNoiTru.vuiLongNhapBacSiDieuTri"));
      return;
    }
    if (!data.cheDoChamSocId && dataBAT_BUOC_NHAP_CHE_DO_CHAM_SOC.eval()) {
      message.error(t("quanLyNoiTru.vuiLongNhapCheDoChamSoc"));
      return;
    }
    if (!data.dienBienBenh) {
      message.error(t("quanLyNoiTru.vuiLongNhapDienBienBenh"));
      return;
    }
    if (!data.loaiCheDoAn && dataCHI_DINH_LOAI_CHE_DO_AN?.eval()) {
      message.error(t("quanLyNoiTru.vuiLongNhapChiDinhLoaiCheDoAn"));
      return;
    }

    let cheDoAnUpdated;

    if (
      dataCHI_DINH_LOAI_CHE_DO_AN?.eval() &&
      [10, 20].includes(data?.loaiCheDoAn)
    ) {
      const mergedSuatAn =
        Array.isArray(listDvSuatAn) && listDvSuatAn.length > 0
          ? listDvSuatAn
              .map(
                (item) =>
                  `${item.tenDichVu}${item.ghiChu ? ` (${item.ghiChu})` : ""}`
              )
              .join("; ")
          : "";

      const mergedCPDD =
        Array.isArray(listDvCPDD) && listDvCPDD.length > 0
          ? listDvCPDD
              .map(
                (item) =>
                  `${item.tenDichVu}${item.ghiChu ? ` (${item.ghiChu})` : ""}`
              )
              .join("; ")
          : "";

      cheDoAnUpdated = [mergedSuatAn, mergedCPDD].filter(Boolean).join("; ");
    }

    const payload = {
      ...data,
      ...(cheDoAnUpdated ? { cheDoAn: cheDoAnUpdated } : {}),
      nbDotDieuTriId: currentToDieuTri?.nbDotDieuTriId,
      id: id,
      khoaChiDinhId: currentToDieuTri?.khoaNbId,
      thoiGianYLenh:
        data?.thoiGianYLenh &&
        moment(data?.thoiGianYLenh).format("YYYY-MM-DD HH:mm:ss"),
      thoiGianKham: moment(data?.thoiGianKham).format("YYYY-MM-DD HH:mm:ss"),
      moTaChanDoan: {
        dsCdChinh: data.dsMoTaChinh || [],
        dsCdKemTheo: data.dsMoTaKemTheo || [],
      },
    };

    const saveFunc = () => {
      showLoading();
      patch({
        ...payload,
      })
        .then(() => {
          getToDieuTriById(id);
          if (
            data?.thoiGianYLenh &&
            currentToDieuTri.thoiGianYLenh &&
            moment(data?.thoiGianYLenh).diff(
              moment(currentToDieuTri?.thoiGianYLenh)
            ) !== 0
          ) {
            getListAllDichVu();
          }
          if (
            currentToDieuTri.dsCdChinhId[0] !== data.dsCdChinhId[0] &&
            GOI_Y_PHAC_DO_TO_DIEU_TRI_SUA_CDCHINH?.eval()
          ) {
            onSearchPhacDo({
              maBenhId: data.dsCdChinhId[0],
              active: true,
            }).then((s) => {
              if (s?.length) {
                refChiDinhPhacDo.current &&
                  refChiDinhPhacDo.current.show(
                    {
                      maBenhId:
                        data.dsCdChinhId[0] && Number(data.dsCdChinhId[0]),
                      nbDotDieuTriId: currentToDieuTri?.nbDotDieuTriId,
                      chiDinhTuLoaiDichVu: LOAI_DICH_VU.TO_DIEU_TRI,
                      chiDinhTuDichVuId: currentToDieuTri?.id,
                    },
                    () => {
                      onChangeInputSearch({
                        nbDotDieuTriId: currentToDieuTri?.nbDotDieuTriId,
                      });
                    }
                  );
              }
            });
          }
        })
        .catch(() => {})
        .finally(() => {
          hideLoading();
        });
    };

    checkTonTaiToDieuTri({
      nbDotDieuTriId: currentToDieuTri?.nbDotDieuTriId,
      khoaChiDinhId: currentToDieuTri?.khoaChiDinhId,
      chiDinhTuDichVuId: id,
      tuThoiGian:
        data?.thoiGianYLenh &&
        moment(data?.thoiGianYLenh).format("YYYY-MM-DD HH:mm:ss"),
    }).then((res) => {
      if (res) {
        showConfirm(
          {
            title: t("common.canhBao"),
            content: `${t(
              "quanLyNoiTru.daCoToDieuTriXacNhanTaoTrungToDieuTri",
              {
                title: moment(data?.thoiGianYLenh).format("DD-MM-YYYY"),
              }
            )}`,
            cancelText: t("common.huy"),
            okText: t("common.xacNhan"),
            classNameOkText: "button-confirm",
            showImg: true,
            showBtnOk: true,
            typeModal: "error",
          },
          () => {
            saveFunc();
          }
        );
      } else {
        saveFunc();
      }
    });
  };

  const onClose = () => {
    updateData({ activeKey: "2" });
    history.push(
      `/quan-ly-noi-tru/chi-tiet-nguoi-benh-noi-tru/${currentToDieuTri?.nbDotDieuTriId}`
    );
  };

  const onDeleteToDieuTri = () => {
    refModalXoaToDieuTri.current &&
      refModalXoaToDieuTri.current.show(
        {
          nbDotDieuTriId: currentToDieuTri?.nbDotDieuTriId,
          id: currentToDieuTri?.id,
        },
        () => {
          onDelete(currentToDieuTri.id).then(() => {
            history.push(
              `/quan-ly-noi-tru/chi-tiet-nguoi-benh-noi-tru/${currentToDieuTri?.nbDotDieuTriId}?tab=2`
            );
          });
        }
      );
    // showConfirm(
    //   {
    //     title: t("common.thongBao"),
    //     content: `${t("quanLyNoiTru.toDieuTri.banChacChanMuonXoaToDieuTri")}`,
    //     cancelText: t("common.huy"),
    //     okText: t("common.dongY"),
    //     classNameOkText: "button-warning",
    //     showBtnOk: true,
    //     typeModal: "error",
    //   },
    //   () => {
    //     onDelete(currentToDieuTri.id).then(() => {
    //       history.push(
    //         `/quan-ly-noi-tru/chi-tiet-nguoi-benh-noi-tru/${currentToDieuTri?.nbDotDieuTriId}?tab=2`
    //       );
    //     });
    //   }
    // );
  };

  const isThoiGianDotDieuTri = useMemo(() => {
    if (listAllPhieuThuChotDot.length === 0) return true;
    return listAllPhieuThuChotDot.every((item) =>
      moment(currentToDieuTri?.thoiGianYLenh).isAfter(
        moment(item?.thoiGianChotDotDieuTri)
      )
    );
  }, [currentToDieuTri, listAllPhieuThuChotDot]);

  const isDisableEditForm = useMemo(() => {
    return (
      (chiTietNguoiBenhNoiTru?.khoaNbId !== khoaLamViec?.id &&
        !checkRole([ROLES["QUAN_LY_NOI_TRU"].THAO_TAC_NB_KHAC_KHOA])) ||
      (currentToDieuTri?.khoaChiDinhId !== khoaLamViec?.id &&
        !checkRole([ROLES["QUAN_LY_NOI_TRU"].THAO_TAC_NB_KHAC_KHOA])) ||
      ([
        TRANG_THAI_NB.DANG_CHUYEN_KHOA,
        TRANG_THAI_NB.HEN_DIEU_TRI,
        TRANG_THAI_NB.DA_RA_VIEN,
        TRANG_THAI_NB.DA_THANH_TOAN_RA_VIEN,
        TRANG_THAI_NB.DA_THANH_TOAN_HEN_DIEU_TRI,
      ].includes(chiTietNguoiBenhNoiTru?.trangThai) &&
        !checkRole([ROLES["QUAN_LY_NOI_TRU"].THAO_TAC_NB_DA_RA_VIEN]))
    );
  }, [chiTietNguoiBenhNoiTru, khoaLamViec]);

  const isReadonly = useMemo(() => {
    return isDisableEditForm || !isThoiGianDotDieuTri;
  }, [isDisableEditForm, isThoiGianDotDieuTri]);

  const onSaoChepThuocVT = () => {
    refModalSaoChepThuoc.current &&
      refModalSaoChepThuoc.current.show({
        toDieuTriId: id,
        khoaNbId: khoaLamViec?.id,
      });
  };

  const inGiayTo = () => {
    if (currentToDieuTri?.nbDotDieuTriId)
      getListPhieu({
        nbDotDieuTriId: currentToDieuTri.nbDotDieuTriId,
        maManHinh: "007",
        maViTri: "00701",
        chiDinhTuDichVuId: id,
        dsChiDinhTuLoaiDichVu: LOAI_DICH_VU.TO_DIEU_TRI,
      }).then((listPhieu) => {
        setState({ listPhieu: listPhieu });
      });
  };
  const onPrintPhieu = (item) => async () => {
    if (item.key == 0) {
    } else {
      if (item.type == "editor") {
        let mhParams = {};
        //kiểm tra phiếu ký số
        if (checkIsPhieuKySo(item)) {
          //nếu là ký số thì lấy các thông tin về lịch sử ký + tên chân ký + báo cáo id
          mhParams = {
            nbDotDieuTriId: currentToDieuTri.nbDotDieuTriId,
            maManHinh: "007",
            maViTri: "00701",
            chiDinhTuDichVuId: id,
            dsChiDinhTuLoaiDichVu: LOAI_DICH_VU.TO_DIEU_TRI,
            kySo: true,
            maPhieuKy: item.ma,
            baoCaoId: item.baoCaoId,
          };
        }
        let lichSuKyId = item?.dsSoPhieu?.length
          ? item?.dsSoPhieu[0].lichSuKyId
          : "";
        if (item?.dsSoPhieu?.length > 1) {
          lichSuKyId = item.dsSoPhieu.find(
            (x) => x.soPhieu == (id || currentToDieuTri.nbDotDieuTriId)
          )?.lichSuKyId;

          mhParams = { ...mhParams, lichSuKyId };
        }
        if (LIST_PHIEU_CHON_TIEU_CHI.includes(item.ma)) {
          const dataSearch = {
            nbDotDieuTriId: currentToDieuTri?.nbDotDieuTriId,
            dataNHOM_GIAI_PHAU_BENH,
            chiDinhTuDichVuId: currentToDieuTri?.id,
          };
          let dsTieuChi = await getTieuChi({
            maBaoCao: item.ma,
            dataSearch,
          });
          dsTieuChi = dsTieuChi.map((item) => {
            return {
              id: item.id,
              ten: (
                <span
                  title={`${item.maDichVu}-${item.tenDichVu}-${moment(
                    item.thoiGianThucHien
                  ).format("DD/MM/YYYY")}`}
                >
                  <b>
                    {item.maDichVu}-{item.tenDichVu}
                  </b>
                  -{moment(item.thoiGianThucHien).format("DD/MM/YYYY")}
                </span>
              ),
              value: item.id,
            };
          });
          refModalChonTieuChi &&
            refModalChonTieuChi.current.show(
              { data: dsTieuChi },
              (idTieuChi) => {
                showFileEditor({
                  phieu: item,
                  nbDotDieuTriId: currentToDieuTri?.nbDotDieuTriId,
                  nbDvXetNgiemId: idTieuChi,
                  ma: item.ma,
                  id: idTieuChi,
                  lichSuKyId,
                  mhParams,
                });
              }
            );
        } else if (
          LIST_PHIEU_CHON_TIEU_CHI_IN_THEO_SO_PHIEU.includes(item.ma)
        ) {
          let dsTieuChi = item.dsSoPhieu || [];
          dsTieuChi = dsTieuChi.map((item) => {
            return {
              id: item.soPhieu,
              ten: (
                <span
                  title={`${item.ten1}-${item.ten2}-${moment(
                    item.thoiGianThucHien
                  ).format("DD/MM/YYYY")}`}
                >
                  <b>
                    {item.ten1}-{item.ten2}
                  </b>
                  -{moment(item.thoiGianThucHien).format("DD/MM/YYYY")}
                </span>
              ),
              value: item.soPhieu,
              uniqueText: createUniqueText(
                `${item.ten1}-${item.ten2}-${moment(
                  item.thoiGianThucHien
                ).format("DD/MM/YYYY")}`
              ),
            };
          });
          refModalChonTieuChi &&
            refModalChonTieuChi.current.show(
              { data: dsTieuChi },
              (idTieuChi) => {
                showFileEditor({
                  phieu: item,
                  nbDotDieuTriId: currentToDieuTri?.nbDotDieuTriId,
                  nbDvXetNgiemId: idTieuChi,
                  ma: item.ma,
                  id: idTieuChi,
                  lichSuKyId,
                  mhParams,
                });
              }
            );
        } else if (item.ma == "P135") {
          refModalChonTieuChi &&
            refModalChonTieuChi.current.show(
              {
                data: (item.dsSoPhieu || []).map((x) => ({
                  id: x.soPhieu,
                  ten: `Số phiếu ${x.ten1}`,
                })),
              },
              (idTieuChi) => {
                showFileEditor({
                  phieu: item,
                  nbDotDieuTriId: currentToDieuTri?.nbDotDieuTriId,
                  nbDvXetNgiemId: idTieuChi,
                  ma: item.ma,
                  id: idTieuChi,
                  lichSuKyId,
                  mhParams,
                });
              }
            );
        } else {
          if (item.ma == "P086") {
            refModalChonPhieuTruyenDich.current &&
              refModalChonPhieuTruyenDich.current.show(
                {
                  khoaChiDinhId: chiTietNguoiBenhNoiTru?.khoaNbId,
                },
                (filterData) => {
                  showFileEditor({
                    phieu: item,
                    id: filterData?.id,
                    nbDotDieuTriId: currentToDieuTri.nbDotDieuTriId,
                    ma: item.ma,
                    maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                      ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                      : "",
                    chiDinhTuDichVuId: currentToDieuTri.id,
                    chiDinhTuLoaiDichVu: LOAI_DICH_VU.TO_DIEU_TRI,
                    khoaChiDinhId: filterData?.khoaChiDinhId,
                    lichSuKyId,
                    mhParams,
                  });
                }
              );
          } else if (item.ma == "P191") {
            refModalChonPhieuCongKhaiThuoc.current &&
              refModalChonPhieuCongKhaiThuoc.current.show(
                {
                  khoaChiDinhId: chiTietNguoiBenhNoiTru?.khoaNbId,
                  thoiGianYLenh: currentToDieuTri?.thoiGianYLenh,
                },
                (filterData) => {
                  showFileEditor({
                    phieu: item,
                    id: id,
                    nbDotDieuTriId: currentToDieuTri.nbDotDieuTriId,
                    ma: item.ma,
                    maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                      ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                      : "",
                    chiDinhTuDichVuId: currentToDieuTri.id,
                    chiDinhTuLoaiDichVu: LOAI_DICH_VU.TO_DIEU_TRI,
                    khoaChiDinhId: filterData?.khoaChiDinhId,
                    thoiGianYLenh: filterData?.thoiGianYLenh,
                    lichSuKyId,
                    mhParams,
                  });
                }
              );
          } else if (item.ma == "P616") {
            refModalPhieuThuPhanUngThuoc.current &&
              refModalPhieuThuPhanUngThuoc.current.show(
                {
                  khoaId: chiTietNguoiBenhNoiTru?.khoaNbId,
                  thoiGianYLenh: currentToDieuTri?.thoiGianYLenh,
                  ten: item.ten,
                },
                (filterData) => {
                  showFileEditor({
                    phieu: item,
                    id: filterData?.id,
                    nbDotDieuTriId: currentToDieuTri.nbDotDieuTriId,
                    ma: item.ma,
                    maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                      ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                      : "",
                    chiDinhTuDichVuId: currentToDieuTri.id,
                    chiDinhTuLoaiDichVu: LOAI_DICH_VU.TO_DIEU_TRI,
                    khoaId: filterData?.khoaId,
                    tuThoiGian: filterData?.tuThoiGian,
                    denThoiGian: filterData?.denThoiGian,
                    lichSuKyId,
                    mhParams,
                  });
                }
              );
          } else if (item.ma === "P619") {
            const _dsSoPhieu = (item.dsSoPhieu || []).filter(
              (x) =>
                x.soPhieu != currentToDieuTri?.nbDotDieuTriId &&
                x.soPhieu &&
                x.soPhieu !== "null"
            );
            refModalInPhieu.current &&
              refModalInPhieu.current.show(
                {
                  khoaChiDinhId: currentToDieuTri.khoaThucHienId,
                  dsSoPhieu: _dsSoPhieu,
                  ten: item.ten,
                },
                (data) => {
                  const { thoiGianThucHien, khoaChiDinhId, id: idPhieu } = data;
                  showFileEditor({
                    phieu: item,
                    id: idPhieu,
                    nbDotDieuTriId: currentToDieuTri?.nbDotDieuTriId,
                    ma: item.ma,
                    maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                      ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                      : "",
                    khoaChiDinhId,
                    thoiGianThucHien,
                    toDieuTriId: currentToDieuTri.id,
                    mhParams,
                  });
                }
              );
          } else {
            showFileEditor({
              phieu: item,
              id: id,
              nbDotDieuTriId: currentToDieuTri.nbDotDieuTriId,
              ma: item.ma,
              maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                : "",
              khoaChiDinhId: chiTietNguoiBenhNoiTru?.khoaNbId,
              chiDinhTuDichVuId: currentToDieuTri.id,
              chiDinhTuLoaiDichVu: LOAI_DICH_VU.TO_DIEU_TRI,
              lichSuKyId,
              mhParams,
            });
          }
        }
      } else if (item.ma == "P050") {
        refModalChonKetQuaXetNghiem.current &&
          refModalChonKetQuaXetNghiem.current.show({
            nbDotDieuTriId: currentToDieuTri.nbDotDieuTriId,
            thoiGianThucHien: currentToDieuTri?.thoiGianYLenh,
            baoCaoId: item.baoCaoId,
          });
      } else {
        let params = {
          listPhieus: [item],
          nbDotDieuTriId: currentToDieuTri.nbDotDieuTriId,
          id: currentToDieuTri.id,
          showError: true,
          maManHinh: "007",
          maViTri: "00701",
        };

        const onInPhieu = async (data) => {
          try {
            showLoading();
            const { finalFile, dsPhieu } = await getFilePhieuIn(data);

            if ((dsPhieu || []).every((x) => x?.loaiIn == 20)) {
              openInNewTab(finalFile);
            } else {
              printJS({
                printable: finalFile,
                type: "pdf",
              });
            }
          } catch (error) {
            console.log("error", error);
          } finally {
            hideLoading();
          }
        };

        let addParam = {};
        if (item.ma === "P075") {
          if (valuePhieuChiDinhTH == 2) {
            addParam = { inPhieuChiDinh: false };
          } else if (valuePhieuChiDinhTH == 4 || valuePhieuChiDinhTH == 3) {
            const chuaThanhToan = valuePhieuChiDinhTH == 4;
            const callback =
              valuePhieuChiDinhTH == 3
                ? (data) => {
                    if (checkIsPhieuKySo(item)) {
                      refModalSignPrint.current &&
                        refModalSignPrint.current.showToSign({
                          phieuKy: item,
                          payload: {
                            nbDotDieuTriId: currentToDieuTri.nbDotDieuTriId,
                            maManHinh: "007",
                            maViTri: "00701",
                            chiDinhTuDichVuId: id,
                            dsChiDinhTuLoaiDichVu: LOAI_DICH_VU.TO_DIEU_TRI,
                            ...data,
                          },
                        });
                    } else {
                      addParam = { ...addParam, ...data };

                      onInPhieu({ ...params, ...addParam });
                    }
                  }
                : null;

            onPrintTheoDichVu(chuaThanhToan, callback);
            return;
          }
        }

        if (checkIsPhieuKySo(item)) {
          refModalSignPrint.current &&
            refModalSignPrint.current.showToSign({
              phieuKy: item,
              payload: {
                nbDotDieuTriId: currentToDieuTri.nbDotDieuTriId,
                maManHinh: "007",
                maViTri: "00701",
                chiDinhTuDichVuId: id,
                dsChiDinhTuLoaiDichVu: LOAI_DICH_VU.TO_DIEU_TRI,
                ...addParam,
              },
            });
        } else {
          onInPhieu({ ...params, ...addParam });
        }
      }
    }
  };

  const contentPhieuChiDinh = () => {
    return (
      <div
        onClick={(e) => {
          e.preventDefault();
          e.stopPropagation();
        }}
      >
        <Radio.Group
          value={valuePhieuChiDinh}
          onChange={(e) => {
            setValuePhieuChiDinh(e.target.value);
            setState({
              popoverVisible: false,
            });
          }}
        >
          <Space direction="vertical">
            <Radio value={1}>{t("khamBenh.tatCaChiDinh")}</Radio>
            <Radio value={2}>{t("khamBenh.chiDinhChuaIn")}</Radio>
            <Radio value={3}>{t("khamBenh.inChiDinhTheoDichVu")}</Radio>
            <Radio value={4}>{t("khamBenh.tatCaCacDichVuChuaThanhToan")}</Radio>
          </Space>
        </Radio.Group>
      </div>
    );
  };

  const contentPhieuChiDinhTongHop = () => {
    return (
      <div
        onClick={(e) => {
          e.preventDefault();
          e.stopPropagation();
        }}
      >
        <Radio.Group
          value={valuePhieuChiDinhTH}
          onChange={(e) => {
            setState({
              popoverVisiblePhieuTH: false,
            });
            setValuePhieuChiDinhTH(e.target.value);
          }}
        >
          <Space direction="vertical">
            <Radio value={1}>{t("pttt.tatCaChiDinh")}</Radio>
            <Radio value={2}>{t("pttt.chiDinhChuaIn")}</Radio>
            <Radio value={3}>{t("pttt.inChiDinhTheoDichVu")}</Radio>
            <Radio value={4}>{t("khamBenh.tatCaCacDichVuChuaThanhToan")}</Radio>
          </Space>
        </Radio.Group>
      </div>
    );
  };

  const onPrint = async () => {
    let res = await getTatCaGiayChiDinh({
      nbDotDieuTriId: currentToDieuTri?.nbDotDieuTriId,
      chiDinhTuDichVuId: currentToDieuTri?.id,
      chiDinhTuLoaiDichVu: LOAI_DICH_VU.TO_DIEU_TRI,
    });
    const dsFilePdf = res?.reduce((acc, item) => {
      if (Array.isArray(item)) {
        // xử lý phiếu , có những phiếu có mảng con bên trong
        let list = item.map((itemChild) => itemChild.file.pdf);
        acc = [...acc, [...list]];
        return acc;
      }
      acc = [...acc, ...item.filePdf];
      return acc;
    }, []);
    const s = await getDataDanhSachPhieu({
      dsFile: flatten(dsFilePdf),
      mode: 0,
    });
    if ((res || []).every((x1) => (x1 || []).every((x2) => x2.loaiIn == 20))) {
      openInNewTab(s);
    } else {
      printJS({
        printable: s,
        type: "pdf",
      });
    }
  };
  // --------------------------------------------------------------------------------------------------------------------------------
  const onPrintPhieuChuaIn = async () => {
    let res = await getTatCaGiayChiDinh({
      nbDotDieuTriId: currentToDieuTri?.nbDotDieuTriId,
      chiDinhTuDichVuId: currentToDieuTri?.id,
      inPhieuChiDinh: false,
      chiDinhTuLoaiDichVu: LOAI_DICH_VU.TO_DIEU_TRI,
    });
    if (res?.length === 0) {
      message.error(t("khamBenh.khongConPhieuChiDinhChuaIn"));
      return null;
    }
    const dsFilePdf = res?.reduce((acc, item) => {
      if (Array.isArray(item)) {
        // xử lý phiếu , có những phiếu có mảng con bên trong
        let list = item.map((itemChild) => itemChild.file.pdf);
        acc = [...acc, [...list]];
        return acc;
      }
      acc = [...acc, ...item.filePdf];
      return acc;
    }, []);
    const s = await getDataDanhSachPhieu({
      dsFile: flatten(dsFilePdf),
      mode: 0,
    });
    printJS({
      printable: s,
      type: "pdf",
    });
  };
  // --------------------------------------------------------------------------------------------------------------------------------
  const onPrintTheoDichVu = async (chuaThanhToan, callback) => {
    showLoading();
    try {
      let res = null;
      if (!chuaThanhToan) {
        res = await nbDvKyThuatProvider.getDvPhieuChiDinh({
          nbDotDieuTriId: currentToDieuTri?.nbDotDieuTriId,
          chiDinhTuDichVuId: currentToDieuTri?.id,
          chiDinhTuLoaiDichVu: LOAI_DICH_VU.TO_DIEU_TRI,
        });
        refModalInChiDinhTheoDV.current.show(
          {
            data: res.data,
            chiDinhTuLoaiDichVu: LOAI_DICH_VU.TO_DIEU_TRI,
            nbDotDieuTriId: currentToDieuTri?.nbDotDieuTriId,
            chiDinhTuDichVuId: currentToDieuTri?.id,
          },
          callback
        );
      } else {
        res = await nbDvKyThuatProvider.getPhieuChiDinhTongHop({
          nbDotDieuTriId: currentToDieuTri?.nbDotDieuTriId,
          dsChiDinhTuLoaiDichVu: LOAI_DICH_VU.TO_DIEU_TRI,
          chiDinhTuDichVuId: currentToDieuTri?.id,
          thanhToan: false,
        });
        await printProvider.printPdf(res?.data);
      }
    } catch (error) {
      showError(error?.message);
    } finally {
      hideLoading();
    }
  };

  const onPrintPhieuChiDinh = (item) => async (e) => {
    // e.preventDefault();
    // e.stopPropagation();
    showLoading();
    if (item.kySo) {
      let addParam = {};
      if (valuePhieuChiDinh == 2) {
        addParam = { inPhieuChiDinh: false };
      } else if (valuePhieuChiDinh == 4 || valuePhieuChiDinh == 3) {
        const chuaThanhToan = valuePhieuChiDinh == 4;
        const callback =
          valuePhieuChiDinh == 3
            ? (data) => {
                refModalSignPrint.current &&
                  refModalSignPrint.current.showToSign({
                    phieuKy: item,
                    payload: {
                      nbDotDieuTriId: currentToDieuTri.nbDotDieuTriId,
                      maManHinh: "007",
                      maViTri: "00701",
                      chiDinhTuDichVuId: id,
                      dsChiDinhTuLoaiDichVu: LOAI_DICH_VU.TO_DIEU_TRI,
                      ...data,
                    },
                  });
              }
            : null;

        onPrintTheoDichVu(chuaThanhToan, callback);
        return;
      }

      refModalSignPrint.current &&
        refModalSignPrint.current.showToSign({
          phieuKy: item,
          payload: {
            nbDotDieuTriId: currentToDieuTri.nbDotDieuTriId,
            maManHinh: "007",
            maViTri: "00701",
            chiDinhTuDichVuId: id,
            dsChiDinhTuLoaiDichVu: LOAI_DICH_VU.TO_DIEU_TRI,
            ...addParam,
          },
        });
    } else {
      switch (valuePhieuChiDinh) {
        case 1: {
          // tất cả chỉ định
          await onPrint();
          break;
        }
        case 2: {
          // chỉ định chưa in
          await onPrintPhieuChuaIn();
          break;
        }
        case 3: {
          // in chỉ định theo dịch vụ
          await onPrintTheoDichVu();
          break;
        }
        case 4: {
          // in chỉ định theo dịch vụ
          await onPrintTheoDichVu(true);
          break;
        }
        default:
          // tất cả chỉ định
          await onPrint();
          break;
      }
    }
    hideLoading();
  };

  const menu = useMemo(() => {
    return (
      <Menu
        items={(state.listPhieu || []).map((item, index) => {
          if (item.ma == "P057") {
            return {
              key: index + "",
              label: (
                <div style={{ display: "flex" }}>
                  <div onClick={onPrintPhieuChiDinh(item)} style={{ flex: 1 }}>
                    {item.ten || item.tenBaoCao}
                  </div>

                  <Popover
                    getPopupContainer={(trigger) => trigger.parentNode}
                    overlayClassName={"step-wrapper-in-options right"}
                    placement="rightTop"
                    content={contentPhieuChiDinh()}
                    trigger="click"
                    visible={state.popoverVisible}
                  >
                    <SVG.IcOption
                      onClick={(e) => {
                        e.stopPropagation();
                        e.preventDefault();
                        setState({ popoverVisible: !state.popoverVisible });
                      }}
                    />
                  </Popover>
                </div>
              ),
            };
          } else if (item.ma === "P075") {
            return {
              key: index,
              label: (
                <div style={{ display: "flex" }}>
                  <div onClick={onPrintPhieu(item)} style={{ flex: 1 }}>
                    {item.ten || item.tenBaoCao}
                  </div>

                  <Popover
                    getPopupContainer={(trigger) => trigger.parentNode}
                    overlayClassName={"step-wrapper-in-options left"}
                    placement="leftTop"
                    content={contentPhieuChiDinhTongHop()}
                    visible={state.popoverVisiblePhieuTH}
                    trigger="click"
                  >
                    <SVG.IcOption
                      alt=""
                      onClick={(e) => {
                        e.stopPropagation();
                        e.preventDefault();
                        setState({
                          popoverVisiblePhieuTH: !state.popoverVisiblePhieuTH,
                        });
                      }}
                    />
                  </Popover>
                </div>
              ),
            };
          }
          return {
            key: index,
            label: (
              <a href={() => false} onClick={onPrintPhieu(item)}>
                {item.ten || item.tenBaoCao}
              </a>
            ),
          };
        })}
      />
    );
  }, [
    state?.listPhieu,
    id,
    currentToDieuTri,
    valuePhieuChiDinh,
    valuePhieuChiDinhTH,
    state.popoverVisible,
    state.popoverVisiblePhieuTH,
  ]);

  const onScroll = (e) => {
    if (e.target.scrollTop > refHeaderLeft.current?.offsetHeight - 10) {
      setTitle(
        `${t("quanLyNoiTru.toDieuTri.title")} - ${
          chiTietNguoiBenhNoiTru?.tenNb || ""
        }`
      );
    } else {
      setTitle(t("quanLyNoiTru.toDieuTri.title"));
    }
  };

  const onDangKyPHCN = async () => {
    const {
      maHoSo,
      id: nbDotDieuTriId,
      maBenhAn,
    } = chiTietNguoiBenhNoiTru || {};

    if (
      dataBO_BAT_BUOC_CHON_PHAN_LOAI_KHI_DANG_KY_PHCN.toLowerCase() === "true"
    ) {
      await dangKyDotPHCN({
        nbDotDieuTriId,
        dsPhanLoaiPhcnId: null,
        loai: LOAI_DANG_KY.PHUC_HOI_CHUC_NANG,
      });
      await getListDieuTriPHCNByNb({
        page: "",
        size: "",
        maBenhAn,
      });
    } else {
      refModalChonLoaiPHCN.current &&
        refModalChonLoaiPHCN.current.show({
          maHoSo,
          nbDotDieuTriId,
          maBenhAn,
        });
    }
  };

  const onHuyDangKy = (loai) => async () => {
    try {
      showLoading();
      const { id: nbDotDieuTriId, maBenhAn } = chiTietNguoiBenhNoiTru || {};
      const id =
        loai === LOAI_DANG_KY.PHUC_HOI_CHUC_NANG
          ? dieuKienPHCN.idHuyDangKy
          : dieuKienYHCT.idHuyDangKy;

      await huyDangKyDotPHCN({
        nbDotDieuTriId,
        id,
        loai,
      }).then(() => {
        getListDieuTriPHCNByNb({
          page: "",
          size: "",
          maBenhAn,
        });
      });
    } catch (error) {
    } finally {
      hideLoading();
    }
  };

  const onTaoMoiBaoCaoADR = async () => {
    if (state.nbPhieuAdr) {
      window.open(`/editor/bao-cao/EMR_BA275/${state.nbPhieuAdr.id}`);
    } else {
      try {
        showLoading();

        const data = refThongTinToDieuTri.current
          ? refThongTinToDieuTri.current.getData()
          : {};

        const res = await taoMoiBaoCaoAdr({
          nbDotDieuTriId: chiTietNguoiBenhNoiTru?.id,
          chiDinhTuDichVuId: id,
          chiDinhTuLoaiDichVu: LOAI_DICH_VU.TO_DIEU_TRI,
          nguoiBaoCaoId: nhanVienId,
          trangThaiPhieu: [TRANG_THAI_PHIEU_NUMBER.TAO_MOI],
          ngayBaoCao: moment().format("YYYY-MM-DD"),
          ngayPhanUng:
            data?.thoiGianYLenh &&
            moment(data?.thoiGianYLenh).format("YYYY-MM-DD"),
        });

        if (res?.id) {
          setState({ nbPhieuAdr: res });

          window.open(`/editor/bao-cao/EMR_BA275/${res.id}`);
        }
      } catch (error) {
      } finally {
        hideLoading();
      }
    }
  };

  const onDangKyYHCT = async () => {
    const { id: nbDotDieuTriId, maBenhAn } = chiTietNguoiBenhNoiTru || {};
    await dangKyDotPHCN({
      nbDotDieuTriId,
      dsPhanLoaiPhcnId: null,
      loai: LOAI_DANG_KY.Y_HOC_CO_TRUYEN,
    });
    await getListDieuTriPHCNByNb({
      page: "",
      size: "",
      maBenhAn,
    });
  };

  const onDangKyKetHop = (isDangKy) => (e) => {
    const { id: nbDotDieuTriId, maBenhAn } = chiTietNguoiBenhNoiTru || {};
    refModalDangKyKetHop.current &&
      refModalDangKyKetHop.current.show({
        nbDotDieuTriId,
        maBenhAn: maBenhAn,
        isDangKy,
      });
  };

  const openDrawerDsNb = useCallback(() => {
    refDanhSachBenhNhanSidePanel.current &&
      refDanhSachBenhNhanSidePanel.current.showDrawer({
        khoaLamViec: khoaLamViec,
      });
  }, [khoaLamViec]);

  const listLoaiPhucHoiCnDangKy = useMemo(() => {
    const listLoaiPhcnNb = _.uniq(
      listDataByNb
        ?.filter((x) => [10, 30]?.includes(x.trangThai))
        .map((x) => x.loai)
    );
    return listLoaiPhucHoiCn?.filter((x) => !listLoaiPhcnNb?.includes(x.id));
  }, [listDataByNb, listLoaiPhucHoiCn]);

  const getBreadcrumb = ({ tenLoaiPhcn }) => {
    if (tenLoaiPhcn) {
      return [
        {
          link: "/phuc-hoi-chuc-nang",
          title: tenLoaiPhcn || "",
        },
        {
          link: "/phuc-hoi-chuc-nang/ds-dieu-tri-phcn",
          title: tenLoaiPhcn
            ? t("phcn.dieuTriPhcnTen", { ten: tenLoaiPhcn })
            : "",
        },
        {
          link: `/phuc-hoi-chuc-nang/dieu-tri-phcn/${currentToDieuTri?.nbDotDieuTriId}/${phcnId}?tab=1`,
          title: tenLoaiPhcn
            ? t("phcn.chiTietDieuTriPhcnTen", { ten: tenLoaiPhcn })
            : "",
        },
        {
          link: window.location.pathname,
          title: t("quanLyNoiTru.toDieuTri.title"),
        },
      ];
    }

    return [
      { link: "/quan-ly-noi-tru", title: t("quanLyNoiTru.quanLyNoiTru") },
      {
        link: "/quan-ly-noi-tru/danh-sach-nguoi-benh-noi-tru",
        title: t("quanLyNoiTru.danhSachNguoiBenhNoiTru"),
      },
      {
        link:
          `/quan-ly-noi-tru/chi-tiet-nguoi-benh-noi-tru/${chiTietNguoiBenhNoiTru?.id}` +
          transformObjToQueryString(location),
        title: t("quanLyNoiTru.dieuTriNoiTru"),
      },
      {
        link: `/quan-ly-noi-tru/chi-tiet-nguoi-benh-noi-tru/to-dieu-tri/${id}`,
        title: t("quanLyNoiTru.toDieuTri.title"),
      },
    ];
  };

  const onCreateToDieuTri = () => {
    history.push(
      `/quan-ly-noi-tru/chi-tiet-nguoi-benh-noi-tru/${chiTietNguoiBenhNoiTru.id}/to-dieu-tri/them-moi`
    );
  };

  return (
    <MainPage
      breadcrumb={getBreadcrumb({
        tenLoaiPhcn,
      })}
      title={title}
      titleRight={
        <>
          <Row className="header-title-right">
            <div className="flex flex-center">
              <SVG.IcLocation />
              {chiTietNguoiBenhNoiTru?.maKhoaNb &&
                chiTietNguoiBenhNoiTru?.tenKhoaNb && (
                  <b>
                    {chiTietNguoiBenhNoiTru?.maKhoaNb} -{" "}
                    {chiTietNguoiBenhNoiTru?.tenKhoaNb}
                  </b>
                )}
            </div>
            <NextPrevController
              total={listToDieuTri?.length}
              current={currentIndex}
              onChange={onChangeToDieuTri}
            />
          </Row>
        </>
      }
      actionLeft={
        <>
          {chiTietNguoiBenhNoiTru?.id && (
            <Button minWidth={100} onClick={onSaoChepThuocVT}>
              {t("quanLyNoiTru.toDieuTri.saoChepDichVu")}
            </Button>
          )}

          {checkRole([ROLES["QUAN_LY_NOI_TRU"].THEM_MOI_TO_DIEU_TRI]) && (
            <Button onClick={onCreateToDieuTri}>
              {t("quanLyNoiTru.toDieuTri.taoToDieuTriMoi")}
            </Button>
          )}

          {!isReadonly && (
            <Button minWidth={100} onClick={onDeleteToDieuTri}>
              {t("common.xoa")}
            </Button>
          )}
          <AuthWrapper accessRoles={[ROLES["QUAN_LY_NOI_TRU"].DANG_KY_PHCN]}>
            {dieuKienPHCN.dangKy && (
              <Button minWidth={100} onClick={onDangKyPHCN}>
                {listDataPHCN && listDataPHCN.length > 0
                  ? t("quanLyNoiTru.themMoiDotPHCN")
                  : t("quanLyNoiTru.dangKyPHCN")}
              </Button>
            )}
            {dieuKienPHCN.huyDangKy && (
              <Button
                minWidth={100}
                onClick={onHuyDangKy(LOAI_DANG_KY.PHUC_HOI_CHUC_NANG)}
              >
                {t("quanLyNoiTru.huyDangKyPHCN")}
              </Button>
            )}
          </AuthWrapper>
          <AuthWrapper
            accessRoles={[ROLES["PHUC_HOI_CHUC_NANG"].HIEN_THI_NB_DANG_KY_YHCT]}
          >
            {dieuKienYHCT.dangKy && (
              <Button minWidth={100} onClick={onDangKyYHCT}>
                {listDataYHCT && listDataYHCT.length > 0
                  ? t("quanLyNoiTru.themMoiDotYHCT")
                  : t("quanLyNoiTru.dangKyYHCT")}
              </Button>
            )}
            {dieuKienYHCT.huyDangKy && (
              <Button
                minWidth={100}
                onClick={onHuyDangKy(LOAI_DANG_KY.Y_HOC_CO_TRUYEN)}
              >
                {t("quanLyNoiTru.huyDangKyYHCT")}
              </Button>
            )}
          </AuthWrapper>

          {checkRole([ROLES["QUAN_LY_NOI_TRU"].DIEU_TRI_KET_HOP]) &&
            !!listLoaiPhucHoiCnDangKy?.length &&
            chiTietNguoiBenhNoiTru?.trangThai <= 50 && (
              <Button minWidth={100} onClick={onDangKyKetHop(true)}>
                {t("phcn.dieuTriKetHop")}
              </Button>
            )}
          {checkRole([ROLES["QUAN_LY_NOI_TRU"].HUY_DIEU_TRI_KET_HOP]) &&
            !!listDataByNb?.length &&
            listDataByNb?.find((x) => x.trangThai !== 20) &&
            chiTietNguoiBenhNoiTru?.trangThai <= 50 && (
              <Button minWidth={100} onClick={onDangKyKetHop(false)}>
                {t("phcn.huyDieuTriKetHop")}
              </Button>
            )}
          <AuthWrapper
            accessRoles={[
              ROLES["QUAN_LY_BAO_CAO_ADR"].THEM_MOI_CHINH_SUA_BAO_CAO,
            ]}
          >
            <Button minWidth={100} onClick={onTaoMoiBaoCaoADR}>
              {state.nbPhieuAdr
                ? t("quanLyBaoCaoAdr.xemBaoCaoADR")
                : t("quanLyBaoCaoAdr.taoBaoCaoADR")}
            </Button>
          </AuthWrapper>
        </>
      }
      actionRight={
        <>
          <Dropdown overlay={menu} trigger={["click"]} placement="top">
            <Button
              type="default"
              rightIcon={<SVG.IcPrint className="ic-print" />}
              minWidth={100}
              onClick={inGiayTo}
            >
              {t("khamBenh.inGiayTo")}
            </Button>
          </Dropdown>
          <Button minWidth={100} onClick={onClose}>
            {" "}
            {t("common.quayLai")}
          </Button>
          {(!isReadonly ||
            checkRole([
              ROLES["QUAN_LY_NOI_TRU"].TAO_TO_DIEU_TRI_NB_DA_THANH_TOAN_RA_VIEN,
            ])) &&
            checkRole([ROLES["QUAN_LY_NOI_TRU"].THEM_MOI_TO_DIEU_TRI]) && (
              <Button
                minWidth={100}
                type="primary"
                iconHeight={15}
                onClick={onSave}
                rightIcon={<SVG.IcSave />}
              >
                {t("common.luu")}
              </Button>
            )}
        </>
      }
      contentRight={
        modeDsNb === DATA_MODE_DS_NB.MODULE && (
          <Card className="right-content" noPadding={true} bottom={0} top={10}>
            <DanhSachBenhNhan
              khoaLamViec={khoaLamViec}
              modeDsNb={modeDsNb}
              changeModeDsNb={setModeDsNb}
            />
          </Card>
        )
      }
    >
      <Main className="toDieuTri">
        <div className="main" onScroll={debounce(onScroll, 20)}>
          <Row ref={refHeaderLeft}>
            <Col className="header-left" span={24}>
              <ThongTinBenhNhan
                style={{ marginTop: 0 }}
                nbDotDieuTriId={chiTietNguoiBenhNoiTru?.id}
                modeDsNb={modeDsNb}
                openDrawerDsNb={openDrawerDsNb}
                nbDotDieuTriMeId={chiTietNguoiBenhNoiTru.nbDotDieuTriMeId}
              />
            </Col>
          </Row>
          <div>
            <Card top={16} bottom={0}>
              <ThongTinToDieuTri
                currentToDieuTri={currentToDieuTri}
                ref={refThongTinToDieuTri}
                isReadonly={isDisableEditForm}
                isDisableThoiGianDotDieuTri={!isThoiGianDotDieuTri}
                phcnId={phcnId}
              />
            </Card>
          </div>

          <ChiDinhDichVu
            isReadonly={isReadonly}
            layerId={layerId}
            checkChongChiDinh={checkChongChiDinh}
          />
        </div>
        <DanhSachBenhNhanSidePanel
          modeDsNb={modeDsNb}
          ref={refDanhSachBenhNhanSidePanel}
          onChangeMode={setModeDsNb}
        />
      </Main>

      <ModalInChiDinhTheoDV ref={refModalInChiDinhTheoDV} />
      <ModalSaoChepThuoc ref={refModalSaoChepThuoc} />
      <ModalXemKetQuaPDF ref={refModalXemKetQuaPDF} />
      <ModalChonKhoaPhieuTruyenDich ref={refModalChonPhieuTruyenDich} />
      <ModalChonTieuChi ref={refModalChonTieuChi} />
      <ModalChonKetQuaXetNghiem ref={refModalChonKetQuaXetNghiem} />
      <ModalChonPhieuCongKhaiThuoc ref={refModalChonPhieuCongKhaiThuoc} />
      <ModalSignPrint ref={refModalSignPrint} />
      <ModalChiDinhPhacDo ref={refChiDinhPhacDo} />
      <ModalChonLoaiPHCN ref={refModalChonLoaiPHCN} />
      <ModalChonPhieuCanThiepDuoc
        ref={refModalChonPhieuCanThiepDuoc}
      ></ModalChonPhieuCanThiepDuoc>
      <ModalChonPhieuThuPhanUngThuoc ref={refModalPhieuThuPhanUngThuoc} />
      <ModalChonLoaiDangKyKetHopDieuTri
        listAllDieuTriKetHopTheoLoai={listAllDieuTriKetHopTheoLoai}
        listAllDieuTriKetHopTheoKhoa={listAllDieuTriKetHopTheoKhoa}
        ref={refModalDangKyKetHop}
      />
      <ModalInPhieu ref={refModalInPhieu} />
      <ModalXoaToDieuTri ref={refModalXoaToDieuTri} />
    </MainPage>
  );
};

export default ChiTietToDieuTri;
