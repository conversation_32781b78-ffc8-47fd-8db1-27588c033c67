import React, { memo } from "react";
import { Tooltip } from "antd";

import { Main } from "./styled";
import ListPhanLoaiNguoiBenh from "pages/tiepDon/DanhSachNB/DaTiepDon/components/ListPhanLoaiNguoiBenh";

function PhanLoaiNguoiBenh({ thongTinBenhNhan }) {
  const { dsPhanLoaiNbId = [], dsPhanLoaiNb = [] } = thongTinBenhNhan || {};
  const title = dsPhanLoaiNb.map((item) => item.ten).join(", ");

  const content = (
    <Main>
      <ListPhanLoaiNguoiBenh value={dsPhanLoaiNbId.slice(0, 2)} />
    </Main>
  );

  return dsPhanLoaiNb.length > 2 ? (
    <Tooltip title={title}>{content}</Tooltip>
  ) : (
    content
  );
}

export default memo(PhanLoaiNguoiBenh);
