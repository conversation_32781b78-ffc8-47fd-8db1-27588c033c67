import React, { memo } from "react";

import { Main } from "./styled";
import ListPhanLoaiNguoiBenh from "pages/tiepDon/DanhSachNB/DaTiepDon/components/ListPhanLoaiNguoiBenh";

function PhanLoaiNguoiBenh({ thongTinBenhNhan }) {
  const { dsPhanLoaiNbId = [], dsPhanLoaiNb = [] } = thongTinBenhNhan || {};

  const content = (
    <Main>
      <ListPhanLoaiNguoiBenh
        value={dsPhanLoaiNbId.slice(0, 2)}
        allTags={dsPhanLoaiNb}
      />
    </Main>
  );

  return content;
}

export default memo(PhanLoaiNguoiBenh);
