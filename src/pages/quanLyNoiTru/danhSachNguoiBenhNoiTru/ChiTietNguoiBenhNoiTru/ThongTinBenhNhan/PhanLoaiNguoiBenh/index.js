import React, { memo, useState, useEffect } from "react";
import { Tooltip } from "antd";

import { Main } from "./styled";
import ListPhanLoaiNguoiBenh from "pages/tiepDon/DanhSachNB/DaTiepDon/components/ListPhanLoaiNguoiBenh";

function PhanLoaiNguoiBenh({ thongTinBenhNhan }) {
  const { dsPhanLoaiNbId = [], dsPhanLoaiNb = [] } = thongTinBenhNhan || {};
  const [maxTags, setMaxTags] = useState(2);
  const title = dsPhanLoaiNb.map((item) => item.ten).join(", ");

  useEffect(() => {
    const handleResize = () => {
      // Kiểm tra kích thước màn hình để quyết định số lượng tag hiển thị
      const width = window.innerWidth;
      if (width < 768) {
        setMaxTags(1); // <PERSON><PERSON><PERSON> hình nhỏ chỉ hiển thị 1 tag
      } else {
        setMaxTags(2); // Màn hình lớn hiển thị 2 tag
      }
    };

    handleResize(); // G<PERSON>i ngay khi component mount
    window.addEventListener("resize", handleResize);

    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const content = (
    <Main>
      <ListPhanLoaiNguoiBenh value={dsPhanLoaiNbId.slice(0, maxTags)} />
    </Main>
  );

  return dsPhanLoaiNb.length > maxTags ? (
    <Tooltip title={title}>{content}</Tooltip>
  ) : (
    content
  );
}

export default memo(PhanLoaiNguoiBenh);
