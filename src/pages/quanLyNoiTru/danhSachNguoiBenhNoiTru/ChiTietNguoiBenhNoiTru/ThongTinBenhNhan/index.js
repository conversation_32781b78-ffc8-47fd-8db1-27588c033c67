import React, { useRef, memo, useMemo, useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { Avatar, message, Row, Skeleton } from "antd";
import { UserOutlined } from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import { checkRole } from "lib-utils/role-utils";
import {
  DATA_MODE_DS_NB,
  DOI_TUONG,
  DOI_TUONG_KCB,
  ENUM,
  ROLES,
  TRANG_THAI_NB,
  THIET_LAP_CHUNG,
} from "constants/index";
import {
  useConfirm,
  useEnum,
  useListAll,
  useLoading,
  useStore,
  useThietLap,
} from "hooks";
import { IconAction, Tooltip, Image, LazyLoad, Popover } from "components";
import { SVG } from "assets";
import moment from "moment";
import ModalAnhDaiDien from "components/ModalAnhDaiDien";
import ThongTinSoTien from "./ThongTinSoTien";
import { BounceIn, FadeIn } from "animations";
import fileUtils from "utils/file-utils";
import { isEmpty, isNil, upperFirst } from "lodash";
import nbDotDieuTriProvider from "data-access/nb-dot-dieu-tri-provider";
import { PatientInfoWrapper, Main } from "./styled";
import {
  calculateAgeChildren,
  isArray,
  onCheckThongTinTheBh,
} from "utils/index";
import { TableWrapper, HeaderSearch } from "components";
import PhanLoaiNguoiBenh from "./PhanLoaiNguoiBenh";

const IconActionAnimation = BounceIn(IconAction);
const PatientContent = FadeIn("div");

const ThongTinBenhNhan = ({
  nbDotDieuTriId,
  nbLapBenhAn,
  openDrawerDsNb,
  modeDsNb,
  getData,
  isGetThongTinBenhNhan = true,
  isShowGiaHanThe = true,
  isShowMaTiemChung = false,
  isShowSuaChiTiet = true,
  isShowXemChiTiet = true,
  isShowMaBenhAn = true,
  isShowNgayHenKham = false,
  isShowThongTinThanhToan = true,
  isShowFullThongTinThanhToan = false,
  isShowThongTinBaoLanh = false,
  isShowThuocKhangSinh = false,
  isShowLichSuMuonBA = false,
  isShowThoiGianVaoVien = false,
  isShowLichSuTiemChung = false,
  isShowDeNghiTamUng = false,
  isShowHsba = true,
  isShowSdtNb = false,
  isDieuTriDaiHan = false,
  isShowSoDoPhongGiuong = false,
  isShowDoiTuong = true,
  isShowLoaiDoiTuong = false,
  isShowScanBieuMau = false,
  isShowFolderFile = false,
  isTiepDonNoiTru = true,
  isShowThongTinDieuTriLao = false,
  isShowAvatar = true,
  isShowThongTinDiaChi = true,
  isShowMaHs = true,
  isShowMaNb = true,
  isShowThongTinBaoHiem = true,
  isShowShowSdtNb = true,
  isShowNguoiBaoLanh = true,
  isShowThoiGianHenKham = true,
  isShowMaNbLienKet = false,
  chiTietDuyetBH,
  style,
  labelGiuong = "",
  labelPhong = "",
  isShowSecondaryPatient = false,
  isShowNgaySinh = false,
  isShowLogChinhSua = false,
  quayMacDinhNoiTruQRCode = null,
  isShowDsPhieuThuTamUng = false,
  renderThongTinMeCon = null,
  isShowGioSinh = false,
  isShowPhieuNbLienKet,
  nbLienKetDotDieuTriId,
  isShowDongBoGia = true,
  nbDotDieuTriMeId,
}) => {
  const { t } = useTranslation();
  const { showLoading, hideLoading } = useLoading();
  const { showConfirm } = useConfirm();

  const [dataGOP_PHIEU_THU_DICH_VU_NHA_THUOC] = useThietLap(
    THIET_LAP_CHUNG.GOP_PHIEU_THU_DICH_VU_NHA_THUOC,
    null
  );
  const [dataHIEN_THI_CAC_FIELD_CANH_BAO_NOI_TRU] = useThietLap(
    THIET_LAP_CHUNG.HIEN_THI_CAC_FIELD_CANH_BAO_NOI_TRU
  );

  const [state, _setState] = useState({
    patientInfoWrapperHeight: 0,
    maNbLienKet: null,
  });
  const setState = (data = {}) => {
    _setState((preState) => {
      return { ...preState, ...data };
    });
  };
  const {
    quanLyNoiTru: { getNbLapBenhAnById },
    nbDotDieuTri: {
      getThongTinCoBan,
      clearThongTinNguoiBenh,
      getTtNbLienKet,
      lienKetNb,
      updateData,
      getById,
    },
    deNghiTamUng: { clearData },
    tiepDon: { giamDinhThe },
  } = useDispatch();
  const isLoadingThongTinCoBan = useStore(
    "nbDotDieuTri.isLoadingThongTinCoBan",
    false
  );
  const thongTinCoBan1 = useStore("nbDotDieuTri.thongTinCoBan");
  const chiTietNguoiBenhNoiTruById = useStore("nbDotDieuTri.thongTinBenhNhan");

  const chiTietNguoiBenhNoiTru = useStore(
    "danhSachNguoiBenhNoiTru.chiTietNguoiBenhNoiTru"
  );
  /*
case này sử dụng trong trường hợp trong popup (vd như popup hồ sơ bệnh án có nhiều lịch sử khám)
trong một vài trường hợp, người bệnh hiển thị trong popup không phải là người bệnh đang xem ở phía dưới 
do đã nhấn vào lịch sử khám khác.
nên sử dụng thongtincoban2 để tách biệt ra, đồng thời load thông tin người bệnh không làm ảnh hưởng đến màn hình phía dưới
sử dụng isShowSecondaryPatient để đánh dấu sử dụng riêng và gọi action getThongTinCoBan với thongTinCoBan2
getThongTinCoBan(nbDotDieuTriId, "thongTinCoBan2");
*/

  const thongTinCoBan2 = useStore("nbDotDieuTri.thongTinCoBan2");
  const refPatientInfoWrapper = useRef(null);
  const refModalDanhSachBieuMauScan = useRef(null);
  const refModalFoderAndFile = useRef(null);
  const refModalSoDoPhongGiuong = useRef(null);
  const refModalChinhSuaThongTin = useRef(null);
  const refModalHoSoBenhAn = useRef(null);
  const refModalDeNghiTamUng = useRef(null);
  const refModalLichSuThuocKhangSinh = useRef(null);
  const refModalThemMoiNbDieuTriLao = useRef(null);
  const refModalChinhSuaThoiGianNhapVien = useRef(null);
  const [listDoiTuong] = useEnum(ENUM.DOI_TUONG);
  const [listGioiTinh] = useEnum(ENUM.GIOI_TINH);
  const [listAllLoaiDoiTuong] = useListAll("loaiDoiTuong", {}, true);
  const refModalGiaHanThe = useRef(null);
  const refModalLichSuTiemChung = useRef(null);
  const refAnhDaiDien = useRef(null);
  const refModalLichSuMuonBA = useRef(null);
  const refModalTimKiemNB = useRef(null);
  const refModalLichSuChinhSua = useRef(null);
  const refModalDsPhieuThuTamUng = useRef(null);
  const refModalLichSuKCB = useRef(null);
  const refModalDanhSachPhieuNbLienKet = useRef(null);
  const refModalCheckBaoHiem = useRef();
  const refModalDkiVanTay = useRef(null);

  const thongTinCoBan = useMemo(() => {
    if (!isShowSecondaryPatient) return thongTinCoBan1;
    return thongTinCoBan2;
  }, [thongTinCoBan1, thongTinCoBan2, isShowSecondaryPatient]);

  const {
    thangTuoi,
    tuoi,
    hangTheId,
    iconHangThe,
    diemToiThieu,
    nbLienKetId,
    tenHangThe,
    nbThongTinId,
    trangThaiNb,
  } = thongTinCoBan;

  const canNang =
    thangTuoi != null || tuoi < 16 ? `${thongTinCoBan?.canNang} kg` : "";

  const tuoiNguoiBenh = useMemo(() => {
    if (nbDotDieuTriMeId && trangThaiNb === TRANG_THAI_NB.DANG_DIEU_TRI) {
      return calculateAgeChildren(thongTinCoBan.ngaySinh);
    }
    return thongTinCoBan.tuoi2;
  }, [nbDotDieuTriMeId, thongTinCoBan]);

  const gioiTinh = useMemo(() => {
    return (
      (listGioiTinh || []).find((item) => item.id === thongTinCoBan.gioiTinh) ||
      {}
    );
  }, [thongTinCoBan, listGioiTinh]);

  const soTienConLaiMemo = useMemo(() => {
    let result = thongTinCoBan?.tienConLai || 0;
    if (dataGOP_PHIEU_THU_DICH_VU_NHA_THUOC?.toLowerCase() == "false") {
      result =
        (thongTinCoBan?.tienConLai || 0) +
        (thongTinCoBan?.tienNhaThuocChuaThanhToan || 0);
    }

    return Math.round(result);
  }, [
    thongTinCoBan?.tienConLai,
    thongTinCoBan?.tienNhaThuocChuaThanhToan,
    dataGOP_PHIEU_THU_DICH_VU_NHA_THUOC,
  ]);

  const onShowHoSoBenhAn = () => {
    refModalHoSoBenhAn.current &&
      refModalHoSoBenhAn.current.show({
        nbThongTinId,
        nbDotDieuTriId,
        maBenhAn: thongTinCoBan.maBenhAn,
        isDieuTriDaiHan,
      });
  };

  const onShowLichSuThuocKhangSinh = () => {
    refModalLichSuThuocKhangSinh.current &&
      refModalLichSuThuocKhangSinh.current.show({
        nbThongTinId,
        nbDotDieuTriId,
      });
  };

  const onShowLichSuKCB = () => {
    refModalLichSuKCB.current &&
      refModalLichSuKCB.current.show({
        soCCCD: thongTinCoBan.maSoGiayToTuyThan,
      });
  };

  const onShowThongTinNb = (isEdit) => {
    refModalChinhSuaThongTin.current &&
      refModalChinhSuaThongTin.current.show(
        {
          id: nbDotDieuTriId,
          isEdit,
          isTiepDonNoiTru,
        },
        () => {
          getThongTinCoBan(nbDotDieuTriId);
          getById(nbDotDieuTriId);
          getData && getData();
        }
      );
  };

  useEffect(() => {
    if (nbLienKetId && isShowMaNbLienKet) {
      getTtNbLienKet(nbLienKetId).then((res) => {
        setState({ maNbLienKet: res?.maNb });
      });
    }
  }, [nbLienKetId, isShowMaNbLienKet]);

  useEffect(() => {
    if (nbDotDieuTriId && isGetThongTinBenhNhan) {
      getThongTinCoBan(
        nbDotDieuTriId,
        isShowSecondaryPatient ? "thongTinCoBan2" : "thongTinCoBan"
      );
      getById(nbDotDieuTriId);
      return () => {
        //sau khi thoát trang thì xoá thông tin người bệnh
        if (isShowSecondaryPatient) {
          updateData({ thongTinCoBan2: null });
        } else clearThongTinNguoiBenh();
      };
    }
  }, [nbDotDieuTriId, isGetThongTinBenhNhan, isShowSecondaryPatient]);

  useEffect(() => {
    if (
      refPatientInfoWrapper.current &&
      thongTinCoBan &&
      !isLoadingThongTinCoBan
    ) {
      const myObserver = new ResizeObserver((entries) => {
        if (
          entries?.length &&
          entries[0].contentRect?.height != state.patientInfoWrapperHeight
        )
          setState({
            patientInfoWrapperHeight: entries[0].contentRect?.height,
          });
      });
      // start listening to changes
      myObserver.observe(refPatientInfoWrapper.current);

      // later, stop listening to changes
      return () => {
        myObserver.disconnect();
      };
    }
  }, [thongTinCoBan, refPatientInfoWrapper.current, isLoadingThongTinCoBan]);

  const onDeNghiTamUng = () => {
    clearData();
    refModalDeNghiTamUng.current &&
      refModalDeNghiTamUng.current.show({
        nbDotDieuTriId,
        quayMacDinhNoiTruQRCode,
      });
  };

  const onXemLichSuTiemChung = () => {
    refModalLichSuTiemChung.current &&
      refModalLichSuTiemChung.current.show({ thongTinCoBan: thongTinCoBan });
  };

  const onShowLichSu = () => {
    refModalLichSuMuonBA.current &&
      refModalLichSuMuonBA.current.show({
        nbDotDieuTriId,
      });
  };

  const onDongBoGia = () => {
    const onSubmit = () => {
      showLoading();
      nbDotDieuTriProvider
        .dongBoDuLieuGoiMo({
          dsId: [nbDotDieuTriId],
        })
        .then((res) => {
          if (res?.code === 0) {
            message.success(t("danhMuc.dongBoGiaThanhCong"));
          } else {
            message.error(t("danhMuc.dongBoGiaThatBai"));
          }
        })
        .catch((err) => {
          message.error(err?.message || t("danhMuc.dongBoGiaThatBai"));
        })
        .finally(() => {
          hideLoading();
        });
    };
    showConfirm(
      {
        title: t("common.xacNhan"),
        content: t("common.banCoChacMuonDongBoGia", {
          tenNb: thongTinCoBan.tenNb,
        }),
        onOk: onSubmit,
        okText: t("common.dongY"),
        cancelText: t("common.huy"),
        classNameOkText: "button-warning",
        showImg: true,
        showBtnOk: true,
        typeModal: "warning",
      },
      () => {
        onSubmit();
      }
    );
  };

  const onShowAnhDaiDien = () => {
    if (thongTinCoBan.anhDaiDien)
      refAnhDaiDien.current &&
        refAnhDaiDien.current.show({
          anhDaiDien: thongTinCoBan.anhDaiDien,
          tenNb: thongTinCoBan.tenNb,
        });
  };

  const onShowScanBieuMau = () => {
    refModalDanhSachBieuMauScan.current &&
      refModalDanhSachBieuMauScan.current.show({
        nbDotDieuTriId,
      });
  };

  const onShowFolderFile = () => {
    refModalFoderAndFile.current &&
      refModalFoderAndFile.current.show({
        nbThongTinId,
      });
  };

  const onShowSoDoPhongGiuong = () => {
    if (nbLapBenhAn) {
      refModalSoDoPhongGiuong.current &&
        refModalSoDoPhongGiuong.current.show({
          nbDotDieuTriId: null,
          khoaId: nbLapBenhAn.khoaNhapVienId,
        });
    }
  };

  const onShowChinhSuaThoiGianNhapVien = () => {
    refModalChinhSuaThoiGianNhapVien.current &&
      refModalChinhSuaThoiGianNhapVien.current.show(
        {
          nbDotDieuTriId,
          thoiGianLapBenhAn: nbLapBenhAn?.thoiGianLapBenhAn,
        },
        {
          onSaveSucess: () => {
            getThongTinCoBan(nbDotDieuTriId);
            getNbLapBenhAnById(nbDotDieuTriId);
          },
        }
      );
  };

  const onShowTTDieuTriLao = () => {
    refModalThemMoiNbDieuTriLao.current &&
      refModalThemMoiNbDieuTriLao.current.show({ nbDotDieuTriId });
  };

  const onShowLogModal = () => {
    refModalLichSuChinhSua.current &&
      refModalLichSuChinhSua.current.show({ id: nbDotDieuTriId });
  };

  const onShowDsPhieuThuTamUng = () => {
    refModalDsPhieuThuTamUng.current &&
      refModalDsPhieuThuTamUng.current.show({
        nbDotDieuTriId,
      });
  };

  const onShowModalDangKyVanTay = () => {
    refModalDkiVanTay.current &&
      refModalDkiVanTay.current.show({
        nbDotDieuTriId,
      });
  };

  const onShowDanhSachPhieuNbLienKet = () => {
    refModalDanhSachPhieuNbLienKet.current &&
      refModalDanhSachPhieuNbLienKet.current.show({
        nbDotDieuTriId: nbLienKetDotDieuTriId,
      });
  };

  const renderAdditionalItem = (label, content, showBullet = true) => {
    return (
      <span className="additional-item">
        <span>{`${showBullet ? "\u00A0-\u00A0" : ""}${label}`}:</span>
        <b className="info">{content}</b>
      </span>
    );
  };

  function formatDaysToWeeks(days) {
    if (!days) return null;
    const weeks = Math.floor(days / 7);
    const remainDays = days % 7;
    return `${weeks > 0 ? `${weeks} tuần` : ""}${
      remainDays > 0 ? ` ${remainDays} ngày` : ""
    }`;
  }

  const renderDoiTuong = useMemo(() => {
    return renderAdditionalItem(
      t("common.doiTuong"),
      listDoiTuong.find((item) => item.id === thongTinCoBan.doiTuong)?.ten
    );
  }, [thongTinCoBan, listDoiTuong]);

  const renderLoaiDoiTuong = useMemo(() => {
    return renderAdditionalItem(
      t("common.loaiDoiTuong"),
      listAllLoaiDoiTuong.find(
        (item) => item.id === thongTinCoBan.loaiDoiTuongId
      )?.ten
    );
  }, [thongTinCoBan, listAllLoaiDoiTuong]);

  const maskPhoneNumber = (phone) => {
    if (!phone || checkRole([ROLES["TIEP_DON"].HIEN_THI_DAY_DU_SDT])) {
      return phone;
    }
    return phone.replace(/(\d{4})\d{3}(\d{3})/, "$1xxx$2");
  };

  const renderSdtNguoiBenh = useMemo(() => {
    return (
      isShowSdtNb &&
      renderAdditionalItem(
        t("common.sdt"),
        maskPhoneNumber(thongTinCoBan.soDienThoai)
      )
    );
  }, [isShowSdtNb, thongTinCoBan]);

  const renderNguoiBaoLanh = useMemo(() => {
    const {
      tenNguoiBaoLanh1,
      sdtNguoiBaoLanh1,
      tenNguoiBaoLanh2,
      sdtNguoiBaoLanh2,
    } = thongTinCoBan || {};

    let result = [
      tenNguoiBaoLanh1,
      tenNguoiBaoLanh2,
      sdtNguoiBaoLanh1,
      sdtNguoiBaoLanh2,
    ].filter((item) => item);

    return (
      isShowThongTinBaoLanh &&
      !!result?.length &&
      renderAdditionalItem(t("common.nguoiBaoLanh"), result.join("/"))
    );
  }, [isShowThongTinBaoLanh, thongTinCoBan]);

  const getNguyCoTeNga = (danhGia) => {
    const DANH_GIA = {
      1: t("quanLyNoiTru.khongCoNguyCoNga"),
      2: t("quanLyNoiTru.nguyCoThap"),
      3: t("quanLyNoiTru.nguyCoTrungBinh"),
      4: t("quanLyNoiTru.nguyCoCao"),
    };

    const COLOR = {
      1: "#0747a6",
      2: "#172b4d",
      3: "#ff8b00",
      4: "#de350b",
    };

    const level = danhGia[0];
    return {
      text: DANH_GIA[level] || "",
      color: COLOR[level] || "blue",
    };
  };

  const renderDiUng = useMemo(() => {
    const diUng = thongTinCoBan?.diUng;
    if (isNil(diUng)) return null;

    const diUngValue = diUng ? t("common.co") : t("common.khong");
    return renderAdditionalItem(t("quanLyNoiTru.diUng"), diUngValue);
  }, [thongTinCoBan]);

  const renderChiTietDiUng = useMemo(() => {
    const dsTacNhanDiUng = thongTinCoBan?.dsTacNhanDiUng;
    if (!isArray(dsTacNhanDiUng, 1)) return null;

    const chiTietText = dsTacNhanDiUng.map((item) => item.ten).join(", ");
    return renderAdditionalItem(t("quanLyNoiTru.chiTietDiUng"), chiTietText);
  }, [thongTinCoBan]);

  const renderNguyCoTeNga = useMemo(() => {
    const khungDanhGia = chiTietNguoiBenhNoiTru?.khungDanhGiaNguyCoNgaMorse;
    if (!isArray(khungDanhGia, 1)) return null;

    const latestAssessment = khungDanhGia[0];

    let danhGia = null;
    let latestTime = null;

    const periods = [
      "sauKhiBiTeNga",
      "thayDoiTinhTrangBenhLy",
      "sauThuThuat",
      "sauPhauThuat",
      "vaoKhoa",
    ];

    for (const period of periods) {
      const periodData = latestAssessment[period];
      if (periodData?.danhGia && periodData?.thoiGianDanhGia) {
        const time = new Date(periodData.thoiGianDanhGia);
        if (!latestTime || time > latestTime) {
          latestTime = time;
          danhGia = periodData.danhGia;
        }
      }
    }

    if (!danhGia) return null;

    const { text, color } = getNguyCoTeNga(danhGia);

    return renderAdditionalItem(
      t("quanLyNoiTru.nguyCoTeNga"),
      <span style={{ color, fontWeight: "bold" }}>{text}</span>
    );
  }, [chiTietNguoiBenhNoiTru]);

  const renderCanhBaoSom = useMemo(() => {
    const canhBaoSomValue = chiTietNguoiBenhNoiTru?.chamSocCap23CanhBaoSom;
    if (!canhBaoSomValue) return null;

    return renderAdditionalItem(t("quanLyNoiTru.canhBaoSom"), canhBaoSomValue);
  }, [chiTietNguoiBenhNoiTru]);

  const renderChamSocCap = useMemo(() => {
    const tenCheDoChamSoc = chiTietNguoiBenhNoiTru?.tenCheDoChamSoc;
    if (!tenCheDoChamSoc) return null;

    return renderAdditionalItem(
      t("quanLyNoiTru.chamSocCap"),
      upperFirst(tenCheDoChamSoc)
    );
  }, [chiTietNguoiBenhNoiTru]);

  const isShowThoiGianNhapVien =
    nbLapBenhAn &&
    checkRole([ROLES["QUAN_LY_NOI_TRU"].CHINH_SUA_THOI_GIAN_LAP_BENH_AN]) &&
    nbLapBenhAn.trangThai > 10 &&
    nbLapBenhAn.trangThai < 100;

  const renderThoiGianVaoVien = useMemo(() => {
    if (!isShowThoiGianVaoVien) return null;
    let date = null;
    if (nbLapBenhAn) {
      if (nbLapBenhAn?.thoiGianLapBenhAn)
        date = moment(nbLapBenhAn?.thoiGianLapBenhAn).format(
          "DD/MM/YYYY HH:mm:ss"
        );
    } else {
      if (thongTinCoBan?.thoiGianVaoVien)
        date = moment(thongTinCoBan.thoiGianVaoVien).format(
          "DD/MM/YYYY HH:mm:ss"
        );
    }

    const node = isShowThoiGianNhapVien ? (
      <div className="thoi-gian-vao-vien-editable">
        <span>{date}</span>
        <SVG.IcEdit
          className="cursor-pointer"
          onClick={onShowChinhSuaThoiGianNhapVien}
        />
      </div>
    ) : (
      <span>{date}</span>
    );

    return renderAdditionalItem(
      nbLapBenhAn
        ? t("dieuTriDaiHan.thoiGianLapBenhAn")
        : t("tenTruong.thoiGianVaoVien"),
      node
    );
  }, [
    isShowThoiGianVaoVien,
    nbLapBenhAn,
    thongTinCoBan,
    isShowThoiGianNhapVien,
  ]);

  const renderThoiGianHenKham = useMemo(() => {
    return (
      isShowNgayHenKham &&
      renderAdditionalItem(
        t("tiepDon.ngayHenKham"),
        thongTinCoBan.thoiGianHenKham
          ? moment(thongTinCoBan.thoiGianHenKham).format("DD/MM/YYYY")
          : ""
      )
    );
  }, [isShowNgayHenKham, thongTinCoBan]);

  const mergeTitle = (...args) => (
    <>
      {args
        .filter((item) => (item === 0 ? false : Boolean(item)))
        .map((arg, index) => (
          <React.Fragment key={index}>
            {index > 0 && " - "}
            {arg}
          </React.Fragment>
        ))}
    </>
  );

  const onLienKetNb = (value) => () => {
    lienKetNb({
      nbDotDieuTriId,
      nbLienKetId: value,
    }).then(() => {
      getThongTinCoBan(nbDotDieuTriId);
    });
  };

  const onShowPopup = () => {
    refModalTimKiemNB.current &&
      refModalTimKiemNB.current.show((value) => {
        onLienKetNb(value?.idNb)();
      });
  };

  const renderDoiTuong1 = () => {
    const tenDoiTuong = thongTinCoBan.thongTuyen
      ? t("tiepDon.thongTuyen")
      : thongTinCoBan.capCuu
      ? t("tiepDon.capCuu")
      : thongTinCoBan.traiTuyen
      ? t("thuNgan.traiTuyen")
      : thongTinCoBan.dungTuyen
      ? t("thuNgan.dungTuyen")
      : "";
    const mienCungChiTra = thongTinCoBan.mienCungChiTra
      ? t("common.mienCCT")
      : "";

    return tenDoiTuong && mergeTitle(tenDoiTuong, mienCungChiTra);
  };

  const renderThongTinBaoHiem = useMemo(() => {
    if (thongTinCoBan.doiTuong !== DOI_TUONG.BAO_HIEM) return;
    const mucHuong = thongTinCoBan.mucHuongTheBhyt ? (
      thongTinCoBan.theTam ? (
        <span>
          <span>({thongTinCoBan.mucHuongTheBhyt}%</span>
          <span> - {renderDoiTuong1()}</span>
          <span style={{ color: "red" }}> - {t("common.theTam")}</span>)
        </span>
      ) : (
        <span>({thongTinCoBan.mucHuongTheBhyt}%)</span>
      )
    ) : (
      ""
    );
    return (
      <>
        {renderAdditionalItem(
          t("common.soBHYT"),
          <>
            {thongTinCoBan.maTheBhyt} {mucHuong}
          </>
        )}
        {renderAdditionalItem(
          t("common.hanThe"),
          thongTinCoBan.tuNgayTheBhyt
            ? `${thongTinCoBan.tuNgayTheBhyt
                ?.toDateObject()
                .format("dd/MM/yyyy")} - ${thongTinCoBan.denNgayTheBhyt
                ?.toDateObject()
                .format("dd/MM/yyyy")}`
            : ""
        )}
      </>
    );
  }, [thongTinCoBan]);

  const onCheckThe = () => {
    let data = {
      hoTen: thongTinCoBan.tenNb,
      maThe: thongTinCoBan?.maTheBhyt,
      ngaySinh: moment(thongTinCoBan?.ngaySinh).format("YYYY-MM-DD"),
      khoaId: thongTinCoBan.khoaNbId,
    };
    giamDinhThe(data)
      .then((data) => {
        refModalCheckBaoHiem.current.show({
          show: true,
          data: data,
          hoTen: thongTinCoBan.tenNb,
          diaChi: thongTinCoBan.diaChi,
        });
        const errors = onCheckThongTinTheBh({
          thongTinCoBan,
          dataBaoHiem: data?.data,
          listGioiTinh,
        });
        if (errors?.length) {
          const content = (
            <div>
              <div style={{ color: "red", paddingBottom: "10px" }}>
                {t("common.thongTinBaoHiemCuaNguoiBenhDangKhacTrenCong")}{" "}
              </div>
              <TableWrapper
                columns={[
                  {
                    title: <HeaderSearch title={t("thuNgan.truongThongTin")} />,
                    width: 100,
                    dataIndex: "title",
                    key: "title",
                  },
                  {
                    title: <HeaderSearch title={t("baoCao.nguoiBenh")} />,
                    width: 150,
                    dataIndex: "nguoiBenh",
                    key: "nguoiBenh",
                  },
                  {
                    title: <HeaderSearch title={t("thuNgan.congBaoHiem")} />,
                    width: 150,
                    dataIndex: "congBh",
                    key: "congBh",
                  },
                ]}
                dataSource={errors || []}
              />
            </div>
          );
          console.log("errors", errors);
          showConfirm({
            title: t("common.thongBao"),
            content: content,
            cancelText: t("common.huy"),
            classNameOkText: "button-warning",
            typeModal: "warning",
            isContentElement: true,
            width: 768,
          });
        }
      })
      .catch((e) => {});
  };

  return (
    <Main style={{ ...style }} className="thong-tin-benh-nhan">
      <PatientInfoWrapper>
        {isShowAvatar && (
          <div className="img-avatar">
            {thongTinCoBan.anhDaiDien ? (
              <Image
                preview={false}
                src={thongTinCoBan.anhDaiDien}
                width={60}
                height={60}
                onClick={onShowAnhDaiDien}
              />
            ) : (
              <Avatar icon={<UserOutlined />} size={60} shape={"square"} />
            )}
          </div>
        )}

        {isEmpty(thongTinCoBan) || isLoadingThongTinCoBan ? (
          <div className="patient-content">
            <Skeleton active paragraph={{ rows: 1 }} />
          </div>
        ) : (
          <PatientContent className="patient-content">
            <div className="head">
              {isShowMaBenhAn ? (
                <div className="benhAn">
                  {window.location.pathname.indexOf("/quan-ly-noi-tru") >= 0
                    ? `${t("common.maBa")}: ${thongTinCoBan.maBenhAn || ""}`
                    : thongTinCoBan?.tiemChung
                    ? `${t("tiemChung.maTiemChung")}: ${
                        thongTinCoBan.maTiemChung || ""
                      }`
                    : isShowMaTiemChung &&
                      thongTinCoBan?.doiTuongKcb ==
                        DOI_TUONG_KCB.DIEU_TRI_NOI_TRU
                    ? `${t("tiemChung.maTiemChung")}: ${
                        thongTinCoBan.maTiemChung || ""
                      } | ${t("common.maBa")}: ${thongTinCoBan.maBenhAn || ""}`
                    : `${t("common.maBa")}: ${thongTinCoBan.maBenhAn || ""}`}
                </div>
              ) : (
                isShowMaNb && (
                  <div className="benhAn">
                    {renderAdditionalItem(
                      t("common.maNb"),
                      thongTinCoBan.maNb,
                      false
                    )}
                  </div>
                )
              )}
              <div className="name">
                <span className="text-fullname">{thongTinCoBan.tenNb}</span>{" "}
                <span className="more-info">
                  {mergeTitle(
                    gioiTinh.ten && (
                      <>
                        (
                        {mergeTitle(
                          gioiTinh.ten,
                          tuoiNguoiBenh,
                          isShowNgaySinh &&
                            thongTinCoBan?.ngaySinh &&
                            moment(thongTinCoBan?.ngaySinh).format(
                              "DD/MM/YYYY"
                            ),
                          isShowGioSinh &&
                            thongTinCoBan?.ngaySinh &&
                            moment(thongTinCoBan?.ngaySinh).format("HH:mm:ss"),
                          thongTinCoBan.canNang && canNang,
                          isArray(thongTinCoBan?.dsPhanLoaiNbId, true) && (
                            <PhanLoaiNguoiBenh
                              thongTinBenhNhan={thongTinCoBan}
                            />
                          )
                        )}
                        )
                      </>
                    ),
                    mergeTitle(
                      thongTinCoBan.khoaNbVietTat,
                      thongTinCoBan.tenPhong
                        ? `${labelPhong}${thongTinCoBan.tenPhong}`
                        : "",
                      thongTinCoBan.soHieuGiuong
                        ? `${labelGiuong}${thongTinCoBan.soHieuGiuong}`
                        : ""
                    ),
                    formatDaysToWeeks(thongTinCoBan.ngayThai),
                    canNang,
                    hangTheId && iconHangThe && (
                      <span className="hangTheIcon">
                        <Popover
                          placement="top"
                          content={`${tenHangThe}: ${diemToiThieu} ${t(
                            "hsba.diem"
                          )}`}
                        >
                          <img
                            src={`${fileUtils.absoluteFileUrl(iconHangThe)}`}
                            alt=""
                          />
                        </Popover>
                      </span>
                    )
                  )}
                </span>
                {renderThongTinMeCon && renderThongTinMeCon()}
              </div>
              {!isEmpty(thongTinCoBan) && (
                <div className="bunch-icon">
                  {checkRole([
                    ROLES["QUAN_LY_NOI_TRU"].HIEN_THI_ICON_DANG_KY_VAN_TAY,
                  ]) && (
                    <Tooltip
                      title={t("common.dangKyVanTay")}
                      placement="topLeft"
                    >
                      <IconActionAnimation
                        onClick={() => onShowModalDangKyVanTay()}
                      >
                        <SVG.IcFingerPrint />
                      </IconActionAnimation>
                    </Tooltip>
                  )}
                  {checkRole([ROLES["HO_SO_BENH_AN"].XEM_LICH_SU_KCB]) && (
                    <Tooltip
                      title={t("common.lichSuKhamChuaBenh")}
                      placement="topLeft"
                    >
                      <IconActionAnimation onClick={() => onShowLichSuKCB()}>
                        <SVG.IcLichSuKCB />
                      </IconActionAnimation>
                    </Tooltip>
                  )}
                  {isShowThuocKhangSinh && (
                    <Tooltip
                      title={t("kho.xemLichSuSuDungThuocKhangSinh")}
                      placement="topLeft"
                      overlayStyle={{ maxWidth: "500px" }}
                    >
                      <IconActionAnimation
                        onClick={() => onShowLichSuThuocKhangSinh()}
                      >
                        <SVG.IcLichSu />
                      </IconActionAnimation>
                    </Tooltip>
                  )}
                  {checkRole([
                    ROLES["QUAN_LY_NOI_TRU"].SUA_THONG_TIN_HANH_CHINH_NOI_TRU,
                  ]) &&
                    isShowXemChiTiet && (
                      <Tooltip
                        title={t("common.xemChiTietThongTin")}
                        placement="topLeft"
                      >
                        <IconActionAnimation
                          onClick={() => onShowThongTinNb(false)}
                        >
                          <SVG.IcInfo />
                        </IconActionAnimation>
                      </Tooltip>
                    )}
                  {checkRole([
                    ROLES["QUAN_LY_NOI_TRU"].SUA_THONG_TIN_HANH_CHINH_NOI_TRU,
                  ]) &&
                    isShowSuaChiTiet && (
                      <Tooltip
                        title={t("common.suaChiTietThongTin")}
                        placement="topLeft"
                      >
                        <IconActionAnimation
                          onClick={() => onShowThongTinNb(true)}
                        >
                          <SVG.IcEdit />
                        </IconActionAnimation>
                      </Tooltip>
                    )}
                  {checkRole([
                    ROLES["QUAN_LY_NOI_TRU"].XEM_HO_SO_BENH_AN_NOI_TRU,
                  ]) &&
                    isShowHsba && (
                      <Tooltip
                        title={t("quanLyNoiTru.xemHoSoBenhAn")}
                        placement="topLeft"
                      >
                        <IconActionAnimation onClick={() => onShowHoSoBenhAn()}>
                          <SVG.IcHsba />
                        </IconActionAnimation>
                      </Tooltip>
                    )}
                  {thongTinCoBan.doiTuong === DOI_TUONG.BAO_HIEM &&
                    isShowGiaHanThe && (
                      <Tooltip
                        title={t(
                          "quanLyNoiTru.giaHanThe.giaHanTheChuyenDoiTuong"
                        )}
                        placement="topLeft"
                      >
                        <IconActionAnimation
                          onClick={() => {
                            if (
                              thongTinCoBan.trangThaiNb ===
                              TRANG_THAI_NB.CHO_TIEP_NHAN_VAO_KHOA
                            ) {
                              return;
                            }
                            refModalGiaHanThe.current &&
                              refModalGiaHanThe.current.show({
                                nbDotDieuTriId: nbDotDieuTriId,
                              });
                          }}
                        >
                          <SVG.IcDoiDoiTuong />
                        </IconActionAnimation>
                      </Tooltip>
                    )}
                  {isShowLichSuTiemChung && (
                    <Tooltip
                      title={t("quanLyNoiTru.xemLichSuTiemChung")}
                      placement="topLeft"
                    >
                      <IconActionAnimation onClick={onXemLichSuTiemChung}>
                        <SVG.IcTiemChungBg />
                      </IconActionAnimation>
                    </Tooltip>
                  )}
                  {isShowDeNghiTamUng && (
                    <Tooltip
                      title={t("thuNgan.quanLyTamUng.deNghiTamUng")}
                      placement="topLeft"
                    >
                      <IconActionAnimation onClick={onDeNghiTamUng}>
                        <SVG.IcDeNghiTamTung className="icon-tam-ung" />
                      </IconActionAnimation>
                    </Tooltip>
                  )}
                  {checkRole([
                    ROLES["QUAN_LY_NOI_TRU"]
                      .HIEN_THI_DANH_SACH_PHIEU_THU_TAM_UNG,
                  ]) &&
                    isShowDsPhieuThuTamUng && (
                      <Tooltip
                        title={t("thuNgan.quanLyTamUng.danhSachPhieuThuTamUng")}
                        placement="topLeft"
                      >
                        <IconActionAnimation onClick={onShowDsPhieuThuTamUng}>
                          <SVG.IcThuTamUng className="icon-tam-ung" />
                        </IconActionAnimation>
                      </Tooltip>
                    )}
                  {isShowLichSuMuonBA && (
                    <Tooltip title={t("khth.xemLichSu")} placement="topLeft">
                      <IconActionAnimation onClick={onShowLichSu}>
                        <SVG.IcLichSu />
                      </IconActionAnimation>
                    </Tooltip>
                  )}
                  {isShowScanBieuMau && (
                    <Tooltip
                      title={t("common.scanBieuMauHsba")}
                      placement="topLeft"
                    >
                      <IconActionAnimation onClick={onShowScanBieuMau}>
                        <SVG.IcScanBieuMau color="white" />
                      </IconActionAnimation>
                    </Tooltip>
                  )}
                  {isShowFolderFile && (
                    <Tooltip title={t("common.thuMuc")} placement="topLeft">
                      <IconActionAnimation onClick={onShowFolderFile}>
                        <SVG.IcFolderUpload />
                      </IconActionAnimation>
                    </Tooltip>
                  )}
                  {checkRole([
                    ROLES["QUAN_LY_NOI_TRU"].XEM_SO_DO_PHONG_GIUONG,
                  ]) &&
                    isShowSoDoPhongGiuong && (
                      <Tooltip
                        title={t("quanLyNoiTru.dvNoiTru.soDoPhongGiuong")}
                        placement="topLeft"
                      >
                        <IconActionAnimation
                          onClick={() => onShowSoDoPhongGiuong()}
                        >
                          <SVG.IcPhongGiuong />
                        </IconActionAnimation>
                      </Tooltip>
                    )}
                  {modeDsNb === DATA_MODE_DS_NB.DRAWER && (
                    <Tooltip
                      title={t("quanLyNoiTru.xemDsNb")}
                      placement="topLeft"
                    >
                      <IconActionAnimation onClick={openDrawerDsNb}>
                        <SVG.IcList />
                      </IconActionAnimation>
                    </Tooltip>
                  )}
                  {checkRole([ROLES["QUAN_LY_NOI_TRU"].DIEU_TRI_LAO]) &&
                    isShowThongTinDieuTriLao && (
                      <Tooltip
                        title={t("quanLyNoiTru.dieuTriLao")}
                        placement="topLeft"
                      >
                        <IconActionAnimation
                          onClick={() => onShowTTDieuTriLao()}
                        >
                          <SVG.IcDieuTriLaoNoiTru className="ic-dieu-tri-lao" />
                        </IconActionAnimation>
                      </Tooltip>
                    )}
                  {checkRole([ROLES["QUAN_LY_NOI_TRU"].XEM_CHECK_LOG_NB]) &&
                    isShowLogChinhSua && (
                      <Tooltip
                        title={t("quanLyNoiTru.kiemTraLichSuThayDoi")}
                        placement="topLeft"
                      >
                        <IconActionAnimation onClick={onShowLogModal}>
                          <SVG.IcShowLog />
                        </IconActionAnimation>
                      </Tooltip>
                    )}
                  {isShowPhieuNbLienKet && (
                    <Tooltip
                      title={t("common.xemDanhSachPhieuHoSoDuSinh")}
                      placement="topLeft"
                    >
                      <IconActionAnimation
                        onClick={onShowDanhSachPhieuNbLienKet}
                      >
                        <SVG.IcList />
                      </IconActionAnimation>
                    </Tooltip>
                  )}
                  {isShowDongBoGia && (
                    <Tooltip
                      title={t("khamSucKhoe.dongBoGia")}
                      placement="topLeft"
                    >
                      <IconActionAnimation onClick={onDongBoGia}>
                        <SVG.IcDongBoGia />
                      </IconActionAnimation>
                    </Tooltip>
                  )}
                  {thongTinCoBan?.maTheBhyt && (
                    <Tooltip title={t("thuNgan.kiemTraThongTinBhyt")}>
                      <IconActionAnimation fill={false}>
                        <SVG.IcTheBaoHiem onClick={onCheckThe} />
                      </IconActionAnimation>
                    </Tooltip>
                  )}
                </div>
              )}
            </div>
            <div className="patient-information" ref={refPatientInfoWrapper}>
              <Row>
                <span>
                  {isShowMaNbLienKet && (
                    <span className="ma-lien-ket ml2">
                      {t("common.maNbLienKet")}:{" "}
                      {nbLienKetId ? state.maNbLienKet : ""}
                      <Tooltip title={t("dieuTriDaiHan.suaMaNbLienKet")}>
                        <SVG.IcEdit
                          onClick={onShowPopup}
                          style={{ cursor: "pointer", marginLeft: 5 }}
                        />
                      </Tooltip>
                      {nbLienKetId && (
                        <Tooltip title={t("dieuTriDaiHan.xoaMaNbLienKet")}>
                          <SVG.IcDelete
                            onClick={onLienKetNb(null)}
                            style={{ cursor: "pointer", marginLeft: 5 }}
                          />
                        </Tooltip>
                      )}
                    </span>
                  )}
                  {isShowThongTinDiaChi &&
                    renderAdditionalItem(
                      t("common.diaChi"),
                      thongTinCoBan.diaChi,
                      state.patientInfoWrapperHeight > 40
                    )}
                  {isShowMaHs &&
                    renderAdditionalItem(
                      t("common.maHs"),
                      thongTinCoBan.maHoSo
                    )}
                  {isShowMaNb &&
                    isShowMaBenhAn &&
                    renderAdditionalItem(t("common.maNb"), thongTinCoBan.maNb)}
                  {isShowFullThongTinThanhToan ? <br /> : null}
                  {isShowDoiTuong && renderDoiTuong}
                  {isShowLoaiDoiTuong && renderLoaiDoiTuong}
                  {isShowThongTinBaoHiem && renderThongTinBaoHiem}
                  {isShowShowSdtNb && renderSdtNguoiBenh}
                  {isShowNguoiBaoLanh && renderNguoiBaoLanh}
                  {isShowThoiGianHenKham && renderThoiGianHenKham}
                  {isShowFullThongTinThanhToan ? <br /> : null}
                  {renderThoiGianVaoVien}
                  {isShowThongTinThanhToan && !isShowFullThongTinThanhToan && (
                    <>
                      {renderAdditionalItem(
                        t("thuNgan.soTienConLai"),
                        <span style={{ color: "red" }}>
                          {(soTienConLaiMemo || 0).formatPrice()}đ
                        </span>
                      )}
                      {renderAdditionalItem(
                        t("thuNgan.soTienTamUng"),
                        ((thongTinCoBan.tienTamUng || 0) &&
                          (
                            thongTinCoBan.tienTamUng -
                            (thongTinCoBan.tienHoanUng || 0)
                          ).formatPrice()) + "đ"
                      )}
                    </>
                  )}
                  {chiTietDuyetBH && (
                    <>
                      {renderAdditionalItem(
                        t("khth.nguoiDuyetBH"),
                        chiTietDuyetBH.nguoiDuyet
                      )}
                      {renderAdditionalItem(
                        t("khth.thoiGianDuyetBh"),
                        chiTietDuyetBH.thoiGianDuyet
                      )}
                    </>
                  )}
                </span>
              </Row>
              {dataHIEN_THI_CAC_FIELD_CANH_BAO_NOI_TRU?.eval() && (
                <Row>
                  {renderDiUng}
                  {renderChiTietDiUng}
                  {renderNguyCoTeNga}
                  {renderCanhBaoSom}
                  {renderChamSocCap}
                </Row>
              )}
            </div>
          </PatientContent>
        )}
      </PatientInfoWrapper>
      {checkRole([ROLES["QUAN_LY_NOI_TRU"].THONG_TIN_SO_TIEN]) &&
        isShowFullThongTinThanhToan &&
        isShowThongTinThanhToan && (
          <ThongTinSoTien soTienConLai={soTienConLaiMemo} />
        )}
      {(isShowXemChiTiet || isShowSuaChiTiet) && (
        <LazyLoad
          component={() =>
            import("../../../../tiepDon/TiepDon/ModalChinhSuaThongTin")
          }
          ref={refModalChinhSuaThongTin}
        />
      )}
      {isShowHsba && (
        <LazyLoad
          component={() =>
            import(
              "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/Modal/ModalHoSoBenhAn"
            )
          }
          ref={refModalHoSoBenhAn}
        />
      )}
      {isShowGiaHanThe && (
        <LazyLoad
          component={() =>
            import("pages/quanLyNoiTru/components/ModalGiaHanThe")
          }
          ref={refModalGiaHanThe}
        />
      )}
      {isShowDeNghiTamUng && (
        <LazyLoad
          component={() =>
            import(
              "pages/thuNgan/quanLyTamUng/chiTietQuanLyTamUng/Modal/ModalDeNghiTamUng"
            )
          }
          ref={refModalDeNghiTamUng}
        />
      )}
      {isShowLichSuTiemChung && (
        <LazyLoad
          component={() =>
            import(
              "pages/hoSoBenhAn/ChiTietNguoiBenh/containers/ModalLichSuTiemChung"
            )
          }
          ref={refModalLichSuTiemChung}
        />
      )}
      {isShowThuocKhangSinh && (
        <LazyLoad
          component={() =>
            import(
              "pages/kho/DuyetDuocLamSang/ChiTietDuyetDuocLamSang/ModalLichSuThuocKhangSinh"
            )
          }
          ref={refModalLichSuThuocKhangSinh}
        />
      )}
      {thongTinCoBan.anhDaiDien && <ModalAnhDaiDien ref={refAnhDaiDien} />}
      {isShowLichSuMuonBA && (
        <LazyLoad
          component={() =>
            import(
              "pages/keHoachTongHop/ChiTietLuuTruBA/modals/ModalLichSuMuonBA"
            )
          }
          ref={refModalLichSuMuonBA}
        />
      )}
      {isShowScanBieuMau && (
        <LazyLoad
          component={() =>
            import("pages/hoSoBenhAn/components/ModalDanhSachBieuMauScan")
          }
          ref={refModalDanhSachBieuMauScan}
        />
      )}
      {isShowFolderFile && (
        <LazyLoad
          component={() => import("components/ModalFolderAndFile")}
          ref={refModalFoderAndFile}
        ></LazyLoad>
      )}
      {isShowSoDoPhongGiuong && (
        <LazyLoad
          component={() => import("pages/quanLyNoiTru/ModalSoDoPhongGiuong")}
          ref={refModalSoDoPhongGiuong}
          isShowInfo={false}
        />
      )}
      {checkRole([ROLES["QUAN_LY_NOI_TRU"].DIEU_TRI_LAO]) &&
        isShowThongTinDieuTriLao && (
          <LazyLoad
            component={() =>
              import(
                "pages/quanLyDieuTriLao/DanhSachNbDieuTriLao/components/ModalThemMoiNbDieuTriLao"
              )
            }
            ref={refModalThemMoiNbDieuTriLao}
          />
        )}
      {isShowMaNbLienKet && (
        <LazyLoad
          component={() => import("pages/khamBenh/components/ModalTimKiemNB")}
          ref={refModalTimKiemNB}
        />
      )}
      {isShowThoiGianNhapVien && (
        <LazyLoad
          component={() =>
            import(
              "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/ThongTinVaoVien/ModalChinhSuaThoiGianNhapVien"
            )
          }
          ref={refModalChinhSuaThoiGianNhapVien}
        />
      )}
      {checkRole([ROLES["QUAN_LY_NOI_TRU"].XEM_CHECK_LOG_NB]) &&
        isShowLogChinhSua && (
          <LazyLoad
            component={() =>
              import(
                "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ModalLichSuChinhSua"
              )
            }
            ref={refModalLichSuChinhSua}
          />
        )}
      <LazyLoad
        component={() =>
          import(
            "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/ModalDangKyVanTay"
          )
        }
        ref={refModalDkiVanTay}
      />
      {checkRole([
        ROLES["QUAN_LY_NOI_TRU"].HIEN_THI_DANH_SACH_PHIEU_THU_TAM_UNG,
      ]) &&
        isShowDsPhieuThuTamUng && (
          <LazyLoad
            component={() =>
              import("pages/tiepDon/KeDichVuKham/RightPanel/ModalDsThuTamUng")
            }
            ref={refModalDsPhieuThuTamUng}
          />
        )}

      {checkRole([ROLES["HO_SO_BENH_AN"].XEM_LICH_SU_KCB]) && (
        <LazyLoad
          component={() => import("components/ModalLichSuKCB")}
          ref={refModalLichSuKCB}
        />
      )}
      {isShowPhieuNbLienKet && (
        <LazyLoad
          component={() =>
            import(
              "pages/hoSoBenhAn/ChiTietNguoiBenh/containers/ModalXemPhieuHoSoLienKet"
            )
          }
          ref={refModalDanhSachPhieuNbLienKet}
        />
      )}
      {thongTinCoBan?.maTheBhyt && (
        <LazyLoad
          component={() =>
            import("pages/tiepDon/components/ThongTinTiepDon/ModalCheckBaoHiem")
          }
          ref={refModalCheckBaoHiem}
          isShowButtonOk={false}
          isShowButtonCancel={true}
        />
      )}
    </Main>
  );
};

export default memo(ThongTinBenhNhan);
