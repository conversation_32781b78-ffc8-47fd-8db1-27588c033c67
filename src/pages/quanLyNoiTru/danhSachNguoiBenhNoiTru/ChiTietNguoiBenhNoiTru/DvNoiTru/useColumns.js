import React, { useMemo, useState } from "react";
import {
  DatePicker,
  InputTimeout,
  Select,
  TableWrapper,
  TextField,
  Checkbox,
} from "components";
import { useEnum, useListAll, useStore } from "hooks";
import DOMPurify from "dompurify";
import { t } from "i18next";
import { groupBy, isEmpty } from "lodash";
import moment from "moment";
import {
  ENUM,
  HIEU_LUC,
  LOAI_CHI_DINH,
  LOAI_DICH_VU,
  TRANG_THAI_THUOC,
  YES_NO,
} from "constants/index";
import KetQuaXetNghiem from "pages/khamBenh/ChiDinhDichVu/components/KetQuaXetNghiem";

const { Column } = TableWrapper;

function useColumns() {
  const [state, setState] = useState({});
  const [listTrangThaiDichVu] = useEnum(ENUM.TRANG_THAI_DICH_VU);
  const [listLoaiDichVu] = useEnum(ENUM.LOAI_DICH_VU);
  const [listTrangThaiMau] = useEnum(ENUM.TRANG_THAI_MAU);
  const [listNhomMau] = useEnum(ENUM.NHOM_MAU);
  const [listloaiThuoc] = useEnum(ENUM.LOAI_THUOC);
  const [listTrangThaiThuoc] = useEnum(ENUM.TRANG_THAI_THUOC);

  const listLoaiHinhThanhToanCuaDoiTuong = useStore(
    "loaiDoiTuongLoaiHinhTT.listLoaiHinhThanhToanCuaDoiTuong",
    []
  );

  const [listAllNhomDichVuCap1] = useListAll("nhomDichVuCap1", {}, true);
  const [listAllKho] = useListAll("kho", {}, true);
  const [listAllPhong] = useListAll("phong", {}, true);
  const [listAllKhoa] = useListAll("khoa", {}, true);
  const [listAllNhanVien] = useListAll("nhanVien", {}, true);
  const [listAllNhomDichVuCap2] = useListAll("nhomDichVuCap2", {}, true);
  const [listtrangThaiHoan] = useEnum(ENUM.TRANG_THAI_HOAN);
  const [listPhanLoaiPTTT] = useEnum(ENUM.BENH_PHAM);
  const [listAllBenhPham] = useListAll("benhPham", {}, true);

  const listTrangThaiDvMemo = useMemo(() => {
    const groupTrangThai = groupBy(listTrangThaiDichVu, "ten");
    return Object.keys(groupTrangThai).map((key, index) => ({
      ten: key,
      id: index,
      value: groupTrangThai[key].map((x) => x.id),
    }));
  }, [listTrangThaiDichVu]);

  const customSearch = (key, value, onSearch) => {
    let newSearch = {},
      newState = { ...state, [key]: value };
    if (key === "dotXuat") {
      newSearch["dsLoaiChiDinh"] =
        value == "true"
          ? LOAI_CHI_DINH.DOT_XUAT
          : value == "false"
          ? [LOAI_CHI_DINH.THUONG, LOAI_CHI_DINH.BO_SUNG]
          : undefined;
      if (value) {
        newState["boSung"] = null;
      }
      delete newSearch.dotXuat;
    } else if (key === "boSung") {
      newSearch["dsLoaiChiDinh"] =
        value == "true"
          ? LOAI_CHI_DINH.BO_SUNG
          : value == "false"
          ? [LOAI_CHI_DINH.THUONG, LOAI_CHI_DINH.DOT_XUAT]
          : undefined;
      if (value) {
        newState["dotXuat"] = null;
      }
      delete newSearch.boSung;
    }
    setState(newState);
    if (!isEmpty(newSearch)) {
      for (const key in newSearch) {
        onSearch(key, newSearch[key]);
      }
    }
  };

  const NOITRU_COLUMNS = {
    STT: () =>
      Column({
        title: t("common.stt"),
        width: 40,
        dataIndex: "index",
        key: "index",
        align: "center",
        ignore: true,
      }),
    TEN_NHOM_DICH_VU_CAP1: ({ onSearch, onClickSort, dataSortColumn }) =>
      Column({
        title: t("quanLyNoiTru.dvNoiTru.nhomDvCap1"),
        width: 130,
        dataIndex: "tenNhomDichVuCap1",
        key: "tenNhomDichVuCap1",
        i18Name: "quanLyNoiTru.dvNoiTru.nhomDvCap1",
        sort_key: "tenNhomDichVuCap1",
        dataSort: dataSortColumn["tenNhomDichVuCap1"] || "",
        onClickSort: onClickSort,
        selectSearch: true,
        renderSearch: (
          <Select
            data={listAllNhomDichVuCap1 || []}
            placeholder={t("common.nhapTenDichVu")}
            onChange={(e) => {
              const value = e ? [e] : [];
              onSearch("dsNhomDichVuCap1Id", value);
            }}
          />
        ),
      }),
    TEN_NHOM_DICH_VU_CAP2: ({ onSearch, onClickSort, dataSortColumn }) =>
      Column({
        title: t("quanLyNoiTru.dvNoiTru.nhomDvCap2"),
        width: 130,
        dataIndex: "tenNhomDichVuCap2",
        key: "tenNhomDichVuCap2",
        i18Name: "quanLyNoiTru.dvNoiTru.nhomDvCap2",
        sort_key: "tenNhomDichVuCap2",
        dataSort: dataSortColumn["tenNhomDichVuCap2"] || "",
        onClickSort: onClickSort,
        selectSearch: true,
        renderSearch: (
          <Select
            data={listAllNhomDichVuCap2 || []}
            placeholder={t("common.nhapTenDichVu")}
            onChange={(e) => {
              const value = e ? [e] : [];
              onSearch("dsNhomDichVuCap2Id", value);
            }}
          />
        ),
      }),
    TEN_DICH_VU: ({ onSearch, onClickSort, dataSortColumn, ...rest }) =>
      Column({
        title: t("common.tenDichVu"),
        width: 250,
        dataIndex: "tenDichVu",
        key: "tenDichVu",
        i18Name: "common.tenDichVu",
        sort_key: "tenDichVu",
        dataSort: dataSortColumn["tenDichVu"] || "",
        onClickSort: onClickSort,
        renderSearch: (
          <InputTimeout
            placeholder={t("common.nhapTenDichVu")}
            onChange={(e) => {
              onSearch("tenDichVu", e);
            }}
          />
        ),
        ...rest,
      }),
    TRANG_THAI: ({ onSearch, onClickSort, dataSortColumn }) =>
      Column({
        title: t("common.trangThai"),
        width: 120,
        dataIndex: "trangThai",
        key: "trangThai",
        i18Name: "common.trangThai",
        sort_key: "trangThai",
        dataSort: dataSortColumn["trangThai"] || "",
        onClickSort: onClickSort,
        render: (value) =>
          (listTrangThaiDichVu || []).find((e) => e.id === value)?.ten,
        selectSearch: true,
        renderSearch: (
          <Select
            data={listTrangThaiDvMemo}
            mode="multiple"
            placeholder={t("common.chonTrangThai")}
            onChange={(e) => {
              const selectedTrangThai = listTrangThaiDvMemo.filter((item) =>
                (e || []).includes(item.id)
              );
              const dsTrangThai = (selectedTrangThai || []).reduce(
                (a, b) => [...a, ...b.value],
                []
              );

              onSearch("dsTrangThai", dsTrangThai);
            }}
            dropdownMatchSelectWidth={200}
          />
        ),
      }),
    THOI_GIAN_THUC_HIEN: ({ onSearch, onClickSort, dataSortColumn }, rest) =>
      Column({
        title: t("quanLyNoiTru.dvNoiTru.thoiGianThucHien"),
        sort_key: "thoiGianThucHien",
        width: 160,
        dataIndex: "thoiGianThucHien",
        key: "thoiGianThucHien",
        i18Name: "quanLyNoiTru.dvNoiTru.thoiGianThucHien",
        dataSort: dataSortColumn["thoiGianThucHien"] || "",
        onClickSort: onClickSort,
        render: (item) => item && moment(item).format("DD/MM/YYYY HH:mm:ss"),
        selectSearch: true,
        renderSearch: (
          <DatePicker
            format="DD/MM/YYYY"
            placeholder={t("quanLyNoiTru.dvNoiTru.nhapThoiGian")}
            onChange={(e) => {
              onSearch("ThoiGianThucHien", e);
            }}
          />
        ),
        ...rest,
      }),
    THOI_GIAN_TIEP_NHAN: ({ onSearch, onClickSort, dataSortColumn }, rest) =>
      Column({
        title: t("cdha.thoiGianTiepNhan"),
        sort_key: "thoiGianTiepNhan",
        width: 160,
        dataIndex: "thoiGianTiepNhan",
        key: "thoiGianTiepNhan",
        i18Name: "cdha.thoiGianTiepNhan",
        dataSort: dataSortColumn["thoiGianTiepNhan"] || "",
        onClickSort: onClickSort,
        render: (item) => item && moment(item).format("DD/MM/YYYY HH:mm:ss"),
        selectSearch: true,
        renderSearch: (
          <DatePicker
            format="DD/MM/YYYY"
            placeholder={t("quanLyNoiTru.dvNoiTru.nhapThoiGian")}
            onChange={(e) => {
              onSearch("thoiGianTiepNhan", e);
            }}
          />
        ),
        ...rest,
      }),
    THOI_GIAN_CO_KET_QUA: ({ onSearch, onClickSort, dataSortColumn }, rest) =>
      Column({
        title: t("quanLyNoiTru.dvNoiTru.thoiGianCoKetQua"),
        sort_key: "thoiGianCoKetQua",
        width: 160,
        dataIndex: "thoiGianCoKetQua",
        key: "thoiGianCoKetQua",
        i18Name: "quanLyNoiTru.dvNoiTru.thoiGianCoKetQua",
        dataSort: dataSortColumn["thoiGianCoKetQua"] || "",
        onClickSort: onClickSort,
        render: (item) => item && moment(item).format("DD/MM/YYYY HH:mm:ss"),
        selectSearch: true,
        renderSearch: (
          <DatePicker
            format="DD/MM/YYYY"
            placeholder={t("quanLyNoiTru.dvNoiTru.nhapThoiGian")}
            onChange={(e) => {
              onSearch("ThoiGianCoKetQua", e);
            }}
          />
        ),
        ...rest,
      }),
    THOI_GIAN_CHI_DINH: ({ onSearch, onClickSort, dataSortColumn }) =>
      Column({
        title: t("quanLyNoiTru.dvNoiTru.thoiGianChiDinh"),
        sort_key: "thoiGianChiDinh",
        width: 160,
        dataIndex: "thoiGianChiDinh",
        key: "thoiGianChiDinh",
        i18Name: "quanLyNoiTru.dvNoiTru.thoiGianChiDinh",
        dataSort: dataSortColumn["thoiGianChiDinh"] || "",
        onClickSort: onClickSort,
        render: (item) => item && moment(item).format("DD/MM/YYYY HH:mm:ss"),
        selectSearch: true,
        renderSearch: onSearch && (
          <DatePicker
            format="DD/MM/YYYY"
            placeholder={t("quanLyNoiTru.dvNoiTru.nhapThoiGian")}
            onChange={(e) => {
              onSearch("ThoiGianChiDinh", e);
            }}
          />
        ),
      }),
    THOI_GIAN_PHAT: ({ onSearch, onClickSort, dataSortColumn }) =>
      Column({
        title: t("quanLyNoiTru.dvNoiTru.thoiGianPhat"),
        sort_key: "thoiGianDuyet",
        width: 160,
        dataIndex: "thoiGianDuyet",
        key: "thoiGianDuyet",
        i18Name: "quanLyNoiTru.dvNoiTru.thoiGianPhat",
        dataSort: dataSortColumn["thoiGianDuyet"] || "",
        onClickSort: onClickSort,
        render: (item) => item && moment(item).format("DD/MM/YYYY HH:mm:ss"),
        selectSearch: true,
        renderSearch: (
          <DatePicker
            format="DD/MM/YYYY"
            placeholder={t("quanLyNoiTru.dvNoiTru.nhapThoiGian")}
            onChange={(e) => {
              onSearch("ThoiGianDuyet", e);
            }}
          />
        ),
      }),
    MAN_HINH_CHI_DINH: ({ onSearch, onClickSort, dataSortColumn }) =>
      Column({
        title: t("quanLyNoiTru.dvNoiTru.manHinhChiDinh"),
        sort_key: "chiDinhTuLoaiDichVu",
        width: 160,
        dataIndex: "chiDinhTuLoaiDichVu",
        key: "chiDinhTuLoaiDichVu",
        i18Name: "quanLyNoiTru.dvNoiTru.manHinhChiDinh",
        dataSort: dataSortColumn["chiDinhTuLoaiDichVu"] || "",
        onClickSort: onClickSort,
        render: (value) =>
          (listLoaiDichVu || []).find((e) => e.id === value)?.ten,
        selectSearch: true,
        renderSearch: (
          <Select
            data={listLoaiDichVu}
            mode="multiple"
            placeholder={t("quanLyNoiTru.dvNoiTru.chonManHinhChiDinh")}
            onChange={(e) => {
              onSearch("dsChiDinhTuLoaiDichVu", e);
            }}
            dropdownMatchSelectWidth={200}
          />
        ),
      }),
    KHONG_TINH_TIEN: ({ onSearch, onClickSort, dataSortColumn }, rest) =>
      Column({
        title: t("quanLyNoiTru.dvNoiTru.khongTinhTien"),
        width: 120,
        dataIndex: "khongTinhTien",
        key: "khongTinhTien",
        align: "center",
        i18Name: "quanLyNoiTru.dvNoiTru.khongTinhTien",
        sort_key: "khongTinhTien",
        dataSort: dataSortColumn["khongTinhTien"] || "",
        onClickSort: onClickSort,
        render: (item) => <Checkbox checked={item} />,
        selectSearch: true,
        renderSearch: (
          <Select
            data={HIEU_LUC}
            defaultValue=""
            onChange={(e) => {
              onSearch("khongTinhTien", e);
            }}
            hasAllOption={true}
            dropdownMatchSelectWidth={120}
          />
        ),
        ...rest,
      }),
    TU_TRA: ({ onSearch, onClickSort, dataSortColumn }, rest) =>
      Column({
        title: t("quanLyNoiTru.dvNoiTru.tuTra"),
        width: 100,
        dataIndex: "tuTra",
        key: "tuTra",
        align: "center",
        i18Name: "quanLyNoiTru.dvNoiTru.tuTra",
        sort_key: "tuTra",
        dataSort: dataSortColumn["tuTra"] || "",
        onClickSort: onClickSort,
        render: (item) => <Checkbox checked={item} />,
        selectSearch: true,
        renderSearch: (
          <Select
            data={HIEU_LUC}
            defaultValue=""
            onChange={(e) => {
              onSearch("tuTra", e);
            }}
            hasAllOption={true}
            dropdownMatchSelectWidth={120}
          />
        ),
        ...rest,
      }),
    DOT_XUAT: ({ onSearch, onClickSort, dataSortColumn }, rest) =>
      Column({
        title: t("kho.dotXuat"),
        width: 120,
        dataIndex: "loaiChiDinh",
        key: "dotXuat",
        align: "center",
        i18Name: "kho.dotXuat",
        sort_key: "loaiChiDinh",
        dataSort: dataSortColumn["loaiChiDinh"] || "",
        onClickSort: onClickSort,
        render: (item) => <Checkbox checked={item == LOAI_CHI_DINH.DOT_XUAT} />,
        selectSearch: true,
        renderSearch: (
          <Select
            data={YES_NO}
            value={state.dotXuat}
            onChange={(e) => {
              customSearch("dotXuat", e, onSearch);
            }}
            placeholder={t("kho.chonDotXuat")}
          />
        ),
        ...rest,
      }),
    BO_SUNG: ({ onSearch, onClickSort, dataSortColumn }, rest) =>
      Column({
        title: t("kho.boSung"),
        width: 120,
        dataIndex: "loaiChiDinh",
        key: "boSung",
        align: "center",
        i18Name: "kho.boSung",
        sort_key: "loaiChiDinh",
        dataSort: dataSortColumn["loaiChiDinh"] || "",
        onClickSort: onClickSort,
        render: (item) => <Checkbox checked={item == LOAI_CHI_DINH.BO_SUNG} />,
        renderSearch: (
          <Select
            data={YES_NO}
            value={state.boSung}
            onChange={(e) => {
              customSearch("boSung", e, onSearch);
            }}
            placeholder={t("kho.chonBoSung")}
          />
        ),
        selectSearch: true,

        ...rest,
      }),
    LOAI_HINH_THANH_TOAN: ({ onSearch, onClickSort, dataSortColumn }, rest) =>
      Column({
        title: t("quanLyNoiTru.dvNoiTru.loaiHinhThanhToan"),
        width: 140,
        dataIndex: "tenLoaiHinhThanhToan",
        key: "tenLoaiHinhThanhToan",
        i18Name: "quanLyNoiTru.dvNoiTru.loaiHinhThanhToan",
        sort_key: "tenLoaiHinhThanhToan",
        dataSort: dataSortColumn["tenLoaiHinhThanhToan"] || "",
        onClickSort: onClickSort,
        selectSearch: true,
        renderSearch: (
          <Select
            data={(listLoaiHinhThanhToanCuaDoiTuong || []).map((item) => ({
              id: item.loaiHinhThanhToanId,
              ten: item.tenLoaiHinhThanhToan,
            }))}
            placeholder={t("baoCao.chonLoaiHinhThanhToan")}
            onChange={(e) => {
              onSearch("dsLoaiHinhThanhToanId", e);
            }}
          />
        ),
        ...rest,
      }),
    TEN_PHONG_THUC_HIEN: ({ onSearch, onClickSort, dataSortColumn }, rest) =>
      Column({
        title: t("quanLyNoiTru.dvNoiTru.tenPhongThucHien"),
        width: 140,
        dataIndex: "tenPhongThucHien",
        key: "tenPhongThucHien",
        i18Name: "quanLyNoiTru.dvNoiTru.tenPhongThucHien",
        sort_key: "tenPhongThucHien",
        dataSort: dataSortColumn["tenPhongThucHien"] || "",
        onClickSort: onClickSort,
        selectSearch: true,
        renderSearch: (
          <Select
            data={listAllPhong}
            mode="multiple"
            placeholder={t("baoCao.chonPhongThucHien")}
            onChange={(e) => {
              onSearch("dsPhongThucHienId", e);
            }}
          />
        ),
        ...rest,
      }),
    KET_LUAN: ({ onClickSort, dataSortColumn }) =>
      Column({
        title: t("quanLyNoiTru.dvNoiTru.ketLuan"),
        width: 600,
        dataIndex: "ketLuan",
        key: "ketLuan",
        i18Name: "quanLyNoiTru.dvNoiTru.ketLuan",
        align: "justify",
        render: (value) => (
          <div
            className="ketQua-ketLuan"
            dangerouslySetInnerHTML={{
              __html: DOMPurify.sanitize(value),
            }}
          />
        ),
      }),
    THANH_TIEN: ({ onClickSort, dataSortColumn }) =>
      Column({
        title: t("common.thanhTien"),
        width: 100,
        dataIndex: "thanhTien",
        key: "thanhTien",
        align: "right",
        i18Name: "common.thanhTien",
        sort_key: "thanhTien",
        dataSort: dataSortColumn["thanhTien"] || "",
        onClickSort: onClickSort,
        render: (value) => (value || 0).formatPrice(),
      }),
    KET_QUA: ({ onClickSort, dataSortColumn }, rest) =>
      Column({
        title: t("quanLyNoiTru.dvNoiTru.ketQua"),
        width: 600,
        dataIndex: "ketQua",
        key: "ketQua",
        i18Name: "quanLyNoiTru.dvNoiTru.ketQua",
        align: "justify",
        render: (item, record, index) =>
          record.loaiDichVu === LOAI_DICH_VU.XET_NGHIEM ? (
            <KetQuaXetNghiem
              data={{ value: item, row: record, index }}
              isNbThieuTien={false}
              showKetLuan={false}
              isXetNghiem={true}
            />
          ) : (
            <div
              className="ketQua-ketLuan"
              dangerouslySetInnerHTML={{
                __html: DOMPurify.sanitize(item),
              }}
            />
          ),
        ...rest,
      }),
    KHOA_CHI_DINH: ({ onSearch, onClickSort, dataSortColumn, value }, rest) =>
      Column({
        title: t("quanLyNoiTru.dvNoiTru.tenKhoaChiDinh"),
        width: 200,
        dataIndex: "tenKhoaChiDinh",
        key: "tenKhoaChiDinh",
        i18Name: "quanLyNoiTru.dvNoiTru.tenKhoaChiDinh",
        sort_key: "tenKhoaChiDinh",
        dataSort: dataSortColumn["tenKhoaChiDinh"] || "",
        onClickSort: onClickSort,
        selectSearch: true,
        renderSearch: (
          <Select
            data={listAllKhoa}
            placeholder={t("baoCao.chonKhoaChiDinh")}
            value={value}
            onChange={(e) => {
              onSearch("khoaChiDinhId", e);
            }}
          />
        ),
        ...rest,
      }),
    TEN_KHOA_CHI_DINH: ({
      onSearch,
      onClickSort,
      dataSortColumn,
      value,
      isDislaySearch,
    }) =>
      Column({
        title: t("quanLyNoiTru.dvNoiTru.tenKhoaChiDinh"),
        sort_key: "tenKhoaChiDinh",
        width: 200,
        dataIndex: "tenKhoaChiDinh",
        key: "tenKhoaChiDinh",
        i18Name: "quanLyNoiTru.dvNoiTru.tenKhoaChiDinh",
        dataSort: dataSortColumn["tenKhoaChiDinh"] || "",
        onClickSort: onClickSort,
        selectSearch: true,
        ...(isDislaySearch
          ? {
              renderSearch: (
                <Select
                  data={listAllKhoa}
                  mode="multiple"
                  placeholder={t("baoCao.chonKhoaChiDinh")}
                  value={value}
                  onChange={(e) => {
                    onSearch("dsKhoaChiDinhId", e);
                  }}
                />
              ),
            }
          : {}),
      }),
    TEN_KHOA_THUC_HIEN: ({ onSearch, onClickSort, dataSortColumn }) =>
      Column({
        title: t("quanLyNoiTru.dvNoiTru.tenKhoaThucHien"),
        sort_key: "tenKhoaThucHien",
        width: 250,
        dataIndex: "tenKhoaThucHien",
        key: "tenKhoaThucHien",
        i18Name: "quanLyNoiTru.dvNoiTru.tenKhoaThucHien",
        dataSort: dataSortColumn["tenKhoaThucHien"] || "",
        onClickSort: onClickSort,
        selectSearch: true,
        renderSearch: (
          <Select
            data={listAllKhoa}
            mode="multiple"
            placeholder={t("baoCao.chonKhoaThucHien")}
            onChange={(e) => {
              onSearch("dsKhoaThucHienId", e);
            }}
          />
        ),
      }),
    BAC_SY_CHI_DINH: ({ onSearch, onClickSort, dataSortColumn }) =>
      Column({
        title: t("hsba.bsChiDinh"),
        width: 160,
        dataIndex: "tenBacSiChiDinh",
        key: "tenBacSiChiDinh",
        i18Name: "hsba.bsChiDinh",
        sort_key: "tenBacSiChiDinh",
        dataSort: dataSortColumn["tenBacSiChiDinh"] || "",
        onClickSort: onClickSort,
        selectSearch: true,
        renderSearch: (
          <Select
            data={listAllNhanVien}
            mode="multiple"
            placeholder={t("hsba.chonBsChiDinh")}
            onChange={(e) => {
              onSearch("dsBacSiChiDinhId", e);
            }}
            dropdownMatchSelectWidth={300}
          />
        ),
      }),
    TT35: ({ onSearch, onClickSort, dataSortColumn }, rest) =>
      Column({
        title: t("quanLyNoiTru.dvNoiTru.tt35"),
        sort_key: "tenMucDich",
        width: 160,
        dataIndex: "tenMucDich",
        key: "tenMucDich",
        i18Name: "quanLyNoiTru.dvNoiTru.tt35",
        dataSort: dataSortColumn["tenMucDich"] || "",
        onClickSort: onClickSort,
        renderSearch: (
          <InputTimeout
            placeholder={t("quanLyNoiTru.dvNoiTru.nhapTT35")}
            onChange={(e) => {
              onSearch("tenMucDich", e);
            }}
          />
        ),
        ...rest,
      }),
    TEN_THUOC: ({ onSearch, onClickSort, dataSortColumn, ...rest }) =>
      Column({
        title: t("hsba.tenThuoc"),
        width: 250,
        dataIndex: "tenDichVu",
        key: "tenDichVu",
        i18Name: "hsba.tenThuoc",
        sort_key: "tenDichVu",
        dataSort: dataSortColumn["tenDichVu"] || "",
        onClickSort: onClickSort,
        renderSearch: (
          <InputTimeout
            placeholder={t("cdha.nhapTenThuoc")}
            onChange={(e) => {
              onSearch("tenDichVu", e);
            }}
          />
        ),
        ...rest,
      }),
    TEN_CHE_PHAM_DINH_DUONG: ({ onSearch, onClickSort, dataSortColumn }) =>
      Column({
        title: t("quanLyNoiTru.dvNoiTru.tenChePhamDD"),
        width: 250,
        dataIndex: "tenDichVu",
        key: "tenDichVu",
        i18Name: "quanLyNoiTru.dvNoiTru.tenChePhamDD",
        sort_key: "tenDichVu",
        dataSort: dataSortColumn["tenDichVu"] || "",
        onClickSort: onClickSort,
        renderSearch: (
          <InputTimeout
            placeholder={t("quanLyNoiTru.dvNoiTru.nhapTenChePhamDD")}
            onChange={(e) => {
              onSearch("tenDichVu", e);
            }}
          />
        ),
      }),
    MA_CHE_PHAM_DINH_DUONG: ({ onSearch, onClickSort, dataSortColumn }) =>
      Column({
        title: t("quanLyNoiTru.dvNoiTru.maChePhamDD"),
        width: 100,
        dataIndex: "maDichVu",
        key: "maDichVu",
        align: "center",
        i18Name: "quanLyNoiTru.dvNoiTru.maChePhamDD",
        sort_key: "maDichVu",
        dataSort: dataSortColumn["maDichVu"] || "",
        onClickSort: onClickSort,
        renderSearch: (
          <InputTimeout
            placeholder={t("quanLyNoiTru.dvNoiTru.nhapMaChePhamDD")}
            onChange={(e) => {
              onSearch("maDichVu", e);
            }}
          />
        ),
      }),
    DON_VI_TINH: ({ onSearch, onClickSort, dataSortColumn }) =>
      Column({
        title: t("quanLyNoiTru.dvNoiTru.dvt"),
        width: 250,
        dataIndex: "tenDonViTinh",
        key: "tenDonViTinh",
        i18Name: "quanLyNoiTru.dvNoiTru.dvt",
        sort_key: "tenDonViTinh",
        dataSort: dataSortColumn["tenDonViTinh"] || "",
        onClickSort: onClickSort,
        renderSearch: (
          <InputTimeout
            placeholder={t("quanLyNoiTru.dvNoiTru.nhapDonViTinh")}
            onChange={(e) => {
              onSearch("tenDonViTinh", e);
            }}
          />
        ),
      }),
    LUU_Y: ({ onClickSort, dataSortColumn }) =>
      Column({
        title: t("quanLyNoiTru.dvNoiTru.luuY"),
        width: 100,
        dataIndex: "ghiChu",
        key: "ghiChu",
        i18Name: "quanLyNoiTru.dvNoiTru.luuY",
        sort_key: "ghiChu",
        dataSort: dataSortColumn["ghiChu"] || "",
        onClickSort: onClickSort,
      }),
    NGUOI_CHI_DINH: ({ onSearch, onClickSort, dataSortColumn }) =>
      Column({
        title: t("quanLyNoiTru.dvNoiTru.nguoiChiDinh"),
        width: 250,
        dataIndex: "tenBacSiChiDinh",
        key: "tenBacSiChiDinh",
        i18Name: "quanLyNoiTru.dvNoiTru.nguoiChiDinh",
        sort_key: "tenBacSiChiDinh",
        dataSort: dataSortColumn["tenBacSiChiDinh"] || "",
        onClickSort: onClickSort,
        selectSearch: true,
        renderSearch: (
          <Select
            data={listAllNhanVien}
            mode="multiple"
            placeholder={t("quanLyNoiTru.dvNoiTru.chonNguoiChiDinh")}
            onChange={(e) => {
              onSearch("dsBacSiChiDinhId", e);
            }}
          />
        ),
      }),
    CHE_PHAM_MAU: ({ onSearch, onClickSort, dataSortColumn }) =>
      Column({
        title: t("quanLyNoiTru.dvNoiTru.chePhamMau"),
        width: 250,
        dataIndex: "tenDichVu",
        key: "tenDichVu",
        i18Name: "quanLyNoiTru.dvNoiTru.chePhamMau",
        sort_key: "tenDichVu",
        dataSort: dataSortColumn["tenDichVu"] || "",
        onClickSort: onClickSort,
        renderSearch: (
          <InputTimeout
            placeholder={t("quanLyNoiTru.dvNoiTru.chePhamMau")}
            onChange={(e) => {
              onSearch("tenDichVu", e);
            }}
          />
        ),
      }),
    TEN_HOA_CHAT: ({ onSearch, onClickSort, dataSortColumn }) =>
      Column({
        title: t("quanLyNoiTru.dvNoiTru.tenHoaChat"),
        width: 250,
        dataIndex: "tenDichVu",
        key: "tenDichVu",
        i18Name: "common.tenDichVu",
        sort_key: "tenDichVu",
        dataSort: dataSortColumn["tenDichVu"] || "",
        onClickSort: onClickSort,
        renderSearch: (
          <InputTimeout
            placeholder={t("quanLyNoiTru.dvNoiTru.tenHoaChat")}
            onChange={(e) => {
              onSearch("tenDichVu", e);
            }}
          />
        ),
      }),
    TEN_VAT_TU: ({ onSearch, onClickSort, dataSortColumn, ...rest }) =>
      Column({
        title: t("quanLyNoiTru.dvNoiTru.tenVatTu"),
        width: 250,
        dataIndex: "tenDichVu",
        key: "tenDichVu",
        i18Name: "quanLyNoiTru.dvNoiTru.tenVatTu",
        sort_key: "tenDichVu",
        dataSort: dataSortColumn["tenDichVu"] || "",
        onClickSort: onClickSort,
        renderSearch: (
          <InputTimeout
            placeholder={t("vatTu.nhapTenVatTu")}
            onChange={(e) => {
              onSearch("tenDichVu", e);
            }}
          />
        ),
        ...rest,
      }),
    TEN_GIUONG: ({ onSearch, onClickSort, dataSortColumn }) =>
      Column({
        title: t("quanLyNoiTru.dvNoiTru.tenDVGiuong"),
        width: 250,
        dataIndex: "tenDichVu",
        key: "tenDichVu",
        i18Name: "quanLyNoiTru.dvNoiTru.tenDVGiuong",
        sort_key: "tenDichVu",
        dataSort: dataSortColumn["tenDichVu"] || "",
        onClickSort: onClickSort,
        renderSearch: (
          <InputTimeout
            placeholder={t("quanLyNoiTru.dvNoiTru.nhapTenDVGiuong")}
            onChange={(e) => {
              onSearch("tenDichVu", e);
            }}
          />
        ),
      }),
    TRANG_THAI_MAU: ({ onSearch, onClickSort, dataSortColumn }) =>
      Column({
        title: t("common.trangThai"),
        width: 160,
        dataIndex: "trangThai",
        key: "trangThai",
        align: "center",
        i18Name: "common.trangThai",
        sort_key: "trangThai",
        dataSort: dataSortColumn["trangThai"] || "",
        onClickSort: onClickSort,
        render: (item) => listTrangThaiMau.find((x) => x.id == item)?.ten || "",
        selectSearch: true,
        renderSearch: (
          <Select
            data={listTrangThaiMau}
            placeholder={t("common.chonTrangThai")}
            onChange={(e) => {
              onSearch("trangThai", e);
            }}
          />
        ),
      }),
    NHOM_MAU_KE: ({ onClickSort, dataSortColumn }, rest) =>
      Column({
        title: t("quanLyNoiTru.dvNoiTru.nhomMauNb"),
        width: 120,
        dataIndex: "nhomMauNb",
        key: "nhomMauNb",
        align: "center",
        i18Name: "quanLyNoiTru.dvNoiTru.nhomMauNb",
        sort_key: "nhomMauNb",
        dataSort: dataSortColumn["nhomMauNb"] || "",
        onClickSort: onClickSort,
        render: (item) => listNhomMau.find((x) => x.id == item)?.ten || "",
        ...rest,
      }),
    NHOM_MAU_PHAT: ({ onClickSort, dataSortColumn }) =>
      Column({
        title: t("quanLyNoiTru.dvNoiTru.nhomMauPhat"),
        width: 120,
        dataIndex: "nhomMau",
        key: "nhomMau",
        align: "center",
        i18Name: "quanLyNoiTru.dvNoiTru.nhomMauPhat",
        sort_key: "nhomMau",
        dataSort: dataSortColumn["nhomMau"] || "",
        onClickSort: onClickSort,
        render: (item) => listNhomMau.find((x) => x.id == item)?.ten || "",
      }),
    MA_TUI_MAU: ({ onSearch, onClickSort, dataSortColumn }) =>
      Column({
        title: t("quanLyNoiTru.mau.maTuiMau"),
        width: 100,
        dataIndex: "maTuiMau",
        key: "maTuiMau",
        align: "center",
        i18Name: "quanLyNoiTru.mau.maTuiMau",
        sort_key: "maTuiMau",
        dataSort: dataSortColumn["maTuiMau"] || "",
        onClickSort: onClickSort,
        renderSearch: (
          <InputTimeout
            placeholder={t("danhMuc.nhapMa")}
            onChange={(e) => {
              onSearch("maTuiMau", e);
            }}
          />
        ),
      }),
    CACH_DUNG: ({ onSearch, onClickSort, dataSortColumn }) =>
      Column({
        title: t("quanLyNoiTru.toDieuTri.cachDung"),
        width: 100,
        dataIndex: "cachDung",
        key: "cachDung",
        align: "center",
        i18Name: "quanLyNoiTru.toDieuTri.cachDung",
        sort_key: "cachDung",
        dataSort: dataSortColumn["cachDung"] || "",
        onClickSort: onClickSort,
        renderSearch: (
          <InputTimeout
            placeholder={t("quanLyNoiTru.toDieuTri.nhapCachDung")}
            onChange={(e) => {
              onSearch("cachDung", e);
            }}
          />
        ),
      }),

    THOI_GIAN_NAM: ({ onSearch, onClickSort, dataSortColumn }) =>
      Column({
        title: t("quanLyNoiTru.dvNoiTru.thoiGianNam"),
        sort_key: "thoiGianThucHien",
        width: 180,
        dataIndex: "thoiGianThucHien",
        key: "thoiGianThucHien",
        i18Name: "quanLyNoiTru.dvNoiTru.thoiGianNam",
        dataSort: dataSortColumn["thoiGianThucHien"] || "",
        onClickSort: onClickSort,
        render: (item) => item && moment(item).format("DD/MM/YYYY HH:mm:ss"),
        selectSearch: true,
        renderSearch: (
          <DatePicker
            format="DD/MM/YYYY"
            placeholder={t("quanLyNoiTru.dvNoiTru.nhapThoiGian")}
            onChange={(e) => {
              onSearch("ThoiGianThucHien", e);
            }}
          />
        ),
      }),
    THOI_GIAN_PHAT_MAU: ({ onSearch, onClickSort, dataSortColumn }) =>
      Column({
        title: t("quanLyNoiTru.dvNoiTru.thoiGianPhatMau"),
        width: 160,
        dataIndex: "thoiGianPhat",
        key: "thoiGianPhat",
        i18Name: "quanLyNoiTru.dvNoiTru.thoiGianPhatMau",
        sort_key: "thoiGianPhat",
        dataSort: dataSortColumn["thoiGianPhat"] || "",
        onClickSort: onClickSort,
        render: (item) => item && moment(item).format("DD/MM/YYYY HH:mm:ss"),
        selectSearch: true,
        renderSearch: (
          <DatePicker
            format="DD/MM/YYYY"
            placeholder={t("quanLyNoiTru.dvNoiTru.nhapThoiGian")}
            onChange={(e) => {
              onSearch("thoiGianPhat", e);
            }}
          />
        ),
      }),
    THOI_GIAN_DUNG: ({ onClickSort, dataSortColumn }) =>
      Column({
        title: t("quanLyNoiTru.dvNoiTru.thoiGianDung"),
        width: 160,
        dataIndex: "thoiGianPhat",
        key: "thoiGianPhat",
        i18Name: "quanLyNoiTru.dvNoiTru.thoiGianDung",
        sort_key: "thoiGianPhat",
        dataSort: dataSortColumn["thoiGianPhat"] || "",
        onClickSort: onClickSort,
        render: (item) => item && moment(item).format("DD/MM/YYYY HH:mm:ss"),
      }),
    DUONG_DUNG: ({ onClickSort, dataSortColumn }, rest) =>
      Column({
        title: t("quanLyNoiTru.dvNoiTru.duongDung"),
        width: 160,
        dataIndex: "tenDuongDung",
        key: "tenDuongDung",
        i18Name: "quanLyNoiTru.dvNoiTru.duongDung",
        sort_key: "tenDuongDung",
        dataSort: dataSortColumn["tenDuongDung"] || "",
        onClickSort: onClickSort,
        ...rest,
      }),
    NGAY_SAN_XUAT: ({ onClickSort, dataSortColumn }) =>
      Column({
        title: t("quanLyNoiTru.dvNoiTru.ngaySanXuat"),
        width: 180,
        dataIndex: "ngaySanXuat",
        key: "ngaySanXuat",
        i18Name: "quanLyNoiTru.dvNoiTru.ngaySanXuat",
        sort_key: "ngaySanXuat",
        dataSort: dataSortColumn["ngaySanXuat"] || "",
        onClickSort: onClickSort,
        render: (item) => item && moment(item).format("DD/MM/YYYY HH:mm:ss"),
      }),
    HAN_SU_DUNG: ({ onClickSort, dataSortColumn }) =>
      Column({
        title: t("quanLyNoiTru.dvNoiTru.hanSuDung"),
        width: 180,
        dataIndex: "ngayHanSuDung",
        key: "ngayHanSuDung",
        i18Name: "quanLyNoiTru.dvNoiTru.hanSuDung",
        sort_key: "ngayHanSuDung",
        dataSort: dataSortColumn["ngayHanSuDung"] || "",
        onClickSort: onClickSort,
        render: (item) => item && moment(item).format("DD/MM/YYYY HH:mm:ss"),
      }),

    DA_PHAT: ({ onSearch, onClickSort, dataSortColumn }) =>
      Column({
        title: t("common.daPhat"),
        width: 100,
        dataIndex: "phat",
        key: "phat",
        align: "center",
        i18Name: "common.daPhat",
        sort_key: "phat",
        dataSort: dataSortColumn["phat"] || "",
        onClickSort: onClickSort,
        render: (item, record) => (
          <Checkbox
            checked={record?.trangThai >= TRANG_THAI_THUOC.DA_PHAT.id}
          />
        ),
        selectSearch: true,
        renderSearch: (
          <Select
            data={HIEU_LUC}
            defaultValue=""
            onChange={(e) => {
              onSearch("phat", e);
            }}
            hasAllOption={true}
            dropdownMatchSelectWidth={120}
          />
        ),
      }),
    DA_PHAT_VAT_TU: ({ onSearch, onClickSort, dataSortColumn }) =>
      Column({
        title: t("common.daPhat"),
        width: 100,
        dataIndex: "phat",
        key: "phat",
        align: "center",
        i18Name: "common.daPhat",
        sort_key: "phat",
        dataSort: dataSortColumn["phat"] || "",
        onClickSort: onClickSort,
        render: (item, record) => <Checkbox checked={item} />,
        selectSearch: true,
        renderSearch: (
          <Select
            data={HIEU_LUC}
            defaultValue=""
            onChange={(e) => {
              onSearch("phat", e);
            }}
            hasAllOption={true}
          />
        ),
      }),

    TRANG_THAI_THANH_TOAN: ({ onSearch, onClickSort, dataSortColumn }) =>
      Column({
        title: t("quanLyNoiTru.toDieuTri.trangThaiThanhToan"),
        width: 130,
        dataIndex: "trangThaiThanhToan",
        key: "trangThaiThanhToan",
        align: "center",
        i18Name: "quanLyNoiTru.toDieuTri.trangThaiThanhToan",
        sort_key: "trangThaiThanhToan",
        dataSort: dataSortColumn["trangThaiThanhToan"] || "",
        onClickSort: onClickSort,
        render: (item, record) => <Checkbox checked={item} />,
        selectSearch: true,
        renderSearch: (
          <Select
            data={HIEU_LUC}
            defaultValue=""
            onChange={(e) => {
              onSearch("trangThaiThanhToan", e);
            }}
            hasAllOption={true}
          />
        ),
      }),
    TEN_PHONG: ({ onSearch, onClickSort, dataSortColumn }) =>
      Column({
        title: t("common.phong"),
        width: 160,
        dataIndex: "tenPhong",
        key: "tenPhong",
        align: "center",
        i18Name: "common.phong",
        sort_key: "tenPhong",
        dataSort: dataSortColumn["tenPhong"] || "",
        onClickSort: onClickSort,
        selectSearch: true,
        renderSearch: (
          <Select
            data={listAllPhong}
            placeholder={t("common.chonPhong")}
            onChange={(e) => {
              onSearch("phongId", e);
            }}
          />
        ),
      }),

    TT20: ({ onSearch, onClickSort, dataSortColumn }) =>
      Column({
        title: t("common.tt20"),
        sort_key: "tenMucDich",
        width: 160,
        dataIndex: "tenMucDich",
        key: "tenMucDich",
        i18Name: "common.tt20",
        dataSort: dataSortColumn["tenMucDich"] || "",
        onClickSort: onClickSort,
        renderSearch: (
          <InputTimeout
            placeholder={t("quanLyNoiTru.dvNoiTru.nhapTT20")}
            onChange={(e) => {
              onSearch("tenMucDich", e);
            }}
          />
        ),
      }),

    TEN_KHO: ({ onSearch, onClickSort, dataSortColumn }) =>
      Column({
        title: t("quanLyNoiTru.dvNoiTru.kho"),
        sort_key: "tenKho",
        width: 160,
        dataIndex: "tenKho",
        key: "tenKho",
        i18Name: "quanLyNoiTru.dvNoiTru.kho",
        dataSort: dataSortColumn["tenKho"] || "",
        onClickSort: onClickSort,
        selectSearch: true,
        renderSearch: (
          <Select
            data={listAllKho}
            mode="multiple"
            placeholder={t("baoCao.chonKho")}
            onChange={(e) => {
              onSearch("dsKhoId", e);
            }}
            dropdownMatchSelectWidth={220}
          />
        ),
      }),
    SO_PHIEU_LINH: ({ onSearch, onClickSort, dataSortColumn }) =>
      Column({
        title: t("quanLyNoiTru.dvNoiTru.soPhieuLinh"),
        sort_key: "soPhieuLinh",
        width: 160,
        dataIndex: "soPhieuLinh",
        key: "soPhieuLinh",
        align: "center",
        i18Name: "quanLyNoiTru.dvNoiTru.soPhieuLinh",
        dataSort: dataSortColumn["soPhieuLinh"] || "",
        onClickSort: onClickSort,
        renderSearch: (
          <InputTimeout
            placeholder={t("quanLyNoiTru.dvNoiTru.nhapSoPhieuLinh")}
            onChange={(e) => {
              onSearch("soPhieuLinh", e);
            }}
          />
        ),
      }),
    GHI_CHU: ({ onClickSort, dataSortColumn }) =>
      Column({
        title: t("quanLyNoiTru.dvNoiTru.ghiChu"),
        width: 100,
        dataIndex: "ghiChu",
        key: "ghiChu",
        i18Name: "quanLyNoiTru.dvNoiTru.ghiChu",
        sort_key: "ghiChu",
        dataSort: dataSortColumn["ghiChu"] || "",
        onClickSort: onClickSort,
      }),
    SO_HIEU_GIUONG: ({ onSearch, onClickSort, dataSortColumn }) =>
      Column({
        title: t("quanLyNoiTru.phongGiuong.soHieuGiuong"),
        width: 120,
        dataIndex: "soHieu",
        key: "soHieu",
        align: "center",
        i18Name: "quanLyNoiTru.phongGiuong.soHieuGiuong",
        sort_key: "soHieu",
        dataSort: dataSortColumn["soHieu"] || "",
        onClickSort: onClickSort,
        renderSearch: (
          <InputTimeout
            placeholder={t("quanLyNoiTru.phongGiuong.nhapSoHieuGiuong")}
            onChange={(e) => {
              onSearch("soHieu", e);
            }}
          />
        ),
      }),
    SO_LUONG: ({ onClickSort, dataSortColumn }, rest) =>
      Column({
        title: t("quanLyNoiTru.dvNoiTru.soLuong"),
        width: 50,
        dataIndex: "soLuong",
        key: "soLuong",
        align: "center",
        i18Name: "quanLyNoiTru.dvNoiTru.soLuong",
        sort_key: "soLuong",
        dataSort: dataSortColumn["soLuong"] || "",
        onClickSort: onClickSort,
        ...rest,
      }),
    SO_LUONG_KE: ({ onClickSort, dataSortColumn }, rest) =>
      Column({
        title: t("quanLyNoiTru.dvNoiTru.soLuongKe"),
        width: 80,
        dataIndex: "soLuong",
        key: "soLuong",
        align: "right",
        i18Name: "quanLyNoiTru.dvNoiTru.soLuongKe",
        sort_key: "soLuong",
        dataSort: dataSortColumn["soLuong"] || "",
        onClickSort: onClickSort,
        ...rest,
      }),
    SO_LUONG_YEU_CAU: ({ onClickSort, dataSortColumn }, rest) =>
      Column({
        title: t("quanLyNoiTru.dvNoiTru.soLuongKe"),
        width: 80,
        dataIndex: "soLuongYeuCau",
        key: "soLuongYeuCau",
        align: "right",
        i18Name: "quanLyNoiTru.dvNoiTru.soLuongKe",
        sort_key: "soLuongYeuCau",
        dataSort: dataSortColumn["soLuongYeuCau"] || "",
        onClickSort: onClickSort,
        ...rest,
      }),
    DON_GIA_BH: ({ onClickSort, dataSortColumn }, rest) =>
      Column({
        title: t("danhMuc.donGiaBh"),
        width: 100,
        dataIndex: "giaBaoHiem",
        key: "giaBaoHiem",
        align: "right",
        i18Name: "danhMuc.donGiaBh",
        sort_key: "giaBaoHiem",
        dataSort: dataSortColumn["giaBaoHiem"] || "",
        onClickSort: onClickSort,
        ...rest,
        render: (value) => (value || 0).formatPrice(),
      }),
    DON_GIA_KHONG_BH: ({ onClickSort, dataSortColumn }, rest) =>
      Column({
        title: t("danhMuc.donGiaKhongBh"),
        width: 130,
        dataIndex: "giaKhongBaoHiem",
        key: "giaKhongBaoHiem",
        align: "right",
        i18Name: "danhMuc.donGiaKhongBh",
        sort_key: "giaKhongBaoHiem",
        dataSort: dataSortColumn["giaKhongBaoHiem"] || "",
        onClickSort: onClickSort,
        ...rest,
        render: (value) => (value || 0).formatPrice(),
      }),
    NHA_THUOC: ({ onSearch, onClickSort, dataSortColumn }) =>
      Column({
        title: t("nhaThuoc.nhaThuoc"),
        width: 100,
        dataIndex: "nhaThuoc",
        key: "nhaThuoc",
        align: "center",
        i18Name: "nhaThuoc.nhaThuoc",
        sort_key: "nhaThuoc",
        dataSort: dataSortColumn["nhaThuoc"] || "",
        onClickSort: onClickSort,
        render: (item) => <Checkbox checked={item} />,
        selectSearch: true,
        renderSearch: (
          <Select
            data={YES_NO}
            defaultValue=""
            onChange={(e) => {
              onSearch("nhaThuoc", e);
            }}
            hasAllOption={true}
          />
        ),
      }),
    CHE_PHAM_MAU_DVKT: ({ onSearch, onClickSort, dataSortColumn }) =>
      Column({
        title: t("quanLyNoiTru.dvNoiTru.chePhamMau"),
        width: 200,
        dataIndex: "tenChePhamMau",
        key: "tenChePhamMau",
        i18Name: "quanLyNoiTru.dvNoiTru.chePhamMau",
        sort_key: "tenChePhamMau",
        dataSort: dataSortColumn["tenChePhamMau"] || "",
        onClickSort: onClickSort,
        renderSearch: (
          <InputTimeout
            placeholder={t("quanLyNoiTru.dvNoiTru.chePhamMau")}
            onChange={(e) => {
              onSearch("tenChePhamMau", e);
            }}
          />
        ),
      }),
    TRANG_THAI_MAU_DVKT: ({ onSearch, onClickSort, dataSortColumn }) =>
      Column({
        title: t("quanLyNoiTru.dvNoiTru.trangThaiMau"),
        width: 120,
        dataIndex: "trangThaiMau",
        key: "trangThaiMau",
        i18Name: "quanLyNoiTru.dvNoiTru.trangThaiMau",
        sort_key: "trangThaiMau",
        dataSort: dataSortColumn["trangThaiMau"] || "",
        onClickSort: onClickSort,
        render: (item) => listTrangThaiMau.find((x) => x.id == item)?.ten || "",
        selectSearch: true,
        renderSearch: (
          <Select
            data={listTrangThaiMau}
            placeholder={t("common.chonTrangThai")}
            onChange={(e) => {
              onSearch("trangThaiMau", e);
            }}
            dropdownMatchSelectWidth={150}
          />
        ),
      }),
    LOAI_THUOC: ({ onSearch, onClickSort, dataSortColumn }) =>
      Column({
        title: t("kho.loaiThuoc"),
        width: 150,
        dataIndex: "loai",
        key: "loai",
        align: "left",
        i18Name: "kho.loaiThuoc",
        sort_key: "loai",
        dataSort: dataSortColumn["loai"] || "",
        onClickSort: onClickSort,
        render: (item) => listloaiThuoc.find((x) => x.id === item)?.ten,
        selectSearch: true,
        renderSearch: (
          <Select
            data={listloaiThuoc}
            placeholder={t("pttt.chonLoaiThuoc")}
            onChange={(e) => {
              onSearch("loai", e);
            }}
          />
        ),
      }),
    TEN_KHO_TAI_KHOA: ({ onClickSort, dataSortColumn }, rest) =>
      Column({
        title: t("kho.khoTaiKhoa"),
        width: 180,
        dataIndex: "tenKhoTaiKhoa",
        key: "tenKhoTaiKhoa",
        align: "left",
        i18Name: "kho.khoTaiKhoa",
        sort_key: "tenKhoTaiKhoa",
        dataSort: dataSortColumn["tenKhoTaiKhoa"] || "",
        onClickSort: onClickSort,
        ...rest,
      }),
    NGUON_KHAC: ({ onClickSort, dataSortColumn }, rest) =>
      Column({
        title: t("danhMuc.nguonKhac"),
        width: 180,
        dataIndex: "nguonKhacId",
        key: "nguonKhacId",
        align: "left",
        i18Name: "danhMuc.nguonKhac",
        sort_key: "nguonKhacId",
        dataSort: dataSortColumn["nguonKhacId"] || "",
        onClickSort: onClickSort,
        ...rest,
      }),
    TRANG_THAI_THUOC: ({ onSearch, onClickSort, dataSortColumn }) =>
      Column({
        title: t("kho.trangThaiThuoc"),
        width: 180,
        dataIndex: "trangThai",
        key: "trangThai",
        align: "left",
        i18Name: "kho.trangThaiThuoc",
        sort_key: "trangThai",
        dataSort: dataSortColumn["trangThai"] || "",
        onClickSort: onClickSort,
        render: (item) =>
          listTrangThaiThuoc.find((x) => x.id == item)?.ten || "",
        selectSearch: true,
        renderSearch: (
          <Select
            data={listTrangThaiThuoc}
            placeholder={t("common.chonTrangThai")}
            onChange={(e) => {
              onSearch("trangThai", e);
            }}
          />
        ),
      }),
    CANH_BAO: ({ onClickSort, dataSortColumn, onSearch }) =>
      Column({
        title: t("quanLyNoiTru.canhBaoBatThuong"),
        width: 150,
        dataIndex: "canhBao",
        key: "canhBao",
        align: "left",
        i18Name: "quanLyNoiTru.canhBaoBatThuong",
        sort_key: "canhBao",
        dataSort: dataSortColumn["canhBao"] || "",
        onClickSort: onClickSort,
        selectSearch: true,
        renderSearch: (
          <Select
            data={[
              { id: true, ten: t("quanLyNoiTru.coBatThuong") },
              { id: false, ten: t("quanLyNoiTru.khongBatThuong") },
            ]}
            placeholder={t("common.tuyChon")}
            hasAllOption={true}
            onChange={(e) => {
              onSearch("coCanhBao", e);
            }}
          />
        ),
        render: (value) => {
          return (
            <div
              dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(value) }}
            />
          );
        },
      }),
    TRANG_THAI_HOAN: ({ onClickSort, dataSortColumn, onSearch }) =>
      Column({
        title: t("thuNgan.trangThaiHoan"),
        width: 130,
        dataIndex: "trangThaiHoan",
        key: "trangThaiHoan",
        i18Name: "thuNgan.trangThaiHoan",
        sort_key: "trangThaiHoan",
        dataSort: dataSortColumn["trangThaiHoan"] || "",
        onClickSort: onClickSort,
        selectSearch: true,
        renderSearch: (
          <Select
            hasAllOption={true}
            data={listtrangThaiHoan}
            placeholder={t("common.chonTrangThai")}
            onChange={(e) => {
              onSearch("dsTrangThaiHoan", e);
            }}
            defaultValue={0}
          />
        ),
        render: (item, list, index) => {
          return listtrangThaiHoan?.find((e) => e.id === item)?.ten;
        },
      }),
    PHAN_LOAI_PTTT: ({ onSearch, onClickSort, dataSortColumn }) =>
      Column({
        title: t("danhMuc.phanLoaiPttt"),
        width: 150,
        dataIndex: "phanLoaiPtTt",
        key: "phanLoaiPtTt",
        i18Name: "danhMuc.phanLoaiPttt",
        sort_key: "phanLoaiPtTt",
        dataSort: dataSortColumn["phanLoaiPtTt"] || "",
        onClickSort: onClickSort,
        selectSearch: true,
        renderSearch: (
          <Select
            data={listPhanLoaiPTTT || []}
            placeholder={t("common.timKiem")}
            onChange={(e) => onSearch("phanLoaiPtTt", e)}
          />
        ),
        render: (item) =>
          (listPhanLoaiPTTT || []).find((x) => x.id == item)?.ten,
      }),
    BENH_PHAM: ({ onSearch, onClickSort, dataSortColumn }) =>
      Column({
        title: t("xetNghiem.benhPham"),
        width: 150,
        dataIndex: "tenBenhPham",
        key: "tenBenhPham",
        i18Name: "danhMuc.benhPham",
        sort_key: "tenBenhPham",
        dataSort: dataSortColumn["tenBenhPham"] || "",
        onClickSort: onClickSort,
        selectSearch: true,
        renderSearch: (
          <Select
            data={listAllBenhPham || []}
            placeholder={t("common.timKiem")}
            onChange={(e) => onSearch("dsBenhPhamId", e)}
          />
        ),
      }),
    MA_DICH_VU: ({ onSearch, onClickSort, dataSortColumn, ...rest }) =>
      Column({
        title: t("common.maDichVu"),
        width: 200,
        dataIndex: "maDichVu",
        key: "maDichVu",
        i18Name: "common.maDichVu",
        sort_key: "maDichVu",
        dataSort: dataSortColumn["maDichVu"] || "",
        onClickSort: onClickSort,
        renderSearch: (
          <InputTimeout
            placeholder={t("common.nhapMaDichVu")}
            onChange={(e) => {
              onSearch("maDichVu", e);
            }}
          />
        ),
        ...rest,
      }),
    SO_KET_NOI: ({ onSearch, onClickSort, dataSortColumn, ...rest }) =>
      Column({
        title: t("cdha.soKetNoi"),
        width: 200,
        dataIndex: "soKetNoi",
        key: "soKetNoi",
        i18Name: "cdha.soKetNoi",
        sort_key: "soKetNoi",
        dataSort: dataSortColumn["soKetNoi"] || "",
        onClickSort: onClickSort,
        renderSearch: (
          <InputTimeout
            placeholder={t("cdha.timSoKetNoi")}
            onChange={(e) => {
              onSearch("soKetNoi", e);
            }}
          />
        ),
        ...rest,
      }),
  };

  return [NOITRU_COLUMNS];
}

export default useColumns;
