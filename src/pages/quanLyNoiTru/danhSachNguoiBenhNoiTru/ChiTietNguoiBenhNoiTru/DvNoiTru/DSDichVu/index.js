import React, {
  useEffect,
  useRef,
  useState,
  forwardRef,
  useImperativeHandle,
} from "react";
import { Main } from "./styled";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import {
  DOI_TUONG_SU_DUNG,
  LOAI_DICH_VU,
  ROLES,
  THIET_LAP_CHUNG,
  TRANG_THAI_DICH_VU,
  TRANG_THAI_NB,
  TRANG_THAI_PHIEU_THU_THANH_TOAN,
  GIOI_TINH_BY_VALUE,
} from "constants/index";
import {
  useConfirm,
  useListAll,
  useLoading,
  useStore,
  useThietLap,
} from "hooks";
import ChinhSuaDVKyThuat from "pages/chiDinhDichVu/DichVuKyThuat/ChinhSuaDVKyThuat";
import ModalDoiDichVu from "pages/khamBenh/ChiDinhDichVu/ModalDoiDichVu";
import { canEditOrUpdate } from "pages/khamBenh/ChiDinhDichVu/utils";
import ModalChiTietDichVu from "../../DvNgoaiTru/ModalChiTietDichVu";
import { useParams } from "react-router-dom";
import {
  Checkbox,
  Tooltip,
  Pagination,
  TableWrapper,
  HeaderSearch,
  Select,
  DatePicker,
  InputTimeout,
} from "components";
import useColumns from "../useColumns";
import { InputNumber } from "antd";
import moment from "moment";
import { SVG } from "assets";
import { cloneDeep } from "lodash";
import { checkRole, checkRoleOr } from "lib-utils/role-utils";
import ModalLichSuChinhSua from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ModalLichSuChinhSua";
import { checkBatBuocPhanPhongGiuong } from "utils";
import useViTriChamCong, {
  KEYS_VI_TRI_CHAM_CONG,
} from "pages/khamBenh/ChiDinhDichVu/ModalChiDinhDichVu/useViTriChamCong";
import useThietLapGiaoDien from "pages/khamBenh/ChiDinhDichVu/ModalChiDinhDichVu/useThietLapGiaoDien";
import DOMPurify from "dompurify";
import { useDonViKetNoiPacs } from "pages/hoSoBenhAn/ChiTietNguoiBenh/hooks/useDonViKetNoiPacs";
import { checkChotThoiGianDotDieuTri } from "pages/quanLyNoiTru/utils";
import { TRANG_THAI } from "pages/xetNghiem/configs";
import ModalHuyHoanDichVu from "components/ModalHuyHoanDichVu";
import ModalHoanDichVu from "components/ModalHoanDichVu";
import KetQuaXetNghiem from "pages/khamBenh/ChiDinhDichVu/components/KetQuaXetNghiem";

const { Column, Setting } = TableWrapper;

const DSDichVu = (
  {
    isEdit = false,
    khoaLamViecId,
    isReadonlyDvNoiTru,
    onShowDienBienChiSoXetNghiem = () => {},
    isBatBuocPhanPhongGiuong,
  },
  ref
) => {
  const { showConfirm } = useConfirm();
  const { id } = useParams();
  const { t } = useTranslation();
  const { DATA_TEN_HIEN_THI } = useThietLapGiaoDien({
    useToDieuTriNoiTruSettings: true,
  });
  const [, , , getTenField] = useViTriChamCong({
    show: true,
  });
  const [NOITRU_COLUMNS] = useColumns();
  const refSuaThongTin = useRef(null);
  const refSettings = useRef(null);
  const refLogModal = useRef(null);
  const modalRefThongTinDichVu = useRef(null);
  const refModalDoiDichVu = useRef(null);
  const refModalHoanDichVu = useRef(null);
  const refModalHuyHoanDichVu = useRef(null);

  const [dataPageSize, isFinish] = useThietLap(THIET_LAP_CHUNG.PAGE_SIZE, 10);
  const { checkDonViKetNoi } = useDonViKetNoiPacs();
  const [dataHOAN_DICH_VU_NOI_TRU_DA_THANH_TOAN] = useThietLap(
    THIET_LAP_CHUNG.HOAN_DICH_VU_NOI_TRU_DA_THANH_TOAN
  );

  const {
    pacs: { getUrl },
    chiDinhKhamBenh: { inPhieuKetQua, onDeleteDichVu },
    dvNoiTru: { onSearchDvKt, updateData, onSortChangeDvkt },
    phongThucHien: { onSearchParams: searchPhongThucHien },
    loaiDoiTuongLoaiHinhTT: { getListLoaiDoiTuongTT },
    chiDinhKhamBenh: { xemKetQua, huyXemKetQua },
  } = useDispatch();

  const [state, _setState] = useState({
    show: false,
    currentItem: {},
    currentIndex: -1,
    isCheckedAll: false,
    dataSource: [],
    listLoaiHinhThanhToanCuaDoiTuong: [],
  });

  const setState = (data) => {
    _setState({
      ...state,
      ...data,
    });
  };
  const listAllPhieuThuChotDot = useStore(
    "dsPhieuThuChotDotDieuTri.listAllPhieuThuChotDot",
    []
  );

  const thongTinCoBan = useStore("nbDotDieuTri.thongTinCoBan", {});
  const chiTietNguoiBenhNoiTru = useStore(
    "danhSachNguoiBenhNoiTru.chiTietNguoiBenhNoiTru"
  );
  const dataSortColumnDvkt = useStore("dvNoiTru.dataSortColumnDvkt", {});
  const [listAllNguonKhacChiTra] = useListAll("nguonKhac", {}, true);

  const [dataSearch, setDataSearch] = useState({
    page: 0,
    size: 10,
    totalElements: 0,
    dsDoiTuongKcb: [2, 3, 4],
    nbDotDieuTriId: id,
  });
  const { dsDichVu } = useSelector((state) => state.dvNoiTru);

  const listPhongThucHien = useSelector(
    (state) => state.phongThucHien.listData
  );
  const { showLoading, hideLoading } = useLoading();

  const onSearchByParams = (params) => {
    onSearchDvKt(params).then((s) => {
      const { pageNumber, pageSize, totalElements } = s || {};
      setDataSearch({
        ...dataSearch,
        ...params,
        page: pageNumber,
        size: pageSize,
        totalElements: totalElements,
      });
    });
  };

  useEffect(() => {
    if (id && isFinish) {
      onSearchByParams({
        ...dataSearch,
        nbDotDieuTriId: id,
        dsKhoaChiDinhId: khoaLamViecId ? [khoaLamViecId] : [],
        size: dataPageSize,
        dsTrangThaiHoan: [0],
      });
    }
  }, [id, khoaLamViecId, isFinish, dataPageSize]);

  useEffect(() => {
    if (dsDichVu) {
      setState({
        dataSource: dsDichVu,
        orgData: dsDichVu,
      });
    }
  }, [dsDichVu]);

  useEffect(() => {
    if (thongTinCoBan.loaiDoiTuongId && state.currentItem?.id)
      getListLoaiDoiTuongTT(
        {
          page: "",
          size: "",
          loaiDoiTuongId: thongTinCoBan.loaiDoiTuongId,
          active: true,
          dsDichVuId: state.currentItem.dichVuId
            ? [state.currentItem.dichVuId]
            : null,
          khoaChiDinhId: state.currentItem.khoaChiDinhId,
          ngayThucHien: state.currentItem.ngayThucHien,
          ngayVaoVien:
            thongTinCoBan.thoiGianVaoVien &&
            moment(thongTinCoBan.thoiGianVaoVien).format("YYYY-MM-DD"),
          doiTuongKcb: thongTinCoBan.doiTuongKcb,
        },
        { ignoreUpdateStore: isEdit }
      )
        .then((data) => {
          isEdit &&
            setState({
              listLoaiHinhThanhToanCuaDoiTuong: data.map((item) => ({
                id: item.loaiHinhThanhToanId,
                ten: item.tenLoaiHinhThanhToan,
              })),
            });
        })
        .catch(
          () => isEdit && setState({ listLoaiHinhThanhToanCuaDoiTuong: [] })
        );
  }, [thongTinCoBan.loaiDoiTuongId, state.currentItem]);

  useImperativeHandle(ref, () => ({
    refreshList: () => {
      onSearchByParams({ ...dataSearch });
    },
    getEditList: () => {
      return state.dataSource.filter((x) => x.isEditing);
    },
    cancelEditList: () => {
      setState({
        dataSource: cloneDeep(state.orgData),
        currentItem: {},
        currentIndex: -1,
      });
    },

    clearSelect: () => {
      setState({ currentItem: {}, currentIndex: -1 });
    },
    onSettings: onSettings,
    onHoanDv: onHoanDv,
    onHuyHoan: onHuyHoan,
  }));

  const onViewPacs = (data) => {
    getUrl({ id: data?.id }).then((res) => {
      window.open(res, "_blank").focus();
    });
  };

  const onViewPdf = async (data) => {
    const { nbDotDieuTriId, soPhieuId, loaiDichVu, soKetNoi, id } = data;
    try {
      showLoading();

      const obj = {
        nbDotDieuTriId,
        loaiDichVu,
        nbDichVuId: id,
      };

      switch (loaiDichVu) {
        case LOAI_DICH_VU.XET_NGHIEM:
          obj.dsSoPhieuId = soPhieuId ? [soPhieuId] : null;
          break;
        case LOAI_DICH_VU.CDHA:
          obj.dsSoKetNoi = soKetNoi ? [soKetNoi] : null;
          break;
        default:
          break;
      }

      await inPhieuKetQua(obj);
    } catch (error) {
    } finally {
      hideLoading();
    }
  };

  const onDelete = (record) => {
    showConfirm(
      {
        title: t("common.xoaDuLieu"),
        content: t("common.banCoChacMuonXoa") + (record?.tenDichVu || "") + "?",
        cancelText: t("common.quayLai"),
        okText: t("common.dongY"),
        classNameOkText: "button-error",
        showBtnOk: true,
        typeModal: "error",
      },
      () => {
        onDeleteDichVu({
          id: record.id,
          loaiDichVu: record.loaiDichVu,
        }).then((s) => refreshData());
      }
    );
  };

  const refreshData = () => {
    onSearchByParams({ ...dataSearch });
  };

  const onEdit = (record) => () => {
    if (!checkBatBuocPhanPhongGiuong(isBatBuocPhanPhongGiuong))
      refSuaThongTin.current &&
        refSuaThongTin.current.show(record, () => {
          onSearchByParams({ ...dataSearch });
        });
  };

  const onSettings = () => {
    refSettings && refSettings.current.settings();
  };

  const onSearch = (key, value) => {
    const newSearch = { ...dataSearch, [key]: value, page: 0 };

    onSearchByParams(newSearch);
  };

  const onSearchDate = (key, value) => {
    const newSearch = {
      ...dataSearch,
      [`tu${key}`]: value ? value.format("DD/MM/YYYY 00:00:00") : null,
      [`den${key}`]: value ? value.format("DD/MM/YYYY 23:59:59") : null,
    };

    onSearchByParams(newSearch);
  };

  const onChange = (key) => (e) => {
    let value = e;
    if (["khongTinhTien", "tuTra"].includes(key)) {
      value = e?.target?.checked;
    }

    if (key == "thoiGianThucHien") {
      value = e instanceof moment ? e.format() : null;
    }
    if (key == "thoiGianTiepNhan") {
      value = e instanceof moment ? e.format() : null;
    }

    let dataSource = cloneDeep(state.dataSource);

    dataSource[state.currentIndex][key] = value;
    if (key === "khongTinhTien" && value) {
      dataSource[state.currentIndex].tuTra = !value;
    }
    if (key === "tuTra" && value) {
      dataSource[state.currentIndex].khongTinhTien = !value;
    }
    dataSource[state.currentIndex].isEditing = true;

    if (key == "phongThucHienId") {
      dataSource[state.currentIndex].tenPhongThucHien =
        listPhongThucHien.find((x) => x.phongId == value)?.ten || "";
    }
    if (key == "ketQua") {
      dataSource[state.currentIndex].ketQua = value || "";
    }
    setState({ dataSource });
  };

  const onClickSort = (key, value) => {
    onSortChangeDvkt({
      [key]: value,
      dataSearch,
    });
  };

  const onDoiDichVu = (record) => {
    if (refModalDoiDichVu.current)
      refModalDoiDichVu.current.show(record, () => {
        refreshData();
      });
  };

  const onShowLogModal = (record) => (e) => {
    refLogModal && refLogModal.current.show(record);
    e.preventDefault();
    e.stopPropagation();
  };

  const onXemKetQua = (record) => async () => {
    try {
      showLoading();
      let s = await xemKetQua({
        payload: [record.id],
        loaiDichVu: record.loaiDichVu,
      });

      if (s.code === 0) {
        refreshData();
      }
    } catch (error) {
      console.error(error);
    } finally {
      hideLoading();
    }
  };

  const onHuyXemKetQua = (record) => async () => {
    try {
      showLoading();

      const s = await huyXemKetQua({
        payload: [record.id],
        loaiDichVu: record.loaiDichVu,
      });

      if (s.code === 0) {
        refreshData();
      }
    } catch (error) {
      console.error(error);
    } finally {
      hideLoading();
    }
  };

  const widthActionColumn = (data) => {
    const iconWidth = 24;
    const iconMargin = 8;

    // Đếm số icon ở line có nhiều icon nhất
    const maxIcons = Math.max(
      ...data.map((item) => {
        let count = 1; // luôn có icon xem chi tiết

        if (item.loaiDichVu === LOAI_DICH_VU.CDHA || checkDonViKetNoi(item))
          count++;
        if (
          [
            TRANG_THAI_DICH_VU.DA_CO_KET_QUA,
            TRANG_THAI_DICH_VU.DA_HOAN_TAT_DICH_VU,
          ].includes(item.trangThai)
        )
          count++;

        const isChot = checkChotThoiGianDotDieuTri(
          { thoiGian: item.thoiGianThucHien },
          listAllPhieuThuChotDot
        );
        if (isChot) count++;
        if (!isReadonlyDvNoiTru && isChot) count++;

        if (
          [
            LOAI_DICH_VU.XET_NGHIEM,
            LOAI_DICH_VU.CDHA,
            LOAI_DICH_VU.PHAU_THUAT_THU_THUAT,
            LOAI_DICH_VU.KHAM,
          ].includes(item.loaiDichVu) &&
          item.thanhToan !== TRANG_THAI_PHIEU_THU_THANH_TOAN.DA_THANH_TOAN &&
          item.doiDichVuKhiCoKetQua &&
          ![
            TRANG_THAI_NB.DA_RA_VIEN,
            TRANG_THAI_NB.DA_THANH_TOAN_RA_VIEN,
            TRANG_THAI_NB.DA_THANH_TOAN_HEN_DIEU_TRI,
          ].includes(thongTinCoBan?.trangThaiNb) &&
          !isReadonlyDvNoiTru
        ) {
          count++;
        }

        if (
          [
            TRANG_THAI_DICH_VU.DA_CO_KET_QUA,
            TRANG_THAI_DICH_VU.DA_DUYET,
          ]?.includes(item.trangThai) &&
          ((item.loaiDichVu == LOAI_DICH_VU.XET_NGHIEM &&
            checkRole([ROLES["KHAM_BENH"].XEM_HUY_KET_QUA_XN])) ||
            (item.loaiDichVu == LOAI_DICH_VU.CDHA &&
              checkRole([ROLES["KHAM_BENH"].XEM_HUY_KET_QUA_CDHA])) ||
            (item.loaiDichVu == LOAI_DICH_VU.PHAU_THUAT_THU_THUAT &&
              checkRole([ROLES["KHAM_BENH"].XEM_HUY_KET_QUA_PTTT])))
        ) {
          count++;
        }

        if (
          [TRANG_THAI_DICH_VU.DA_HOAN_TAT_DICH_VU]?.includes(item.trangThai) &&
          ((item.loaiDichVu == LOAI_DICH_VU.XET_NGHIEM &&
            checkRole([ROLES["KHAM_BENH"].XEM_HUY_KET_QUA_XN])) ||
            (item.loaiDichVu == LOAI_DICH_VU.CDHA &&
              checkRole([ROLES["KHAM_BENH"].XEM_HUY_KET_QUA_CDHA])) ||
            (item.loaiDichVu == LOAI_DICH_VU.PHAU_THUAT_THU_THUAT &&
              checkRole([ROLES["KHAM_BENH"].XEM_HUY_KET_QUA_PTTT])))
        ) {
          count++;
        }

        if (checkRole([ROLES["QUAN_LY_NOI_TRU"].XEM_CHECK_LOG_NB])) count++;
        if (dataHOAN_DICH_VU_NOI_TRU_DA_THANH_TOAN?.eval()) {
          if (isShowIconHoanDV(item) || item.trangThaiHoan === 10) {
            count++;
          }
        }

        return count;
      })
    );

    return maxIcons * (iconWidth + iconMargin) + 16; // 16: padding
  };

  const isShowIconHoanDV = (record) => {
    if (record.thanhToan === 50 && record.trangThaiHoan === 0) {
      if (
        (record.loaiDichVu === LOAI_DICH_VU.KHAM &&
          TRANG_THAI_DICH_VU["YEU_CAU_HOAN"].includes(record.trangThai)) ||
        (record.loaiDichVu === LOAI_DICH_VU.CDHA &&
          TRANG_THAI_DICH_VU["YEU_CAU_HOAN"].includes(record.trangThai)) ||
        (record.loaiDichVu === LOAI_DICH_VU.XET_NGHIEM &&
          TRANG_THAI["YEU_CAU_HOAN"].includes(record.trangThai)) ||
        record.loaiDichVu === LOAI_DICH_VU.NGOAI_DIEU_TRI
      ) {
        return true;
      }
      return false;
    }
    return false;
  };

  const onHoanDv = (data, callback = null) => {
    let gioiTinh = thongTinCoBan.gioiTinh
      ? GIOI_TINH_BY_VALUE[thongTinCoBan.gioiTinh]
      : "";

    let tuoi =
      thongTinCoBan.thangTuoi > 36 || thongTinCoBan.tuoi
        ? `${thongTinCoBan.tuoi} ${t("common.tuoi")}`
        : `${thongTinCoBan.thangTuoi} ${t("common.thang")}`;

    if (data?.length) {
      data.forEach((itemLoop) => {
        itemLoop.gioiTinh = gioiTinh;
        itemLoop.tuoi = tuoi;
      });

      refModalHoanDichVu.current &&
        refModalHoanDichVu.current.show(
          {
            data: data,
            selectedRowKeys: data.map((x) => x?.id),
          },
          () => {
            if (callback) {
              callback();
            } else {
              refreshData();
            }
          }
        );
    } else {
      message.error(t("khamBenh.chiDinh.khongCoDichVuThoaManDieuKienDeHoan"));
    }
  };

  const onHuyHoan = (data, callback = null) => {
    if (refModalHuyHoanDichVu.current)
      refModalHuyHoanDichVu.current.show({ data, nbDotDieuTriId: id }, () => {
        if (callback) {
          callback();
        } else {
          refreshData();
        }
      });
  };

  const columns = [
    NOITRU_COLUMNS.STT(),
    NOITRU_COLUMNS.TEN_NHOM_DICH_VU_CAP1({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnDvkt,
    }),
    NOITRU_COLUMNS.MA_DICH_VU({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnDvkt,
    }),
    NOITRU_COLUMNS.TEN_DICH_VU({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnDvkt,
      className: "center tenDichVu",
    }),
    NOITRU_COLUMNS.SO_KET_NOI({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnDvkt,
    }),
    NOITRU_COLUMNS.SO_LUONG(
      {
        onSearch,
        onClickSort,
        dataSortColumn: dataSortColumnDvkt,
      },
      {
        render: (item, list, index) => {
          const canEditSoLuong =
            [15, 20, 30, 40, 25, 35, 43, 38, 46, 62].includes(
              list?.trangThai
            ) ||
            (list?.doiDichVuKhiCoKetQua &&
              [20, 30, 40].includes(list?.loaiDichVu) &&
              ![100, 120, 130].includes(list.trangThai)) ||
            LOAI_DICH_VU.NGOAI_DIEU_TRI === list?.loaiDichVu;

          if (index === state.currentIndex && canEditSoLuong) {
            return (
              <InputNumber defaultValue={item} onChange={onChange("soLuong")} />
            );
          } else {
            return item;
          }
        },
      }
    ),
    NOITRU_COLUMNS.TRANG_THAI({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnDvkt,
    }),
    NOITRU_COLUMNS.THOI_GIAN_THUC_HIEN(
      {
        onSearch: onSearchDate,
        onClickSort,
        dataSortColumn: dataSortColumnDvkt,
      },
      isEdit && {
        render: (item, list, index) => {
          if (index === state.currentIndex) {
            return (
              <DatePicker
                showTime
                placeholder={t("common.chonNgay")}
                defaultValue={item ? moment(item) : null}
                format="DD/MM/YYYY HH:mm:ss"
                onChange={onChange("thoiGianThucHien")}
              />
            );
          } else {
            return item ? moment(item).format("DD/MM/YYYY HH:mm:ss") : "";
          }
        },
      }
    ),
    NOITRU_COLUMNS.THOI_GIAN_TIEP_NHAN(
      {
        onSearch: onSearchDate,
        onClickSort,
        dataSortColumn: dataSortColumnDvkt,
      },
      isEdit && {
        render: (item, list, index) => {
          if (index === state.currentIndex) {
            return (
              <DatePicker
                showTime
                placeholder={t("common.chonNgay")}
                defaultValue={item ? moment(item) : null}
                format="DD/MM/YYYY HH:mm:ss"
                onChange={onChange("thoiGianTiepNhan")}
              />
            );
          } else {
            return item ? moment(item).format("DD/MM/YYYY HH:mm:ss") : "";
          }
        },
      }
    ),
    NOITRU_COLUMNS.THOI_GIAN_CO_KET_QUA(
      {
        onSearch: onSearchDate,
        onClickSort,
        dataSortColumn: dataSortColumnDvkt,
      },
      isEdit && {
        render: (item, list, index) => {
          if (index === state.currentIndex) {
            return (
              <DatePicker
                showTime
                placeholder={t("common.chonNgay")}
                defaultValue={item ? moment(item) : null}
                format="DD/MM/YYYY HH:mm:ss"
                onChange={onChange("thoiGianCoKetQua")}
              />
            );
          } else {
            return item ? moment(item).format("DD/MM/YYYY HH:mm:ss") : "";
          }
        },
      }
    ),
    NOITRU_COLUMNS.NGUON_KHAC(
      {
        onSearch,
        onClickSort,
        dataSortColumn: dataSortColumnDvkt,
      },
      {
        render: (item, list, index) => {
          if (index === state.currentIndex && isEdit) {
            return (
              <Select
                defaultValue={state.currentItem?.nguonKhacId}
                data={listAllNguonKhacChiTra}
                placeholder={t("danhMuc.nguonKhac")}
                onChange={onChange("nguonKhacId")}
              />
            );
          } else
            return (
              item && listAllNguonKhacChiTra?.find((o) => o.id === item)?.ten
            );
        },
      }
    ),
    NOITRU_COLUMNS.KHONG_TINH_TIEN(
      { onSearch, onClickSort, dataSortColumn: dataSortColumnDvkt },
      isEdit && {
        render: (item, list, index) => {
          if (index === state.currentIndex) {
            return (
              <Checkbox
                checked={item}
                disabled={
                  [20, 30, 40, 45, 60].includes(list.loaiDichVu)
                    ? !checkRoleOr([
                        ROLES["QUAN_LY_NOI_TRU"]
                          .CHINH_SUA_KHONG_TINH_TIEN_POPUP_CHI_DINH_DVKT,
                      ])
                    : false ||
                      !checkRole([
                        ROLES["QUAN_LY_NOI_TRU"].TICK_KHONG_TINH_TIEN,
                      ])
                }
                onChange={onChange("khongTinhTien")}
              />
            );
          } else {
            return <Checkbox checked={item} disabled />;
          }
        },
      }
    ),
    NOITRU_COLUMNS.TU_TRA(
      { onSearch, onClickSort, dataSortColumn: dataSortColumnDvkt },
      isEdit && {
        render: (item, list, index) => {
          if (index === state.currentIndex) {
            return <Checkbox checked={item} onChange={onChange("tuTra")} />;
          } else {
            return <Checkbox checked={item} disabled />;
          }
        },
      }
    ),
    NOITRU_COLUMNS.LOAI_HINH_THANH_TOAN(
      { onSearch, onClickSort, dataSortColumn: dataSortColumnDvkt },
      isEdit && {
        render: (item, list, index) => {
          if (index === state.currentIndex) {
            return (
              <Select
                value={list?.loaiHinhThanhToanId}
                data={state.listLoaiHinhThanhToanCuaDoiTuong}
                placeholder={t("baoCao.chonLoaiHinhThanhToan")}
                onChange={onChange("loaiHinhThanhToanId")}
              />
            );
          } else {
            return item;
          }
        },
      }
    ),
    NOITRU_COLUMNS.TEN_PHONG_THUC_HIEN(
      { onSearch, onClickSort, dataSortColumn: dataSortColumnDvkt },
      isEdit && {
        render: (item, list, index) => {
          if (
            index === state.currentIndex &&
            canEditOrUpdate(list.trangThai, list.loaiDichVu)
          ) {
            return (
              <Select
                defaultValue={item}
                placeholder={t("common.chonPhong")}
                data={(listPhongThucHien || []).map((item) => ({
                  id: item.phongId,
                  ten: item.ten,
                }))}
                onChange={onChange("phongThucHienId")}
              />
            );
          } else {
            return item;
          }
        },
      }
    ),
    NOITRU_COLUMNS.KET_QUA(
      { onClickSort, dataSortColumn: dataSortColumnDvkt },
      isEdit && {
        render: (item, list, index) => {
          if (
            index === state.currentIndex &&
            (list.dsDoiTuongSuDung || []).includes(
              DOI_TUONG_SU_DUNG.THUC_HIEN_TAI_KHOA
            ) &&
            list.loaiDichVu === LOAI_DICH_VU.XET_NGHIEM
          ) {
            return <InputTimeout value={item} onChange={onChange("ketQua")} />;
          } else if (list.loaiDichVu === LOAI_DICH_VU.XET_NGHIEM) {
            return (
              <KetQuaXetNghiem
                data={{ value: item, row: list, index }}
                isNbThieuTien={false}
                showKetLuan={false}
                isXetNghiem={true}
              />
            );
          } else {
            return (
              <div
                className="ketQua-ketLuan"
                dangerouslySetInnerHTML={{
                  __html: DOMPurify.sanitize(item),
                }}
              />
            );
          }
        },
      }
    ),
    NOITRU_COLUMNS.THANH_TIEN({
      onClickSort,
      dataSortColumn: dataSortColumnDvkt,
    }),
    NOITRU_COLUMNS.TT35(
      { onSearch, onClickSort, dataSortColumn: dataSortColumnDvkt },
      isEdit && {
        render: (item, list, index) => {
          if (index === state.currentIndex) {
            return <Select placeholder={t("danhMuc.chonMucDichSuDung")} />;
          } else {
            return item;
          }
        },
      }
    ),
    NOITRU_COLUMNS.KET_LUAN({
      onClickSort,
      dataSortColumn: dataSortColumnDvkt,
    }),

    NOITRU_COLUMNS.TEN_NHOM_DICH_VU_CAP2({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnDvkt,
    }),
    NOITRU_COLUMNS.BAC_SY_CHI_DINH({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnDvkt,
    }),
    NOITRU_COLUMNS.TEN_KHOA_CHI_DINH({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnDvkt,
      value: dataSearch?.dsKhoaChiDinhId,
      isDislaySearch: checkRole([
        ROLES["QUAN_LY_NOI_TRU"].THAO_TAC_NB_DA_RA_VIEN,
      ]),
    }),
    NOITRU_COLUMNS.THOI_GIAN_CHI_DINH({
      onSearch: onSearchDate,
      onClickSort,
      dataSortColumn: dataSortColumnDvkt,
    }),
    NOITRU_COLUMNS.TEN_KHOA_THUC_HIEN({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnDvkt,
    }),
    NOITRU_COLUMNS.MAN_HINH_CHI_DINH({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnDvkt,
    }),
    NOITRU_COLUMNS.DON_GIA_BH({
      onClickSort,
      dataSortColumn: dataSortColumnDvkt,
    }),
    NOITRU_COLUMNS.DON_GIA_KHONG_BH({
      onClickSort,
      dataSortColumn: dataSortColumnDvkt,
    }),
    NOITRU_COLUMNS.CHE_PHAM_MAU_DVKT({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnDvkt,
    }),
    NOITRU_COLUMNS.MA_TUI_MAU({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnDvkt,
    }),
    NOITRU_COLUMNS.TRANG_THAI_MAU_DVKT({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnDvkt,
    }),
    NOITRU_COLUMNS.CANH_BAO({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnDvkt,
    }),
    Column({
      title: t("khamBenh.chiDinh.ptvcTtvcNguoiTH"),
      dataIndex: "tenNguoiThucHien",
      key: "tenNguoiThucHien",
      width: 160,
      show: false,
      i18Name: "khamBenh.chiDinh.ptvcTtvcNguoiTH",
    }),
    ...KEYS_VI_TRI_CHAM_CONG.map((key) =>
      Column({
        title: DATA_TEN_HIEN_THI[key],
        dataIndex: getTenField(key),
        key: key,
        width: 160,
        show: false,
        i18Name: DATA_TEN_HIEN_THI[key],
      })
    ),
    NOITRU_COLUMNS.TRANG_THAI_HOAN({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnDvkt,
    }),
    NOITRU_COLUMNS.PHAN_LOAI_PTTT({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnDvkt,
    }),
    NOITRU_COLUMNS.BENH_PHAM({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnDvkt,
    }),
    Column({
      title: t("common.nguoiXemKetQua"),
      dataIndex: "tenNguoiXemKetQua",
      key: "tenNguoiXemKetQua",
      width: 160,
      show: true,
      i18Name: "common.nguoiXemKetQua",
    }),
    Column({
      title: t("common.thoiGianXemKetQua"),
      dataIndex: "thoiGianXemKetQua",
      key: "thoiGianXemKetQua",
      width: 160,
      show: true,
      i18Name: "common.thoiGianXemKetQua",
      render: (item) => item && moment(item).format("DD/MM/YYYY HH:mm:ss"),
    }),
    Column({
      title: (
        <>
          {t("common.tienIch")} <Setting refTable={refSettings} />
        </>
      ),
      width: widthActionColumn(state.dataSource),
      align: "center",
      fixed: "right",
      ignore: true,
      hidden: isEdit,
      render: (data, item, index) => {
        const isChotThoiGianDotDieuTri = checkChotThoiGianDotDieuTri(
          { thoiGian: item.thoiGianThucHien },
          listAllPhieuThuChotDot
        );

        return (
          <div className="action-wrapper">
            {/* SAKURA-42821 */}
            <Tooltip title={t("common.xemChiTiet")} placement="bottom">
              <SVG.IcEye
                className="ic-action"
                onClick={() =>
                  modalRefThongTinDichVu.current &&
                  modalRefThongTinDichVu.current.show({
                    ttDichVu: data,
                    ttNb: chiTietNguoiBenhNoiTru,
                  })
                }
              />
            </Tooltip>
            {(data.loaiDichVu === LOAI_DICH_VU.CDHA ||
              checkDonViKetNoi(data)) && (
              <Tooltip title={t("hsba.xemKqPacs")} placement="bottom">
                <SVG.IcViewImagePacs
                  onClick={() => onViewPacs(data)}
                  className="ic-action"
                />
              </Tooltip>
            )}
            {[
              TRANG_THAI_DICH_VU.DA_CO_KET_QUA,
              TRANG_THAI_DICH_VU.DA_HOAN_TAT_DICH_VU,
            ].includes(data.trangThai) &&
              [LOAI_DICH_VU.XET_NGHIEM, LOAI_DICH_VU.CDHA].includes(
                data.loaiDichVu
              ) && (
                <Tooltip title={t("hsba.xemKqPdf")} placement="bottom">
                  <SVG.IcPdf
                    className="ic-action"
                    onClick={() => onViewPdf(data)}
                  />
                </Tooltip>
              )}
            {isChotThoiGianDotDieuTri && (
              <Tooltip
                title={t("quanLyNoiTru.chinhSuaDichVu")}
                placement="bottom"
              >
                <SVG.IcEdit className="ic-action" onClick={onEdit(item)} />
              </Tooltip>
            )}
            {!isReadonlyDvNoiTru && isChotThoiGianDotDieuTri && (
              <Tooltip title={t("tiepDon.xoaDichVu")} placement="bottom">
                <SVG.IcDelete
                  className="ic-action"
                  onClick={() => onDelete(item)}
                />
              </Tooltip>
            )}
            {[
              LOAI_DICH_VU.XET_NGHIEM,
              LOAI_DICH_VU.CDHA,
              LOAI_DICH_VU.PHAU_THUAT_THU_THUAT,
              LOAI_DICH_VU.KHAM,
            ].includes(item.loaiDichVu) &&
              item.thanhToan !==
                TRANG_THAI_PHIEU_THU_THANH_TOAN.DA_THANH_TOAN &&
              item.doiDichVuKhiCoKetQua &&
              ![
                TRANG_THAI_NB.DA_RA_VIEN,
                TRANG_THAI_NB.DA_THANH_TOAN_RA_VIEN,
                TRANG_THAI_NB.DA_THANH_TOAN_HEN_DIEU_TRI,
              ].includes(thongTinCoBan?.trangThaiNb) &&
              !isReadonlyDvNoiTru && (
                <Tooltip title={t("tiepDon.doiDichVu")} placement="bottom">
                  <SVG.IcChuyenDichVu
                    className="ic-action"
                    onClick={() => onDoiDichVu(item)}
                  />
                </Tooltip>
              )}
            {[
              TRANG_THAI_DICH_VU.DA_CO_KET_QUA,
              TRANG_THAI_DICH_VU.DA_DUYET,
            ]?.includes(item.trangThai) &&
              ((item.loaiDichVu == LOAI_DICH_VU.XET_NGHIEM &&
                checkRole([ROLES["KHAM_BENH"].XEM_HUY_KET_QUA_XN])) ||
                (item.loaiDichVu == LOAI_DICH_VU.CDHA &&
                  checkRole([ROLES["KHAM_BENH"].XEM_HUY_KET_QUA_CDHA])) ||
                (item.loaiDichVu == LOAI_DICH_VU.PHAU_THUAT_THU_THUAT &&
                  checkRole([ROLES["KHAM_BENH"].XEM_HUY_KET_QUA_PTTT]))) && (
                <Tooltip
                  title={t("common.xacNhanXemKetQua")}
                  placement="bottom"
                >
                  <SVG.IcTick
                    color={"var(--color-green-primary)"}
                    className="ic-action"
                    onClick={onXemKetQua(item)}
                  />
                </Tooltip>
              )}
            {[TRANG_THAI_DICH_VU.DA_HOAN_TAT_DICH_VU]?.includes(
              item.trangThai
            ) &&
              ((item.loaiDichVu == LOAI_DICH_VU.XET_NGHIEM &&
                checkRole([ROLES["KHAM_BENH"].XEM_HUY_KET_QUA_XN])) ||
                (item.loaiDichVu == LOAI_DICH_VU.CDHA &&
                  checkRole([ROLES["KHAM_BENH"].XEM_HUY_KET_QUA_CDHA])) ||
                (item.loaiDichVu == LOAI_DICH_VU.PHAU_THUAT_THU_THUAT &&
                  checkRole([ROLES["KHAM_BENH"].XEM_HUY_KET_QUA_PTTT]))) && (
                <Tooltip
                  title={t("common.huyXacNhanXemKetQua")}
                  placement="bottom"
                >
                  <SVG.IcCloseCircle
                    color={"var(--color-red-primary)"}
                    className="ic-action"
                    onClick={onHuyXemKetQua(item)}
                  />
                </Tooltip>
              )}
            {checkRole([ROLES["QUAN_LY_NOI_TRU"].XEM_CHECK_LOG_NB]) && (
              <Tooltip
                placement="leftBottom"
                title={t("quanLyNoiTru.kiemTraLichSuThayDoi")}
              >
                <SVG.IcShowLog
                  className="ic-action"
                  onClick={onShowLogModal(item)}
                />
              </Tooltip>
            )}

            {dataHOAN_DICH_VU_NOI_TRU_DA_THANH_TOAN?.eval() && (
              <>
                {/* Hoàn dịch vụ */}
                {isShowIconHoanDV(item) && (
                  <Tooltip
                    title={t("khamBenh.chiDinh.hoanDichVu")}
                    placement="leftBottom"
                  >
                    <SVG.IcHoanDv
                      onClick={() => onHoanDv([item])}
                      className="ic-action"
                    />
                  </Tooltip>
                )}

                {/* Hủy yêu cầu hoàn */}
                {item.trangThaiHoan === 10 && (
                  <Tooltip
                    title={t("khamBenh.chiDinh.huyYeuCauHoan")}
                    placement="leftBottom"
                  >
                    <SVG.IcHuyHoanDv
                      className="ic-action"
                      onClick={() => onHuyHoan([item])}
                      title={t("khamBenh.chiDinh.huyYeuCauHoan")}
                    />
                  </Tooltip>
                )}
              </>
            )}
          </div>
        );
      },
    }),
  ];

  const onRow = (record, index) => {
    return {
      onClick: () => {
        if (!isEdit) return;
        if (index !== state.currentIndex) {
          setState({
            currentItem: record,
            currentIndex: index,
          });

          searchPhongThucHien({
            dsDichVuId: [record.dichVuId],
            khoaChiDinhId: record?.khoaChiDinhId,
            loaiDoiTuongId: thongTinCoBan?.loaiDoiTuongId,
            loaiHinhThanhToanId: record?.loaiHinhThanhToanId,
            nbDotDieuTriId: record?.nbDotDieuTriId,
            gioiTinh: thongTinCoBan?.gioiTinh,
            doiTuongKcb: thongTinCoBan?.doiTuongKcb,
          });
        }
      },
    };
  };

  const onChangePage = (page) => {
    const newSearch = { ...dataSearch, page: page - 1 };

    setDataSearch(newSearch);
    onSearchByParams(newSearch);
  };

  const handleSizeChange = (size) => {
    const newSearch = { ...dataSearch, page: 0, size };

    setDataSearch(newSearch);
    onSearchByParams(newSearch);
  };

  const onCheckAll = (e) => {
    setState({
      selectedRowKeys: e.target?.checked
        ? (dsDichVu || []).map((x) => x.id)
        : [],
      isCheckedAll: e.target?.checked,
    });

    updateData({
      listDeleteDv: e.target?.checked ? dsDichVu : [],
    });
  };

  const onSelectChange = (selectedRowKeys, data) => {
    setState({
      selectedRowKeys: [...new Set(selectedRowKeys)],
      isCheckedAll: selectedRowKeys.length == dsDichVu.length,
    });

    updateData({
      listDeleteDv: data,
    });
  };

  const rowSelection = {
    columnTitle: (
      <HeaderSearch
        title={
          <Checkbox
            onChange={onCheckAll}
            checked={state.isCheckedAll}
          ></Checkbox>
        }
      />
    ),
    columnWidth: 40,
    onChange: onSelectChange,
    selectedRowKeys: state.selectedRowKeys,
  };

  const setRowClassName = (record) => {
    if (record.khongTinhTien) {
      return "green-color";
    }
    if (record.tuTra) {
      return "orange-color";
    }
  };

  return (
    <Main noPadding={true}>
      <div className="table-content">
        <TableWrapper
          columns={columns}
          dataSource={state.dataSource || []}
          onRow={onRow}
          rowSelection={isEdit && rowSelection}
          rowKey={(record) => record.id}
          tableName="table_DVNOITRU_DSDichVu"
          ref={refSettings}
          columnResizable={true}
          rowClassName={setRowClassName}
        />
        <Pagination
          onChange={onChangePage}
          current={dataSearch?.page + 1}
          pageSize={dataSearch?.size}
          listData={dsDichVu || []}
          total={dataSearch?.totalElements}
          onShowSizeChange={handleSizeChange}
        />
      </div>

      <ChinhSuaDVKyThuat
        ref={refSuaThongTin}
        isReadonlyDvNoiTru={isReadonlyDvNoiTru}
        screen={ChinhSuaDVKyThuat.SCREEN["QUAN_LY_NOI_TRU/DV_NOI_TRU"]}
      />
      <ModalChiTietDichVu
        ref={modalRefThongTinDichVu}
        onShowDienBienChiSoXetNghiem={onShowDienBienChiSoXetNghiem}
      />
      <ModalDoiDichVu ref={refModalDoiDichVu} />
      {checkRole([ROLES["QUAN_LY_NOI_TRU"].XEM_CHECK_LOG_NB]) && (
        <ModalLichSuChinhSua ref={refLogModal} bangKey="nb_dich_vu" />
      )}
      <ModalHoanDichVu ref={refModalHoanDichVu} />
      <ModalHuyHoanDichVu ref={refModalHuyHoanDichVu} />
    </Main>
  );
};

export default forwardRef(DSDichVu);
