import React, {
  useEffect,
  useRef,
  useState,
  forwardRef,
  useImperativeHandle,
} from "react";
import { Main } from "./styled";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import Icon from "@ant-design/icons";
import ChinhSuaVatTu from "pages/chiDinhDichVu/DichVuVatTu/ChinhSuaVatTu";
import { useParams } from "react-router-dom";
import {
  Checkbox,
  Pagination,
  TableWrapper,
  Tooltip,
  HeaderSearch,
  Select,
  DatePicker,
} from "components";
import useColumns from "../useColumns";
import { LOAI_DICH_VU, THIET_LAP_CHUNG, ROLES } from "constants/index";
import { InputNumber } from "antd";
import moment from "moment";
import { useConfirm, useListAll, useStore, useThietLap } from "hooks";
import { SVG } from "assets";
import { cloneDeep } from "lodash";
import { checkBatBuocPhanPhong<PERSON>iuong } from "utils";
import { checkRole } from "lib-utils/role-utils";
import { checkChotThoiGianDotDieuTri } from "pages/quanLyNoiTru/utils";
import ModalHoanThuocVTYT from "components/ModalHoanThuocVTYT";

const { Column, Setting } = TableWrapper;

const DSVatTu = (
  {
    isEdit = false,
    khoaLamViecId,
    isReadonlyDvNoiTru,
    isBatBuocPhanPhongGiuong,
    khongBatBuocKhoa,
  },
  ref
) => {
  const { showConfirm } = useConfirm();
  const { id } = useParams();
  const { t } = useTranslation();
  const [NOITRU_COLUMNS] = useColumns();
  const refSuaThongTin = useRef(null);
  const refSettings = useRef(null);
  const refModalHoanThuocVTYT = useRef(null);
  const [dataPageSize, isFinish] = useThietLap(THIET_LAP_CHUNG.PAGE_SIZE, 10);
  const [dataHOAN_DICH_VU_NOI_TRU_DA_THANH_TOAN] = useThietLap(
    THIET_LAP_CHUNG.HOAN_DICH_VU_NOI_TRU_DA_THANH_TOAN
  );

  const [state, _setState] = useState({
    show: false,
    currentItem: {},
    currentIndex: -1,
    isCheckedAll: false,
    dataSource: [],
  });

  const setState = (data) => {
    _setState({
      ...state,
      ...data,
    });
  };
  const listAllPhieuThuChotDot = useStore(
    "dsPhieuThuChotDotDieuTri.listAllPhieuThuChotDot",
    []
  );

  const dataSortColumnVatTu = useStore("dvNoiTru.dataSortColumnVatTu", {});
  const [listAllNguonKhacChiTra] = useListAll("nguonKhac", {}, true);

  const [dataSearch, setDataSearch] = useState({
    page: 0,
    size: 10,
    totalElements: 0,
    dsDoiTuongKcb: [2, 3, 4],
    nbDotDieuTriId: id,
    dsTrangThaiHoan: [0, 10, 20],
  });
  const { dsVatTu } = useSelector((state) => state.dvNoiTru);
  const {
    dvNoiTru: { onSearchDvVatTu, updateData, onSortChangeVatTu },
    chiDinhVatTu: { onDeleteDichVu },
  } = useDispatch();

  const onSearchByParams = (params) => {
    onSearchDvVatTu(params).then((s) => {
      const { pageNumber, pageSize, totalElements } = s || {};
      setDataSearch({
        ...dataSearch,
        ...params,
        page: pageNumber,
        size: pageSize,
        totalElements: totalElements,
      });
    });
  };

  useEffect(() => {
    if ((id, isFinish)) {
      onSearchByParams({
        ...dataSearch,
        nbDotDieuTriId: id,
        dsKhoaChiDinhId:
          khoaLamViecId && !khongBatBuocKhoa ? [khoaLamViecId] : [],
        size: dataPageSize,
      });
    }
  }, [id, khoaLamViecId, isFinish, dataPageSize, khongBatBuocKhoa]);

  useEffect(() => {
    if (dsVatTu) {
      setState({
        dataSource: dsVatTu.map((item) => ({
          ...item,
          loaiDichVu: LOAI_DICH_VU.VAT_TU,
        })),
      });
    }
  }, [dsVatTu]);

  useImperativeHandle(ref, () => ({
    refreshList: () => {
      onSearchByParams({ ...dataSearch });
    },
    getEditList: () => {
      return state.dataSource
        .filter((x) => x.isEditing)
        .map((i) => ({ ...i, soLuong: i.soLuongYeuCau }));
    },
    clearSelect: () => {
      setState({ currentItem: {}, currentIndex: -1 });
    },
    onSettings: onSettings,
    onHoanDv: onHoanDv,
    onHuyHoan: onHuyHoan,
  }));

  const onChange = (key) => (e) => {
    let value = e;
    if (["khongTinhTien", "tuTra"].includes(key)) {
      value = e?.target?.checked;
    }

    if (key == "thoiGianThucHien") {
      value = e instanceof moment ? e.format() : null;
    }

    let dataSource = cloneDeep(state.dataSource);

    dataSource[state.currentIndex][key] = value;
    if (key === "khongTinhTien" && value) {
      dataSource[state.currentIndex].tuTra = !value;
    }
    if (key === "tuTra" && value) {
      dataSource[state.currentIndex].khongTinhTien = !value;
    }
    dataSource[state.currentIndex].isEditing = true;
    setState({ dataSource });
  };

  const onEdit = (record) => () => {
    if (!checkBatBuocPhanPhongGiuong(isBatBuocPhanPhongGiuong))
      refSuaThongTin.current &&
        refSuaThongTin.current.show(record, () => {
          onSearchByParams({ ...dataSearch });
        });
  };

  const onDelete = (record) => {
    showConfirm(
      {
        title: t("common.xoaDuLieu"),
        content: t("common.banCoChacMuonXoa") + (record?.tenDichVu || "") + "?",
        cancelText: t("common.quayLai"),
        okText: t("common.dongY"),
        classNameOkText: "button-error",
        showBtnOk: true,
        typeModal: "error",
      },
      () => {
        onDeleteDichVu(record.id).then((s) => refreshData());
      }
    );
  };

  const refreshData = () => {
    onSearchByParams({ ...dataSearch });
  };

  const onSettings = () => {
    refSettings && refSettings.current.settings();
  };

  const onSearch = (key, value) => {
    const newSearch = { ...dataSearch, [key]: value };

    onSearchByParams(newSearch);
  };

  const onSearchDate = (key, value) => {
    const newSearch = {
      ...dataSearch,
      [`tu${key}`]: value ? value.format("DD/MM/YYYY 00:00:00") : null,
      [`den${key}`]: value ? value.format("DD/MM/YYYY 23:59:59") : null,
    };

    onSearchByParams(newSearch);
  };

  const onClickSort = (key, value) => {
    onSortChangeVatTu({
      [key]: value,
      dataSearch,
    });
  };

  const onHuyHoan = (data) => {
    if (refModalHoanThuocVTYT.current)
      refModalHoanThuocVTYT.current.show(
        data,
        null,
        () => {
          refreshData();
        },
        null,
        {
          type: "huyHoan",
        }
      );
  };

  const onHoanDv = (data) => {
    if (refModalHoanThuocVTYT.current)
      refModalHoanThuocVTYT.current.show(data, null, () => {
        refreshData();
      });
  };

  const columns = [
    NOITRU_COLUMNS.STT(),
    NOITRU_COLUMNS.TEN_VAT_TU({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnVatTu,
      className: "tenDichVu",
    }),
    NOITRU_COLUMNS.THOI_GIAN_THUC_HIEN(
      {
        onSearch: onSearchDate,
        onClickSort,
        dataSortColumn: dataSortColumnVatTu,
      },
      {
        render: (item, list, index) => {
          if (index === state.currentIndex) {
            return (
              <DatePicker
                showTime
                placeholder={t("common.chonNgay")}
                defaultValue={item ? moment(item) : null}
                format="DD/MM/YYYY HH:mm:ss"
                onChange={onChange("thoiGianThucHien")}
              />
            );
          } else {
            return item ? moment(item).format("DD/MM/YYYY HH:mm:ss") : "";
          }
        },
      }
    ),
    NOITRU_COLUMNS.SO_LUONG_YEU_CAU(
      {
        onClickSort,
        dataSortColumn: dataSortColumnVatTu,
      },
      {
        render: (item, list, index) => {
          if (index === state.currentIndex && !list?.phieuLinhId) {
            return (
              <InputNumber
                defaultValue={item}
                onChange={onChange("soLuongYeuCau")}
              />
            );
          } else {
            return item;
          }
        },
      }
    ),
    Column({
      title: t("hsba.slDung"),
      width: 80,
      dataIndex: "soLuong",
      key: "soLuong",
      align: "center",
      i18Name: "hsba.slDung",
      sort_key: "soLuong",
      dataSort: dataSortColumnVatTu["soLuong"] || "",
      onClickSort: onClickSort,
    }),
    Column({
      title: t("quanLyNoiTru.dvNoiTru.soLuongTra"),
      width: 80,
      dataIndex: "soLuongTra",
      key: "soLuongTra",
      align: "center",
      i18Name: "quanLyNoiTru.dvNoiTru.soLuongTra",
      sort_key: "soLuongTra",
      dataSort: dataSortColumnVatTu["soLuongTra"] || "",
      onClickSort: onClickSort,
    }),
    Column({
      title: t("quanLyNoiTru.dvNoiTru.tenDonViTinh"),
      width: 100,
      dataIndex: "tenDonViTinh",
      key: "tenDonViTinh",
      i18Name: "quanLyNoiTru.dvNoiTru.tenDonViTinh",
      sort_key: "tenDonViTinh",
      dataSort: dataSortColumnVatTu["tenDonViTinh"] || "",
      onClickSort: onClickSort,
    }),
    NOITRU_COLUMNS.TEN_KHO({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnVatTu,
    }),
    NOITRU_COLUMNS.NGUON_KHAC(
      {
        onSearch,
        onClickSort,
        dataSortColumn: dataSortColumnVatTu,
      },
      {
        render: (item, list, index) => {
          if (index === state.currentIndex && isEdit) {
            return (
              <Select
                defaultValue={state.currentItem?.nguonKhacId}
                data={listAllNguonKhacChiTra}
                placeholder={t("danhMuc.nguonKhac")}
                onChange={onChange("nguonKhacId")}
              />
            );
          } else
            return (
              item && listAllNguonKhacChiTra?.find((o) => o.id === item)?.ten
            );
        },
      }
    ),
    NOITRU_COLUMNS.DA_PHAT_VAT_TU({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnVatTu,
    }),
    NOITRU_COLUMNS.KHONG_TINH_TIEN(
      {
        onSearch,
        onClickSort,
        dataSortColumn: dataSortColumnVatTu,
      },
      {
        render: (item, list, index) => {
          if (index === state.currentIndex) {
            return (
              <Checkbox
                checked={item}
                onChange={onChange("khongTinhTien")}
                disabled={
                  !checkRole([ROLES["QUAN_LY_NOI_TRU"].TICK_KHONG_TINH_TIEN])
                }
              />
            );
          } else {
            return <Checkbox checked={item} />;
          }
        },
      }
    ),
    NOITRU_COLUMNS.TU_TRA(
      {
        onSearch,
        onClickSort,
        dataSortColumn: dataSortColumnVatTu,
      },
      {
        render: (item, list, index) => {
          if (index === state.currentIndex) {
            return <Checkbox checked={item} onChange={onChange("tuTra")} />;
          } else {
            return <Checkbox checked={item} />;
          }
        },
      }
    ),
    NOITRU_COLUMNS.THANH_TIEN({
      onClickSort,
      dataSortColumn: dataSortColumnVatTu,
    }),
    NOITRU_COLUMNS.BAC_SY_CHI_DINH({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnVatTu,
    }),
    NOITRU_COLUMNS.TEN_KHOA_CHI_DINH({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnVatTu,
      value: khongBatBuocKhoa ? undefined : dataSearch?.dsKhoaChiDinhId,
      isDislaySearch: checkRole([
        ROLES["QUAN_LY_NOI_TRU"].THAO_TAC_NB_DA_RA_VIEN,
      ]),
    }),
    NOITRU_COLUMNS.SO_PHIEU_LINH({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnVatTu,
    }),
    NOITRU_COLUMNS.THOI_GIAN_CHI_DINH({
      onSearch: onSearchDate,
      onClickSort,
      dataSortColumn: dataSortColumnVatTu,
    }),
    NOITRU_COLUMNS.THOI_GIAN_PHAT({
      onSearch: onSearchDate,
      onClickSort,
      dataSortColumn: dataSortColumnVatTu,
    }),
    NOITRU_COLUMNS.GHI_CHU({
      onClickSort,
      dataSortColumn: dataSortColumnVatTu,
    }),
    NOITRU_COLUMNS.MAN_HINH_CHI_DINH({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnVatTu,
    }),
    NOITRU_COLUMNS.DON_GIA_BH({
      onClickSort,
      dataSortColumn: dataSortColumnVatTu,
    }),
    NOITRU_COLUMNS.DON_GIA_KHONG_BH({
      onClickSort,
      dataSortColumn: dataSortColumnVatTu,
    }),
    NOITRU_COLUMNS.BO_SUNG({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnVatTu,
    }),
    NOITRU_COLUMNS.DOT_XUAT({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnVatTu,
    }),
    Column({
      title: (
        <>
          {t("common.tienIch")} <Setting refTable={refSettings} />
        </>
      ),
      width: 100,
      align: "center",
      fixed: "right",
      ignore: true,
      hidden: isEdit,
      render: (data, item, index) => {
        const isChotThoiGianDotDieuTri = checkChotThoiGianDotDieuTri(
          { thoiGian: item.thoiGianThucHien },
          listAllPhieuThuChotDot
        );
        return (
          <div className="flex">
            {isChotThoiGianDotDieuTri && (
              <>
                <Tooltip
                  title={t("quanLyNoiTru.chinhSuaDichVu")}
                  placement="bottom"
                >
                  <SVG.IcEdit className="ic-action" onClick={onEdit(item)} />
                </Tooltip>
                {!isReadonlyDvNoiTru && (
                  <Tooltip title={t("tiepDon.xoaDichVu")} placement="bottom">
                    <SVG.IcDelete
                      className="ic-action"
                      onClick={() => onDelete(item)}
                    />
                  </Tooltip>
                )}
              </>
            )}

            {dataHOAN_DICH_VU_NOI_TRU_DA_THANH_TOAN?.eval() && (
              <>
                {item.thanhToan === 50 && item.trangThaiHoan === 0 && (
                  <Tooltip
                    title={t("khamBenh.chiDinh.hoanDichVu")}
                    placement="bottom"
                  >
                    <SVG.IcHoanDv
                      className="icon"
                      onClick={() => onHoanDv([item])}
                    />
                  </Tooltip>
                )}

                {item.trangThaiHoan === 10 && (
                  <Tooltip
                    title={t("khamBenh.chiDinh.huyYeuCauHoan")}
                    placement="bottom"
                  >
                    <SVG.IcHuyHoanDv
                      className="icon"
                      onClick={() => onHuyHoan([item])}
                    />
                  </Tooltip>
                )}
              </>
            )}
          </div>
        );
      },
    }),
  ];

  const onRow = (record, index) => {
    return {
      onClick: () => {
        if (!isEdit) return;

        if (index !== state.currentIndex) {
          setState({
            currentItem: record,
            currentIndex: index,
          });
        }
      },
    };
  };

  const onChangePage = (page) => {
    const newSearch = { ...dataSearch, page: page - 1 };

    setDataSearch(newSearch);
    onSearchByParams(newSearch);
  };

  const handleSizeChange = (size) => {
    const newSearch = { ...dataSearch, page: 0, size };

    setDataSearch(newSearch);
    onSearchByParams(newSearch);
  };

  const onSelectChange = (selectedRowKeys, data) => {
    setState({
      selectedRowKeys: [...new Set(selectedRowKeys)],
      isCheckedAll: selectedRowKeys.length == dsVatTu.length,
    });

    updateData({
      listDeleteDv: data,
    });
  };

  const onCheckAll = (e) => {
    setState({
      selectedRowKeys: e.target?.checked
        ? (dsVatTu || []).map((x) => x.id)
        : [],
      isCheckedAll: e.target?.checked,
    });

    updateData({
      listDeleteDv: e.target?.checked
        ? dsVatTu.map((x) => ({ ...x, loaiDichVu: LOAI_DICH_VU.VAT_TU }))
        : [],
    });
  };

  const rowSelection = {
    columnTitle: (
      <HeaderSearch
        title={
          <Checkbox
            onChange={onCheckAll}
            checked={state.isCheckedAll}
          ></Checkbox>
        }
      />
    ),
    columnWidth: 40,
    onChange: onSelectChange,
    selectedRowKeys: state.selectedRowKeys,
  };

  const setRowClassName = (record) => {
    if (record.khongTinhTien) {
      return "green-color";
    }
    if (record.tuTra) {
      return "orange-color";
    }
  };

  return (
    <Main noPadding={true}>
      <div className="table-content">
        <TableWrapper
          columns={columns}
          dataSource={state.dataSource || []}
          onRow={onRow}
          rowSelection={isEdit && rowSelection}
          rowKey={(record) => record.id}
          tableName="table_DVNOITRU_DSVatTu"
          ref={refSettings}
          columnResizable={true}
          rowClassName={setRowClassName}
        />

        <Pagination
          onChange={onChangePage}
          current={dataSearch?.page + 1}
          pageSize={dataSearch?.size}
          listData={dsVatTu || []}
          total={dataSearch?.totalElements}
          onShowSizeChange={handleSizeChange}
          style={{ flex: 1, justifyContent: "flex-end" }}
        />
      </div>

      <ChinhSuaVatTu
        ref={refSuaThongTin}
        isReadonlyDvNoiTru={isReadonlyDvNoiTru}
      />
      <ModalHoanThuocVTYT ref={refModalHoanThuocVTYT} />
    </Main>
  );
};

export default forwardRef(DSVatTu);
