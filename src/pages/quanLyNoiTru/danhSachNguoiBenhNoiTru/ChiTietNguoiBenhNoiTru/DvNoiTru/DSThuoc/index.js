import React, {
  useEffect,
  useRef,
  useState,
  forwardRef,
  useImperativeHandle,
} from "react";
import { Main } from "./styled";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import Icon from "@ant-design/icons";
import SuaThongTinThuoc from "pages/khamBenh/DonThuoc/SuaThongTinThuoc";
import { useParams } from "react-router-dom";
import {
  Checkbox,
  Pagination,
  TableWrapper,
  Tooltip,
  HeaderSearch,
  Select,
  DatePicker,
} from "components";
import useColumns from "../useColumns";
import { InputNumber } from "antd";
import moment from "moment";
import {
  LOAI_CHI_DINH,
  LOAI_DICH_VU,
  THIET_LAP_CHUNG,
  TRANG_THAI_THUOC,
  ROLES,
} from "constants/index";
import { useConfirm, useListAll, useStore, useThietLap } from "hooks";
import { SVG } from "assets";
import { checkBatBuocPhan<PERSON><PERSON><PERSON><PERSON>ong } from "utils";
import { checkRole } from "lib-utils/role-utils";
import { checkChotThoiGianDotDieuTri } from "pages/quanLyNoiTru/utils";
import { gopThuocKhacLo } from "utils/chi-dinh-thuoc-utils";
import ModalHoanThuocVTYT from "components/ModalHoanThuocVTYT";

const { Column, Setting } = TableWrapper;

const DSThuoc = (
  {
    isEdit = false,
    khoaLamViecId,
    isReadonlyDvNoiTru,
    isBatBuocPhanPhongGiuong,
    khongBatBuocKhoa,
  },
  ref
) => {
  const { showConfirm } = useConfirm();
  const { id } = useParams();
  const { t } = useTranslation();
  const [NOITRU_COLUMNS] = useColumns();
  const refSuaThongTinThuoc = useRef(null);
  const refSettings = useRef(null);
  const refModalHoanThuocVTYT = useRef(null);

  const [dataPageSize, isFinish] = useThietLap(THIET_LAP_CHUNG.PAGE_SIZE, 10);
  const [dataHOAN_DICH_VU_NOI_TRU_DA_THANH_TOAN] = useThietLap(
    THIET_LAP_CHUNG.HOAN_DICH_VU_NOI_TRU_DA_THANH_TOAN
  );

  const [state, _setState] = useState({
    show: false,
    currentItem: {},
    currentIndex: -1,
    isCheckedAll: false,
    dataSource: [],
  });

  const setState = (data) => {
    _setState({
      ...state,
      ...data,
    });
  };
  const listAllPhieuThuChotDot = useStore(
    "dsPhieuThuChotDotDieuTri.listAllPhieuThuChotDot",
    []
  );

  const dataSortColumnThuoc = useStore("dvNoiTru.dataSortColumnThuoc", {});
  const chiTietNguoiBenhNoiTru = useStore(
    "danhSachNguoiBenhNoiTru.chiTietNguoiBenhNoiTru"
  );
  const [dataSearch, setDataSearch] = useState({
    page: 0,
    size: 10,
    totalElements: 0,
    dsDoiTuongKcb: [2, 3, 4],
    nbDotDieuTriId: id,
    dsTrangThaiHoan: [0, 10, 20],
  });

  const listMucDichSuDung = useStore("mucDichSuDung.listMucDichSuDung", []);
  const [listAllNguonKhacChiTra] = useListAll("nguonKhac", {}, true);

  const dsThuoc = useSelector((state) => state.dvNoiTru.dsThuoc);
  const {
    auth: { nhanVienId },
  } = useSelector((state) => state.auth);
  const {
    dvNoiTru: { onSearchDvThuoc, updateData, onSortChangeThuoc },
    chiDinhDichVuKho: { onDeleteDichVu, onDeleteAll },
    mucDichSuDung: { onSearch: getListTT20 },
    lieuDung: { getListAllLieuDung },
    chiDinhKhamBenh: { updateConfigData },
  } = useDispatch();

  const onSearchByParams = (params) => {
    onSearchDvThuoc(params).then((s) => {
      const { pageNumber, pageSize, totalElements } = s || {};
      setDataSearch({
        ...dataSearch,
        ...params,
        page: pageNumber,
        size: pageSize,
        totalElements: totalElements,
      });
    });
  };

  useEffect(() => {
    if (chiTietNguoiBenhNoiTru.id) {
      updateConfigData({
        configData: {
          thongTinNguoiBenh: chiTietNguoiBenhNoiTru,
        },
      });
    }
  }, [chiTietNguoiBenhNoiTru.id]);

  useEffect(() => {
    if (id && isFinish) {
      onSearchByParams({
        ...dataSearch,
        nbDotDieuTriId: id,
        dsKhoaChiDinhId:
          khoaLamViecId && !khongBatBuocKhoa ? [khoaLamViecId] : [],
        size: dataPageSize,
      });
    }
  }, [id, khoaLamViecId, isFinish, dataPageSize, khongBatBuocKhoa]);

  useEffect(() => {
    if (dsThuoc) {
      let _dsThuoc = gopThuocKhacLo(dsThuoc);

      setState({
        dataSource: _dsThuoc.map((item, idx) => ({
          ...item,
          index: idx + 1,
          loaiDichVu: LOAI_DICH_VU.THUOC,
        })),
      });
    }
  }, [dsThuoc]);

  useImperativeHandle(ref, () => ({
    refreshList: () => {
      onSearchByParams({ ...dataSearch });
    },
    getEditList: () => {
      return state.dataSource.filter((x) => x.isEditing);
    },
    clearSelect: () => {
      setState({ currentItem: {}, currentIndex: -1 });
    },
    onSettings: onSettings,
    onHoanDv: onHoanDv,
    onHuyHoan: onHuyHoan,
  }));

  const onChange = (key) => (e) => {
    let value = e;
    if (["khongTinhTien", "tuTra"].includes(key)) {
      value = e?.target?.checked;
    }

    if (key == "thoiGianThucHien") {
      value = e instanceof moment ? e.format() : null;
    }

    let dataSource = state.dataSource;

    dataSource[state.currentIndex][key] = value;
    if (key === "khongTinhTien" && value) {
      dataSource[state.currentIndex].tuTra = !value;
    }
    if (key === "tuTra" && value) {
      dataSource[state.currentIndex].khongTinhTien = !value;
    }
    dataSource[state.currentIndex].isEditing = true;

    setState({ dataSource });
  };

  const onEdit = (record) => () => {
    if (!checkBatBuocPhanPhongGiuong(isBatBuocPhanPhongGiuong))
      refSuaThongTinThuoc.current &&
        refSuaThongTinThuoc.current.show({ data: record }, () => {
          onSearchByParams({ ...dataSearch });
        });
  };

  const onDelete = (record) => {
    showConfirm(
      {
        title: t("common.xoaDuLieu"),
        content: t("common.banCoChacMuonXoa") + (record?.tenDichVu || "") + "?",
        cancelText: t("common.quayLai"),
        okText: t("common.dongY"),
        classNameOkText: "button-error",
        showBtnOk: true,
        typeModal: "error",
      },
      async () => {
        if (record.dsThuocGopId) {
          //Xóa các thuốc gộp khác lô
          await onDeleteAll(record?.dsThuocGopId);
        } else {
          await onDeleteDichVu(record.id);
        }
        refreshData();
      }
    );
  };

  const refreshData = () => {
    onSearchByParams({ ...dataSearch });
  };

  const onSettings = () => {
    refSettings && refSettings.current.settings();
  };

  const onSearch = (key, value) => {
    const newSearch = { ...dataSearch, [key]: value };

    onSearchByParams(newSearch);
  };

  const onSearchDate = (key, value) => {
    const newSearch = {
      ...dataSearch,
      [`tu${key}`]: value ? value.format("DD/MM/YYYY 00:00:00") : null,
      [`den${key}`]: value ? value.format("DD/MM/YYYY 23:59:59") : null,
    };

    onSearchByParams(newSearch);
  };

  const onClickSort = (key, value) => {
    onSortChangeThuoc({
      [key]: value,
      dataSearch,
    });
  };

  const isTrangThaiTuChoiDuyetDLS = (dsThuoc || []).some(
    (item) => item.trangThai == TRANG_THAI_THUOC.TU_CHOI_DUYET_DUOC_LAM_SANG.id
  );

  const onHuyHoan = (data) => {
    if (refModalHoanThuocVTYT.current)
      refModalHoanThuocVTYT.current.show(
        data,
        null,
        () => {
          refreshData();
        },
        null,
        {
          type: "huyHoan",
        }
      );
  };

  const onHoanDv = (data) => {
    if (refModalHoanThuocVTYT.current)
      refModalHoanThuocVTYT.current.show(data, null, () => {
        refreshData();
      });
  };

  const columns = [
    NOITRU_COLUMNS.STT(),
    NOITRU_COLUMNS.TEN_THUOC({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnThuoc,
      className: "tenDichVu",
    }),
    NOITRU_COLUMNS.THOI_GIAN_THUC_HIEN(
      {
        onSearch: onSearchDate,
        onClickSort,
        dataSortColumn: dataSortColumnThuoc,
      },
      isEdit && {
        render: (item, list, index) => {
          if (index === state.currentIndex) {
            return (
              <DatePicker
                showTime
                placeholder={t("common.chonNgay")}
                defaultValue={item ? moment(item) : null}
                format="DD/MM/YYYY HH:mm:ss"
                onChange={onChange("thoiGianThucHien")}
              />
            );
          } else {
            return item ? moment(item).format("DD/MM/YYYY HH:mm:ss") : "";
          }
        },
      }
    ),
    Column({
      title: t("hsba.slKe"),
      width: 80,
      dataIndex: "soLuongYeuCau",
      key: "soLuongYeuCau",
      align: "center",
      i18Name: "hsba.slKe",
      sort_key: "soLuongYeuCau",
      dataSort: dataSortColumnThuoc["soLuongYeuCau"] || "",
      onClickSort: onClickSort,
      render: (item, list, index) => {
        if (index === state.currentIndex && !list?.phieuLinhId) {
          return (
            <InputNumber defaultValue={item} onChange={onChange("soLuong")} />
          );
        } else {
          return item;
        }
      },
    }),
    Column({
      title: t("hsba.slDung"),
      width: 100,
      dataIndex: "soLuong",
      key: "soLuong",
      align: "center",
      i18Name: "hsba.slDung",
      sort_key: "soLuong",
      dataSort: dataSortColumnThuoc["soLuong"] || "",
      onClickSort: onClickSort,
    }),
    Column({
      title: t("quanLyNoiTru.dvNoiTru.soLuongTra"),
      width: 100,
      dataIndex: "soLuongTra",
      key: "soLuongTra",
      align: "center",
      i18Name: "quanLyNoiTru.dvNoiTru.soLuongTra",
      sort_key: "soLuongTra",
      dataSort: dataSortColumnThuoc["soLuongTra"] || "",
      onClickSort: onClickSort,
    }),
    Column({
      title: t("quanLyNoiTru.dvNoiTru.tenDonViTinh"),
      width: 100,
      dataIndex: "tenDonViTinh",
      key: "tenDonViTinh",
      i18Name: "quanLyNoiTru.dvNoiTru.tenDonViTinh",
      sort_key: "tenDonViTinh",
      dataSort: dataSortColumnThuoc["tenDonViTinh"] || "",
      onClickSort: onClickSort,
    }),
    NOITRU_COLUMNS.TEN_KHO({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnThuoc,
    }),
    NOITRU_COLUMNS.NGUON_KHAC(
      {
        onSearch,
        onClickSort,
        dataSortColumn: dataSortColumnThuoc,
      },
      {
        render: (item, list, index) => {
          if (index === state.currentIndex && isEdit) {
            return (
              <Select
                defaultValue={state.currentItem?.nguonKhacId}
                data={listAllNguonKhacChiTra}
                placeholder={t("danhMuc.nguonKhac")}
                onChange={onChange("nguonKhacId")}
              />
            );
          } else
            return (
              item && listAllNguonKhacChiTra?.find((o) => o.id === item)?.ten
            );
        },
      }
    ),
    NOITRU_COLUMNS.DA_PHAT({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnThuoc,
    }),
    NOITRU_COLUMNS.KHONG_TINH_TIEN(
      {
        onSearch,
        onClickSort,
        dataSortColumn: dataSortColumnThuoc,
      },
      isEdit && {
        render: (item, list, index) => {
          if (index === state.currentIndex) {
            return (
              <Checkbox
                checked={item}
                onChange={onChange("khongTinhTien")}
                disabled={
                  !checkRole([ROLES["QUAN_LY_NOI_TRU"].TICK_KHONG_TINH_TIEN])
                }
              />
            );
          } else {
            return <Checkbox checked={item} disabled />;
          }
        },
      }
    ),
    NOITRU_COLUMNS.TU_TRA(
      {
        onSearch,
        onClickSort,
        dataSortColumn: dataSortColumnThuoc,
      },
      isEdit && {
        render: (item, list, index) => {
          if (index === state.currentIndex) {
            return <Checkbox checked={item} onChange={onChange("tuTra")} />;
          } else {
            return <Checkbox checked={item} disabled />;
          }
        },
      }
    ),
    NOITRU_COLUMNS.THANH_TIEN({
      onClickSort,
      dataSortColumn: dataSortColumnThuoc,
    }),
    NOITRU_COLUMNS.TT20(
      {
        onSearch,
        onClickSort,
        dataSortColumn: dataSortColumnThuoc,
      },
      isEdit && {
        render: (item, list, index) => {
          if (index === state.currentIndex) {
            return (
              <Select
                data={listMucDichSuDung}
                placeholder={t("danhMuc.chonMucDichSuDung")}
                onChange={onChange("mucDichId")}
                defaultValue={list?.mucDichId}
              />
            );
          } else {
            return item;
          }
        },
      }
    ),
    NOITRU_COLUMNS.BAC_SY_CHI_DINH({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnThuoc,
    }),
    NOITRU_COLUMNS.TEN_KHOA_CHI_DINH({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnThuoc,
      value: khongBatBuocKhoa ? undefined : dataSearch?.dsKhoaChiDinhId,
      isDislaySearch: checkRole([
        ROLES["QUAN_LY_NOI_TRU"].THAO_TAC_NB_DA_RA_VIEN,
      ]),
    }),
    Column({
      title: t("common.lieuDung"),
      sort_key: "tenLieuDung",
      width: 180,
      dataIndex: "tenLieuDung",
      key: "tenLieuDung",
      i18Name: "common.lieuDung",
      sort_key: "tenLieuDung",
      dataSort: dataSortColumnThuoc["tenLieuDung"] || "",
      onClickSort: onClickSort,
    }),
    Column({
      title: t("common.cachDung"),
      sort_key: "cachDung",
      width: 150,
      dataIndex: "cachDung",
      key: "cachDung",
      i18Name: "common.cachDung",
      sort_key: "cachDung",
      dataSort: dataSortColumnThuoc["cachDung"] || "",
      onClickSort: onClickSort,
    }),
    NOITRU_COLUMNS.SO_PHIEU_LINH({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnThuoc,
    }),
    NOITRU_COLUMNS.THOI_GIAN_CHI_DINH({
      onSearch: onSearchDate,
      onClickSort,
      dataSortColumn: dataSortColumnThuoc,
    }),
    NOITRU_COLUMNS.THOI_GIAN_PHAT({
      onSearch: onSearchDate,
      onClickSort,
      dataSortColumn: dataSortColumnThuoc,
    }),
    NOITRU_COLUMNS.GHI_CHU({
      onClickSort,
      dataSortColumn: dataSortColumnThuoc,
    }),
    NOITRU_COLUMNS.MAN_HINH_CHI_DINH({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnThuoc,
    }),
    NOITRU_COLUMNS.DON_GIA_BH({
      onClickSort,
      dataSortColumn: dataSortColumnThuoc,
    }),
    NOITRU_COLUMNS.DON_GIA_KHONG_BH({
      onClickSort,
      dataSortColumn: dataSortColumnThuoc,
    }),
    NOITRU_COLUMNS.TRANG_THAI_THUOC({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnThuoc,
    }),
    Column({
      title: t("kho.ghiChuKhoaDuoc"),
      width: 180,
      dataIndex: "ghiChuDls",
      key: "ghiChuDls",
      align: "left",
      i18Name: "kho.ghiChuKhoaDuoc",
      sort_key: "ghiChuDls",
      dataSort: dataSortColumnThuoc["ghiChuDls"] || "",
      onClickSort: onClickSort,
      hidden: !isTrangThaiTuChoiDuyetDLS,
    }),
    NOITRU_COLUMNS.NHA_THUOC({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnThuoc,
    }),
    Column({
      title: t("common.slHuy"),
      sort_key: "soLuongHuy",
      width: 100,
      dataIndex: "soLuongHuy",
      key: "soLuongHuy",
      i18Name: "common.slHuy",
      sort_key: "soLuongHuy",
      dataSort: dataSortColumnThuoc["soLuongHuy"] || "",
      onClickSort: onClickSort,
    }),
    Column({
      title: t("common.lyDoHuy"),
      sort_key: "lyDoHuy",
      width: 150,
      dataIndex: "lyDoHuy",
      key: "lyDoHuy",
      i18Name: "common.lyDoHuy",
      sort_key: "lyDoHuy",
      dataSort: dataSortColumnThuoc["lyDoHuy"] || "",
      onClickSort: onClickSort,
    }),
    NOITRU_COLUMNS.LOAI_THUOC({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnThuoc,
    }),
    NOITRU_COLUMNS.TEN_KHO_TAI_KHOA({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnThuoc,
    }),
    NOITRU_COLUMNS.DOT_XUAT({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnThuoc,
    }),
    NOITRU_COLUMNS.BO_SUNG({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnThuoc,
    }),
    Column({
      title: (
        <>
          {t("common.tienIch")} <Setting refTable={refSettings} />
        </>
      ),
      width: 100,
      align: "center",
      fixed: "right",
      ignore: true,
      hidden: isEdit,
      render: (data, item, index) => {
        const isChotThoiGianDotDieuTri = checkChotThoiGianDotDieuTri(
          { thoiGian: item.thoiGianThucHien },
          listAllPhieuThuChotDot
        );
        return (
          <div className="ic-action">
            {isChotThoiGianDotDieuTri && (
              <>
                <Tooltip
                  title={t("quanLyNoiTru.chinhSuaDichVu")}
                  placement="bottom"
                >
                  <Icon
                    className="ic-action"
                    component={SVG.IcEdit}
                    onClick={onEdit(item)}
                  ></Icon>
                </Tooltip>
                {!isReadonlyDvNoiTru && (
                  <Tooltip title={t("tiepDon.xoaDichVu")} placement="bottom">
                    <Icon
                      className="ic-action"
                      component={SVG.IcDelete}
                      onClick={() => onDelete(item)}
                    ></Icon>
                  </Tooltip>
                )}
              </>
            )}

            {dataHOAN_DICH_VU_NOI_TRU_DA_THANH_TOAN?.eval() && (
              <>
                {item.thanhToan === 50 && item.trangThaiHoan === 0 && (
                  <Tooltip
                    title={t("khamBenh.chiDinh.hoanDichVu")}
                    placement="bottom"
                  >
                    <SVG.IcHoanDv
                      className="icon"
                      onClick={() => onHoanDv([item])}
                    />
                  </Tooltip>
                )}

                {item.trangThaiHoan === 10 && (
                  <Tooltip
                    title={t("khamBenh.chiDinh.huyYeuCauHoan")}
                    placement="bottom"
                  >
                    <SVG.IcHuyHoanDv
                      className="icon"
                      onClick={() => onHuyHoan([item])}
                    />
                  </Tooltip>
                )}
              </>
            )}
          </div>
        );
      },
    }),
  ];

  const onRow = (record, index) => {
    return {
      onClick: () => {
        if (!isEdit) return;

        if (index !== state.currentIndex) {
          setState({
            currentItem: record,
            currentIndex: index,
          });

          getListTT20({ dataSearch: { dichVuId: record.dichVuId } });
          getListAllLieuDung(
            {
              page: "",
              size: "",
              active: true,
              bacSiId: nhanVienId,
              dichVuId: record.dichVuId,
            },
            nhanVienId + "_" + record.dichVuId
          );
        }
      },
    };
  };

  const onChangePage = (page) => {
    const newSearch = { ...dataSearch, page: page - 1 };

    setDataSearch(newSearch);
    onSearchByParams(newSearch);
  };

  const handleSizeChange = (size) => {
    const newSearch = { ...dataSearch, page: 0, size };

    setDataSearch(newSearch);
    onSearchByParams(newSearch);
  };

  const onSelectChange = (selectedRowKeys, data) => {
    setState({
      selectedRowKeys: [...new Set(selectedRowKeys)],
      isCheckedAll: selectedRowKeys.length == dsThuoc.length,
    });

    updateData({
      listDeleteDv: data,
    });
  };

  const onCheckAll = (e) => {
    setState({
      selectedRowKeys: e.target?.checked
        ? (dsThuoc || []).map((x) => x.id)
        : [],
      isCheckedAll: e.target?.checked,
    });

    updateData({
      listDeleteDv: e.target?.checked
        ? dsThuoc.map((x) => ({ ...x, loaiDichVu: LOAI_DICH_VU.THUOC }))
        : [],
    });
  };

  const rowSelection = {
    columnTitle: (
      <HeaderSearch
        title={
          <Checkbox
            onChange={onCheckAll}
            checked={state.isCheckedAll}
          ></Checkbox>
        }
      />
    ),
    columnWidth: 40,
    onChange: onSelectChange,
    selectedRowKeys: state.selectedRowKeys,
  };

  const setRowClassName = (record) => {
    if (record.khongTinhTien) {
      return "green-color";
    }
    if (record.tuTra) {
      return "orange-color";
    }
  };

  return (
    <Main noPadding={true}>
      <div className="table-content">
        <TableWrapper
          columns={columns}
          dataSource={state.dataSource || []}
          onRow={onRow}
          rowKey={(record) => record.id}
          rowSelection={isEdit && rowSelection}
          tableName="table_DVNOITRU_DSThuoc"
          ref={refSettings}
          columnResizable={true}
          rowClassName={setRowClassName}
        />

        <Pagination
          onChange={onChangePage}
          current={dataSearch?.page + 1}
          pageSize={dataSearch?.size}
          listData={dsThuoc || []}
          total={dataSearch?.totalElements}
          onShowSizeChange={handleSizeChange}
          style={{ flex: 1, justifyContent: "flex-end" }}
        />
      </div>

      <SuaThongTinThuoc
        ref={refSuaThongTinThuoc}
        isReadonlyDvNoiTru={isReadonlyDvNoiTru}
      />
      <ModalHoanThuocVTYT ref={refModalHoanThuocVTYT} />
    </Main>
  );
};

export default forwardRef(DSThuoc);
