import React, {
  useEffect,
  useRef,
  useState,
  forwardRef,
  useImperativeHandle,
} from "react";
import { Main } from "./styled";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { useConfirm, useEnum, useStore, useThietLap } from "hooks";
import { LOAI_DICH_VU, ENUM, THIET_LAP_CHUNG, ROLES } from "constants/index";
import Icon from "@ant-design/icons";
import ChinhSuaSuatAn from "pages/chiDinhDichVu/DichVuSuatAn/ChinhSuaSuatAn";
import { useParams } from "react-router-dom";
import {
  Checkbox,
  Tooltip,
  Pagination,
  Select,
  TableWrapper,
  HeaderSearch,
  DatePicker,
} from "components";
import useColumns from "../useColumns";
import { InputNumber } from "antd";
import moment from "moment";
import { SVG } from "assets";
import { checkBatBuocPhanPhongGiuong } from "utils";
import { checkRole } from "lib-utils/role-utils";
import { checkChotThoiGianDotDieuTri } from "pages/quanLyNoiTru/utils";

const { Column, Setting } = TableWrapper;

const DSSuatAn = (
  {
    isEdit = false,
    khoaLamViecId,
    isReadonlyDvNoiTru,
    isBatBuocPhanPhongGiuong,
    khongBatBuocKhoa,
  },
  ref
) => {
  const { showConfirm } = useConfirm();
  const { id } = useParams();
  const { t } = useTranslation();
  const [NOITRU_COLUMNS] = useColumns();
  const [dataPageSize, isFinish] = useThietLap(THIET_LAP_CHUNG.PAGE_SIZE, 10);
  const [listLoaiChiDinh] = useEnum(ENUM.LOAI_CHI_DINH);

  const [state, _setState] = useState({
    show: false,
    currentItem: {},
    currentIndex: -1,
    isCheckedAll: false,
    dataSource: [],
  });

  const setState = (data) => {
    _setState({
      ...state,
      ...data,
    });
  };
  const listAllPhieuThuChotDot = useStore(
    "dsPhieuThuChotDotDieuTri.listAllPhieuThuChotDot",
    []
  );

  const dataSortColumnSuatAn = useStore("dvNoiTru.dataSortColumnSuatAn", {});

  const [dataSearch, setDataSearch] = useState({
    page: 0,
    size: 10,
    totalElements: 0,
    dsDoiTuongKcb: [2, 3, 4],
    nbDotDieuTriId: id,
    dsTrangThaiHoan: [0, 10, 20, 40],
  });
  const { dsSuatAn } = useSelector((state) => state.dvNoiTru);
  const {
    dvNoiTru: { onSearchDvSuatAn, updateData, onSortChangeSuatAn },
    chiDinhSuatAn: { xoaSuatAn },
  } = useDispatch();

  const refSuaThongTin = useRef(null);
  const refSettings = useRef(null);

  const [listTrangThai] = useEnum(ENUM.TRANG_THAI_PHIEU_LINH_SUAT_AN);

  const onSearchByParams = (params) => {
    onSearchDvSuatAn(params).then((s) => {
      const { pageNumber, pageSize, totalElements } = s || {};
      setDataSearch({
        ...dataSearch,
        ...params,
        page: pageNumber,
        size: pageSize,
        totalElements: totalElements,
      });
    });
  };

  useEffect(() => {
    if (id && isFinish) {
      onSearchByParams({
        ...dataSearch,
        nbDotDieuTriId: id,
        dsKhoaChiDinhId:
          khoaLamViecId && !khongBatBuocKhoa ? [khoaLamViecId] : [],
        size: dataPageSize,
      });
    }
  }, [id, khoaLamViecId, isFinish, dataPageSize, khongBatBuocKhoa]);

  useEffect(() => {
    if (dsSuatAn) {
      setState({
        dataSource: dsSuatAn.map((item) => ({
          ...item,
          loaiDichVu: LOAI_DICH_VU.SUAT_AN,
        })),
      });
    }
  }, [dsSuatAn]);

  useImperativeHandle(ref, () => ({
    refreshList: () => {
      onSearchByParams({ ...dataSearch });
    },
    getEditList: () => {
      return state.dataSource.filter((x) => x.isEditing);
    },
    clearSelect: () => {
      setState({ currentItem: {}, currentIndex: -1 });
    },
    onSettings: onSettings,
  }));

  const onChange = (key) => (e) => {
    let value = e;
    if (["khongTinhTien", "tuTra"].includes(key)) {
      value = e?.target?.checked;
    }

    if (key == "thoiGianThucHien") {
      value = e instanceof moment ? e.format() : null;
    }

    let dataSource = state.dataSource;

    dataSource[state.currentIndex][key] = value;
    if (key === "khongTinhTien" && value) {
      dataSource[state.currentIndex].tuTra = !value;
    }
    if (key === "tuTra" && value) {
      dataSource[state.currentIndex].khongTinhTien = !value;
    }
    dataSource[state.currentIndex].isEditing = true;

    setState({ dataSource });
  };

  const onDelete = (record) => {
    showConfirm(
      {
        title: t("common.xoaDuLieu"),
        content: t("common.banCoChacMuonXoa") + (record?.tenDichVu || "") + "?",
        cancelText: t("common.quayLai"),
        okText: t("common.dongY"),
        classNameOkText: "button-error",
        showBtnOk: true,
        typeModal: "error",
      },
      () => {
        xoaSuatAn(record.id).then((s) => refreshData());
      }
    );
  };

  const refreshData = () => {
    onSearchByParams({ ...dataSearch });
  };

  const onEdit = (record) => () => {
    if (!checkBatBuocPhanPhongGiuong(isBatBuocPhanPhongGiuong))
      refSuaThongTin.current &&
        refSuaThongTin.current.show(record, () => {
          onSearchByParams({ ...dataSearch });
        });
  };

  const onSettings = () => {
    refSettings && refSettings.current.settings();
  };

  const onSearch = (key, value) => {
    const newSearch = { ...dataSearch, [key]: value };

    onSearchByParams(newSearch);
  };

  const onSearchDate = (key, value) => {
    const newSearch = {
      ...dataSearch,
      [`tu${key}`]: value ? value.format("DD/MM/YYYY 00:00:00") : null,
      [`den${key}`]: value ? value.format("DD/MM/YYYY 23:59:59") : null,
    };

    onSearchByParams(newSearch);
  };

  const onClickSort = (key, value) => {
    onSortChangeSuatAn({
      [key]: value,
      dataSearch,
    });
  };

  const columns = [
    NOITRU_COLUMNS.STT(),
    NOITRU_COLUMNS.TEN_DICH_VU({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnSuatAn,
    }),
    Column({
      title: t("quanLyNoiTru.suatAn.loaiBuaAn"),
      width: 100,
      dataIndex: "tenLoaiBuaAn",
      key: "tenLoaiBuaAn",
      i18Name: "quanLyNoiTru.suatAn.loaiBuaAn",
      sort_key: "tenLoaiBuaAn",
      dataSort: dataSortColumnSuatAn["tenLoaiBuaAn"] || "",
      onClickSort: onClickSort,
    }),
    Column({
      title: t("quanLyNoiTru.suatAn.dvt"),
      width: 100,
      dataIndex: "tenDonViTinh",
      key: "tenDonViTinh",
      i18Name: "quanLyNoiTru.suatAn.dvt",
      sort_key: "tenDonViTinh",
      dataSort: dataSortColumnSuatAn["tenDonViTinh"] || "",
      onClickSort: onClickSort,
    }),
    Column({
      title: t("common.trangThai"),
      width: 160,
      dataIndex: "trangThai",
      key: "trangThai",
      i18Name: "common.trangThai",
      sort_key: "trangThai",
      dataSort: dataSortColumnSuatAn["trangThai"] || "",
      onClickSort: onClickSort,
      render: (value) => (listTrangThai || []).find((e) => e.id === value)?.ten,
      selectSearch: true,
      renderSearch: (
        <Select
          data={listTrangThai}
          mode="multiple"
          placeholder={t("common.chonTrangThai")}
          onChange={(e) => onSearch("dsTrangThai", e)}
        />
      ),
    }),
    NOITRU_COLUMNS.THOI_GIAN_THUC_HIEN(
      {
        onSearch: onSearchDate,
        onClickSort,
        dataSortColumn: dataSortColumnSuatAn,
      },
      isEdit && {
        render: (item, list, index) => {
          if (index === state.currentIndex) {
            return (
              <DatePicker
                showTime
                placeholder={t("common.chonNgay")}
                defaultValue={item ? moment(item) : null}
                format="DD/MM/YYYY HH:mm:ss"
                onChange={onChange("thoiGianThucHien")}
              />
            );
          } else {
            return item ? moment(item).format("DD/MM/YYYY HH:mm:ss") : "";
          }
        },
      }
    ),
    NOITRU_COLUMNS.SO_LUONG_KE(
      { onClickSort, dataSortColumn: dataSortColumnSuatAn },
      {
        render: (item, list, index) => {
          if (index === state.currentIndex && !list?.phieuLinhId) {
            return (
              <InputNumber defaultValue={item} onChange={onChange("soLuong")} />
            );
          } else {
            return item;
          }
        },
      }
    ),
    NOITRU_COLUMNS.SO_PHIEU_LINH({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnSuatAn,
    }),
    NOITRU_COLUMNS.DOT_XUAT({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnSuatAn,
    }),
    NOITRU_COLUMNS.BO_SUNG({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnSuatAn,
    }),
    Column({
      title: t("quanLyNoiTru.loaiTra"),
      width: 120,
      dataIndex: "loaiTra",
      key: "loaiTra",
      align: "center",
      i18Name: "quanLyNoiTru.loaiTra",
      sort_key: "loaiTra",
      dataSort: dataSortColumnSuatAn["loaiTra"] || "",
      onClickSort: onClickSort,
      render: (item) => listLoaiChiDinh.find((x) => x.id === item)?.ten,
    }),

    NOITRU_COLUMNS.KHONG_TINH_TIEN(
      {
        onSearch,
        onClickSort,
        dataSortColumn: dataSortColumnSuatAn,
      },
      isEdit && {
        render: (item, list, index) => {
          if (index === state.currentIndex) {
            return (
              <Checkbox
                checked={item}
                onChange={onChange("khongTinhTien")}
                disabled={
                  !checkRole([ROLES["QUAN_LY_NOI_TRU"].TICK_KHONG_TINH_TIEN])
                }
              />
            );
          } else {
            return <Checkbox checked={item} disabled />;
          }
        },
      }
    ),
    NOITRU_COLUMNS.TU_TRA(
      {
        onSearch,
        onClickSort,
        dataSortColumn: dataSortColumnSuatAn,
      },
      isEdit && {
        render: (item, list, index) => {
          if (index === state.currentIndex) {
            return <Checkbox checked={item} onChange={onChange("tuTra")} />;
          } else {
            return <Checkbox checked={item} disabled />;
          }
        },
      }
    ),
    NOITRU_COLUMNS.BAC_SY_CHI_DINH({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnSuatAn,
    }),
    NOITRU_COLUMNS.TEN_KHOA_CHI_DINH({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnSuatAn,
      value: khongBatBuocKhoa ? undefined : dataSearch?.dsKhoaChiDinhId,
      isDislaySearch: checkRole([
        ROLES["QUAN_LY_NOI_TRU"].THAO_TAC_NB_DA_RA_VIEN,
      ]),
    }),
    NOITRU_COLUMNS.THOI_GIAN_CHI_DINH({
      onSearch: onSearchDate,
      onClickSort,
      dataSortColumn: dataSortColumnSuatAn,
    }),
    NOITRU_COLUMNS.THOI_GIAN_PHAT({
      onSearch: onSearchDate,
      onClickSort,
      dataSortColumn: dataSortColumnSuatAn,
    }),
    NOITRU_COLUMNS.GHI_CHU({
      onClickSort,
      dataSortColumn: dataSortColumnSuatAn,
    }),
    NOITRU_COLUMNS.MAN_HINH_CHI_DINH({
      onSearch,
      onClickSort,
      dataSortColumn: dataSortColumnSuatAn,
    }),
    Column({
      title: (
        <>
          {t("common.tienIch")} <Setting refTable={refSettings} />
        </>
      ),
      width: 100,
      align: "center",
      fixed: "right",
      ignore: true,
      hidden: isEdit,
      render: (data, item, index) => {
        const isChotThoiGianDotDieuTri = checkChotThoiGianDotDieuTri(
          { thoiGian: item.thoiGianThucHien },
          listAllPhieuThuChotDot
        );
        return (
          <div className="ic-action">
            {isChotThoiGianDotDieuTri && (
              <>
                <Tooltip
                  title={t("quanLyNoiTru.chinhSuaDichVu")}
                  placement="bottom"
                >
                  <Icon
                    className="ic-action"
                    onClick={onEdit(item)}
                    component={SVG.IcEdit}
                  ></Icon>
                </Tooltip>
                {!isReadonlyDvNoiTru && (
                  <Tooltip title={t("tiepDon.xoaDichVu")} placement="bottom">
                    <Icon
                      className="ic-action"
                      component={SVG.IcDelete}
                      onClick={() => onDelete(item)}
                    ></Icon>
                  </Tooltip>
                )}
              </>
            )}
          </div>
        );
      },
    }),
  ];

  const onRow = (record, index) => {
    return {
      onClick: () => {
        if (!isEdit) return;

        if (index !== state.currentIndex) {
          setState({
            currentItem: record,
            currentIndex: index,
          });
        }
      },
    };
  };

  const onChangePage = (page) => {
    const newSearch = { ...dataSearch, page: page - 1 };

    setDataSearch(newSearch);
    onSearchByParams(newSearch);
  };

  const handleSizeChange = (size) => {
    const newSearch = { ...dataSearch, page: 0, size };

    setDataSearch(newSearch);
    onSearchByParams(newSearch);
  };

  const onSelectChange = (selectedRowKeys, data) => {
    setState({
      selectedRowKeys: [...new Set(selectedRowKeys)],
      isCheckedAll: selectedRowKeys.length == dsSuatAn.length,
    });

    updateData({
      listDeleteDv: data,
    });
  };

  const onCheckAll = (e) => {
    setState({
      selectedRowKeys: e.target?.checked
        ? (dsSuatAn || []).map((x) => x.id)
        : [],
      isCheckedAll: e.target?.checked,
    });
  };

  const rowSelection = {
    columnTitle: (
      <HeaderSearch
        title={
          <Checkbox
            onChange={onCheckAll}
            checked={state.isCheckedAll}
          ></Checkbox>
        }
      />
    ),
    columnWidth: 40,
    onChange: onSelectChange,
    selectedRowKeys: state.selectedRowKeys,
  };

  return (
    <Main noPadding={true}>
      <div className="table-content">
        <TableWrapper
          columns={columns}
          dataSource={state.dataSource || []}
          onRow={onRow}
          rowSelection={isEdit && rowSelection}
          rowKey={(record) => record.id}
          tableName="table_DVNOITRU_DSSuatAn"
          ref={refSettings}
          columnResizable={true}
        />

        <Pagination
          onChange={onChangePage}
          current={dataSearch?.page + 1}
          pageSize={dataSearch?.size}
          listData={dsSuatAn || []}
          total={dataSearch?.totalElements}
          onShowSizeChange={handleSizeChange}
          style={{ flex: 1, justifyContent: "flex-end" }}
        />
      </div>

      <ChinhSuaSuatAn
        ref={refSuaThongTin}
        isReadonlyDvNoiTru={isReadonlyDvNoiTru}
      />
    </Main>
  );
};

export default forwardRef(DSSuatAn);
