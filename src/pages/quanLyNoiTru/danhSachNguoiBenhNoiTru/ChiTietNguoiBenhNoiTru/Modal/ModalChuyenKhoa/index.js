import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useState,
  useRef,
  useMemo,
} from "react";
import { Col, Form, Input, message, Row } from "antd";
import { Main } from "./styled";
import { useSelector, useDispatch } from "react-redux";
import {
  firstLetterWordUpperCase,
  formatDecimal,
  isArray,
  parseListConfig,
} from "utils/index";
import {
  ModalTemplate,
  Select,
  Button,
  SelectLoadMore,
  DateTimePicker,
  Checkbox,
  AlertMessage,
} from "components";
import moment from "moment";
import { orderBy } from "lodash";
import { useHistory } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";

import {
  useEnum,
  useStore,
  useThietLap,
  useCache,
  useLoading,
  useConfirm,
} from "hooks";
import {
  ENUM,
  THIET_LAP_CHUNG,
  LOAI_DICH_VU,
  CACHE_KEY,
  ROLES,
  TRANG_THAI_THE_BAO_HIEM,
  DOI_TUONG,
  DS_TINH_CHAT_KHOA,
} from "constants/index";
import { useTranslation } from "react-i18next";
import dichVuKyThuatProvider from "data-access/categories/dm-dv-ky-thuat-provider";
import ModalChonDvGoiPttt from "../ModalChonDvGoiPttt";
import ModalBoSungPhong from "../ModalBoSungPhong";
import { SVG } from "assets";
import { checkRole } from "lib-utils/role-utils";
import { refConfirm } from "app";
import { toSafePromise } from "lib-utils";
import nbDvKyThuatProvider from "data-access/nb-dv-ky-thuat-provider";

const mapData = (i) => ({
  value: i.dichVuId,
  label: `${i.ten} (${formatDecimal(i.giaKhongBaoHiem)}|BH: ${formatDecimal(
    i.giaBaoHiem
  )}|${t("quanLyNoiTru.phuThu")}: ${formatDecimal(i.giaPhuThu)})`,
  ma: i.ma,
  ten: i.ten,
  dsPhongThucHien: i.dsPhongThucHien,
});

const ModalChuyenKhoa = (props, ref) => {
  const history = useHistory();
  const [form] = Form.useForm();
  const { onKiemTraHoSo } = props;
  const refModal = useRef(null);
  const refModalChonDvGoiPttt = useRef(null);
  const refModalBoSungPhong = useRef(null);
  const { showLoading, hideLoading } = useLoading();
  // const [minThoiGian, setMinThoiGian] = useState(null);
  const [listGioiTinh] = useEnum(ENUM.GIOI_TINH);
  const [listTrangThaiTheBhyt] = useEnum(ENUM.TRANG_THAI_THE_BAO_HIEM);
  const { t } = useTranslation();
  const { showConfirm } = useConfirm();
  const [state, _setState] = useState({
    show: false,
    isMuonNb: false,
    isKhoaPttt: false,
    requiredGoiMo: true,
    existDvPttt: false,
    selectedDv: null,
    selectedGoi: null,
    selectedDsPhongThucHien: [],
    isShowTraGiuongTuChon: false,
  });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };
  const { listDataTongHop } = useSelector((state) => state.khoa);
  const { listToDieuTri } = useSelector((state) => state.toDieuTri);

  const chiTietNguoiBenhNoiTru = useStore(
    "danhSachNguoiBenhNoiTru.chiTietNguoiBenhNoiTru",
    {}
  );
  const listAllPhieuThuChotDot = useStore(
    "dsPhieuThuChotDotDieuTri.listAllPhieuThuChotDot",
    []
  );
  const thongTinCoBan = useStore("nbDotDieuTri.thongTinCoBan");
  const authId = useStore("auth.auth.id", null);
  const [khoaLamViec, , loadFinish] = useCache(
    authId,
    CACHE_KEY.DATA_KHOA_LAM_VIEC,
    null,
    false
  );
  const {
    khoa: { getListKhoaTongHop },
    nbChuyenKhoa: { chuyenKhoa, getDsPttt },
    danhSachNguoiBenhNoiTru: { nbMuonNb },
    toDieuTri: { getToDieuTri },
    noiTruPhongGiuong: { onSearch: getDsPhongGiuong },
  } = useDispatch();

  const [dataBAT_BUOC_LOAI_HINH_THANH_TOAN] = useThietLap(
    THIET_LAP_CHUNG.BAT_BUOC_LOAI_HINH_THANH_TOAN
  );
  const [dataMAC_DINH_DOI_LOAI_BENH_AN_KHI_CHUYEN_KHOA, loadFinishThietLap] =
    useThietLap(THIET_LAP_CHUNG.MAC_DINH_DOI_LOAI_BENH_AN_KHI_CHUYEN_KHOA);
  const [
    dataHIEN_THI_CHECK_BOX_KHONG_TU_DONG_TINH_LAI_NGAY_GIUONG,
    loadFinishThietLapTinhGiuong,
  ] = useThietLap(
    THIET_LAP_CHUNG.HIEN_THI_CHECK_BOX_KHONG_TU_DONG_TINH_LAI_NGAY_GIUONG
  );
  const [
    dataCHUYEN_KHOA_RA_VIEN_KHONG_TINH_LAI_GIUONG,
    loadFinishThietLapChuyenKhoa,
  ] = useThietLap(THIET_LAP_CHUNG.CHUYEN_KHOA_RA_VIEN_KHONG_TINH_LAI_GIUONG);
  const [
    dataCANH_BAO_NB_CHUA_KE_DV_PT_YC_KHI_CHUYEN_KHOA,
    loadFinishThietLapCanhBao,
  ] = useThietLap(THIET_LAP_CHUNG.CANH_BAO_NB_CHUA_KE_DV_PT_YC_KHI_CHUYEN_KHOA);
  const [dataKHOA_DE, loadFinishKhoaDe] = useThietLap(THIET_LAP_CHUNG.KHOA_DE);

  const isChotDotDieuTri = useMemo(() => {
    if (
      !Array.isArray(listAllPhieuThuChotDot) ||
      listAllPhieuThuChotDot.length === 0 ||
      !loadFinishThietLapChuyenKhoa
    ) {
      return false;
    }

    return (
      listAllPhieuThuChotDot.some((item) => !!item?.chotDotDieuTriId) &&
      dataCHUYEN_KHOA_RA_VIEN_KHONG_TINH_LAI_GIUONG?.eval()
    );
  }, [
    listAllPhieuThuChotDot,
    dataCHUYEN_KHOA_RA_VIEN_KHONG_TINH_LAI_GIUONG,
    loadFinishThietLapChuyenKhoa,
  ]);

  const { isCanhBaoChuaKeDvPttt, maNhomDvCap2, soTienTamUng } = useMemo(() => {
    let obj = {
      isCanhBaoChuaKeDvPttt: false,
      maNhomDvCap2: null,
      soTienTamUng: 0,
    };
    if (!dataCANH_BAO_NB_CHUA_KE_DV_PT_YC_KHI_CHUYEN_KHOA) {
      return obj;
    }
    let listThietLap =
      dataCANH_BAO_NB_CHUA_KE_DV_PT_YC_KHI_CHUYEN_KHOA?.split("/");
    if (isArray(listThietLap, 3)) {
      obj.isCanhBaoChuaKeDvPttt = listThietLap[0]?.toLowerCase() === "true";
      obj.maNhomDvCap2 = listThietLap[1];
      obj.soTienTamUng = listThietLap[2] ? Number(listThietLap[2]) : 0;
    }
    return obj;
  }, [dataCANH_BAO_NB_CHUA_KE_DV_PT_YC_KHI_CHUYEN_KHOA]);

  const { data: listAllDichVuKyThuat } = useQuery({
    queryKey: [
      "danhSachTatCaDichVuKyThuatCuaNguoiBenh",
      chiTietNguoiBenhNoiTru?.id,
    ],
    queryFn: () => {
      return nbDvKyThuatProvider.getDsDichVu({
        nbDotDieuTriId: chiTietNguoiBenhNoiTru?.id,
        page: 0,
        size: 9999,
        dsTrangThaiHoan: [0, 10, 20, 40],
      });
    },
    enabled:
      !!isCanhBaoChuaKeDvPttt && !!state.show && !!chiTietNguoiBenhNoiTru?.id,
    select: (res) => res?.data || [],
  });

  useEffect(() => {
    if (state.show) {
      getListKhoaTongHop({
        page: "",
        size: "",
        active: true,
        dsTinhChatKhoa: 70,
      });
    }
  }, [state.show]);

  useImperativeHandle(ref, () => ({
    show: () => {
      setState({
        show: true,
        selectedDv: null,
        selectedGoi: null,
        isMuonNb: false,
      });

      //lấy thông tin phẫu thuật thủ thuật
      getDsPttt({
        nbDotDieuTriId: chiTietNguoiBenhNoiTru.id,
        khoaChiDinhId: chiTietNguoiBenhNoiTru.khoaNbId,
        dsTrangThai: [25],
        khongThucHien: false,
        phauThuat: true,
      }).then((res) => {
        setState({
          existDvPttt: res && res.length > 0 ? true : false,
        });
      });

      if (
        checkRole([
          ROLES["QUAN_LY_NOI_TRU"].HIEN_THI_CHECKBOX_TRA_GIUONG_TU_CHON,
        ])
      ) {
        //lấy ds phòng giường để check xem có giường tự chọn
        getDsPhongGiuong({
          nbDotDieuTriId: chiTietNguoiBenhNoiTru.id,
          sort: "tuThoiGian,asc",
        }).then((res) => {
          //nếu có tồn tại loại giường tự chọn (50)
          if ((res?.data || []).some((x) => x.loai === 50)) {
            setState({ isShowTraGiuongTuChon: true });
            form.setFieldsValue({ traGiuongTuChon: false });
          } else {
            setState({ isShowTraGiuongTuChon: false });
          }
        });
      } else {
        setState({ isShowTraGiuongTuChon: false });
      }
    },
  }));

  useEffect(() => {
    if (state?.show) {
      refModal.current && refModal.current.show();
    } else {
      refModal.current && refModal.current.hide();
    }
  }, [state?.show]);

  useEffect(() => {
    // denThoiGianSdGiuong khác null -> thời gian chuyển lấy trường denThoiGianSdGiuong, k cho người dùng sửa
    // denThoiGianSdGiuong = null -> người dùng phải chọn th.g lớn hơn tuThoiGianSdGiuong
    if (chiTietNguoiBenhNoiTru) {
      const { tuThoiGianSdGiuong, denThoiGianSdGiuong } =
        chiTietNguoiBenhNoiTru;
      // if (!denThoiGianSdGiuong) {
      //   setMinThoiGian(tuThoiGianSdGiuong);
      // } else {
      //   setMinThoiGian(null);
      // }

      form.setFieldsValue({
        tenKhoaNb: chiTietNguoiBenhNoiTru?.tenKhoaNb,
        tuThoiGian: denThoiGianSdGiuong
          ? moment(denThoiGianSdGiuong)
          : moment(),
      });
    }
  }, [chiTietNguoiBenhNoiTru]);

  useEffect(() => {
    if (state.show && loadFinishThietLap && loadFinishThietLapTinhGiuong) {
      form.setFieldsValue({
        doiLoaiBenhAn: !!dataMAC_DINH_DOI_LOAI_BENH_AN_KHI_CHUYEN_KHOA?.eval(),
        khongTinhLaiNgayGiuong:
          !!dataHIEN_THI_CHECK_BOX_KHONG_TU_DONG_TINH_LAI_NGAY_GIUONG?.eval() ||
          isChotDotDieuTri,
      });
    }
  }, [
    state.show,
    dataMAC_DINH_DOI_LOAI_BENH_AN_KHI_CHUYEN_KHOA,
    loadFinishThietLap,
    dataHIEN_THI_CHECK_BOX_KHONG_TU_DONG_TINH_LAI_NGAY_GIUONG,
    loadFinishThietLapTinhGiuong,
    isChotDotDieuTri,
  ]);

  const gioiTinh =
    (listGioiTinh || []).find(
      (item) => item.id === chiTietNguoiBenhNoiTru?.gioiTinh
    ) || {};

  const handleClickBack = () => {
    form.setFieldsValue({
      khoaId: null,
      dichVuId: null,
      tuThoiGian: moment(),
      isMuonNb: false,
      denThoiGian: null,
      doiLoaiBenhAn: false,
    });
    setState({
      show: false,
      isKhoaPttt: false,
      requiredGoiMo: true,
      selectedDv: null,
      selectedGoi: null,
      selectedDsPhongThucHien: [],
    });
  };

  const handleClickNext = () => {
    form.submit();
  };

  const onHanldeSubmit = (values, boQuaKiemTraThoiGianTaiKhoa = false) => {
    const {
      khoaId,
      dichVuId,
      isMuonNb,
      doiLoaiBenhAn,
      denThoiGian,
      loaiHinhThanhToan,
      khongTinhLaiNgayGiuong,
      traGiuongTuChon,
    } = values;
    const {
      selectedDv,
      selectedGoi,
      selectedDsPhongThucHien,
      isShowTraGiuongTuChon,
    } = state;

    if (moment(denThoiGian) < moment())
      return message.error(
        t("quanLyNoiTru.thoiGianMuonNbPhaiLonHonThoiGianHienTai")
      );

    const onSubmit = (phongId) => {
      const payload = {
        khoaId,
        tuKhoaId: chiTietNguoiBenhNoiTru.khoaNbId,
        nbDotDieuTriId: chiTietNguoiBenhNoiTru.id,
        tuThoiGian: moment(values.tuThoiGian).format("YYYY-MM-DD HH:mm:ss"),
        loai: 20,
        loaiHinhThanhToan,
        doiLoaiBenhAn,
        goiPtTt: selectedGoi
          ? {
              nbDotDieuTriId: chiTietNguoiBenhNoiTru.id,
              nbDichVu: {
                dichVuId: selectedGoi.id,
                chiDinhTuLoaiDichVu: 210,
              },
              dsCdhaTdcnPtTt: [
                {
                  nbDichVu: {
                    dichVuId: selectedDv.dichVuId,
                    chiDinhTuLoaiDichVu: 210,
                    soLuong: 1,
                  },
                  nbDotDieuTriId: chiTietNguoiBenhNoiTru.id,
                  nbDvKyThuat: { phongThucHienId: selectedGoi.phongThucHienId },
                },
              ],
            }
          : undefined,
        cdhaTdcnPtTt: dichVuId
          ? {
              nbDichVu: {
                dichVuId: dichVuId,
                chiDinhTuLoaiDichVu: 210,
                soLuong: 1,
              },
              nbDotDieuTriId: chiTietNguoiBenhNoiTru.id,
              nbDvKyThuat: {
                phongThucHienId: phongId,
              },
            }
          : undefined,
        ...(khongTinhLaiNgayGiuong &&
        checkRole([
          ROLES["QUAN_LY_NOI_TRU"]
            .HIEN_THI_CHECK_BOX_KHONG_TU_DONG_TINH_LAI_NGAY_GIUONG,
        ])
          ? {
              chotGiuong: true,
            }
          : {}),
        ...(isShowTraGiuongTuChon ? { traGiuongTuChon } : {}),
        ...(boQuaKiemTraThoiGianTaiKhoa && {
          boQuaKiemTraThoiGianTaiKhoa: true,
        }),
      };
      const onChuyenKhoa = async () => {
        showLoading();
        try {
          const [e, s] = await toSafePromise(chuyenKhoa(payload));
          if (e) {
            if (e.code == 7987) {
              onKiemTraHoSo?.({ thongTinHoSo: e.data });
            }
            if (e?.code === 1024) {
              showConfirm(
                {
                  title: t("common.thongBao"),
                  content: e?.message,
                  cancelText: t("common.huy"),
                  okText: t("common.dongY"),
                  showImg: false,
                  showBtnOk: true,
                  showBtnCancel: true,
                  typeModal: "warning",
                },
                () => {
                  onHanldeSubmit(values, true); //boQuaKiemTraThoiGianTaiKhoa: true
                },
                () => {
                  handleClickBack();
                }
              );
            }
            return;
          }
          if (isMuonNb) {
            await nbMuonNb({
              nbDotDieuTriId: chiTietNguoiBenhNoiTru.id,
              denKhoaId: chiTietNguoiBenhNoiTru.khoaNbId,
              denThoiGian:
                denThoiGian &&
                moment(denThoiGian).format("YYYY-MM-DD HH:mm:ss"),
            });
          }
          setTimeout(() => {
            history.go();
          }, 500);
        } catch (err) {
          console.log("err", err);
        } finally {
          hideLoading();
        }
        handleClickBack();
      };
      let _isCanhBaoChuaKeDvPttt = false;
      if (isCanhBaoChuaKeDvPttt) {
        const hasMaNhomDVCap2 = listAllDichVuKyThuat?.some(
          (item) => item.maNhomDichVuCap2 === maNhomDvCap2
        );
        const hasTienTamUng = thongTinCoBan?.tienTamUng >= soTienTamUng;
        const khoaChuyenDi = listDataTongHop.find(
          (item) => item.id === payload?.tuKhoaId
        );
        let hasKhoa =
          parseListConfig(dataKHOA_DE).includes(khoaChuyenDi?.ma) ||
          khoaChuyenDi?.dsTinhChatKhoa?.includes(DS_TINH_CHAT_KHOA.PHAU_THUAT);
        _isCanhBaoChuaKeDvPttt = hasMaNhomDVCap2 && hasKhoa && hasTienTamUng;
      }
      if (
        chiTietNguoiBenhNoiTru?.doiTuong == DOI_TUONG.BAO_HIEM &&
        ![
          TRANG_THAI_THE_BAO_HIEM.THE_DUNG,
          TRANG_THAI_THE_BAO_HIEM.DA_DUYET_THE,
        ].includes(chiTietNguoiBenhNoiTru?.trangThaiTheBhyt)
      ) {
        if (
          checkRole([ROLES.QUAN_LY_NOI_TRU.CHUYEN_KHOA_TRUOC_KHI_DUYET_BH_SAI])
        ) {
          refConfirm.current?.show(
            {
              title: t("common.thongBao"),
              content: `${t(
                "quanLyNoiTru.trangThaiTheBHYTSaiBanCoMuonChuyenKhoa",
                {
                  trangThai:
                    listTrangThaiTheBhyt?.find(
                      (item) =>
                        item.id == chiTietNguoiBenhNoiTru?.trangThaiTheBhyt
                    )?.ten || "",
                }
              )}`,
              cancelText: t("common.huy"),
              okText: t("common.dongY"),
              classNameOkText: "button-warning",
              showBtnOk: true,
              typeModal: "warning",
            },
            () => {
              onChuyenKhoa();
            }
          );
        } else {
          message.error(
            t("quanLyNoiTru.trangThaiTheBHYTSaiVuiLongKiemTraLai", {
              trangThai:
                listTrangThaiTheBhyt?.find(
                  (item) => item.id == chiTietNguoiBenhNoiTru?.trangThaiTheBhyt
                )?.ten || "",
            })
          );
        }
      } else if (_isCanhBaoChuaKeDvPttt) {
        refConfirm.current?.show(
          {
            title: t("common.thongBao"),
            content: `${t(
              "quanLyNoiTru.canKiemTraNbSuDungDvPtttTruocKhiChuyenKhoa"
            )}`,
            cancelText: t("common.quayLai"),
            okText: t("common.tiepTuc"),
            classNameOkText: "button-warning",
            showBtnOk: true,
            typeModal: "warning",
          },
          () => {
            onChuyenKhoa();
          }
        );
      } else {
        onChuyenKhoa();
      }
    };

    if (!dichVuId) {
      onSubmit();
    } else {
      if (selectedDsPhongThucHien && selectedDsPhongThucHien.length != 1) {
        refModalBoSungPhong.current &&
          refModalBoSungPhong.current.show(selectedDv, (phongId) => {
            onSubmit(phongId);
          });
      } else {
        onSubmit(state.selectedDsPhongThucHien[0].phongId);
      }
    }
  };

  useEffect(() => {
    loadFinish &&
      khoaLamViec &&
      chiTietNguoiBenhNoiTru?.id &&
      getToDieuTri({
        nbDotDieuTriId: chiTietNguoiBenhNoiTru.id,
        dsKhoaChiDinhId: khoaLamViec.id,
      });
  }, [loadFinish, khoaLamViec, chiTietNguoiBenhNoiTru?.id]);

  const ngayThucHienMoiNhat = useMemo(() => {
    if (!listToDieuTri || listToDieuTri.length == 0) return;
    const listToDieuTriOrder = orderBy(
      listToDieuTri || [],
      "thoiGianYLenh",
      "asc"
    );
    return listToDieuTriOrder[0].thoiGianYLenh
      ? moment(listToDieuTriOrder[0].thoiGianYLenh).format("YYYY-MM-DD")
      : null;
  }, [listToDieuTri]);

  const dsLoaiHinhTT = useMemo(() => {
    const rs =
      !!state?.listDVKSK?.length &&
      state?.listDVKSK.find((o) => o.dichVuId === state?.dichVuId)
        ?.dsLoaiHinhThanhToan;

    return rs || [];
  }, [state?.listDVKSK, state?.dichVuId]);

  useEffect(() => {
    const dsTTMacDinh = dsLoaiHinhTT.filter((o) => o.macDinh);
    const ttMacDinh = dsTTMacDinh?.length === 1;
    ttMacDinh && form.setFieldsValue({ loaiHinhThanhToan: dsTTMacDinh[0].id });
  }, [dsLoaiHinhTT]);

  const onShowGoiPttt = () => {
    refModalChonDvGoiPttt.current &&
      refModalChonDvGoiPttt.current.show(
        {
          nbDotDieuTriId: chiTietNguoiBenhNoiTru.id,
        },
        (data) => {
          const { selectedDv, selectedGoi } = data || {};
          form.setFieldsValue({
            tenGoiMo: selectedGoi?.dichVu?.ten || "",
            tenDichVu: `${selectedDv.ten} (${formatDecimal(
              selectedDv.giaKhongBaoHiem
            )}|BH: ${formatDecimal(selectedDv.giaBaoHiem)}|${t(
              "quanLyNoiTru.phuThu"
            )}: ${formatDecimal(selectedDv.giaPhuThu)})`,
          });
          setState({
            selectedDv,
            selectedGoi,
          });
        }
      );
  };

  const onValuesChange = (changedValues, allValues) => {
    if (changedValues.hasOwnProperty("khoaId")) {
      const selectedKhoa = listDataTongHop.find(
        (x) => x.id == changedValues.khoaId
      );
      setState({
        isKhoaPttt:
          !state.existDvPttt &&
          (selectedKhoa?.dsTinhChatKhoa || []).includes(30),
      });
    }

    if (changedValues.hasOwnProperty("dichVuId")) {
      setState({
        requiredGoiMo: !changedValues.dichVuId,
        dichVuId: changedValues.dichVuId,
      });
    }

    if (changedValues.hasOwnProperty("isMuonNb")) {
      setState({
        isMuonNb: changedValues.isMuonNb,
      });
    }

    if (changedValues.hasOwnProperty("tuThoiGian")) {
      setState({
        tuThoiGian: changedValues.tuThoiGian
          ? moment(changedValues.tuThoiGian)
          : moment(),
      });
    }
  };

  const getValueApi = (listDVKSK = []) => setState({ listDVKSK });

  const onChangeDichVu = (e, data) => {
    setState({
      selectedDsPhongThucHien: data?.dsPhongThucHien,
      selectedDv: data,
    });
  };
  const onChangeGoiMo = (e, data) => {
    !e?.target?.value &&
      setState({
        selectedDv: null,
        selectedGoi: null,
      });
  };

  const enableSave = loadFinishKhoaDe && loadFinishThietLapCanhBao;

  return (
    <ModalTemplate
      width={"60%"}
      ref={refModal}
      title={t("pttt.chuyenKhoa")}
      onCancel={handleClickBack}
      rightTitle={
        <>
          <span className="font-color">
            {firstLetterWordUpperCase(chiTietNguoiBenhNoiTru?.tenNb)}
          </span>
          {gioiTinh.ten && (
            <span className="normal-weight"> - {gioiTinh?.ten} </span>
          )}

          {chiTietNguoiBenhNoiTru.tuoi2 && (
            <span className="normal-weight">
              - {chiTietNguoiBenhNoiTru?.tuoi2}{" "}
            </span>
          )}
        </>
      }
      actionLeft={<Button.QuayLai onClick={handleClickBack} />}
      actionRight={
        <Button
          type="primary"
          minWidth={100}
          onClick={handleClickNext}
          rightIcon={<SVG.IcSave />}
          disabled={!enableSave}
        >
          {t("common.luu")}
        </Button>
      }
    >
      <Main>
        <AlertMessage
          keyThietLap={THIET_LAP_CHUNG.TEN_CANH_BAO_POPUP_CHUYEN_KHOA}
        />
        <Form
          form={form}
          layout="vertical"
          onFinish={onHanldeSubmit}
          onValuesChange={onValuesChange}
        >
          <Form.Item
            label={t("pttt.khoaChuyenDen")}
            name="khoaId"
            rules={[
              {
                required: true,
                message: `${t("pttt.vuiLongChonKhoaChuyenDen")}!`,
              },
            ]}
          >
            <Select
              data={(listDataTongHop || []).filter(
                (x) => x.id !== chiTietNguoiBenhNoiTru?.khoaNbId
              )}
              placeholder={t("pttt.chonKhoaChuyenDen")}
            />
          </Form.Item>
          <Form.Item
            label={t("pttt.thoiGianChuyen")}
            name="tuThoiGian"
            rules={[
              {
                required: true,
                message: `${t("quanLyNoiTru.vuiLongChonThoiGianChuyenHopLe")}!`,
              },
            ]}
          >
            <DateTimePicker
              showTime
              format={"DD/MM/YYYY HH:mm:ss"}
              // disabled={!minThoiGian}
              // disabledDate={(date) => moment(date).isBefore(minThoiGian)}
            ></DateTimePicker>
          </Form.Item>

          {state.isKhoaPttt &&
            (state.selectedGoi ? (
              <Form.Item label={t("common.tenDichVu")} name="tenDichVu">
                <Input prefix={<SVG.IcSearch />} />
              </Form.Item>
            ) : (
              <Form.Item label={t("common.tenDichVu")} name="dichVuId">
                <SelectLoadMore
                  api={dichVuKyThuatProvider.searchAll}
                  getValueApi={getValueApi}
                  mapData={mapData}
                  addParam={{
                    active: true,
                    loaiDichVu: LOAI_DICH_VU.PHAU_THUAT_THU_THUAT,
                    loaiDoiTuongId: chiTietNguoiBenhNoiTru?.loaiDoiTuongId,
                    khoaNbId: chiTietNguoiBenhNoiTru?.khoaNbId,
                    ngayThucHien: ngayThucHienMoiNhat,
                  }}
                  onChange={onChangeDichVu}
                  keySearch={"ten"}
                  className="input-filter"
                  placeholder={t("common.timTenDichVu")}
                  prefix={<SVG.IcSearch />}
                />
              </Form.Item>
            ))}

          {state.isKhoaPttt && (
            <>
              <Form.Item label={t("pttt.tenGoiMo")} name="tenGoiMo">
                <Input
                  prefix={<SVG.IcSearch />}
                  onClick={onShowGoiPttt}
                  onChange={onChangeGoiMo}
                  placeholder={t("pttt.timTenGoiMo")}
                />
              </Form.Item>
              <Form.Item
                label={t("quanLyNoiTru.dvNoiTru.loaiHinhThanhToan")}
                name="loaiHinhThanhToan"
                rules={
                  dataBAT_BUOC_LOAI_HINH_THANH_TOAN.toLowerCase() === "true" &&
                  !!state.selectedDv && [
                    {
                      required: true,
                      message: `${t(
                        "quanLyNoiTru.vuiLongChonLoaiHinhThanhToan"
                      )}!`,
                    },
                  ]
                }
              >
                <Select
                  data={dsLoaiHinhTT.map((o) => ({
                    ...o,
                    ten: o.tenLoaiHinhThanhToan,
                  }))}
                  placeholder={t("danhMuc.chonLoaiHinhThanhToan")}
                />
              </Form.Item>
            </>
          )}
          <Form.Item label={t("pttt.khoaChuyenDi")} name="tenKhoaNb">
            <Input disabled></Input>
          </Form.Item>

          {/* mượn người bệnh */}
          <div className="flex gap-16">
            <Form.Item valuePropName="checked" name="isMuonNb">
              <Checkbox>{t("quanLyNoiTru.muonNb")}</Checkbox>
            </Form.Item>

            <Form.Item valuePropName="checked" name="doiLoaiBenhAn">
              <Checkbox>
                {t("quanLyNoiTru.doiLoaiBATheoKhoaChuyenDen")}
              </Checkbox>
            </Form.Item>

            {checkRole([
              ROLES["QUAN_LY_NOI_TRU"]
                .HIEN_THI_CHECK_BOX_KHONG_TU_DONG_TINH_LAI_NGAY_GIUONG,
            ]) ? (
              <Form.Item valuePropName="checked" name="khongTinhLaiNgayGiuong">
                <Checkbox softDisabled={isChotDotDieuTri}>
                  {t("quanLyNoiTru.khongTinhLaiNgayGiuong")}
                </Checkbox>
              </Form.Item>
            ) : null}

            {state.isShowTraGiuongTuChon && (
              <Form.Item valuePropName="checked" name="traGiuongTuChon">
                <Checkbox>{t("quanLyNoiTru.traGiuongTuChon")}</Checkbox>
              </Form.Item>
            )}
          </div>

          <div className="flex gap-16"></div>

          {state.isMuonNb && (
            <Form.Item
              label={t("quanLyNoiTru.muonNbDen")}
              name="denThoiGian"
              rules={[
                {
                  required: true,
                  message: t("quanLyNoiTru.vuiLongChonThoiGian"),
                },
              ]}
            >
              <DateTimePicker
                // disabledDate={(current) =>
                //   current && current < moment().endOf("day")
                // }
                showTime={{ format: "HH:mm:ss" }}
                compareTime={(a, b) => a < b}
                pointTime={moment().endOf("minute")}
                placeholder={t("quanLyNoiTru.muonNbDen")}
              />
            </Form.Item>
          )}
        </Form>
      </Main>

      <ModalChonDvGoiPttt ref={refModalChonDvGoiPttt} />
      <ModalBoSungPhong ref={refModalBoSungPhong} />
    </ModalTemplate>
  );
};

//current && current < moment().endOf('day');

export default forwardRef(ModalChuyenKhoa);
