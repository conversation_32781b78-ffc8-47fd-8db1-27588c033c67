import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from "react";
import { Form } from "antd";
import { Main } from "./styled";
import { useDispatch } from "react-redux";
import { But<PERSON>, DateTimePicker, ModalTemplate } from "components";
import moment from "moment";
import { useTranslation } from "react-i18next";
import { useEnum, useStore } from "hooks";
import { ENUM } from "constants/index";
import { firstLetterWordUpperCase } from "utils";
import { SVG } from "assets";

const ModalDuKienRaVien = (props, ref) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const refModal = useRef(null);
  const [listgioiTinh] = useEnum(ENUM.GIOI_TINH);
  const chiTietNguoiBenhNoiTru = useStore(
    "danhSachNguoiBenhNoiTru.chiTietNguoiBenhNoiTru"
  );
  const {
    danhSachNguoiBenhNoiTru: { duKienRaVien, getNbNoiTruById },
    noiTruPhongGiuong: { onSearch: getDsPhongGiuong },
  } = useDispatch();
  const [state, _setState] = useState({ show: false });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  useImperativeHandle(ref, () => ({
    show: ({ thoiGianRaVien }) => {
      setState({ show: true });
      form.setFieldsValue({ thoiGianRaVien: thoiGianRaVien });
    },
  }));

  useEffect(() => {
    if (state?.show) {
      refModal.current && refModal.current.show();
    } else {
      refModal.current && refModal.current.hide();
    }
  }, [state?.show]);

  useEffect(() => {}, []);
  const gioiTinh =
    (listgioiTinh || []).find(
      (item) => item.id === chiTietNguoiBenhNoiTru?.gioiTinh
    ) || {};

  const onHanldeSubmit = (values) => {
    const payload = {
      id: chiTietNguoiBenhNoiTru.id,
      thoiGianRaVien:
        values.thoiGianRaVien &&
        moment(values.thoiGianRaVien).format("YYYY-MM-DD HH:mm:ss"),
    };
    duKienRaVien(payload).then(() => {
      getNbNoiTruById(chiTietNguoiBenhNoiTru.id);
      getDsPhongGiuong({
        nbDotDieuTriId: chiTietNguoiBenhNoiTru.id,
        sort: "tuThoiGian,desc",
      });
      onCancel();
    });
  };

  const onCancel = () => {
    setState({ show: false });
    form.resetFields();
  };
  const onSubmit = () => {
    form.submit();
  };

  let tuoi =
    chiTietNguoiBenhNoiTru?.thangTuoi > 36 || chiTietNguoiBenhNoiTru?.tuoi
      ? `${chiTietNguoiBenhNoiTru?.tuoi} ${t("common.tuoi")}`
      : `${chiTietNguoiBenhNoiTru?.thangTuoi} ${t("common.thang")}`;
  return (
    <ModalTemplate
      width={500}
      ref={refModal}
      title={t("quanLyNoiTru.duKienRaVien")}
      rightTitle={
        <>
          <span className="font-color">
            {firstLetterWordUpperCase(chiTietNguoiBenhNoiTru?.tenNb)}
          </span>
          {gioiTinh.ten && (
            <span className="normal-weight"> - {gioiTinh.ten} </span>
          )}

          {tuoi && <span className="normal-weight">- {tuoi}</span>}
        </>
      }
      onCancel={onCancel}
      actionLeft={<Button.QuayLai onClick={onCancel} />}
      actionRight={
        <Button
          type="primary"
          minWidth={100}
          onClick={onSubmit}
          rightIcon={<SVG.IcSave />}
        >
          {t("common.luu")}
        </Button>
      }
    >
      <Main>
        <Form
          form={form}
          layout="vertical"
          onFinish={onHanldeSubmit}
          className="form-custom"
        >
          <Form.Item
            label={t("quanLyNoiTru.thoiGianDuKienRaVien")}
            name="thoiGianRaVien"
            rules={[
              {
                required: true,
                message: t("quanLyNoiTru.vuiLongNhapThoiGianDuKienRaVien"),
              },
            ]}
          >
            <DateTimePicker
              placeholder={t("quanLyNoiTru.thoiGianDuKienRaVien")}
              disabledDate={(date) => moment(date).isBefore(moment())}
            />
          </Form.Item>
        </Form>
      </Main>
    </ModalTemplate>
  );
};

export default forwardRef(ModalDuKienRaVien);
