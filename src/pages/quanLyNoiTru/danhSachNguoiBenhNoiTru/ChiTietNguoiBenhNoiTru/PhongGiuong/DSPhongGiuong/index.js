import React, { memo, useEffect, useMemo, useRef, useState } from "react";
import { TableWrapper, HeaderSearch, Select, Tooltip } from "components";
import { Input, Empty } from "antd";
import { Main } from "./styled";
import ModalChiTietGiuong from "./ModalChiTietGiuong";
import ModalChinhSua from "./ModalChinhSua";
import { useDispatch, useSelector } from "react-redux";
import moment from "moment";
import { useConfirm, useEnum, useListAll, useStore } from "hooks";
import {
  ENUM,
  MAN_HINH_PHIEU_IN,
  VI_TRI_PHIEU_IN,
  ROLES,
} from "constants/index";
import { useTranslation } from "react-i18next";
import { SVG } from "assets";
import ModalTraGiuong from "./ModalTraGiuong";
import DropDownPrintRecord from "./DropDownPrintRecord";
import { checkRole } from "lib-utils/role-utils";

const { Setting } = TableWrapper;

const DSPhongGiuong = ({ nbDotDieuTriId, ...props }) => {
  const { showConfirm } = useConfirm();
  const { t } = useTranslation();
  const {
    isReadonly,
    isNbChoTiepNhanVaoKhoa,
    onKiemTraHoSo,
    khoaLamViec,
    isEdit = true,
    isPopup = false,
    khoaNbId,
    isGiuongTuChon = false,
  } = props;
  //ref
  const refModalChiTietGiuong = useRef(null);
  const refModalChinhSua = useRef(null);
  const refSettings = useRef(null);
  const refModalTraGiuong = useRef(null);

  const [state, _setState] = useState({
    dataSortColumn: {},
    listPhieu: [],
    visible: false,
  });
  const { dataSortColumn } = state;
  const setState = (data) => {
    _setState((pre) => ({ ...pre, ...data }));
  };

  const [listAllLoaiGiuong] = useListAll("loaiGiuong", {
    page: "",
    size: "",
    active: true,
  });
  const [listLoaiChuyenKhoa] = useEnum(ENUM.LOAI_CHUYEN_KHOA);
  console.log("listLoaiChuyenKhoa", listLoaiChuyenKhoa);

  const { dsPhongGiuong } = useSelector((state) => state.noiTruPhongGiuong);
  const listAllPhieuThuChotDot = useStore(
    "dsPhieuThuChotDotDieuTri.listAllPhieuThuChotDot",
    []
  );

  const {
    noiTruPhongGiuong: { onSearch, deletePhongGiuong, getPhieuDatGiuong },
    danhSachNguoiBenhNoiTru: { getNbNoiTruById },
  } = useDispatch();

  useEffect(() => {
    if (nbDotDieuTriId) {
      onSearch({ nbDotDieuTriId: nbDotDieuTriId, sort: "tuThoiGian,asc" });
    }
  }, [nbDotDieuTriId]);

  const dataSource = useMemo(() => {
    if (isGiuongTuChon) {
      let _dsGiuongTuChon = (dsPhongGiuong || []).filter(
        (item) => item.loai === 50
      ); //50: giường tự chọn

      //thêm các line dv giường con
      _dsGiuongTuChon = _dsGiuongTuChon.map((item, index) => ({
        ...item,
        index: index + 1,
        ...((item.dsGiuongNguoiNha || []).length > 0
          ? {
              children: (item.dsGiuongNguoiNha || []).map((x) => ({
                ...x,
                isChild: true,
              })),
            }
          : {}),
      }));

      return _dsGiuongTuChon;
    }
    return (dsPhongGiuong || [])
      .filter((item) => item.loai !== 50)
      .map((item, index) => ({ ...item, index: index + 1 }));
  }, [isGiuongTuChon, dsPhongGiuong]);

  //function
  function onViewDetail(item) {
    return () => {
      if (item.isChild) {
        const _nbChuyenKhoaInfo = { ...item, id: item.nbChuyenKhoaId };

        refModalChiTietGiuong.current &&
          refModalChiTietGiuong.current.show(_nbChuyenKhoaInfo, {
            isGiuongNguoiNha: true,
            giuongNguoiNhaInfo: item,
          });
      } else {
        refModalChiTietGiuong.current &&
          refModalChiTietGiuong.current.show(item);
      }
    };
  }

  function onEdit(item) {
    return () => {
      refModalChinhSua.current && refModalChinhSua.current.show(item);
    };
  }

  const onClickSort = (key, value) => {
    const sort = { ...dataSortColumn, [key]: value };
    setState({ dataSortColumn: sort });
  };

  function refreshList() {
    if (nbDotDieuTriId) {
      onSearch({ nbDotDieuTriId: nbDotDieuTriId, sort: "tuThoiGian,asc" });

      getNbNoiTruById(nbDotDieuTriId);
    }
  }

  function onDelete(item) {
    return (e) => {
      e.stopPropagation();

      showConfirm(
        {
          title: t("common.xoaDuLieu"),
          content: `${t("common.banChacChanMuonXoa")}?`,
          cancelText: t("common.quayLai"),
          okText: t("common.dongY"),
          classNameOkText: "button-error",
          showBtnOk: true,
          typeModal: "error",
        },
        () => {
          deletePhongGiuong(item?.id).then(() => {
            refreshList();
          });
        },
        () => {}
      );
    };
  }

  const onSettings = () => {
    refSettings && refSettings.current.settings();
  };

  const renderEmptyTextTable = () => {
    return (
      <div>
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description={<span>{t("common.khongCoDuLieuPhuHop")}</span>}
        />
      </div>
    );
  };

  const onTraGiuong = (item) => () => {
    refModalTraGiuong.current && refModalTraGiuong.current.show(item);
  };

  const hasChotDot = useMemo(() => {
    return (
      listAllPhieuThuChotDot?.some((phieu) => !!phieu.chotDotDieuTriId) || false
    );
  }, [listAllPhieuThuChotDot]);

  const maxThoiGianChotDot = useMemo(() => {
    if (!listAllPhieuThuChotDot?.length) return null;
    return moment(
      Math.max(
        ...listAllPhieuThuChotDot.map((item) =>
          moment(item.thoiGianChotDotDieuTri).valueOf()
        )
      )
    );
  }, [listAllPhieuThuChotDot]);

  //column
  const columns = [
    {
      title: <HeaderSearch title={t("common.stt")} />,
      dataIndex: "index",
      width: "50px",
      align: "center",
      ignore: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("quanLyNoiTru.phong")}
          sort_key="phong"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.phong || 0}
          search={<Input placeholder={t("common.timKiem")} />}
        />
      ),
      width: 120,
      dataIndex: "tenPhong",
      show: true,
      i18Name: "quanLyNoiTru.phong",
    },
    {
      title: (
        <HeaderSearch
          title={t("quanLyNoiTru.phongGiuong.soHieuGiuong")}
          sort_key="soHieuGiuong"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.soHieuGiuong || 0}
          search={<Input placeholder={t("common.timKiem")} />}
        />
      ),
      width: 150,
      dataIndex: "soHieuGiuong",
      show: true,
      i18Name: "quanLyNoiTru.phongGiuong.soHieuGiuong",
      render: (item, list) =>
        list?.loai == 30 ? t("quanLyNoiTru.nghiDieuTri") : item,
    },
    {
      title: (
        <HeaderSearch
          isTitleCenter={true}
          title={t("quanLyNoiTru.phongGiuong.loaiGiuong")}
          sort_key="loaiGiuongId"
          dataSort={dataSortColumn["loaiGiuongId"] || 0}
          onClickSort={onClickSort}
          searchSelect={
            <Select
              placeholder={t("baoCao.chonLoai")}
              data={listAllLoaiGiuong}
              hasAllOption={true}
            />
          }
        />
      ),
      width: 120,
      dataIndex: "loaiGiuongId",
      key: "loaiGiuongId",
      i18Name: "quanLyNoiTru.phongGiuong.loaiGiuong",
      show: true,
      render: (item) => {
        const res = listAllLoaiGiuong.find((el) => el.id === item) || {};
        return res.ten;
      },
    },
    ...(isGiuongTuChon
      ? [
          {
            title: (
              <HeaderSearch
                title={t("quanLyNoiTru.phongGiuong.tenDichVuGiuong")}
                sort_key="tenDvGiuong"
                onClickSort={onClickSort}
                dataSort={dataSortColumn.tenDvGiuong || 0}
                search={<Input placeholder={t("common.timKiem")} />}
              />
            ),
            width: 250,
            dataIndex: "tenDvGiuong",
            show: true,
            i18Name: "quanLyNoiTru.phongGiuong.tenDichVuGiuong",
          },
        ]
      : []),
    {
      title: (
        <HeaderSearch
          title={t("quanLyNoiTru.phongGiuong.khoaNbNam")}
          sort_key="khoaNBNam"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.khoaNBNam || 0}
          search={<Input placeholder={t("common.timKiem")} />}
        />
      ),
      width: 250,
      dataIndex: "tenKhoa",
      show: true,
      i18Name: "quanLyNoiTru.phongGiuong.khoaNbNam",
    },
    {
      title: (
        <HeaderSearch
          title={t("quanLyNoiTru.phongGiuong.namTuNgay")}
          sort_key="namTuNgay"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.namTuNgay || 0}
          search={<Input placeholder={t("common.timKiem")} />}
        />
      ),
      width: 180,
      dataIndex: "tuThoiGian",
      i18Name: "quanLyNoiTru.phongGiuong.namTuNgay",
      show: true,
      render: (field, item, index) => {
        return (
          <div>{field && moment(field).format("DD/MM/YYYY HH:mm:ss")}</div>
        );
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("quanLyNoiTru.phongGiuong.namDenNgay")}
          sort_key="namDenNgay"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.namDenNgay || 0}
          search={<Input placeholder={t("common.timKiem")} />}
        />
      ),
      width: 180,
      dataIndex: "denThoiGian",
      i18Name: "quanLyNoiTru.phongGiuong.namDenNgay",
      show: true,
      render: (field, item, index) => {
        return (
          <div>{field && moment(field).format("DD/MM/YYYY  HH:mm:ss")}</div>
        );
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("quanLyNoiTru.phongGiuong.soNgayGiuong")}
          sort_key="soNgay"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.soNgay || 0}
          search={<Input placeholder={t("common.timKiem")} />}
        />
      ),
      width: 120,
      align: "right",
      dataIndex: "soNgay",
      i18Name: "quanLyNoiTru.phongGiuong.soNgayGiuong",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          isTitleCenter={true}
          title={t("quanLyNoiTru.phongGiuong.loai")}
          sort_key="loai"
          dataSort={dataSortColumn["loai"] || 0}
          onClickSort={onClickSort}
          searchSelect={
            <Select
              placeholder={t("baoCao.chonLoai")}
              data={listLoaiChuyenKhoa}
              hasAllOption={true}
            />
          }
        />
      ),
      width: "120px",
      dataIndex: "loai",
      key: "loai",
      i18Name: "quanLyNoiTru.phongGiuong.loai",
      show: true,
      render: (item) => {
        const res = listLoaiChuyenKhoa.find((el) => el.id === item) || {};
        return res.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          title={
            <>
              {t("common.tienIch")}
              <Setting refTable={refSettings} />
            </>
          }
        />
      ),
      width: "180px",
      align: "center",
      fixed: "right",
      hidden: isNbChoTiepNhanVaoKhoa,
      ignore: true,
      render: (item, list, index) => {
        const tuThoiGian = list.tuThoiGian ? moment(list.tuThoiGian) : null;
        const denThoiGian = list.denThoiGian ? moment(list.denThoiGian) : null;

        const hideEditDelete =
          hasChotDot &&
          !!maxThoiGianChotDot &&
          ((tuThoiGian && tuThoiGian.isBefore(maxThoiGianChotDot)) ||
            (denThoiGian && denThoiGian.isBefore(maxThoiGianChotDot)));

        //Nếu tài khoản không có quyền: 2100306 + không tồn tại line chuyển khoa (20), chuyển giường (40) nào ở sau
        const afterArr = dataSource.slice(index + 1);
        const checkQuyenXoaGiuong =
          checkRole([
            ROLES["QUAN_LY_NOI_TRU"].HIEN_THI_ICON_XOA_LINE_PHONG_GIUONG,
          ]) || afterArr.every((x) => ![20, 40].includes(x.loai));

        return (
          <>
            {!isReadonly && (
              <Tooltip title={t("common.xemChiTiet")}>
                <SVG.IcEye className="ic-action" onClick={onViewDetail(item)} />
              </Tooltip>
            )}

            {!item.isChild && (
              <>
                {isEdit && !isPopup && !hideEditDelete && (
                  <Tooltip title={t("common.chinhSua")}>
                    <SVG.IcEdit className="ic-action" onClick={onEdit(item)} />
                  </Tooltip>
                )}
                {!isReadonly && !hideEditDelete && checkQuyenXoaGiuong && (
                  <Tooltip title={t("common.xoa")}>
                    <SVG.IcDelete
                      className="ic-action"
                      onClick={onDelete(item)}
                    />
                  </Tooltip>
                )}
                {/* giường tự chọn + nằm đến ngày = null */}
                {!isReadonly && isGiuongTuChon && !list?.denThoiGian && (
                  <Tooltip title={t("quanLyNoiTru.traGiuong")}>
                    <SVG.IcHoanDv
                      className="ic-action"
                      onClick={onTraGiuong(list)}
                    />
                  </Tooltip>
                )}
                {isGiuongTuChon && !isReadonly && (
                  <DropDownPrintRecord
                    record={list}
                    nbDotDieuTriId={nbDotDieuTriId}
                    maViTri={VI_TRI_PHIEU_IN.NOI_TRU.IN_GIUONG_TU_CHON}
                    maManHinh={MAN_HINH_PHIEU_IN.NOI_TRU}
                    otherParams={{
                      nbChuyenKhoaId: list?.id,
                      dsTrangThaiHoan: [0, 10],
                    }}
                  />
                )}
              </>
            )}
          </>
        );
      },
    },
  ];

  return (
    <Main>
      <TableWrapper
        ref={refSettings}
        bordered
        columns={columns}
        dataSource={dataSource}
        tableName="table_QLNT_DanhSachPhongGiuong"
        rowKey={(record) => `${record.id}`}
        locale={{
          emptyText: renderEmptyTextTable(),
        }}
      />

      <ModalChiTietGiuong
        ref={refModalChiTietGiuong}
        refreshList={refreshList}
        isReadonly={isReadonly}
        khoaLamViec={khoaLamViec}
        khoaNbId={khoaNbId}
        isGiuongTuChon={isGiuongTuChon}
      />
      <ModalChinhSua
        ref={refModalChinhSua}
        refreshList={refreshList}
        isReadonly={isReadonly}
        onKiemTraHoSo={onKiemTraHoSo}
        khoaLamViec={khoaLamViec}
        khoaNbId={khoaNbId}
        isGiuongTuChon={isGiuongTuChon}
      />
      <ModalTraGiuong
        refreshList={refreshList}
        isReadonly={isReadonly}
        ref={refModalTraGiuong}
      />
    </Main>
  );
};

export default memo(DSPhongGiuong);
