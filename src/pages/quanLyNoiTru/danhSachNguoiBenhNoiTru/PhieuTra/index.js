import React, { useEffect, useMemo, useRef, useState } from "react";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import DanhSach from "./DanhSach";
import ModalTaoPhieuTra from "./ModalTaoPhieuTra";
import { Main, MainPage } from "./styled";
import { Button, KhoaThucHien } from "components";
import { useStore } from "hooks";
import { DS_TINH_CHAT_KHOA } from "constants/index";
import ModalTaoPhieuTraSuatAn from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/PhieuTraSuatAn/ModalTaoPhieu";

const DanhSachPhieuTra = (props) => {
  const { t } = useTranslation();

  const refModalPhieuTra = useRef();
  const refModalTaoPhieuTraSuatAn = useRef();
  const [state, _setState] = useState({ dataSortColumn: {} });
  const {
    phieuNhapXuat: { getListPhieu, updateData },
  } = useDispatch();

  const setState = (data) => {
    _setState((pre) => ({ ...pre, ...data }));
  };
  const clickTaoPhieuTra = () => {
    if (refModalPhieuTra.current) {
      refModalPhieuTra.current.show();
    }
  };
  const khoaLamViec = useStore("phieuNhapXuat.khoaLamViec", {});
  const isNoiTru = window.location.pathname.indexOf("/quan-ly-noi-tru") !== -1;

  const { linkDetail, breadcrumb } = useMemo(() => {
    const { pathname } = window.location;
    const isPttt = pathname.indexOf("/phau-thuat-thu-thuat") !== -1;
    const isCdha = pathname.indexOf("/chan-doan-hinh-anh") !== -1;
    const moduleName = isPttt
      ? "phau-thuat-thu-thuat"
      : isCdha
      ? "chan-doan-hinh-anh"
      : "quan-ly-noi-tru";

    return {
      linkDetail: `/${moduleName}/chi-tiet-phieu-tra/`,
      breadcrumb: [
        {
          link: `/${moduleName}`,
          title: isPttt
            ? t("pttt.quanLyPhauThuatThuThuat")
            : isCdha
            ? t("cdha.cdha")
            : t("quanLyNoiTru.quanLyNoiTru"),
        },
        {
          link: `/${moduleName}/${
            isPttt
              ? "danh-sach-nguoi-benh"
              : isCdha
              ? "thuc-hien-cdha-tdcn"
              : "danh-sach-nguoi-benh-noi-tru"
          }`,
          title: isPttt
            ? t("pttt.danhSachPhauThuatThuThuat")
            : isCdha
            ? t("cdha.thucHienCdhaTdcn")
            : t("quanLyNoiTru.danhSachNguoiBenhNoiTru"),
        },
        {
          link: `/${moduleName}/danh-sach-phieu-tra`,
          title: t("quanLyNoiTru.danhSachPhieuTra"),
        },
      ],
    };
  }, [window.location]);

  useEffect(() => {
    if (state.khoaChiDinhId) {
      getListPhieu({
        page: 0,
        size: 10,
        dsLoaiNhapXuat: [70, 71, 72],
        khoaChiDinhId: state.khoaChiDinhId,
      });
    }
  }, [state.khoaChiDinhId]);

  const onChangeKhoa = (khoa) => {
    setState({ khoaChiDinhId: khoa?.id });
    updateData({ khoaLamViec: khoa });
  };

  return (
    <MainPage
      breadcrumb={breadcrumb}
      title={
        <div className="title">
          <label>{t("quanLyNoiTru.danhSachPhieuTra")}</label>
          <KhoaThucHien
            dsTinhChatKhoa={isNoiTru ? DS_TINH_CHAT_KHOA.NOI_TRU : null}
            // cacheKey={"DATA_KHOA_LAM_VIEC_PHIEU_TRA"}
            onChange={onChangeKhoa}
            justifyContent={"left"}
          />
        </div>
      }
      titleRight={
        <Button
          className="btn_new"
          type={"success"}
          iconHeight={20}
          onClick={clickTaoPhieuTra}
        >
          <span>{t("quanLyNoiTru.taoPhieuTra")}</span>
        </Button>
      }
    >
      <Main noPadding={true}>
        <DanhSach linkDetail={linkDetail} khoaChiDinhId={state.khoaChiDinhId} />
        <ModalTaoPhieuTra
          khoaLamViec={{ id: khoaLamViec?.id }}
          ref={refModalPhieuTra}
          disabledLoaiNhapXuat={true}
          refModalTaoPhieuTraSuatAn={refModalTaoPhieuTraSuatAn}
        />
        <ModalTaoPhieuTraSuatAn
          ref={refModalTaoPhieuTraSuatAn}
          khoaLamViecId={khoaLamViec?.id}
        />
      </Main>
    </MainPage>
  );
};

export default DanhSachPhieuTra;
