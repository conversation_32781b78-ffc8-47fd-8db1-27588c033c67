import { Input } from "antd";
import {
  <PERSON>box,
  <PERSON><PERSON>icker,
  HeaderSearch,
  Select,
  TableWrapper,
  Button,
  Pagination,
  SelectLoadMore,
  InputTimeout,
} from "components";
import React, { useEffect, useMemo, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { useHistory } from "react-router-dom";
import { useEnum, useListAll } from "hooks";
import moment from "moment";
import dmKhoProvider from "data-access/categories/dm-kho-provider";
import dmKhoaProvider from "data-access/categories/dm-khoa-provider";
import { ENUM, LOAI_CHI_DINH } from "constants/index";
import { SVG } from "assets";
import { lowerFirst } from "lodash";

const addParam = { active: true };

const DanhSach = ({
  linkDetail,
  khoaChiDinhId,
  isModal = false,
  onGoToDetail = () => {},
  onPrintPhieu = () => {},
}) => {
  const refTimeOut = useRef();
  const [state, _setState] = useState({
    dataSortColumn: {},
    listCheckId: [],
    param: {
      page: 0,
      size: 10,
      dsLoaiNhapXuat: [70, 71, 72],
    },
  });
  const { dataSortColumn } = state;
  const setState = (data) => {
    _setState((pre) => ({ ...pre, ...data }));
  };
  const { t } = useTranslation();
  const { dsPhieuNhapXuat, totalElements } = useSelector(
    (state) => state.phieuNhapXuat
  );
  const [listTrangThaiPhieuNhapXuat] = useEnum(ENUM.TRANG_THAI_PHIEU_NHAP_XUAT);
  const [listLoaiDichVu] = useEnum(ENUM.LOAI_DICH_VU);
  const [listTrangThaiNb] = useEnum(ENUM.TRANG_THAI_NB);
  const [listLoaiChiDinh] = useEnum(ENUM.LOAI_CHI_DINH);
  const [listLoaiNhapXuat] = useEnum(ENUM.LOAI_NHAP_XUAT);
  const [listAllMaPhieuLinh] = useListAll("maPhieuLinh", {}, true);

  const {
    phieuNhapXuat: { getListPhieu },
  } = useDispatch();

  const refSettings = useRef(null);

  const onClickSort = (key, value) => {
    const sort = { ...dataSortColumn, [key]: value };
    setState({ dataSortColumn: sort });
  };

  const onSearchInput = (key) => (e) => {
    let value = { [key]: e };
    if (key == "thoiGianTaoPhieu") {
      value = {
        [`tuThoiGianTaoPhieu`]:
          e instanceof moment ? `${e.format("YYYY-MM-DD")} 00:00:00` : "",
        [`denThoiGianTaoPhieu`]:
          e instanceof moment ? `${e.format("YYYY-MM-DD")} 23:59:59` : "",
      };
    }

    if (key == "thoiGianDuyet") {
      value = {
        [`tuThoiGianDuyet`]:
          e instanceof moment ? `${e.format("YYYY-MM-DD")} 00:00:00` : "",
        [`denThoiGianDuyet`]:
          e instanceof moment ? `${e.format("YYYY-MM-DD")} 23:59:59` : "",
      };
    }

    let newParam = {
      ...state.param,
      page: 0,
      ...value,
    };

    setState({ param: newParam });
    getListPhieu({
      ...newParam,
      dsTrangThai: newParam.dsTrangThai ? [newParam.dsTrangThai] : undefined,
      dsKhoId: newParam.dsKhoId ? [newParam.dsKhoId] : undefined,
    });
  };

  const listLoaiNhapXuatMemo = useMemo(() => {
    return listLoaiNhapXuat.filter((i) => [70, 71, 72].includes(i.id));
  }, [listLoaiNhapXuat]);

  useEffect(() => {
    setState({
      param: {
        ...state.param,
        khoaChiDinhId: khoaChiDinhId,
      },
    });
  }, [khoaChiDinhId]);
  const history = useHistory();

  const onRow = (record) => {
    return {
      onClick: (e) => {
        if (isModal) return;

        if (e.target.nodeName !== "INPUT") {
          history.push(linkDetail + record.id);
        }
      },
    };
  };

  const onChangePage = (page) => {
    const newParam = {
      ...state.param,
      page: page - 1,
    };
    setState({ param: newParam });
    getListPhieu(newParam);
  };

  const onSizeChange = (size) => {
    const newParam = {
      ...state.param,
      page: 0,
      size,
    };
    setState({ param: newParam });
    getListPhieu(newParam);
  };

  const onSettings = () => {
    refSettings && refSettings.current.settings();
  };

  const clickCheckbox = (id) => (e) => {
    if (e.target.checked) {
      setState({ listCheckId: [...state.listCheckId, id] });
    } else {
      setState({ listCheckId: state.listCheckId.filter((i) => i != id) });
    }
  };

  const onClickAll = (e) => {
    if (e.target.checked) {
      setState({ listCheckId: dsPhieuNhapXuat.map((i) => i.id) });
    } else {
      setState({ listCheckId: [] });
    }
  };

  //column
  const columns = () => [
    {
      title: (
        <HeaderSearch
          title={
            <Checkbox
              checked={state.listCheckId?.length === dsPhieuNhapXuat.length}
              onClick={onClickAll}
            ></Checkbox>
          }
          //   sort_key="soPhieuLinh"
          //   onClickSort={onClickSort}
          //   dataSort={dataSortColumn.soPhieuLinh || 0}
          //   search={<Input placeholder="Tìm kiếm" />}
        />
      ),
      dataIndex: "id",
      width: "50px",
      align: "center",
      hidden: isModal,
      render: (item) => (
        <Checkbox
          checked={state.listCheckId.includes(item)}
          onClick={clickCheckbox(item)}
        ></Checkbox>
      ),
    },
    {
      title: <HeaderSearch title={t("common.stt")} />,
      dataIndex: "index",
      width: "50px",
      align: "center",
      render: (_, __, index) => state.param.page * state.param.size + index + 1,
    },
    {
      title: (
        <HeaderSearch
          title={t("quanLyNoiTru.soPhieuTra")}
          sort_key="soPhieu"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.soPhieu || 0}
          search={
            <InputTimeout
              placeholder={t("common.timKiem")}
              onChange={onSearchInput("soPhieu")}
            />
          }
        />
      ),
      width: 100,
      dataIndex: "soPhieu",
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.phieuTraLan")}
          sort_key="lan"
          dataSort={dataSortColumn["lan"] || ""}
          onClickSort={onClickSort}
          search={
            <InputTimeout
              placeholder={t("common.timKiem")}
              onChange={onSearchInput("lan")}
            />
          }
        />
      ),
      width: "100px",
      dataIndex: "lan",
      key: "lan",
    },
    {
      title: (
        <HeaderSearch
          title={t("quanLyNoiTru.khoNhan")}
          sort_key="khoId"
          onClickSort={onClickSort}
          dataSort={dataSortColumn["khoId"] || 0}
          searchSelect={
            <SelectLoadMore
              api={dmKhoProvider.searchAll}
              addParam={addParam}
              onChange={onSearchInput("dsKhoId")}
              keySearch={"ten"}
              placeholder={t("quanLyNoiTru.chonKho")}
            />
          }
        />
      ),
      width: 160,
      dataIndex: "tenKho",
    },
    {
      title: (
        <HeaderSearch
          title={t("quanLyNoiTru.trangThaiPhieu")}
          sort_key="trangThai"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.trangThai || 0}
          searchSelect={
            <Select
              data={listTrangThaiPhieuNhapXuat}
              placeholder={t("quanLyNoiTru.chonTrangThai")}
              defaultValue=""
              onChange={onSearchInput("dsTrangThai")}
              hasAllOption={true}
            />
          }
        />
      ),
      dataIndex: "trangThai",
      key: "trangThai",
      width: "120px",
      render: (item) =>
        listTrangThaiPhieuNhapXuat?.find((i) => i.id === item)?.ten,
    },
    {
      title: (
        <HeaderSearch
          title={t("quanLyNoiTru.loaiTra")}
          sort_key="loaiNhapXuat"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.trangThai || 0}
          searchSelect={
            <Select
              data={listLoaiNhapXuatMemo}
              placeholder={t("quanLyNoiTru.chonLoaiTra")}
              defaultValue=""
              onChange={onSearchInput("loaiNhapXuat")}
              hasAllOption={true}
            />
          }
        />
      ),
      dataIndex: "loaiNhapXuat",
      key: "loaiNhapXuat",
      width: "120px",
      render: (item) => listLoaiNhapXuatMemo?.find((i) => i.id === item)?.ten,
    },
    {
      title: (
        <HeaderSearch
          title={t("quanLyNoiTru.loaiHangHoa")}
          sort_key="loaiDichVu"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.loaiDichVu || 0}
          searchSelect={
            <Select
              data={listLoaiDichVu}
              placeholder={t("quanLyNoiTru.chonLoaiHangHoa")}
              defaultValue=""
              onChange={onSearchInput("loaiDichVu")}
              hasAllOption={true}
            />
          }
        />
      ),
      width: "120px",
      dataIndex: "loaiDichVu",
      render: (item) => listLoaiDichVu?.find((i) => i.id === item)?.ten,
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.dotXuat")}
          searchSelect={
            <Select
              data={[10, 0]
                .map((item) => listLoaiChiDinh?.find((i) => i.id === item))
                ?.filter((i) => i)}
              hasAllOption={true}
              placeholder={t("danhMuc.chonTitle", {
                title: lowerFirst(t("quanLyNoiTru.cpdd.loaiChiDinh")),
              })}
              onChange={(e) => {
                let value =
                  e?.length && e[0] !== "" ? e.map(Number) : undefined;
                onSearchInput("dsLoaiChiDinh")(value);
              }}
              dropdownMatchSelectWidth={120}
              mode="multiple"
            />
          }
        />
      ),
      width: "100px",
      dataIndex: "loaiChiDinh",
      key: "loaiChiDinh",
      i18Name: "kho.dotXuat",
      align: "center",
      show: true,
      render: (item) => {
        return <Checkbox checked={item === LOAI_CHI_DINH.DOT_XUAT}></Checkbox>;
      },
    },
    {
      title: <HeaderSearch title={t("common.dienGiai")} />,
      show: true,
      i18Name: "common.dienGiai",
      width: 320,
      render: (item, list, index) => {
        const maPhieuLinh =
          (listAllMaPhieuLinh || []).find((x) => x.id == list?.phieuLinhId)
            ?.ten || "";
        const loaiHangHoa = listLoaiDichVu?.find(
          (i) => i.id === list?.loaiDichVu
        )?.ten;

        return (
          <div>
            {t("quanLyNoiTru.phieuTraNoiTru")} {loaiHangHoa} {maPhieuLinh}{" "}
            {t("common.tuNgay")}
            {list?.tuThoiGian
              ? moment(list?.tuThoiGian)?.format("DD/MM/YYYY HH:mm:ss")
              : " "}{" "}
            {t("common.denNgay")}{" "}
            {list?.denThoiGian
              ? moment(list?.denThoiGian)?.format("DD/MM/YYYY HH:mm:ss")
              : " "}
          </div>
        );
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("quanLyNoiTru.khoaTra")}
          sort_key="khoaChiDinhId"
          // onClickSort={onClickSort}
          dataSort={dataSortColumn["khoaChiDinhId"] || 0}
          searchSelect={
            <SelectLoadMore
              api={dmKhoaProvider.searchAll}
              addParam={addParam}
              onChange={onSearchInput("khoaChiDinhId")}
              keySearch={"ten"}
              placeholder={t("common.chonKhoa")}
            />
          }
        />
      ),
      width: "200px",
      dataIndex: "tenKhoaChiDinh",
    },
    {
      title: (
        <HeaderSearch
          title={t("quanLyNoiTru.ngayTaoPhieu")}
          sort_key="thoiGianTaoPhieu"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.thoiGianTaoPhieu || 0}
          searchDate={
            <DatePicker
              format="DD/MM/YYYY"
              placeholder={t("common.chonNgay")}
              onChange={onSearchInput("thoiGianTaoPhieu")}
            />
          }
        />
      ),
      width: "150px",
      dataIndex: "thoiGianTaoPhieu",
      render: (item) =>
        item ? moment(item).format("DD/MM/YYYY HH:mm:ss") : "",
    },
    {
      title: (
        <HeaderSearch
          title={t("quanLyNoiTru.ngayDuyetPhieu")}
          sort_key="thoiGianDuyet"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.thoiGianDuyet || 0}
          searchDate={
            <DatePicker
              format="DD/MM/YYYY"
              placeholder={t("common.chonNgay")}
              onChange={onSearchInput("thoiGianDuyet")}
            />
          }
        />
      ),
      width: "150px",
      dataIndex: "thoiGianDuyet",
      render: (item) =>
        item ? moment(item).format("DD/MM/YYYY HH:mm:ss") : "",
    },
    {
      title: (
        <HeaderSearch
          title={t("quanLyNoiTru.nguoiTao")}
          sort_key="tenNguoiTaoPhieu"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.tenNguoiTaoPhieu || 0}
          search={
            <InputTimeout
              placeholder={t("common.timKiem")}
              onChange={onSearchInput("tenNguoiTaoPhieu")}
            />
          }
        />
      ),
      width: "150px",
      dataIndex: "tenNguoiTaoPhieu",
    },
    {
      title: (
        <HeaderSearch
          title={t("quanLyNoiTru.nguoiDuyet")}
          sort_key="tenNguoiDuyet"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.tenNguoiDuyet || 0}
          search={
            <InputTimeout
              placeholder={t("common.timKiem")}
              onChange={onSearchInput("tenNguoiDuyet")}
            />
          }
        />
      ),
      width: "150px",
      dataIndex: "tenNguoiDuyet",
    },
    {
      title: <HeaderSearch title={t("common.tienIch")} />,
      key: "",
      width: 80,
      dataIndex: "",
      hideSearch: true,
      fixed: "right",
      hidden: !isModal,
      render: (_, item, index) => {
        return (
          <>
            <SVG.IcPrint
              className="ic-action"
              onClick={() => onPrintPhieu(item)}
            />
            <SVG.IcEye
              className="ic-action"
              onClick={() => {
                onGoToDetail(item);
              }}
            />
          </>
        );
      },
    },
  ];
  return (
    <>
      <TableWrapper
        columns={columns({
          t,
          onClickSort,
          onSettings,
          dataSortColumn,
          listTrangThaiNb,
        })}
        scroll={{ x: 1800 }}
        dataSource={dsPhieuNhapXuat}
        onRow={onRow}
        rowKey={(record) => `${record.id}`}
        tableName="table_QLNT_DSPhieuTra"
        ref={refSettings}
      />
      {!!totalElements && (
        <Pagination
          onChange={onChangePage}
          current={state.param.page + 1}
          pageSize={state.param.size}
          listData={dsPhieuNhapXuat}
          total={totalElements}
          onShowSizeChange={onSizeChange}
        />
      )}
      {state.listCheckId.length > 0 && (
        <div className="button-action">
          <Button
            className="btn_new"
            rightIcon={<SVG.IcDelete className="red" />}
            iconHeight={20}
            // onClick={
            //   refTimKiemPhieuNhap.current &&
            //   refTimKiemPhieuNhap.current.onCreateNew
            // }
          >
            <span>{t("quanLyNoiTru.xoaPhieu")}</span>
          </Button>
          <Button
            className="btn_new"
            rightIcon={<SVG.IcPrint className="blue" />}
            iconHeight={20}
            // onClick={
            //   refTimKiemPhieuNhap.current &&
            //   refTimKiemPhieuNhap.current.onCreateNew
            // }
          >
            <span>{t("quanLyNoiTru.inPhieu")}</span>
          </Button>
          <Button
            className="btn_new"
            type="primary"
            rightIcon={<SVG.IcSend />}
            iconHeight={20}
            // onClick={
            //   refTimKiemPhieuNhap.current &&
            //   refTimKiemPhieuNhap.current.onCreateNew
            // }
          >
            <span>{t("quanLyNoiTru.guiDuyet")}t</span>
          </Button>
        </div>
      )}
    </>
  );
};

export default DanhSach;
