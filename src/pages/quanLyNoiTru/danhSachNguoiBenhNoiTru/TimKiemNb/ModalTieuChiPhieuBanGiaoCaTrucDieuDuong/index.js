import React, {
  forwardRef,
  useImperativeHandle,
  useRef,
  useState,
  useEffect,
} from "react";
import { useTranslation } from "react-i18next";
import { DatePicker, Button, ModalTemplate, Select } from "components";
import { HOTKEY, LOAI_DICH_VU } from "constants/index";
import { Main } from "./styled";
import { Form, message, TreeSelect } from "antd";
import { SVG } from "assets";
import moment from "moment";
import { useListAll, useQueryAll, useStore } from "hooks";
import { query } from "redux-store/stores";

const ModalTieuChiPhieuBanGiaoCaTrucDieuDuong = (props, ref) => {
  const { t } = useTranslation();
  const [state, _setState] = useState({
    show: false,
    data: [],
  });
  const setState = (data = {}) => {
    _setState((state) => ({ ...state, ...data }));
  };

  const refModal = useRef(null);
  const refCallback = useRef(null);
  const [form] = Form.useForm();
  const khoaId = Form.useWatch("khoaId", form);

  const NOI_DUNG_BAN_GIAO_DATA = [
    {
      title: t("common.thuoc"),
      value: LOAI_DICH_VU.THUOC,
      key: LOAI_DICH_VU.THUOC,
      children: [],
    },
    {
      title: t("quanLyNoiTru.dvNoiTru.chePhamMau"),
      value: LOAI_DICH_VU.CHE_PHAM_MAU,
      key: LOAI_DICH_VU.CHE_PHAM_MAU,
      children: [],
    },
    {
      title: t("khamBenh.dichVuKyThuat"),
      value: 0,
      key: "0",
      children: [
        {
          title: t("dashboard.xetNghiem"),
          value: LOAI_DICH_VU.XET_NGHIEM,
          key: LOAI_DICH_VU.XET_NGHIEM,
        },
        {
          title: t("dashboard.cdha"),
          value: LOAI_DICH_VU.CDHA,
          key: LOAI_DICH_VU.CDHA,
        },
        {
          title: t("khamBenh.chiDinh.enum.ptTt"),
          value: LOAI_DICH_VU.PHAU_THUAT_THU_THUAT,
          key: LOAI_DICH_VU.PHAU_THUAT_THU_THUAT,
        },
      ],
    },
    {
      title: t("phieuIn.theoDoiChamSoc"),
      value: 201,
      key: 201,
      children: [],
    },
  ];

  const { data: listKhoaTheoTaiKhoan } = useQueryAll(
    query.khoa.queryKhoaTheoTaiKhoan
  );
  const { data: listAllPhong } = useQueryAll(
    query.phong.queryAllPhong({
      params: {
        khoaId,
      },
      enabled: state.show,
    })
  );
  const nhanVienId = useStore("auth.auth.nhanVienId");
  const [listAllNhanVien] = useListAll("nhanVien", {}, true);

  useImperativeHandle(ref, () => ({
    show: (data = {}, callBack) => {
      setState({
        show: true,
        data,
      });
      form.setFieldsValue({
        khoaId: data?.khoaId || null,
        dieuDuongId: nhanVienId,
        tuThoiGian: moment().set("hour", 0).set("minute", 0).set("second", 0),
        denThoiGian: moment()
          .set("hour", 23)
          .set("minute", 59)
          .set("second", 59),
      });
      refCallback.current = callBack;
    },
  }));
  const onOk = (isOk) => () => {
    if (isOk) {
      form.submit();
    } else {
      setState({ show: false });
    }
  };
  const hotKeys = [
    {
      keyCode: HOTKEY.ESC,
      onEvent: () => {
        onOk(false)();
      },
    },
    {
      keyCode: HOTKEY.F4,
      onEvent: () => {
        onOk(true)();
      },
    },
  ];

  useEffect(() => {
    if (state.show) {
      refModal.current.show();
    } else {
      refModal.current.hide();
    }
  }, [state.show]);

  const onHandleSubmit = () => {
    form
      .validateFields()
      .then((values) => {
        const data = {
          ...values,
          tuThoiGian: values?.tuThoiGian
            ? moment(values?.tuThoiGian).format("YYYY-MM-DD HH:mm:00")
            : null,
          denThoiGian: values?.denThoiGian
            ? moment(values?.denThoiGian).format("YYYY-MM-DD HH:mm:59")
            : null,
        };

        refCallback.current && refCallback.current(data);
        setState({ show: false });
      })
      .catch((e) => {
        message.error(e);
      });
  };

  const validateTuThoiGian = (rule, value) => {
    const denThoiGian = form.getFieldValue("denThoiGian");
    const startDate = moment(value);
    const endDate = moment(denThoiGian);
    const diffInDays = endDate.diff(startDate, "days");

    if (denThoiGian && value && diffInDays < 0) {
      return Promise.reject("Validation failed");
    }
    return Promise.resolve();
  };
  const validateDenThoiGian = (rule, value) => {
    const tuThoiGian = form.getFieldValue("tuThoiGian");
    const startDate = moment(tuThoiGian);
    const endDate = moment(value);
    const diffInDays = endDate.diff(startDate, "days");
    if (tuThoiGian && value && diffInDays < 0) {
      return Promise.reject("Validation failed");
    }
    return Promise.resolve();
  };

  const isDateDisabled = (date) => {
    const tuThoiGian = form.getFieldValue("tuThoiGian");
    const maxDate = moment(tuThoiGian).add(2, "days");
    const minDate = moment(tuThoiGian);

    return date.isAfter(maxDate, "day") || date.isBefore(minDate, "day");
  };

  return (
    <ModalTemplate
      ref={refModal}
      hotKeys={hotKeys}
      onCancel={onOk(false)}
      title={t("quanLyNoiTru.chonTieuChi")}
      width={520}
      actionLeft={
        <Button.QuayLai onClick={onOk(false)}>
          {t("common.quayLai")} [ESC]
        </Button.QuayLai>
      }
      actionRight={
        <Button
          type="primary"
          minWidth={100}
          onClick={onOk(true)}
          rightIcon={<SVG.IcSave></SVG.IcSave>}
        >
          <span> {t("common.dongY")}</span>{" "}
        </Button>
      }
    >
      <Main>
        <Form
          form={form}
          layout="vertical"
          style={{ width: "100%" }}
          className="form-custom"
          onFinish={onHandleSubmit}
        >
          <Form.Item
            label={t("common.khoa")}
            name="khoaId"
            style={{ width: "100%" }}
            rules={[
              {
                required: true,
                message: t("quanLyNoiTru.chonKhoa"),
              },
            ]}
          >
            <Select
              className="select"
              placeholder={t("common.chonKhoa")}
              data={listKhoaTheoTaiKhoan}
              allowClear={false}
            />
          </Form.Item>
          <Form.Item
            label={t("quanLyNoiTru.toDieuTri.tuNgay")}
            name="tuThoiGian"
            style={{ width: "100%" }}
            rules={[
              {
                required: true,
                message: t("common.vuiLongChonNgay"),
              },
              {
                message: t(
                  "quanLyNoiTru.toDieuTri.thoiGianTuNgayPhaiNhoHonThoiGianDenNgay"
                ),
                validator: validateTuThoiGian,
              },
            ]}
          >
            <DatePicker format={"DD/MM/YYYY HH:mm:ss"} showTime />
          </Form.Item>
          <Form.Item
            label={t("quanLyNoiTru.toDieuTri.denNgay")}
            name="denThoiGian"
            style={{ width: "100%" }}
            rules={[
              {
                required: true,
                message: t("common.vuiLongChonNgay"),
              },
              {
                message: t(
                  "quanLyNoiTru.toDieuTri.thoiGianDenNgayPhaiLonHonThoiGianTuNgay"
                ),
                validator: validateDenThoiGian,
              },
            ]}
          >
            <DatePicker
              format={"DD/MM/YYYY HH:mm:ss"}
              showTime
              disabledDate={isDateDisabled}
            />
          </Form.Item>
          <Form.Item
            label={t("quanLyNoiTru.dieuDuongPhuTrach")}
            name="dieuDuongId"
            style={{ width: "100%" }}
            rules={[
              {
                required: true,
                message: t("quanLyNoiTru.vuiLongChonTenDieuDuongPhuTrach"),
              },
            ]}
          >
            <Select
              className="select"
              placeholder={t("quanLyNoiTru.chonTenDieuDuongPhuTrach")}
              data={listAllNhanVien}
              allowClear={false}
            />
          </Form.Item>
          <Form.Item label={t("phieuIn.noiDungBanGiao")} name="dsLoaiDichVu">
            <TreeSelect
              treeData={NOI_DUNG_BAN_GIAO_DATA}
              treeCheckable={true}
              placeholder={t("phieuIn.chonNoiDungBanGiao")}
              style={{ width: "100%" }}
            />
          </Form.Item>
          <Form.Item label={t("common.phong")} name="dsPhongId">
            <Select
              className="select"
              placeholder={t("common.chonPhong")}
              data={listAllPhong}
              allowClear={false}
              mode="multiple"
            />
          </Form.Item>
        </Form>
      </Main>
    </ModalTemplate>
  );
};
export default forwardRef(ModalTieuChiPhieuBanGiaoCaTrucDieuDuong);
