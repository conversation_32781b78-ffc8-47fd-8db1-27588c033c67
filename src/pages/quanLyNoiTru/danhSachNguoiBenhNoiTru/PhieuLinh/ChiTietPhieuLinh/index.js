import React, { useEffect, useMemo, useRef, useState } from "react";
import { MainPage } from "./styled";
import { useDispatch, useSelector } from "react-redux";
import { Tooltip, Dropdown, But<PERSON>, AuthWrapper } from "components";
import { useHistory, useLocation, useParams } from "react-router-dom";
import TrangThai from "pages/kho/components/TrangThai";
import { Menu, message } from "antd";
import { useTranslation } from "react-i18next";
import { useConfirm, useEnum, useLoading, useThietLap } from "hooks";
import ModalNhapLyDo from "pages/kho/components/ModalNhapLyDo";
import ModalInDsNb from "./ModalInDsNb";
import ChiTietPhieuLinh from "./chiTiet";
import {
  TRANG_THAI_PHIEU_NHAP_XUAT,
  ROLES,
  THIET_LAP_CHUNG,
  ENUM,
  LOAI_DICH_VU,
  TRANG_THAI_THUOC,
  LOAI_NHAP_XUAT,
  TRANG_THAI_HOAN,
  DS_TINH_CHAT_KHOA,
} from "constants/index";
import { checkRole } from "lib-utils/role-utils";
import { SVG } from "assets";
import { transformObjToQueryString } from "hooks/useQueryString/queryString";
import { parseInPhieuLinhTraSauDuyet } from "utils/thiet-lap-chung-utils";
import { xuatFileExcelKho } from "utils/kho-utils";
import ModalChonPhong from "./ModalChonPhong";
import { isArray, isBoolean } from "utils/index";
import useCanPrintPhieu from "pages/quanLyNoiTru/hooks/useCanPrintPhieu";
import ModalChonMaPhieuLinh from "./ModalChonMaPhieuLinh";

const ChiTiet = ({ ...props }) => {
  const { showConfirm } = useConfirm();
  const { id } = useParams();
  const history = useHistory();
  const { showLoading, hideLoading } = useLoading();
  const { t } = useTranslation();
  const { thongTinPhieu } = useSelector((state) => state.phieuNhapXuat);
  const refModalNhapLyDo = useRef(null);
  const refModalInDsNb = useRef(null);
  const refModalChonPhong = useRef(null);
  const refModalChonMaPhieuLinh = useRef(null);
  const { goBack } = useHistory();
  const location = useLocation();
  const [state, _setState] = useState({ isEdit: false });
  const setState = (data) => {
    _setState((pre) => ({ ...pre, ...data }));
  };
  const { dsNbDvKho } = useSelector((state) => state.nbDvKho);
  const [listTrangThaiPhieuNhapXuat] = useEnum(ENUM.TRANG_THAI_PHIEU_NHAP_XUAT);
  const [listLoaiNhapXuat] = useEnum(ENUM.LOAI_NHAP_XUAT);
  const listTrangThaiDls = Object.values(TRANG_THAI_THUOC);

  const [IN_PHIEU_LINH_TRA_SAU_DUYET] = useThietLap(
    THIET_LAP_CHUNG.IN_PHIEU_LINH_TRA_SAU_DUYET,
    null
  );
  const [dataYC_DUYET_DLS_PHIEU_LINH, isFinish] = useThietLap(
    THIET_LAP_CHUNG.YC_DUYET_DLS_PHIEU_LINH
  );
  const [MA_LOAI_XUAT_TU_TRUC_TRA_LAI] = useThietLap(
    THIET_LAP_CHUNG.MA_LOAI_XUAT_TU_TRUC_TRA_LAI
  );

  const {
    nbDvKho: {
      onSuaSoLuong,
      getNbDvKho: getDsNb,
      getDsSlLe,
      getDsSlLinhTrenKhoTaiKhoa,
      getDsSlLeLinhTruoc,
    },
    phieuNhapXuat: {
      getById,
      xoaPhieu,
      resetData,
      inPhieuLinh,
      xuatPhieuLinh,
      guiDuyetPhieu,
      duyetPhieu,
      huyDuyet,
      tuChoiDuyet,
      inPhieuDsLinhThuoc,
      inPhieuDsLinhVatTu,
      inPhieuLinhBuKichCo,
      inPhieuCongKhaiThuoc,
      inPhieuNhapXuat,
      xuatPhieuNhapXuat,
      inPhieuLinhThuocDieuTriNgoaiTru,
      inPhieuLinhChiTiet,
      inPhieuDsNbLinhThuoc,
      inPhieuLinhMau,
      inTemDuTruMau,
      duyetNhapPhieu,
      huyDuyetNhapPhieu,
    },
  } = useDispatch();

  const dsTrangThaiDlsIds = useCanPrintPhieu(thongTinPhieu);

  useEffect(() => {
    getDsSlLe({ phieuLinhNoId: id });
    getDsSlLinhTrenKhoTaiKhoa({
      phieuNhapXuatId: id,
      dsTrangThaiHoan: [0, 10, 20],
    });
    getDsSlLeLinhTruoc({ phieuLinhTraId: id });
    getById(id);
    return () => {
      resetData();
    };
  }, [id]);

  useEffect(() => {
    if (id && thongTinPhieu && Object.keys(thongTinPhieu).length) {
      const param =
        thongTinPhieu?.loaiNhapXuat === 80 ? "phieuLinhId" : "phieuNhapXuatId";
      getDsNb({ [param]: id, dsTrangThaiHoan: [0, 10, 20] });
    }
  }, [thongTinPhieu, id]);

  const isXacNhanNhap = useMemo(() => {
    if (thongTinPhieu) {
      const { khoaChiDinh, loaiNhapXuat } = thongTinPhieu || {};
      let dsTinhChatKhoa = khoaChiDinh?.dsTinhChatKhoa || [];
      return (
        loaiNhapXuat === LOAI_NHAP_XUAT.LINH_NOI_TRU &&
        dsTinhChatKhoa.includes(DS_TINH_CHAT_KHOA.XAC_NHAN_NHAP_KHO)
      );
    }
    return false;
  }, [thongTinPhieu]);

  const isYCDuyetDLS = useMemo(() => {
    //Chỉ bắt thêm dk  trạng thái duyệt DLS với phiếu lĩnh, lĩnh bù Thuốc (loaiDichVu=100)
    return (
      thongTinPhieu.loaiDichVu === LOAI_DICH_VU.THUOC &&
      [80, 85].includes(thongTinPhieu.loaiNhapXuat) &&
      dataYC_DUYET_DLS_PHIEU_LINH &&
      dataYC_DUYET_DLS_PHIEU_LINH.toLowerCase() === "true"
    );
  }, [dataYC_DUYET_DLS_PHIEU_LINH, thongTinPhieu?.loaiNhapXuat]);

  const isShowButton =
    window.location.pathname.indexOf("/kho/chi-tiet-phieu-linh/") !== -1;

  const { linkBack, chains, hiddenBtn, showBtnHuyDuyet } = useMemo(() => {
    const { pathname } = window.location;
    if (pathname.indexOf("/kho/chi-tiet-phieu-linh/") !== -1) {
      return {
        hiddenBtn: false,
        showBtnHuyDuyet: [isXacNhanNhap ? 28 : 30].some(
          (i) => i === thongTinPhieu.trangThai
        ),
        linkBack: "/kho/xuat-kho",
        chains: [
          { title: t("kho.kho"), link: "/kho" },
          {
            title: t("kho.xuatKho"),
            link:
              "/kho/xuat-kho" +
              transformObjToQueryString(location?.state || {}),
          },
          {
            link: "/kho/chi-tiet-phieu-linh/" + id,
            title: t("pttt.chiTietPhieuLinh"),
          },
        ],
      };
    }
    if (pathname.indexOf("/phau-thuat-thu-thuat/chi-tiet-phieu-linh/") !== -1) {
      return {
        hiddenBtn: true,
        showBtnHuyDuyet: false,
        linkBack: "/phau-thuat-thu-thuat/danh-sach-phieu-linh",
        chains: [
          {
            link: "/phau-thuat-thu-thuat",
            title: t("pttt.quanLyPhauThuatThuThuat"),
          },
          {
            link: "/phau-thuat-thu-thuat/danh-sach-nguoi-benh",
            title: t("pttt.danhSachPhauThuatThuThuat"),
          },
          {
            link: "/phau-thuat-thu-thuat/danh-sach-phieu-linh",
            title: t("pttt.danhSachPhieuLinh"),
          },
          {
            link: "/phau-thuat-thu-thuat/chi-tiet-phieu-linh/" + id,
            title: t("pttt.chiTietPhieuLinh"),
          },
        ],
      };
    }
    if (pathname.indexOf("/chan-doan-hinh-anh/chi-tiet-phieu-linh/") !== -1) {
      return {
        hiddenBtn: true,
        showBtnHuyDuyet: false,
        linkBack: "/chan-doan-hinh-anh/danh-sach-phieu-linh",
        chains: [
          {
            link: "/chan-doan-hinh-anh",
            title: t("cdha.cdha"),
          },
          {
            link: "/chan-doan-hinh-anh/thuc-hien-cdha-tdcn",
            title: t("cdha.thucHienCdhaTdcn"),
          },
          {
            link: "/chan-doan-hinh-anh/danh-sach-phieu-linh",
            title: t("pttt.danhSachPhieuLinh"),
          },
          {
            link: "/chan-doan-hinh-anh/chi-tiet-phieu-linh/" + id,
            title: t("pttt.chiTietPhieuLinh"),
          },
        ],
      };
    }
    return {
      hiddenBtn: true,
      showBtnHuyDuyet: false,
      linkBack: "/quan-ly-noi-tru/danh-sach-phieu-linh",
      chains: [
        { link: "/quan-ly-noi-tru", title: t("quanLyNoiTru.quanLyNoiTru") },
        {
          link: "/quan-ly-noi-tru/danh-sach-nguoi-benh-noi-tru",
          title: t("quanLyNoiTru.danhSachNguoiBenhNoiTru"),
        },
        {
          link: "/quan-ly-noi-tru/danh-sach-phieu-linh",
          title: t("pttt.danhSachPhieuLinh"),
        },
        {
          link: "/quan-ly-noi-tru/chi-tiet-phieu-linh/" + id,
          title: t("pttt.chiTietPhieuLinh"),
        },
      ],
    };
  }, [window.location, thongTinPhieu, isXacNhanNhap]);

  const { showTuChoiDuyet, showHuyGuiDuyet, isNoiTru, isKho, isCdha, isPttt } =
    useMemo(() => {
      const { pathname } = window.location;
      const isNoiTru =
        pathname.indexOf("/quan-ly-noi-tru/chi-tiet-phieu-linh/") !== -1;
      const isKho = pathname.indexOf("/kho/chi-tiet-phieu-linh/") !== -1;
      const isCdha =
        pathname.indexOf("/chan-doan-hinh-anh/chi-tiet-phieu-linh") !== -1;
      const isPttt =
        pathname.indexOf("/phau-thuat-thu-thuat/chi-tiet-phieu-linh/") !== -1;
      const objBtn = { showTuChoiDuyet: false, showHuyGuiDuyet: false };
      if (isFinish) {
        if (isNoiTru) {
          objBtn.showHuyGuiDuyet =
            (dataYC_DUYET_DLS_PHIEU_LINH?.eval() &&
              thongTinPhieu.trangThai ===
                TRANG_THAI_PHIEU_NHAP_XUAT.CHO_DUYET &&
              thongTinPhieu.trangThaiDls <
                TRANG_THAI_THUOC.DA_DUYET_DUOC_LAM_SANG.id) ||
            (checkRole([ROLES["KHO"].HUY_GUI_DUYET_PHIEU_LINH_NOI_TRU]) &&
              thongTinPhieu.trangThai === TRANG_THAI_PHIEU_NHAP_XUAT.CHO_DUYET);
        }
        if (isKho) {
          objBtn.showTuChoiDuyet =
            thongTinPhieu.trangThai === TRANG_THAI_PHIEU_NHAP_XUAT.CHO_DUYET &&
            (dataYC_DUYET_DLS_PHIEU_LINH?.eval()
              ? thongTinPhieu.trangThaiDls <
                TRANG_THAI_THUOC.DA_DUYET_DUOC_LAM_SANG.id
              : true);
        }
        if (isCdha || isPttt) {
          //
        }
      }
      return { ...objBtn, isNoiTru, isKho, isCdha, isPttt };
    }, [
      window.location,
      dataYC_DUYET_DLS_PHIEU_LINH,
      isFinish,
      thongTinPhieu?.trangThai,
      thongTinPhieu?.trangThaiDls,
    ]);

  const { trangThaiInIds, trangThaiIn, loaiNhapXuatIds, loaiNhapXuat } =
    useMemo(() => {
      let trangThaiInIds = [];
      let trangThaiIn = [];
      let loaiNhapXuatIds = [];
      let loaiNhapXuat = [];

      if (listTrangThaiPhieuNhapXuat) {
        //TH ko có thiết lập chung thì ko  bắt in phiếu theo trạng thái
        if (IN_PHIEU_LINH_TRA_SAU_DUYET == null) {
          trangThaiIn = listTrangThaiPhieuNhapXuat;
          trangThaiInIds = listTrangThaiPhieuNhapXuat.map((x) => x.id);
          loaiNhapXuat = listLoaiNhapXuat;
          loaiNhapXuatIds = listLoaiNhapXuat.map((x) => x.id);
        } else {
          const { dsTrangThaiPhieu, dsLoaiNhapXuat } =
            parseInPhieuLinhTraSauDuyet(IN_PHIEU_LINH_TRA_SAU_DUYET);

          trangThaiInIds = dsTrangThaiPhieu;
          trangThaiIn = trangThaiInIds.map((item) =>
            listTrangThaiPhieuNhapXuat.find((x) => x.id == item)
          );

          loaiNhapXuatIds = dsLoaiNhapXuat;
          loaiNhapXuat = loaiNhapXuatIds.map((item) =>
            listLoaiNhapXuat.find((x) => x.id == item)
          );
        }
      }

      return { trangThaiInIds, trangThaiIn, loaiNhapXuatIds, loaiNhapXuat };
    }, [
      IN_PHIEU_LINH_TRA_SAU_DUYET,
      listTrangThaiPhieuNhapXuat,
      listLoaiNhapXuat,
    ]);

  const onPrint = async () => {
    if (
      loaiNhapXuatIds.includes(thongTinPhieu.loaiNhapXuat) &&
      !trangThaiInIds.includes(thongTinPhieu.trangThai)
    ) {
      message.error(
        t("quanLyNoiTru.chiChoPhepInPhieuVoiCacTrangThai", {
          title: trangThaiIn.map((x) => x.ten).join("; "),
        })
      );
      return;
    }

    if (!isBoolean(dsTrangThaiDlsIds) && isArray(dsTrangThaiDlsIds, true)) {
      let trangThaiIn = listTrangThaiDls.filter((i) =>
        dsTrangThaiDlsIds.includes(i.id)
      );
      message.error(
        t("quanLyNoiTru.chiChoPhepInPhieuVoiCacTrangThaiDls", {
          title: trangThaiIn.map((x) => x.ten).join("; "),
        })
      );
      return;
    }

    try {
      showLoading();

      await inPhieuLinh({ id, printMerge: true, openInNewTab: true });
    } catch (error) {
      console.error(error);
    } finally {
      hideLoading();
    }
  };
  const onDelete = () => {
    showConfirm(
      {
        title: t("common.canhBao"),
        content: `${t("quanLyNoiTru.xoaPhieuSo", {
          title: thongTinPhieu?.soPhieu,
        })}`,
        cancelText: t("common.dong"),
        okText: t("common.xoa"),
        classNameOkText: "button-error",
        showImg: true,
        showBtnOk: true,
      },
      () => {
        xoaPhieu({ id: thongTinPhieu.id }).then((s) => {
          onBack();
        });
      },
      () => {}
    );
  };

  const isXacNhanNhapKho = isXacNhanNhap && !isKho;

  const times = useMemo(() => {
    const {
      thoiGianDuyet,
      thoiGianGuiDuyet,
      thoiGianTaoPhieu,
      thoiGianDuyetNhap,
      trangThai,
    } = thongTinPhieu || {};
    let arrTime = [thoiGianTaoPhieu, thoiGianGuiDuyet];
    if (isXacNhanNhapKho) {
      let _thoiGianDuyet =
        trangThai >= TRANG_THAI_PHIEU_NHAP_XUAT.CHO_XAC_NHAN_NHAP
          ? thoiGianDuyet
          : null;
      arrTime.splice(2, 0, _thoiGianDuyet);
      let _thoiGianDuyetNhap =
        trangThai === TRANG_THAI_PHIEU_NHAP_XUAT.HOAN_THANH
          ? thoiGianDuyetNhap
          : null;
      arrTime.push(_thoiGianDuyetNhap);
    } else {
      arrTime.push(thoiGianDuyet);
    }
    return arrTime.map((item) => item);
  }, [thongTinPhieu, isXacNhanNhapKho]);

  const onBack = () => {
    history.push(linkBack);
  };

  const onXacNhanNhap = async () => {
    try {
      showLoading();
      await duyetNhapPhieu({ id: thongTinPhieu?.id });
    } catch (error) {
      console.error(error);
    } finally {
      hideLoading();
    }
  };

  const onHuyXacNhanNhap = async () => {
    try {
      showLoading();
      await huyDuyetNhapPhieu({ id: thongTinPhieu?.id });
    } catch (error) {
      console.error(error);
    } finally {
      hideLoading();
    }
  };

  const onGuiDuyetPhieu = () => {
    showLoading();
    guiDuyetPhieu({ id })
      .then(() => {
        onPrint();
      })
      .finally(() => {
        hideLoading();
      });
  };

  const onHuyDuyet = () => {
    refModalNhapLyDo.current &&
      refModalNhapLyDo.current.show(
        {
          title: t("khoMau.lyDoHuyDuyet"),
          message: t("khoMau.dienLyDoHuyDuyet"),
        },
        (lyDo) => {
          huyDuyet({ id, lyDo });
        }
      );
  };

  const onTuChoiDuyet = () => {
    refModalNhapLyDo.current &&
      refModalNhapLyDo.current.show(
        {
          title: t("khoMau.lyDoTuChoi"),
          message: t("khoMau.dienLyDoTuChoiDuyet"),
        },
        (lyDo) => {
          tuChoiDuyet({ id, lyDo });
        }
      );
  };

  const onDuyetPhieu = async () => {
    showLoading();
    try {
      await duyetPhieu({ id });
    } finally {
      hideLoading();
    }
  };

  const onSave = () => {
    if (state?.dataSave?.length) {
      let payload = (state.dataSave || []).map((item) => ({
        nbDichVuId: item.id,
        loNhapId: item.loNhapId,
        soLuong: item.soLuong,
      }));
      onSuaSoLuong({ payload, loaiDichVu: thongTinPhieu.loaiDichVu }).then(
        () => {
          setState({ isEdit: false });
          getDsSlLe({ phieuLinhNoId: id });
          getDsSlLinhTrenKhoTaiKhoa({
            phieuNhapXuatId: id,
            dsTrangThaiHoan: [0, 10, 20],
          });
          getById(id);

          const param =
            thongTinPhieu?.loaiNhapXuat === 80
              ? "phieuLinhId"
              : "phieuNhapXuatId";
          getDsNb({ [param]: id, dsTrangThaiHoan: [0, 10, 20] });
        }
      );
    }
  };
  const onPrintDanhSachBenhNhan = () => {
    const listPhongId = dsNbDvKhoMemo
      .filter((item) => item.phongId)
      .map((item) => item.phongId);

    if (!isArray(listPhongId, 1)) {
      inPhieuLinhChiTiet({
        id,
        openInNewTab: true,
      });
    } else {
      refModalChonPhong.current?.show(
        {
          listPhongId,
        },
        {
          onSubmit: (dsPhongId) => {
            return inPhieuLinhChiTiet({
              id,
              dsPhongId,
              openInNewTab: true,
            });
          },
        }
      );
    }
  };
  const onPrintDanhSachPhatThuoc = async () => {
    showLoading();
    try {
      await inPhieuDsLinhThuoc({ phieuLinhId: id });
    } catch (err) {
      console.log("err", err);
    } finally {
      hideLoading();
    }
  };

  const onPrintDanhSachNbLinhThuoc = async (tachTheoNb) => {
    showLoading();
    try {
      await inPhieuDsNbLinhThuoc({ phieuLinhId: id, tachTheoNb });
    } catch (err) {
      console.log("err", err);
    } finally {
      hideLoading();
    }
  };

  const onPrintDanhSachPhatVatTu = async () => {
    showLoading();
    try {
      await inPhieuDsLinhVatTu(id);
    } catch (err) {
      console.log("err", err);
    } finally {
      hideLoading();
    }
  };

  const onPrintPhieuLinhBuKichCo = async () => {
    showLoading();
    try {
      await inPhieuLinhBuKichCo(id);
    } catch (err) {
      console.log("err", err);
    } finally {
      hideLoading();
    }
  };

  const onPrintPhieuThucHienCongKhaiThuocNb = async () => {
    showLoading();
    try {
      await inPhieuCongKhaiThuoc(id);
    } catch (err) {
      console.log("err", err);
    } finally {
      hideLoading();
    }
  };

  const onPrintChiTietPhieuXuat = async () => {
    showLoading();
    try {
      await inPhieuNhapXuat({ dsId: [id] });
    } catch (err) {
      console.error(err);
    } finally {
      hideLoading();
    }
  };

  const onPrintphieuXuatKhoTongHop = async ({ dsPhieuLinhId }) => {
    showLoading();
    try {
      await inPhieuNhapXuat({ dsId: [id], dsPhieuLinhId, phieuTongHop: true });
    } catch (err) {
      console.error(err);
    } finally {
      hideLoading();
    }
  };

  const onClicknPrintphieuXuatKhoTongHop = () => {
    refModalChonMaPhieuLinh.current?.show({}, (dsPhieuLinhId) => {
      onPrintphieuXuatKhoTongHop({ dsPhieuLinhId });
    });
  };

  const onPrintPhieuLinhTongHopHangHoa = async () => {
    try {
      showLoading();

      await inPhieuLinh({ id, printMerge: true, phieuTongHop: true });
    } catch (error) {
      console.error(error);
    } finally {
      hideLoading();
    }
  };

  const onPrintPhieuLinhThuoc = async () => {
    showLoading();
    try {
      await inPhieuLinhThuocDieuTriNgoaiTru(id);
    } catch (err) {
      console.error(err);
    } finally {
      hideLoading();
    }
  };

  const onExportFileChiTietPhieuXuat = async () => {
    try {
      showLoading();
      const listMaLoaiXuatTuTruc = MA_LOAI_XUAT_TU_TRUC_TRA_LAI?.split(",");
      const payload = listMaLoaiXuatTuTruc.includes(
        thongTinPhieu?.hinhThucNhapXuat?.ma
      )
        ? { xuatTraLai: true }
        : {};

      const s = await xuatPhieuNhapXuat({ dsId: [id], ...payload });
      xuatFileExcelKho(s);
    } catch (error) {
      console.error(error);
    } finally {
      hideLoading();
    }
  };

  const onExportFileChiTietPhieu = async () => {
    try {
      if (
        loaiNhapXuatIds.includes(thongTinPhieu.loaiNhapXuat) &&
        !trangThaiInIds.includes(thongTinPhieu.trangThai)
      ) {
        t("quanLyNoiTru.daCoToDieuTriXacNhanTaoTrungToDieuTri", {
          title: moment(data?.thoiGianYLenh).format("DD-MM-YYYY"),
        });
        message.error(
          t("quanLyNoiTru.chiChoPhepInPhieuVoiCacTrangThai", {
            title: trangThaiIn.map((x) => x.ten).join("; "),
          })
        );
        return;
      }

      showLoading();

      const s = await xuatPhieuLinh({ id });
      xuatFileExcelKho(s);
    } catch (error) {
      console.error(error);
    } finally {
      hideLoading();
    }
  };

  const onPrintPhieuChePhamMau = async () => {
    try {
      showLoading();

      await inPhieuLinhMau({ id, printMerge: true });
    } catch (error) {
      console.error(error);
    } finally {
      hideLoading();
    }
  };

  const onPrintTemDuTruMau = async () => {
    try {
      showLoading();

      await inTemDuTruMau({
        phieuNhapXuatId: id,
        dsTrangThaiHoan: [
          TRANG_THAI_HOAN.THUONG,
          TRANG_THAI_HOAN.CHO_DUYET_HOAN,
          TRANG_THAI_HOAN.CHO_DUYET_DOI,
        ],
      });
    } catch (error) {
      console.error(error);
    } finally {
      hideLoading();
    }
  };

  let dsNbDvKhoMemo = useMemo(() => {
    return props?.maDichVu
      ? dsNbDvKho.filter((item) => item.maDichVu === props?.maDichVu)
      : dsNbDvKho;
  }, [props?.maDichVu, dsNbDvKho]);

  const onSetData = (data) => {
    setState({ dataSave: data });
  };

  const menu = (
    <Menu
      items={[
        ...(isKho !== -1
          ? [
              {
                key: 0,
                label: (
                  <a href={() => false} onClick={onPrintChiTietPhieuXuat}>
                    {t("kho.inChiTietPhieuXuat")}
                  </a>
                ),
              },
            ]
          : []),
        {
          key: 1,
          label: (
            <a href={() => false} onClick={onPrint}>
              {t("kho.inChiTietPhieu")}
            </a>
          ),
        },
        {
          key: 2,
          label: (
            <a href={() => false} onClick={onPrintDanhSachBenhNhan}>
              {t("kho.inDanhSachBenhNhan")}
            </a>
          ),
        },
        {
          key: 3,
          label: (
            <a href={() => false} onClick={onPrintDanhSachPhatThuoc}>
              {t("kho.inDanhSachLinhThuoc")}
            </a>
          ),
        },
        {
          key: 4,
          label: (
            <a href={() => false} onClick={onPrintPhieuLinhBuKichCo}>
              {t("kho.inChiTietPhieuLinhBuKichCo")}
            </a>
          ),
        },
        ...([LOAI_DICH_VU.VAT_TU].includes(thongTinPhieu?.loaiDichVu)
          ? [
              {
                key: 5,
                label: (
                  <a href={() => false} onClick={onPrintDanhSachPhatVatTu}>
                    {t("kho.inDanhSachPhatVatTu")}
                  </a>
                ),
              },
            ]
          : []),
        {
          key: 6,
          label: (
            <a href={() => false} onClick={onPrintPhieuThucHienCongKhaiThuocNb}>
              {t("kho.phieuThucHienCongKhaiThuocNbTheoPhieuLinh")}
            </a>
          ),
        },
        ...(isNoiTru
          ? [
              {
                key: 7,
                label: (
                  <a href={() => false} onClick={onPrintPhieuLinhThuoc}>
                    {t("kho.phieuLinhThuocDieuTriNgoaiTru")}
                  </a>
                ),
              },
            ]
          : []),
        ...([LOAI_DICH_VU.THUOC].includes(thongTinPhieu?.loaiDichVu)
          ? [
              {
                key: 8,
                label: (
                  <a
                    href={() => false}
                    onClick={() => onPrintDanhSachNbLinhThuoc(false)}
                  >
                    {t("kho.phieuXuatKhoTheoDanhSachBenhNhan")}
                  </a>
                ),
              },
              {
                key: 9,
                label: (
                  <a
                    href={() => false}
                    onClick={() => onPrintDanhSachNbLinhThuoc(true)}
                  >
                    {t("kho.phieuXuatSuDungBenhNhan")}
                  </a>
                ),
              },
            ]
          : []),
        ...(isKho
          ? [
              {
                key: 10,
                label: (
                  <a
                    href={() => false}
                    onClick={onClicknPrintphieuXuatKhoTongHop}
                  >
                    {t("kho.phieuXuatKhoTongHop")}
                  </a>
                ),
              },
            ]
          : []),
        ...(isNoiTru
          ? [
              {
                key: 11,
                label: (
                  <a
                    href={() => false}
                    onClick={onPrintPhieuLinhTongHopHangHoa}
                  >
                    {t("kho.phieuLinhTongHopChiaHangHoa")}
                  </a>
                ),
              },
            ]
          : []),
        ...([LOAI_NHAP_XUAT.CHE_PHAM_MAU].includes(thongTinPhieu?.loaiNhapXuat)
          ? [
              {
                key: 12,
                label: (
                  <a href={() => false} onClick={onPrintPhieuChePhamMau}>
                    {t("kho.phieuTongHopDuTruMauVaChePhamMau")}
                  </a>
                ),
              },
            ]
          : []),
        {
          key: 13,
          label: (
            <a href={() => false} onClick={onPrintTemDuTruMau}>
              {t("quanLyNoiTru.inTemDuTruMau")}
            </a>
          ),
        },
      ]}
    />
  );

  const items = [
    {
      key: 1,
      label: (
        <a onClick={onExportFileChiTietPhieuXuat}>
          <div className="flex icon_utilities gap-8">
            <SVG.IcDownload />
            <span>{t("kho.xuatFileChiTietPhieuXuat")}</span>
          </div>
        </a>
      ),
    },
    {
      key: 2,
      label: (
        <a onClick={onExportFileChiTietPhieu}>
          <div className="flex icon_utilities gap-8">
            <SVG.IcDownload />
            <span>{t("kho.xuatFileChiTietPhieu")}</span>
          </div>
        </a>
      ),
    },
  ];

  const renderBtnGuiDuyet = () => {
    if (
      [
        TRANG_THAI_PHIEU_NHAP_XUAT.TAO_MOI,
        TRANG_THAI_PHIEU_NHAP_XUAT.TAO_MOI_DA_GIU_CHO,
      ].includes(thongTinPhieu.trangThai) &&
      checkRole([ROLES["KHO"].GUI_DUYET_PHIEU_XUAT_KHO])
    ) {
      let isShow = true;
      //Nếu gán quyền trên thì với loại dịch vụ = Vật tư thì nút Gửi duyệt chỉ hiển thị ở màn hình Xuất kho
      if (
        thongTinPhieu.loaiDichVu === LOAI_DICH_VU.VAT_TU &&
        checkRole([ROLES["KHO"].CHI_HIEN_THI_GUI_DUYET_XUAT_KHO])
      ) {
        const { pathname } = window.location;
        if (pathname.indexOf("/kho/chi-tiet-phieu-linh/") == -1) {
          isShow = false;
        }
      }

      if (isShow) {
        return (
          <Button
            className="right-btn"
            onClick={onGuiDuyetPhieu}
            rightIcon={<SVG.IcSend />}
            type="primary"
            minWidth={100}
          >
            {t("kho.guiDuyet")}
          </Button>
        );
      }
    }
    return null;
  };

  return (
    <MainPage
      breadcrumb={chains}
      title={
        <div className="wrapper-title">
          {t("pttt.chiTietPhieuLinh")}
          <div className="header-action">
            {isKho && (
              <Dropdown
                menu={{ items }}
                trigger={"click"}
                overlayClassName="danh-muc-dropdown-tien-ich"
              >
                <Button
                  rightIcon={<SVG.IcMore />}
                  style={{ marginLeft: "10px" }}
                  height={28}
                >
                  {t("common.tienIch")}
                </Button>
              </Dropdown>
            )}
            {![20, 30].includes(thongTinPhieu?.trangThai) && (
              <Tooltip title={t("kho.xoaPhieu")}>
                <div className="action-btn" onClick={onDelete}>
                  <SVG.IcDelete />
                </div>
              </Tooltip>
            )}
          </div>
        </div>
      }
      titleRight={
        <TrangThai times={times} {...(isXacNhanNhapKho && { type: 6 })} />
      }
    >
      <ChiTietPhieuLinh
        onSetData={onSetData}
        isEdit={state.isEdit}
        renderBottom={
          <div className="bottom">
            <div className="left">
              <Button.QuayLai onClick={goBack} />
            </div>
            <div className="right">
              <Tooltip title={t("kho.inChiTietPhieu")}>
                <Dropdown overlay={menu} trigger={["click"]}>
                  <Button rightIcon={<SVG.IcPrint />} iconHeight={15}>
                    {t("common.inGiayTo")}
                  </Button>
                </Dropdown>
              </Tooltip>

              {thongTinPhieu.trangThai ===
                TRANG_THAI_PHIEU_NHAP_XUAT.CHO_DUYET &&
                !state.isEdit &&
                isShowButton && (
                  <Button
                    onClick={() => setState({ isEdit: true })}
                    className="left-btn"
                    rightIcon={<SVG.IcEdit />}
                    minWidth={100}
                  >
                    {t("kho.suaPhieu")}
                  </Button>
                )}

              {state.isEdit && isShowButton && (
                <Button
                  className="right-btn"
                  onClick={() => setState({ isEdit: false })}
                  rightIcon={<SVG.IcCloseCircle />}
                  minWidth={90}
                >
                  {t("common.huy")}
                </Button>
              )}
              {state.isEdit && isShowButton && (
                <Button className="right-btn" onClick={onSave} minWidth={90}>
                  {t("common.luu")}
                </Button>
              )}
              {renderBtnGuiDuyet()}
              {showBtnHuyDuyet &&
                checkRole([ROLES["KHO"].HUY_DUYET_PHIEU_XUAT_KHO]) && (
                  <Button
                    className="right-btn"
                    onClick={onHuyDuyet}
                    rightIcon={<SVG.IcCloseCircle />}
                    minWidth={100}
                  >
                    {t("kho.huyDuyet")}
                  </Button>
                )}
              {(showHuyGuiDuyet || showTuChoiDuyet) && (
                <Button
                  className="left-btn"
                  onClick={onTuChoiDuyet}
                  rightIcon={<SVG.IcCloseCircle />}
                  minWidth={100}
                >
                  {t(
                    showHuyGuiDuyet ? "khoMau.huyGuiDuyet" : "kho.tuChoiDuyet"
                  )}
                </Button>
              )}
              {(isYCDuyetDLS
                ? thongTinPhieu.trangThaiDls ===
                  TRANG_THAI_THUOC.DA_DUYET_DUOC_LAM_SANG.id
                : true) &&
                thongTinPhieu.trangThai ===
                  TRANG_THAI_PHIEU_NHAP_XUAT.CHO_DUYET &&
                !hiddenBtn &&
                checkRole([ROLES["KHO"].DUYET_PHIEU_NHAP_KHO]) && (
                  <Button
                    className="right-btn"
                    onClick={onDuyetPhieu}
                    type={"primary"}
                    rightIcon={<SVG.IcSave />}
                    minWidth={100}
                  >
                    {t("common.duyet")}
                  </Button>
                )}
              {isXacNhanNhapKho &&
                onXacNhanNhap &&
                thongTinPhieu?.trangThai ===
                  TRANG_THAI_PHIEU_NHAP_XUAT.CHO_XAC_NHAN_NHAP && (
                  <>
                    <Button
                      className="right-btn"
                      minWidth={80}
                      type="primary"
                      onClick={onXacNhanNhap}
                    >
                      {t("kho.xacNhanNhap")}
                    </Button>
                  </>
                )}
              {isXacNhanNhapKho &&
                onHuyXacNhanNhap &&
                thongTinPhieu?.trangThai ===
                  TRANG_THAI_PHIEU_NHAP_XUAT.HOAN_THANH && (
                  <AuthWrapper accessRoles={[ROLES["KHO"].HUY_XAC_NHAN_NHAP]}>
                    <Button
                      className="right-btn"
                      minWidth={80}
                      type="primary"
                      onClick={onHuyXacNhanNhap}
                    >
                      {t("kho.huyDuyetNhap")}
                    </Button>
                  </AuthWrapper>
                )}
            </div>
          </div>
        }
        {...props}
      />

      <ModalNhapLyDo {...props} isEdit={state.isEdit} ref={refModalNhapLyDo} />
      <ModalInDsNb ref={refModalInDsNb} />
      <ModalChonPhong ref={refModalChonPhong} />
      <ModalChonMaPhieuLinh ref={refModalChonMaPhieuLinh} />
    </MainPage>
  );
};

export default ChiTiet;
