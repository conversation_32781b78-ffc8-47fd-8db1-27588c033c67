import React, {
  useState,
  useImperativeHandle,
  forwardRef,
  useEffect,
  useRef,
  useMemo,
} from "react";
import { Input, message, Row, Col } from "antd";
import { Main } from "./styled";
import { useDispatch } from "react-redux";
import moment from "moment";
import { useTranslation } from "react-i18next";
import {
  Button,
  ModalTemplate,
  InputTimeout,
  Checkbox,
  Select,
} from "components";
import { LOAI_DICH_VU, ROLES, THIET_LAP_CHUNG } from "constants/index";
import { useLoading, useThietLap } from "hooks";
import { SVG } from "assets";
import { checkRole } from "lib-utils/role-utils";

const FormItem = ({ children, label, ...props }) => {
  return (
    <div className="form-item">
      <div className="label">{label}</div>
      <div>{children}</div>
    </div>
  );
};

const ModalSuaThongTinVacxin = (props, ref) => {
  const { t } = useTranslation();
  const { showLoading, hideLoading } = useLoading();
  const [state, _setState] = useState({
    show: false,
    data: {},
    listSoLoVacxin: [],
  });
  const refCallback = useRef(null);
  const [CHO_PHEP_CHON_LO_KHI_KE_VACXIN] = useThietLap(
    THIET_LAP_CHUNG.CHO_PHEP_CHON_LO_KHI_KE_VACXIN
  );
  const [dataAN_CHECKBOX_TU_TRA] = useThietLap(
    THIET_LAP_CHUNG.AN_CHECKBOX_TU_TRA
  );

  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };
  const refModal = useRef(null);
  const {
    chiDinhDichVuKho: { suaThongTinVacxin, getDsSoLoVacxin },
  } = useDispatch();

  useImperativeHandle(ref, () => ({
    show: async (options = {}, callback) => {
      const { data } = options;
      setState({
        show: true,
        data,
      });
      console.log("data", data);
      getDsSoLoVacxin({
        dsLoaiDichVu: [LOAI_DICH_VU.VAC_XIN],
        ma: data.maDichVu,
        theoSoLuongTonKho: 15,
        khoId: data.khoId,
      }).then((res) => {
        setState({ listSoLoVacxin: res });
      });

      refCallback.current = callback;
    },
  }));

  const enableChonLo = useMemo(() => {
    return (
      CHO_PHEP_CHON_LO_KHI_KE_VACXIN &&
      CHO_PHEP_CHON_LO_KHI_KE_VACXIN.toLowerCase() == "true"
    );
  }, [CHO_PHEP_CHON_LO_KHI_KE_VACXIN]);

  useEffect(() => {
    if (state?.show) {
      refModal.current && refModal.current.show();
    } else {
      refModal.current && refModal.current.hide();
    }
  }, [state?.show]);

  const onCancel = () => {
    setState({ show: false });
    refModal.current && refModal.current.hide();
  };

  const onOK = (isOk) => () => {
    if (isOk) {
      onHandleSubmit(state.data);
    } else {
      setState({ show: false });
    }
  };
  const onHandleSubmit = async (values) => {
    try {
      showLoading();
      let payload = {
        id: values.id,
        nbDichVu: {
          soLuong: values.soLuong,
          tuTra: values.tuTra,
          khongTinhTien: values.khongTinhTien,
        },
        nbDvKho: {
          khoId: state.data?.khoId,
          loNhapId: values?.loNhapId,
        },
        muiTiem: values.muiTiem,
        lieuLuong: values.lieuLuong,
      };
      await suaThongTinVacxin([payload]);
      if (refCallback.current) refCallback.current();
      onCancel();
    } catch (error) {
      message.error(error);
    } finally {
      hideLoading();
    }
  };
  const onChange = (type) => (e) => {
    const data = { ...state.data };
    if (type == "tuTra" && e) {
      data["khongTinhTien"] = false;
    } else if (type == "khongTinhTien" && e) {
      data["tuTra"] = false;
    }
    if (type == "thoiGianThucHien" && e == null) {
      data["thoiGianThucHien"] = data.thoiGianThucHien;
    }
    data[type] = e;
    setState({ data });
  };

  const render = (type) => {
    switch (type) {
      case "soLuong":
        return (
          <InputTimeout
            type="number"
            min={0}
            step={2}
            value={state.data.soLuong}
            onChange={onChange("soLuong")}
          />
        );
      case "lieuLuong":
        return (
          <InputTimeout
            value={state.data.lieuLuong}
            onChange={onChange("lieuLuong")}
          />
        );
      case "muiTiem":
        return (
          <InputTimeout
            type="number"
            min={0}
            step={1}
            value={state.data.muiTiem}
            onChange={onChange("muiTiem")}
          />
        );
      case "tuTra":
        return (
          <Checkbox
            checked={state.data.tuTra}
            onChange={(e) => onChange("tuTra")(e.target.checked)}
          >
            {t("common.tuTra")}
          </Checkbox>
        );
      case "khongTinhTien":
        return (
          <Checkbox
            checked={state.data.khongTinhTien}
            onChange={(e) => onChange("khongTinhTien")(e.target.checked)}
            disabled={
              !checkRole([ROLES["QUAN_LY_NOI_TRU"].TICK_KHONG_TINH_TIEN])
            }
          >
            {t("common.khongTinhTien")}
          </Checkbox>
        );
      case "loNhapId":
        return (
          <Select
            data={(state.listSoLoVacxin || []).map((item) => ({
              id: item.loNhapId,
              ten: `Số lô: ${item.soLo} - HSD: ${
                item.ngayHanSuDung
                  ? moment(item.ngayHanSuDung).format("DD/MM/YYYY")
                  : ""
              }`,
            }))}
            value={state.data.loNhapId}
            onChange={onChange("loNhapId")}
            disabled={!enableChonLo}
          ></Select>
        );
      default:
        break;
    }
  };

  return (
    <ModalTemplate
      ref={refModal}
      title={t("tiemChung.thongTinVacxin")}
      onCancel={onCancel}
      width={800}
      actionLeft={<Button.QuayLai onClick={onOK(false)} />}
      actionRight={
        !state.isReadonly && (
          <Button
            type="primary"
            minWidth={100}
            rightIcon={<SVG.IcSave />}
            onClick={onOK(true)}
          >
            {t("common.luu")}
          </Button>
        )
      }
    >
      <fieldset disabled={state.isReadonly} style={{ width: "100%" }}>
        <Main>
          <Row gutter={16}>
            <Col xs={24}>
              <FormItem label={t("tiemChung.tenVacxin")} name="tenDichVu">
                <Input value={state.data.tenDichVu} disabled />
              </FormItem>
            </Col>
            <Col xs={8}>
              <FormItem label={t("common.soLuong")} name="soLuong">
                {render("soLuong")}
              </FormItem>
            </Col>
            <Col xs={8}>
              <FormItem label={t("tiemChung.muiTiem")} name="muiTiem">
                {render("muiTiem")}
              </FormItem>
            </Col>
            <Col xs={8}>
              <FormItem label={t("tiemChung.lieuLuong")} name="lieuLuong">
                {render("lieuLuong")}
              </FormItem>
            </Col>

            <Col xs={12}>
              <FormItem
                label={`${t("kho.soLo")} - ${t("kho.hanSuDung")}`}
                name="loNhapId"
              >
                {render("loNhapId")}
              </FormItem>
            </Col>
            {!dataAN_CHECKBOX_TU_TRA?.eval() && (
              <Col xs={6}>
                <FormItem label="&nbsp;" name="tuTra">
                  {render("tuTra")}
                </FormItem>
              </Col>
            )}
            <Col xs={6}>
              <FormItem label="&nbsp;" name="khongTinhTien">
                {render("khongTinhTien")}
              </FormItem>
            </Col>
          </Row>
        </Main>
      </fieldset>
    </ModalTemplate>
  );
};

export default forwardRef(ModalSuaThongTinVacxin);
