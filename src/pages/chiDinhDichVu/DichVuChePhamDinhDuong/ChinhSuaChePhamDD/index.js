import React, {
  useState,
  useImperativeHandle,
  forwardRef,
  useEffect,
  useRef,
  useMemo,
} from "react";
import { Input, message, Row, Col } from "antd";
import { Main } from "./styled";
import { useDispatch } from "react-redux";
import ModalThemLieuDung from "pages/khamBenh/DonThuoc/ModalThemLieuDung";
import moment from "moment";
import { useTranslation } from "react-i18next";
import { useStore, useListAll, useEnum, useThietLap } from "hooks";
import { ENUM, LOAI_CHI_DINH, ROLES, THOI_DIEM_DUNG } from "constants/index";
import {
  DateTimePicker,
  Button,
  Select,
  ModalTemplate,
  InputTimeout,
  Checkbox,
} from "components";
import { containText, roundNumberPoint } from "utils";
import { cloneDeep } from "lodash";
import chiDinhThuocUtils from "utils/chi-dinh-thuoc-utils";
import { TRANG_THAI_NB, THIET_LAP_CHUNG } from "constants/index";
import { SVG } from "assets";
import { convertPayloadThuoc } from "./config";
import { checkRole } from "lib-utils/role-utils";

const formatTime = "HH:mm:ss";

const FormItem = ({ children, label, style, ...props }) => {
  return (
    <div className="form-item" style={style}>
      <div className="label">{label || ""}</div>
      <div>{children}</div>
    </div>
  );
};

const SuaThongTinCPDD = ({ isReadonlyDvNoiTru }, ref) => {
  const configData = useStore("chiDinhKhamBenh.configData");
  const { t } = useTranslation();
  const [state, _setState] = useState({
    show: false,
    data: {},
  });
  const refCallback = useRef(null);

  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };
  const refModalThemLieuDung = useRef(null);
  const refModal = useRef(null);
  const {
    lieuDungThuoc: { createOrEdit: createOrEditLieuDungThuoc },
    mucDichSuDung: { onSearch },
    chiDinhChePhamDinhDuong: { themThongTin },
    lieuDung: { createOrEdit: createOrEditLieuDung, getListAllLieuDung },
  } = useDispatch();

  const listDataLieuDung = useStore("lieuDung.listAllLieuDung", []);
  const listMucDichSuDung = useStore("mucDichSuDung.listMucDichSuDung", []);
  const [listAllDuongDung] = useListAll("duongDung", {}, true);
  const nhanVienId = useStore("auth.auth.nhanVienId", null);

  const [dataAN_CHECKBOX_TU_TRA] = useThietLap(
    THIET_LAP_CHUNG.AN_CHECKBOX_TU_TRA
  );

  const isNbDaRaVien = useMemo(() => {
    return configData?.thongTinNguoiBenh?.trangThai >= TRANG_THAI_NB.DA_RA_VIEN;
  }, [configData?.thongTinNguoiBenh]);

  useImperativeHandle(ref, () => ({
    show: async (options = {}, callback) => {
      const { data, isReadonly } = options;
      const item = {
        ...data,
        sttNgaySuDung:
          !data?.sttNgaySuDung || data?.sttNgaySuDung == 0
            ? null
            : data?.sttNgaySuDung,
        thoiGianBatDau:
          data?.thoiGianBatDau &&
          moment(data.thoiGianBatDau).format("HH:mm:ss"),
        thoiGianThucHien:
          data?.thoiGianThucHien && moment(data.thoiGianThucHien),
        dotXuat: data?.loaiChiDinh == LOAI_CHI_DINH.DOT_XUAT,
        boSung: data?.loaiChiDinh == LOAI_CHI_DINH.BO_SUNG,
      };

      getListAllLieuDung(
        {
          bacSiId: nhanVienId,
          dichVuId: data.dichVuId,
          page: "",
          size: "",
          active: true,
        },
        nhanVienId + "_" + data.dichVuId
      );
      onSearch({ dataSearch: { dichVuId: data.dichVuId } });
      setState({
        show: true,
        data: {
          ...item,
          soLuong: String(item.soLuong),
        },
        isReadonly,
        soLuong: String(item.soLuong),
      });

      refCallback.current = callback;
    },
  }));

  useEffect(() => {
    if (state?.show) {
      refModal.current && refModal.current.show();
    } else {
      refModal.current && refModal.current.hide();
    }
  }, [state?.show]);

  const onCancel = () => {
    setState({ show: false });
    refModal.current && refModal.current.hide();
  };

  const onOK = (isOk) => () => {
    if (isOk) {
      onHandleSubmit(state.data);
    } else {
      setState({ show: false });
    }
  };
  const onHandleSubmit = async (values) => {
    const _soLuong = parseFloat(String(values.soLuong).replace(/,/g, "."));
    let payload = [];

    payload = [
      convertPayloadThuoc({ values, id: values.id, soLuong: _soLuong }),
    ];
    themThongTin(payload)
      .then((s) => {
        if (s?.code === 0) {
          if (refCallback.current) refCallback.current();
          onCancel();
        } else {
          message.error(s?.[0]?.message || s?.message);
        }
      })
      .catch((err) => {
        console.log("err: ", err);
      });
  };

  const genarateCachDung = ({ data }) => {
    let cachDung = data.tenDuongDung || "";
    if (data.soLan1Ngay && data.soLuong1Lan) {
      cachDung = `${cachDung} ngày ${data.soLan1Ngay} lần, mỗi lần ${data.soLuong1Lan} ${data.tenDonViTinh}`;

      if (data.thoiDiem) {
        cachDung = `${cachDung} ${data.thoiDiem}`;
      }
    }

    return cachDung;
  };

  //xử lý tính lại số lượng + SL sơ cấp khi các field số ngày, số lần / ngày, số lượng / lần thay đổi
  const genarateSoLuong = (data, soLuong) => {
    if (data.heSoDinhMuc != 1) {
      const soLuongSoCap = roundNumberPoint(soLuong / data.heSoDinhMuc, 6); // nếu các màn khác thì lấy đến số thập phân thứ 2

      data.soLuong = soLuong;
      data.soLuongSoCap = soLuongSoCap;
    } else {
      data.soLuong = soLuong;
      data.soLuongSoCap = soLuong;
    }

    data.soLuong = roundNumberPoint(data.soLuong, 6);
    data.soLuongSoCap = roundNumberPoint(data.soLuongSoCap, 6);
  };

  const onChange = (type) => (e, list) => {
    const data = { ...state.data };
    if (type == "thoiGianThucHien" && e == null) {
      data["thoiGianThucHien"] = data.thoiGianThucHien;
    }
    data[type] = e;

    if (type === "soLuong") {
      const _soLuong = parseFloat(String(data[type]).replace(/,/g, ".")) || 0;
      data.soLuongSoCap = _soLuong / (data.heSoDinhMuc || 1);
    }

    if (type === "soLuongSoCap") {
      data.soLuong = (Number(data.soLuongSoCap) || 0) * (data.heSoDinhMuc || 1);
    }

    if (type === "dotXuat" && e) {
      data.boSung = false;
    } else if (type === "boSung" && e) {
      data.dotXuat = false;
    }

    if (type === "lieuDungId" && list) {
      const soLuong1Lan = list?.soLuong1Lan;
      const soLan1Ngay = list?.soLan1Ngay;
      data.cachDung = genarateCachDung({
        data: {
          ...data,
          soLuong1Lan,
          soLan1Ngay,
        },
      });

      data.lieuDungId = e;
      data.soLuong1Lan = soLuong1Lan || data.soLuong1Lan;
      data.soLan1Ngay = soLan1Ngay || data.soLan1Ngay;
      if (data.lieuDungId && soLuong1Lan && soLan1Ngay) {
        genarateSoLuong(
          data,
          chiDinhThuocUtils.tinhSoLuong({
            soNgay: 1,
            soLuong1Lan: soLuong1Lan,
            soLan1Ngay: soLan1Ngay,
          })
        );
      }
    }

    if ((type === "soLan1Ngay" || type === "soLuong1Lan") && e) {
      if (!data.phieuLinhId) {
        genarateSoLuong(
          data,
          chiDinhThuocUtils.tinhSoLuong({
            soNgay: 1,
            soLan1Ngay: type === "soLan1Ngay" ? e : data.soLan1Ngay,
            soLuong1Lan: type === "soLuong1Lan" ? e : data.soLuong1Lan,
          })
        );
      }
    }

    if (type === "thoiDiem") {
      data.thoiDiem = e;
      data.cachDung = genarateCachDung({ data });
    }

    if (
      type === "soLan1Ngay" ||
      type === "soLuong1Lan" ||
      type === "cachGio" ||
      type === "thoiGianBatDau"
    ) {
      const thoiGianSang = [];
      const thoiGianChieu = [];
      const thoiGianToi = [];
      const thoiGianDem = [];
      for (let i = 0; i < data.soLan1Ngay; i++) {
        const thoiGian = moment(data.thoiGianBatDau, formatTime).add(
          data.cachGio * i,
          "hours"
        );

        if (
          thoiGian.diff(moment("00:00:00", formatTime)) >= 0 &&
          thoiGian.diff(moment("12:00:00", formatTime)) <= 0
        ) {
          thoiGianSang.push(thoiGian);
        }

        if (
          thoiGian.diff(moment("12:00:01", formatTime)) >= 0 &&
          thoiGian.diff(moment("18:00:00", formatTime)) <= 0
        ) {
          thoiGianChieu.push(thoiGian);
        }
        if (
          thoiGian.diff(moment("18:00:01", formatTime)) >= 0 &&
          thoiGian.diff(moment("22:00:00", formatTime)) <= 0
        ) {
          thoiGianToi.push(thoiGian);
        }

        if (
          thoiGian.diff(moment("22:00:01", formatTime)) >= 0 &&
          thoiGian.diff(moment("23:59:59", formatTime)) <= 0
        ) {
          thoiGianDem.push(thoiGian);
        }
      }
      data.cachDung = genarateCachDung({ data });
    }

    setState({ data });
  };
  const onChangeSelect =
    (type, index) =>
    (e = [], list) => {
      const data = { ...state.data };
      if (type === "thoiDiem") {
        data.thoiDiem = e[0];

        if (data.soLuong1Lan || data.soLan1Ngay) {
          const tenDuongDung = `${
            data.tenDuongDung ? data.tenDuongDung + " - " : ""
          }`;
          let cachDung = `${tenDuongDung} ngày ${data.soLan1Ngay} lần, mỗi lần ${data.soLuong1Lan} ${data.tenDonViTinh}`;
          if (e) {
            cachDung = `${cachDung} ${e}`;
          }
          data.cachDung = cachDung;
        }
      }

      setState({ data });
    };
  const listAllLieuDung = useMemo(() => {
    if (
      state.data &&
      state.data.lieuDungId &&
      state.data.tenLieuDung &&
      !state.searchLieuDungWord &&
      !listDataLieuDung.map((item) => item.id).includes(state.data.lieuDungId)
    ) {
      return [
        ...listDataLieuDung,
        { id: state.data.lieuDungId, ten: state.data.tenLieuDung },
      ];
    } else {
      return listDataLieuDung;
    }
  }, [listDataLieuDung, state.data, state?.searchLieuDungWord]);

  const onSearchLieuDung = (e) => {
    if (e) {
      getListAllLieuDung({
        page: "",
        size: "",
        ten: e,
      });
    } else {
      getListAllLieuDung({
        bacSiId: nhanVienId,
        dichVuId: state?.data.dichVuId,
        page: "",
        size: "",
      });
    }
    setState({ searchLieuDungWord: e || "" });
  };

  const filterOption = (input = "", option) => {
    return containText(option?.children, input);
  };

  const render = (type) => {
    const isReadonly = isNbDaRaVien || state.data.phieuLinhId;
    switch (type) {
      case "lieuDungId":
        return (
          <Select
            data={listAllLieuDung}
            defaultValue={state.data.tenLieuDung}
            value={state.data.lieuDungId}
            onChange={onChange("lieuDungId")}
            onSearch={onSearchLieuDung}
            disabled={isReadonlyDvNoiTru}
            filterOption={filterOption}
            dropdownClassName="table-thuoc-lieu-dung"
            notFoundContent={
              <div>
                <div style={{ color: "#7A869A", textAlign: "center" }}>
                  <small>{t("common.khongCoDuLieuPhuHop")}</small>
                </div>
                <Row justify="center">
                  <Button
                    trigger="click"
                    style={{
                      border: "1px solid",
                      borderRadius: "10px",
                      width: "215px",
                      margin: "auto",
                      lineHeight: 0,
                      // boxShadow: "-1px 3px 1px 1px #d9d9d9",
                      cursor: "pointer",
                    }}
                    onClick={() =>
                      refModalThemLieuDung &&
                      refModalThemLieuDung.current.show(
                        {
                          tenLieuDung: state.searchLieuDungWord,
                          data: state?.data,
                        },
                        (res) => {
                          const { values } = res;
                          values.bacSiId = nhanVienId;
                          createOrEditLieuDung(values).then(async (s) => {
                            const dataCustom = {
                              lieuDung: {
                                ...s,
                              },
                              lieuDungId: s.id,
                              dichVuId: state?.data?.dichVuId,
                            };
                            await createOrEditLieuDungThuoc(dataCustom);
                            getListAllLieuDung(
                              {
                                bacSiId: nhanVienId,
                                dichVuId: state?.data?.dichVuId,
                                page: "",
                                size: "",
                                active: true,
                              },
                              nhanVienId + "_" + state?.data.dichVuId
                            );
                            let updateDataSelected = cloneDeep(state.data);
                            updateDataSelected.lieuDungId = s?.id;
                            updateDataSelected.cachDung = genarateCachDung({
                              data: {
                                ...updateDataSelected,
                                tenDuongDung: s?.duongDung?.ten,
                              },
                            });
                            setState({
                              data: updateDataSelected,
                            });
                          });
                        }
                      )
                    }
                  >
                    {t("khamBenh.donThuoc.themNhanhLieuDungBS")}
                  </Button>
                </Row>
              </div>
            }
          ></Select>
        );
      case "dotDung":
        return (
          <InputTimeout
            type="number"
            min={0}
            value={state.data.dotDung}
            onChange={onChange("dotDung")}
            disabled={isReadonlyDvNoiTru}
          />
        );
      case "ghiChu":
        return (
          <InputTimeout
            value={state.data.ghiChu}
            onChange={onChange("ghiChu")}
            disabled={isReadonlyDvNoiTru}
          />
        );
      case "soLuong":
        return (
          <InputTimeout
            // type="number"
            min={0}
            value={state.data.soLuong}
            onChange={onChange("soLuong")}
            disabled={
              isReadonly || !!state.data.soPhieuLinh || isReadonlyDvNoiTru
            }
          />
        );
      case "soLuongSoCap":
        return (
          <InputTimeout
            type="number"
            min={0}
            value={state.data.soLuongSoCap}
            onChange={onChange("soLuongSoCap")}
            disabled={
              isReadonly || !!state.data.soPhieuLinh || isReadonlyDvNoiTru
            }
          />
        );
      case "tenDonViTinh":
        return <Input value={state.data.tenDonViTinh} disabled />;
      case "tenDvtSoCap":
        return <Input value={state.data.tenDvtSoCap} disabled />;
      case "tuTra":
        if (dataAN_CHECKBOX_TU_TRA?.eval()) return null;
        return (
          <Checkbox
            checked={state.data.tuTra}
            onChange={(e) => onChange("tuTra")(e.target.checked)}
            disabled={listMucDichSuDung.length || isReadonlyDvNoiTru}
          />
        );
      case "khongTinhTien":
        return (
          <Checkbox
            disabled={listMucDichSuDung.length || isReadonlyDvNoiTru}
            checked={state.data.khongTinhTien}
            onChange={(e) => onChange("khongTinhTien")(e.target.checked)}
          ></Checkbox>
        );
      case "thoiGianThucHien":
        return (
          <DateTimePicker
            value={state.data.thoiGianThucHien}
            defaultValue={moment(state.data.thoiGianThucHien).format(
              "DD/MM/YYYY HH:mm:ss"
            )}
            onChange={onChange("thoiGianThucHien")}
            disabled={isReadonlyDvNoiTru}
          />
        );
      case "duongDungId":
        return (
          <Select
            data={listAllDuongDung}
            placeholder={t("common.duongDung")}
            value={state.data.duongDungId}
            onChange={onChange("duongDungId")}
            disabled={isReadonlyDvNoiTru}
          />
        );
      case "soLan1Ngay":
        return (
          <InputTimeout
            type="number"
            min={0}
            value={state.data.soLan1Ngay}
            onChange={onChange("soLan1Ngay")}
            disabled={isNbDaRaVien || isReadonlyDvNoiTru}
          />
        );
      case "soLuong1Lan":
        return (
          <InputTimeout
            min={0}
            value={state.data.soLuong1Lan}
            onChange={onChange("soLuong1Lan")}
            disabled={isNbDaRaVien || isReadonlyDvNoiTru}
          />
        );
      case "cachDung":
        return (
          <InputTimeout
            value={state.data.cachDung}
            onChange={onChange("cachDung")}
            disabled={isReadonlyDvNoiTru}
          />
        );
      case "thoiDiem":
        if (state.data.thoiDiem) {
          return (
            <InputTimeout
              value={state.data.thoiDiem}
              onChange={onChange("thoiDiem")}
              disabled={isReadonlyDvNoiTru}
            />
          );
        } else {
          return (
            <Select
              mode="tags"
              style={{
                width: "100%",
              }}
              value={state.data.thoiDiem || []}
              placeholder={t("khamBenh.donThuoc.nhapThoiDiemDung")}
              onChange={onChangeSelect("thoiDiem")}
              options={[
                {
                  value: THOI_DIEM_DUNG.TRUOC_AN,
                  label: THOI_DIEM_DUNG.TRUOC_AN,
                },
                { value: THOI_DIEM_DUNG.SAU_AN, label: THOI_DIEM_DUNG.SAU_AN },
                {
                  value: THOI_DIEM_DUNG.TRONG_KHI_AN,
                  label: THOI_DIEM_DUNG.TRONG_KHI_AN,
                },
                {
                  value: THOI_DIEM_DUNG.KHI_SOT,
                  label: THOI_DIEM_DUNG.KHI_SOT,
                },
                {
                  value: THOI_DIEM_DUNG.BUOI_TOI,
                  label: THOI_DIEM_DUNG.BUOI_TOI,
                },
              ]}
              disabled={isReadonlyDvNoiTru}
            />
          );
        }
      case "sttNgaySuDung":
        return (
          <InputTimeout
            type="number"
            min={0}
            value={state.data.sttNgaySuDung}
            onChange={onChange("sttNgaySuDung")}
            disabled={isReadonlyDvNoiTru}
          />
        );
      case "soPhieuLinh":
        return <Input value={state.data.soPhieuLinh} disabled />;
      default:
        break;
    }
  };

  return (
    <ModalTemplate
      ref={refModal}
      title={t("quanLyNoiTru.dvNoiTru.thongTinChePhamDD")}
      onCancel={onCancel}
      width={configData.isNgoaiTru ? "90%" : 900}
      actionLeft={<Button.QuayLai onClick={onOK(false)} />}
      actionRight={
        !isReadonlyDvNoiTru && (
          <Button
            type="primary"
            minWidth={100}
            rightIcon={<SVG.IcSave />}
            onClick={onOK(true)}
          >
            {t("common.luu")}
          </Button>
        )
      }
    >
      <Main>
        <Row gutter={6}>
          <Col xs={11}>
            <FormItem
              label={t("quanLyNoiTru.cpdd.tenChePhamDinhDuong")}
              name="tenDichVu"
            >
              <Input value={state.data.tenDichVu} disabled />
            </FormItem>
          </Col>
          <Col xs={3} offset={1}>
            <FormItem label={t("common.lan/ngay")} name="soLan1Ngay">
              {render("soLan1Ngay")}
            </FormItem>
          </Col>
          <Col xs={3}>
            <FormItem label={t("common.sl/lan")} name="soLuong1Lan">
              {render("soLuong1Lan")}
            </FormItem>
          </Col>
          <Col xs={3}>
            <FormItem label={t("common.soLuong")} name="soLuong">
              {render("soLuong")}
            </FormItem>
          </Col>
          <Col xs={3}>
            <FormItem
              label={t("quanLyNoiTru.toDieuTri.dvt")}
              name="tenDonViTinh"
            >
              {render("tenDonViTinh")}
            </FormItem>
          </Col>

          {/*  */}
          <Col xs={11}>
            <FormItem
              label={t("vatTu.thoiGianThucHien")}
              name="thoiGianThucHien"
            >
              {render("thoiGianThucHien")}
            </FormItem>
          </Col>

          <Col xs={6} offset={1}>
            <FormItem
              label={t("quanLyNoiTru.dvNoiTru.soPhieuLinh")}
              name="soPhieuLinh"
            >
              {render("soPhieuLinh")}
            </FormItem>
          </Col>
          <Col xs={3}>
            <FormItem label={t("common.slSoCap")} name="soLuongSoCap">
              {render("soLuongSoCap")}
            </FormItem>
          </Col>
          <Col xs={3}>
            <FormItem label={t("kho.dvtSoCap")} name="tenDvtSoCap">
              {render("tenDvtSoCap")}
            </FormItem>
          </Col>

          {/*  */}
          <Col xs={11}>
            <FormItem
              label={t("khamBenh.donThuoc.thoiDiemDung")}
              name="thoiDiem"
            >
              {render("thoiDiem")}
            </FormItem>
          </Col>
          <Col xs={12} offset={1}>
            <FormItem label={t("common.lieuDung")} name="lieuDungId">
              {render("lieuDungId")}
            </FormItem>
          </Col>

          {/*  */}
          <Col xs={11}>
            <FormItem label={t("common.cachDung")} name="cachDung">
              {render("cachDung")}
            </FormItem>
          </Col>
          <Col xs={12} offset={1}>
            <FormItem label={t("common.luuY")} name="ghiChu">
              {render("ghiChu")}
            </FormItem>
          </Col>
          <Col xs={3}>
            <FormItem
              name="dotXuat"
              valuePropName="checked"
              style={{
                display: "flex",
                alignItems: "center",
                paddingLeft: "10px",
              }}
            >
              <Checkbox
                checked={state.data.dotXuat}
                onChange={(e) => onChange("dotXuat")(e.target.checked)}
                disabled={!!state.data.soPhieuLinh || isReadonlyDvNoiTru}
              >
                {t("kho.dotXuat")}
              </Checkbox>
            </FormItem>
          </Col>
          <Col xs={9}>
            <FormItem
              name="boSung"
              valuePropName="checked"
              style={{ display: "flex", alignItems: "center" }}
            >
              <Checkbox
                checked={state.data.boSung}
                onChange={(e) => onChange("boSung")(e.target.checked)}
                disabled={!!state.data.soPhieuLinh || isReadonlyDvNoiTru}
              >
                {t("kho.boSung")}{" "}
              </Checkbox>
            </FormItem>
          </Col>
          <Col xs={3}>
            <FormItem
              name="tuTra"
              valuePropName="checked"
              style={{
                display: "flex",
                alignItems: "center",
                paddingLeft: "10px",
              }}
            >
              <Checkbox
                checked={state.data.tuTra}
                onChange={(e) => onChange("tuTra")(e.target.checked)}
                disabled={isReadonlyDvNoiTru}
              >
                {t("common.tuTra")}
              </Checkbox>
            </FormItem>
          </Col>
          <Col xs={5}>
            <FormItem
              name="khongTinhTien"
              valuePropName="checked"
              style={{ display: "flex", alignItems: "center" }}
            >
              <Checkbox
                disabled={
                  isReadonlyDvNoiTru ||
                  !checkRole([ROLES["QUAN_LY_NOI_TRU"].TICK_KHONG_TINH_TIEN])
                }
                checked={state.data.khongTinhTien}
                onChange={(e) => onChange("khongTinhTien")(e.target.checked)}
              >
                {t("common.khongTinhTien")}{" "}
              </Checkbox>
            </FormItem>
          </Col>
        </Row>
      </Main>
      <ModalThemLieuDung ref={refModalThemLieuDung} />
    </ModalTemplate>
  );
};

export default forwardRef(SuaThongTinCPDD);
