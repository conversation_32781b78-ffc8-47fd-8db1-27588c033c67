import React, { useState, useRef, useEffect, useMemo } from "react";
import { Checkbox, Tooltip, TableWrapper, HeaderSearch } from "components";
import { MainTable } from "../styled";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import ChinhSuaVatTu from "pages/chiDinhDichVu/DichVuVatTu/ChinhSuaVatTu";
import ModalChuyenDichVu from "pages/phauThuatThuThuat/ChiTietNguoiBenh/ModalChuyenDichVu";
import {
  LOAI_CHI_DINH,
  LOAI_DICH_VU,
  ROLES,
  TRANG_THAI_HOAN,
  TRANG_THAI_THUOC,
} from "constants/index";
import { groupBy } from "lodash";
import { SVG } from "assets";
import { useConfirm } from "hooks";
import ModalNhapSoLuong from "pages/khamBenh/DonThuoc/ModalNhapSoLuong";
import { checkRole } from "lib-utils/role-utils";

const { Setting } = TableWrapper;

function Table(props) {
  const { showConfirm } = useConfirm();
  const refSuaThongTin = useRef(null);
  const refChuyenDichVu = useRef(null);
  const refModalNhapSoLuong = useRef(null);
  const refSettings = useRef(null);
  const {
    chiDinhVatTu: { onDeleteDichVu, getListDichVuVatTu, themThongTin },
    traHangHoa: { postDsDvThuocTraKho },
  } = useDispatch();

  const isNoiTru = useMemo(() => {
    return (
      window.location.pathname.indexOf(
        "/quan-ly-noi-tru/chi-tiet-nguoi-benh-noi-tru/to-dieu-tri"
      ) >= 0
    );
  }, []);

  const {
    listDvVatTu,
    disabledAll,
    chiDinhTuDichVuId,
    nbDotDieuTriId,
    onThemVatTuCon,
    isShowSoLuongDinhMuc,
    isReadonly,
  } = props;
  const [state, _setState] = useState({
    dataVatTu: [],
  });
  const { t } = useTranslation();
  const setState = (data) => {
    _setState({
      ...state,
      ...data,
    });
  };

  const onDelete = (record) => {
    const ten = record.ten || record.tenDichVu || "";
    showConfirm(
      {
        title: t("common.xoaDuLieu"),
        content: t("common.banCoChacMuonXoa") + ten + "?",
        cancelText: t("common.quayLai"),
        okText: t("common.dongY"),
        classNameOkText: "button-error",
        showImg: true,
        showBtnOk: true,
      },
      () => {
        onDeleteDichVu(record.id).then((s) =>
          getListDichVuVatTu({
            nbDotDieuTriId: nbDotDieuTriId,
            chiDinhTuDichVuId: chiDinhTuDichVuId,
            dsTrangThaiHoan: [0, 10, 20],
            chiDinhTuLoaiDichVu: LOAI_DICH_VU.PHAU_THUAT_THU_THUAT,
          })
        );
      },
      () => {}
    );
  };

  const onEdit = (record) => () => {
    refSuaThongTin.current && refSuaThongTin.current.show(record);
  };

  const onChuyenVatTu = (data) => {
    refChuyenDichVu.current &&
      refChuyenDichVu.current.show(data, () => {
        getListDichVuVatTu({
          nbDotDieuTriId: data?.nbDotDieuTriId,
          chiDinhTuDichVuId: data?.chiDinhTuDichVuId,
          chiDinhTuLoaiDichVu: data?.chiDinhTuLoaiDichVu,
          dsTrangThaiHoan: [0, 10, 20],
        });
      });
  };

  const onSettings = () => {
    refSettings && refSettings.current.settings();
  };

  useEffect(() => {
    let dataSource = [];
    if (listDvVatTu.length) {
      const grouped = groupBy(listDvVatTu, "sttBo");
      Object.keys(grouped || []).map((key) => {
        if (key && key !== "undefined" && key !== "null") {
          let data = {
            tenDichVu: grouped[key][0].tenVatTuBo,
            vatTuBo: true,
            sttBo: grouped[key][0].sttBo,
            khoId: grouped[key][0].khoId,
            vatTuBoId: grouped[key][0].vatTuBoId,
            children: (grouped[key] || []).map((item) => ({
              ...item,
              vatTuCon: true,
            })),
          };
          dataSource.push(data);
        } else {
          dataSource.push(...grouped[key]);
        }
      });
      setState({ dataVatTu: dataSource });
    }
  }, [listDvVatTu]);

  const onCreate = (item) => {
    onThemVatTuCon &&
      onThemVatTuCon({
        sttBo: item?.sttBo,
        khoId: item?.khoId,
        vatTuBoId: item?.vatTuBoId,
      });
  };

  const onNgungYLenh = (record) => async (e) => {
    refModalNhapSoLuong.current &&
      refModalNhapSoLuong.current.show({}, async (value) => {
        await postDsDvThuocTraKho({
          loaiHangHoa: LOAI_DICH_VU.VAT_TU,
          payload: [
            {
              nbDichVuId: record.id,
              soLuong: value,
            },
          ],
        });
        refreshData();
      });
  };

  const columns = [
    {
      title: <HeaderSearch title={t("common.stt")} sort_key="index" />,
      width: "64px",
      dataIndex: "index",
      key: "index",
      align: "center",
      ignore: true,
      render: (item, row, index) => {
        return row?.vatTuCon ? null : index + 1;
      },
    },
    {
      title: <HeaderSearch title={t("danhMuc.maVatTu")} />,
      width: 120,
      dataIndex: "maDichVu",
      key: "maDichVu",
      colSpan: 1,
      i18Name: "danhMuc.maVatTu",
      show: true,
    },
    {
      title: <HeaderSearch title={t("khamBenh.donThuoc.tenVatTu")} />,
      width: "362px",
      dataIndex: "tenDichVu",
      key: "tenDichVu",
      colSpan: 1,
      i18Name: "khamBenh.donThuoc.tenVatTu",
      show: true,
      className: "tenDichVu",
      render: (item, data) => {
        let _tenThuocDungKem = data.tenDichVuChiDinh
          ? `- ĐK - ${data.tenDichVuChiDinh}`
          : "";
        let _tenDichVu = data.vatTuTaiSuDung
          ? data.moi
            ? `${item} (Mới)`
            : `${item} (Cũ)`
          : item;

        return `${_tenDichVu} ${_tenThuocDungKem}`;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("khamBenh.donThuoc.slChiDinh")}
          sort_key="soLuongYeuCau"
        />
      ),
      width: "110px",
      dataIndex: "soLuongYeuCau",
      key: "soLuongYeuCau",
      colSpan: 1,
      align: "center",
      i18Name: "khamBenh.donThuoc.slChiDinh",
      show: true,
      render: (item, list) => {
        return list.vatTuBo
          ? null
          : item + ` ${list?.tenDonViTinh ? list?.tenDonViTinh : ""}`;
      },
    },
    ...(isShowSoLuongDinhMuc
      ? [
          {
            title: (
              <HeaderSearch
                title={t("khamBenh.donThuoc.slDinhMuc")}
                sort_key="soLuongDinhMuc"
              />
            ),
            width: "110px",
            dataIndex: "soLuongDinhMuc",
            key: "soLuongDinhMuc",
            colSpan: 1,
            align: "center",
            i18Name: "khamBenh.donThuoc.slDinhMuc",
            show: true,
          },
        ]
      : []),
    {
      title: (
        <HeaderSearch
          title={t("khamBenh.donThuoc.slTra")}
          sort_key="soLuongTra"
        />
      ),
      width: "90px",
      dataIndex: "soLuongTra",
      key: "soLuongTra",
      colSpan: 1,
      align: "center",
      i18Name: "khamBenh.donThuoc.slTra",
      show: true,
      render: (item, list) => {
        return item;
      },
    },
    {
      title: <HeaderSearch title={t("khamBenh.donThuoc.slThucDung")} />,
      width: "90px",
      dataIndex: "soLuong",
      key: "soLuong",
      colSpan: 1,
      align: "center",
      i18Name: "khamBenh.donThuoc.slThucDung",
      show: true,
    },
    {
      title: <HeaderSearch title={t("pttt.kho")} sort_key="tenKho" />,
      width: "150px",
      dataIndex: "tenKho",
      key: "tenKho",
      colSpan: 1,
      i18Name: "pttt.kho",
      show: true,
      render: (item, list) => {
        return item;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("khamBenh.vatTu.bacSiChiDinh")}
          sort_key="tenBacSiChiDinh"
        />
      ),
      width: "150px",
      dataIndex: "tenBacSiChiDinh",
      key: "tenBacSiChiDinh",
      colSpan: 1,
      i18Name: "khamBenh.vatTu.bacSiChiDinh",
      show: true,
    },

    {
      title: (
        <HeaderSearch
          title={t("khamBenh.donThuoc.soPhieuLinh")}
          sort_key="soPhieuLinh"
        />
      ),
      width: "120px",
      dataIndex: "soPhieuLinh",
      key: "soPhieuLinh",
      colSpan: 1,
      i18Name: "khamBenh.donThuoc.soPhieuLinh",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("khamBenh.donThuoc.daDuyetPhat")}
          sort_key="phat"
        />
      ),
      width: "120px",
      dataIndex: "phat",
      key: "phat",
      colSpan: 1,
      align: "center",
      i18Name: "khamBenh.donThuoc.daDuyetPhat",
      show: true,
      render: (item, record) => {
        return <Checkbox checked={item} />;
      },
    },
    {
      title: <HeaderSearch title={t("common.tuTra")} />,
      dataIndex: "tuTra",
      key: "tuTra",
      width: 50,
      show: false,
      i18Name: "common.tuTra",
      align: "center",
      render: (item) => {
        return <Checkbox checked={item} />;
      },
    },
    {
      title: <HeaderSearch title={t("common.khongTinhTien")} />,
      width: 80,
      dataIndex: "khongTinhTien",
      key: "khongTinhTien",
      show: false,
      i18Name: "common.khongTinhTien",
      align: "center",
      render: (item) => {
        return <Checkbox checked={item} />;
      },
    },
    {
      title: <HeaderSearch title={t("common.thanhTien")} />,
      width: 120,
      dataIndex: "thanhTien",
      key: "thanhTien",
      show: false,
      i18Name: "common.thanhTien",
      render: (item) => {
        return item && item.formatPrice();
      },
    },
    {
      title: <HeaderSearch title={t("vatTu.kichCo")} sort_key="tenKichCoVt" />,
      width: 120,
      dataIndex: "tenKichCoVt",
      key: "tenKichCoVt",
      i18Name: "vatTu.kichCo",
      show: true,
    },
    {
      title: <HeaderSearch title={t("kho.dotXuat")} />,
      width: 80,
      dataIndex: "dotXuat",
      key: "dotXuat",
      i18Name: "kho.dotXuat",
      align: "center",
      show: true,
      render: (item, record) => {
        return (
          <Checkbox checked={record?.loaiChiDinh == LOAI_CHI_DINH.DOT_XUAT} />
        );
      },
    },
    {
      title: <HeaderSearch title={t("kho.boSung")} />,
      width: 80,
      dataIndex: "boSung",
      key: "boSung",
      i18Name: "kho.boSung",
      align: "center",
      show: true,
      render: (item, record) => {
        return (
          <Checkbox checked={record?.loaiChiDinh == LOAI_CHI_DINH.BO_SUNG} />
        );
      },
    },
    {
      title: (
        <HeaderSearch
          title={
            <>
              {t("common.khac")}
              <Setting refTable={refSettings} />
            </>
          }
        />
      ),
      width: 170,
      key: "action",
      align: "center",
      colSpan: 1,
      fixed: "right",
      ignore: true,
      render: (item, record, index) => {
        return disabledAll ? (
          <></>
        ) : (
          <div className="action-btn">
            {record.vatTuBo && !isReadonly && (
              <SVG.IcAdd
                onClick={() => onCreate(record)}
                className="ic-action"
              />
            )}
            {!record.vatTuBo && (
              <Tooltip title={t("tiepDon.suaThongTin")} placement="bottom">
                <SVG.IcEdit className="ic-action" onClick={onEdit(record)} />
              </Tooltip>
            )}
            {!isReadonly && !isNoiTru && (
              <Tooltip title={t("pttt.chuyenDichVu")} placement="bottom">
                <SVG.IcChuyenDichVu
                  className="ic-action"
                  onClick={() => onChuyenVatTu(record)}
                />
              </Tooltip>
            )}
            {!isReadonly && (
              <Tooltip title={t("tiepDon.xoaDichVu")} placement="bottom">
                <SVG.IcDelete
                  className="ic-action"
                  onClick={() => onDelete(record)}
                />
              </Tooltip>
            )}
            {record.trangThai < TRANG_THAI_THUOC.DA_PHAT.id &&
              record.trangThaiHoan === TRANG_THAI_HOAN.THUONG &&
              checkRole([ROLES["KHAM_BENH"].NGUNG_Y_LENH_KHONG_THUC_HIEN]) && (
                <>
                  <Tooltip title={t("khamBenh.ngungYLenh")} placement="bottom">
                    <SVG.IcXoaHoSo
                      className="ic-action"
                      onClick={onNgungYLenh(record)}
                      color="var(--color-red-primary)"
                    />
                  </Tooltip>
                </>
              )}
          </div>
        );
      },
    },
  ];

  const setRowClassName = (record) => {
    if (record.khongTinhTien) {
      return "green-color";
    }
    if (record.tuTra) {
      return "orange-color";
    }
  };

  return (
    <MainTable>
      <TableWrapper
        columns={columns}
        dataSource={state?.dataVatTu}
        scroll={{ x: 1000 }}
        tableName="table_PhauThuatThuThuat_VatTu"
        ref={refSettings}
        expandIconColumnIndex={1}
        expandable={{
          expandIcon: ({ expanded, onExpand, record }) =>
            record?.children?.length ? (
              expanded ? (
                <SVG.IcExpandDown
                  onClick={(e) => {
                    onExpand(record, e);
                    e.stopPropagation();
                  }}
                />
              ) : (
                <SVG.IcExpandRight
                  onClick={(e) => {
                    onExpand(record, e);
                    e.stopPropagation();
                  }}
                />
              )
            ) : null,
        }}
        styleWrap={(state?.dataVatTu || []).length > 5 ? { height: 300 } : {}}
        rowClassName={setRowClassName}
      />
      <ChinhSuaVatTu
        ref={refSuaThongTin}
        isShowSoLuongDinhMuc={isShowSoLuongDinhMuc}
        isReadonlyDvNoiTru={isReadonly}
      />
      <ModalChuyenDichVu ref={refChuyenDichVu} chinhSuaDichVu={themThongTin} />
      <ModalNhapSoLuong ref={refModalNhapSoLuong} />
    </MainTable>
  );
}

export default React.memo(Table);
