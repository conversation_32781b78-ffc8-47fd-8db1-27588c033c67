import React, {
  useState,
  useImperativeHandle,
  forwardRef,
  useRef,
  useEffect,
  useMemo,
} from "react";
import { Col, Input, message, Row, Form } from "antd";
import { Main } from "./styled";
import { useDispatch, useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import {
  DatePicker,
  Checkbox,
  Select,
  Button,
  ModalTemplate,
  DateTimePicker,
} from "components";
import moment from "moment";
import { useEnum, useListAll, useStore, useThietLap, useQueryAll } from "hooks";
import {
  ENUM,
  THIET_LAP_CHUNG,
  DS_TINH_CHAT_KHOA,
  ROLES,
} from "constants/index";
import { SVG } from "assets";
import { isArray } from "utils";
import { query } from "redux-store/stores";
import { checkRole } from "lib-utils/role-utils";

const ChinhSuaMau = ({ isReadonlyDvNoiTru, afterSubmit }, ref) => {
  const { t } = useTranslation();
  const refModal = useRef(null);
  const refCallback = useRef(null);
  const [form] = Form.useForm();
  const [listTrangThai] = useEnum(ENUM.TRANG_THAI_MAU);
  const [listNhomMau] = useEnum(ENUM.NHOM_MAU);
  const [listMucDo] = useEnum(ENUM.MUC_DO_CHE_PHAM_MAU);
  const [listXetNghiemCmv] = useEnum(ENUM.XET_NGHIEM_CMV);
  const [listDonViTocDoTruyen] = useEnum(ENUM.DON_VI_TOC_DO_TRUYEN);
  const [state, _setState] = useState({ data: [] });

  const setState = (data = {}) => {
    _setState((_state) => ({
      ..._state,
      ...data,
    }));
  };

  const { data: listKhoaTheoTaiKhoan } = useQueryAll(
    query.khoa.queryKhoaTheoTaiKhoan
  );

  const [
    dataHIEN_THI_TAT_CA_KHOA_SU_DUNG_MAU,
    isFetchedHIEN_THI_TAT_CA_KHOA_SU_DUNG_MAU,
  ] = useThietLap(THIET_LAP_CHUNG.HIEN_THI_TAT_CA_KHOA_SU_DUNG_MAU);
  const [dataAN_CHECKBOX_TU_TRA] = useThietLap(
    THIET_LAP_CHUNG.AN_CHECKBOX_TU_TRA
  );

  const { data: listAllKhoa } = useQueryAll(
    query.khoa.queryAllKhoa({
      params: {
        dsTinhChatKhoa: [
          DS_TINH_CHAT_KHOA.NOI_TRU,
          DS_TINH_CHAT_KHOA.PHAU_THUAT,
        ],
      },
      enabled:
        isFetchedHIEN_THI_TAT_CA_KHOA_SU_DUNG_MAU &&
        dataHIEN_THI_TAT_CA_KHOA_SU_DUNG_MAU?.eval(),
    })
  );

  const listKhoa = useMemo(() => {
    if (!isFetchedHIEN_THI_TAT_CA_KHOA_SU_DUNG_MAU) return [];
    return dataHIEN_THI_TAT_CA_KHOA_SU_DUNG_MAU?.eval()
      ? listAllKhoa
      : listKhoaTheoTaiKhoan;
  }, [
    dataHIEN_THI_TAT_CA_KHOA_SU_DUNG_MAU,
    listAllKhoa,
    listKhoaTheoTaiKhoan,
    isFetchedHIEN_THI_TAT_CA_KHOA_SU_DUNG_MAU,
  ]);

  const [dataMucDoChePhamMau, isFinish] = useThietLap(
    THIET_LAP_CHUNG.MUC_DO_CHE_PHAM_MAU
  );

  const listMucDoChePhamMau = useMemo(() => {
    if (isFinish && dataMucDoChePhamMau) {
      return listMucDo.filter((x) =>
        dataMucDoChePhamMau
          .split(",")
          .map((s) => Number(s.trim()))
          .includes(x.id)
      );
    }
    return listMucDo;
  }, [isFinish, dataMucDoChePhamMau, listMucDo]);

  const { listAllDuongDung = [] } = useSelector((state) => state.duongDung);

  const { getListChePhamMau, themThongTin } = useDispatch().chiDinhMau;
  const { getListAllDuongDung } = useDispatch().duongDung;
  const { getListAllNhanVien } = useDispatch().nhanVien;

  const listAllNhanVien = useStore("nhanVien.listAllNhanVien", []);

  const chiTietNguoiBenhNoiTru = useStore(
    "danhSachNguoiBenhNoiTru.chiTietNguoiBenhNoiTru"
  );
  const [listAllNguonKhacChiTra] = useListAll("nguonKhac", {}, true);

  useImperativeHandle(ref, () => ({
    show: (record = {}, callback) => {
      setState({ data: [], show: true, record: record });
      form.setFieldsValue({
        ...record,
        thoiGianThucHien: moment(record?.thoiGianThucHien),
        ...(record?.thoiGianDuyet && {
          thoiGianDuyet: moment(record?.thoiGianDuyet),
        }),
      });
      getListAllDuongDung({
        page: "",
        size: "",
        active: true,
        sort: "ten,asc",
      });
      getListAllNhanVien({ page: "", size: "", active: true });
      refCallback.current = callback;
      refModal.current && refModal.current.show();
    },
  }));

  useEffect(() => {
    if (isArray(listKhoa, true) && state.show && state.record) {
      const khoaSuDung = listKhoa.find(
        (i) => i.ma === chiTietNguoiBenhNoiTru?.maKhoaNb
      );
      if (khoaSuDung && !state.record.khoaThucHienId) {
        form.setFieldValue("khoaThucHienId", khoaSuDung.id);
      }
    }
  }, [listKhoa, state.record, state.show, chiTietNguoiBenhNoiTru]);

  const onCancel = () => {
    setState({ show: false, disabledButton: false });
    form.resetFields();
    refModal.current && refModal.current.hide();
  };

  const onSave = () => {
    form.submit();
  };

  const onHandledSubmit = (values) => {
    if (values.nbDichVu?.soLuong) {
      message.error(t("khamBenh.donThuoc.truongBatBuocDienSoLuong"));
      return;
    }
    if ((values.ghiChu || "").length > 1000) {
      message.error(t("khamBenh.donThuoc.nhapLuuYKhongQua1000KyTu"));
      return;
    }
    setState({ disabledButton: true });
    let payload = {
      id: state?.record?.id,
      nbDotDieuTriId: state?.record?.nbDotDieuTriId,
      mucDo: values?.mucDo,
      duongDungId: values?.duongDungId,
      maTuiMau: values.maTuiMau,
      nhomMau: values.nhomMau,
      nhomMauNb: values.nhomMauNb,
      nguoiPhat1Id: values.nguoiPhat1Id,
      khoaThucHienId: values.khoaThucHienId,
      nbDichVu: {
        ghiChu: values?.ghiChu,
        soLuong: values?.soLuong,
        tuTra: values?.tuTra,
        khongTinhTien: values?.khongTinhTien,
        thoiGianThucHien: moment(values.thoiGianThucHien).format(
          "YYYY/MM/DD HH:mm:ss"
        ),
        dichVuId: state?.record?.dichVuId,
        chiDinhTuDichVuId: state?.record?.chiDinhTuDichVuId,
        chiDinhTuLoaiDichVu: state?.record?.chiDinhTuLoaiDichVu,
        khoaChiDinhId: state?.record?.khoaChiDinhId,
        nguonKhacId: values?.nguonKhacId || null,
      },
      dsXetNghiemCmv: values?.dsXetNghiemCmv,
      cachTruyen: values?.cachTruyen,
      tocDoTruyen: values?.tocDoTruyen,
      donViTocDoTruyen: values?.donViTocDoTruyen,
    };
    themThongTin([payload])
      .then((s) => {
        if (afterSubmit) afterSubmit(s);
        else {
          getListChePhamMau({
            nbDotDieuTriId: state?.record.nbDotDieuTriId,
            chiDinhTuDichVuId: state?.record?.chiDinhTuDichVuId,
            chiDinhTuLoaiDichVu: 210,
          }).then((res) => {});
          if (refCallback.current) refCallback.current();
        }
        onCancel();
      })
      .catch(() => {});
  };
  const onTick = (key) => (e) => {
    if (key === "tuTra" && e) {
      form.setFieldsValue({ khongTinhTien: false });
    } else if (key === "khongTinhTien" && e) {
      form.setFieldsValue({ tuTra: false });
    }
  };
  return (
    <ModalTemplate
      ref={refModal}
      title={t("pttt.chinhSuaChePhamMau")}
      onCancel={onCancel}
      actionLeft={<Button.QuayLai onClick={onCancel} />}
      actionRight={
        !isReadonlyDvNoiTru && (
          <Button
            type="primary"
            onClick={onSave}
            minWidth={100}
            rightIcon={<SVG.IcSave />}
            disabled={state?.disabledButton}
          >
            {t("common.luu")}
          </Button>
        )
      }
      width={850}
    >
      <Main>
        <div className="info-content">
          <Form form={form} layout="vertical" onFinish={onHandledSubmit}>
            <Row>
              <Col span={12}>
                <Form.Item label={t("quanLyNoiTru.dvNoiTru.chePhamMau")}>
                  <Input value={state.record?.tenDichVu} disabled></Input>
                </Form.Item>
              </Col>
              <Col span={6}>
                <div className="form-item">
                  <Form.Item label={t("common.soLuong")} name="soLuong">
                    <Input disabled type="number"></Input>
                  </Form.Item>
                </div>
              </Col>
              <Col span={6}>
                <div className="form-item">
                  <Form.Item label={t("common.trangThai")} name="trangThai">
                    <Select disabled data={listTrangThai || []} />
                  </Form.Item>
                </div>
              </Col>
            </Row>

            <Row>
              <Col span={6}>
                <div className="form-item">
                  <Form.Item label={t("khoMau.nhomMauPhat")} name="nhomMau">
                    <Select
                      data={listNhomMau || []}
                      disabled={isReadonlyDvNoiTru}
                    />
                  </Form.Item>
                </div>
              </Col>
              <Col span={6}>
                <div className="form-item">
                  <Form.Item label={t("khoMau.nhomMauNb")} name="nhomMauNb">
                    <Select data={listNhomMau || []} disabled />
                  </Form.Item>
                </div>
              </Col>
              <Col span={3}>
                <div className="form-item">
                  <Form.Item
                    label={t("quanLyNoiTru.dvNoiTru.theTich")}
                    name="theTich"
                  >
                    <Input disabled></Input>
                  </Form.Item>
                </div>
              </Col>

              <Col span={3}>
                <div className="form-item">
                  <Form.Item label={t("vatTu.dvt")} name="tenDonViTinh">
                    <Input disabled></Input>
                  </Form.Item>
                </div>
              </Col>

              <Col span={6}>
                <div className="form-item">
                  <Form.Item
                    label={t("quanLyNoiTru.mau.maTuiMau")}
                    name="maTuiMau"
                  >
                    <Input disabled></Input>
                  </Form.Item>
                </div>
              </Col>
            </Row>

            <Row>
              <Col span={6}>
                <div className="form-item">
                  <Form.Item label={t("common.duongDung")} name="duongDungId">
                    <Select
                      data={listAllDuongDung || []}
                      placeholder={t("common.chonDuongDung")}
                      disabled={isReadonlyDvNoiTru}
                    />
                  </Form.Item>
                </div>
              </Col>

              <Col span={6}>
                <div className="form-item">
                  <Form.Item
                    label={t("common.thoiGianThucHien")}
                    name="thoiGianThucHien"
                  >
                    <DateTimePicker
                      placeholder={t("common.chonThoiGian")}
                      disabled={isReadonlyDvNoiTru}
                    />
                  </Form.Item>
                </div>
              </Col>

              <Col span={6}>
                <div className="form-item">
                  <Form.Item
                    label={t("quanLyNoiTru.dvNoiTru.nguoiPhatMau")}
                    name="nguoiPhat1Id"
                  >
                    <Select data={listAllNhanVien || []} disabled />
                  </Form.Item>
                </div>
              </Col>

              <Col span={6}>
                <div className="form-item">
                  <Form.Item
                    label={t("quanLyNoiTru.dvNoiTru.thoiGianPhat")}
                    name="thoiGianDuyet"
                  >
                    <DatePicker
                      format={"DD-MM-YYYY HH:mm:ss"}
                      placeholder=" "
                      disabled
                    />
                  </Form.Item>
                </div>
              </Col>
            </Row>

            <Row>
              <Col span={12}>
                <div className="form-item">
                  <Form.Item label={t("khoMau.mucDo")} name="mucDo">
                    <Select
                      data={listMucDoChePhamMau}
                      placeholder={t("khoMau.mucDo")}
                      disabled={isReadonlyDvNoiTru}
                    ></Select>
                  </Form.Item>
                </div>
              </Col>
              <Col span={12}>
                <div className="form-item">
                  <Form.Item
                    label={t("khoMau.khoaSuDung")}
                    name="khoaThucHienId"
                  >
                    <Select
                      data={listKhoa}
                      placeholder={t("khoMau.khoaSuDung")}
                      disabled={isReadonlyDvNoiTru}
                    ></Select>
                  </Form.Item>
                </div>
              </Col>
            </Row>
            <Row>
              <Col span={12}>
                <div className="form-item">
                  <Form.Item label={t("common.cachTruyen")} name="cachTruyen">
                    <Input.TextArea
                      placeholder={t("common.cachTruyen")}
                      disabled={isReadonlyDvNoiTru}
                    ></Input.TextArea>
                  </Form.Item>
                </div>
              </Col>
              <Col span={12}>
                <div className="form-item">
                  <Form.Item label={t("common.ghiChu")} name="ghiChu">
                    <Input.TextArea
                      placeholder={t("common.noiDung")}
                      disabled={isReadonlyDvNoiTru}
                    ></Input.TextArea>
                  </Form.Item>
                </div>
              </Col>
              <Col span={12}>
                <div className="form-item">
                  <Form.Item
                    label={t("quanLyNoiTru.capPhatThuoc.tocDoTruyen")}
                    name="tocDoTruyen"
                  >
                    <Input type="number" disabled={isReadonlyDvNoiTru} />
                  </Form.Item>
                </div>
              </Col>
              <Col span={12}>
                <div className="form-item">
                  <Form.Item
                    label={t("quanLyNoiTru.capPhatThuoc.donViTocDoTruyen")}
                    name="donViTocDoTruyen"
                  >
                    <Select
                      data={listDonViTocDoTruyen}
                      disabled={isReadonlyDvNoiTru}
                    />
                  </Form.Item>
                </div>
              </Col>
              <Col span={12}>
                <Row>
                  {!dataAN_CHECKBOX_TU_TRA?.eval() && (
                    <Col span={8}>
                      <div className="form-item">
                        <Form.Item
                          label=" "
                          name="tuTra"
                          valuePropName="checked"
                        >
                          <Checkbox
                            disabled={isReadonlyDvNoiTru}
                            onChange={onTick("tuTra")}
                          >
                            {t("common.tuTra")}
                          </Checkbox>
                        </Form.Item>
                      </div>
                    </Col>
                  )}
                  <Col span={8}>
                    <div className="form-item">
                      <Form.Item
                        label=" "
                        name="khongTinhTien"
                        valuePropName="checked"
                      >
                        <Checkbox
                          disabled={
                            isReadonlyDvNoiTru ||
                            !checkRole([
                              ROLES["QUAN_LY_NOI_TRU"].TICK_KHONG_TINH_TIEN,
                            ])
                          }
                          onChange={onTick("khongTinhTien")}
                        >
                          {t("common.khongTinhTien")}
                        </Checkbox>
                      </Form.Item>
                    </div>
                  </Col>
                </Row>
              </Col>
            </Row>
            <Row>
              <Col span={12}>
                <div className="form-item">
                  <Form.Item label={t("danhMuc.nguonKhac")} name="nguonKhacId">
                    <Select
                      data={listAllNguonKhacChiTra}
                      placeholder={t("danhMuc.nguonKhac")}
                      disabled={isReadonlyDvNoiTru}
                    />
                  </Form.Item>
                </div>
              </Col>
              <Col span={12}>
                <div className="form-item">
                  <Form.Item label={t("kho.tenKho")}>
                    <Input value={state.record?.tenKho} disabled></Input>
                  </Form.Item>
                </div>
              </Col>
            </Row>
            <Row>
              <Col span={12}>
                <div className="form-item">
                  <Form.Item
                    label={t("khoMau.dieuCheBoSung")}
                    name="dsXetNghiemCmv"
                  >
                    <Select
                      data={listXetNghiemCmv}
                      placeholder={t("khoMau.dieuCheBoSung")}
                      disabled={isReadonlyDvNoiTru}
                      mode="multiple"
                      showArrow
                    />
                  </Form.Item>
                </div>
              </Col>
            </Row>
          </Form>
        </div>
      </Main>
    </ModalTemplate>
  );
};

export default forwardRef(ChinhSuaMau);
