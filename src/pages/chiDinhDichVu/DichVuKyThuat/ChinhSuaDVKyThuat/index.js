import React, {
  useState,
  useImperativeHandle,
  forwardRef,
  useRef,
  useEffect,
  useMemo,
} from "react";
import {
  Col,
  Input,
  Row,
  Form,
  InputNumber,
  Select as SelectAntd,
  message,
} from "antd";
import { Main } from "./styled";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import {
  Checkbox,
  DateTimePicker,
  Select,
  Button,
  ModalTemplate,
} from "components";
import moment from "moment";
import { useSelector } from "react-redux";
import { useListAll, useLoading, useStore, useEnum, useThietLap } from "hooks";
import { LOAI_DICH_VU, ROLES, ENUM, THIET_LAP_CHUNG } from "constants/index";
import { SVG } from "assets";
import { checkRole, checkRoleOr } from "lib-utils/role-utils";
import nbDvXetNghiemProvider from "data-access/nb-dv-xet-nghiem-provider";
import useThietLapGiaoDien from "pages/khamBenh/ChiDinhDichVu/ModalChiDinhDichVu/useThietLapGiaoDien";
import useViTriChamCong, {
  KEYS_VI_TRI_CHAM_CONG,
} from "pages/khamBenh/ChiDinhDichVu/ModalChiDinhDichVu/useViTriChamCong";

const SCREEN = {
  "QUAN_LY_NOI_TRU/DV_NGOAI_TRU": "QUAN_LY_NOI_TRU/DV_NGOAI_TRU",
  "QUAN_LY_NOI_TRU/DV_NOI_TRU": "QUAN_LY_NOI_TRU/DV_NOI_TRU",
};

const ChinhSuaDVKyThuat = forwardRef(
  ({ isReadonlyDvNoiTru, isDvXetNghiemMauDiKem, screen }, ref) => {
    const { t } = useTranslation();
    const refModal = useRef(null);
    const refCallback = useRef(null);
    const [form] = Form.useForm();
    const lanThu = Form.useWatch("lanThu", form);
    const thoiGianCoKetQua = Form.useWatch("thoiGianCoKetQua", form);
    const [state, _setState] = useState({ data: [] });
    const setState = (data = {}) => {
      _setState((_state) => ({
        ..._state,
        ...data,
      }));
    };
    const { showLoading, hideLoading } = useLoading();
    const {
      chiDinhKhamBenh: { themThongTinDV, getDsSoPhieuCLS },
      phongThucHien: { onSearchParams: searchPhongThucHien },
      loaiDoiTuongLoaiHinhTT: { getListLoaiDoiTuongTT },
      pttt: { getDsPhuCapPTTTChiTiet },
    } = useDispatch();
    const [dataAN_CHECKBOX_TU_TRA] = useThietLap(
      THIET_LAP_CHUNG.AN_CHECKBOX_TU_TRA
    );

    const listPhongThucHien = useSelector(
      (state) => state.phongThucHien.listData
    );

    const listLoaiHinhThanhToanCuaDoiTuong = useStore(
      "loaiDoiTuongLoaiHinhTT.listLoaiHinhThanhToanCuaDoiTuong",
      []
    );

    const thongTinCoBan = useStore("nbDotDieuTri.thongTinCoBan", {});
    const listMucDichSuDung = useStore("mucDichSuDung.listMucDichSuDung", []);
    const [listAllNhanVien] = useListAll("nhanVien", {}, true);
    const [listAllNguonKhacChiTra] = useListAll("nguonKhac", {}, true);
    const dsPhuCapPTTT = useStore("pttt.dsPhuCapPTTT", []);
    const soPhieuCls = useSelector((state) => state.chiDinhKhamBenh.soPhieuCls);

    const [listDanhGiaLaoTrenKq] = useEnum(ENUM.DANH_GIA_LAO_TREN_KQ);
    const [listDanhGiaKetQuaLaoKhang] = useEnum(
      ENUM.DANH_GIA_KET_QUA_LAO_KHANG
    );
    const [listGiaiDoanLao] = useEnum(ENUM.GIAI_DOAN_LAO);
    const [listPhanLoaiPTTT] = useEnum(ENUM.PHAN_LOAI_PTTT);

    const { DATA_TEN_HIEN_THI, dataThietLap } = useThietLapGiaoDien();

    const [renderDataListVTCC, checkTonTaiThietLap] = useViTriChamCong({
      show: true,
    });

    const listAllLoaiHinhThanhToan = useMemo(() => {
      return listLoaiHinhThanhToanCuaDoiTuong.map((item) => ({
        ...item,
        id: item.loaiHinhThanhToanId,
        ten: item.tenLoaiHinhThanhToan,
      }));
    }, [listLoaiHinhThanhToanCuaDoiTuong]);

    const dataPhongThucHien = useMemo(() => {
      return (listPhongThucHien || []).map((item) => ({
        id: item.phongId,
        ten: `${item?.ma} - ${item?.ten}`,
        dichVuId: item.dichVuId,
      }));
    }, [listPhongThucHien]);

    const isTabQuanLyNoiTruDvNgoaiTru =
      screen === SCREEN["QUAN_LY_NOI_TRU/DV_NGOAI_TRU"];
    const isTabQuanLyNoiTruDvNoiTru =
      screen === SCREEN["QUAN_LY_NOI_TRU/DV_NOI_TRU"];

    useImperativeHandle(ref, () => ({
      show: (record = {}, callback) => {
        setState({
          data: [],
          show: true,
          record: record,
          disabledField: record.thanhToan, // !record.thanhToan && [155, 160].includes(record.trangThai),
          disabledField2: ![15, 20, 30, 40, 25, 35, 43, 38, 46, 62].includes(
            record.trangThai
          ),
          disabledSoLuong:
            record?.doiDichVuKhiCoKetQua &&
            [20, 30, 40].includes(record?.loaiDichVu) &&
            ![100, 120, 130].includes(record.trangThai),
        });
        form.setFieldsValue({
          ...record,
          soPhieu: record?.soPhieuId,
          thoiGianThucHien: record?.thoiGianThucHien
            ? moment(record.thoiGianThucHien)
            : null,
          thoiGianTiepNhan: record?.thoiGianTiepNhan
            ? moment(record.thoiGianTiepNhan)
            : null,
          thoiGianCoKetQua: record?.thoiGianCoKetQua
            ? moment(record.thoiGianCoKetQua)
            : null,
          thoiGianKetLuan: record?.thoiGianKetLuan
            ? moment(record.thoiGianKetLuan)
            : null,
        });

        if (
          [
            LOAI_DICH_VU.PHAU_THUAT_THU_THUAT,
            LOAI_DICH_VU.CDHA,
            LOAI_DICH_VU.XET_NGHIEM,
          ].includes(record.loaiDichVu) &&
          (record.phanLoaiPtTt || record.phanLoaiPtTt == 0)
        ) {
          getDsPhuCapPTTTChiTiet({
            dichVuId: record.dichVuId,
            nhomDichVuCap2Id: record.nhomDichVuCap2Id,
            nhomDichVuCap1Id: record.nhomDichVuCap1Id,
            phanLoai: record.phanLoaiPtTt,
            ngay: moment().format("YYYY-MM-DD"),
          });
        }

        searchPhongThucHien({
          dsDichVuId: [record.dichVuId],
          khoaChiDinhId: record?.khoaChiDinhId,
          loaiDoiTuongId: thongTinCoBan?.loaiDoiTuongId,
          loaiHinhThanhToanId: record?.loaiHinhThanhToanId,
          nbDotDieuTriId: record?.nbDotDieuTriId,
          gioiTinh: thongTinCoBan?.gioiTinh,
          doiTuongKcb: thongTinCoBan?.doiTuongKcb,
        });
        getDsSoPhieuCLS({
          loaiDichVu: record.loaiDichVu,
          nbDotDieuTriId: record.nbDotDieuTriId,
        });

        getListLoaiDoiTuongTT({
          active: true,
          page: "",
          size: "",
          dsDichVuId: record.dichVuId,
          loaiDoiTuongId: thongTinCoBan?.loaiDoiTuongId,
          khoaChiDinhId: record?.khoaChiDinhId,
          ngayThucHien:
            record?.thoiGianThucHien &&
            moment(record?.thoiGianThucHien).format("YYYY-MM-DD"),
          ngayVaoVien:
            thongTinCoBan?.thoiGianVaoVien &&
            moment(thongTinCoBan?.thoiGianVaoVien).format("YYYY-MM-DD"),
          ngaySinh:
            thongTinCoBan?.ngaySinh &&
            moment(thongTinCoBan?.ngaySinh).format("YYYY-MM-DD"),
          doiTuongKcb: thongTinCoBan?.doiTuongKcb,
        });

        refModal.current && refModal.current.show();
        refCallback.current = callback;
      },
    }));
    const handleChange = (key) => (e) => {
      if (key === "tuTra" && e) {
        form.setFieldsValue({ khongTinhTien: false });
      } else if (key === "khongTinhTien" && e) {
        form.setFieldsValue({ tuTra: false });
      }
      if (key === "lanThu" && (e.target.value == "" || e.target.value < 2)) {
        form.setFieldsValue({ lyDo: "" });
      }
    };

    const onCancel = () => {
      setState({ show: false, disabledButton: false });
      form.resetFields();
      refModal.current && refModal.current.hide();
    };
    const onSave = () => {
      form.submit();
    };

    const onHandledSubmit = (values) => {
      form.validateFields().then((values) => {
        let {
          ghiChu,
          tuTra,
          soLuong,
          thoiGianThucHien,
          thoiGianTiepNhan,
          thoiGianCoKetQua,
          thoiGianKetLuan,
          khongTinhTien,
          loaiHinhThanhToanId,
          phongThucHienId,
          mucDichId,
          tuVanVienId,
          nguonKhacId,
          mauSo,
          lanThu,
          lyDo,
          danhGiaLaoTrenXq,
          danhGiaKetQuaLaoKhang,
          danhGiaKhac,
          giaiDoanLao,
          ...rest
        } = values;

        let obj = {
          body:
            state.record.loaiDichVu === LOAI_DICH_VU.NGOAI_DIEU_TRI ||
            state.record.loaiDichVu === LOAI_DICH_VU.THUOC
              ? [
                  {
                    id: state.record.id,
                    nbDichVu: {
                      ghiChu,
                      tuTra,
                      soLuong,
                      khongTinhTien,
                      dichVuId: state.record.dichVuId,
                      phongThucHienId,
                      thoiGianThucHien,
                      nguonKhacId: nguonKhacId || null,
                      ...(state.record.loaiDichVu ===
                      LOAI_DICH_VU.NGOAI_DIEU_TRI
                        ? { loaiHinhThanhToanId }
                        : {}),
                    },
                    nbDvKyThuat: {
                      phongThucHienId,
                      tuVanVienId,
                    },
                  },
                ]
              : {
                  thoiGianCoKetQua,
                  thoiGianKetLuan:
                    state.record?.loaiDichVu === LOAI_DICH_VU.KHAM
                      ? isTabQuanLyNoiTruDvNgoaiTru
                        ? thoiGianCoKetQua
                        : isTabQuanLyNoiTruDvNoiTru
                        ? thoiGianKetLuan
                        : null
                      : null,
                  mauSo,
                  lanThu,
                  lyDo,
                  danhGiaLaoTrenXq,
                  danhGiaKetQuaLaoKhang,
                  danhGiaKhac,
                  giaiDoanLao,
                  nbDvKyThuat: {
                    phongThucHienId,
                    tuVanVienId,
                    thoiGianTiepNhan,
                    ...rest,
                  },
                  nbDichVu: {
                    ghiChu,
                    tuTra,
                    soLuong,
                    thoiGianThucHien,
                    khongTinhTien,
                    loaiHinhThanhToanId: loaiHinhThanhToanId || null,
                    mucDichId,
                    nguonKhacId: nguonKhacId || null,
                  },
                  ...(state.record?.loaiDichVu ===
                  LOAI_DICH_VU.PHAU_THUAT_THU_THUAT
                    ? { phanLoaiPtTt: rest.phanLoaiPtTt }
                    : {}),
                  ...([
                    LOAI_DICH_VU.PHAU_THUAT_THU_THUAT,
                    LOAI_DICH_VU.CDHA,
                  ].includes(state.record?.loaiDichVu)
                    ? {
                        ptTtNguoiThucHien: KEYS_VI_TRI_CHAM_CONG.filter(
                          (key) =>
                            !["nguoiTiepNhanId", "dieuDuongId"].includes(key) &&
                            dataThietLap.includes(key) &&
                            checkTonTaiThietLap(
                              key,
                              dsPhuCapPTTT,
                              state?.record?.loaiDichVu
                            )
                        ).reduce(
                          (accumulator, currentValue) => ({
                            ...accumulator,
                            ...(rest[currentValue] !==
                            state?.record?.[currentValue]
                              ? { [currentValue]: rest[currentValue] || null }
                              : {}),
                          }),
                          {}
                        ),
                      }
                    : {}),
                  ...(state?.record?.loaiDichVu == LOAI_DICH_VU.XET_NGHIEM
                    ? {
                        phuThucHien1Id: rest?.phuPtv1Id || null,
                        phuThucHien2Id: rest?.phuPtv2Id || null,
                        phuThucHien3Id: rest?.phuPtv3Id || null,
                        thanhVienKhacId: rest?.thanhVienKhacId || null,
                        nguoiThucHien2Id: rest?.nguoiThucHien2Id || null,
                      }
                    : {}),
                },
          id: state.record.id,
          loaiDichVu: state.record.loaiDichVu,
        };

        showLoading();
        if (isDvXetNghiemMauDiKem) {
          nbDvXetNghiemProvider
            .themThongTin([
              {
                id: obj.id,
                ...obj.body,
              },
            ])
            .then((s) => {
              if (refCallback.current) refCallback.current();
              message.success(t("pttt.themThongTinThanhCong"));
              onCancel();
            })
            .catch((e) => {
              message.error(
                t(e?.message || "quanLyNoiTru.themThongTinThatBai")
              );
            })
            .finally(() => {
              hideLoading();
            });
        } else {
          themThongTinDV(obj)
            .then((s) => {
              if (s.code === 0) {
                if (refCallback.current) refCallback.current();
                onCancel();
              }
            })
            .finally(() => {
              hideLoading();
            });
        }
      });
    };
    const blockInvalidChar = (e) =>
      ["e", "E", "+", "-", "."].includes(e.key) && e.preventDefault();

    const listKeys = KEYS_VI_TRI_CHAM_CONG.filter(
      (key) =>
        dataThietLap.includes(key) &&
        checkTonTaiThietLap(key, dsPhuCapPTTT, state?.record?.loaiDichVu)
    );

    return (
      <ModalTemplate
        ref={refModal}
        title={t("common.thongTinDichVu")}
        onCancel={onCancel}
        width={650}
        actionLeft={<Button.QuayLai onClick={onCancel} />}
        actionRight={
          !isReadonlyDvNoiTru && (
            <Button
              type="primary"
              minWidth={100}
              rightIcon={<SVG.IcSave />}
              disabled={state?.disabledButton}
              onClick={onSave}
            >
              {t("common.luu")}
            </Button>
          )
        }
      >
        <Main>
          <div className="info-content">
            <Form
              form={form}
              layout="vertical"
              onValuesChange={(changedValues) => {
                if (changedValues.hasOwnProperty("phanLoaiPtTt")) {
                  getDsPhuCapPTTTChiTiet({
                    dichVuId: state?.record?.dichVuId,
                    nhomDichVuCap2Id: state?.record?.nhomDichVuCap2Id,
                    nhomDichVuCap1Id: state?.record?.nhomDichVuCap1Id,
                    phanLoai: changedValues.phanLoaiPtTt,
                    ngay: moment().format("YYYY-MM-DD"),
                  });
                }
              }}
              onFinish={onHandledSubmit}
            >
              <Row gutter={8}>
                <Col span={12}>
                  <Form.Item
                    label={t("common.soLuong")}
                    name="soLuong"
                    rules={[
                      {
                        required: true,
                        message: t("common.vuiLongNhapSoLuong"),
                      },
                      {
                        pattern: new RegExp(
                          /^(0*[1-9][0-9]*(\.[0-9]+)?|0+\.[0-9]*[1-9][0-9]*)$/
                        ),
                        message: t("pttt.yeuCauNhapSoLuongLonHon0"),
                      },
                    ]}
                  >
                    <InputNumber
                      disabled={
                        (!state?.disabledSoLuong &&
                          state?.disabledField2 &&
                          !checkRole([
                            ROLES["QUAN_LY_NOI_TRU"].SUA_SO_LUONG,
                          ])) ||
                        isReadonlyDvNoiTru
                      }
                      min={0}
                      placeholder={t("common.vuiLongNhapSoLuong")}
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label={t("khamBenh.chiDinh.phongThucHien")}
                    name="phongThucHienId"
                  >
                    <SelectAntd
                      // disabled={state?.disabledField2 || isReadonlyDvNoiTru}
                      allowClear
                      placeholder={t("khamBenh.chiDinh.chonTenPhongThucHien")}
                    >
                      {dataPhongThucHien.map((item, index) => {
                        return (
                          <SelectAntd.Option key={index} value={item?.id}>
                            {`${item?.ten}`}
                          </SelectAntd.Option>
                        );
                      })}
                    </SelectAntd>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item label={t("common.luuY")} name="ghiChu">
                    <Input
                      className="input-option"
                      placeholder={t("khamBenh.chiDinh.vuiLongNhapLuuY")}
                      disabled={isReadonlyDvNoiTru}
                    />
                  </Form.Item>
                </Col>
                {![LOAI_DICH_VU.KHAM, LOAI_DICH_VU.NGOAI_DIEU_TRI].includes(
                  state?.record?.loaiDichVu
                ) && (
                  <Col span={12}>
                    <Form.Item
                      label={t("khamBenh.chiDinh.soPhieu")}
                      name="soPhieu"
                    >
                      <Select
                        disabled={state?.disabledField2 || isReadonlyDvNoiTru}
                        data={soPhieuCls}
                        placeholder={t("khamBenh.chiDinh.chonSoPhieu")}
                      />
                    </Form.Item>
                  </Col>
                )}
                <Col
                  span={12}
                  style={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "space-between",
                  }}
                >
                  {!dataAN_CHECKBOX_TU_TRA?.eval() && (
                    <Form.Item label=" " name="tuTra" valuePropName="checked">
                      <Checkbox
                        disabled={
                          listMucDichSuDung.length || isReadonlyDvNoiTru
                        }
                        onChange={handleChange("tuTra")}
                      >
                        {t("khamBenh.chiDinh.tuTra")}
                      </Checkbox>
                    </Form.Item>
                  )}
                  <Form.Item
                    label=" "
                    name="khongTinhTien"
                    valuePropName="checked"
                  >
                    <Checkbox
                      disabled={
                        listMucDichSuDung.length ||
                        isReadonlyDvNoiTru ||
                        ([20, 30, 40, 45, 60].includes(
                          state?.record?.loaiDichVu
                        )
                          ? !checkRoleOr([
                              ROLES["QUAN_LY_NOI_TRU"]
                                .CHINH_SUA_KHONG_TINH_TIEN_POPUP_CHI_DINH_DVKT,
                            ])
                          : false) ||
                        !checkRole([
                          ROLES["QUAN_LY_NOI_TRU"].TICK_KHONG_TINH_TIEN,
                        ])
                      }
                      onChange={handleChange("khongTinhTien")}
                    >
                      {t("common.khongTinhTien")}
                    </Checkbox>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label={t("quanLyNoiTru.ngayThucHien")}
                    name="thoiGianThucHien"
                  >
                    <DateTimePicker
                      disabled={state?.disabledField || isReadonlyDvNoiTru}
                    ></DateTimePicker>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label={t("theoDoiDieuTri.ngayTiepNhan")}
                    name="thoiGianTiepNhan"
                  >
                    <DateTimePicker
                      disabled={state?.disabledField || isReadonlyDvNoiTru}
                    ></DateTimePicker>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label={t("khamBenh.chiDinh.loaiHinhThanhToan")}
                    name="loaiHinhThanhToanId"
                  >
                    <Select
                      data={listAllLoaiHinhThanhToan}
                      placeholder={t("khamBenh.chiDinh.loaiHinhThanhToan")}
                      disabled={listMucDichSuDung.length || isReadonlyDvNoiTru}
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item label="TT35" name="mucDichId">
                    <Select
                      disabled={state?.disabledField || isReadonlyDvNoiTru}
                      data={listMucDichSuDung}
                      placeholder="TT35"
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item label={t("hsba.tuVanVien")} name="tuVanVienId">
                    <Select
                      disabled={state?.disabledField || isReadonlyDvNoiTru}
                      data={listAllNhanVien}
                      valueNumber={true}
                      placeholder={t("hsba.tuVanVien")}
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item label={t("danhMuc.nguonKhac")} name="nguonKhacId">
                    <Select
                      data={listAllNguonKhacChiTra}
                      placeholder={t("danhMuc.nguonKhac")}
                      disabled={isReadonlyDvNoiTru}
                    />
                  </Form.Item>
                </Col>
                {![LOAI_DICH_VU.NGOAI_DIEU_TRI].includes(
                  state?.record?.loaiDichVu
                ) &&
                  !(
                    [LOAI_DICH_VU.KHAM].includes(state?.record?.loaiDichVu) &&
                    isTabQuanLyNoiTruDvNgoaiTru
                  ) && (
                    <Col span={12}>
                      <Form.Item
                        label={t("quanLyNoiTru.dvNoiTru.thoiGianCoKetQua")}
                        name="thoiGianCoKetQua"
                      >
                        <DateTimePicker
                          disabled={
                            isTabQuanLyNoiTruDvNgoaiTru || isReadonlyDvNoiTru
                          }
                        />
                      </Form.Item>
                    </Col>
                  )}

                {state?.record?.loaiDichVu === LOAI_DICH_VU.KHAM &&
                  isTabQuanLyNoiTruDvNgoaiTru && (
                    <Col span={12}>
                      <Form.Item
                        label={t("hsba.thoiGianKetLuan")}
                        name="thoiGianCoKetQua"
                      >
                        <DateTimePicker disabled={!thoiGianCoKetQua} />
                      </Form.Item>
                    </Col>
                  )}
                {state?.record?.loaiDichVu === LOAI_DICH_VU.KHAM &&
                  isTabQuanLyNoiTruDvNoiTru && (
                    <Col span={12}>
                      <Form.Item
                        label={t("hsba.thoiGianKetLuan")}
                        name="thoiGianKetLuan"
                      >
                        <DateTimePicker />
                      </Form.Item>
                    </Col>
                  )}
                {state?.record?.loaiDichVu === LOAI_DICH_VU.XET_NGHIEM &&
                  [1, 2].includes(state?.record?.loaiXnVitimes) && (
                    <Col span={12}>
                      <Form.Item
                        label={t("quanLyNoiTru.dvNoiTru.mauSo")}
                        name="mauSo"
                        rules={[
                          {
                            pattern: new RegExp(
                              /^(0*[1-9][0-9]*(\.[0-9]+)?|0+\.[0-9]*[1-9][0-9]*)$/
                            ),
                            message: t("quanLyNoiTru.yeuCauNhapTitleLonHon0", {
                              title: t(
                                "quanLyNoiTru.dvNoiTru.mauSo"
                              ).toLowerCase(),
                            }),
                          },
                          {
                            pattern: new RegExp(/^.{1,3}$/),
                            message: t("danhMuc.vuiLongNhapGiaTriKhongQuaKyTu")
                              .replace(
                                "{0}",
                                t("quanLyNoiTru.dvNoiTru.mauSo").toLowerCase()
                              )
                              .replace("{1}", 3),
                          },
                        ]}
                      >
                        <Input
                          className="input-option hidden-arrow"
                          placeholder={`${t("danhMuc.vuiLongNhapTitle", {
                            title: t(
                              "quanLyNoiTru.dvNoiTru.mauSo"
                            ).toLowerCase(),
                          })}`}
                          onKeyDown={blockInvalidChar}
                          type="number"
                          min={0}
                          disabled={isReadonlyDvNoiTru}
                        />
                      </Form.Item>
                    </Col>
                  )}
                {state?.record?.loaiDichVu === LOAI_DICH_VU.XET_NGHIEM &&
                  state?.record?.loaiXnVitimes === 6 && (
                    <>
                      <Col span={12}>
                        <Form.Item
                          label={t("quanLyNoiTru.dvNoiTru.lanThu")}
                          name="lanThu"
                          rules={[
                            {
                              pattern: new RegExp(
                                /^(0*[1-9][0-9]*(\.[0-9]+)?|0+\.[0-9]*[1-9][0-9]*)$/
                              ),
                              message: t(
                                "quanLyNoiTru.yeuCauNhapTitleLonHon0",
                                {
                                  title: t("quanLyNoiTru.dvNoiTru.lanThu"),
                                }
                              ),
                            },
                            {
                              pattern: new RegExp(/^.{1,3}$/),
                              message: t(
                                "danhMuc.vuiLongNhapGiaTriKhongQuaKyTu"
                              )
                                .replace(
                                  "{0}",
                                  t("quanLyNoiTru.dvNoiTru.lanThu")
                                )
                                .replace("{1}", 3),
                            },
                          ]}
                        >
                          <Input
                            className="input-option hidden-arrow"
                            placeholder={`${t("danhMuc.vuiLongNhapTitle", {
                              title: t("quanLyNoiTru.dvNoiTru.lanThu"),
                            })}`}
                            onKeyDown={blockInvalidChar}
                            type="number"
                            min={0}
                            onChange={handleChange("lanThu")}
                            disabled={isReadonlyDvNoiTru}
                          />
                        </Form.Item>
                      </Col>
                      <Col span={12}>
                        <Form.Item
                          label={t("quanLyNoiTru.dvNoiTru.lyDo")}
                          name="lyDo"
                          rules={[
                            {
                              max: 255,
                              message: t(
                                "danhMuc.vuiLongNhapGiaTriKhongQuaKyTu"
                              )
                                .replace(
                                  "{0}",
                                  t("quanLyNoiTru.dvNoiTru.lyDo").toLowerCase()
                                )
                                .replace("{1}", 255),
                            },
                          ]}
                        >
                          <Input
                            className="input-option"
                            placeholder={`${t("danhMuc.vuiLongNhapTitle", {
                              title: t(
                                "quanLyNoiTru.dvNoiTru.lyDo"
                              ).toLowerCase(),
                            })}`}
                            disabled={
                              (lanThu && lanThu >= 2 ? false : true) ||
                              isReadonlyDvNoiTru
                            }
                          />
                        </Form.Item>
                      </Col>
                    </>
                  )}
                {state?.record?.loaiDichVu === LOAI_DICH_VU.CDHA &&
                  state?.record?.guiVitimes === true && (
                    <>
                      <Col span={12}>
                        <Form.Item
                          label={t("quanLyNoiTru.dvNoiTru.danhGiaLaoTrenXq")}
                          name="danhGiaLaoTrenXq"
                        >
                          <Select
                            data={listDanhGiaLaoTrenKq}
                            placeholder={t(
                              "quanLyNoiTru.dvNoiTru.danhGiaLaoTrenXq"
                            )}
                            disabled={isReadonlyDvNoiTru}
                          />
                        </Form.Item>
                      </Col>
                      <Col span={12}>
                        <Form.Item
                          label={t(
                            "quanLyNoiTru.dvNoiTru.danhGiaKetQuaLaoKhang"
                          )}
                          name="danhGiaKetQuaLaoKhang"
                        >
                          <Select
                            data={listDanhGiaKetQuaLaoKhang}
                            placeholder={t(
                              "quanLyNoiTru.dvNoiTru.danhGiaKetQuaLaoKhang"
                            )}
                            disabled={isReadonlyDvNoiTru}
                          />
                        </Form.Item>
                      </Col>
                      <Col span={12}>
                        <Form.Item
                          label={t("quanLyNoiTru.dvNoiTru.danhGiaKhac")}
                          name="danhGiaKhac"
                          rules={[
                            {
                              max: 255,
                              message: t(
                                "danhMuc.vuiLongNhapGiaTriKhongQuaKyTu"
                              )
                                .replace(
                                  "{0}",
                                  t(
                                    "quanLyNoiTru.dvNoiTru.danhGiaKhac"
                                  ).toLowerCase()
                                )
                                .replace("{1}", 255),
                            },
                          ]}
                        >
                          <Input
                            className="input-option"
                            placeholder={`${t("danhMuc.vuiLongNhapTitle", {
                              title: t(
                                "quanLyNoiTru.dvNoiTru.danhGiaKhac"
                              ).toLowerCase(),
                            })}`}
                            disabled={isReadonlyDvNoiTru}
                          />
                        </Form.Item>
                      </Col>
                      <Col span={12}>
                        <Form.Item
                          label={t("quanLyNoiTru.dvNoiTru.giaiDoanLao")}
                          name="giaiDoanLao"
                        >
                          <Select
                            data={listGiaiDoanLao}
                            placeholder={t("quanLyNoiTru.dvNoiTru.giaiDoanLao")}
                            disabled={isReadonlyDvNoiTru}
                          />
                        </Form.Item>
                      </Col>
                    </>
                  )}
                {isTabQuanLyNoiTruDvNoiTru && (
                  <>
                    {state?.record?.loaiDichVu ===
                      LOAI_DICH_VU.PHAU_THUAT_THU_THUAT &&
                      dataThietLap.includes("phanLoaiPtTt") && (
                        <Col span={12}>
                          <Form.Item
                            label={t("baoCao.phanLoaiPttt")}
                            name={"phanLoaiPtTt"}
                          >
                            <Select
                              disabled={
                                state?.disabledField || isReadonlyDvNoiTru
                              }
                              data={listPhanLoaiPTTT}
                              placeholder={t("baoCao.chonPhanLoaiPttt")}
                            />
                          </Form.Item>
                        </Col>
                      )}

                    {[
                      LOAI_DICH_VU.PHAU_THUAT_THU_THUAT,
                      LOAI_DICH_VU.CDHA,
                      LOAI_DICH_VU.XET_NGHIEM,
                    ].includes(state?.record?.loaiDichVu)
                      ? listKeys.map((key) => {
                          return (
                            <Col span={12} key={key}>
                              <Form.Item
                                label={DATA_TEN_HIEN_THI[key]}
                                name={key}
                              >
                                <Select
                                  disabled={
                                    state?.disabledField || isReadonlyDvNoiTru
                                  }
                                  data={renderDataListVTCC(key)}
                                />
                              </Form.Item>
                            </Col>
                          );
                        })
                      : null}
                  </>
                )}
              </Row>
            </Form>
          </div>
        </Main>
      </ModalTemplate>
    );
  }
);

ChinhSuaDVKyThuat.SCREEN = SCREEN;

export default ChinhSuaDVKyThuat;
