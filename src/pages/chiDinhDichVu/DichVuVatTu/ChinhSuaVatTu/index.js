import React, {
  useState,
  useImperativeHandle,
  forwardRef,
  useRef,
} from "react";
import { Col, Input, message, Row, Form } from "antd";
import { Main } from "./styled";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import {
  Checkbox,
  DateTimePicker,
  Button,
  ModalTemplate,
  Select,
  InputTimeout,
} from "components";
import moment from "moment";
import { useListAll, useStore, useThietLap } from "hooks";
import { LOAI_CHI_DINH, ROLES, THIET_LAP_CHUNG } from "constants/index";
import { SVG } from "assets";
import { checkRole } from "lib-utils/role-utils";

const ChinhSuaVatTu = (
  { isReadonlyDvNoiTru, isShowSoLuongDinhMuc = false },
  ref
) => {
  const { t } = useTranslation();
  const refModal = useRef(null);
  const refCallback = useRef(null);
  const [form] = Form.useForm();
  const [state, _setState] = useState({ data: [] });
  const setState = (data = {}) => {
    _setState((_state) => ({
      ..._state,
      ...data,
    }));
  };

  const {
    chiDinhVatTu: { getListDichVuVatTu, themThongTin },
    kichCo: { getListAllKichCo },
  } = useDispatch();
  const listAllKichCo = useStore("kichCo.listAllKichCo", []);
  const [dataNHAP_KICH_CO_VAT_TU] = useThietLap(
    THIET_LAP_CHUNG.NHAP_KICH_CO_VAT_TU,
    "0"
  );
  const [dataAN_CHECKBOX_TU_TRA] = useThietLap(
    THIET_LAP_CHUNG.AN_CHECKBOX_TU_TRA
  );
  const [listAllNguonKhacChiTra] = useListAll("nguonKhac", {}, true);

  useImperativeHandle(ref, () => ({
    show: (record = {}, callback) => {
      setState({ data: [], show: true, record: record });
      form.setFieldsValue({
        ...record,
        dotXuat: record.loaiChiDinh == LOAI_CHI_DINH.DOT_XUAT,
        boSung: record.loaiChiDinh == LOAI_CHI_DINH.BO_SUNG,
        thoiGianThucHien: moment(record?.thoiGianThucHien),
      });
      getListAllKichCo(
        {
          active: true,
          page: "",
          size: "",
          dichVuId: record?.dichVuId,
        },
        "",
        false
      );
      refCallback.current = callback;
      refModal.current && refModal.current.show();
    },
  }));
  const onCancel = () => {
    setState({ show: false, disabledButton: false });
    form.resetFields();
    refModal.current && refModal.current.hide();
  };
  const onSave = () => {
    form.submit();
  };

  const onHandledSubmit = (values) => {
    if (values.nbDichVu?.soLuong) {
      message.error(t("khamBenh.donThuoc.truongBatBuocDienSoLuong"));
      return;
    }
    if ((values.ghiChu || "").length > 1000) {
      message.error(t("khamBenh.donThuoc.nhapLuuYKhongQua1000KyTu"));
      return;
    }
    setState({ disabledButton: true });
    let payload = {
      id: state?.record?.id,
      nbDotDieuTriId: state?.record?.nbDotDieuTriId,
      nbDichVu: {
        ghiChu: values?.ghiChu,
        soLuong: values?.soLuong,
        dichVuId: state?.record?.dichVuId,
        tuTra: values?.tuTra,
        khongTinhTien: values?.khongTinhTien,
        chiDinhTuDichVuId: state?.record?.chiDinhTuDichVuId,
        chiDinhTuLoaiDichVu: state?.record?.chiDinhTuLoaiDichVu,
        khoaChiDinhId: state?.record?.khoaChiDinhId,
        thoiGianThucHien: moment(values.thoiGianThucHien).format(
          "YYYY/MM/DD HH:mm:ss"
        ),
        nguonKhacId: values?.nguonKhacId || null,
      },
      nbDvKho: {
        loaiChiDinh: values.dotXuat
          ? LOAI_CHI_DINH.DOT_XUAT
          : values.boSung
          ? LOAI_CHI_DINH.BO_SUNG
          : LOAI_CHI_DINH.THUONG,
      },
      kichCoVtId: values?.kichCoVtId,
    };
    themThongTin([payload])
      .then((s) => {
        getListDichVuVatTu({
          nbDotDieuTriId: state?.record.nbDotDieuTriId,
          chiDinhTuDichVuId: state?.record?.chiDinhTuDichVuId,
          chiDinhTuLoaiDichVu: state?.record?.chiDinhTuLoaiDichVu,
          dsTrangThaiHoan: [0, 10, 20],
        }).then((res) => {});
        if (refCallback.current) refCallback.current();
        onCancel();
      })
      .catch(() => {
        onCancel();
      });
  };
  const onTick = (key) => (e) => {
    if (key === "tuTra" && e) {
      form.setFieldsValue({ khongTinhTien: false });
    } else if (key === "khongTinhTien" && e) {
      form.setFieldsValue({ tuTra: false });
    }
    if (key === "dotXuat" && e) {
      form.setFieldsValue({ boSung: false });
    } else if (key === "boSung" && e) {
      form.setFieldsValue({ dotXuat: false });
    }
  };

  return (
    <ModalTemplate
      ref={refModal}
      title={t("khamBenh.donThuoc.thongTinVatTu")}
      onCancel={onCancel}
      width={650}
      actionLeft={<Button.QuayLai onClick={onCancel} />}
      actionRight={
        !isReadonlyDvNoiTru && (
          <Button
            type="primary"
            minWidth={100}
            rightIcon={<SVG.IcSave />}
            disabled={state?.disabledButton}
            onClick={onSave}
          >
            {t("common.luu")}
          </Button>
        )
      }
    >
      <fieldset disabled={isReadonlyDvNoiTru} style={{ width: "100%" }}>
        <Main>
          <div className="info-content">
            <Form form={form} layout="vertical" onFinish={onHandledSubmit}>
              <Row>
                <Col span={12}>
                  <Form.Item label={t("vatTu.tenVatTu")} name="tenDichVu">
                    <Input disabled></Input>
                  </Form.Item>
                  <Form.Item
                    label={t("vatTu.thoiGianThucHien")}
                    name="thoiGianThucHien"
                  >
                    <DateTimePicker
                      disabled={isReadonlyDvNoiTru}
                    ></DateTimePicker>
                  </Form.Item>
                  <Form.Item label={t("danhMuc.nguonKhac")} name="nguonKhacId">
                    <Select
                      data={listAllNguonKhacChiTra}
                      placeholder={t("danhMuc.nguonKhac")}
                      disabled={isReadonlyDvNoiTru}
                    />
                  </Form.Item>
                  <Form.Item label={t("vatTu.luuY")} name="ghiChu">
                    <Input.TextArea
                      disabled={isReadonlyDvNoiTru}
                    ></Input.TextArea>
                  </Form.Item>
                  {dataNHAP_KICH_CO_VAT_TU == "1" && (
                    <Form.Item label={t("vatTu.kichCo")} name="kichCoVtId">
                      <Select
                        data={listAllKichCo}
                        disabled={isReadonlyDvNoiTru}
                      ></Select>
                    </Form.Item>
                  )}
                </Col>
                <Col span={11} offset={1}>
                  <Row>
                    <Col span={isShowSoLuongDinhMuc ? 8 : 12}>
                      <Form.Item
                        label={t("common.soLuong")}
                        name="soLuong"
                        rules={[
                          {
                            validator: (rules, value, callback) => {
                              if (value <= 0) {
                                callback(t("vatTu.vuiLongNhapSoLuongLon0"));
                              } else {
                                callback();
                              }
                            },
                          },
                        ]}
                      >
                        <InputTimeout
                          type="number"
                          style={{ width: "100%" }}
                          min={0.01}
                          step={1}
                          parser={(value) => value.replaceAll(",", ".")}
                          formatter={(value) => value.replaceAll(".", ",")}
                          disabled={isReadonlyDvNoiTru}
                        />
                      </Form.Item>
                    </Col>
                    {isShowSoLuongDinhMuc && (
                      <Col span={7} offset={1}>
                        <Form.Item
                          label={t("khamBenh.donThuoc.slDinhMuc")}
                          name="soLuongDinhMuc"
                        >
                          <Input disabled></Input>
                        </Form.Item>
                      </Col>
                    )}
                    <Col span={isShowSoLuongDinhMuc ? 7 : 11} offset={1}>
                      <Form.Item label={t("vatTu.dvt")} name="tenDonViTinh">
                        <Input disabled></Input>
                      </Form.Item>
                    </Col>
                  </Row>
                  <Form.Item label={t("vatTu.phieuLinh")} name="soPhieuLinh">
                    <Input disabled></Input>
                  </Form.Item>

                  <Row>
                    <Col span={12}>
                      <Form.Item
                        label=" "
                        name="dotXuat"
                        valuePropName="checked"
                      >
                        <Checkbox
                          disabled={
                            !!form.getFieldValue("soPhieuLinh") ||
                            isReadonlyDvNoiTru
                          }
                          onChange={onTick("dotXuat")}
                        >
                          {t("kho.dotXuat")}
                        </Checkbox>
                      </Form.Item>
                    </Col>
                    <Col span={11} offset={1}>
                      <Form.Item
                        label=" "
                        name="boSung"
                        valuePropName="checked"
                      >
                        <Checkbox
                          disabled={
                            !!form.getFieldValue("soPhieuLinh") ||
                            isReadonlyDvNoiTru
                          }
                          onChange={onTick("boSung")}
                        >
                          {t("kho.boSung")}
                        </Checkbox>
                      </Form.Item>
                    </Col>
                  </Row>
                  <Row>
                    {!dataAN_CHECKBOX_TU_TRA?.eval() && (
                      <Col span={12}>
                        <Form.Item
                          label=" "
                          name="tuTra"
                          valuePropName="checked"
                        >
                          <Checkbox
                            disabled={isReadonlyDvNoiTru}
                            onChange={onTick("tuTra")}
                          >
                            {t("vatTu.tuTra")}
                          </Checkbox>
                        </Form.Item>
                      </Col>
                    )}
                    <Col span={11} offset={1}>
                      <Form.Item
                        label=" "
                        name="khongTinhTien"
                        valuePropName="checked"
                      >
                        <Checkbox
                          disabled={
                            isReadonlyDvNoiTru ||
                            !checkRole([
                              ROLES["QUAN_LY_NOI_TRU"].TICK_KHONG_TINH_TIEN,
                            ])
                          }
                          onChange={onTick("khongTinhTien")}
                        >
                          {t("vatTu.khongTinhTien")}
                        </Checkbox>
                      </Form.Item>
                    </Col>
                  </Row>
                </Col>
              </Row>
            </Form>
          </div>
        </Main>
      </fieldset>
    </ModalTemplate>
  );
};

export default forwardRef(ChinhSuaVatTu);
