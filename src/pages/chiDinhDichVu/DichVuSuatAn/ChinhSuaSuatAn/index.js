import React, {
  useState,
  useImperativeHandle,
  forwardRef,
  useRef,
} from "react";
import { Col, Input, message, Row, Form } from "antd";
import { Main } from "./styled";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import {
  DateTimePicker,
  Select,
  Button,
  ModalTemplate,
  Checkbox,
} from "components";
import moment from "moment";
import { useStore, useEnum, useThietLap } from "hooks";
import { ENUM, LOAI_CHI_DINH, ROLES, THIET_LAP_CHUNG } from "constants/index";
import { SVG } from "assets";
import { checkRole } from "lib-utils/role-utils";

const ChinhSuaSuatAn = ({ isReadonlyDvNoiTru }, ref) => {
  const { t } = useTranslation();
  const refModal = useRef(null);
  const refCallback = useRef(null);
  const [form] = Form.useForm();
  const [state, _setState] = useState({ data: [] });
  const listAllLoaiBuaAn = useStore("loaiBuaAn.listAllLoaiBuaAn", []);
  const [listTrangThai] = useEnum(ENUM.TRANG_THAI_PHIEU_LINH_SUAT_AN);
  const [dataBAT_BUOC_NHAP_LOAI_BUA_AN] = useThietLap(
    THIET_LAP_CHUNG.BAT_BUOC_NHAP_LOAI_BUA_AN,
    ""
  );
  const [dataAN_CHECKBOX_TU_TRA] = useThietLap(
    THIET_LAP_CHUNG.AN_CHECKBOX_TU_TRA
  );
  const batBuocDienLoaiBuaAn =
    (dataBAT_BUOC_NHAP_LOAI_BUA_AN || "").toLowerCase() == "true";

  const setState = (data = {}) => {
    _setState((_state) => ({
      ..._state,
      ...data,
    }));
  };

  const { getDsSuatAn, themThongTin } = useDispatch().chiDinhSuatAn;

  useImperativeHandle(ref, () => ({
    show: (record = {}, callback) => {
      setState({ data: [], show: true, record: record });
      form.setFieldsValue({
        ...record,
        dotXuat: record.loaiChiDinh === LOAI_CHI_DINH.DOT_XUAT,
        boSung: record.loaiChiDinh === LOAI_CHI_DINH.BO_SUNG,
        thoiGianThucHien: moment(record?.thoiGianThucHien),
      });

      refCallback.current = callback;
      refModal.current && refModal.current.show();
    },
  }));

  const onCancel = () => {
    setState({ show: false, disabledButton: false });
    form.resetFields();
    refModal.current && refModal.current.hide();
  };
  const onSave = () => {
    form.submit();
  };

  const onHandledSubmit = (values) => {
    if (values.nbDichVu?.soLuong) {
      message.error(t("khamBenh.donThuoc.truongBatBuocDienSoLuong"));
      return;
    }
    if ((values.ghiChu || "").length > 1000) {
      message.error(t("khamBenh.donThuoc.nhapLuuYKhongQua1000KyTu"));
      return;
    }
    if (batBuocDienLoaiBuaAn && !values.loaiBuaAnId) {
      message.error(t("danhMuc.vuiLongChonLoaiBuaAn"));
      return;
    }
    setState({ disabledButton: true });
    let payload = {
      id: state?.record?.id,
      nbDotDieuTriId: state?.record?.nbDotDieuTriId,
      loaiChiDinh: values?.dotXuat
        ? LOAI_CHI_DINH.DOT_XUAT
        : values?.boSung
        ? LOAI_CHI_DINH.BO_SUNG
        : LOAI_CHI_DINH.THUONG,
      loaiBuaAnId: values?.loaiBuaAnId,
      nbDichVu: {
        ghiChu: values?.ghiChu,
        soLuong: values?.soLuong,
        tuTra: values?.tuTra,
        khongTinhTien: values?.khongTinhTien,
        thoiGianThucHien: moment(values.thoiGianThucHien).format(
          "YYYY/MM/DD HH:mm:ss"
        ),
        dichVuId: state?.record?.dichVuId,
        chiDinhTuDichVuId: state?.record?.chiDinhTuDichVuId,
        chiDinhTuLoaiDichVu: state?.record?.chiDinhTuLoaiDichVu,
        khoaChiDinhId: state?.record?.khoaChiDinhId,
      },
    };
    themThongTin([payload])
      .then((s) => {
        getDsSuatAn({
          nbDotDieuTriId: state?.record.nbDotDieuTriId,
          chiDinhTuDichVuId: state?.record?.chiDinhTuDichVuId,
          chiDinhTuLoaiDichVu: 210,
          dsTrangThaiHoan: [0, 10, 20, 40],
        }).then((res) => {});
        if (refCallback.current) refCallback.current();
        onCancel();
      })
      .catch(() => {});
  };
  const onTick = (key) => (e) => {
    if (key === "tuTra" && e) {
      form.setFieldsValue({ khongTinhTien: false });
    } else if (key === "khongTinhTien" && e) {
      form.setFieldsValue({ tuTra: false });
    }

    if (key === "dotXuat" && e) {
      form.setFieldsValue({ boSung: false });
    } else if (key === "boSung" && e) {
      form.setFieldsValue({ dotXuat: false });
    }
  };
  return (
    <ModalTemplate
      ref={refModal}
      title={t("quanLyNoiTru.chinhSuaThongTinSuatAn")}
      onCancel={onCancel}
      actionLeft={<Button.QuayLai onClick={onCancel} />}
      actionRight={
        !isReadonlyDvNoiTru && (
          <Button
            type="primary"
            onClick={onSave}
            minWidth={100}
            rightIcon={<SVG.IcSave />}
            disabled={state?.disabledButton}
          >
            {t("common.luu")}
          </Button>
        )
      }
      width={850}
    >
      <Main>
        <div className="info-content">
          <Row>
            <Col span={24} className="title-dv">
              {t("common.tenDichVu")}
            </Col>
            <Col span={24} className="name-dv">
              {state.record?.tenDichVu}
            </Col>
          </Row>
          <br />
          <Form form={form} layout="vertical" onFinish={onHandledSubmit}>
            <Row>
              <Col span={6}>
                <div className="form-item">
                  <Form.Item
                    label={t("quanLyNoiTru.suatAn.loaiBuaAn")}
                    name="loaiBuaAnId"
                  >
                    <Select
                      data={listAllLoaiBuaAn || []}
                      disabled={state.record?.phieuLinhId || isReadonlyDvNoiTru}
                    />
                  </Form.Item>
                </div>
                <div className="form-item">
                  <Form.Item
                    label={t("quanLyNoiTru.suatAn.soPhieuLinh")}
                    name="soPhieuLinh"
                  >
                    <Input type="number" disabled></Input>
                  </Form.Item>
                </div>
              </Col>
              <Col span={6}>
                <div className="form-item">
                  <Form.Item label={t("common.trangThai")} name="trangThai">
                    <Select disabled data={listTrangThai || []} />
                  </Form.Item>
                </div>
                <div className="form-item">
                  <Form.Item
                    label={t("quanLyNoiTru.soPhieuTra")}
                    name="soPhieuTra"
                  >
                    <Input value={""} disabled></Input>
                  </Form.Item>
                </div>
              </Col>
              <Col span={12}>
                <Row>
                  <Col span={12}>
                    <div className="form-item">
                      <Form.Item label={t("common.soLuong")} name="soLuong">
                        <Input
                          type="number"
                          disabled={
                            state.record?.phieuLinhId || isReadonlyDvNoiTru
                          }
                        ></Input>
                      </Form.Item>
                    </div>
                  </Col>
                  <Col span={12}>
                    <div className="form-item">
                      <Form.Item label={t("vatTu.dvt")} name="tenDonViTinh">
                        <Input defaultValue={t("common.lan")} disabled></Input>
                      </Form.Item>
                    </div>
                  </Col>
                </Row>

                <Row>
                  <Col span={24}>
                    <div className="form-item">
                      <Form.Item
                        label={t("quanLyNoiTru.ngayThucHien")}
                        name="thoiGianThucHien"
                      >
                        <DateTimePicker
                          placeholder={t("common.chonThoiGian")}
                          disabled={isReadonlyDvNoiTru}
                        ></DateTimePicker>
                      </Form.Item>
                    </div>
                  </Col>
                </Row>
              </Col>
            </Row>

            <Row>
              <Col span={12}>
                <div className="form-item">
                  <Form.Item label={t("common.ghiChu")} name="ghiChu">
                    <Input.TextArea
                      placeholder={t("common.noiDung")}
                      disabled={isReadonlyDvNoiTru}
                    ></Input.TextArea>
                  </Form.Item>
                </div>
              </Col>
              <Col span={12}>
                <Row>
                  <Col span={8}>
                    <div className="form-item">
                      <Form.Item
                        label=" "
                        name="dotXuat"
                        valuePropName="checked"
                      >
                        <Checkbox
                          onChange={onTick("dotXuat")}
                          disabled={
                            isReadonlyDvNoiTru || !!state?.record?.soPhieuLinh
                          }
                        >
                          {t("kho.dotXuat")}
                        </Checkbox>
                      </Form.Item>
                    </div>
                  </Col>
                  <Col span={8}>
                    <div className="form-item">
                      <Form.Item
                        label=" "
                        name="boSung"
                        valuePropName="checked"
                      >
                        <Checkbox
                          onChange={onTick("boSung")}
                          disabled={
                            isReadonlyDvNoiTru || !!state?.record?.soPhieuLinh
                          }
                        >
                          {t("kho.boSung")}
                        </Checkbox>
                      </Form.Item>
                    </div>
                  </Col>
                </Row>
              </Col>
              <Col span={12}>
                <Row>
                  {!dataAN_CHECKBOX_TU_TRA?.eval() && (
                    <Col span={8}>
                      <div className="form-item">
                        <Form.Item
                          label=" "
                          name="tuTra"
                          valuePropName="checked"
                        >
                          <Checkbox
                            disabled={isReadonlyDvNoiTru}
                            onChange={onTick("tuTra")}
                          >
                            {t("common.tuTra")}
                          </Checkbox>
                        </Form.Item>
                      </div>
                    </Col>
                  )}
                  <Col span={8}>
                    <div className="form-item">
                      <Form.Item
                        label=" "
                        name="khongTinhTien"
                        valuePropName="checked"
                      >
                        <Checkbox
                          disabled={
                            isReadonlyDvNoiTru ||
                            !checkRole([
                              ROLES["QUAN_LY_NOI_TRU"].TICK_KHONG_TINH_TIEN,
                            ])
                          }
                          onChange={onTick("khongTinhTien")}
                        >
                          {t("quanLyNoiTru.khongTinhTien")}
                        </Checkbox>
                      </Form.Item>
                    </div>
                  </Col>
                </Row>
              </Col>
            </Row>
          </Form>
        </div>
      </Main>
    </ModalTemplate>
  );
};

export default forwardRef(ChinhSuaSuatAn);
