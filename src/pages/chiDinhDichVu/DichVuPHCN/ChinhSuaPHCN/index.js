import React, {
  useState,
  useImperativeHandle,
  forwardRef,
  useRef,
  useMemo,
} from "react";
import { Col, Input, message, Row, Form } from "antd";
import { toSafePromise } from "lib-utils";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import {
  Checkbox,
  DateTimePicker,
  Select,
  Button,
  ModalTemplate,
} from "components";
import moment from "moment";
import { useStore, useListAll, useThietLap } from "hooks";
import {
  DOI_TUONG_SU_DUNG,
  LOAI_DICH_VU,
  THIET_LAP_CHUNG,
} from "constants/index";
import { SVG } from "assets";
import { Main } from "./styled";
import { selectMaTen } from "redux-store/selectors";

const ChinhSuaPHCN = (props, ref) => {
  const { t } = useTranslation();
  const refModal = useRef(null);
  const refCallback = useRef(null);
  const [form] = Form.useForm();
  const [state, _setState] = useState({ data: [] });
  const listMucDichSuDung = useStore("mucDichSuDung.listMucDichSuDung", []);
  const listPhongThucHien = useStore("phongThucHien.listData", []);
  const listLoaiHinhThanhToanCuaDoiTuong = useStore(
    "loaiDoiTuongLoaiHinhTT.listLoaiHinhThanhToanCuaDoiTuong",
    []
  );
  const thongTinCoBan = useStore("nbDotDieuTri.thongTinCoBan");
  const [listAllNguonKhacChiTra] = useListAll("nguonKhac", {}, true);
  const [listAllNhanVien] = useListAll("nhanVien", {}, state.show);
  const [dataAN_CHECKBOX_TU_TRA] = useThietLap(
    THIET_LAP_CHUNG.AN_CHECKBOX_TU_TRA
  );

  const setState = (data = {}) => {
    _setState((_state) => ({
      ..._state,
      ...data,
    }));
  };

  const {
    phongThucHien: { onSearchParams: searchPhongThucHien },
    chiDinhPHCN: { themThongTinPHCN },
    loaiDoiTuongLoaiHinhTT: { getListLoaiDoiTuongTT },
    tiepNhanCDHA: { updateKetQua },
  } = useDispatch();

  useImperativeHandle(ref, () => ({
    show: (record = {}, callback) => {
      setState({ data: [], show: true, record: record });
      form.setFieldsValue({
        ...record,
        thoiGianThucHien: moment(record?.thoiGianThucHien),
        thoiGianCoKetQua: record?.thoiGianCoKetQua
          ? moment(record?.thoiGianCoKetQua)
          : null,
        thoiGianTiepNhan: record?.thoiGianTiepNhan
          ? moment(record?.thoiGianTiepNhan)
          : null,
      });

      searchPhongThucHien({
        dsDichVuId: [record.dichVuId],
        khoaChiDinhId: record?.khoaChiDinhId,
        loaiDoiTuongId: thongTinCoBan.loaiDoiTuongId,
        loaiHinhThanhToanId: record?.loaiHinhThanhToanId,
        nbDotDieuTriId: record?.nbDotDieuTriId,
        gioiTinh: thongTinCoBan.gioiTinh,
        doiTuongKcb: thongTinCoBan.doiTuongKcb,
      });

      getListLoaiDoiTuongTT({
        active: true,
        page: "",
        size: "",
        dsDichVuId: record.dichVuId,
        loaiDoiTuongId: thongTinCoBan.loaiDoiTuongId,
        khoaChiDinhId: record?.khoaChiDinhId,
        ngayThucHien:
          record?.thoiGianThucHien &&
          moment(record?.thoiGianThucHien).format("YYYY-MM-DD"),
        ngaySinh:
          thongTinCoBan?.ngaySinh &&
          moment(thongTinCoBan?.ngaySinh).format("YYYY-MM-DD"),
        ngayVaoVien:
          thongTinCoBan.thoiGianVaoVien &&
          moment(thongTinCoBan.thoiGianVaoVien).format("YYYY-MM-DD"),
        doiTuongKcb: thongTinCoBan.doiTuongKcb,
      });
      refCallback.current = callback;
      refModal.current && refModal.current.show();
    },
  }));

  const dataPhongThucHien = useMemo(() => {
    return (listPhongThucHien || []).map((item) => ({
      id: item.phongId,
      ten: `${item?.ma} - ${item?.ten}`,
      dichVuId: item.dichVuId,
    }));
  }, [listPhongThucHien]);

  const onCancel = () => {
    setState({ show: false, disabledButton: false });
    form.resetFields();
    refModal.current && refModal.current.hide();
  };

  const onSave = () => {
    form.submit();
  };

  const listAllLoaiHinhThanhToan = useMemo(() => {
    return listLoaiHinhThanhToanCuaDoiTuong.map((item) => ({
      ...item,
      id: item.loaiHinhThanhToanId,
      ten: item.tenLoaiHinhThanhToan,
    }));
  }, [listLoaiHinhThanhToanCuaDoiTuong]);

  const onHandledSubmit = (values) => {
    const { ketQua, ketLuan } = values;
    if (values.nbDichVu?.soLuong) {
      message.error(t("khamBenh.donThuoc.truongBatBuocDienSoLuong"));
      return;
    }
    if ((values.ghiChu || "").length > 1000) {
      message.error(t("khamBenh.donThuoc.nhapLuuYKhongQua1000KyTu"));
      return;
    }
    setState({ disabledButton: true });
    let payload = {
      id: state?.record?.id,
      loaiDichVu: state?.record?.loaiDichVu,
      nbDotDieuTriId: state?.record?.nbDotDieuTriId,
      thoiGianCoKetQua: values?.thoiGianCoKetQua
        ? moment(values?.thoiGianCoKetQua).format("YYYY/MM/DD HH:mm:ss")
        : null,
      nbDichVu: {
        ghiChu: values?.ghiChu,
        soLuong: values?.soLuong,
        tuTra: values?.tuTra,
        khongTinhTien: values?.khongTinhTien,
        thoiGianThucHien: values.thoiGianThucHien
          ? moment(values.thoiGianThucHien).format("YYYY/MM/DD HH:mm:ss")
          : null,
        dichVuId: state?.record?.dichVuId,
        loaiHinhThanhToanId: values?.loaiHinhThanhToanId || null,
        mucDichId: values?.mucDichId,
        nguonKhacId: values?.nguonKhacId || null,
      },
      nbDvKyThuat: {
        phongThucHienId: values?.phongThucHienId,
        soPhieu: values?.soPhieu,
        thoiGianTiepNhan: values?.thoiGianTiepNhan
          ? moment(values?.thoiGianTiepNhan).format("YYYY/MM/DD HH:mm:ss")
          : null,
      },
      nguoiThucHienId: values?.nguoiThucHienId,
    };
    let promises = [];
    if (
      (values.hasOwnProperty("ketQua") && ketQua !== state.record?.ketQua) ||
      (values.hasOwnProperty("ketLuan") && ketLuan !== state.record?.ketLuan)
    ) {
      promises.push(
        toSafePromise(
          updateKetQua({
            id: state.record?.id,
            ketQua: ketQua ?? null,
            ketLuan: ketLuan ?? null,
          })
        )
      );
    }
    promises.push(themThongTinPHCN(payload));
    Promise.all(promises)
      .then((s) => {
        if (refCallback.current) refCallback.current();
        onCancel();
      })
      .catch(() => {})
      .finally(() => {
        setState({ disabledButton: false });
      });
  };
  const onTick = (key) => (e) => {
    if (key === "tuTra" && e) {
      form.setFieldsValue({ khongTinhTien: false });
    } else if (key === "khongTinhTien" && e) {
      form.setFieldsValue({ tuTra: false });
    }
  };

  const isThucHienTaiKhoa = (state.record?.dsDoiTuongSuDung || []).includes(
    DOI_TUONG_SU_DUNG.THUC_HIEN_TAI_KHOA
  );

  return (
    <ModalTemplate
      ref={refModal}
      title={t("phcn.chinhSuaThongTinPHCN")}
      onCancel={onCancel}
      width={650}
    >
      <Main>
        <div className="info-content">
          <Row>
            <Col span={24} className="title-dv">
              {t("common.tenDichVu")}
            </Col>
            <Col span={24} className="name-dv">
              {state.record?.tenDichVu}
            </Col>
          </Row>
          <br />
          <Form form={form} layout="vertical" onFinish={onHandledSubmit}>
            <Row>
              <Col span={12}>
                <div className="form-item">
                  <Form.Item label={t("common.soLuong")} name="soLuong">
                    <Input
                      type="number"
                      disabled={state.record?.phieuLinhId}
                    ></Input>
                  </Form.Item>
                </div>
              </Col>
              <Col span={12}>
                <div className="form-item">
                  <Form.Item label={t("common.soPhieu")} name="soPhieuThu">
                    <Input placeholder={t("common.soPhieu")}></Input>
                  </Form.Item>
                </div>
              </Col>
              <Col span={12}>
                <Form.Item
                  label={t("tiepDon.phongThucHien")}
                  name="phongThucHienId"
                >
                  <Select data={dataPhongThucHien || []} />
                </Form.Item>
              </Col>
              <Col span={12}>
                <div className="form-item">
                  <Form.Item label={t("danhMuc.moTa")} name="moTa">
                    <Input placeholder={t("danhMuc.moTa")}></Input>
                  </Form.Item>
                </div>
              </Col>
              <Col span={12}>
                <div className="form-item">
                  <Form.Item
                    label={t("common.ngayThucHien")}
                    name="thoiGianThucHien"
                  >
                    <DateTimePicker placeholder={t("common.chonThoiGian")} />
                  </Form.Item>
                </div>
              </Col>
              <Col span={12}>
                <div className="form-item">
                  <Form.Item
                    label={t("phcn.ngayCoKetQua")}
                    name="thoiGianCoKetQua"
                  >
                    <DateTimePicker placeholder={t("common.chonThoiGian")} />
                  </Form.Item>
                </div>
              </Col>
              <Col span={12}>
                <div className="form-item">
                  <Form.Item
                    label={t("theoDoiDieuTri.ngayTiepNhan")}
                    name="thoiGianTiepNhan"
                  >
                    <DateTimePicker placeholder={t("common.chonThoiGian")} />
                  </Form.Item>
                </div>
              </Col>
              <Col span={12}>
                <div className="form-item">
                  <Form.Item
                    label={t("tiepDon.loaiHinhThanhToan")}
                    name="loaiHinhThanhToanId"
                  >
                    <Select
                      data={listAllLoaiHinhThanhToan || []}
                      placeholder={t("tiepDon.loaiHinhThanhToan")}
                    />
                  </Form.Item>
                </div>
              </Col>
              <Col span={12}>
                <div className="form-item">
                  <Form.Item label={t("phcn.tt35")} name="mucDichId">
                    <Select
                      data={listMucDichSuDung}
                      placeholder={t("phcn.tt35")}
                    />
                  </Form.Item>
                </div>
              </Col>
              {isThucHienTaiKhoa &&
                [LOAI_DICH_VU.CDHA, LOAI_DICH_VU.PHAU_THUAT_THU_THUAT].includes(
                  state.record?.loaiDichVu
                ) && (
                  <>
                    <Col span={12}>
                      <div className="form-item">
                        <Form.Item label={t("common.ketQua")} name="ketQua">
                          <Input.TextArea />
                        </Form.Item>
                      </div>
                    </Col>
                    <Col span={12}>
                      <div className="form-item">
                        <Form.Item label={t("common.ketLuan")} name="ketLuan">
                          <Input.TextArea />
                        </Form.Item>
                      </div>
                    </Col>
                  </>
                )}
              <Col span={12}>
                <div className="form-item">
                  <Form.Item label={t("common.ghiChu")} name="ghiChu">
                    <Input.TextArea
                      placeholder={t("common.ghiChu")}
                    ></Input.TextArea>
                  </Form.Item>
                </div>
              </Col>
              {!dataAN_CHECKBOX_TU_TRA?.eval() && (
                <Col span={6}>
                  <div className="form-item">
                    <Form.Item label=" " name="tuTra" valuePropName="checked">
                      <Checkbox onChange={onTick("tuTra")}>
                        {t("common.tuTra")}
                      </Checkbox>
                    </Form.Item>
                  </div>
                </Col>
              )}
              <Col span={6}>
                <div className="form-item">
                  <Form.Item
                    label=" "
                    name="khongTinhTien"
                    valuePropName="checked"
                  >
                    <Checkbox onChange={onTick("khongTinhTien")}>
                      {t("common.khongTinhTien")}
                    </Checkbox>
                  </Form.Item>
                </div>
              </Col>
              <Col span={12}>
                <Form.Item label={t("danhMuc.nguonKhac")} name="nguonKhacId">
                  <Select
                    data={listAllNguonKhacChiTra}
                    placeholder={t("danhMuc.nguonKhac")}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <div className="form-item">
                  <Form.Item
                    label={t("khamBenh.chiDinh.nguoiThucHien")}
                    name="nguoiThucHienId"
                  >
                    <Select
                      data={listAllNhanVien || []}
                      placeholder={t("khamBenh.chiDinh.nguoiThucHien")}
                      getLabel={selectMaTen}
                    />
                  </Form.Item>
                </div>
              </Col>
            </Row>
          </Form>
        </div>
        <div className="footer-btn">
          <Button type={"default"} onClick={onCancel} minWidth={100}>
            {t("common.huy")}
          </Button>
          <Button
            type="primary"
            onClick={onSave}
            minWidth={100}
            rightIcon={<SVG.IcSave />}
            disabled={state?.disabledButton}
          >
            {t("common.luu")}
          </Button>
        </div>
      </Main>
    </ModalTemplate>
  );
};

export default forwardRef(ChinhSuaPHCN);
