import React, { useEffect, useState } from "react";
import { BaseSearch } from "components";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import { useEnum, useListAll, useStore, useThietLap } from "hooks";
import {
  LOAI_PHIEU_NHAP,
  THANG_DU_TRU,
  TRANG_THAI_PHIEU,
  LIST_SCAN_CHUNG_TU,
  ENUM,
  THIET_LAP_CHUNG,
  HINH_THUC_NHAP_XUAT,
} from "constants/index";
import {
  getAllQueryString,
  setQueryStringValues,
} from "hooks/useQueryString/queryString";
import moment from "moment";
import { select } from "redux-store/stores";
import { isArray, isNumber, isUndefined } from "utils/index";
import { SVG } from "assets";
import { Main } from "./styled";
import { setQueryStringValue } from "hooks/useQueryString/queryString";

const TimKiem = () => {
  const [state, _setState] = useState({});
  const setState = (data) => {
    _setState((pre) => ({ ...pre, ...data }));
  };

  const { t } = useTranslation();
  const [listAllNguonNhapKho] = useListAll("nguonNhapKho", {}, true);
  const [listAllKhoa] = useListAll("khoa", { active: true }, true);
  const [listLoaiChiDinh] = useEnum(ENUM.LOAI_CHI_DINH);

  const {
    nhapKho: { onChangeInputSearch, onSizeChange },
    quyetDinhThau: { getListAllQuyetDinhThau },
  } = useDispatch();

  const listKhoUser = useStore("kho.listKhoUser", []);
  const [listHinhThucNhapXuat] = useListAll(
    "hinhThucNhapXuat",
    {
      active: true,
      page: "",
      size: "",
      dsHinhThucNhapXuat: HINH_THUC_NHAP_XUAT.HINH_THUC_NHAP,
    },
    true
  );

  const [dataSO_PHIEU_NHAP_XUAT_KHO] = useThietLap(
    THIET_LAP_CHUNG.SO_PHIEU_NHAP_XUAT_KHO
  );

  useEffect(() => {
    getListAllQuyetDinhThau({ page: "", size: "", active: true });
  }, []);

  const onSearch = ({ page, size, dsKhoNhapIdMacDinh, ...payload }) => {
    if (!isArray(listKhoUser, true)) return;
    let search = payload;
    if (search.dsTrangThai) {
      search.dsTrangThai = isArray(search.dsTrangThai)
        ? search.dsTrangThai
        : [search.dsTrangThai];
    }

    if (search.dsQuyetDinhThauId) {
      search.dsQuyetDinhThauId = isArray(search.dsQuyetDinhThauId)
        ? search.dsQuyetDinhThauId
        : [search.dsQuyetDinhThauId];
    }

    if (search.dsKhoaChiDinhId) {
      search.dsKhoaChiDinhId = isArray(search.dsKhoaChiDinhId)
        ? search.dsKhoaChiDinhId
        : [search.dsKhoaChiDinhId];
    }

    if (search.dsNguonNhapKhoId) {
      search.dsNguonNhapKhoId = isArray(search.dsNguonNhapKhoId)
        ? search.dsNguonNhapKhoId
        : [search.dsNguonNhapKhoId];
    }

    if (search.dsKhoNhapId) {
      search.dsKhoNhapId = search?.dsKhoNhapId?.length
        ? search.dsKhoNhapId
        : isArray(listKhoUser, true)
        ? listKhoUser.map((i) => i.id)
        : undefined;
    }

    if (search.chungTu === "") {
      search.chungTu = undefined;
    }
    if (search.dsLoaiChiDinh) {
      search.dsLoaiChiDinh = search.dsLoaiChiDinh?.length
        ? search.dsLoaiChiDinh.map(Number)
        : undefined;
    }

    if (search.dsHinhThucNhapXuatId) {
      search.dsHinhThucNhapXuatId = search.dsHinhThucNhapXuatId?.length
        ? search.dsHinhThucNhapXuatId.map(Number)
        : undefined;
    }

    if (page) {
      onSizeChange({
        page: parseInt(page || 0),
        size: parseInt(size || 10),
        dataSearch: {
          ...search,
        },
      });
    } else {
      onChangeInputSearch({
        ...search,
      });
    }
    setState({ dsKhoNhapIdMacDinh, ...search });
  };

  const listAllDoiTac = useStore("doiTac.listAllDoiTac", []);

  useEffect(() => {
    const { page, size, dataSortColumn, ...queries } = getAllQueryString(
      undefined,
      { filterEmpty: false }
    );
    let newQueries = { ...queries };
    if (newQueries.hasOwnProperty("tuThoiGianTaoPhieu")) {
      if (isNumber(newQueries.tuThoiGianTaoPhieu)) {
        newQueries.tuThoiGianTaoPhieu = moment(
          +newQueries.tuThoiGianTaoPhieu
        ).format("YYYY-MM-DD 00:00:00");
      } else {
        newQueries.tuThoiGianTaoPhieu = null;
      }
    } else {
      newQueries.tuThoiGianTaoPhieu = moment()
        .startOf("days")
        .format("YYYY-MM-DD 00:00:00");
    }
    if (newQueries.hasOwnProperty("denThoiGianTaoPhieu")) {
      if (isNumber(newQueries.denThoiGianTaoPhieu)) {
        newQueries.denThoiGianTaoPhieu = moment(
          +newQueries.denThoiGianTaoPhieu
        ).format("YYYY-MM-DD 23:59:59");
      } else {
        newQueries.denThoiGianTaoPhieu = null;
      }
    } else {
      newQueries.denThoiGianTaoPhieu = moment()
        .endOf("days")
        .format("YYYY-MM-DD 23:59:59");
    }
    if (isNumber(newQueries.tuThoiGianDuyet))
      newQueries.tuThoiGianDuyet = moment(+newQueries.tuThoiGianDuyet).format(
        "YYYY-MM-DD 00:00:00"
      );
    if (isNumber(newQueries.denThoiGianDuyet))
      newQueries.denThoiGianDuyet = moment(+newQueries.denThoiGianDuyet).format(
        "YYYY-MM-DD 23:59:59"
      );

    newQueries = Object.fromEntries(
      Object.entries(newQueries).filter(([_, v]) => !isUndefined(v))
    );

    if (newQueries.dsKhoNhapId) {
      newQueries.dsKhoNhapIdMacDinh = newQueries.dsKhoNhapId
        .split(",")
        .map(Number);
      newQueries.dsKhoNhapId = newQueries.dsKhoNhapIdMacDinh;
    } else {
      newQueries.dsKhoNhapIdMacDinh = (listKhoUser || []).map(
        (item) => item.id
      );
      newQueries.dsKhoNhapId = newQueries.dsKhoNhapIdMacDinh;
    }
    if (newQueries.dsKhoaChiDinhId)
      newQueries.dsKhoaChiDinhId = +newQueries.dsKhoaChiDinhId;
    if (newQueries.dsTrangThai)
      newQueries.dsTrangThai = +newQueries.dsTrangThai;
    if (newQueries.loaiNhapXuat)
      newQueries.loaiNhapXuat = +newQueries.loaiNhapXuat;
    if (newQueries.dsQuyetDinhThauId)
      newQueries.dsQuyetDinhThauId = +newQueries.dsQuyetDinhThauId;
    if (newQueries.nhaCungCapId) {
      newQueries.nhaCungCapId = +newQueries.nhaCungCapId;
    }
    if (newQueries.dsNguonNhapKhoId) {
      newQueries.dsNguonNhapKhoId = +newQueries.dsNguonNhapKhoId;
    }
    if (newQueries.dsLoaiChiDinh) {
      newQueries.dsLoaiChiDinh = newQueries.dsLoaiChiDinh
        .split(",")
        .map(Number);
    }
    if (newQueries.chungTu) {
      newQueries.chungTu = newQueries.chungTu.eval();
    }
    if (newQueries.dsHinhThucNhapXuatId) {
      newQueries.dsHinhThucNhapXuatId = newQueries.dsHinhThucNhapXuatId
        .split(",")
        .map(Number);
    }

    if (listKhoUser.length) {
      onSearch({ ...newQueries, page, size });
    }
  }, [listKhoUser]);

  const onChange = (key) => (e) => {
    const newList = Object.assign([], state[key]) || [];
    const index = newList.findIndex((item) => item === e);

    if (index !== -1) {
      newList.splice(index, 1);
    } else if (e) {
      newList.push(e);
    }

    let value = !e ? null : newList;
    if ((key === "dsTrangThai" || key === "dsKhoNhapId") && !newList.length) {
      value = null;
    }
    setState({ [key]: value });
    if (!listKhoUser.length) return;
    onChangeInputSearch({ [key]: value });
    setQueryStringValue(key, value);
  };

  return (
    <Main>
      <BaseSearch
        cacheData={state}
        dataInput={[
          {
            widthInput: "220px",
            placeholder: t("kho.tenKho"),
            functionChangeInput: onSearch,
            keyValueInput: "dsKhoNhapId",
            type: "selectCheckbox",
            title: t("kho.tenKho"),
            listSelect: listKhoUser,
            hasCheckAll: true,
            hasSearch: true,
            virtual: true,
            height: 400,
            defaultValue: state.dsKhoNhapIdMacDinh,
          },
          {
            widthInput: "190px",
            placeholder: t("kho.chonLoaiPhieu"),
            functionChangeInput: onSearch,
            keyValueInput: "loaiNhapXuat",
            type: "select",
            listSelect: LOAI_PHIEU_NHAP,
          },
          {
            widthInput: "164px",
            placeholder: t("kho.trangThaiPhieu"),
            functionChangeInput: onSearch,
            keyValueInput: "dsTrangThai",
            type: "select",
            listSelect: TRANG_THAI_PHIEU,
          },
          {
            widthInput: "145px",
            placeholder: t("kho.quyetDinhThau.title"),
            functionChangeInput: onSearch,
            keyValueInput: "dsQuyetDinhThauId",
            type: "select",
            listSelect: select.quyetDinhThau.listAllQuyetDinhThau,
            getLabel: (item) => item.quyetDinhThau,
          },
          {
            widthInput: "164px",
            placeholder: t("kho.khoaChiDinh"),
            functionChangeInput: onSearch,
            keyValueInput: "dsKhoaChiDinhId",
            type: "select",
            listSelect: listAllKhoa,
          },
          {
            widthInput: "220px",
            placeholder: t("kho.timKiemPhieuTraLan"),
            keyValueInput: "lan",
            functionChangeInput: onSearch,
            searchOnInput: true,
          },
          {
            widthInput: "200px",
            placeholder: t("kho.timKiemSoPhieu"),
            keyValueInput: "soPhieu",
            functionChangeInput: onSearch,
            searchOnInput: true,
          },
          {
            widthInput: "200px",
            placeholder: t("kho.nhapSoHoaDon"),
            keyValueInput: "soHoaDon",
            functionChangeInput: onSearch,
            searchOnInput: true,
          },
          {
            widthInput: "350px",
            type: "dateRange",
            state: state,
            setState: setState,
            functionChangeInput: onSearch,
            title: t("khoMau.ngayTaoPhieu"),
            placeholder: [t("common.tuNgay"), t("common.denNgay")],
            keyValueInput: ["tuThoiGianTaoPhieu", "denThoiGianTaoPhieu"],
            showTimeDefaultValue: {
              from: moment().startOf("day"),
              to: moment().endOf("day"),
            },
            format: "DD/MM/YYYY HH:mm:ss",
            allowClear: true,
          },
          {
            widthInput: "350px",
            type: "dateRange",
            state: state,
            setState: setState,
            functionChangeInput: onSearch,
            keyValueInput: ["tuThoiGianDuyet", "denThoiGianDuyet"],
            title: t("khoMau.ngayDuyetPhieu"),
            placeholder: [t("common.tuNgay"), t("common.denNgay")],
            showTimeDefaultValue: {
              from: moment().startOf("day"),
              to: moment().endOf("day"),
            },
            format: "DD/MM/YYYY HH:mm:ss",
            allowClear: true,
          },
          ...([1, 2, 3, 4].includes(parseInt(dataSO_PHIEU_NHAP_XUAT_KHO))
            ? [
                {
                  widthInput: "200px",
                  placeholder: t("goiDichVu.soPhieuDoiUng"),
                  keyValueInput: "soPhieuDoiUng",
                  functionChangeInput: onSearch,
                  searchOnInput: true,
                },
              ]
            : []),
          {
            widthInput: "200px",
            placeholder: t("danhMuc.chonNhaCungCap"),
            keyValueInput: "nhaCungCapId",
            functionChangeInput: onSearch,
            type: "select",
            listSelect: listAllDoiTac,
          },
        ]}
        filter={{
          open: true,
          width: "110px",
          funcSearchData: (data) => {
            onSearch(data);
            setQueryStringValues(data);
          },
          title: t("nhaThuoc.locPhieu"),
          data: [
            {
              widthInput: "212px",
              placeholder: t("kho.chonNguonNhapKho"),
              key: "dsNguonNhapKhoId",
              functionChangeInput: onSearch,
              type: "select",
              dataSelect: listAllNguonNhapKho,
            },
            {
              widthInput: "212px",
              placeholder: t("kho.chonThangDuTru"),
              key: "thangDuTru",
              functionChangeInput: onSearch,
              type: "select",
              dataSelect: THANG_DU_TRU,
            },
            {
              widthInput: "212px",
              placeholder: t("kho.scanChungTu"),
              key: "chungTu",
              functionChangeInput: onSearch,
              type: "select",
              defaultValue: "",
              dataSelect: LIST_SCAN_CHUNG_TU,
              hasAllOption: true,
            },
            {
              placeholder: t("quanLyNoiTru.cpdd.loaiChiDinh"),
              key: "dsLoaiChiDinh",
              type: "select",
              mode: "multiple",
              dataSelect: listLoaiChiDinh,
            },
            {
              placeholder: t("kho.hinhThucNhap"),
              key: "dsHinhThucNhapXuatId",
              type: "select",
              mode: "multiple",
              dataSelect: listHinhThucNhapXuat,
            },
          ],
        }}
      />
      <div className="array-store" style={{ flexFlow: "row wrap" }}>
        {listKhoUser?.length === state.dsKhoNhapId?.length
          ? null
          : (state.dsKhoNhapId || []).map((item, index) => {
              let currentKho = listKhoUser.find(
                (x) => x.value == item || x.id === item
              );
              return (
                <div className="item" style={{ marginTop: 5 }} key={index}>
                  <span>{currentKho?.ten || currentKho?.label}</span>
                  <SVG.IcCancel
                    onClick={() => {
                      onChange("dsKhoNhapId")(item);
                    }}
                  />
                </div>
              );
            })}

        {(state.dsTrangThai || []).map((item, index) => {
          return (
            <div className="item" style={{ marginTop: 5 }} key={index}>
              <span>{TRANG_THAI_PHIEU.find((x) => x.id == item)?.ten}</span>
              <SVG.IcCancel
                onClick={() => {
                  onChange("dsTrangThai")(item);
                }}
              />
            </div>
          );
        })}
      </div>
    </Main>
  );
};

export default TimKiem;
