import React, {
  useEffect,
  useRef,
  useState,
  useImperative<PERSON><PERSON>le,
  forwardRef,
  useMemo,
} from "react";
import { <PERSON><PERSON><PERSON>, Header<PERSON>earch, TableWrapper, Pagination } from "components";
import { useDispatch, useSelector } from "react-redux";
import { useHistory } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { formatNumber } from "utils";
import moment from "moment";
import {
  useEnum,
  useLazyKVMap,
  useQueryAll,
  useThietLap,
  useStore,
} from "hooks";
import {
  ENUM,
  THIET_LAP_CHUNG,
  TRANG_THAI_THUOC,
  PHAN_LOAI_DOI_TUONG_KCB,
  TRANG_THAI_XAC_NHAN_BAO_HIEM,
  TRANG_THAI_PHIEU_NHAP_XUAT,
} from "constants/index";
import {
  getAllQueryString,
  setQueryStringValues,
} from "hooks/useQueryString/queryString";
import { query } from "redux-store/stores";
import { SVG } from "assets";
import { Main } from "./styled";
import classNames from "classnames";
import { cloneDeep } from "lodash";

const { Column, Setting } = TableWrapper;

const DanhSach = (_, ref) => {
  const { t } = useTranslation();
  const refSettings = useRef(null);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);

  const {
    listDonThuocNgoaiTru,
    totalElements,
    page,
    size,
    dataSortColumn,
    isLoading,
  } = useSelector((state) => state.phatThuocNgoaiTru);
  const { data: listAllKho } = useQueryAll(query.kho.queryAllKho);
  const [listTrangThaiPhieuNhapXuat] = useEnum(ENUM.TRANG_THAI_PHIEU_NHAP_XUAT);
  const [dataHIEN_THI_TRANG_THAI_XAC_NHAN_BHYT] = useThietLap(
    THIET_LAP_CHUNG.HIEN_THI_TRANG_THAI_XAC_NHAN_BHYT
  );
  const selectedRowId = useStore("phatThuocNgoaiTru.selectedRowId");

  const [getKhoById] = useLazyKVMap(listAllKho);

  useImperativeHandle(ref, () => ({
    getSelectedRowKeys: () => selectedRowKeys,
    getListHuyGiuTon: () => {
      const validKeys = selectedRowKeys.filter((id) => {
        const donThuoc = listDonThuocNgoaiTru.find((x) => x.id === id);
        return (
          donThuoc?.trangThai === TRANG_THAI_PHIEU_NHAP_XUAT.TAO_MOI_DA_GIU_CHO
        );
      });
      return validKeys;
    },
    setSelectedRowKeys: (keys) => setSelectedRowKeys(keys),
  }));

  const {
    phatThuocNgoaiTru: {
      getListDonThuocNgoaiTru,
      onSizeChange,
      onSortChange,
      updateData,
    },
  } = useDispatch();
  const listTrangThaiDls = Object.values(TRANG_THAI_THUOC);

  useEffect(() => {
    return () => {
      updateData({ dataSearch: {}, showButtonHuyGiuTon: false });
    };
  }, []);

  useEffect(() => {
    setSelectedRowKeys([]);
  }, [listDonThuocNgoaiTru]);

  const donThuocMap = useMemo(() => {
    return new Map((listDonThuocNgoaiTru || []).map((x) => [x.id, x]));
  }, [listDonThuocNgoaiTru]);

  const onChangePage = (page) => {
    setQueryStringValues({ page: page - 1 });
    getListDonThuocNgoaiTru({ page: page - 1 });
  };

  const handleSizeChange = (size) => {
    setQueryStringValues({ size: size, page: 0 });
    onSizeChange({ size });
  };

  const onClickSort = (key, value) => {
    let sort = cloneDeep(dataSortColumn);
    sort[key] = value;
    for (let key in sort) {
      if (!sort[key]) delete sort[key];
    }
    setQueryStringValues({ dataSortColumn: JSON.stringify(sort), page: 0 });
    onSortChange({ [key]: value });
  };

  const history = useHistory();

  const onOpenDetail = (record) => (e) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }
    history.push({
      pathname: `/kho/phat-thuoc-ngoai-tru/chi-tiet/${record.id}`,
      state: {
        queryString: getAllQueryString(undefined, {
          filterEmpty: false,
        }),
        record,
      },
    });
  };

  const onRow = (record) => {
    return {
      onClick: () => {
        const selection = window.getSelection();
        if (selection.type !== "Range") {
          onOpenDetail(record)();
          updateData({
            selectedRowId: record.id,
          });
        }
      },
    };
  };

  const setRowClassName = (record) => {
    return classNames("", {
      "row-orange": !!record?.lyDo,
      "row-selected-detail": record.id === selectedRowId,
    });
  };

  const renderTrangThaiDls = (data) => {
    const { trangThaiDls } = data || {};
    let title = "";
    if (trangThaiDls) {
      title = listTrangThaiDls?.find((item) => item.id === trangThaiDls)?.i18;
      if (title) return t(title);
    } else {
      return t("kho.choDuyetDLS");
    }
  };

  const columns = [
    {
      title: <HeaderSearch title={t("common.stt")} />,
      width: 50,
      dataIndex: "index",
      key: "index",
      align: "center",
      ignore: true,
      fixed: "left",
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.sttXuatThuoc")}
          sort_key="stt"
          dataSort={dataSortColumn["stt"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: 100,
      dataIndex: "stt",
      key: "stt",
      align: "center",
    },
    {
      title: (
        <HeaderSearch
          title={t("common.soPhieu")}
          sort_key="soPhieu"
          dataSort={dataSortColumn["soPhieu"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: 120,
      dataIndex: "soPhieu",
      i18Name: "common.soPhieu",
      key: "soPhieu",
      align: "center",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.trangThaiDuyetDls")}
          sort_key="trangThaiDls"
          dataSort={dataSortColumn["trangThaiDls"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: 140,
      dataIndex: "trangThaiDls",
      i18Name: "kho.trangThaiDuyetDls",
      key: "trangThaiDls",
      show: true,
      render: (_, item) => renderTrangThaiDls(item),
    },
    Column({
      title: t("kho.kho"),
      width: 160,
      dataIndex: "khoId",
      i18Name: "kho.kho",
      key: "khoId",
      show: true,
      render: (value) => {
        return getKhoById(value)?.ten || "";
      },
    }),
    {
      title: (
        <HeaderSearch
          title={t("common.khoa")}
          sort_key="tenKhoaNb"
          dataSort={dataSortColumn["tenKhoaNb"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: 180,
      dataIndex: "tenKhoaNb",
      i18Name: "common.khoa",
      key: "tenKhoaNb",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("common.hoTenNguoiBenh")}
          sort_key="thanhTien"
          dataSort={dataSortColumn["tenNb"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: 220,
      dataIndex: "tenNb",
      i18Name: "common.hoTenNguoiBenh",
      key: "tenNb",
      align: "left",
      show: true,
      className: "tenNb",
      render: (item, _, __) =>
        item && <span style={{ fontWeight: "500" }}>{item}</span>,
    },
    {
      title: (
        <HeaderSearch
          title={t("common.thanhTien")}
          sort_key="thanhTien"
          dataSort={dataSortColumn["thanhTien"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: 120,
      dataIndex: "thanhTien",
      i18Name: "common.thanhTien",
      key: "thanhTien",
      align: "right",
      show: true,
      render: (field, _, __) =>
        (field && formatNumber(Number.parseFloat(`${field}`))) || "",
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.thanhTienSauVat")}
          sort_key="thanhTienSauVat"
          dataSort={dataSortColumn["thanhTienSauVat"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: 120,
      dataIndex: "thanhTienSauVat",
      i18Name: "kho.thanhTienSauVat",
      key: "thanhTienSauVat",
      align: "right",
      show: true,
      render: (field, _, __) =>
        (field && formatNumber(Number.parseFloat(`${field}`))) || "",
    },
    {
      title: (
        <HeaderSearch
          title={t("common.maHoSo")}
          sort_key="trangThai"
          dataSort={dataSortColumn["maHoSo"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: 120,
      dataIndex: "maHoSo",
      i18Name: "common.maHoSo",
      key: "maHoSo",
      align: "center",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("common.maNb")}
          sort_key="maNb"
          dataSort={dataSortColumn["maNb"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: 120,
      dataIndex: "maNb",
      i18Name: "common.maNb",
      key: "maNb",
      align: "center",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("common.diaChi")}
          sort_key="diaChi"
          dataSort={dataSortColumn["diaChi"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: 250,
      dataIndex: "diaChi",
      i18Name: "common.diaChi",
      key: "diaChi",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("tiepDon.ngayDangKy")}
          sort_key="thoiGianVaoVien"
          dataSort={dataSortColumn["thoiGianVaoVien"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: 160,
      dataIndex: "thoiGianVaoVien",
      i18Name: "tiepDon.ngayDangKy",
      key: "thoiGianVaoVien",
      align: "center",
      show: true,
      render: (item) => {
        return item ? moment(item).format("DD/MM/YYYY HH:mm:ss") : null;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.ngayTaoDon")}
          sort_key="thoiGianTaoPhieu"
          dataSort={dataSortColumn["thoiGianTaoPhieu"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: 160,
      dataIndex: "thoiGianTaoPhieu",
      i18Name: "kho.ngayTaoDon",
      key: "thoiGianTaoPhieu",
      align: "center",
      show: true,
      render: (item) => {
        return item ? moment(item).format("DD/MM/YYYY HH:mm:ss") : null;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("kho.ngayHoanThanh")}
          sort_key="thoiGianDuyet"
          dataSort={dataSortColumn["thoiGianDuyet"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: 160,
      dataIndex: "thoiGianDuyet",
      i18Name: "kho.ngayHoanThanh",
      key: "thoiGianDuyet",
      align: "center",
      show: true,
      render: (item) => {
        return item ? moment(item).format("DD/MM/YYYY HH:mm:ss") : null;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("nhaThuoc.trangThaiDon")}
          sort_key="trangThai"
          dataSort={dataSortColumn["trangThai"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: 140,
      dataIndex: "trangThai",
      i18Name: "nhaThuoc.trangThaiDon",
      key: "trangThai",
      show: true,
      render: (item) => {
        return listTrangThaiPhieuNhapXuat.find((x) => x.id === item)?.ten;
      },
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.doiTuongKCB")}
          sort_key="doiTuongKcb"
          dataSort={dataSortColumn["doiTuongKcb"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: 120,
      dataIndex: "doiTuongKcb",
      key: "doiTuongKcb",
      i18Name: "thuNgan.doiTuongKCB",
      show: true,
      render: (item) => {
        const doiTuong = PHAN_LOAI_DOI_TUONG_KCB.find((value) =>
          value.referIds.includes(item)
        );
        return doiTuong ? t(doiTuong.i18n) || doiTuong.ten : null;
      },
    },
    {
      title: <HeaderSearch title={t("common.lyDoHuy")} />,
      width: 200,
      dataIndex: "lyDo",
      key: "lyDo",
      i18Name: "common.lyDo",
      show: true,
    },
    {
      title: (
        <HeaderSearch
          title={t("thuNgan.xacNhanBhyt")}
          sort_key="trangThaiXacNhanBaoHiem"
          dataSort={dataSortColumn["trangThaiXacNhanBaoHiem"] || ""}
          onClickSort={onClickSort}
        />
      ),
      width: 140,
      dataIndex: "trangThaiXacNhanBaoHiem",
      key: "trangThaiXacNhanBaoHiem",
      show: dataHIEN_THI_TRANG_THAI_XAC_NHAN_BHYT?.eval(),
      hidden: !dataHIEN_THI_TRANG_THAI_XAC_NHAN_BHYT?.eval(),
      i18Name: "thuNgan.xacNhanBhyt",
      render: (item) => {
        const trangThai = TRANG_THAI_XAC_NHAN_BAO_HIEM.find(
          (value) => value.id === item
        );
        return trangThai ? t(trangThai.i18n) || trangThai.ten : null;
      },
    },
    {
      title: (
        <HeaderSearch
          title={
            <>
              {t("kho.xemDonThuoc")} <Setting refTable={refSettings} />
            </>
          }
        />
      ),
      width: 140,
      dataIndex: "action",
      key: "action",
      align: "center",
      fixed: "right",
      ignore: true,
      render: () => {
        return (
          <Tooltip title={t("kho.xemDonThuoc")}>
            <SVG.IcEye className="ic-action" />
          </Tooltip>
        );
      },
    },
  ];

  return (
    <Main noPadding={true} top={10}>
      <TableWrapper
        columns={columns}
        loading={isLoading}
        dataSource={listDonThuocNgoaiTru || []}
        onRow={onRow}
        rowSelection={{
          type: "checkbox",
          selectedRowKeys,
          onChange: (selectedRowKeys) => {
            const showButtonHuyGiuTon = selectedRowKeys.some(
              (id) =>
                donThuocMap.get(id)?.trangThai ===
                TRANG_THAI_PHIEU_NHAP_XUAT.TAO_MOI_DA_GIU_CHO
            );

            updateData({ showButtonHuyGiuTon });
            setSelectedRowKeys(selectedRowKeys);
          },
        }}
        rowKey="id"
        rowClassName={setRowClassName}
        scroll={{ x: 2100 }}
        ref={refSettings}
        tableName={"table_KHO_DSPhatThuocNgoaiTruRaVien"}
      />
      {!!totalElements && (
        <Pagination
          onChange={onChangePage}
          current={page + 1}
          pageSize={size}
          listData={listDonThuocNgoaiTru}
          total={totalElements}
          onShowSizeChange={handleSizeChange}
          useSearchParamsPageSize={false}
        />
      )}
    </Main>
  );
};

export default forwardRef(DanhSach);
