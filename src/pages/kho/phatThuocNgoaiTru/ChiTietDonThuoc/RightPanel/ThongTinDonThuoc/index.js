import React, { useState, useRef, useEffect, useMemo } from "react";
import { Main, GlobalStyle } from "./styled";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import { Row, Menu, message } from "antd";
import moment from "moment";
import { useHistory, useParams, useLocation } from "react-router-dom";
import ModalNhapLyDo from "pages/kho/components/ModalNhapLyDo";
import { useConfirm, useEnum, useLoading, useStore, useThietLap } from "hooks";
import {
  ENUM,
  HOTKEY,
  LOAI_KHO,
  LOAI_NHAP_XUAT,
  ROLES,
  THIET_LAP_CHUNG,
  TRANG_THAI_HOAN,
  TRANG_THAI_PHIEU_NHAP_XUAT_2,
} from "constants/index";
import { Button, Dropdown } from "components";
import { SVG } from "assets";
import { checkRole } from "lib-utils/role-utils";
import printProvider from "data-access/print-provider";
import { transformObjToQueryString } from "hooks/useQueryString/queryString";
import { isArray, isNumber, roundToDigits } from "utils/index";
import phieuNhapXuatProvider from "data-access/kho/phieu-nhap-xuat-provider";
import { toSafePromise } from "lib-utils";

const ThongTinDonThuoc = ({ isThemMoi, layerId }) => {
  const { showConfirm } = useConfirm();
  const refModalNotification = useRef(null);
  const { id } = useParams();
  const { t } = useTranslation();
  const history = useHistory();
  const { showLoading, hideLoading } = useLoading();
  const { state: locationState } = useLocation();
  const { queryString, record } = locationState || {};
  const infoPatient = useStore("thuocChiTiet.infoPatient", {});
  const listAllKho = useStore("kho.listAllKho", []);
  const listKhoUser = useStore("kho.listKhoUser", []);
  const [dataCANH_BAO_DUYET_DLS_KHI_PHAT_THUOC] = useThietLap(
    THIET_LAP_CHUNG.CANH_BAO_DUYET_DLS_KHI_PHAT_THUOC_TAO_PHIEU_LINH
  );
  const [dataTU_DONG_LAM_TRON_SO_LUONG_KE_THUOC] = useThietLap(
    THIET_LAP_CHUNG.TU_DONG_LAM_TRON_SO_LUONG_KE_THUOC
  );
  const [dataGOI_STT_PHAT_THUOC_BHYT] = useThietLap(
    THIET_LAP_CHUNG.GOI_STT_PHAT_THUOC_BHYT
  );
  const [dataTRANG_THAI_IN_PHIEU_PHAT_THUOC] = useThietLap(
    THIET_LAP_CHUNG.TRANG_THAI_IN_PHIEU_PHAT_THUOC
  );
  const [dataQUET_DON_THUOC_CHUYEN_CHO_PHAT] = useThietLap(
    THIET_LAP_CHUNG.QUET_DON_THUOC_CHUYEN_CHO_PHAT
  );
  const {
    thuocChiTiet: {
      updateData,
      updateGhiChuDonThuocById,
      searchDonThuoc,
      inTemThuoc,
    },
    phatThuocNgoaiTru: { postDuyet, postHuyDuyet, giuTon, huyGiuTon },
    kho: { getListAllKho, getTheoTaiKhoan },
    phieuIn: { getListPhieu, getFilePhieuIn, showFileEditor },
    phieuNhapXuat: { huySoanDonPhieuNhapXuat },
  } = useDispatch();
  const [listTrangThaiPhieuNhapXuat] = useEnum(ENUM.TRANG_THAI_PHIEU_NHAP_XUAT);

  const { onRegisterHotkey } = useDispatch().phimTat;
  const refF4 = useRef();
  const refF12 = useRef();
  const refModalNhapLyDo = useRef(null);
  const [state, _setState] = useState({});
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  useEffect(() => {
    getTheoTaiKhoan({ dsLoaiKho: LOAI_KHO.NHA_THUOC });
    getListAllKho({ active: true, page: "", size: "" });
    // đăng ký phím tắt
    onRegisterHotkey({
      layerId,
      hotKeys: [
        {
          keyCode: HOTKEY.F4, //F4
          onEvent: () => {
            refF4.current && refF4.current();
          },
        },
        {
          keyCode: HOTKEY.F12, //F12
          onEvent: () => {
            if (refF12.current) refF12.current();
          },
        },
      ],
    });
  }, []);

  useEffect(() => {
    if (infoPatient?.nbThongTinChung?.id)
      getListPhieu({
        nbDotDieuTriId: infoPatient?.nbThongTinChung?.id,
        maManHinh: "018",
        maViTri: "01801",
        chiDinhTuDichVuId: id,
      }).then((res) => {
        setState({
          listPhieu: res || [],
        });
      });
  }, [infoPatient]);

  refF12.current = () => {
    if (
      !infoPatient?.phieuThu?.thanhToan &&
      (infoPatient?.phieuXuat?.trangThai === 10 ||
        infoPatient?.phieuXuat?.trangThai === 15 ||
        infoPatient?.phieuXuat?.trangThai === 20) &&
      refModalNotification.current
    )
      refModalNotification.current.show();
  };

  useEffect(() => {
    if (listKhoUser?.length === 1) {
      // default khoId , nếu kho chỉ có 1 giá trị
      setState({ khoId: listKhoUser[0].id });
      updateData({ khoId: listKhoUser[0].id });
    }
  }, [listKhoUser]);

  const onChange = (key) => (e) => {
    const value = (e?.target && e.target.value) || e;
    setState({ [key]: value });
    if (key === "khoId") {
      updateData({ khoId: value });
    }
  };

  const onHuyPhat = () => {
    refModalNhapLyDo.current &&
      refModalNhapLyDo.current.show(
        {
          title: t("kho.lyDoHuyPhat"),
          message: t("kho.dienLyDoHuyPhat"),
        },
        (lyDo) => {
          postHuyDuyet({ id, lyDo }).then(() => {
            searchDonThuoc(id);
          });
        }
      );
  };
  const onConfirm = (callback) =>
    showConfirm(
      {
        title: t("common.canhBao"),
        content: `${t("kho.donThuocChuaDuocDuyetLamSang")}.\r\n${t(
          "kho.tiepTucPhatThuoc"
        )}?`,
        cancelText: t("common.dong"),
        okText: t("kho.phatThuoc.title"),
        showBtnOk: true,
        typeModal: "warning",
        classNameOkText: "button-warning",
      },
      () => {
        callback();
      },
      () => {}
    );
  const onPhat = () => {
    const duyetPhatThuoc = () =>
      postDuyet({ id }).then(() => {
        const { maHoSo, ...rest } = queryString || {};
        history.push(
          "/kho/phat-thuoc-ngoai-tru" + transformObjToQueryString({ ...rest })
        );
      });
    const action = () =>
      dataCANH_BAO_DUYET_DLS_KHI_PHAT_THUOC?.toLowerCase() === "true" &&
      record.trangThai === 15 &&
      !record.trangThaiDls
        ? onConfirm(duyetPhatThuoc)
        : duyetPhatThuoc();
    action();
  };

  const onHuyXuat = () => {
    refModalNhapLyDo.current &&
      refModalNhapLyDo.current.show(
        {
          title: t("kho.lyDoHuyXuat"),
          message: t("kho.dienLyDoHuyXuat"),
        },
        async (lyDo) => {
          const [err] = await toSafePromise(
            phieuNhapXuatProvider.tuChoiDuyet({ id, lyDo })
          );
          if (err) {
            message.error(err.message || t("kho.huyXuatKhongThanhCong"));
          } else {
            message.success(t("kho.huyXuatThanhCong"));
            searchDonThuoc(id);
          }
        }
      );
  };

  const onHuySoanDon = () => {
    huySoanDonPhieuNhapXuat({ id }).then(() => {
      searchDonThuoc(id);
    });
  };

  const onHuyGiuTon = () => {
    huyGiuTon({ id }).then(() => {
      searchDonThuoc(id);
    });
  };
  const onGiuTon = () => {
    giuTon(id).then(() => {
      searchDonThuoc(id);
    });
  };

  const renderButton = () => {
    const isShowButtonHuyXuat =
      infoPatient?.phieuXuat?.trangThai === 20 &&
      !!dataGOI_STT_PHAT_THUOC_BHYT?.eval();

    return (
      <Row className="select-row-last">
        {infoPatient?.phieuXuat?.trangThai2 ===
          TRANG_THAI_PHIEU_NHAP_XUAT_2.CHO_PHAT &&
          dataQUET_DON_THUOC_CHUYEN_CHO_PHAT?.eval() && (
            <Button type="primary" minWidth={100} onClick={onHuySoanDon}>
              {t("kho.huySoanDon")}
            </Button>
          )}
        {infoPatient?.phieuXuat?.loaiNhapXuat !=
          LOAI_NHAP_XUAT.XUAT_VAC_XIN_TIEM && (
          <>
            {infoPatient?.phieuXuat?.trangThai == 10 &&
              checkRole([ROLES["PHAT_THUOC_NGOAI_TRU"].GIU_TON]) && (
                <Button type="default" minWidth={100} onClick={onGiuTon}>
                  {t("kho.giuTon")}
                </Button>
              )}
            {/* Tạo mới, đã giữ chỗ */}
            {infoPatient?.phieuXuat?.trangThai == 15 &&
              checkRole([ROLES["PHAT_THUOC_NGOAI_TRU"].HUY_GIU_TON]) && (
                <Button type="default" minWidth={100} onClick={onHuyGiuTon}>
                  {t("kho.huyGiuTon")}
                </Button>
              )}
          </>
        )}

        {infoPatient?.phieuXuat?.trangThai !== 30 ? (
          <Button type="primary" minWidth={100} onClick={onPhat}>
            {t("nhaThuoc.phat")}
          </Button>
        ) : (
          <Button type="primary" minWidth={100} onClick={onHuyPhat}>
            {t("kho.huyPhat")}
          </Button>
        )}
        {isShowButtonHuyXuat && (
          <Button type="default" minWidth={100} onClick={onHuyXuat}>
            {t("kho.huyXuat")}
          </Button>
        )}
      </Row>
    );
  };

  const onPrintPhieu = (item) => async () => {
    if (item?.type == "editor") {
      showFileEditor({
        phieu: item,
        nbDotDieuTriId: infoPatient?.nbThongTinChung?.id,
      });
    } else {
      try {
        showLoading();
        const { finalFile, dsPhieu } = await getFilePhieuIn({
          listPhieus: [item],
          showError: true,
          phieuNhapXuatId: id,
          loaiNhapXuat: 20,
          nbDotDieuTriId: infoPatient?.nbThongTinChung?.id,
        });
        if ((dsPhieu || []).every((x) => x?.loaiIn == 20)) {
          openInNewTab(finalFile);
        } else {
          printProvider.printPdf(dsPhieu);
        }
      } catch (error) {
        console.log("error", error);
      } finally {
        hideLoading();
      }
    }
  };

  const contentPrint = useMemo(() => {
    let _listPhieu = state?.listPhieu || [];
    const dsTrangThaiPhieu = (dataTRANG_THAI_IN_PHIEU_PHAT_THUOC || "")
      .split(",")
      .map((i) => parseInt(i.trim()))
      .filter((i) => !isNaN(i));

    if (
      isArray(dsTrangThaiPhieu, true) &&
      !dsTrangThaiPhieu.includes(infoPatient?.phieuXuat?.trangThai)
    ) {
      _listPhieu = _listPhieu.filter((x) => x.ma !== "P166"); //Phiếu phát thuốc
    }
    return (
      <Menu
        items={_listPhieu.map((item, index) => ({
          key: index,
          label: (
            <a href={() => false} onClick={onPrintPhieu(item)}>
              {item.ten || item.tenBaoCao}
            </a>
          ),
        }))}
      />
    );
  }, [state.listPhieu]);

  const isShowTienConLai = useMemo(() => {
    if (infoPatient?.nbThongTinChung) {
      const { tienTamUng, tienHoanUng, tienHoanUngThanhToan } =
        infoPatient.nbThongTinChung || {};
      const tongTienHoanUng = (tienHoanUng || 0) + (tienHoanUngThanhToan || 0);
      return (tienTamUng || 0) > 0 && tienTamUng > tongTienHoanUng;
    }
    return false;
  }, [infoPatient?.nbThongTinChung]);

  const onPrintTemThuoc = () => {
    inTemThuoc({
      phieuNhapXuatId: infoPatient?.phieuXuat?.id,
      loaiNhapXuat: 120,
    });
  };

  const TinhSoCap = (field, item, heSoDinhMuc = 1) => {
    let value = item?.[field] ? item?.[field] / heSoDinhMuc : null;
    value =
      value && dataTU_DONG_LAM_TRON_SO_LUONG_KE_THUOC.toLowerCase() == "true"
        ? roundToDigits(value, 3)
        : value;
    return value;
  };

  return (
    <Main>
      <GlobalStyle />
      <div className="info">
        <div className="title">{t("kho.thongTinDonThuoc")}</div>
        <Row className="select-row-2">
          <div className="title-item">{t("kho.kho")}:</div>
          <div className="title-item">
            <b>
              {(listAllKho || []).find((x) => x.id === infoPatient?.khoId)?.ten}
            </b>
          </div>
        </Row>
        <Row className="select-row-2">
          <div className="title-item">{t("thuNgan.tongTienBH")}:</div>
          <div className="title-item">
            <b>
              {(infoPatient?.dsThuoc || [])
                .filter((x) =>
                  [
                    TRANG_THAI_HOAN.THUONG,
                    TRANG_THAI_HOAN.CHO_DUYET_HOAN,
                  ]?.includes(x.nbDichVu.trangThaiHoan)
                )
                ?.reduce((acc, cur) => {
                  const { nbDichVu, nbDvKho } = cur || {};
                  const soLuong = TinhSoCap(
                    "soLuongYeuCau",
                    nbDvKho,
                    nbDichVu?.dichVu?.heSoDinhMuc
                  );
                  const tongTien =
                    isNumber(soLuong) && isNumber(nbDichVu?.giaBaoHiem)
                      ? soLuong * nbDichVu.giaBaoHiem
                      : 0;
                  return acc + tongTien;
                }, 0)
                .formatPrice()}
            </b>
          </div>
        </Row>
        <Row className="select-row-2">
          <div className="title-item">{t("common.thanhTien")}:</div>
          <div className="title-item">
            <b>
              {(infoPatient?.dsThuoc || [])
                .filter((x) =>
                  [
                    TRANG_THAI_HOAN.THUONG,
                    TRANG_THAI_HOAN.CHO_DUYET_HOAN,
                  ]?.includes(x.nbDichVu.trangThaiHoan)
                )
                ?.reduce(
                  (total, item) =>
                    (total = total + (item.nbDichVu.thanhTien || 0)),
                  0
                )
                .formatPrice()}
            </b>
          </div>
        </Row>
        {isShowTienConLai && (
          <Row className="select-row-2">
            <div className="title-item">{t("common.soTienConLai")}:</div>
            <div className="title-item">
              <b>
                {(infoPatient?.nbThongTinChung?.tienConLai || 0).formatPrice()}
              </b>
            </div>
          </Row>
        )}
        <Row>
          <div
            xxl={24}
            className={infoPatient?.phieuThu?.thanhToan ? "title-2" : "title-1"}
          >
            {t("common.ghiChu")}
          </div>
          <textarea
            className="textarea"
            // defaultValue={selectedDonThuoc?.nbDichVu?.ghiChu}
            onChange={onChange("ghiChu")}
            defaultValue={infoPatient?.phieuXuat?.ghiChu}
            onBlur={(e) => {
              if (isThemMoi) {
              } else {
                updateGhiChuDonThuocById({
                  id,
                  phieuXuat: { ghiChu: e.target.value },
                });
              }
            }}
          ></textarea>
        </Row>
        <Row>
          <div
            className={infoPatient?.phieuThu?.thanhToan ? "title-2" : "title-1"}
          >
            {t("kho.soPhieu")}: {infoPatient?.phieuXuat?.soPhieu}
          </div>
        </Row>
        <Row>
          <div
            className={infoPatient?.phieuThu?.thanhToan ? "title-2" : "title-1"}
          >
            {t("nhaThuoc.trangThaiDon")}:
            <span
              style={{
                color:
                  infoPatient?.phieuXuat?.trangThai === 15 ? "red" : "#049254",
              }}
            >
              {` ${
                infoPatient?.phieuXuat?.trangThai
                  ? listTrangThaiPhieuNhapXuat?.find(
                      (item) => item.id === infoPatient?.phieuXuat?.trangThai
                    )?.ten || ""
                  : ""
              }`}
            </span>
          </div>
        </Row>
        <Row>
          <div
            className={infoPatient?.phieuThu?.thanhToan ? "title-2" : "title-1"}
          >
            {t("hsba.thoiGianPhat")}:
            {` ${
              infoPatient?.phieuXuat?.thoiGianDuyet
                ? moment(infoPatient?.phieuXuat?.thoiGianDuyet).format(
                    "DD/MM/YYYY HH:mm:ss"
                  )
                : ""
            }`}
          </div>
        </Row>
        <Row>
          <div
            className={infoPatient?.phieuThu?.thanhToan ? "title-2" : "title-1"}
          >
            {t("nhaThuoc.nguoiPhat")}:
            {` ${
              infoPatient?.phieuXuat?.nguoiDuyet?.ten
                ? infoPatient.phieuXuat.nguoiDuyet.ten
                : ""
            }`}
          </div>
        </Row>
        {dataQUET_DON_THUOC_CHUYEN_CHO_PHAT?.eval() && (
          <>
            <Row>
              <div
                className={
                  infoPatient?.phieuThu?.thanhToan ? "title-2" : "title-1"
                }
              >
                {t("kho.nguoiSoanDon")}:
                {` ${infoPatient.phieuXuat?.nguoiSoanDon?.ten ?? ""}`}
              </div>
            </Row>
          </>
        )}
      </div>
      <div className="footer">
        {renderButton()}
        {!isThemMoi &&
          infoPatient?.phieuXuat?.loaiNhapXuat !=
            LOAI_NHAP_XUAT.XUAT_VAC_XIN_TIEM && (
            <Dropdown
              overlay={contentPrint}
              trigger={["click"]}
              placement="topLeft"
            >
              {/* <Popover overlayClassName="kho-print-popover" content={contentPrint}> */}
              <div className="wrap-button">
                <div>
                  <Button
                    type="primary"
                    iconHeight={15}
                    minWidth={100}
                    rightIcon={<SVG.IcPrint />}
                  >
                    <span>{t("common.inGiayTo")}</span>
                  </Button>
                </div>
              </div>
              {/* </Popover> */}
            </Dropdown>
          )}
      </div>
      <ModalNhapLyDo ref={refModalNhapLyDo} />
    </Main>
  );
};

export default ThongTinDonThuoc;
