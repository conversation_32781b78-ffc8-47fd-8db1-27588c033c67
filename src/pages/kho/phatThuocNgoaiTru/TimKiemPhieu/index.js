import React, { useEffect, useMemo, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { BaseSearch } from "components";
import {
  CACHE_KEY,
  LOAI_KHO,
  LOAI_NHAP_XUAT,
  LOAI_PHONG,
  THIET_LAP_CHUNG,
  PHAN_LOAI_DOI_TUONG_KCB,
  TRANG_THAI_XAC_NHAN_BAO_HIEM,
  TRANG_THAI_THANH_TOAN_PHIEU_THU,
  ROLES,
} from "constants/index";
import { useTranslation } from "react-i18next";
import { useHistory } from "react-router-dom";
import moment from "moment";
import {
  useCache,
  useStore,
  useLazyKVMap,
  useThietLap,
  useQueryAll,
} from "hooks";
import {
  getAllQueryString,
  setQueryStringValues,
  setQueryStringValue,
} from "hooks/useQueryString/queryString";
import { query } from "redux-store/stores";
import { Main } from "./styled";
import { checkRole } from "lib-utils/role-utils";
import { isNumber, isUndefined } from "utils/index";
import { omit } from "lodash";
import { toSafePromise } from "lib-utils";

const TimKiemPhieu = (props) => {
  const { nhaTamUng, dataHIEN_THI_QUAY_THUOC_NGOAI_TRU } = props;
  const { t } = useTranslation();
  const history = useHistory();
  const { data: listAllNhanVien } = useQueryAll(
    query.nhanVien.queryAllNhanVien
  );

  const { data: listKhoUser, isFetched: isFetchedKhoUser } = useQueryAll(
    query.kho.queryKhoTheoTaiKhoan({
      params: {
        dsKhongPhaiLoaiKho: [LOAI_KHO.NHA_THUOC],
      },
    })
  );

  const authId = useStore("auth.auth.id", null);
  const { data: listAllKhoa } = useQueryAll(query.khoa.queryAllKhoa);
  const { data: listAllPhong } = useQueryAll(
    query.phong.queryAllPhong({
      params: { dsLoaiPhong: LOAI_PHONG.PHONG_KHAM_TIEM_CHUNG },
    })
  );

  const [dataGOI_STT_PHAT_THUOC_BHYT] = useThietLap(
    THIET_LAP_CHUNG.GOI_STT_PHAT_THUOC_BHYT
  );
  const [dataQUET_DON_THUOC_CHUYEN_CHO_PHAT] = useThietLap(
    THIET_LAP_CHUNG.QUET_DON_THUOC_CHUYEN_CHO_PHAT
  );

  const TRANG_THAI_DON_THUOC_NGOAI_TRU = useMemo(
    () =>
      [
        { id: 10, ten: t("common.taoMoi") },
        { id: 15, ten: t("common.taoMoiDaGiuCho") },
        {
          id: 20,
          ten: t("kho.daXuat"),
          show: !!dataGOI_STT_PHAT_THUOC_BHYT?.eval(),
        },
        { id: 30, ten: t("common.hoanThanh") },
      ].filter((x) => x.show ?? true),
    [dataGOI_STT_PHAT_THUOC_BHYT]
  );

  const [cacheDsKhoId, setCacheDsKhoId, loadFinish] = useCache(
    authId,
    CACHE_KEY.DATA_PHAT_THUOC_NGOAI_TRU,
    [],
    false
  );
  const { dataSearch } = useSelector((state) => state.phatThuocNgoaiTru);

  const [dataHIEN_THI_TRANG_THAI_XAC_NHAN_BHYT] = useThietLap(
    THIET_LAP_CHUNG.HIEN_THI_TRANG_THAI_XAC_NHAN_BHYT
  );
  const [getPhanLoaiDoiTuongKcb] = useLazyKVMap(PHAN_LOAI_DOI_TUONG_KCB);

  const {
    phatThuocNgoaiTru: {
      onChangeInputSearch,
      getListDonThuocNgoaiTru,
      onSizeChange,
    },
    danhSachPhieuThu: { goiNbTiepTheo },
    phieuNhapXuat: { soanDonPhieuNhapXuat },
  } = useDispatch();

  const macDinhThoiGianThanhToan = checkRole([
    ROLES["KHO"].MAC_DINH_GIA_TRI_BO_LOC_THOI_GIAN_THANH_TOAN_TAO_HOA_DON,
  ]);

  const [state, _setState] = useState(() => {
    const startOfDay = moment().startOf("day");
    const endOfDay = moment().endOf("day");

    return {
      dsTrangThai: [15, 10],
      dsLoaiNhapXuat: [
        LOAI_NHAP_XUAT.NB_NGOAI_TRU,
        LOAI_NHAP_XUAT.PHAT_NOI_TRU,
        LOAI_NHAP_XUAT.XUAT_VAC_XIN_TIEM,
      ],
      tuThoiGianThanhToan: macDinhThoiGianThanhToan ? startOfDay : null,
      denThoiGianThanhToan: macDinhThoiGianThanhToan ? endOfDay : null,
      tuThoiGianTaoPhieu: macDinhThoiGianThanhToan ? null : startOfDay,
      denThoiGianTaoPhieu: macDinhThoiGianThanhToan ? null : endOfDay,
      thanhToan: macDinhThoiGianThanhToan ? 50 : null,
    };
  });

  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  useEffect(() => {
    const { page, size, dataSortColumn, soPhieu, maNb, ...queries } =
      getAllQueryString(undefined, { filterEmpty: false });

    let newQueries = { ...queries };

    if (newQueries.hasOwnProperty("tuThoiGianTaoPhieu")) {
      if (isNumber(newQueries.tuThoiGianTaoPhieu)) {
        newQueries.tuThoiGianTaoPhieu = moment(
          newQueries.tuThoiGianTaoPhieu.toDateObject()
        );
      } else if (isUndefined(newQueries.tuThoiGianTaoPhieu)) {
        newQueries.tuThoiGianTaoPhieu = null;
      }
    }
    if (newQueries.hasOwnProperty("denThoiGianTaoPhieu")) {
      if (isNumber(newQueries.denThoiGianTaoPhieu)) {
        newQueries.denThoiGianTaoPhieu = moment(
          newQueries.denThoiGianTaoPhieu.toDateObject()
        );
      } else if (isUndefined(newQueries.denThoiGianTaoPhieu)) {
        newQueries.denThoiGianTaoPhieu = null;
      }
    }
    if (newQueries.hasOwnProperty("tuThoiGianThanhToan")) {
      if (isNumber(newQueries.tuThoiGianThanhToan)) {
        newQueries.tuThoiGianThanhToan = moment(
          newQueries.tuThoiGianThanhToan.toDateObject()
        );
      } else if (isUndefined(newQueries.tuThoiGianThanhToan)) {
        newQueries.tuThoiGianThanhToan = null;
      }
    }
    if (newQueries.hasOwnProperty("denThoiGianThanhToan")) {
      if (isNumber(newQueries.denThoiGianThanhToan)) {
        newQueries.denThoiGianThanhToan = moment(
          newQueries.denThoiGianThanhToan.toDateObject()
        );
      } else if (isUndefined(newQueries.denThoiGianThanhToan)) {
        newQueries.denThoiGianThanhToan = null;
      }
    }

    newQueries = Object.fromEntries(
      Object.entries(newQueries).filter(([_, v]) => !isUndefined(v))
    );

    if (newQueries.dsTrangThai) {
      let dsTrangThai = newQueries?.dsTrangThai?.split(",")?.map(Number);
      newQueries.dsTrangThai = dsTrangThai;
    }

    if (newQueries.dsPhongChiDinhId) {
      let dsPhongChiDinhId = newQueries?.dsPhongChiDinhId
        ?.split(",")
        ?.map(Number);
      newQueries.dsPhongChiDinhId = dsPhongChiDinhId;
    }

    if (newQueries.dsKhoaNbId) {
      let dsKhoaNbId = newQueries?.dsKhoaNbId?.split(",")?.map(Number);
      newQueries.dsKhoaNbId = dsKhoaNbId;
    }

    if (newQueries.dsDoiTuongKcb) {
      newQueries.dsDoiTuongKcb = parseInt(newQueries?.dsDoiTuongKcb);
    }

    if (isNumber(newQueries.trangThaiXacNhanBaoHiem)) {
      newQueries.trangThaiXacNhanBaoHiem = parseInt(
        newQueries?.trangThaiXacNhanBaoHiem
      );
    }

    if (isNumber(newQueries.thanhToan)) {
      newQueries.thanhToan = parseInt(newQueries?.thanhToan);
    }

    setState({
      page: parseInt(page || 0),
      size: parseInt(size || 10),
      ...newQueries,
    });
  }, []);

  const listKhoIdMemo = useMemo(() => {
    return listKhoUser.length
      ? listKhoUser.map((x) => {
          return x.id;
        })
      : [];
  }, [listKhoUser]);

  useEffect(() => {
    if (!loadFinish || !isFetchedKhoUser) return;
    const tuThoiGianTaoPhieu =
      state.tuThoiGianTaoPhieu instanceof moment
        ? state.tuThoiGianTaoPhieu?.format("YYYY-MM-DD 00:00:00")
        : state.tuThoiGianTaoPhieu;
    const denThoiGianTaoPhieu =
      state.denThoiGianTaoPhieu instanceof moment
        ? state.denThoiGianTaoPhieu?.format("YYYY-MM-DD 23:59:59")
        : state.denThoiGianTaoPhieu;
    const tuThoiGianThanhToan =
      state.tuThoiGianThanhToan instanceof moment
        ? state.tuThoiGianThanhToan?.format("YYYY-MM-DD 00:00:00")
        : state.tuThoiGianThanhToan;
    const denThoiGianThanhToan =
      state.denThoiGianThanhToan instanceof moment
        ? state.denThoiGianThanhToan?.format("YYYY-MM-DD 23:59:59")
        : state.denThoiGianThanhToan;
    const dsDoiTuongKcb = state.dsDoiTuongKcb
      ? getPhanLoaiDoiTuongKcb(state.dsDoiTuongKcb).referIds
      : undefined;

    if (cacheDsKhoId.length) {
      onSizeChange({
        ...state,
        dsKhoId: cacheDsKhoId,
        tuThoiGianTaoPhieu,
        denThoiGianTaoPhieu,
        tuThoiGianThanhToan,
        denThoiGianThanhToan,
        dsDoiTuongKcb,
      });
      setState({ dsKhoId: cacheDsKhoId });
    } else {
      onSizeChange({
        ...state,
        dsKhoId: listKhoIdMemo,
        tuThoiGianTaoPhieu,
        denThoiGianTaoPhieu,
        tuThoiGianThanhToan,
        denThoiGianThanhToan,
        dsDoiTuongKcb,
      });

      setState({ dsKhoId: [] });
    }
  }, [listKhoIdMemo, loadFinish, isFetchedKhoUser]);

  const listTrangThaiThanhToan = useMemo(() => {
    if (macDinhThoiGianThanhToan) {
      return TRANG_THAI_THANH_TOAN_PHIEU_THU.filter((i) => i.id === 50);
    }
    return TRANG_THAI_THANH_TOAN_PHIEU_THU;
  }, [macDinhThoiGianThanhToan, TRANG_THAI_THANH_TOAN_PHIEU_THU]);

  const onSearchInput = (key) => (e) => {
    let value = "";
    if (e.length > 0) {
      value = e;
    }
    if (key === "dsKhoId" && !value) {
      onChangeInputSearch({
        [key]: listKhoIdMemo,
      });
    } else {
      onChangeInputSearch({ [key]: value });
    }
    if (key === "dsKhoId") {
      setCacheDsKhoId(value ? value : []);
      setState({ dsKhoId: value });
    }
  };

  const onSearch = (type) => async (data) => {
    setState(data);
    if (
      (type == "maHoSo" || type === "quetDonThuocChuyenChoPhat") &&
      Object.values(data).filter(Boolean).length !== 0
    ) {
      getListDonThuocNgoaiTru({
        dataSearch: { ...dataSearch, ...data },
      }).then(async (s) => {
        const checkUniqueNbDotDieuTriI = new Set(
          s?.data?.map((item) => item.nbDotDieuTriId)
        ).size;
        const firstItem = s?.data?.[0];

        const pushChiTiet = (data) => {
          history.push({
            pathname: `/kho/phat-thuoc-ngoai-tru/chi-tiet/${data?.id}`,
            state: {
              queryString: omit(getAllQueryString(), [
                "maHoSo",
                "maThe",
                "maNb",
                "tenNb",
              ]),
              record: data,
            },
          });
        };

        if (type === "maHoSo") {
          if (checkUniqueNbDotDieuTriI > 1) return;
          if (
            nhaTamUng?.khongLaySo &&
            nhaTamUng?.id &&
            firstItem?.nbDotDieuTriId &&
            dataHIEN_THI_QUAY_THUOC_NGOAI_TRU?.eval()
          ) {
            try {
              await goiNbTiepTheo({
                quayId: nhaTamUng.id,
                nbDotDieuTriId: firstItem?.nbDotDieuTriId,
              });
            } catch (error) {
              console.error("Error calling goiNbTiepTheo:", error);
            }
          }
          if (s?.data?.length == 1) {
            setTimeout(() => {
              pushChiTiet(firstItem);
            }, 300);
          }
        } else if (
          type === "quetDonThuocChuyenChoPhat" &&
          firstItem?.id &&
          dataQUET_DON_THUOC_CHUYEN_CHO_PHAT?.eval()
        ) {
          const [err, res] = await toSafePromise(
            soanDonPhieuNhapXuat({ id: firstItem?.id })
          );
          if (res) {
            setTimeout(() => {
              pushChiTiet(firstItem);
            }, 300);
          }
        }
      });
    } else {
      onChangeInputSearch(data);
    }
  };

  const funcSearchData = (item) => {
    let data = item;
    if (data.tuThoiGianVaoVien) {
      data.tuThoiGianVaoVien = moment(data.tuThoiGianVaoVien).format(
        "YYYY-MM-DD 00:00:00"
      );
    }
    if (data.denThoiGianVaoVien) {
      data.denThoiGianVaoVien = moment(data.denThoiGianVaoVien).format(
        "YYYY-MM-DD 23:59:59"
      );
    }

    if (data.tuThoiGianDuyet) {
      data.tuThoiGianDuyet = moment(data.tuThoiGianDuyet).format(
        "YYYY-MM-DD 00:00:00"
      );
    }
    if (data.denThoiGianDuyet) {
      data.denThoiGianDuyet = moment(data.denThoiGianDuyet).format(
        "YYYY-MM-DD 23:59:59"
      );
    }

    if (data.tuThoiGianTaoPhieu) {
      data.tuThoiGianTaoPhieu = moment(data.tuThoiGianTaoPhieu).format(
        "YYYY-MM-DD 00:00:00"
      );
    }
    if (data.denThoiGianTaoPhieu) {
      data.denThoiGianTaoPhieu = moment(data.denThoiGianTaoPhieu).format(
        "YYYY-MM-DD 23:59:59"
      );
    }

    if (data.tuThoiGianThanhToan) {
      data.tuThoiGianThanhToan = moment(data.tuThoiGianThanhToan).format(
        "YYYY-MM-DD 00:00:00"
      );
    }
    if (data.denThoiGianThanhToan) {
      data.denThoiGianThanhToan = moment(data.denThoiGianThanhToan).format(
        "YYYY-MM-DD 23:59:59"
      );
    }

    if (!data.dsKhoId?.length) {
      data.dsKhoId = listKhoIdMemo;
    }

    onChangeInputSearch(data);
  };

  const onChangeDoiTuongKcb = (data) => {
    setQueryStringValue("dsDoiTuongKcb", data.dsDoiTuongKcb);
    onChangeInputSearch({
      dsDoiTuongKcb: data.dsDoiTuongKcb
        ? getPhanLoaiDoiTuongKcb(data.dsDoiTuongKcb).referIds
        : undefined,
    });
    setState({ dsDoiTuongKcb: data.dsDoiTuongKcb });
  };

  return (
    <Main>
      <BaseSearch
        cacheData={state}
        filter={{
          maxHeight: "500px",
          open: true,
          width: "110px",
          title: t("nhaThuoc.locPhieu"),
          funcSearchData: (data) => {
            const { sort, dsKhoId, dsLoaiNhapXuat, soPhieu, maNb, ...rest } =
              data;
            setQueryStringValues(rest);
            funcSearchData(data);
          },
          data: [
            {
              key: "soPhieu",
              placeholder: t("common.soPhieu"),
              type: "normal",
              title: t("common.soPhieu"),
            },
            {
              key: "nguoiDuyetId",
              placeholder: t("nhaThuoc.nguoiPhat"),
              type: "select",
              dataSelect: listAllNhanVien,
              title: t("nhaThuoc.nguoiPhat"),
            },
            {
              key: ["tuThoiGianDuyet", "denThoiGianDuyet"],
              placeholder: [t("common.tuNgay"), t("common.denNgay")],
              type: "date-1",
              title: t("nhaThuoc.ngayPhat"),
              format: "DD/MM/YYYY",
            },
            {
              key: ["tuThoiGianVaoVien", "denThoiGianVaoVien"],
              placeholder: [t("common.tuNgay"), t("common.denNgay")],
              type: "date-time-range",
              title: t("tiepDon.ngayDangKy"),
            },
            {
              title: t("kho.khoa"),
              key: "dsKhoaNbId",
              placeholder: t("kho.chonKhoaNb"),
              dataSelect: listAllKhoa,
              type: "select",
              mode: "multiple",
              defaultValue: state.dsKhoaNbId,
            },
            {
              key: "dsPhongChiDinhId",
              placeholder: t("kho.phongKhamSangLoc"),
              type: "select",
              dataSelect: listAllPhong,
              title: t("kho.phongKhamSangLoc"),
              mode: "multiple",
              defaultValue: state.dsPhongChiDinhId,
            },
          ],
        }}
        dataInput={[
          {
            widthInput: "232px",
            keyValueInput: "dsKhoId",
            functionChangeInput: ({ dsKhoId }) => {
              onSearchInput("dsKhoId")(dsKhoId);
              setState({ dsKhoId });
            },
            dropdownWidth: 260,
            type: "virtualizedSelect",
            value: state.dsKhoId,
            placeholder: t("kho.tenKho"),
            showConfirmButton: true,
            showSearch: true,
            options: listKhoUser,
          },
          {
            widthInput: "420px",
            placeholder: t("kho.quetQrNguoiBenhHoacNhapMaHoSoDeTimKiem"),
            functionChangeInput: onSearch("maHoSo"),
            isScanQR: true,
            autoFocus: true,
            qrGetValue: "maHoSo",
            keysFlexible: [
              {
                key: "tenNb",
                type: "string",
              },
              {
                key: "maHoSo",
                type: "maHoSo",
              },
              {
                key: "maThe",
                type: "hex",
              },
            ],
          },
          {
            keyValueInput: "maNb",
            widthInput: "180px",
            placeholder: t("common.maNb"),
            type: "normal",
            title: t("common.maNb"),
            functionChangeInput: onSearch("maNb"),
          },
          {
            keyValueInput: "dsDoiTuongKcb",
            widthInput: "212px",
            placeholder: t("thuNgan.doiTuongKCB"),
            functionChangeInput: onChangeDoiTuongKcb,
            type: "select",
            title: t("thuNgan.doiTuongKCB"),
            listSelect: PHAN_LOAI_DOI_TUONG_KCB,
          },
          {
            widthInput: "200px",
            keyValueInput: "dsTrangThai",
            functionChangeInput: ({ dsTrangThai }) => {
              onChangeInputSearch({
                dsTrangThai,
              });
              setState({ dsTrangThai });
              setQueryStringValue("dsTrangThai", dsTrangThai);
            },
            type: "selectCheckbox",
            virtual: true,
            listSelect: TRANG_THAI_DON_THUOC_NGOAI_TRU,
            title: t("nhaThuoc.trangThaiDon"),
            defaultValue: state.dsTrangThai,
          },
          ...(dataHIEN_THI_TRANG_THAI_XAC_NHAN_BHYT?.eval()
            ? [
                {
                  keyValueInput: "trangThaiXacNhanBaoHiem",
                  widthInput: "212px",
                  placeholder: t("thuNgan.xacNhanBhyt"),
                  functionChangeInput: ({ trangThaiXacNhanBaoHiem }) => {
                    onChangeInputSearch({
                      trangThaiXacNhanBaoHiem,
                    });
                    setState({ trangThaiXacNhanBaoHiem });
                    setQueryStringValue(
                      "trangThaiXacNhanBaoHiem",
                      trangThaiXacNhanBaoHiem
                    );
                  },
                  type: "select",
                  title: t("thuNgan.xacNhanBhyt"),
                  listSelect: TRANG_THAI_XAC_NHAN_BAO_HIEM,
                },
              ]
            : []),
          {
            widthInput: "240px",
            type: "dateRange",
            state: state,
            setState: setState,
            keyValueInput: ["tuThoiGianTaoPhieu", "denThoiGianTaoPhieu"],
            functionChangeInput: (e) => {
              const tuThoiGianTaoPhieu = e?.tuThoiGianTaoPhieu
                ? moment(e.tuThoiGianTaoPhieu)
                    .startOf("day")
                    .format("YYYY-MM-DD HH:mm:ss")
                : undefined;

              const denThoiGianTaoPhieu = e?.denThoiGianTaoPhieu
                ? moment(e.denThoiGianTaoPhieu)
                    .endOf("day")
                    .format("YYYY-MM-DD HH:mm:ss")
                : undefined;

              const newData = {
                ...e,
                ...(tuThoiGianTaoPhieu && { tuThoiGianTaoPhieu }),
                ...(denThoiGianTaoPhieu && { denThoiGianTaoPhieu }),
              };

              onChangeInputSearch(newData);
              setState(newData);
            },
            title: t("kho.ngayTaoDon"),
            placeholder: [t("common.tuNgay"), t("common.denNgay")],
            format: "DD/MM/YYYY",
            allowClear: true,
          },
          {
            widthInput: "232px",
            type: "dateRange2",
            state: state,
            setState: setState,
            keyValueInput: ["tuThoiGianThanhToan", "denThoiGianThanhToan"],
            functionChangeInput: (e) => {
              onChangeInputSearch(e);
              setState(e);
            },
            title: t("thuNgan.thoiGianThanhToan"),
            placeholder: [t("common.tuNgay"), t("common.denNgay")],
            format: "DD/MM/YYYY",
            allowClear: true,
            allowEmpty: [true, true],
          },
          {
            keyValueInput: "thanhToan",
            widthInput: "180px",
            placeholder: t("thuNgan.trangThaiThanhToan"),
            functionChangeInput: ({ thanhToan }) => {
              onChangeInputSearch({
                thanhToan,
              });
              setState({ thanhToan });
              setQueryStringValue("thanhToan", thanhToan);
            },
            type: "select",
            listSelect: listTrangThaiThanhToan,
            hasAllOption: !macDinhThoiGianThanhToan,
            allowClear: macDinhThoiGianThanhToan,
          },
          {
            widthInput: "120px",
            placeholder: t("kho.tuSo"),
            keyValueInput: "tuStt",
            functionChangeInput: onSearch("tuStt"),
            searchOnInput: true,
          },
          {
            widthInput: "120px",
            placeholder: t("kho.denSo"),
            keyValueInput: "denStt",
            functionChangeInput: onSearch("denStt"),
            searchOnInput: true,
          },
          ...(dataQUET_DON_THUOC_CHUYEN_CHO_PHAT?.eval()
            ? [
                {
                  widthInput: "490px",
                  placeholder: t(
                    "kho.quetQrNguoiBenhHoacNhapMaHoSoDeChuyenChoPhat"
                  ),
                  functionChangeInput: onSearch("quetDonThuocChuyenChoPhat"),
                  isScanQR: true,
                  autoFocus: true,
                  qrGetValue: "maHoSo",
                  keysFlexible: [
                    {
                      key: "tenNb",
                      type: "string",
                    },
                    {
                      key: "maHoSo",
                      type: "maHoSo",
                    },
                    {
                      key: "maThe",
                      type: "hex",
                    },
                  ],
                },
              ]
            : []),
        ]}
      />
    </Main>
  );
};
export default TimKiemPhieu;
