import React, {
  useState,
  useRef,
  useMemo,
  forwardRef,
  useImperativeHandle,
  useEffect,
} from "react";
import { useDispatch } from "react-redux";
import { Select } from "antd";
import ModalDanhSachHangHoa from "../ModalDanhSachHangHoa";
import Header1 from "pages/kho/components/Header1";
import { InputTimeout, InputSearch } from "components";
import { useParams } from "react-router-dom";
import { Main, AntCheckbox, GlobalStyle } from "./styled";
import { containText } from "utils";
import { useStore, useQueryString, useThietLap, useGuid } from "hooks";
import {
  LOAI_NHAP_XUAT,
  HOTKEY,
  THEO_SO_LUONG_TON_KHO,
  TRANG_THAI_PHIEU_NHAP_XUAT,
  THIET_LAP_CHUNG,
  ROLES,
} from "constants/index";
import { t } from "i18next";
import { checkRole } from "lib-utils/role-utils";
import { SVG } from "assets";
const { Option } = Select;

const SearchHangHoa = forwardRef(
  ({ isEdit, parentState, setParentState, ...props }, ref) => {
    const { id } = useParams();

    const thongTinPhieu = useStore("phieuNhapXuat.thongTinPhieu");
    const listData = useStore("tonKho.listData");
    const listHinhThucNhapXuat = useStore("hinhThucNhapXuat.listTongHop", []);
    const [dichVuId] = useQueryString("dichVuId", "");
    const [dataMA_LOAI_XUAT_HUY] = useThietLap(
      THIET_LAP_CHUNG.MA_LOAI_XUAT_HUY
    );
    const [dataNHAP_KHO_HIEN_THI_THEM_HAM_LUONG] = useThietLap(
      THIET_LAP_CHUNG.NHAP_KHO_HIEN_THI_THEM_HAM_LUONG
    );
    const listMaLoaiXuatHuy = dataMA_LOAI_XUAT_HUY.split(",");
    const {
      tonKho: { onSearch: onSearchTonKho, updateData: updateDataTonKho },
      phieuNhapXuat: {
        onSelectMultiItem,
        onSearch: onSearchChiTiet,
        updateData: updateDataPhieuNhapXuat,
      },
      phimTat: { onRegisterHotkey, onAddLayer },
    } = useDispatch();
    const refKeyDownTime = useRef(null);
    const refSearchHangHoa = useRef();
    const refTimeOut = useRef(null);
    const refModalDanhSachHangHoa = useRef(null);
    const refSelectRow = useRef(null);
    const layerId = useGuid();
    const [state, _setState] = useState({});
    const [type] = useQueryString("type", "");

    const setState = (data = {}) => {
      _setState((_state) => ({
        ..._state,
        ...data,
      }));
    };

    useImperativeHandle(ref, () => ({
      show,
    }));
    const show = () => {
      refSearchHangHoa.current.focus();
      setState({ show: true });
    };

    useEffect(() => {
      onAddLayer({ layerId: layerId });
      onRegisterHotkey({
        layerId: layerId,
        hotKeys: [
          {
            keyCode: HOTKEY.DOWN,
            onEvent: () => {
              if (refSelectRow.current) refSelectRow.current(1);
            },
          },
          {
            keyCode: HOTKEY.UP,
            onEvent: () => {
              if (refSelectRow.current) refSelectRow.current(-1);
            },
          },
          {
            keyCode: HOTKEY.ENTER,
            onEvent: (e) => {
              if (refSearchHangHoa.current) {
                refSearchHangHoa.current.focus();
                setState({ show: true });
                return true;
              }
            },
          },
        ],
      });
    }, []);

    refSelectRow.current = (index) => {
      const indexNextItem =
        (listDsDichVu?.findIndex((item) => item?.props?.value === state?.key) ||
          0) + index;
      if (-1 < indexNextItem && indexNextItem < listDsDichVu.length) {
        setState({ key: listDsDichVu[indexNextItem]?.props?.value });
      }
    };

    const isGetDataByDichVuId = [
      LOAI_NHAP_XUAT.XUAT_CHUYEN_KHO,
      LOAI_NHAP_XUAT.XUAT_TRA_NHA_CUNG_CAP,
      LOAI_NHAP_XUAT.XUAT_KHAC,
    ].includes(thongTinPhieu?.loaiNhapXuat);

    const getDataSearch = (search = {}) => {
      let dataSearch = {
        khoId: thongTinPhieu?.khoId,
        ...(["30", "45"].includes(type) && thongTinPhieu?.khoDoiUngId
          ? { khoDoiUngId: thongTinPhieu.khoDoiUngId }
          : {}),
      };
      return {
        ...dataSearch,
        ...search,
        ...(dichVuId && { vuotCoSo: true }),
        phieuNhapId:
          thongTinPhieu.loaiNhapXuat == 40 ? thongTinPhieu.phieuNhapId : "",
        ...(thongTinPhieu.loaiNhapXuat == LOAI_NHAP_XUAT.XUAT_TRA_TAI_KHOA
          ? {
              dsKhoTaiKhoaId: [thongTinPhieu.khoaChiDinhId],
            }
          : {}),
        theoSoLuongTonKho: listMaLoaiXuatHuy.includes(
          listHinhThucNhapXuat.find(
            (item) => item.id === thongTinPhieu.hinhThucNhapXuatId
          )?.ma
        )
          ? THEO_SO_LUONG_TON_KHO.CON_TON
          : THEO_SO_LUONG_TON_KHO.CON_TON_KHA_DUNG,
      };
    };

    const onSearchData = (value) => {
      onSearchChiTiet({
        phieuNhapXuatId: id,
        page: 0,
        dataSearch: { timKiem: value },
      });
    };

    const onFocus = () => {
      onSearchTonKho({
        dataSearch: getDataSearch({ timKiem: "", ten: "" }),
        page: 0,
        stateIgnoreParams: ["size"],
        fromTongHop: true,
        taiKhoa:
          thongTinPhieu?.loaiNhapXuat === LOAI_NHAP_XUAT.XUAT_TRA_TAI_KHOA
            ? true
            : false,
      });
      setState({ show: true });
    };

    const listDsDichVu = useMemo(() => {
      return listData
        ?.map((item) => {
          const ten = item?.ten || "";
          const ma = item?.ma || "";
          let tenHienThi = item?.tenHoatChat
            ? `${ma} - ${ten} - ${item.tenHoatChat}`
            : `${ma} - ${ten}`;
          const soLuongSoCapKhaDungConHsd = item?.soLuongSoCapKhaDungConHsd;
          if (
            dataNHAP_KHO_HIEN_THI_THEM_HAM_LUONG?.eval() &&
            [
              LOAI_NHAP_XUAT.XUAT_CHUYEN_KHO,
              LOAI_NHAP_XUAT.XUAT_TRA_NHA_CUNG_CAP,
              LOAI_NHAP_XUAT.XUAT_KHAC,
            ].includes(thongTinPhieu?.loaiNhapXuat)
          ) {
            const tenHamLuong = item?.hamLuong ? ` - ${item.hamLuong}` : "";
            tenHienThi += tenHamLuong;
          }
          if (soLuongSoCapKhaDungConHsd) {
            tenHienThi += ` - SL tồn: ${soLuongSoCapKhaDungConHsd}`;
          }
          return { ma, ten, tenHienThi };
        })
        .filter((item, index, self) => {
          return (
            index ===
            self.findIndex(
              (t) =>
                t.ma === item.ma &&
                t.ten === item.ten &&
                t.tenHoatChat === item.tenHoatChat
            )
          );
        })
        .map((item, index) => {
          return (
            <Option
              key={index}
              value={[item.ma, item.ten, item.dichVuId]}
              className="custom-search-option"
            >
              {item.tenHienThi}
            </Option>
          );
        });
    }, [listData, dataNHAP_KHO_HIEN_THI_THEM_HAM_LUONG, thongTinPhieu]);

    const onSearch = (type) => (e) => {
      if (
        !refKeyDownTime.current ||
        new Date() - refKeyDownTime.current > 3000
      ) {
        const value = e?.target ? e?.target?.value : e;
        if (!value) {
          updateDataTonKho({ listData: [] });
        }
        if (refTimeOut.current) {
          clearTimeout(refTimeOut.current);
          refTimeOut.current = null;
        }
        refTimeOut.current = setTimeout(() => {
          setState({ [type]: value });
          onSearchTonKho({
            page: 0,
            fromTongHop: true,
            stateIgnoreParams: ["size"],
            dataSearch: getDataSearch({ [type]: value }),
            taiKhoa:
              thongTinPhieu?.loaiNhapXuat === LOAI_NHAP_XUAT.XUAT_TRA_TAI_KHOA
                ? true
                : false,
          });
        }, 300);
      }
    };
    const onKeyDown = (e) => {
      if (e.keyCode == 13 && !state.key) {
        e.preventDefault();
        e.stopPropagation();
        refKeyDownTime.current = new Date();
        if (listDsDichVu?.length > 1) {
          setState({ show: false });
          refModalDanhSachHangHoa.current.show(
            { ten: e.target?.value },
            (data) => {
              onSelectMultiItem({ data });
            }
          );
        }
      }
    };
    const filterOption = (input = "", option) => {
      return containText(option?.props?.children, input);
    };

    const handleChangeCapNhapCoSo = (key) => (e) => {
      setParentState({ [key]: e.target.checked });
      updateDataPhieuNhapXuat({
        thongTinPhieu: { ...thongTinPhieu, [key]: e.target.checked },
      });
    };

    const handleChangeXuatTheoLo = (e) => {
      setParentState({ xuatTheoLo: e.target.checked });
    };

    const onShowDsHangHoa = (e) => {
      refModalDanhSachHangHoa.current.show(
        {
          ...(isGetDataByDichVuId
            ? { dichVuId: e[2] }
            : {
                ma: e[0],
                ten: e[1],
              }),
          dieuChinhCoSoDuoi: !!parentState?.dieuChinhCoSoDuoi,
        },
        (data) => {
          onSelectMultiItem({ data });
        }
      );
    };

    return (
      <>
        <GlobalStyle />
        <Main>
          <Header1 title={t("khoMau.thongTinHangHoa")} titleMinWidth={150}>
            <InputSearch width={460}>
              {!isEdit ? (
                <InputTimeout
                  placeholder={t("kho.nhapTenHangHoa")}
                  onChange={onSearchData}
                />
              ) : (
                <Select
                  ref={refSearchHangHoa}
                  showSearch
                  allowClear
                  onBlur={() => {
                    setState({ show: false, key: null });
                  }}
                  value={null}
                  onFocus={onFocus}
                  open={state.show}
                  onClear={() => {}}
                  placeholder={t("kho.nhapTenHangHoa")}
                  onSearch={onSearch("timKiem")}
                  onKeyDown={onKeyDown}
                  onSelect={onShowDsHangHoa}
                  // filterOption={filterOption}
                  suffixIcon={<SVG.IcArrowDown color="black" />}
                  defaultActiveFirstOption={false}
                  dropdownMatchSelectWidth={600}
                  // disabled={
                  //   thongTinPhieu.loaiNhapXuat == 40 && !thongTinPhieu.phieuNhapId
                  // }
                >
                  {listDsDichVu}
                </Select>
              )}
            </InputSearch>
            <AntCheckbox
              checked={!!parentState?.xuatTheoLo}
              onChange={handleChangeXuatTheoLo}
            >
              {t("kho.xuatTheoLo")}
            </AntCheckbox>
            <ModalDanhSachHangHoa
              ref={refModalDanhSachHangHoa}
              xuatTheoLo={parentState?.xuatTheoLo}
            />
          </Header1>
          {[LOAI_NHAP_XUAT.DU_TRU, LOAI_NHAP_XUAT.XUAT_CHUYEN_KHO].includes(
            thongTinPhieu?.loaiNhapXuat
          ) && (
            <div>
              <AntCheckbox
                checked={!!parentState?.dieuChinhCoSo}
                onChange={handleChangeCapNhapCoSo("dieuChinhCoSo")}
                disabled={
                  thongTinPhieu?.trangThai !==
                  TRANG_THAI_PHIEU_NHAP_XUAT.CHO_DUYET
                }
              >
                {t("kho.capNhatCoSoTren")}
              </AntCheckbox>
              {checkRole([ROLES["KHO"].CAP_NHAT_CO_SO_DUOI]) && (
                <AntCheckbox
                  checked={!!parentState?.dieuChinhCoSoDuoi}
                  onChange={handleChangeCapNhapCoSo("dieuChinhCoSoDuoi")}
                  disabled={
                    !(
                      (window.location.pathname.indexOf("/chinh-sua") >= 0 ||
                        window.location.pathname.indexOf("/them-moi") >= 0) &&
                      [
                        TRANG_THAI_PHIEU_NHAP_XUAT.TAO_MOI,
                        TRANG_THAI_PHIEU_NHAP_XUAT.TAO_MOI_DA_GIU_CHO,
                        TRANG_THAI_PHIEU_NHAP_XUAT.CHO_DUYET,
                      ].includes(thongTinPhieu?.trangThai)
                    )
                  }
                >
                  {t("kho.capNhatCoSoDuoi")}
                </AntCheckbox>
              )}
            </div>
          )}
        </Main>
      </>
    );
  }
);

export default SearchHangHoa;
