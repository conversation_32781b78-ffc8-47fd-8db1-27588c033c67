import React, { useMemo, useState } from "react";
import { useDispatch } from "react-redux";
import DanhSach<PERSON>angHoa from "../DanhSachHangHoa";
import { MainPage } from "./styled";
import { useEffect } from "react";
import Trang<PERSON><PERSON> from "pages/kho/components/TrangThai";
import { Toolt<PERSON>, Card, Button } from "components";
import { useParams, useLocation, useHistory } from "react-router-dom";
import ThongTinPhieuXuat from "./ThongTinPhieuXuat";
import {
  useConfirm,
  useLoading,
  useQueryString,
  useStore,
  useThietLap,
} from "hooks";
import SearchHangHoa from "../SearchHangHoa";
import Action from "../../phieuNhap/Action";
import { SVG } from "assets";
import {
  LOAI_NHAP_XUAT,
  THIET_LAP_CHUNG,
  CO_CHE_DUYET_PHAT,
  LOAI_DICH_VU,
  ROLES,
  TRANG_THAI_PHIEU_NHAP_XUAT,
  DS_TINH_CHAT_KHOA,
} from "constants/index";
import { Dropdown, Menu } from "antd";
import { transformObjToQueryString } from "hooks/useQueryString/queryString";
import { useTranslation } from "react-i18next";
import { xuatFileExcelKho } from "utils/kho-utils";
import { checkRole } from "lib-utils/role-utils";
import { isArray } from "utils/index";

const PhieuXuat = ({ ...props }) => {
  const { showConfirm } = useConfirm();
  const { id } = useParams();
  const location = useLocation();
  const [xuatType] = useQueryString("type", "");
  const history = useHistory();
  const { t } = useTranslation();
  const { showLoading, hideLoading } = useLoading();
  const [state, _setState] = useState({
    dieuChinhCoSo: false,
    dieuChinhCoSoDuoi: false,
  });

  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  const thongTinPhieu = useStore("phieuNhapXuat.thongTinPhieu");
  const [MA_LOAI_XUAT_TU_TRUC_TRA_LAI] = useThietLap(
    THIET_LAP_CHUNG.MA_LOAI_XUAT_TU_TRUC_TRA_LAI
  );
  const [dataMA_LOAI_XUAT_THANH_LY] = useThietLap(
    THIET_LAP_CHUNG.MA_LOAI_XUAT_THANH_LY
  );

  const {
    phieuNhapXuat: {
      getById,
      resetData,
      xoaPhieu,
      inPhieuNhapXuat,
      inPhieuLinh,
      xuatPhieuLinh,
      xuatPhieuNhapXuat,
      inPhieuBienBanGiaoNhan,
      inPhieuBienBanThanhLy,
    },
    chiDinhDichVuKho: { inDonThuocPhieuTiemChung },
    nbDotDieuTri: { getByTongHopId },
  } = useDispatch();

  const loaiNhapXuat = useMemo(() => {
    if (xuatType) return parseInt(xuatType);
    if (thongTinPhieu.id) return thongTinPhieu.loaiNhapXuat;
    return 0;
  }, [location, xuatType, thongTinPhieu.loaiNhapXuat]);

  const getTitlePage = useMemo(() => {
    if ([0, LOAI_NHAP_XUAT.DU_TRU].includes(loaiNhapXuat))
      return t("khoMau.chiTietPhieuXuat");
    if (loaiNhapXuat == LOAI_NHAP_XUAT.XUAT_CHUYEN_KHO)
      return t("kho.chiTietPhieuXuatChuyenKho");
    if (loaiNhapXuat == LOAI_NHAP_XUAT.XUAT_TRA_NHA_CUNG_CAP)
      return t("kho.chiTietPhieuXuatTraNhaCungCap");
    if (loaiNhapXuat == LOAI_NHAP_XUAT.XUAT_KHAC)
      return t("kho.chiTietPhieuXuatKhac");
    if (loaiNhapXuat == LOAI_NHAP_XUAT.XUAT_VC_XIN_TIEM)
      return t("kho.chiTietPhieuXuatVacXinTiem");
    if (loaiNhapXuat == LOAI_NHAP_XUAT.XUAT_TRA_TAI_KHOA)
      return t("kho.chiTietPhieuXuatTraKhoTaiKhoa");
    if (loaiNhapXuat == LOAI_NHAP_XUAT.XUAT_DAO_HAN_SU_DUNG)
      return t("kho.chiTietPhieuXuatDaoHanSuDung");
  }, [loaiNhapXuat]);
  const times = useMemo(() => {
    const { thoiGianDuyet, thoiGianGuiDuyet, thoiGianTaoPhieu } =
      thongTinPhieu || {};
    return [thoiGianTaoPhieu, thoiGianGuiDuyet, thoiGianDuyet].map(
      (item) => item
    );
  }, [thongTinPhieu]);

  useEffect(() => {
    if (thongTinPhieu.loaiNhapXuat == LOAI_NHAP_XUAT.XUAT_VAC_XIN_TIEM) {
      getByTongHopId(thongTinPhieu.nbDotDieuTriId);
    }
  }, [thongTinPhieu]);

  useEffect(() => {
    if (id) {
      getById(id);
    }
    return () => {
      resetData();
    };
  }, []);

  useEffect(() => {
    if (thongTinPhieu) {
      const { dieuChinhCoSo, dieuChinhCoSoDuoi } = thongTinPhieu;
      setState({
        dieuChinhCoSo: !!dieuChinhCoSo,
        dieuChinhCoSoDuoi: !!dieuChinhCoSoDuoi,
      });
    }
  }, [thongTinPhieu]);

  const isXacNhanNhap = useMemo(() => {
    if (thongTinPhieu) {
      const { kho, loaiNhapXuat } = thongTinPhieu || {};
      let dsCoCheDuyetPhat = kho?.dsCoCheDuyetPhat || [];
      return (
        loaiNhapXuat === LOAI_NHAP_XUAT.DU_TRU &&
        dsCoCheDuyetPhat.includes(CO_CHE_DUYET_PHAT.XAC_NHAN_NHAP)
      );
    }
    return false;
  }, [thongTinPhieu]);

  const isKhongDuyetNhap = useMemo(() => {
    if (thongTinPhieu) {
      const { khoDoiUng, loaiNhapXuat, trangThai } = thongTinPhieu || {};
      let dsCoCheDuyetPhat = khoDoiUng?.dsCoCheDuyetPhat || [];
      return (
        loaiNhapXuat === LOAI_NHAP_XUAT.XUAT_CHUYEN_KHO &&
        dsCoCheDuyetPhat.includes(CO_CHE_DUYET_PHAT.KHONG_DUYET_NHAP) &&
        trangThai === TRANG_THAI_PHIEU_NHAP_XUAT.CHO_DUYET
      );
    }
    return false;
  }, [thongTinPhieu]);

  const showGuiDuyetCustom = useMemo(() => {
    const { khoDoiUng, loaiNhapXuat, trangThai } = thongTinPhieu || {};
    const { dsLoaiDichVu } = khoDoiUng || {};
    if (
      isArray(dsLoaiDichVu, true) &&
      loaiNhapXuat === LOAI_NHAP_XUAT.DU_TRU &&
      [
        TRANG_THAI_PHIEU_NHAP_XUAT.TAO_MOI,
        TRANG_THAI_PHIEU_NHAP_XUAT.TAO_MOI_DA_GIU_CHO,
      ].includes(trangThai)
    ) {
      if (dsLoaiDichVu.includes(LOAI_DICH_VU.THUOC)) {
        return checkRole([ROLES["KHO"].GUI_DUYET_PHIEU_NHAP_KHO_THUOC]);
      } else if (
        dsLoaiDichVu.includes(LOAI_DICH_VU.VAT_TU) ||
        dsLoaiDichVu.includes(LOAI_DICH_VU.HOA_CHAT)
      ) {
        return checkRole([ROLES["KHO"].GUI_DUYET_PHIEU_NHAP_KHO_VT_HOA_CHAT]);
      }
      return false;
    }
    return false;
  }, [thongTinPhieu]);

  const showBienBanThanhLy = useMemo(() => {
    if (dataMA_LOAI_XUAT_THANH_LY) {
      let dsMaLoaiXuatThanhLy = dataMA_LOAI_XUAT_THANH_LY
        .split(",")
        .map((i) => i.trim());
      return (
        thongTinPhieu?.loaiNhapXuat === LOAI_NHAP_XUAT.XUAT_KHAC &&
        (dsMaLoaiXuatThanhLy || []).includes(
          thongTinPhieu?.hinhThucNhapXuat?.ma
        )
      );
    }
    return false;
  }, [dataMA_LOAI_XUAT_THANH_LY, thongTinPhieu]);

  const onPrint = () => {
    if (thongTinPhieu.loaiNhapXuat === LOAI_NHAP_XUAT.XUAT_VAC_XIN_TIEM) {
      inDonThuocPhieuTiemChung({
        nbDotDieuTriId: thongTinPhieu.nbDotDieuTriId,
        phieuThuId: thongTinPhieu.phieuThuId,
      });
    } else {
      inPhieuNhapXuat({ dsId: [id] });
    }
  };
  const onDelete = () => {
    showConfirm(
      {
        title: t("common.canhBao"),
        content: `${t("kho.xoaPhieuSo")} ${thongTinPhieu?.soPhieu}?`,
        cancelText: t("common.dong"),
        okText: t("common.xoa"),
        classNameOkText: "button-error",
        showImg: true,
        showBtnOk: true,
      },
      () => {
        xoaPhieu({ id: thongTinPhieu.id }).then((s) => {
          history.push("/kho/xuat-kho");
        });
      },
      () => {}
    );
  };

  const onPrintPhieuLinhHaoPhi = () => {
    inPhieuLinh({ id });
  };

  const onPrintPhieuXuatTraLai = () => {
    inPhieuNhapXuat({ dsId: [id], xuatTraLai: true });
  };

  const onPrintPhieuBienNhanBanGiao = () => {
    showLoading();
    inPhieuBienBanGiaoNhan({ id }).finally(hideLoading);
  };

  const onPrintBienBanThanhLy = () => {
    showLoading();
    inPhieuBienBanThanhLy({ id }).finally(hideLoading);
  };

  const menu = (
    <Menu
      items={[
        {
          key: 0,
          label: (
            <a href={() => false} onClick={onPrint}>
              {t("kho.inChiTietPhieuXuat")}
            </a>
          ),
        },
        {
          key: 1,
          label: (
            <a href={() => false} onClick={onPrintPhieuLinhHaoPhi}>
              {t("kho.inPhieuLinhHaoPhi")}
            </a>
          ),
        },
        {
          key: 2,
          label: (
            <a href={() => false} onClick={onPrintPhieuXuatTraLai}>
              {t("kho.phieuXuatTraLaiKho")}
            </a>
          ),
        },
        {
          key: 3,
          label: (
            <a href={() => false} onClick={onPrintPhieuBienNhanBanGiao}>
              {t("kho.inBienBanGiaoNhanVacxin")}
            </a>
          ),
        },
        ...(showBienBanThanhLy
          ? [
              {
                key: 4,
                label: (
                  <a href={() => false} onClick={onPrintBienBanThanhLy}>
                    {t("kho.inBienBanThanhLy")}
                  </a>
                ),
              },
            ]
          : []),
      ]}
    />
  );

  const onExportFileChiTietPhieuXuat = async () => {
    try {
      showLoading();
      const listMaLoaiXuatTuTruc = MA_LOAI_XUAT_TU_TRUC_TRA_LAI?.split(",");
      const payload = listMaLoaiXuatTuTruc.includes(
        thongTinPhieu?.hinhThucNhapXuat?.ma
      )
        ? { xuatTraLai: true }
        : {};

      const s = await xuatPhieuNhapXuat({ dsId: [id], ...payload });
      xuatFileExcelKho(s);
    } catch (error) {
      console.error(error);
    } finally {
      hideLoading();
    }
  };

  const onExportFilePhieuLinhHaoPhi = async () => {
    try {
      showLoading();
      const s = await xuatPhieuLinh({ id });
      xuatFileExcelKho(s);
    } catch (error) {
      console.error(error);
    } finally {
      hideLoading();
    }
  };

  const onExportFileXuatTraLai = async () => {
    try {
      showLoading();
      const s = await xuatPhieuNhapXuat({ dsId: [id], xuatTraLai: true });
      xuatFileExcelKho(s);
    } catch (error) {
      console.error(error);
    } finally {
      hideLoading();
    }
  };

  const onAdd = () => {
    history.push(
      `/kho/xuat-kho/them-moi?type=${thongTinPhieu.loaiNhapXuat}&khoId=${thongTinPhieu.khoId}`
    );
    resetData();
  };

  const items = [
    {
      key: 1,
      label: (
        <a onClick={onExportFileChiTietPhieuXuat}>
          <div className="flex icon_utilities gap-8">
            <SVG.IcDownload />
            <span>{t("kho.xuatFileChiTietPhieuXuat")}</span>
          </div>
        </a>
      ),
    },
    {
      key: 2,
      label: (
        <a onClick={onExportFilePhieuLinhHaoPhi}>
          <div className="flex icon_utilities gap-8">
            <SVG.IcDownload />
            <span>{t("kho.xuatFilePhieuLinhHaoPhi")}</span>
          </div>
        </a>
      ),
    },
    {
      key: 3,
      label: (
        <a onClick={onExportFileXuatTraLai}>
          <div className="flex icon_utilities gap-8">
            <SVG.IcDownload />
            <span>{t("kho.xuatFilePhieuXuatTraLaiKho")}</span>
          </div>
        </a>
      ),
    },
  ];

  return (
    <MainPage
      breadcrumb={[
        { title: t("kho.kho"), link: "/kho" },
        {
          title: t("kho.xuatKho"),
          link:
            "/kho/xuat-kho" + transformObjToQueryString(location?.state || {}),
        },
        {
          title: t("khoMau.chiTietPhieuXuat"),
          link: `/kho/xuat-kho/chi-tiet/${id}`,
        },
      ]}
      title={
        <>
          {getTitlePage}
          <div className="header-action">
            {[
              LOAI_NHAP_XUAT.XUAT_CHUYEN_KHO,
              LOAI_NHAP_XUAT.DU_TRU,
              LOAI_NHAP_XUAT.XUAT_KHAC,
              LOAI_NHAP_XUAT.XUAT_TRA_NHA_CUNG_CAP,
            ].includes(thongTinPhieu?.loaiNhapXuat) && (
              <Dropdown
                menu={{ items }}
                trigger={"click"}
                overlayClassName="danh-muc-dropdown-tien-ich"
              >
                <Button
                  rightIcon={<SVG.IcMore />}
                  style={{ marginLeft: "10px" }}
                  height={28}
                >
                  {t("common.tienIch")}
                </Button>
              </Dropdown>
            )}
            {[10, 15].includes(thongTinPhieu?.trangThai) && (
              <div className="action-btn" onClick={onDelete}>
                <SVG.IcDelete />
              </div>
            )}
            {[
              LOAI_NHAP_XUAT.XUAT_CHUYEN_KHO,
              LOAI_NHAP_XUAT.XUAT_KHAC,
              LOAI_NHAP_XUAT.XUAT_TRA_NHA_CUNG_CAP,
            ].includes(thongTinPhieu?.loaiNhapXuat) && (
              <div className="action-btn" onClick={onAdd}>
                <SVG.IcAdd />
              </div>
            )}
          </div>
        </>
      }
      titleRight={<TrangThai times={times} />}
    >
      <Card>
        <ThongTinPhieuXuat />
      </Card>
      <Card noPadding={true} className="thong-tin-hang-hoa">
        <SearchHangHoa
          isEdit={false}
          parentState={state}
          setParentState={setState}
        />
        <DanhSachHangHoa parentState={state} {...props} isEdit={false} />
      </Card>

      <Action
        loaiPhieu={1}
        parentState={state}
        allowEdit={[10, 15, 20].includes(thongTinPhieu?.trangThai)}
        hiddenCancel={
          thongTinPhieu?.loaiNhapXuat === LOAI_NHAP_XUAT.XUAT_CHUYEN_KHO &&
          thongTinPhieu?.trangThai === 30
        }
        otherBtn={
          [20, 28, 30].includes(thongTinPhieu?.trangThai) && (
            <Dropdown overlay={menu} trigger={["click"]}>
              <Button rightIcon={<SVG.IcPrint />} iconHeight={15}>
                {t("common.inGiayTo")}
              </Button>
            </Dropdown>
          )
        }
        showBack={true}
        isXacNhanNhap={isXacNhanNhap}
        isKhongDuyetNhap={isKhongDuyetNhap}
        showGuiDuyetCustom={showGuiDuyetCustom}
      />
    </MainPage>
  );
};

export default PhieuXuat;
