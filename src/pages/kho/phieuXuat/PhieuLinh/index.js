import React, { useEffect, useMemo, useRef, useState } from "react";
import { useConfirm, useLoading } from "hooks";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { Dropdown, Card, Tooltip, TableWrapper, Button } from "components";
import { Menu, Tabs } from "antd";
import ThongTinPhieu from "./ThongTinPhieu";
import DanhSachHangHoa from "./DanhSachHangHoa";
import { useHistory, useLocation, useParams } from "react-router-dom";
import TrangThai from "pages/kho/components/TrangThai";
import Action from "../../phieuNhap/Action";
import DanhSachNguoiBenh from "./DanhSachNguoiBenh";
import { SVG } from "assets";
import { transformObjToQueryString } from "hooks/useQueryString/queryString";
import SoLuongLeLinhSau from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/PhieuLinh/ChiTietPhieuLinh/SoLuongLeLinhSau";
import SoLuongLeLinhTruoc from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/PhieuLinh/ChiTietPhieuLinh/SoLuongLeLinhTruoc";
import { checkRole } from "lib-utils/role-utils";
import { MainPage } from "./styled";
import { xuatFileExcelKho } from "utils/kho-utils";
import { isArray } from "utils/index";
import {
  LOAI_NHAP_XUAT,
  LOAI_DICH_VU,
  TRANG_THAI_PHIEU_NHAP_XUAT,
  ROLES,
  CO_CHE_DUYET_PHAT,
  DS_TINH_CHAT_KHOA,
} from "constants/index";

const { Setting } = TableWrapper;

const ChiTiet = ({ ...props }) => {
  const { showConfirm } = useConfirm();
  const { showLoading, hideLoading } = useLoading();
  const { id } = useParams();
  const history = useHistory();
  const location = useLocation();
  const { t } = useTranslation();
  const refDanhSachHangHoa = useRef(null);

  const [loading, setLoading] = useState(false);
  const [isEditSoLo, setIsEditSoLo] = useState(false);

  const { thongTinPhieu } = useSelector((state) => state.phieuNhapXuat);

  const {
    phieuNhapXuat: {
      getById,
      xoaPhieu,
      resetData,
      inPhieuLinh,
      inPhieuLinhChiTiet,
      suaSoLuongDuyet,
      inPhieuNhapXuat,
      xuatPhieuNhapXuat,
      xuatPhieuLinh,
      xuatPhieuLinhChiTiet,
    },
    nbDvKho: { getNbDvKho: getDsNb, getDsSlLe, getDsSlLeLinhTruoc },
  } = useDispatch();

  const { dsSlLe, dsSlLeLinhTruoc } = useSelector((state) => state.nbDvKho);

  useEffect(() => {
    return () => {
      resetData();
    };
  }, []);

  useEffect(() => {
    getDsSlLe({ phieuLinhNoId: id });
    getDsSlLeLinhTruoc({ phieuLinhTraId: id });
    getDsNb({ phieuLinhId: id });
    getById(id);
    return () => {
      resetData();
    };
  }, [id]);

  const onPrint = () => {
    inPhieuLinh({ id });
  };
  const onDelete = () => {
    showConfirm(
      {
        title: t("common.canhBao"),
        content: `${t("kho.xoaPhieuSo")} ${thongTinPhieu?.soPhieu}?`,
        cancelText: t("common.dong"),
        okText: t("common.xoa"),
        classNameOkText: "button-error",
        showImg: true,
        showBtnOk: true,
      },
      () => {
        xoaPhieu({ id: thongTinPhieu.id }).then((s) => {
          history.push("/kho/nhap-kho");
        });
      },
      () => {}
    );
  };
  const times = useMemo(() => {
    const { thoiGianDuyet, thoiGianGuiDuyet, thoiGianTaoPhieu } =
      thongTinPhieu || {};
    return [thoiGianTaoPhieu, thoiGianGuiDuyet, thoiGianDuyet].map(
      (item) => item
    );
  }, [thongTinPhieu]);

  const showGuiDuyetCustom = useMemo(() => {
    const { khoDoiUng, loaiNhapXuat, trangThai } = thongTinPhieu || {};
    const { dsLoaiDichVu } = khoDoiUng || {};
    if (
      isArray(dsLoaiDichVu, true) &&
      loaiNhapXuat === LOAI_NHAP_XUAT.LINH_BU_TU_TRUC &&
      [
        TRANG_THAI_PHIEU_NHAP_XUAT.TAO_MOI,
        TRANG_THAI_PHIEU_NHAP_XUAT.TAO_MOI_DA_GIU_CHO,
      ].includes(trangThai)
    ) {
      if (dsLoaiDichVu.includes(LOAI_DICH_VU.THUOC)) {
        return checkRole([ROLES["KHO"].GUI_DUYET_PHIEU_NHAP_KHO_THUOC]);
      } else if (
        dsLoaiDichVu.includes(LOAI_DICH_VU.VAT_TU) ||
        dsLoaiDichVu.includes(LOAI_DICH_VU.HOA_CHAT)
      ) {
        return checkRole([ROLES["KHO"].GUI_DUYET_PHIEU_NHAP_KHO_VT_HOA_CHAT]);
      }
      return false;
    }
    return false;
  }, [thongTinPhieu]);

  const isXacNhanNhap = useMemo(() => {
    if (thongTinPhieu) {
      const { kho, loaiNhapXuat, khoaChiDinh } = thongTinPhieu || {};
      let dsCoCheDuyetPhat = kho?.dsCoCheDuyetPhat || [];
      return (
        loaiNhapXuat === LOAI_NHAP_XUAT.LINH_BU_TU_TRUC &&
        dsCoCheDuyetPhat.includes(CO_CHE_DUYET_PHAT.XAC_NHAN_NHAP) &&
        (khoaChiDinh?.dsTinhChatKhoa || []).includes(
          DS_TINH_CHAT_KHOA.XAC_NHAN_NHAP_KHO
        )
      );
    }
    return false;
  }, [thongTinPhieu]);

  const onPrintDanhSachBenhNhan = () => {
    inPhieuLinhChiTiet({ id });
  };

  const onPrintChiTietPhieuXuat = async () => {
    showLoading();
    try {
      await inPhieuNhapXuat({ dsId: [id] });
    } catch (err) {
      console.error(err);
    } finally {
      hideLoading();
    }
  };

  const menu = (
    <Menu
      items={[
        {
          key: 0,
          label: (
            <a href={() => false} onClick={onPrintChiTietPhieuXuat}>
              {t("kho.inChiTietPhieuXuat")}
            </a>
          ),
        },
        {
          key: 1,
          label: (
            <a href={() => false} onClick={onPrint}>
              {t("kho.inChiTietPhieuLinhBu")}
            </a>
          ),
        },
        {
          key: 2,
          label: (
            <a href={() => false} onClick={onPrintDanhSachBenhNhan}>
              {t("kho.inDanhSachBenhNhan")}
            </a>
          ),
        },
      ]}
    />
  );

  const exportFile = async (action, params) => {
    try {
      showLoading();
      const s = await action(params);
      xuatFileExcelKho(s);
    } catch (error) {
      console.error(error);
    } finally {
      hideLoading();
    }
  };

  const onExportFilePhieuNhapKho = () =>
    exportFile(xuatPhieuNhapXuat, { dsId: [id] });
  const onExportFileChiTietPhieuLinhBu = () =>
    exportFile(xuatPhieuLinh, { id });
  const onExportFileDanhSachBenhNhan = () =>
    exportFile(xuatPhieuLinhChiTiet, { id });

  const items = [
    {
      key: 1,
      label: (
        <a onClick={onExportFilePhieuNhapKho}>
          <div className="flex icon_utilities gap-8">
            <SVG.IcDownload />
            <span>{t("kho.xuatFileChiTietPhieuXuat")}</span>
          </div>
        </a>
      ),
    },
    {
      key: 2,
      label: (
        <a onClick={onExportFileChiTietPhieuLinhBu}>
          <div className="flex icon_utilities gap-8">
            <SVG.IcDownload />
            <span>{t("kho.xuatFileChiTietPhieuLinhBu")}</span>
          </div>
        </a>
      ),
    },
    {
      key: 3,
      label: (
        <a onClick={onExportFileDanhSachBenhNhan}>
          <div className="flex icon_utilities gap-8">
            <SVG.IcDownload />
            <span>{t("kho.xuatFileDanhSachBenhNhan")}</span>
          </div>
        </a>
      ),
    },
  ];

  const tabItems = [
    {
      key: "danhSachHangHoa",
      label: (
        <div className="tab-btn">
          {t("kho.danhSachHangHoa")}
          <Setting refTable={refDanhSachHangHoa} />
        </div>
      ),
      children: (
        <DanhSachHangHoa
          {...props}
          isEdit={false}
          ref={refDanhSachHangHoa}
          isEditSoLo={isEditSoLo}
        />
      ),
      shouldShow: true,
    },
    {
      key: "danhSachNguoiBenhSuDung",
      label: t("kho.danhSachNguoiBenhSuDung"),
      children: <DanhSachNguoiBenh {...props} isEdit={false} />,
      shouldShow: true,
    },
    {
      key: "soLuongLeLinhChoLanSau",
      label: t("kho.soLuongLeLinhChoLanSau"),
      children: <SoLuongLeLinhSau {...props} isEdit={false} />,
      shouldShow: !!dsSlLe?.length,
    },
    {
      key: "soLuongLeLinhChoLanTruoc",
      label: t("kho.soLuongLeLinhChoLanTruoc"),
      children: <SoLuongLeLinhTruoc {...props} isEdit={false} />,
      shouldShow: !!dsSlLeLinhTruoc?.length,
    },
  ].filter((item) => item.shouldShow);

  const onSave = () => {
    setLoading(true);
    suaSoLuongDuyet(id)
      .then(() => {
        getById(id);
      })
      .catch(() => {})
      .finally(() => {
        setLoading(false);
        setIsEditSoLo(false);
      });
  };

  return (
    <MainPage
      breadcrumb={[
        { title: t("kho.kho"), link: "/kho" },
        {
          title: t("kho.xuatKho"),
          link:
            "/kho/xuat-kho" + transformObjToQueryString(location?.state || {}),
        },
        {
          title: t("kho.chiTietPhieuXuat"),
          link: `/kho/xuat-kho/chi-tiet-linh-bu/${id}`,
        },
      ]}
      title={
        <div className="wrapper-title">
          {t("kho.chiTietPhieuXuat")}
          <div className="header-action">
            <Dropdown overlay={menu} trigger={["click"]}>
              <div className="action-btn">
                <SVG.IcPrint />
              </div>
            </Dropdown>

            <Tooltip title={t("kho.xoaPhieuLinhBu")}>
              <div className="action-btn" onClick={onDelete}>
                <SVG.IcDelete />
              </div>
            </Tooltip>
            <Dropdown
              menu={{ items }}
              trigger={"click"}
              overlayClassName="danh-muc-dropdown-tien-ich"
            >
              <Button
                rightIcon={<SVG.IcMore />}
                style={{ marginLeft: "10px" }}
                height={28}
              >
                {t("common.tienIch")}
              </Button>
            </Dropdown>
          </div>
        </div>
      }
      titleRight={<TrangThai times={times} />}
    >
      <Card>
        <ThongTinPhieu {...props} />
      </Card>
      <Card noPadding={true}>
        <Tabs defaultActiveKey="danhSachHangHoa" items={tabItems} />
      </Card>
      <Action
        loaiPhieu={1}
        showGuiDuyetCustom={showGuiDuyetCustom}
        isXacNhanNhap={isXacNhanNhap}
        otherBtn={
          thongTinPhieu.trangThai === 20 &&
          checkRole([ROLES["KHO"].CHINH_SUA_SO_LO_TRUOC_KHI_XUAT]) && (
            <>
              {isEditSoLo ? (
                <>
                  <Button
                    onClick={() => setIsEditSoLo(false)}
                    className="left-btn"
                    rightIcon={<SVG.IcEdit />}
                    minWidth={80}
                  >
                    {t("common.huy")}
                  </Button>
                  <Button
                    onClick={onSave}
                    className="left-btn"
                    rightIcon={<SVG.IcSave />}
                    minWidth={80}
                    loading={loading}
                  >
                    {t("common.luu")}
                  </Button>
                </>
              ) : (
                <Button
                  onClick={() => setIsEditSoLo(true)}
                  className="left-btn"
                  rightIcon={<SVG.IcEdit />}
                  minWidth={80}
                >
                  {t("common.sua")}
                </Button>
              )}
            </>
          )
        }
      />
    </MainPage>
  );
};

export default ChiTiet;
