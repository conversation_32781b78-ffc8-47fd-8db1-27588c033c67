import React, { useMemo, useRef } from "react";
import { useHistory, useParams } from "react-router-dom";
import { <PERSON><PERSON>, HotKeyTrigger } from "components";
import { useDispatch } from "react-redux";
import { Main } from "./styled";
import ModalNhapLyDo from "pages/kho/components/ModalNhapLyDo";
import {
  LOAI_NHAP_XUAT,
  ROLES,
  TRANG_THAI_THUOC,
  THIET_LAP_CHUNG,
  LOAI_DICH_VU,
  TRANG_THAI_PHIEU_NHAP_XUAT,
  CO_CHE_DUYET_PHAT,
} from "constants/index";
import { checkRole } from "lib-utils/role-utils";
import { SVG } from "assets";
import { useLoading, useStore, useThietLap } from "hooks";
import { t } from "i18next";
import classNames from "classnames";
import { isArray, isBoolean } from "utils/index";
import { message } from "antd";

const mapButton = ({
  onEdit,
  onSave,
  onGuiDuyet<PERSON>hieu,
  onTuChoiDuyet,
  onHuyDuyet,
  onDuyetPhieu,
  layerId,
  loaiPhieu,
}) => ({
  suaPhieu: {
    condition: [
      {
        loaiPhieu: 2,
        trangThai: 10,
        loaiNhapXuat: [10, 20],
        roles: ROLES["KHO"].SUA_PHIEU_NHAP_KHO,
      },
      {
        loaiPhieu: 1,
        trangThai: 10,
        loaiNhapXuat: [30, 40, 90, 45],
        roles: ROLES["KHO"].SUA_PHIEU_XUAT_KHO,
      },
      {
        loaiPhieu: 2,
        trangThai: 15,
        loaiNhapXuat: [10, 20],
        roles: ROLES["KHO"].SUA_PHIEU_NHAP_KHO,
      },
      {
        loaiPhieu: 1,
        trangThai: 15,
        loaiNhapXuat: [30, 40, 90, 45],
        roles: ROLES["KHO"].SUA_PHIEU_XUAT_KHO,
      },
      {
        loaiPhieu: 1,
        trangThai: 20,
        loaiNhapXuat: [20],
        roles: ROLES["KHO"].SUA_PHIEU_XUAT_KHO,
      },
      {
        loaiPhieu: 1,
        trangThai: 20,
        loaiNhapXuat: [20],
        roles: ROLES["KHO"].SUA_PHIEU_XUAT_KHO,
      },
      // phieu nhap khac
      {
        loaiPhieu: 2,
        trangThai: 10,
        loaiNhapXuat: [12],
        roles: ROLES["KHO"].SUA_PHIEU_XUAT_KHO,
      },
    ],
    component: (
      <Button
        key={1}
        onClick={onEdit}
        className="left-btn"
        rightIcon={<SVG.IcEdit />}
        minWidth={80}
      >
        {t("kho.suaPhieu")}
      </Button>
    ),
  },
  luuVaGuiDuyet: {
    condition: [
      // phieu nhap khac
      // {
      //   loaiPhieu: 2,
      //   trangThai: 10,
      //   loaiNhapXuat: [12],
      //   roles: ROLES["KHO"].GUI_DUYET_PHIEU_NHAP_KHO,
      // },
      {
        loaiPhieu: 2,
        trangThai: undefined,
        loaiNhapXuat: [12],
        roles: ROLES["KHO"].GUI_DUYET_PHIEU_NHAP_KHO,
      },
    ],
    component: (
      <HotKeyTrigger
        layerIds={[layerId]}
        hotKey="F4"
        triggerEvent={() => {
          onSave(true);
        }}
      >
        <Button
          key={2}
          onClick={() => onSave(true)}
          className="left-btn"
          minWidth={80}
          rightIcon={<SVG.IcGuiVaDuyet />}
        >
          {t("kho.luuVaGuiDuyet") + `${layerId ? " (F4)" : ""}`}
        </Button>
      </HotKeyTrigger>
    ),
  },
  guiDuyet: {
    condition: [
      {
        loaiPhieu: 2,
        trangThai: 10,
        loaiNhapXuat: [10, 12, 20, 85, 80],
        roles: ROLES["KHO"].GUI_DUYET_PHIEU_NHAP_KHO,
      },
      {
        loaiPhieu: 1,
        trangThai: 10,
        loaiNhapXuat: [30, 40, 90, 50, 45],
        roles: ROLES["KHO"].GUI_DUYET_PHIEU_NHAP_KHO,
      },
      {
        loaiPhieu: 2,
        trangThai: 15,
        loaiNhapXuat: [10, 20, 80],
        roles: ROLES["KHO"].GUI_DUYET_PHIEU_NHAP_KHO,
      },
      {
        loaiPhieu: 1,
        trangThai: 15,
        loaiNhapXuat: [30, 40, 90, 50, 45],
        roles: ROLES["KHO"].GUI_DUYET_PHIEU_NHAP_KHO,
      },
    ],
    component: (
      <Button
        key={3}
        className="right-btn"
        onClick={onGuiDuyetPhieu}
        rightIcon={<SVG.IcSend />}
        type="primary"
      >
        {t("kho.guiDuyet")}
      </Button>
    ),
  },
  tuChoiDuyet: {
    condition: [
      { loaiPhieu: 2, trangThai: 20, loaiNhapXuat: [30, 45] },
      { loaiPhieu: 1, trangThai: 20, loaiNhapXuat: [20, 80] },
    ],
    component: (
      <Button
        key={4}
        className="left-btn"
        onClick={onTuChoiDuyet}
        rightIcon={<SVG.IcCloseCircle />}
        minWidth={80}
      >
        {loaiPhieu == 1 ? t("kho.xuat.tuChoiDuyet") : t("kho.tuChoiDuyet")}
      </Button>
    ),
  },
  huyDuyet: {
    condition: [
      {
        loaiPhieu: 2,
        trangThai: 30,
        loaiNhapXuat: [10, 30, 45],
        roles: ROLES["KHO"].HUY_DUYET_PHIEU_NHAP_KHO,
      },
      {
        loaiPhieu: 1,
        trangThai: 30,
        loaiNhapXuat: [20, 40, 80, 90, 50],
        coCheDuyetPhat: CO_CHE_DUYET_PHAT.DUYET_SAU_KHI_XUAT_HUY,
        roles: ROLES["KHO"].HUY_DUYET_PHIEU_XUAT_KHO,
      },
      {
        loaiPhieu: 1,
        trangThai: 28,
        loaiNhapXuat: [20, 80],
        coCheDuyetPhat: CO_CHE_DUYET_PHAT.XAC_NHAN_NHAP,
        roles: ROLES["KHO"].HUY_DUYET_PHIEU_XUAT_KHO,
      },

      // phieu khac
      {
        loaiPhieu: 2,
        trangThai: 30,
        loaiNhapXuat: [12],
        roles: ROLES["KHO"].HUY_DUYET_PHIEU_XUAT_KHO,
      },
    ],
    component: (
      <Button
        key={5}
        className="right-btn"
        onClick={onHuyDuyet}
        rightIcon={<SVG.IcCloseCircle />}
        minWidth={80}
      >
        {loaiPhieu == 1 ? t("kho.xuat.huyDuyet") : t("kho.huyDuyet")}
      </Button>
    ),
  },
  huyGuiDuyet: {
    condition: [
      {
        loaiPhieu: 2,
        trangThai: 20,
        loaiNhapXuat: [10, 20],
        roles: ROLES["KHO"].HUY_GUI_DUYET_PHIEU_XUAT_KHO,
      },
      {
        loaiPhieu: 1,
        trangThai: 20,
        loaiNhapXuat: [30, 40, 90, 50, 45],
        roles: ROLES["KHO"].HUY_GUI_DUYET_PHIEU_XUAT_KHO,
      },

      // phieu khac
      {
        loaiPhieu: 2,
        trangThai: 20,
        loaiNhapXuat: [12],
        roles: ROLES["KHO"].HUY_GUI_DUYET_PHIEU_XUAT_KHO,
      },
    ],
    component: (
      <Button
        key={6}
        className="right-btn"
        onClick={onTuChoiDuyet}
        rightIcon={<SVG.IcCloseCircle />}
        minWidth={80}
      >
        {t("khoMau.huyGuiDuyet")}
      </Button>
    ),
  },
  duyet: {
    condition: [
      {
        loaiPhieu: 2,
        trangThai: 20,
        loaiNhapXuat: [10, 30, 45],
        roles: ROLES["KHO"].DUYET_PHIEU_NHAP_KHO,
      },
      {
        loaiPhieu: 1,
        trangThai: 20,
        loaiNhapXuat: [20, 40, 80, 90, 50],
        roles: ROLES["KHO"].DUYET_PHIEU_XUAT_KHO,
      },

      // phieu khac
      {
        loaiPhieu: 2,
        trangThai: 20,
        loaiNhapXuat: [12],
        roles: ROLES["KHO"].DUYET_PHIEU_XUAT_KHO,
      },
    ],
    component: (
      <Button
        key={7}
        className="right-btn"
        onClick={onDuyetPhieu}
        type={"primary"}
        rightIcon={<SVG.IcSave />}
        minWidth={80}
      >
        {loaiPhieu == 1 ? t("kho.xuat.duyet") : t("common.duyet")}
      </Button>
    ),
  },
  phatThuoc: {
    condition: [
      {
        loaiPhieu: 1,
        trangThai: 15,
        loaiNhapXuat: [130],
      },
    ],
    component: (
      <Button
        key={7}
        className="right-btn"
        onClick={onDuyetPhieu}
        type={"primary"}
        minWidth={80}
      >
        {t("kho.phatThuoc.title")}
      </Button>
    ),
  },
  huyPhat: {
    condition: [
      {
        loaiPhieu: 1,
        trangThai: 30,
        loaiNhapXuat: [130],
      },
    ],
    component: (
      <Button
        key={7}
        className="right-btn"
        type={"primary"}
        minWidth={80}
        onClick={onHuyDuyet}
        rightIcon={<SVG.IcCloseCircle />}
      >
        {t("nhaThuoc.huyPhat")}
      </Button>
    ),
  },
});

const Action = ({
  onSave,
  loaiPhieu = 2, // 1: xuất kho, khác (2): nhập kho
  allowEdit = true,
  hiddenCancel = false, // ẩn nút hủy duyệt
  showBack,
  listBtn,
  otherBtn,
  otherRightBtn,
  parentState,
  handleEditSlHuy,
  onSaveEditData,
  isXuatChuyenKho,
  isXacNhanNhap,
  isKhongDuyetNhap,
  showThanhToanNCC,
  showGuiDuyetCustom,
  layerId,
  dsTrangThaiDlsIds,
  ...props
}) => {
  const thongTinPhieu = useStore("phieuNhapXuat.thongTinPhieu");
  const { id } = useParams();
  const refModalNhapLyDo = useRef(null);
  const { showLoading, hideLoading } = useLoading();

  const [dataYC_DUYET_DLS_PHIEU_LINH, isFinish] = useThietLap(
    THIET_LAP_CHUNG.YC_DUYET_DLS_PHIEU_LINH
  );
  const [dataYC_DUYET_DLS_PHIEU_XUAT_CHUYEN] = useThietLap(
    THIET_LAP_CHUNG.YC_DUYET_DLS_PHIEU_XUAT_CHUYEN
  );
  const listTrangThaiDls = Object.values(TRANG_THAI_THUOC);

  const isYCDuyetDLS = useMemo(() => {
    //Chỉ bắt thêm dk  trạng thái duyệt DLS với phiếu lĩnh, lĩnh bù Thuốc (loaiDichVu=100)
    return (
      thongTinPhieu.loaiDichVu === LOAI_DICH_VU.THUOC &&
      thongTinPhieu.loaiNhapXuat === 80 &&
      dataYC_DUYET_DLS_PHIEU_LINH &&
      dataYC_DUYET_DLS_PHIEU_LINH.toLowerCase() === "true"
    );
  }, [
    dataYC_DUYET_DLS_PHIEU_LINH,
    thongTinPhieu?.loaiNhapXuat,
    thongTinPhieu.loaiDichVu,
  ]);

  const {
    phieuNhapXuat: {
      guiDuyetPhieu,
      duyetPhieu,
      huyDuyet,
      tuChoiDuyet,
      inPhieuLinh,
      inPhieuNhapXuat,
    },
  } = useDispatch();

  const { push, goBack } = useHistory();

  const onDuyetPhieu = async () => {
    showLoading();
    try {
      await duyetPhieu({ id, capNhatCoSo: parentState?.dieuChinhCoSo });
    } finally {
      hideLoading();
    }
  };

  const onHuyDuyet = () => {
    refModalNhapLyDo.current &&
      refModalNhapLyDo.current.show(
        {
          title: t("khoMau.lyDoHuyDuyet"),
          message: t("khoMau.dienLyDoHuyDuyet"),
        },
        (lyDo) => {
          huyDuyet({ id, lyDo });
        }
      );
  };

  const onGuiDuyetPhieu = () => {
    guiDuyetPhieu({ id }).then(() => {
      if (loaiPhieu == 1) {
        //phiếu xuất
        inPhieuNhapXuat({ dsId: [id] });
      } else {
        if (![10, 12].includes(thongTinPhieu.loaiNhapXuat)) {
          if (
            !isBoolean(dsTrangThaiDlsIds) &&
            isArray(dsTrangThaiDlsIds, true)
          ) {
            let trangThaiIn = listTrangThaiDls.filter((i) =>
              dsTrangThaiDlsIds.includes(i.id)
            );
            message.error(
              t("quanLyNoiTru.chiChoPhepInPhieuVoiCacTrangThaiDls", {
                title: trangThaiIn.map((x) => x.ten).join("; "),
              })
            );
            return;
          }
          inPhieuLinh({ id, printMerge: true });
        }
      }
    });
  };

  const onTuChoiDuyet = () => {
    refModalNhapLyDo.current &&
      refModalNhapLyDo.current.show(
        {
          title: t("khoMau.lyDoTuChoi"),
          message: t("khoMau.dienLyDoTuChoiDuyet"),
        },
        (lyDo) => {
          tuChoiDuyet({ id, lyDo });
        }
      );
  };

  const onEdit = (e) => {
    if (loaiPhieu == 1) {
      push(`/kho/xuat-kho/chinh-sua/${thongTinPhieu?.id}`);
    } else {
      if (thongTinPhieu.loaiNhapXuat == 30) {
        push(`/kho/xuat-kho/chinh-sua/${thongTinPhieu?.id}`);
      } else if (thongTinPhieu.thangDuTru) {
        push(`/kho/phieu-nhap-du-tru/chinh-sua/${thongTinPhieu?.id}`);
      } else if (thongTinPhieu.loaiNhapXuat == LOAI_NHAP_XUAT.NHAP_KHAC) {
        push(`/kho/phieu-nhap-khac/chinh-sua/${thongTinPhieu?.id}`);
      } else {
        push(`/kho/phieu-nhap-nha-cung-cap/chinh-sua/${thongTinPhieu?.id}`);
      }
    }
  };

  const buttonList = mapButton({
    onEdit,
    onSave,
    onGuiDuyetPhieu,
    onTuChoiDuyet,
    onHuyDuyet,
    onDuyetPhieu,
    layerId,
    loaiPhieu,
  });

  const RightBtnComponent = ({ children }) =>
    showBack ? <div className="right-btn">{children}</div> : <>{children}</>;

  return (
    <Main className={classNames("action", { "action-ncc": showThanhToanNCC })}>
      {showBack && <Button.QuayLai onClick={goBack} />}

      <RightBtnComponent>
        {!parentState?.isEditSlDuyet && otherBtn && otherBtn}
        {thongTinPhieu.trangThai === 20 &&
          isXuatChuyenKho &&
          !parentState?.isEditSlDuyet && (
            <Button
              className="right-btn"
              minWidth={80}
              rightIcon={<SVG.IcEdit />}
              loading={parentState?.loading}
              onClick={handleEditSlHuy(true)}
            >
              {t("kho.suaPhieu")}
            </Button>
          )}
        {parentState?.isEditSlDuyet && (
          <>
            <Button
              className="right-btn"
              minWidth={80}
              rightIcon={<SVG.IcCloseCircle />}
              loading={parentState?.loading}
              onClick={handleEditSlHuy(false)}
            >
              {t("common.huy")}
            </Button>
            <Button
              className="right-btn"
              minWidth={80}
              type="primary"
              rightIcon={<SVG.IcSave />}
              loading={parentState?.loading}
              onClick={onSaveEditData}
            >
              {t("kho.luuPhieu")}
            </Button>
          </>
        )}
        {!parentState?.isEditSlDuyet &&
          (listBtn
            ? listBtn.map((item) => buttonList[item])
            : Object.keys(buttonList)
                .filter((key) => {
                  const btn = buttonList[key];

                  // loaiPhieu = 1 là phiếu xuất kho
                  // còn lại là các loại phiếu khác
                  if (isYCDuyetDLS && key === "duyet" && loaiPhieu == 1) {
                    return (
                      thongTinPhieu.trangThaiDls ===
                        TRANG_THAI_THUOC.DA_DUYET_DUOC_LAM_SANG.id &&
                      thongTinPhieu.trangThai ===
                        TRANG_THAI_PHIEU_NHAP_XUAT.CHO_DUYET &&
                      checkRole([ROLES["KHO"].DUYET_PHIEU_XUAT_KHO])
                    );
                  }

                  //Nếu là nhập dự trù (loaiNhapXuat = 20) =>
                  //    Ẩn nút Hủy gửi duyệt khi: trangThaiDls != null
                  if (
                    key === "huyGuiDuyet" &&
                    thongTinPhieu.loaiNhapXuat === LOAI_NHAP_XUAT.DU_TRU &&
                    thongTinPhieu.trangThaiDls !== null
                  ) {
                    return false;
                  }
                  if (
                    key === "huyGuiDuyet" &&
                    thongTinPhieu.loaiNhapXuat ===
                      LOAI_NHAP_XUAT.XUAT_CHUYEN_KHO &&
                    dataYC_DUYET_DLS_PHIEU_XUAT_CHUYEN?.eval() &&
                    loaiPhieu == 1 &&
                    ![20, 25, null].includes(thongTinPhieu.trangThaiDls)
                  ) {
                    return false;
                  }
                  //Nếu là Phiếu lĩnh bù (loaiNhapXuat = 80)
                  if (
                    ((key === "huyGuiDuyet" && loaiPhieu != 1) ||
                      (key === "tuChoiDuyet" && loaiPhieu == 1)) &&
                    dataYC_DUYET_DLS_PHIEU_LINH?.eval() &&
                    thongTinPhieu.loaiNhapXuat ===
                      LOAI_NHAP_XUAT.LINH_BU_TU_TRUC
                  ) {
                    return (
                      thongTinPhieu.trangThai ===
                        TRANG_THAI_PHIEU_NHAP_XUAT.CHO_DUYET &&
                      thongTinPhieu.trangThaiDls <
                        TRANG_THAI_THUOC.DA_DUYET_DUOC_LAM_SANG.id &&
                      btn.condition.some(
                        (condition) =>
                          !condition.roles || checkRole([condition.roles])
                      )
                    );
                  }

                  // Nếu Loại nhập xuất (LoaiNhapXuat) = 30
                  // Ẩn nút Duyệt ở nhập kho kho: Cơ chế duyệt phát (CoCheDuyetPhat) = Không duyệt nhập
                  // Hiễn thị nút Duyệt ở xuất kho khi: Cơ chế duyệt phát (CoCheDuyetPhat) = Không duyệt nhập
                  if (key === "duyet" && isKhongDuyetNhap) {
                    return loaiPhieu == 1 ? true : false;
                  }

                  if (showGuiDuyetCustom && key === "guiDuyet") {
                    return true;
                  }

                  return btn.condition.some(
                    (condition) =>
                      condition.loaiPhieu === loaiPhieu &&
                      condition.trangThai === thongTinPhieu.trangThai &&
                      (!condition.roles || checkRole([condition.roles])) &&
                      condition.loaiNhapXuat.some(
                        (lnx) => lnx === thongTinPhieu.loaiNhapXuat
                      ) &&
                      (isXacNhanNhap && condition.coCheDuyetPhat
                        ? condition.coCheDuyetPhat ===
                          CO_CHE_DUYET_PHAT.XAC_NHAN_NHAP
                        : true)
                  );
                })
                .map((key) => buttonList[key])
          ).map((btn) => btn.component)}
        {otherRightBtn && otherRightBtn}
      </RightBtnComponent>
      <ModalNhapLyDo ref={refModalNhapLyDo} />
    </Main>
  );
};

export default Action;
