import {
  DatePicker,
  Checkbox,
  TableWrapper,
  InputTimeout,
  HeaderSearch,
  Tooltip,
} from "components";
import { useStore } from "hooks";
import { useWindowSize } from "hooks";
import moment from "moment";
import TableEmpty from "pages/kho/components/TableEmpty";
import React, { memo, useState } from "react";
import { Main, WrapperPopup } from "./styled";
import { useTranslation } from "react-i18next";
import { isArray, roundToDigits } from "utils";
import { useMemo } from "react";
import { customSortBySttAndName } from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/PhieuLinh/utils";

const SoLuongLeLinh = ({
  renderColumns = () => [],
  onFocusSearchHangHoa,
  isEdit,
  onSearch = () => {},
  loaiNhapXuat,
  dataSource,
  ...props
}) => {
  const { t } = useTranslation();
  const dsNbDvKho = useStore("nbDvKho.dsNbDvKho", []);
  const [state, _setState] = useState({ dataSortColumn: {}, listCheckId: [] });
  const { dataSortColumn } = state;
  const setState = (data) => {
    _setState((pre) => ({ ...pre, ...data }));
  };

  const dataTable = useMemo(() => {
    let listData = dataSource || dsNbDvKho;
    if (isArray(listData, true)) {
      if (!isEdit && loaiNhapXuat === 70) {
        return customSortBySttAndName(listData, [, "tenDichVu"]).map(
          (item, index) => ({ ...item, index: index + 1 })
        );
      } else {
        return listData;
      }
    } else {
      return [];
    }
  }, [dsNbDvKho, isEdit, loaiNhapXuat, dataSource]);

  const onClickSort = (key, value) => {
    const sort = { ...dataSortColumn, [key]: value };
    setState({ dataSortColumn: sort });
  };

  const size = useWindowSize();

  const clickCheckbox = (id) => (e) => {
    if (e.target.checked) {
      setState({ listCheckId: [...state.listCheckId, id] });
    } else {
      setState({ listCheckId: state.listCheckId.filter((i) => i != id) });
    }
  };

  const onClickAll = (e) => {
    if (e.target.checked) {
      setState({ listCheckId: dsNbDvKho.map((i) => i.id) });
    } else {
      setState({ listCheckId: [] });
    }
  };

  const soLuongSoCap = (sl, dv) => {
    return roundToDigits((sl || 0) / (dv || 1), 3);
  };

  const renderTitlePop = (item) => (
    <WrapperPopup>
      <div className="title-pop">
        {item?.maDichVu} - {item?.tenDichVu}
      </div>
      <div className="pop-content">
        <div className="item-pop">
          {t("kho.soLuongKhaDung")}: {item.soLuong}
        </div>
        <div className="item-pop">
          {t("kho.duongDung")}: {item.tenDuongDung}
        </div>
        <div className="item-pop">
          {t("kho.soLuongConLai")}: {item.soLuongYeuCau}
        </div>
        <div className="item-pop">
          {t("kho.nhaSanXuat")}: {item.tenNhaSanXuat}
        </div>
        <div className="item-pop">
          {t("kho.hamLuong")}: {item.hamLuong}
        </div>
        <div className="item-pop">{t("kho.quyCach")}:</div>
        <div className="item-pop">
          {t("kho.tenHoatChat")}: {item.tenHoatChat}
        </div>
      </div>
    </WrapperPopup>
  );
  const commonCol = {
    check: {
      title: (
        <HeaderSearch
          title={
            <Checkbox
              checked={state.listCheckId.length === dsNbDvKho.length}
              onClick={onClickAll}
            ></Checkbox>
          }
          //   sort_key="soPhieuLinh"
          //   onClickSort={onClickSort}
          //   dataSort={dataSortColumn.soPhieuLinh || 0}
          //   search={<Input placeholder="Tìm kiếm" />}
        />
      ),
      dataIndex: "index",
      width: "50px",
      align: "center",
      render: (_, item) => (
        <Checkbox
          checked={state.listCheckId.includes(item?.id)}
          onClick={clickCheckbox(item?.id)}
        ></Checkbox>
      ),
    },
    stt: {
      title: <HeaderSearch title={t("common.stt")} />,
      width: size.width <= 1400 ? 50 : 50,
      dataIndex: "index",
      key: "index",
      hideSearch: true,
      align: "center",
      render: (_, __, index) => index + 1,
    },
    maHoSo: {
      title: (
        <HeaderSearch
          title={t("common.maHoSo")}
          sort_key="maHoSo"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.maHoSo || 0}
          search={
            <InputTimeout
              placeholder={t("common.timMaHoSo")}
              onChange={(maHoSo) => onSearch({ maHoSo })}
            />
          }
        />
      ),
      width: 100,
      dataIndex: "maHoSo",
      key: "maHoSo",
      type: true,
      hideSearch: false,
    },
    maNb: {
      title: (
        <HeaderSearch
          title={t("common.maNb")}
          sort_key="maNb"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.maNb || 0}
          search={
            <InputTimeout
              placeholder={t("cdha.timMaNb")}
              onChange={(maNb) => onSearch({ maNb })}
            />
          }
        />
      ),
      width: 120,
      dataIndex: "maNb",
      key: "maNb",
      type: true,
      hideSearch: false,
      show: true,
      i18Name: "common.maNb",
    },
    tenNb: {
      title: (
        <HeaderSearch
          title={t("common.tenNb")}
          sort_key="tenNb"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.tenNb || 0}
          search={
            <InputTimeout
              placeholder={t("common.timTenNb")}
              onChange={(tenNb) => onSearch({ tenNb })}
            />
          }
        />
      ),
      width: 250,
      dataIndex: "tenNb",
      key: "tenNb",
      type: true,
      hideSearch: false,
    },
    tenHangHoa: {
      title: (
        <HeaderSearch
          title={t("kho.tenHangHoa")}
          sort_key="tenDichVu"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.tenDichVu || 0}
          search={
            <InputTimeout
              placeholder={t("kho.timKiemTheoTenHangHoa")}
              onChange={(tenDichVu) => onSearch({ tenDichVu })}
            />
          }
        />
      ),
      width: 200,
      dataIndex: "tenDichVu",
      key: "tenDichVu",
      type: true,
      hideSearch: false,
      render: (value, item, index) => (
        <Tooltip
          overlayInnerStyle={{ width: 400 }}
          overlay={renderTitlePop(item)}
          color={"white"}
        >
          {item?.tenDichVu}
        </Tooltip>
      ),
    },
    slTra: {
      title: (
        <HeaderSearch
          title={t("kho.slTraThuCap")}
          sort_key="soLuongTra"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.soLuongTra || 0}
        />
      ),
      key: "soLuongTra",
      width: size.width <= 1400 ? 70 : 70,
      dataIndex: "soLuongTra",
      hideSearch: true,
      align: "right",
      render: (_, item, __) => item.soLuongTra,
    },
    slTraSoCap: {
      title: (
        <HeaderSearch
          title={t("kho.slTraSoCap")}
          sort_key="soLuongTra"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.soLuongTra || 0}
        />
      ),
      key: "soLuongTra",
      width: size.width <= 1400 ? 70 : 70,
      dataIndex: "soLuongTra",
      hideSearch: true,
      align: "right",
      render: (_, item, __) => soLuongSoCap(item.soLuongTra, item.heSoDinhMuc),
    },
    ngayThucHien: {
      title: (
        <HeaderSearch
          title={t("kho.ngayThucHien")}
          sort_key="thoiGianThucHien"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.thoiGianThucHien || 0}
          searchDate={
            <DatePicker
              format="DD/MM/YYYY"
              placeholder={t("common.chonNgay")}
              onChange={(value) =>
                onSearch({
                  thoiGianThucHien: value,
                })
              }
            />
          }
        />
      ),
      width: 150,
      dataIndex: "thoiGianThucHien",
      key: "thoiGianThucHien",
      // hideSearch: true,
      render: (value, _, __) =>
        value ? moment(value)?.format("DD/MM/YYYY") : "",
    },
    ngayThucHienNb: {
      title: (
        <HeaderSearch
          title={t("kho.ngayThucHien")}
          sort_key="thoiGianYLenh"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.thoiGianYLenh || 0}
          searchDate={
            <DatePicker
              format="DD/MM/YYYY"
              placeholder={t("common.chonNgay")}
              onChange={(value) =>
                onSearch({
                  thoiGianYLenh: value,
                })
              }
            />
          }
        />
      ),
      width: 150,
      dataIndex: "thoiGianYLenh",
      key: "thoiGianYLenh",
      // hideSearch: true,
      render: (value, _, __) =>
        value ? moment(value)?.format("DD/MM/YYYY") : "",
    },
    ngayKe: {
      title: (
        <HeaderSearch
          title={t("nhaThuoc.ngayKe")}
          sort_key="thoiGianChiDinh"
          onClickSort={onClickSort}
          dataSort={dataSortColumn.thoiGianChiDinh || 0}
          searchDate={
            <DatePicker
              format="DD/MM/YYYY"
              placeholder={t("common.chonNgay")}
              onChange={(value) =>
                onSearch({
                  thoiGianChiDinh: value,
                })
              }
            />
          }
        />
      ),
      width: 150,
      dataIndex: "thoiGianChiDinh",
      key: "thoiGianChiDinh",
      // hideSearch: true,
      render: (value, _, __) =>
        value ? moment(value)?.format("DD/MM/YYYY") : "",
    },
  };
  return (
    <Main className="main">
      <TableWrapper
        scroll={{ y: 453, x: "auto" }}
        rowKey={"key"}
        columns={renderColumns({
          commonCol,
        })}
        dataSource={dataTable}
        locale={{
          emptyText: (
            <TableEmpty
              onClickButton={onFocusSearchHangHoa}
              showButton={isEdit}
            />
          ),
        }}
      />
    </Main>
  );
};

export default memo(SoLuongLeLinh);
