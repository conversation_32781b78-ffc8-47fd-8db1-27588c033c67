import React, { useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import { useParams } from "react-router-dom";
import moment from "moment";

import { useStore } from "hooks";

import SoLuongLeLinh from "../SoLuongLeLinh";
import { HeaderSearch } from "components";
import { SVG } from "assets";
import { containText } from "utils/index";

const DanhSachNguoiBenh = ({ loaiNhapXuat, onSearch, ...otherProps }) => {
  const [state, _setState] = useState({
    searchLocal: {},
  });
  const dsNbDvKho = useStore("nbDvKho.dsNbDvKho", []);

  const setState = (data) => {
    _setState((pre) => ({ ...pre, ...data }));
  };

  const { t } = useTranslation();
  const { id } = useParams();

  const {
    nbDvKho: { getNbDvKho: getDsNb },
  } = useDispatch();

  const dsNbDvKhoMemo = useMemo(() => {
    let _data = dsNbDvKho || [];
    Object.keys(state.searchLocal)
      .filter((key) => !!state.searchLocal[key])
      .forEach((key) => {
        _data = _data.filter((item) =>
          ["thoiGianThucHien", "thoiGianChiDinh", "thoiGianYLenh"].includes(key)
            ? moment(item[key]).isSame(moment(state.searchLocal[key]), "date")
            : containText(item[key], state.searchLocal[key])
        );
      });
    return _data;
  }, [dsNbDvKho, state.searchLocal]);

  const renderColumns = ({ commonCol }) => [
    commonCol.check,
    commonCol.stt,
    commonCol.maHoSo,
    commonCol.maNb,
    commonCol.tenNb,
    commonCol.tenHangHoa,
    commonCol.slTra,
    commonCol.slTraSoCap,
    commonCol.ngayThucHienNb,
    commonCol.ngayKe,
    {
      title: <HeaderSearch title={t("common.tienIch")} />,
      key: "",
      width: "60px",
      dataIndex: "",
      hideSearch: true,
      align: "right",
      // render: (_, item, index) => {
      //   return (
      //     <div
      //       style={{
      //         display: "flex",
      //         alignItems: "center",
      //         justifyContent: "space-around",
      //       }}
      //     >
      //       <SVG.IcDelete />
      //     </div>
      //   );
      // },
    },
  ];

  const onChangeInputSearch = (data) => {
    if (onSearch) {
      setState({ searchLocal: { ...state.searchLocal, ...data } });
    } else {
      getDsNb({ phieuLinhId: id, ...data });
    }
  };

  return (
    <SoLuongLeLinh
      loaiNhapXuat={loaiNhapXuat}
      onSearch={onChangeInputSearch}
      renderColumns={renderColumns}
      dataSource={dsNbDvKhoMemo}
    />
  );
};

export default DanhSachNguoiBenh;
