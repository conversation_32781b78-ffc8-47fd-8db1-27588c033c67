import React, { useEffect, useRef, useMemo } from "react";
import { MainPage } from "./styled";
import { useDispatch, useSelector } from "react-redux";
import { Dropdown, Card, Tooltip, <PERSON><PERSON>, AuthWrapper } from "components";
import ThongTinPhieu from "./ThongTinPhieu";
import Danh<PERSON>achHangHoa from "./DanhSachHangHoa";
import { useHistory, useParams, useLocation } from "react-router-dom";
import TrangThai from "pages/kho/components/TrangThai";
import Action from "../Action";
import { Menu, message, Tabs } from "antd";
import DanhSachNguoiBenh from "./DanhSachNguoiBenh";
import { useTranslation } from "react-i18next";
import { SVG } from "assets";
import { useConfirm, useLoading } from "hooks";
import { transformObjToQueryString } from "hooks/useQueryString/queryString";
import useCanPrintPhieu from "pages/quanLyNoiTru/hooks/useCanPrintPhieu";
import {
  TRANG_THAI_THUOC,
  LOAI_NHAP_XUAT,
  CO_CHE_DUYET_PHAT,
  TRANG_THAI_PHIEU_NHAP_XUAT,
  ROLES,
  DS_TINH_CHAT_KHOA,
} from "constants/index";
import { isArray, isBoolean } from "utils/index";

const ChiTiet = ({ ...props }) => {
  const { showConfirm } = useConfirm();
  const { id } = useParams();
  const { t } = useTranslation();
  const history = useHistory();

  const { thongTinPhieu } = useSelector((state) => state.phieuNhapXuat);
  const { state: locationState } = useLocation();
  const { showLoading, hideLoading } = useLoading();
  const dsTrangThaiDlsIds = useCanPrintPhieu(thongTinPhieu);
  const listTrangThaiDls = Object.values(TRANG_THAI_THUOC);

  const {
    phieuNhapXuat: {
      getById,
      xoaPhieu,
      resetData,
      inPhieuLinh,
      inPhieuLinhChiTiet,
      duyetNhapPhieu,
      huyDuyetNhapPhieu,
    },
    nbDvKho: { getNbDvKho: getDsNb },
  } = useDispatch();

  useEffect(() => {
    getDsNb({ phieuLinhId: id });
    getById(id);
    return () => {
      resetData();
    };
  }, []);

  const onDelete = () => {
    showConfirm(
      {
        title: t("common.canhBao"),
        content: `${t("quanLyNoiTru.xoaPhieuSo", {
          title: thongTinPhieu?.soPhieu,
        })}`,
        cancelText: t("common.dong"),
        okText: t("common.xoa"),
        classNameOkText: "button-error",
        showImg: true,
        showBtnOk: true,
      },
      () => {
        xoaPhieu({ id: thongTinPhieu.id }).then((s) => {
          history.push("/kho/nhap-kho");
        });
      },
      () => {}
    );
  };

  const isXacNhanNhap = useMemo(() => {
    if (thongTinPhieu) {
      const { kho, loaiNhapXuat, khoaChiDinh } = thongTinPhieu || {};
      let dsCoCheDuyetPhat = kho?.dsCoCheDuyetPhat || [];
      return (
        loaiNhapXuat === LOAI_NHAP_XUAT.LINH_BU_TU_TRUC &&
        dsCoCheDuyetPhat.includes(CO_CHE_DUYET_PHAT.XAC_NHAN_NHAP) &&
        (khoaChiDinh?.dsTinhChatKhoa || []).includes(
          DS_TINH_CHAT_KHOA.XAC_NHAN_NHAP_KHO
        )
      );
    }
    return false;
  }, [thongTinPhieu]);

  const times = useMemo(() => {
    const {
      thoiGianDuyet,
      thoiGianGuiDuyet,
      thoiGianTaoPhieu,
      thoiGianDuyetNhap,
      trangThai,
    } = thongTinPhieu || {};
    let arrTime = [thoiGianTaoPhieu, thoiGianGuiDuyet];
    if (isXacNhanNhap) {
      let _thoiGianDuyet =
        trangThai >= TRANG_THAI_PHIEU_NHAP_XUAT.CHO_XAC_NHAN_NHAP
          ? thoiGianDuyet
          : null;
      arrTime.splice(2, 0, _thoiGianDuyet);
      let _thoiGianDuyetNhap =
        trangThai === TRANG_THAI_PHIEU_NHAP_XUAT.HOAN_THANH
          ? thoiGianDuyetNhap
          : null;
      arrTime.push(_thoiGianDuyetNhap);
    } else {
      arrTime.push(thoiGianDuyet);
    }
    return arrTime.map((item) => item);
  }, [thongTinPhieu, isXacNhanNhap]);

  const onPrint = async (printFunction, { id, isPhieuLinh } = {}) => {
    if (
      isPhieuLinh &&
      !isBoolean(dsTrangThaiDlsIds) &&
      isArray(dsTrangThaiDlsIds, true)
    ) {
      let trangThaiIn = listTrangThaiDls.filter((i) =>
        dsTrangThaiDlsIds.includes(i.id)
      );
      message.error(
        t("quanLyNoiTru.chiChoPhepInPhieuVoiCacTrangThaiDls", {
          title: trangThaiIn.map((x) => x.ten).join("; "),
        })
      );
      return;
    }

    try {
      showLoading();
      await printFunction({ id });
    } catch (error) {
      console.error(error);
    } finally {
      hideLoading();
    }
  };

  const onPrintPhieuLinh = () =>
    onPrint(inPhieuLinh, { id, isPhieuLinh: true });
  const onPrintPhieuLinhChiTiet = () => onPrint(inPhieuLinhChiTiet, { id });

  const onXacNhanNhap = async () => {
    try {
      showLoading();
      await duyetNhapPhieu({ id: thongTinPhieu?.id });
    } catch (error) {
      console.error(error);
    } finally {
      hideLoading();
    }
  };

  const onHuyXacNhanNhap = async () => {
    try {
      showLoading();
      await huyDuyetNhapPhieu({ id: thongTinPhieu?.id });
    } catch (error) {
      console.error(error);
    } finally {
      hideLoading();
    }
  };

  const menu = (
    <Menu
      items={[
        {
          key: 0,
          label: (
            <a href={() => false} onClick={onPrintPhieuLinh}>
              {t("kho.inChiTietPhieuNhap")}
            </a>
          ),
        },
        {
          key: 1,
          label: (
            <a href={() => false} onClick={onPrintPhieuLinhChiTiet}>
              {t("kho.inDanhSachBenhNhan")}
            </a>
          ),
        },
      ]}
    />
  );

  return (
    <MainPage
      breadcrumb={[
        { title: t("kho.kho"), link: "/kho" },
        {
          title: t("kho.nhapKho"),
          link: "/kho/nhap-kho" + transformObjToQueryString(locationState),
        },
        {
          title: t("kho.chiTietLinhBu"),
          link: `/kho/nhap-kho/chi-tiet-linh-bu/${id}`,
        },
      ]}
      title={
        <div className="wrapper-title">
          {t("kho.phieuLinhBuTuTruc")}
          <div className="header-action">
            {[10, 15].includes(thongTinPhieu?.trangThai) && (
              <Tooltip title={t("kho.xoaPhieu")}>
                <div className="action-btn" onClick={onDelete}>
                  <SVG.IcDelete />
                </div>
              </Tooltip>
            )}
          </div>
        </div>
      }
      titleRight={
        <TrangThai times={times} {...(isXacNhanNhap && { type: 6 })} />
      }
    >
      <Card>
        <ThongTinPhieu {...props} />
      </Card>
      <Card noPadding={true}>
        <Tabs defaultActiveKey="1">
          <Tabs.TabPane tab={t("kho.danhSachHangHoa")} key="1">
            <DanhSachHangHoa {...props} isEdit={false} />
          </Tabs.TabPane>
          <Tabs.TabPane tab={t("kho.danhSachNguoiBenhSuDung")} key="2">
            <DanhSachNguoiBenh {...props} isEdit={false} />
          </Tabs.TabPane>
        </Tabs>
      </Card>
      <Action
        otherBtn={
          <>
            <Dropdown overlay={menu} trigger={["click"]}>
              <Button rightIcon={<SVG.IcPrint />} minWidth={80}>
                {t("common.inGiayTo")}
              </Button>
            </Dropdown>
            {isXacNhanNhap &&
              onXacNhanNhap &&
              thongTinPhieu?.trangThai ===
                TRANG_THAI_PHIEU_NHAP_XUAT.CHO_XAC_NHAN_NHAP && (
                <>
                  <Button
                    className="right-btn"
                    minWidth={80}
                    type="primary"
                    onClick={onXacNhanNhap}
                  >
                    {t("kho.xacNhanNhap")}
                  </Button>
                </>
              )}
            {isXacNhanNhap &&
              onHuyXacNhanNhap &&
              thongTinPhieu?.trangThai ===
                TRANG_THAI_PHIEU_NHAP_XUAT.HOAN_THANH && (
                <AuthWrapper accessRoles={[ROLES["KHO"].HUY_XAC_NHAN_NHAP]}>
                  <Button
                    className="right-btn"
                    minWidth={80}
                    type="primary"
                    onClick={onHuyXacNhanNhap}
                  >
                    {t("kho.huyDuyetNhap")}
                  </Button>
                </AuthWrapper>
              )}
          </>
        }
        showBack={true}
        dsTrangThaiDlsIds={dsTrangThaiDlsIds}
      />
    </MainPage>
  );
};

export default ChiTiet;
