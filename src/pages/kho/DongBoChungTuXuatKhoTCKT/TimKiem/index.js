import React, { useRef, useCallback, useMemo } from "react";
import moment from "moment";
import { useTranslation } from "react-i18next";

import { useEnum, useQueryAll } from "hooks";

import { BaseSearch } from "components";
import { ENUM } from "constants/index";
import { query } from "redux-store/stores";
import { LOAI_DICH_VU } from "constants/index";
import { LIST_LOAI_NHAP_XUAT } from "../configs";
import { useSearchParamsOptimized } from "../hooks";

const TimKiem = () => {
  const { t } = useTranslation();
  const refDateOptions1 = useRef(null);
  const refDateOptions2 = useRef(null);

  const nhomDichVuParams = useMemo(
    () => ({
      params: {
        dsLoaiDichVu: [
          LOAI_DICH_VU.THUOC,
          LOAI_DICH_VU.VAT_TU,
          LOAI_DICH_VU.HOA_CHAT,
          LOAI_DICH_VU.CHE_PHAM_MAU,
        ],
      },
    }),
    []
  );

  const { data: listKhoaTheoTaiKhoan } = useQueryAll(
    query.khoa.queryKhoaTheoTaiKhoan
  );
  const { data: listKhoTheoTaiKhoan } = useQueryAll(
    query.kho.queryKhoTheoTaiKhoan
  );
  const { data: listAllNguonNhapKho } = useQueryAll(
    query.nguonNhapKho.queryAllNguonNhapKho
  );
  const { data: listAllNhomDichVuCap1 } = useQueryAll(
    query.nhomDichVuCap1.queryAllNhomDichVuCap1(nhomDichVuParams)
  );

  const nhomDichVuCap2Params = useMemo(
    () => ({
      params: {
        dsNhomDichVuKhoCap1Id: listAllNhomDichVuCap1?.map((i) => i.id) || [],
      },
      enabled: !!listAllNhomDichVuCap1?.length,
    }),
    [listAllNhomDichVuCap1]
  );

  const { data: listAllNhomDichVuCap2 } = useQueryAll(
    query.nhomDichVuCap2.queryAllNhomDichVuCap2(nhomDichVuCap2Params)
  );

  const [listTrangThaiDongBoChungTu] = useEnum(
    ENUM.TRANG_THAI_DONG_BO_CHUNG_TU
  );

  const { searchParams, setSearchParams } = useSearchParamsOptimized();

  const onChangeInputSearch = useCallback(
    (params) => {
      setSearchParams({
        page: 0,
        ...params,
      });
    },
    [setSearchParams]
  );

  const dataInput = useMemo(
    () => [
      {
        widthInput: "250px",
        type: "dateOptions",
        state: {
          tuThoiGian: searchParams.tuThoiGian
            ? moment(searchParams.tuThoiGian)
            : null,
          denThoiGian: searchParams.denThoiGian
            ? moment(searchParams.denThoiGian)
            : null,
        },
        functionChangeInput: ({ tuThoiGian, denThoiGian }) => {
          onChangeInputSearch({
            tuThoiGian: tuThoiGian
              ? moment(tuThoiGian).format("YYYY-MM-DD 00:00:00")
              : null,
            denThoiGian: denThoiGian
              ? moment(denThoiGian).format("YYYY-MM-DD 23:59:59")
              : null,
          });
        },
        keyValueInput: ["tuThoiGian", "denThoiGian"],
        title: t("baoCao.thoiGianDuyet"),
        placeholder: [t("common.tuNgay"), t("common.denNgay")],
        format: "DD/MM/YYYY",
        ref: refDateOptions1,
        ignoreKeys: ["tatCa"],
      },
      {
        widthInput: "232px",
        keyValueInput: "dsKhoaId",
        listSelect: listKhoaTheoTaiKhoan,
        title: t("common.khoa"),
        functionChangeInput: onChangeInputSearch,
        type: "select",
        mode: "multiple",
        placeholder: t("common.chonKhoa"),
      },
      {
        widthInput: "232px",
        keyValueInput: "dsKhoId",
        listSelect: listKhoTheoTaiKhoan,
        title: t("kho.khoXuat"),
        functionChangeInput: onChangeInputSearch,
        type: "select",
        mode: "multiple",
        placeholder: t("baoCao.chonKhoXuat"),
      },
      {
        widthInput: "232px",
        keyValueInput: "dsLoaiNhapXuat",
        functionChangeInput: onChangeInputSearch,
        title: t("kho.loaiNhapXuat"),
        listSelect: LIST_LOAI_NHAP_XUAT,
        functionChangeInput: onChangeInputSearch,
        type: "select",
        mode: "multiple",
        placeholder: t("kho.chonLoaiNhapXuat"),
      },
      {
        widthInput: "232px",
        keyValueInput: "dsNguonNhapKhoId",
        listSelect: listAllNguonNhapKho,
        title: t("danhMuc.nguonNhapKho"),
        functionChangeInput: onChangeInputSearch,
        type: "select",
        mode: "multiple",
        placeholder: t("kho.chonNguonNhapKho"),
      },
      {
        widthInput: "232px",
        keyValueInput: "dsNhomDichVuCap2Id",
        title: t("quanLyNoiTru.dvNoiTru.nhomDvCap2"),
        listSelect: listAllNhomDichVuCap2,
        functionChangeInput: onChangeInputSearch,
        type: "select",
        mode: "multiple",
        placeholder: t("danhMuc.chonNhomDichVuCap2"),
      },
      {
        widthInput: "150px",
        placeholder: t("common.trangThai"),
        keyValueInput: "dsTrangThaiDongBo",
        listSelect: listTrangThaiDongBoChungTu,
        functionChangeInput: onChangeInputSearch,
        type: "select",
        mode: "multiple",
      },
      {
        widthInput: "250px",
        type: "dateOptions",
        state: {
          tuThoiGianDongBo: searchParams.tuThoiGianDongBo
            ? moment(searchParams.tuThoiGianDongBo)
            : null,
          denThoiGianDongBo: searchParams.denThoiGianDongBo
            ? moment(searchParams.denThoiGianDongBo)
            : null,
        },
        functionChangeInput: ({ tuThoiGianDongBo, denThoiGianDongBo }) => {
          onChangeInputSearch({
            tuThoiGianDongBo: tuThoiGianDongBo
              ? moment(tuThoiGianDongBo).format("YYYY-MM-DD 00:00:00")
              : null,
            denThoiGianDongBo: denThoiGianDongBo
              ? moment(denThoiGianDongBo).format("YYYY-MM-DD 23:59:59")
              : null,
          });
        },
        keyValueInput: ["tuThoiGianDongBo", "denThoiGianDongBo"],
        title: t("kho.ngayGui"),
        placeholder: [t("common.tuNgay"), t("common.denNgay")],
        format: "DD/MM/YYYY",
        ref: refDateOptions2,
      },
    ],
    [
      searchParams,
      onChangeInputSearch,
      t,
      listKhoaTheoTaiKhoan,
      listKhoTheoTaiKhoan,
      listAllNguonNhapKho,
      listAllNhomDichVuCap2,
      listTrangThaiDongBoChungTu,
      refDateOptions1,
      refDateOptions2,
    ]
  );

  const dataFilter = [];

  return (
    <BaseSearch
      cacheData={searchParams}
      dataInput={dataInput}
      filter={{
        open: true,
        width: "110px",
        funcSearchData: onChangeInputSearch,
        data: dataFilter,
      }}
    />
  );
};

export default TimKiem;
