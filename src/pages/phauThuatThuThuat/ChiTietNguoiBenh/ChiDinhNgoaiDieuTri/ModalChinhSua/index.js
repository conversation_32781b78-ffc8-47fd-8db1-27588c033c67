import React, {
  forwardRef,
  useImperativeHandle,
  useRef,
  useState,
} from "react";
import { Checkbox, Button, ModalTemplate, Select } from "components";
import { Main } from "./styled";
import { Row, Col, Form, Input, InputNumber } from "antd";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import FormWraper from "components/FormWraper";
import { SVG } from "assets";
import { useThietLap } from "hooks";
import { LOAI_DICH_VU, THIET_LAP_CHUNG } from "constants/index";

const { TextArea } = Input;

const ModalChinhSua = (props, ref) => {
  const { t } = useTranslation();
  const refModal = useRef(null);
  const [form] = Form.useForm();

  const {
    chiDinhKhamBenh: { themThongTinDV },
  } = useDispatch();
  const { getDsDvNgoaiDieuTri } = useDispatch().chiDinhNgoaiDieuTri;
  const { dsDvNgoaiDieuTri } = useSelector(
    (state) => state.chiDinhNgoaiDieuTri
  );
  const [dataAN_CHECKBOX_TU_TRA] = useThietLap(
    THIET_LAP_CHUNG.AN_CHECKBOX_TU_TRA
  );

  const [state, _setState] = useState({
    listChooseDv: [],
    selectedRowKeys: [],
  });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  useImperativeHandle(ref, () => ({
    show: (data = {}) => {
      setState({ item: data });
      getDsDvNgoaiDieuTri({ page: 0, size: 500, dataSearch: {} });

      const { khongTinhTien, dichVuId, tuTra, ghiChu, soLuong } = data;
      form.setFieldsValue({
        khongTinhTien,
        dichVuId,
        tuTra,
        ghiChu,
        soLuong,
      });
      refModal.current && refModal.current.show();
    },
  }));

  const onCancel = () => {
    refModal.current && refModal.current.hide();
  };

  const onFinish = (values) => {
    console.log("Success:", values);
  };

  const onFinishFailed = (errorInfo) => {
    console.log("Failed:", errorInfo);
  };

  function onSubmit() {
    form.validateFields().then((values) => {
      themThongTinDV({
        body: [{ id: state.item?.id, nbDichVu: { ...values } }],
        loaiDichVu: LOAI_DICH_VU.NGOAI_DIEU_TRI,
      }).then(() => {
        props.refreshList();
        onCancel();
      });
    });
  }

  return (
    <ModalTemplate
      ref={refModal}
      width={"50%"}
      title="Chỉnh sửa dịch vụ"
      onCancel={onCancel}
      actionLeft={<Button.QuayLai onClick={onCancel} />}
      actionRight={
        <Button
          type="primary"
          minWidth={100}
          rightIcon={<SVG.IcSave />}
          onClick={onSubmit}
        >
          {t("common.luu")}
        </Button>
      }
    >
      <Main>
        <FormWraper
          name="basic"
          initialValues={{ remember: true }}
          onFinish={onFinish}
          layout={"vertical"}
          onFinishFailed={onFinishFailed}
          autoComplete="off"
          form={form}
        >
          <Row>
            <Col span={16}>
              <Form.Item label="Tên dịch vụ" name="dichVuId">
                <Select
                  data={(dsDvNgoaiDieuTri || []).map((x) => ({
                    id: x.dichVuId,
                    ten: x.ten,
                  }))}
                  placeholder="Loại lên lịch"
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="Số lượng" name="soLuong">
                <InputNumber />
              </Form.Item>
            </Col>
          </Row>

          <Row>
            <Col span={16}>
              <Form.Item label="Lưu ý" name="ghiChu">
                <TextArea rows={4} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="khongTinhTien" valuePropName="checked">
                <Checkbox>Không tính tiền</Checkbox>
              </Form.Item>
              {!dataAN_CHECKBOX_TU_TRA?.eval() && (
                <Form.Item name="tuTra" valuePropName="checked">
                  <Checkbox>Tự túc</Checkbox>
                </Form.Item>
              )}
            </Col>
          </Row>
        </FormWraper>
      </Main>
    </ModalTemplate>
  );
};
export default forwardRef(ModalChinhSua);
