import React, {
  memo,
  useCallback,
  useEffect,
  useMemo,
  useState,
  useRef,
} from "react";
import { Col, Input, message, Row, Divider, Form } from "antd";
import {
  Checkbox,
  InputTimeout,
  Select,
  SelectLoadMore,
  SelectLargeData,
  ImageEdit,
  Button,
  DateTimePicker,
  Tooltip,
} from "components";
import ElementFilter from "components/common/ElementFilter";
import dichVuKyThuatProvider from "data-access/categories/dm-dv-ky-thuat-provider";
import useThongTinNb from "../hook/useThongTinNb";
import moment from "moment";
import _, { debounce } from "lodash";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { Main, AddButtonStyled } from "./styled";
import {
  DOI_TUONG_KCB,
  ENUM,
  LOAI_DICH_VU,
  LOAI_PHONG,
  ROLES,
  THIET_LAP_CHUNG,
  TRANG_THAI_DICH_VU,
  TRANG_THAI_HOAN,
  TRANG_THAI_PHIEU_THU_THANH_TOAN,
  TY_LE_THANH_TOAN,
} from "constants/index";
import {
  useConfirm,
  useEnum,
  useListAll,
  useQueryAll,
  useStore,
  useThietLap,
} from "hooks";
import { useParams } from "react-router-dom";
import ModalChonDvGoiPttt from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/Modal/ModalChonDvGoiPttt";
import ModalThemMoiMauKetQua from "../ModalThemMoiMauKetQua";
import ProtocolForm from "pages/danhMuc2/mauKetQuaPTTT/components/ProtocolForm";
import { SVG } from "assets";
import { checkRole } from "lib-utils/role-utils";
import { query } from "redux-store/stores";
import env from "module_env/ENV";

const { SelectChanDoan } = SelectLargeData;

const mapData = (i) => ({
  value: i.dichVuId,
  label: i.ten,
  ma: i.ma,
  maNhomDichVuCap1: i.maNhomDichVuCap1,
  phanLoaiPtTt: i.phanLoaiPtTt,
  maTuongDuong: i.maTuongDuong,
  tenTuongDuong: i.tenTuongDuong,
});

const mapDataMa = (i) => ({
  value: i.dichVuId,
  label: i.ma,
  ten: i.ten,
  maNhomDichVuCap1: i.maNhomDichVuCap1,
  phanLoaiPtTt: i.phanLoaiPtTt,
  maTuongDuong: i.maTuongDuong,
  tenTuongDuong: i.tenTuongDuong,
});

const ThongTinPTTT = ({ listAllPtttCungCaKip, khoaLamViecId }) => {
  const { showConfirm } = useConfirm();
  const [thongTinNb] = useThongTinNb();
  const { id } = useParams();
  const chiTietPhauThuat = useStore("pttt.chiTietPhauThuat", {});
  const [listPhanLoaiPTTT] = useEnum(ENUM.PHAN_LOAI_PTTT);
  const [listTrangThaiDichVu] = useEnum(ENUM.TRANG_THAI_DICH_VU);
  const [listLoaiPtTt] = useEnum(ENUM.LOAI_PTTT);
  const listDvVatTu = useStore("chiDinhVatTu.listDvVatTu", []);
  const listDvThuoc = useStore("chiDinhDichVuKho.listDvThuoc", []);
  const listDvHoaChat = useStore("chiDinhHoaChat.listDvHoaChat", []);
  const refChiDinhGoiMo = useRef(null);
  const refModalThemMauKetQua = useRef(null);
  const refUpload = useRef(null);
  const refDsLuocDo = useRef([]);
  const [dsLuocDo, setDsLuocDo] = useState([]);
  const [protocolForm] = Form.useForm();

  const [listLoaiThoiGianHanhChinh] = useEnum(ENUM.LOAI_THOI_GIAN_HANH_CHINH);
  const { listAllPhuongPhapVoCam } = useSelector(
    (state) => state.phuongPhapVoCam
  );
  const { listAllPhauThuatVien } = useSelector((state) => state.nhanVien);
  const listAllMauKetQuaPTTT = useStore(
    "mauKetQuaPTTT.listAllMauKetQuaPTTT",
    []
  );

  const nhanVienId = useStore("auth.auth.nhanVienId", null);
  const { data: listAllKhoa } = useQueryAll(query.khoa.queryAllKhoa);
  const { data: listAllNhomDichVuCap1 } = useQueryAll(
    query.nhomDichVuCap1.queryAllNhomDichVuCap1
  );

  const dataToDieuTri = useStore("toDieuTri.currentToDieuTri", {});
  const dichVuCha = useStore("pttt.dichVuCha", {});
  const listAllPhong = useStore("phong.listAllPhong", []);
  const listDataTongHop = useStore("maPttt.listDataTongHop", []);
  const listDataKhoaTongHop = useStore("khoa.listAllKhoa", []);
  const [MA_NHOM_DICH_VU_CAP1_PT] = useThietLap(
    THIET_LAP_CHUNG.MA_NHOM_DICH_VU_CAP1_PT
  );
  const [HIEN_THI_GOI_MO_TRONG_PTTT] = useThietLap(
    THIET_LAP_CHUNG.HIEN_THI_GOI_MO_TRONG_PTTT
  );
  const [BAT_BUOC_NHAP_BUONG_PT] = useThietLap(
    THIET_LAP_CHUNG.BAT_BUOC_NHAP_BUONG_PT
  );
  const [listKiemSoatNhiemKhuan] = useEnum(ENUM.KIEM_SOAT_NHIEM_KHUAN);
  const [listCoKhong] = useEnum(ENUM.CO_KHONG);
  const [listNhomPhauThuat] = useEnum(ENUM.NHOM_PHAU_THUAT);
  const [listLoaiMo] = useEnum(ENUM.LOAI_MO);
  const [listViTriPhauThuat] = useEnum(ENUM.VI_TRI_PHAU_THUAT);
  const [BAT_BUOC_NHAP_LOAI_PTTT] = useThietLap(
    THIET_LAP_CHUNG.BAT_BUOC_NHAP_LOAI_PTTT
  );
  const [AN_LOAI_PTTT] = useThietLap(THIET_LAP_CHUNG.AN_LOAI_PTTT);
  const [XML130_TRUONG_BAT_BUOC] = useThietLap(
    THIET_LAP_CHUNG.XML130_TRUONG_BAT_BUOC
  );
  const [HIEN_THI_DAN_LUU_CHI_TIET_PTTT] = useThietLap(
    THIET_LAP_CHUNG.HIEN_THI_DAN_LUU_CHI_TIET_PTTT
  );
  const [dataBAT_BUOC_NHAP_CHAN_DOAN_SAU_PT] = useThietLap(
    THIET_LAP_CHUNG.BAT_BUOC_NHAP_CHAN_DOAN_SAU_PT
  );
  const [dataHIEN_THI_CAC_TRUONG_CHO_PT_MAT] = useThietLap(
    THIET_LAP_CHUNG.HIEN_THI_CAC_TRUONG_CHO_PT_MAT
  );

  const {
    pttt: {
      updateData,
      updateThongTinPTTT,
      themThongTin,
      getDichVuCha,
      createOrUpdateGoiDv,
      getById,
    },
    mauKetQuaPTTT: { getListAllMauKetQuaPTTT },
    phong: { getListAllPhong },
    chiDinhGoiPTTT: { xoaNbGoiPTTT },
    maPttt: { getListDataTongHop },
  } = useDispatch();

  const [state, _setState] = useState({ dataDichVuKho: [] });

  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  useEffect(() => {
    const dataDichVuKho = [...listDvVatTu, ...listDvThuoc, ...listDvHoaChat];
    setState({ dataDichVuKho });
  }, [listDvVatTu, listDvThuoc, listDvHoaChat]);

  const disabledAll = useMemo(
    () =>
      !checkRole([
        ROLES.PHAU_THUAT_THU_THUAT
          .CHINH_SUA_THONG_TIN_PHAU_THUAT_SAU_KHI_HOAN_THANH_RA_VIEN,
      ]) &&
      (chiTietPhauThuat.trangThai !== 63 ||
        chiTietPhauThuat?.trangThaiHoan === 40),
    [chiTietPhauThuat.trangThai, chiTietPhauThuat.khongThucHien]
  );

  const disabledThoiGian = useMemo(
    () =>
      !checkRole([
        ROLES.PHAU_THUAT_THU_THUAT
          .CHINH_SUA_THONG_TIN_PHAU_THUAT_SAU_KHI_HOAN_THANH_RA_VIEN,
      ]) &&
      (![63, 155].includes(chiTietPhauThuat.trangThai) ||
        chiTietPhauThuat?.trangThaiHoan === 40),
    [chiTietPhauThuat.trangThai, chiTietPhauThuat.trangThaiHoan]
  );

  useEffect(() => {
    return () => {
      updateData({ chiTietPhauThuat: {} });
    };
  }, []);

  useEffect(() => {
    if (nhanVienId && khoaLamViecId)
      getListAllMauKetQuaPTTT({
        active: true,
        page: "",
        size: "",
        loaiDichVu: LOAI_DICH_VU.PHAU_THUAT_THU_THUAT,
        dsBacSiChiDinhId: nhanVienId,
        dsKhoaId: khoaLamViecId,
      });
  }, [nhanVienId, khoaLamViecId]);

  useEffect(() => {
    if (!HIEN_THI_GOI_MO_TRONG_PTTT?.eval() && !chiTietPhauThuat.phuongPhap) {
      updateThongTinPTTT({
        phuongPhap: chiTietPhauThuat.tenTuongDuong ?? "",
      });
    }
  }, [HIEN_THI_GOI_MO_TRONG_PTTT, chiTietPhauThuat?.id]);

  useEffect(() => {
    if (chiTietPhauThuat?.id) {
      if (
        chiTietPhauThuat?.chiDinhTuLoaiDichVu ===
        LOAI_DICH_VU.PHAU_THUAT_THU_THUAT
      ) {
        getDichVuCha(chiTietPhauThuat.chiDinhTuDichVuId);
      }
      getListAllPhong({
        active: true,
        page: "",
        size: "",
        dsLoaiPhong: [LOAI_PHONG.PHONG_GIUONG, LOAI_PHONG.PHONG_GIUONG_TU_CHON],
        khoaId: chiTietPhauThuat?.khoaThucHienId,
      });
    } else {
      updateData({ dichVuCha: null });
    }
  }, [chiTietPhauThuat?.id]);

  const { t, i18n } = useTranslation();
  // console.log("disabledAll", chiTietPhauThuat);
  const listMaPtttQuocTe = useMemo(
    () =>
      listDataTongHop?.map((o) => {
        return { ...o, ten: `${o.ma} - ${o.ten}` };
      }),
    [listDataTongHop]
  );

  const disableDichVu = useMemo(() => {
    if (chiTietPhauThuat.trangThaiHoan === TRANG_THAI_HOAN.KHONG_THUC_HIEN)
      return true;
    if (chiTietPhauThuat.phauThuat || thongTinNb.noiTru) {
      return (
        ![
          TRANG_THAI_DICH_VU.CHO_TIEP_NHAN,
          TRANG_THAI_DICH_VU.DA_TIEP_NHAN,
        ].includes(chiTietPhauThuat.trangThai) ||
        chiTietPhauThuat?.thanhToan ===
          TRANG_THAI_PHIEU_THU_THANH_TOAN.DA_THANH_TOAN
      );
    } else {
      return (
        ![TRANG_THAI_DICH_VU.CHO_TIEP_NHAN].includes(
          chiTietPhauThuat.trangThai
        ) ||
        chiTietPhauThuat?.thanhToan ===
          TRANG_THAI_PHIEU_THU_THANH_TOAN.DA_THANH_TOAN
      );
    }
  }, [
    chiTietPhauThuat.trangThai,
    chiTietPhauThuat.phauThuat,
    thongTinNb,
    chiTietPhauThuat.khongThucHien,
  ]);

  const listBacSi = useMemo(() => {
    return !chiTietPhauThuat?.bacSiChiDinhId ||
      listAllPhauThuatVien?.find(
        (item) => item.id == chiTietPhauThuat?.bacSiChiDinhId
      )
      ? listAllPhauThuatVien || []
      : [
          {
            id: chiTietPhauThuat?.bacSiChiDinhId,
            ten: chiTietPhauThuat?.tenBacSiChiDinh,
          },
          ...(listAllPhauThuatVien || []),
        ];
  }, [listAllPhauThuatVien, chiTietPhauThuat?.bacSiChiDinhId]);

  const updateThongTinPTTTWithDebounce = debounce(updateThongTinPTTT, 1000);

  const onChange = useCallback(
    (key, key2) => (e, data) => {
      const value = e?.hasOwnProperty("target")
        ? e?.target?.type === "checkbox"
          ? e?.target?.checked
          : e?.target?.value
        : e?.hasOwnProperty("_d")
        ? moment(e._d).format("YYYY-MM-DD HH:mm:ss")
        : e;
      if (key === "dichVuId") {
        updateThongTinPTTT({
          [key]: value,
          maDichVu: key2 == "ma" ? data?.label : data?.ma,
          tenDichVu: key2 == "ma" ? data?.ten : data?.label,
          phauThuat: data.maNhomDichVuCap1 === MA_NHOM_DICH_VU_CAP1_PT,
          phanLoai: data?.phanLoaiPtTt,
          ...(!HIEN_THI_GOI_MO_TRONG_PTTT?.eval()
            ? {
                phuongPhap: data.tenTuongDuong ?? "",
              }
            : {}),
        });
      } else if (key === "thoiGianTiepNhan") {
        let thoiGianBanGiaoTruoc = null;
        if (value) {
          thoiGianBanGiaoTruoc = moment(value)
            .subtract(15, "minutes")
            .format("YYYY-MM-DD HH:mm:ss");
        }
        updateThongTinPTTT({
          thoiGianThucHien: value,
          thoiGianTiepNhan: value,
          thoiGianBanGiaoTruoc,
        });
      } else if (key === "thoiGianCoKetQua") {
        let thoiGianKetThuc = value;
        let thoiGianBanGiaoSau = null;
        if (thoiGianKetThuc) {
          thoiGianBanGiaoSau = moment(value)
            .add(10, "minutes")
            .format("YYYY-MM-DD HH:mm:ss");
        }
        updateThongTinPTTT({
          thoiGianCoKetQua: value,
          thoiGianBanGiaoSau: thoiGianBanGiaoSau,
        });
      } else if (key === "dsCdChinhId") {
        updateThongTinPTTT({
          dsCdChinhId: value,
          dsCdKemTheoId: null,
          dsCdKemTheo: null,
        });
      } else if (key === "boSung") {
        updateThongTinPTTT({
          boSung: { ...chiTietPhauThuat.boSung, [key2]: value },
        });
      } else {
        if (key === "dsLuocDo") {
          refDsLuocDo.current = value;
          setDsLuocDo(value);
          updateThongTinPTTTWithDebounce({ [key]: value });
        } else {
          updateThongTinPTTT({ [key]: value });
        }
      }
    },
    [MA_NHOM_DICH_VU_CAP1_PT]
  );

  const addValue = useMemo(
    () => ({
      value: chiTietPhauThuat?.dichVuId,
      label: chiTietPhauThuat?.tenDichVu,
    }),
    [chiTietPhauThuat?.dichVuId]
  );

  const addValueMa = useMemo(
    () => ({
      value: chiTietPhauThuat?.dichVuId,
      label: chiTietPhauThuat?.maDichVu,
    }),
    [chiTietPhauThuat?.dichVuId]
  );

  const addParam = useMemo(
    () => ({
      active: true,
      khoaChiDinhId: chiTietPhauThuat.khoaThucHienId,
      loaiDichVu: 40,
      dsDoiTuongSuDung:
        thongTinNb?.doiTuongKcb === DOI_TUONG_KCB.NGOAI_TRU ? 20 : 30,
    }),
    [chiTietPhauThuat?.id, thongTinNb]
  );

  const onChangeMauKetQua = (e, item) => {
    if (!item) return;

    const {
      cachThuc,
      phuongThuc,
      chanDoan,
      ketLuan,
      phuongPhapVoCamId,
      dsLuocDo,
      dsProtocol,
    } = item;

    const hasProtocolData = dsProtocol && dsProtocol.length > 0;

    updateThongTinPTTT({
      ...(!hasProtocolData && cachThuc ? { cachThuc } : {}),
      ...(phuongThuc ? { phuongPhap: phuongThuc } : {}),
      ...(chanDoan ? { chanDoan } : {}),
      ...(ketLuan ? { ketLuan } : {}),
      ...(phuongPhapVoCamId ? { phuongPhapVoCamId } : {}),
      ...(dsLuocDo ? { dsLuocDo } : {}),
      ...(hasProtocolData ? { dsProtocol } : {}),
    });

    if (dsLuocDo) {
      refDsLuocDo.current = dsLuocDo;
      setDsLuocDo(dsLuocDo);
    }

    setState({
      mauKetQuaPTTT: e,
      selectedProtocolId: hasProtocolData
        ? dsProtocol[0]?.protocolChiTiet?.protocolId
        : null,
    });
  };

  const onChangeLoaiPttt = (e) => {
    updateThongTinPTTT({ loaiPtTt: e });
    if (BAT_BUOC_NHAP_LOAI_PTTT.toLowerCase() === "true" && !e) {
      message.error(t("pttt.chuaNhapLoaiPttt"));
      return;
    }
    let payload = { id: id, loaiPtTt: e || null };
    themThongTin(payload);
  };

  const onDisabledDate = (d) => {
    if (!d) return false;

    const doiTuong = thongTinNb?.doiTuongKcb;
    const chiDinhTuLoaiDichVu = chiTietPhauThuat?.chiDinhTuLoaiDichVu;
    const thoiGianCoKetQua = moment(chiTietPhauThuat?.thoiGianCoKetQua);
    const thoiGianYLenh = moment(dataToDieuTri?.thoiGianYLenh);

    if (
      [
        DOI_TUONG_KCB.DIEU_TRI_NGOAI_TRU,
        DOI_TUONG_KCB.DIEU_TRI_NOI_TRU,
        DOI_TUONG_KCB.DIEU_TRI_NOI_TRU_BAN_NGAY,
      ].includes(doiTuong) &&
      chiDinhTuLoaiDichVu === LOAI_DICH_VU.TO_DIEU_TRI
    ) {
      return (
        d.isAfter(thoiGianCoKetQua, "day") || d.isBefore(thoiGianYLenh, "day") // CHỈ so sánh theo ngày
      );
    } else {
      return d > thoiGianCoKetQua;
    }
  };

  const disabledTime = (current) => {
    if (!current) return {};

    const doiTuong = thongTinNb?.doiTuongKcb;
    const chiDinhTuLoaiDichVu = chiTietPhauThuat?.chiDinhTuLoaiDichVu;
    const thoiGianCoKetQua = moment(chiTietPhauThuat?.thoiGianCoKetQua);
    const thoiGianYLenh = moment(dataToDieuTri?.thoiGianYLenh);

    if (
      ![
        DOI_TUONG_KCB.DIEU_TRI_NGOAI_TRU,
        DOI_TUONG_KCB.DIEU_TRI_NOI_TRU,
        DOI_TUONG_KCB.DIEU_TRI_NOI_TRU_BAN_NGAY,
      ].includes(doiTuong) ||
      chiDinhTuLoaiDichVu !== LOAI_DICH_VU.TO_DIEU_TRI
    ) {
      return {};
    }

    const isSameMin = current.isSame(thoiGianYLenh, "day");
    const isSameMax = current.isSame(thoiGianCoKetQua, "day");

    const disabledHours = () => {
      const hours = [];
      for (let i = 0; i < 24; i++) {
        if (
          (isSameMin && i < thoiGianYLenh.hour()) ||
          (isSameMax && i > thoiGianCoKetQua.hour())
        ) {
          hours.push(i);
        }
      }
      return hours;
    };

    const disabledMinutes = (selectedHour) => {
      const minutes = [];
      if (isSameMin && selectedHour === thoiGianYLenh.hour()) {
        for (let i = 0; i < thoiGianYLenh.minute(); i++) {
          minutes.push(i);
        }
      }
      if (isSameMax && selectedHour === thoiGianCoKetQua.hour()) {
        for (let i = thoiGianCoKetQua.minute() + 1; i < 60; i++) {
          minutes.push(i);
        }
      }
      return minutes;
    };

    const disabledSeconds = (selectedHour, selectedMinute) => {
      const seconds = [];
      if (
        isSameMin &&
        selectedHour === thoiGianYLenh.hour() &&
        selectedMinute === thoiGianYLenh.minute()
      ) {
        for (let i = 0; i < thoiGianYLenh.second(); i++) {
          seconds.push(i);
        }
      }
      if (
        isSameMax &&
        selectedHour === thoiGianCoKetQua.hour() &&
        selectedMinute === thoiGianCoKetQua.minute()
      ) {
        for (let i = thoiGianCoKetQua.second() + 1; i < 60; i++) {
          seconds.push(i);
        }
      }
      return seconds;
    };

    return {
      disabledHours,
      disabledMinutes,
      disabledSeconds,
    };
  };

  const listAllPhanLoaiPTTT = useMemo(() => {
    return listPhanLoaiPTTT.map((item) => ({
      ...item,
      id: item?.id == 0 ? item?.id + "" : item?.id,
    }));
  }, [listPhanLoaiPTTT]);

  const onChiDinhGoiMo = () => {
    refChiDinhGoiMo.current &&
      refChiDinhGoiMo.current.show(
        {
          chiDinhMoPhauThuat: true,
          nbDotDieuTriId: chiTietPhauThuat.nbDotDieuTriId,
          ignoreselectedDv: true,
        },
        (data) => {
          const { selectedDv, selectedGoi } = data || {};
          let payload = null;
          if (chiTietPhauThuat?.nbGoiPtTtId) {
            payload = [
              {
                id: chiTietPhauThuat?.nbGoiPtTtId,
                nbDichVu: { dichVuId: selectedGoi?.dichVu?.id },
              },
            ];
          } else {
            payload = {
              nbDotDieuTriId: chiTietPhauThuat?.nbDotDieuTriId,
              nbDichVu: {
                dichVuId: selectedGoi?.dichVu?.id,
                chiDinhTuDichVuId: chiTietPhauThuat?.chiDinhTuDichVuId,
                chiDinhTuLoaiDichVu: chiTietPhauThuat?.chiDinhTuLoaiDichVu,
                thoiGianThucHien:
                  chiTietPhauThuat?.thoiGianThucHien &&
                  moment(chiTietPhauThuat.thoiGianThucHien).format(
                    "YYYY-MM-DD HH:mm:ss"
                  ),
              },
            };
          }
          createOrUpdateGoiDv(payload).then(() => {
            let payload = {
              id: id,
              nbDichVu: {
                dichVuId: selectedDv?.dichVuId,
              },
            };
            themThongTin(payload).then(() => {
              getById(chiTietPhauThuat.id, { refreshOnly: true });
            });
          });
        }
      );
  };

  const onDeleteGoiDv = () => {
    showConfirm(
      {
        title: t("common.thongBao"),
        content: t("pttt.banCoChacMuonXoaGoiMo"),
        cancelText: t("common.huy"),
        okText: t("common.dongY"),
        classNameOkText: "button-warning",
        showBtnOk: true,
        typeModal: "error",
      },
      () => {
        xoaNbGoiPTTT({
          id: chiTietPhauThuat?.nbGoiPtTtId,
          xoaDvTrongGoi: false,
        }).then(() => {
          getById(chiTietPhauThuat.id);
        });
      }
    );
  };

  const listAllLoaiPttt = useMemo(() => {
    if (listLoaiPtTt?.length) {
      const anLoaiPttt = AN_LOAI_PTTT.split(",");
      return listLoaiPtTt.filter((item) => !anLoaiPttt.includes(item.id + ""));
    }
  }, [listLoaiPtTt, AN_LOAI_PTTT]);

  useEffect(() => {
    if (chiTietPhauThuat.id) {
      refDsLuocDo.current = chiTietPhauThuat.dsLuocDo || [];
      setDsLuocDo(chiTietPhauThuat.dsLuocDo || []);
    }
  }, [chiTietPhauThuat?.id, chiTietPhauThuat?.dsLuocDo]);

  useEffect(() => {
    if (chiTietPhauThuat.dichVuId) {
      getListDataTongHop({
        page: "",
        size: "",
        active: true,
        dichVuId: chiTietPhauThuat.dichVuId,
      }).then((s) => {
        if (s?.length === 1) {
          updateThongTinPTTT({ dsMaPtTtId: [s[0].id] });
        }
      });
    }
  }, [chiTietPhauThuat.dichVuId]);

  const isCheckNhomDvCap1 = useMemo(
    () =>
      listAllNhomDichVuCap1.find(
        (x) => x.id === chiTietPhauThuat.nhomDichVuCap1Id
      )?.ma === MA_NHOM_DICH_VU_CAP1_PT,
    [listAllNhomDichVuCap1, chiTietPhauThuat, MA_NHOM_DICH_VU_CAP1_PT]
  );

  const onThemMauKetQua = () => {
    refModalThemMauKetQua &&
      refModalThemMauKetQua.current.show(chiTietPhauThuat, (data) => {
        setState({ mauKetQuaPTTT: data.ten });
        getListAllMauKetQuaPTTT({
          active: true,
          page: "",
          size: "",
          loaiDichVu: LOAI_DICH_VU.PHAU_THUAT_THU_THUAT,
          isForceCall: true,
          dsBacSiChiDinhId: nhanVienId,
          dsKhoaId: khoaLamViecId,
        });
      });
  };

  const renderFilter = () => {
    const spanColTime = dataHIEN_THI_CAC_TRUONG_CHO_PT_MAT?.eval() ? 6 : 4;

    return (
      <>
        {/* Thông tin dịch vụ */}
        <Row>
          <Col md={3} xl={3} xxl={3}>
            <div className="item-select">
              <label className="label-filter">
                {t("pttt.maDichVuPTTT")} <span style={{ color: "red" }}>*</span>
              </label>

              {addParam?.khoaChiDinhId ? (
                <SelectLoadMore
                  api={dichVuKyThuatProvider.searchAll}
                  mapData={mapDataMa}
                  addParam={addParam}
                  onChange={onChange("dichVuId", "ma")}
                  value={chiTietPhauThuat.dichVuId}
                  addValue={addValueMa}
                  keySearch={"ma"}
                  placeholder={t("pttt.chonMaDichVu")}
                  className="input-filter"
                  disabled={disableDichVu}
                />
              ) : (
                <Input
                  value={chiTietPhauThuat?.maDichVu}
                  className="input-filter"
                  disabled
                />
              )}
            </div>
          </Col>
          <Col
            md={chiTietPhauThuat?.phauThuat ? 9 : 15}
            xl={chiTietPhauThuat?.phauThuat ? 9 : 15}
            xxl={chiTietPhauThuat?.phauThuat ? 9 : 15}
          >
            <div className="item-select p-relative">
              <label
                className={`label-filter ${
                  chiTietPhauThuat?.dichVuId ? "" : "error"
                } `}
              >
                {t("pttt.tenDichVuPTTT")}{" "}
                <span style={{ color: "red" }}>*</span>
              </label>
              {addParam?.khoaChiDinhId ? (
                <SelectLoadMore
                  api={dichVuKyThuatProvider.searchAll}
                  mapData={mapData}
                  addParam={addParam}
                  onChange={onChange("dichVuId", "ten")}
                  value={chiTietPhauThuat.dichVuId}
                  addValue={addValue}
                  keySearch={"ten"}
                  placeholder={t("pttt.chonDichVu")}
                  className="input-filter"
                  disabled={disableDichVu}
                />
              ) : (
                <Input
                  value={chiTietPhauThuat?.tenDichVu}
                  className="input-filter"
                  disabled
                />
              )}
            </div>
          </Col>
          {chiTietPhauThuat?.phauThuat && (
            <Col md={3} xl={3} xxl={3}>
              <div className="item-select">
                <label className="label-filter">
                  {t("pttt.ptttCungCaKip")}
                </label>
                <Select
                  data={listAllPtttCungCaKip}
                  value={chiTietPhauThuat?.ptTtCungKipId}
                  className="input-filter"
                  onChange={onChange("ptTtCungKipId")}
                  getLabel={(item) => item?.tenDichVu}
                />
              </div>
            </Col>
          )}
          {chiTietPhauThuat?.phauThuat && (
            <Col md={3} xl={3} xxl={3}>
              <div className="item-select">
                <label
                  className={`label-filter ${
                    !chiTietPhauThuat?.buongPtTtId &&
                    BAT_BUOC_NHAP_BUONG_PT.toLowerCase() !== "false"
                      ? "error"
                      : ""
                  } `}
                >
                  {t("pttt.buongPt")} <span style={{ color: "red" }}>*</span>
                </label>
                <Select
                  data={listAllPhong}
                  value={chiTietPhauThuat?.buongPtTtId}
                  className="input-filter"
                  onChange={onChange("buongPtTtId")}
                  disabled={!!state?.dataDichVuKho.find((x) => x.soPhieuLinh)}
                />
              </div>
            </Col>
          )}
          <Col md={3} xl={3} xxl={3}>
            <div className="item-select">
              <label className="label-filter">{t("pttt.thoiGianPttt")}</label>
              <Select
                value={chiTietPhauThuat?.loaiThoiGian}
                className="input-filter"
                placeholder={t("pttt.thoiGianPttt")}
                data={listLoaiThoiGianHanhChinh}
                onChange={onChange("loaiThoiGian")}
              />
            </div>
          </Col>
          <Col md={3} xl={3} xxl={3}>
            <div className="item-select">
              <label className="label-filter">{t("pttt.trangThai")}</label>
              <Select
                value={chiTietPhauThuat?.trangThai}
                className="input-filter"
                placeholder={t("pttt.trangThai")}
                data={listTrangThaiDichVu}
                disabled
              />
            </div>
          </Col>
        </Row>

        {/* Gói mổ  */}
        <Row>
          {chiTietPhauThuat?.phauThuat &&
            HIEN_THI_GOI_MO_TRONG_PTTT.toLowerCase() === "true" && (
              <Col md={3} xl={3} xxl={3}>
                <div className="item-select">
                  <label className="label-filter">{t("pttt.maGoiMo")}</label>
                  <Input
                    value={chiTietPhauThuat?.maGoiPtTt}
                    className="input-filter"
                    disabled
                  />
                </div>
              </Col>
            )}
          {chiTietPhauThuat?.phauThuat &&
            HIEN_THI_GOI_MO_TRONG_PTTT.toLowerCase() === "true" && (
              <Col md={17} xl={17} xxl={17}>
                <div className="item-select p-relative">
                  <label className="label-filter">
                    {t("pttt.tenGoiMo")}{" "}
                    <SVG.IcDelete
                      style={{ marginBottom: "-6px", cursor: "pointer" }}
                      onClick={onDeleteGoiDv}
                    />
                  </label>
                  <Input
                    value={chiTietPhauThuat?.tenGoiPtTt}
                    className="input-filter"
                    onClick={onChiDinhGoiMo}
                  />
                </div>
              </Col>
            )}
          {chiTietPhauThuat?.phauThuat &&
            HIEN_THI_GOI_MO_TRONG_PTTT.toLowerCase() === "true" && (
              <Col md={4} xl={4} xxl={4}>
                <div className="item-select">
                  <label className="label-filter">
                    {t("pttt.thanhTienGoiMo")}
                  </label>
                  <Input
                    value={chiTietPhauThuat?.thanhTienGoiPtTt}
                    className="input-filter"
                    disabled
                  />
                </div>
              </Col>
            )}
        </Row>

        {/* Tỷ lệ thanh toán */}
        <Row>
          <Col md={2} xl={2} xxl={2}>
            <div className="item-select">
              <label
                className={`label-filter ${
                  chiTietPhauThuat?.tyLeTtDv ? "" : "error"
                } `}
              >
                {t("pttt.tyLeThanhToan")}(%){" "}
                <span style={{ color: "red" }}>*</span>
              </label>
              <Select
                value={chiTietPhauThuat?.tyLeTtDv}
                className="input-filter"
                placeholder={t("pttt.tyLeThanhToan")}
                data={TY_LE_THANH_TOAN}
                onChange={onChange("tyLeTtDv")}
                disabled={disabledAll}
              />
            </div>
          </Col>
          <Col md={3} xl={3} xxl={3}>
            <div className="item-select">
              <label className="label-filter">{t("pttt.giaKhongBH")}</label>
              <Input
                disabled
                value={(chiTietPhauThuat?.giaKhongBaoHiem || 0)?.formatPrice()}
                className="input-filter"
              />
            </div>
          </Col>
          <Col md={3} xl={3} xxl={3}>
            <div className="item-select">
              <label className="label-filter">{t("pttt.thanhTien")}</label>
              <Input
                disabled
                value={(chiTietPhauThuat?.thanhTien || 0)?.formatPrice()}
                className="input-filter"
              />
            </div>
          </Col>
          <Col md={5} xl={5} xxl={5}>
            <div className="item-select">
              <label className="label-filter">{t("pttt.khoaChiDinh")}</label>
              <Select
                disabled
                className="input-filter"
                value={chiTietPhauThuat.khoaChiDinhId}
                data={listAllKhoa}
              />
            </div>
          </Col>
          {!["pstw"].includes(env.HOSPITAL) && (
            <Col md={5} xl={5} xxl={5}>
              <div className="item-select">
                <label className="label-filter">{t("pttt.nguoiTaoPttt")}</label>
                <Select
                  disabled
                  className="input-filter"
                  placeholder={t("pttt.chonNguoiTaoPttt")}
                  value={chiTietPhauThuat?.bacSiChiDinhId}
                  data={listBacSi}
                />
              </div>
            </Col>
          )}
          <Col
            md={chiTietPhauThuat?.khongThucHien ? 3 : 6}
            xl={chiTietPhauThuat?.khongThucHien ? 3 : 6}
            xxl={chiTietPhauThuat?.khongThucHien ? 3 : 6}
          >
            <div className="item-select">
              <label
                className={`label-filter ${
                  !chiTietPhauThuat?.loaiPtTt &&
                  BAT_BUOC_NHAP_LOAI_PTTT.toLowerCase() === "true"
                    ? "error"
                    : ""
                } `}
              >
                {t("pttt.loaiPttt")}
                <span style={{ color: "red" }}> *</span>
              </label>
              <Select
                data={listAllLoaiPttt}
                value={chiTietPhauThuat?.loaiPtTt}
                className="input-filter"
                onChange={onChangeLoaiPttt}
                placeholder={t("danhMuc.chonLoaiPttt")}
              />
            </div>
          </Col>
          {chiTietPhauThuat?.khongThucHien && (
            <Col md={3} xl={3} xxl={3}>
              <div className="item-select checkbox-pl f-end">
                <Checkbox checked={chiTietPhauThuat?.khongThucHien} />
                <label style={{ marginLeft: "4px" }}>
                  {t("pttt.khongPhauThuat")}
                </label>
              </div>
            </Col>
          )}
        </Row>

        {/* Thời gian tiếp nhận */}
        <Row>
          <Col md={spanColTime} xl={spanColTime} xxl={spanColTime}>
            <div className="item-select">
              <label
                className={`label-filter ${
                  chiTietPhauThuat?.thoiGianTiepNhan ? "" : "error"
                } `}
              >
                {t("pttt.thoiGianBatDauPttt")}
                <span style={{ color: "red" }}> *</span>
              </label>
              <DateTimePicker
                format={"DD/MM/YYYY HH:mm:ss"}
                className="input-filter"
                placeholder={t("pttt.chonThoiGian")}
                showTime
                value={
                  chiTietPhauThuat?.thoiGianTiepNhan
                    ? moment(chiTietPhauThuat?.thoiGianTiepNhan)
                    : null
                }
                disabledDate={onDisabledDate}
                disabledTimeDate={disabledTime}
                onChange={onChange("thoiGianTiepNhan")}
                // disabled={disabledAll}
              />
            </div>
          </Col>
          <Col md={spanColTime} xl={spanColTime} xxl={spanColTime}>
            <div className="item-select">
              <label className="label-filter">
                {t("pttt.thoiGianKetThucPttt")}
              </label>
              <DateTimePicker
                format={"DD/MM/YYYY HH:mm:ss"}
                className="input-filter"
                placeholder={t("pttt.chonThoiGian")}
                showTime
                disabledDate={(d) =>
                  d < moment(chiTietPhauThuat.thoiGianThucHien)
                }
                value={
                  chiTietPhauThuat?.thoiGianCoKetQua
                    ? moment(chiTietPhauThuat?.thoiGianCoKetQua)
                    : null
                }
                onChange={onChange("thoiGianCoKetQua")}
                disabled={disabledAll}
              />
            </div>
          </Col>
          {dataHIEN_THI_CAC_TRUONG_CHO_PT_MAT?.eval() && (
            <>
              <Col md={6} xl={6} xxl={6}>
                <div className="item-select">
                  <label className="label-filter">
                    {t("pttt.gioBanGiaoTruocPt")}
                  </label>
                  <DateTimePicker
                    format={"DD/MM/YYYY HH:mm:ss"}
                    className="input-filter"
                    placeholder={t("pttt.chonThoiGian")}
                    showTime
                    disabledDate={(d) => {
                      const tiepNhan = chiTietPhauThuat?.thoiGianTiepNhan;
                      return tiepNhan
                        ? d < moment(tiepNhan).subtract(15, "minutes")
                        : false;
                    }}
                    value={
                      chiTietPhauThuat?.thoiGianBanGiaoTruoc
                        ? moment(chiTietPhauThuat?.thoiGianBanGiaoTruoc)
                        : null
                    }
                    onChange={onChange("thoiGianBanGiaoTruoc")}
                    disabled={disabledThoiGian}
                  />
                </div>
              </Col>
              <Col md={6} xl={6} xxl={6}>
                <div className="item-select">
                  <label className="label-filter">
                    {t("pttt.gioBanGiaoSauPt")}
                  </label>
                  <DateTimePicker
                    format={"DD/MM/YYYY HH:mm:ss"}
                    className="input-filter"
                    placeholder={t("pttt.chonThoiGian")}
                    showTime
                    disabledDate={(d) =>
                      d < moment(chiTietPhauThuat.thoiGianCoKetQua)
                    }
                    value={
                      chiTietPhauThuat?.thoiGianBanGiaoSau
                        ? moment(chiTietPhauThuat?.thoiGianBanGiaoSau)
                        : null
                    }
                    onChange={onChange("thoiGianBanGiaoSau")}
                    disabled={disabledThoiGian}
                  />
                </div>
              </Col>
            </>
          )}

          <Col md={3} xl={3} xxl={3}>
            <div className="item-select">
              <label className="label-filter">
                {t("pttt.phanLoaiPTTT")} <span style={{ color: "red" }}>*</span>
              </label>
              <Select
                value={
                  chiTietPhauThuat?.phanLoai == 0
                    ? chiTietPhauThuat?.phanLoai + ""
                    : chiTietPhauThuat?.phanLoai
                }
                className="input-filter"
                placeholder={t("pttt.chonLoaiPttt")}
                data={listAllPhanLoaiPTTT}
                onChange={onChange("phanLoai")}
                disabled={
                  disabledAll ||
                  !checkRole([ROLES["PHAU_THUAT_THU_THUAT"].SUA_PHAN_LOAI_PTTT])
                }
              />
            </div>
          </Col>
          <Col md={7} xl={7} xxl={7}>
            <div className="item-select">
              <label className={"label-filter"}>
                {t("pttt.phuongPhapVoCam")}
                {isCheckNhomDvCap1 &&
                  XML130_TRUONG_BAT_BUOC.toLowerCase() === "true" &&
                  !chiTietPhauThuat.phuongPhapVoCamId && (
                    <span style={{ color: "red" }}>*</span>
                  )}
              </label>
              <Select
                value={chiTietPhauThuat.phuongPhapVoCamId}
                className="input-filter"
                placeholder={t("pttt.chonPhuongPhapVoCam")}
                data={listAllPhuongPhapVoCam}
                onChange={onChange("phuongPhapVoCamId")}
                disabled={disabledAll}
              />
            </div>
          </Col>
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">{t("pttt.mauKetQua")}</label>
              <Select
                className="input-filter"
                placeholder={t("pttt.chonMauKetQua")}
                data={listAllMauKetQuaPTTT}
                onChange={onChangeMauKetQua}
                disabled={disabledAll}
                value={state.mauKetQuaPTTT}
                dropdownRender={(menu) => (
                  <>
                    {menu}
                    <Divider style={{ margin: "8px 0" }} />
                    <AddButtonStyled>
                      <Button block type="primary" onClick={onThemMauKetQua}>
                        {t("pttt.themNhanhMauKetQua")}
                      </Button>
                    </AddButtonStyled>
                  </>
                )}
              />
            </div>
          </Col>
        </Row>

        {/* Chẩn đoán trước PTTT */}
        <Row>
          <Col md={12} xl={12} xxl={12}>
            <Row>
              {!["pstw"].includes(env.HOSPITAL) && (
                <>
                  <Col md={24} xl={24} xxl={24}>
                    <div className="item-select">
                      <label className="label-filter">
                        {t("pttt.chanDoanChinhTruocPttt")}
                      </label>
                      <SelectChanDoan
                        value={(chiTietPhauThuat?.dsCdChinhId || []).map(
                          (item) => item + ""
                        )}
                        style={{
                          width: "100%",
                        }}
                        mode="multiple"
                        onChange={
                          chiTietPhauThuat.trangThai ===
                            TRANG_THAI_DICH_VU.DA_TIEP_NHAN &&
                          onChange("dsCdChinhId")
                        }
                        maxItem={1}
                        allowClear={true}
                      />
                    </div>
                  </Col>
                  <Col md={24} xl={24} xxl={24}>
                    <div className="item-select">
                      <label className="label-filter">
                        {t("pttt.chanDoanKemTheoTruocPttt")}
                      </label>
                      <SelectChanDoan
                        value={(chiTietPhauThuat?.dsCdKemTheoId || []).map(
                          (item) => item + ""
                        )}
                        style={{
                          width: "100%",
                        }}
                        mode="multiple"
                        onChange={
                          chiTietPhauThuat.trangThai ===
                            TRANG_THAI_DICH_VU.DA_TIEP_NHAN &&
                          onChange("dsCdKemTheoId")
                        }
                        allowClear={true}
                      />
                    </div>
                  </Col>
                </>
              )}
              <Col md={24} xl={24} xxl={24}>
                <div className="item-select">
                  <label className="label-filter">
                    {t("pttt.chanDoanMoTaChiTietTruocPttt")}
                  </label>
                  <InputTimeout
                    rows={1}
                    className="input-filter"
                    placeholder={t("danhMuc.nhapMoTa")}
                    value={chiTietPhauThuat?.moTa}
                    onChange={onChange("moTa")}
                    disabled={
                      chiTietPhauThuat.trangThai !==
                      TRANG_THAI_DICH_VU.DA_TIEP_NHAN
                    }
                    isTextArea
                  />
                </div>
              </Col>
              {["pstw"].includes(env.HOSPITAL) && (
                <Col span={24}>
                  <div className="item-select">
                    <label className="label-filter">
                      {t("pttt.phuongPhapPttt")}
                    </label>
                    <InputTimeout
                      rows={3}
                      className="input-filter"
                      placeholder={t("pttt.nhapPhuongPhapPhauThuat")}
                      value={chiTietPhauThuat?.phuongPhap}
                      onChange={onChange("phuongPhap")}
                      disabled={disabledAll}
                      isTextArea
                    />
                  </div>
                </Col>
              )}
              <Col md={24} xl={24} xxl={24}>
                <div className="item-select">
                  <label className="label-filter">
                    {t("pttt.benhPhamGuiGiaiPhauBenh")}
                  </label>
                  <Checkbox
                    checked={chiTietPhauThuat?.benhPhamGiaiPhauBenh}
                    disabled={true}
                  />
                </div>
              </Col>
            </Row>
          </Col>
          <Col md={12} xl={12} xxl={12}>
            <Row>
              <Col md={12} xl={12} xxl={12}>
                <div className="item-select">
                  <label className={`label-filter`}>
                    {t("pttt.thoiGianBatDauPpVoCam")}
                  </label>
                  <DateTimePicker
                    format={"DD/MM/YYYY HH:mm:ss"}
                    className="input-filter"
                    placeholder={t("pttt.chonThoiGian")}
                    showTime
                    value={
                      chiTietPhauThuat?.thoiGianBatDauVoCam
                        ? moment(chiTietPhauThuat?.thoiGianBatDauVoCam)
                        : null
                    }
                    onChange={onChange("thoiGianBatDauVoCam")}
                  />
                </div>
              </Col>
              <Col md={12} xl={12} xxl={12}>
                <div className="item-select">
                  <label className="label-filter">
                    {t("pttt.thoiGianKetThucPpVoCam")}
                  </label>
                  <DateTimePicker
                    format={"DD/MM/YYYY HH:mm:ss"}
                    className="input-filter"
                    placeholder={t("pttt.chonThoiGian")}
                    showTime
                    value={
                      chiTietPhauThuat?.thoiGianKetThucVoCam
                        ? moment(chiTietPhauThuat?.thoiGianKetThucVoCam)
                        : null
                    }
                    onChange={onChange("thoiGianKetThucVoCam")}
                  />
                </div>
              </Col>
            </Row>
            <Row>
              <Col span={24} className="item-select">
                <label className={"label-filter"}>
                  {t("pttt.chanDoanSauPttt")}{" "}
                  {chiTietPhauThuat?.phauThuat &&
                    dataBAT_BUOC_NHAP_CHAN_DOAN_SAU_PT?.eval() && (
                      <span style={{ color: "red" }}>*</span>
                    )}
                </label>
                <InputTimeout
                  rows={5}
                  className="input-filter"
                  placeholder={t("pttt.nhapChanDoanSauPhauThuat")}
                  value={chiTietPhauThuat?.chanDoan}
                  onChange={onChange("chanDoan")}
                  disabled={disabledAll}
                  isTextArea
                />
              </Col>
            </Row>
          </Col>
        </Row>

        {/* Chẩn đoán sau PTTT */}
        {!["pstw"].includes(env.HOSPITAL) && (
          <Row>
            <Col md={12} xl={12} xxl={12}>
              <div className="item-select">
                <label className="label-filter">
                  {t("pttt.chanDoanSauPttt")} (ICD 10)
                </label>
                <SelectChanDoan
                  value={(chiTietPhauThuat?.dsCdSauPtId || []).map(
                    (item) => item + ""
                  )}
                  style={{
                    width: "100%",
                  }}
                  mode="multiple"
                  maxItem={1}
                  onChange={onChange("dsCdSauPtId")}
                  allowClear={true}
                />
              </div>
            </Col>
            <Col md={12} xl={12} xxl={12}>
              <div className="item-select">
                <label className="label-filter">
                  {t("common.maPtttQuocTe")}
                </label>
                <Select
                  className="input-filter"
                  placeholder={t("common.chonMaPtttQuocTe")}
                  data={listMaPtttQuocTe}
                  value={chiTietPhauThuat.dsMaPtTtId}
                  onChange={onChange("dsMaPtTtId")}
                  mode="multiple"
                />
              </div>
            </Col>
          </Row>
        )}

        {/* Phương pháp PTTT */}
        {!["pstw"].includes(env.HOSPITAL) && (
          <Row>
            <Col md={12} xl={12} xxl={12}>
              <div className="item-select">
                <label className="label-filter">
                  {t("pttt.phuongPhapPttt")}
                </label>
                <InputTimeout
                  rows={3}
                  className="input-filter"
                  placeholder={t("pttt.nhapPhuongPhapPhauThuat")}
                  value={chiTietPhauThuat?.phuongPhap}
                  onChange={onChange("phuongPhap")}
                  disabled={disabledAll}
                  isTextArea
                />
              </div>
            </Col>

            <Col md={12} xl={12} xxl={12}>
              <div className="item-select">
                <label className="label-filter">{t("pttt.ketLuan")}</label>
                <InputTimeout
                  rows={3}
                  className="input-filter"
                  placeholder={t("pttt.nhapKetLuan")}
                  value={chiTietPhauThuat?.ketLuan}
                  onChange={onChange("ketLuan")}
                  disabled={disabledAll}
                  isTextArea
                />
              </div>
            </Col>
          </Row>
        )}

        {/* Ảnh lược đồ */}
        <Row>
          <Col md={10} xl={10} xxl={10}>
            <div className="item-select h-100">
              <label className="label-filter">
                <div style={{ display: "flex", alignItems: "center" }}>
                  <span style={{ marginRight: 10 }}>
                    {t("pttt.luocDoPttt")}
                  </span>

                  <Tooltip title={t("pttt.chonAnhLuocDoPhauThuatThuThuat")}>
                    <SVG.IcImage
                      style={{ marginRight: 10 }}
                      color={"var(--color-blue-primary)"}
                      onClick={() => {
                        refUpload.current?.onSelectAnhLuocDo();
                      }}
                    />
                  </Tooltip>

                  <Tooltip title={t("pttt.nhanDeTaiAnhLuocDoPttt")}>
                    <SVG.IcCamera
                      color={"var(--color-blue-primary)"}
                      onClick={() => {
                        refUpload.current?.openSelectFile();
                      }}
                    />
                  </Tooltip>
                </div>
              </label>
              <div className="input-filter cal-height">
                <div className="image-list-scroll">
                  {(dsLuocDo || []).map((item, index) => (
                    <div
                      key={index}
                      className={`contain-img ${
                        chiTietPhauThuat.phauThuat ? "img-pt" : "img-tt"
                      }`}
                      disabled={disabledAll}
                    >
                      <ImageEdit
                        typeApi="ptttLuocDo"
                        src={item}
                        afterSave={(s) => {
                          let _dsLuocDo;

                          //dùng ref cho case chọn multi file
                          if (!s) {
                            _dsLuocDo = (refDsLuocDo.current || []).filter(
                              (_, i) => i !== index
                            );
                          } else {
                            _dsLuocDo = [...(refDsLuocDo.current || [])];
                            _dsLuocDo[index] = s;
                          }

                          onChange("dsLuocDo")(_dsLuocDo);
                        }}
                        showChonAnhLuocDo={true}
                        placeholder={t("pttt.nhanDeTaiAnhLuocDoPttt")}
                        dichVuId={chiTietPhauThuat?.dichVuId}
                      />
                    </div>
                  ))}

                  <div
                    className={`contain-img ${
                      chiTietPhauThuat.phauThuat ? "img-pt" : "img-tt"
                    } bg-gray`}
                    disabled={disabledAll}
                  >
                    <ImageEdit
                      ref={refUpload}
                      key={dsLuocDo.length}
                      typeApi="ptttLuocDo"
                      src={null}
                      multiple={true}
                      afterSave={(s) => {
                        let _dsLuocDo;

                        //dùng ref cho case chọn multi file
                        if (s) {
                          _dsLuocDo = [...(refDsLuocDo.current || []), s];
                        } else {
                          _dsLuocDo = refDsLuocDo.current || [];
                        }

                        onChange("dsLuocDo")(_dsLuocDo);
                      }}
                      showChonAnhLuocDo={true}
                      placeholder={t("pttt.nhanDeTaiAnhLuocDoPttt")}
                      dichVuId={chiTietPhauThuat?.dichVuId}
                      tenDichVu={chiTietPhauThuat?.tenDichVu}
                    />
                  </div>
                </div>
                {(HIEN_THI_DAN_LUU_CHI_TIET_PTTT?.eval() ||
                  !chiTietPhauThuat.phauThuat) && (
                  <div className="info-image">
                    <div className="item-child">
                      <span>{t("pttt.danLuu")}:</span>
                      <span>
                        <InputTimeout
                          placeholder={t("pttt.nhapDanLuu")}
                          value={chiTietPhauThuat?.danLuu}
                          onChange={onChange("danLuu")}
                          disabled={disabledAll}
                          isTextArea
                        />
                      </span>
                    </div>
                    <div className="item-child">
                      <span>{t("pttt.bac")}:</span>
                      <span>
                        <InputTimeout
                          placeholder={t("pttt.nhapBac")}
                          value={chiTietPhauThuat?.bac}
                          onChange={onChange("bac")}
                          disabled={disabledAll}
                          isTextArea
                        />
                      </span>
                    </div>
                    <div className="item-child">
                      <span>{t("pttt.ngayRut")}:</span>
                      <span>
                        <DateTimePicker
                          placeholder={t("pttt.chonNgay")}
                          showTime
                          format={"DD/MM/YYYY HH:mm"}
                          onChange={onChange("thoiGianRut")}
                          value={
                            chiTietPhauThuat?.thoiGianRut
                              ? moment(chiTietPhauThuat?.thoiGianRut)
                              : null
                          }
                          disabled={disabledAll}
                        />
                      </span>
                    </div>
                    <div className="item-child">
                      <span>{t("pttt.ngayCatChi")}:</span>
                      <span>
                        <DateTimePicker
                          placeholder={t("pttt.chonNgay")}
                          showTime
                          format={"DD/MM/YYYY HH:mm"}
                          onChange={onChange("thoiGianCatChi")}
                          value={
                            chiTietPhauThuat?.thoiGianCatChi
                              ? moment(chiTietPhauThuat?.thoiGianCatChi)
                              : null
                          }
                          disabled={disabledAll}
                        />
                      </span>
                    </div>
                    <div className="item-child">
                      <span>{t("pttt.khac")}:</span>
                      <span>
                        <InputTimeout
                          placeholder={t("pttt.nhapKhac")}
                          value={chiTietPhauThuat?.khac}
                          onChange={onChange("khac")}
                          disabled={disabledAll}
                          isTextArea
                        />
                      </span>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </Col>
          <Col md={14} xl={14} xxl={14}>
            <div className="item-select">
              <label className="label-filter">{t("pttt.cachThucPttt")}</label>
              {state.selectedProtocolId ? (
                <div
                  className="input-filter"
                  style={{
                    border: "1px solid #d9d9d9",
                    borderRadius: "6px",
                    padding: "8px",
                    minHeight: "400px",
                  }}
                >
                  <ProtocolForm
                    form={protocolForm}
                    protocolId={state.selectedProtocolId}
                    currentData={chiTietPhauThuat?.dsProtocol || []}
                    disabled={disabledAll}
                    onValuesChange={(values, allValues, protocolData) => {
                      updateThongTinPTTT({ dsProtocol: protocolData });
                    }}
                  />
                </div>
              ) : (
                <InputTimeout
                  rows={20}
                  className="input-filter"
                  placeholder={t("pttt.nhapCachThucPttt")}
                  value={chiTietPhauThuat?.cachThuc}
                  onChange={onChange("cachThuc")}
                  disabled={disabledAll}
                  isTextArea
                />
              )}
            </div>
          </Col>
        </Row>

        {/* Sử dụng dụng cụ đặc biệt */}
        <Row>
          {!["pstw"].includes(env.HOSPITAL) && (
            <Col md={6} xl={6} xxl={6}>
              <div className="item-select">
                <label className="label-filter">
                  {t("pttt.suDungDungCuDacBiet")}
                </label>
                <Select
                  value={
                    chiTietPhauThuat.suDungDungCuDacBiet === 0
                      ? chiTietPhauThuat.suDungDungCuDacBiet + ""
                      : chiTietPhauThuat.suDungDungCuDacBiet
                  }
                  className="input-filter"
                  placeholder={t("pttt.suDungDungCuDacBiet")}
                  data={listCoKhong.map((item) => ({
                    ...item,
                    id: item?.id == 0 ? item?.id + "" : item?.id,
                  }))}
                  onChange={onChange("suDungDungCuDacBiet")}
                  disabled={disabledAll}
                />
              </div>
            </Col>
          )}
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">
                {t("pttt.mucDoKiemSoatNhiemKhuan")}
              </label>
              <Select
                value={chiTietPhauThuat.kiemSoatNhiemKhuan}
                className="input-filter"
                placeholder={t("pttt.mucDoKiemSoatNhiemKhuan")}
                data={listKiemSoatNhiemKhuan}
                onChange={onChange("kiemSoatNhiemKhuan")}
                disabled={disabledAll}
              />
            </div>
          </Col>
          {!["pstw"].includes(env.HOSPITAL) && (
            <Col md={6} xl={6} xxl={6}>
              <div className="item-select">
                <label className="label-filter">
                  {t("pttt.nhomPhauThuat")}
                </label>
                <Select
                  value={chiTietPhauThuat.dsNhomPhauThuat}
                  className="input-filter"
                  placeholder={t("pttt.nhomPhauThuat")}
                  data={listNhomPhauThuat}
                  onChange={onChange("dsNhomPhauThuat")}
                  disabled={disabledAll}
                  mode="multiple"
                />
              </div>
            </Col>
          )}
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">
                {t("pttt.loaiMo")} <span style={{ color: "red" }}>*</span>
              </label>
              <Select
                value={chiTietPhauThuat.loaiMo}
                className="input-filter"
                placeholder={t("pttt.loaiMo")}
                data={listLoaiMo}
                onChange={onChange("loaiMo")}
                disabled={disabledAll}
              />
            </div>
          </Col>
        </Row>

        {/* Dụng cụ đặc biệt */}
        <Row>
          {!["pstw"].includes(env.HOSPITAL) && (
            <Col md={12} xl={12} xxl={12}>
              <div className="item-select">
                <label className="label-filter">
                  {t("pttt.dungCuDacBiet")}
                </label>
                <InputTimeout
                  rows={2}
                  className="input-filter"
                  placeholder={t("pttt.nhapDungCuDacBiet")}
                  value={chiTietPhauThuat?.dungCuDacBiet}
                  onChange={onChange("dungCuDacBiet")}
                  disabled={disabledAll}
                  isTextArea
                  maxLength={2000}
                />
              </div>
            </Col>
          )}
          <Col md={6} xl={6} xxl={6}>
            <div className="item-select">
              <label className="label-filter">{t("pttt.khoaChuyenDen")}</label>
              <Select
                value={chiTietPhauThuat.khoaChuyenDenId}
                className="input-filter"
                placeholder={t("pttt.chonKhoaChuyenDen")}
                disabled={disabledAll}
                data={(listDataKhoaTongHop || []).filter(
                  (x) => x.id !== chiTietPhauThuat?.khoaThucHienId
                )}
                onChange={onChange("khoaChuyenDenId")}
              />
            </div>
          </Col>
        </Row>
        {dataHIEN_THI_CAC_TRUONG_CHO_PT_MAT?.eval() && (
          <Row>
            <Col md={6} xl={6} xxl={6}>
              <div className="item-select">
                <label className="label-filter">
                  {t("pttt.matPhauThuat")}{" "}
                  <span style={{ color: "red" }}>*</span>
                </label>
                <Select
                  value={chiTietPhauThuat.viTriPhauThuat}
                  className="input-filter"
                  placeholder={t("pttt.matPhauThuat")}
                  data={listViTriPhauThuat}
                  onChange={onChange("viTriPhauThuat")}
                />
              </div>
            </Col>
            <Col md={6} xl={6} xxl={6}>
              <div className="item-select">
                <label className="label-filter">
                  {t("pttt.phuongPhapPtttMatTrai")}
                </label>
                <InputTimeout
                  rows={1}
                  className="input-filter"
                  placeholder={t("pttt.phuongPhapPtttMatTrai")}
                  value={chiTietPhauThuat?.boSung?.phuongPhapPtTtMatTrai}
                  onChange={onChange("boSung", "phuongPhapPtTtMatTrai")}
                  disabled={disabledAll}
                  isTextArea
                />
              </div>
            </Col>
            <Col md={6} xl={6} xxl={6}>
              <div className="item-select">
                <label className="label-filter">
                  {t("pttt.phuongPhapPtttMatPhai")}
                </label>
                <InputTimeout
                  rows={1}
                  className="input-filter"
                  placeholder={t("pttt.phuongPhapPtttMatPhai")}
                  value={chiTietPhauThuat?.boSung?.phuongPhapPtTtMatPhai}
                  onChange={onChange("boSung", "phuongPhapPtTtMatPhai")}
                  disabled={disabledAll}
                  isTextArea
                />
              </div>
            </Col>
            <Col md={6} xl={6} xxl={6}>
              <div className="item-select">
                <label className="label-filter">{t("common.ghiChu")}</label>
                <InputTimeout
                  rows={1}
                  className="input-filter"
                  placeholder={t("common.ghiChu")}
                  value={chiTietPhauThuat?.ghiChu}
                  onChange={onChange("ghiChu")}
                  disabled={disabledAll}
                  isTextArea
                />
              </div>
            </Col>
          </Row>
        )}
      </>
    );
  };

  return (
    <Main>
      <ElementFilter renderFilter={renderFilter} />
      <ModalChonDvGoiPttt ref={refChiDinhGoiMo} />
      <ModalThemMoiMauKetQua ref={refModalThemMauKetQua} />
    </Main>
  );
};
export default memo(ThongTinPTTT);
