import React, { useRef, useEffect, useState, useMemo } from "react";
import { debounce } from "lodash";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";

import { useEnum, useListAll, useStore } from "hooks";

import { BaseSearch } from "components";
import { ENUM, HOTKEY } from "constants/index";
import { transformQueryString } from "utils/index";
import { LIST_LOAI_DANG_KY_ROLES } from "../../DieuTriPHCN/index";
import { setQueryStringValue } from "hooks/useQueryString/queryString";
import useListDieuTriKetHop, {
  DIEU_TRI_KET_HOP_OPTIONS,
} from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/hooks/useListDieuTriKetHop";

const TimKiemDieuTriPHCN = ({
  layerId,
  isPhcn,
  isYhct,
  isThan<PERSON>han<PERSON>ao,
  isBan<PERSON>han,
  isHoaXaTri,
  isDieuTriKetHop,
  ...props
}) => {
  const { t } = useTranslation();
  const refFocusTenNb = useRef();
  const [listTrangThaiPHCN] = useEnum(ENUM.TRANG_THAI_PHCN);
  const listAllNhanVien = useStore("nhanVien.listAllNhanVien", []);
  const [listAllKhoa] = useListAll("khoa", {}, true);

  const [state, _setState] = useState({});

  const setState = (data) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  const { listAllDieuTriKetHop } = useListDieuTriKetHop({
    manHinh: "PHCN",
    options: DIEU_TRI_KET_HOP_OPTIONS.DANH_SACH_PHCN,
  });

  const {
    phimTat: { onRegisterHotkey },
    nhanVien: { getListAllNhanVien },
    dieuTriPHCN: { searchDieuTriPHCNByParams, clearData },
  } = useDispatch();

  const listLoaiDangKyRolesMemo = useMemo(() => {
    const map = new Map();

    LIST_LOAI_DANG_KY_ROLES.forEach((cur) => {
      if (!cur.condition) return;

      const id = cur.id >= 30 ? 30 : cur.id;
      const ten = cur.id >= 30 ? t("phcn.dieuTriKetHop") : cur.ten;

      if (!map.has(id)) {
        map.set(id, { ...cur, id, ten });
      }
    });

    return Array.from(map.values());
  }, [LIST_LOAI_DANG_KY_ROLES]);

  useEffect(() => {
    onRegisterHotkey({
      layerId,
      hotKeys: [
        {
          keyCode: HOTKEY.F6, //F6
          onEvent: () => {
            refFocusTenNb.current && refFocusTenNb.current.focus();
          },
        },
      ],
    });

    return () => {
      clearData();
    };
  }, []);

  useEffect(() => {
    getListAllNhanVien({ page: "", size: "", active: true });
  }, []);

  useEffect(() => {
    const { page, size, dataSortColumn, ...queries } = transformQueryString({
      dsKhoaId: {
        format: (value) => parseInt(value),
      },
      dsKhoaNbId: {
        format: (value) => Number(value),
      },
      dsTrangThai: {
        format: (value) => value.split(",").map(Number),
        defaultValue: [10, 30],
      },
      loai: {
        format: (value) => parseInt(value),
      },
      loaiPhcnId: {
        format: (value) => parseInt(value),
      },
    });

    setState(queries);
  }, [isPhcn, isYhct, isThanNhanTao, isBanChan, isHoaXaTri]);

  const onSearch = (data) => {
    searchDieuTriPHCNByParams(data);
    setQueryStringValue("page", 0);
  };

  return (
    <BaseSearch
      cacheData={state}
      dataInput={[
        {
          widthInput: "232px",
          placeholder: t("phcn.timTenNbMaHs"),
          keyValueInput: "maHoSo",
          functionChangeInput: onSearch,
          isScanQR: true,
          keysFlexible: [
            {
              key: "tenNb",
              type: "string",
            },
            {
              key: "maHoSo",
              type: "maHoSo",
            },
          ],
        },
        {
          widthInput: "232px",
          placeholder: t("phcn.timMaBa"),
          keyValueInput: "maBenhAn",
          functionChangeInput: onSearch,
        },
        {
          widthInput: "232px",
          keyValueInput: "dsKhoaId",
          placeholder: t("khamBenh.dsBenhNhan.chonKhoaNhapVien"),
          type: "select",
          title: t("phcn.khoaNhapVien"),
          listSelect: listAllKhoa,
          functionChangeInput: onSearch,
        },
        {
          widthInput: "232px",
          keyValueInput: "dsKhoaNbId",
          placeholder: t("dieuTriDaiHan.chonKhoaNB"),
          type: "select",
          title: t("baoCao.khoaNB"),
          listSelect: listAllKhoa,
          functionChangeInput: onSearch,
        },
        {
          widthInput: "232px",
          title: t("common.trangThai"),
          keyValueInput: "dsTrangThai",
          functionChangeInput: debounce(({ dsTrangThai }) => {
            onSearch({
              dsTrangThai,
            });
            setState({ dsTrangThai });
          }, 500),
          type: "selectCheckbox",
          virtual: true,
          hasCheckAll: true,
          defaultValue: state.dsTrangThai,
          listSelect: listTrangThaiPHCN,
        },
      ]}
      filter={{
        open: true,
        width: "110px",
        funcSearchData: (data) => {
          onSearch(data);
          setState(data);
        },
        data: [
          {
            key: "chanDoanBenh",
            placeholder: t("phcn.timTheoCdBenh"),
            defaultValue: state.chanDoanBenh,
            type: "normal",
          },
          {
            key: "dot",
            placeholder: t("phcn.timTheoDot"),
            defaultValue: state.dot,
            type: "normal",
          },
          {
            key: "dsBacSiDieuTriId",
            widthInput: "212px",
            placeholder: t("phcn.timTheoBsDieuTri"),
            dataSelect: listAllNhanVien || [],
            defaultValue: state.dsBacSiDieuTriId,
            type: "select",
          },
          {
            key: "dsBacSiThucHienId",
            widthInput: "212px",
            placeholder: t("phcn.timTheoBsDieuTriPhcn"),
            dataSelect: listAllNhanVien || [],
            defaultValue: state.dsBacSiThucHienId,
            type: "select",
          },
          {
            key: "loai",
            widthInput: "212px",
            placeholder: t("phcn.loaiDangKy"),
            dataSelect: listLoaiDangKyRolesMemo || [],
            allowClear: Boolean(state.loai),
            defaultValue: state.loai,
            type: "select",
          },
          ...(isDieuTriKetHop
            ? [
                {
                  key: "loaiPhcnId",
                  widthInput: "212px",
                  placeholder: t("phcn.loaiDangKyDieuTriKetHop"),
                  dataSelect: listAllDieuTriKetHop || [],
                  defaultValue: state.loaiPhcnId,
                  type: "select",
                },
              ]
            : []),
        ],
      }}
    />
  );
};

export default TimKiemDieuTriPHCN;
