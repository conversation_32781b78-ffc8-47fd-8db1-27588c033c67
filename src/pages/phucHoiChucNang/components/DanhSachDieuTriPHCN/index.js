import React, { useEffect, useRef, useState, useMemo } from "react";
import { Main } from "./styled";
import { Tooltip, TableWrapper, Pagination } from "components";
import { connect } from "react-redux";
import { useTranslation } from "react-i18next";
import { useHistory } from "react-router-dom";
import { useEnum, useListAll } from "hooks";
import { cloneDeep, uniqBy } from "lodash";
import {
  getAllQueryString,
  setQueryStringValue,
  setQueryStringValues,
} from "hooks/useQueryString/queryString";
import moment from "moment";
import { LIST_LOAI_DANG_KY_ROLES } from "../../DieuTriPHCN/index";
import { isArray, transformQueryString } from "utils";
import { ENUM, LOAI_DANG_KY } from "constants/index";
import { SVG } from "assets";
import useListDieuTriKetHop, {
  DIEU_TRI_KET_HOP_OPTIONS,
} from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/hooks/useListDieuTriKetHop";

const { Column, Setting } = TableWrapper;

const DanhSachDieuTriPHCN = ({
  isPhcn,
  isYhct,
  isThanNhanTao,
  isBanChan,
  isHoaXaTri,
  ...props
}) => {
  const { t } = useTranslation();
  const history = useHistory();
  const refSettings = useRef(null);
  const {
    listData,
    page,
    size,
    totalElements,
    dataSortColumn,
    isLoading,

    onSearch,
    onSizeChange,
    onSortChange,
  } = props;

  const [listTrangThaiPHCN] = useEnum(ENUM.TRANG_THAI_PHCN);
  const { listKhoaTheoTaiKhoan, listLoaiPhcnEnums, listAllDieuTriKetHop } =
    useListDieuTriKetHop({
      manHinh: "PHCN",
      options: DIEU_TRI_KET_HOP_OPTIONS.DANH_SACH_PHCN,
    });
  const [listAllPhanLoaiPHCN] = useListAll("phanLoaiPHCN", {}, true);

  const [state, _setState] = useState({
    isCheckedAll: false,
  });
  const setState = (data) => {
    _setState({
      ...state,
      ...data,
    });
  };

  useEffect(() => {
    if (!isArray(listKhoaTheoTaiKhoan, true)) return;
    const {
      page,
      size,
      dataSortColumn = "{}",
      ...queries
    } = transformQueryString({
      dsTrangThai: {
        format: (value) => value.split(",").map(Number),
        defaultValue: [10, 30],
      },
      loai: {
        format: (value) => parseInt(value),
      },
      loaiPhcnId: {
        format: (value) => parseInt(value),
      },
    });
    const sort = JSON.parse(dataSortColumn);

    onSizeChange({
      page: parseInt(page || 0),
      size: parseInt(size || 10),
      dataSearch: {
        ...queries,
        dsKhoaThucHienId: listKhoaTheoTaiKhoan.map((x) => x.id),
      },
      dataSortColumn: sort,
    });
  }, [
    isPhcn,
    isYhct,
    isThanNhanTao,
    isBanChan,
    isHoaXaTri,
    listKhoaTheoTaiKhoan,
  ]);

  const onClickSort = (key, value) => {
    let sort = cloneDeep(dataSortColumn);
    sort[key] = value;
    for (let key in sort) {
      if (!sort[key]) delete sort[key];
    }
    setQueryStringValues({ dataSortColumn: JSON.stringify(sort), page: 0 });
    onSortChange({ [key]: value });
  };

  const isDieuTriKetHop = useMemo(() => {
    return LIST_LOAI_DANG_KY_ROLES.filter((item) => item.condition)?.length > 1;
  }, [LIST_LOAI_DANG_KY_ROLES]);

  const columns = [
    Column({
      title: t("common.stt"),
      width: "50px",
      dataIndex: "index",
      key: "index",
      align: "center",
      ignore: true,
    }),
    Column({
      title: t("phcn.thoiGianDangKy"),
      sort_key: "thoiGianDangKy",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["thoiGianDangKy"] || "",
      width: "160px",
      dataIndex: "thoiGianDangKy",
      i18Name: "phcn.thoiGianDangKy",
      key: "thoiGianDangKy",
      render: (item) =>
        item ? moment(item).format("HH:mm:ss DD/MM/YYYY") : "",
    }),
    Column({
      title: isDieuTriKetHop
        ? t("phcn.loaiDieuTriKetHop")
        : isPhcn
        ? t("phcn.loaiPhcn")
        : isYhct
        ? t("phcn.loaiYhct")
        : isThanNhanTao
        ? t("phcn.loaiThanNhanTao")
        : isBanChan
        ? t("phcn.loaiBanChan")
        : isHoaXaTri
        ? t("phcn.loaiHoaXaTri")
        : "",
      width: 160,
      dataIndex: "dsPhanLoaiPhcnId",
      key: "dsPhanLoaiPhcnId",
      i18Name: isDieuTriKetHop
        ? "phcn.loaiDieuTriKetHop"
        : isPhcn
        ? "phcn.loaiPhcn"
        : isYhct
        ? "phcn.loaiYhct"
        : isThanNhanTao
        ? "phcn.loaiThanNhanTao"
        : isBanChan
        ? "phcn.loaiBanChan"
        : isHoaXaTri
        ? "phcn.loaiHoaXaTri"
        : "",
      render: (item) =>
        (item || [])
          .map(
            (x1) =>
              (listAllPhanLoaiPHCN || []).find((x2) => x2.id == x1)?.ten || ""
          )
          .join(", "),
    }),
    Column({
      title: t("phcn.tenNb"),
      sort_key: "tenNb",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["tenNb"] || "",
      width: "250px",
      dataIndex: "tenNb",
      key: "tenNb",
      i18Name: "phcn.tenNb",
    }),
    Column({
      title: t("common.ngaySinh"),
      sort_key: "ngaySinh",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["ngaySinh"] || "",
      width: "120px",
      dataIndex: "ngaySinh",
      key: "ngaySinh",
      i18Name: "common.ngaySinh",
      render: (value) => value && moment(value).format("DD/MM/YYYY"),
    }),
    Column({
      title: t("common.maHoSo"),
      sort_key: "maHoSo",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["maHoSo"] || "",
      width: "120px",
      dataIndex: "maHoSo",
      key: "maHoSo",
      i18Name: "common.maHoSo",
    }),
    Column({
      title: t("phcn.maBa"),
      width: "120px",
      dataIndex: "maBenhAn",
      key: "maBenhAn",
      i18Name: "phcn.maBa",
    }),
    Column({
      title: t("common.maNb"),
      sort_key: "maNb",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["maNb"] || "",
      width: "140px",
      dataIndex: "maNb",
      key: "maNb",
      i18Name: "common.maNb",
    }),
    Column({
      title: t("phcn.dot"),
      sort_key: "stt",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["stt"] || "",
      width: "60px",
      dataIndex: "stt",
      i18Name: "phcn.dot",
      key: "stt",
    }),
    Column({
      title: t("phcn.giuongPhong"),
      width: "200px",
      key: "giuongPhong",
      i18Name: "phcn.giuongPhong",
      render: (item, list) =>
        `${list?.soHieu ? `${list?.soHieu} - ` : ""} ${list?.tenPhong || ""}`,
    }),
    Column({
      title: t("phcn.khoaNhapVien"),
      width: "200px",
      dataIndex: "tenKhoaNhapVien",
      key: "tenKhoaNhapVien",
      i18Name: "phcn.khoaNhapVien",
    }),
    Column({
      title: t("baoCao.khoaNB"),
      width: "200px",
      dataIndex: "tenKhoaNb",
      key: "tenKhoaNb",
      i18Name: "baoCao.khoaNB",
    }),
    Column({
      title: t("phcn.cdBenh"),
      sort_key: "cdBenh",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["cdBenh"] || "",
      width: "250px",
      dataIndex: "dsCdChinh",
      key: "dsCdChinh",
      i18Name: "phcn.cdBenh",
      render: (item) => (item || []).map((x) => `${x.ma}-${x.ten}`).join(", "),
    }),
    Column({
      title: t("phcn.cdVaoVien"),
      sort_key: "dsCdVaoVien",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["dsCdVaoVien"] || "",
      width: "250px",
      dataIndex: "dsCdVaoVien",
      key: "dsCdVaoVien",
      i18Name: "phcn.cdVaoVien",
      render: (item) => (item || []).map((x) => `${x.ma}-${x.ten}`).join(", "),
    }),

    Column({
      title: t("phcn.bsDieuTri"),
      sort_key: "tenBacSiDieuTri",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["tenBacSiDieuTri"] || "",
      width: "180px",
      dataIndex: "tenBacSiDieuTri",
      i18Name: "phcn.bsDieuTri",
      key: "bsDieuTri",
    }),
    Column({
      title: isDieuTriKetHop
        ? t("phcn.bsDieuTriKetHop")
        : isPhcn
        ? t("phcn.bsDieuTriPhcn")
        : isYhct
        ? t("phcn.bsDieuTriYhct")
        : isThanNhanTao
        ? t("phcn.bsDieuTriThanNhanTao")
        : isBanChan
        ? t("phcn.bsDieuTriBanChan")
        : isHoaXaTri
        ? t("phcn.bsDieuTriHoaXaTri")
        : "",
      sort_key: "tenBacSiThucHien",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["tenBacSiThucHien"] || "",
      width: "180px",
      dataIndex: "tenBacSiThucHien",
      i18Name: isDieuTriKetHop
        ? "phcn.bsDieuTriKetHop"
        : isPhcn
        ? "phcn.bsDieuTriPhcn"
        : isYhct
        ? "phcn.bsDieuTriYhct"
        : isThanNhanTao
        ? "phcn.bsDieuTriThanNhanTao"
        : isBanChan
        ? "phcn.bsDieuTriBanChan"
        : isHoaXaTri
        ? "phcn.bsDieuTriHoaXaTri"
        : "",
      key: "tenBacSiThucHien",
    }),
    Column({
      title: t("phcn.trangThai"),
      sort_key: "trangThai",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["trangThai"] || "",
      width: "140px",
      dataIndex: "trangThai",
      i18Name: "phcn.trangThai",
      key: "trangThai",
      render: (item) =>
        (listTrangThaiPHCN || []).find((x) => x.id == item)?.ten || "",
    }),
    Column({
      title: t("phcn.loaiDangKy"),
      sort_key: "loaiPhcnId",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["loaiPhcnId"] || "",
      width: "120px",
      dataIndex: "loaiPhcnId",
      i18Name: "phcn.loaiDangKy",
      key: "loaiPhcnId",
      render: (item, data) => {
        if (
          data?.loai === LOAI_DANG_KY.PHUC_HOI_CHUC_NANG ||
          data?.loai === LOAI_DANG_KY.Y_HOC_CO_TRUYEN
        ) {
          return listLoaiPhcnEnums.find((x) => x.id == data.loai)?.ten || "";
        }
        return (
          (listAllDieuTriKetHop || []).find((x) => x.id == item)?.ten || ""
        );
      },
    }),
    Column({
      title: t("common.soTienConLai"),
      sort_key: "tienConLai",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["tienConLai"] || "",
      width: "150px",
      dataIndex: "tienConLai",
      key: "tienConLai",
      align: "right",
      i18Name: "common.soTienConLai",
      render: (item) => (item || 0).formatPrice(),
    }),
    Column({
      title: t("thuNgan.soTienTamUng"),
      sort_key: "tienTamUng",
      onClickSort: onClickSort,
      dataSort: dataSortColumn["tienTamUng"] || "",
      width: "150px",
      dataIndex: "tienTamUng",
      key: "tienTamUng",
      align: "right",
      i18Name: "thuNgan.soTienTamUng",
      render: (item) => (item || 0).formatPrice(),
    }),
    Column({
      title: (
        <>
          {t("common.tienIch")} <Setting refTable={refSettings} />
        </>
      ),
      width: "100px",
      align: "center",
      fixed: "right",
      ignore: true,
      render: (list, item, index) => {
        return (
          <div className="flex-center">
            <Tooltip title={t("common.xemChiTiet")}>
              <SVG.IcEye className="ic-action" />
            </Tooltip>
          </div>
        );
      },
    }),
  ];

  const onChangePage = (page) => {
    setQueryStringValue("page", page - 1);
    onSearch({ page: page - 1 });
  };

  const handleSizeChange = (size) => {
    setQueryStringValues({ size: size, page: 0 });
    onSizeChange({ size });
  };

  const onRow = (record) => {
    return {
      onClick: () => {
        const { id, nbDotDieuTriId } = record || {};
        history.push({
          pathname: `/phuc-hoi-chuc-nang/dieu-tri-phcn/${nbDotDieuTriId}/${id}`,
          state: getAllQueryString(),
        });
      },
    };
  };

  return (
    <Main noPadding={true}>
      <TableWrapper
        columns={columns}
        dataSource={uniqBy(listData || [], "id")}
        onRow={onRow}
        rowKey={(record) => record.id}
        tableName="table_PHCN_DieuTriPHCN"
        ref={refSettings}
        loading={isLoading}
      />
      {!!totalElements && (
        <Pagination
          listData={uniqBy(listData || [], "id")}
          onChange={onChangePage}
          current={page + 1}
          pageSize={size}
          total={totalElements}
          onShowSizeChange={handleSizeChange}
        />
      )}
    </Main>
  );
};

const mapStateToProps = (state) => {
  const {
    dieuTriPHCN: {
      listData,
      totalElements,
      page,
      size,
      dataSortColumn,
      isLoading,
    },
  } = state;
  return {
    listData,
    totalElements,
    page,
    size,
    dataSortColumn,
    isLoading,
  };
};

const mapDispatchToProps = ({
  dieuTriPHCN: { onSearch, onSizeChange, onSortChange },
  phieuIn: { showFileEditor },
}) => ({
  onSearch,
  onSizeChange,
  onSortChange,
  showFileEditor,
});

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(DanhSachDieuTriPHCN);
