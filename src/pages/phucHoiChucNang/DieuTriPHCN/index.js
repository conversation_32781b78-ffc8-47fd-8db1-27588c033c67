import React, { useEffect, useMemo, useRef, useState } from "react";
import { Main, MainPage } from "./styled";
import { useDispatch } from "react-redux";
import { t } from "i18next";
import <PERSON><PERSON>iemDieuTriPHCN from "../components/TimKiemDieuTriPHCN";
import DanhSachDieuTriPHCN from "../components/DanhSachDieuTriPHCN";
import { KhoaThucHien } from "components";
import {
  CACHE_KEY,
  DS_TINH_CHAT_KHOA,
  HOTKEY,
  ROLES,
  LOAI_DANG_KY,
} from "constants/index";
import { useGuid } from "hooks";
import { checkRole } from "lib-utils/role-utils";

export const LIST_LOAI_DANG_KY_ROLES = [
  {
    id: LOAI_DANG_KY.PHUC_HOI_CHUC_NANG,
    ten: t("phcn.PHCN"),
    condition: checkRole([
      ROLES["PHUC_HOI_CHUC_NANG"].HIEN_THI_DS_DANG_KY_LOAI_PHCN,
    ]),
  },
  {
    id: LOAI_DANG_KY.Y_HOC_CO_TRUYEN,
    ten: t("phcn.YHCT"),
    condition: checkRole([
      ROLES["PHUC_HOI_CHUC_NANG"].HIEN_THI_DS_DANG_KY_LOAI_YHCT,
    ]),
  },
  {
    id: 30, // giá trị enums cũ
    ten: t("phcn.thanNhanTao"),
    condition: checkRole([
      ROLES["PHUC_HOI_CHUC_NANG"].HIEN_THI_DS_DANG_KY_LOAI_THAN_NHAN_TAO,
    ]),
  },
  {
    id: 40, // giá trị enums cũ
    ten: t("phcn.banChan"),
    condition: checkRole([
      ROLES["PHUC_HOI_CHUC_NANG"].HIEN_THI_DS_DANG_KY_LOAI_BAN_CHAN,
    ]),
  },
  {
    id: 50, // giá trị enums cũ
    ten: t("phcn.hoaXaTri"),
    condition: checkRole([
      ROLES["PHUC_HOI_CHUC_NANG"].HIEN_THI_DS_DANG_KY_LOAI_HOA_XA_TRI,
    ]),
  },
];

const DieuTriPHCN = (props) => {
  const layerId = useGuid();
  const refCreate = useRef();
  const [state, _setState] = useState();
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  const { onAddLayer, onRemoveLayer, onRegisterHotkey } = useDispatch().phimTat;

  const isPhcn = checkRole([
    ROLES["PHUC_HOI_CHUC_NANG"].HIEN_THI_DS_DANG_KY_LOAI_PHCN,
  ]);
  const isYhct = checkRole([
    ROLES["PHUC_HOI_CHUC_NANG"].HIEN_THI_DS_DANG_KY_LOAI_YHCT,
  ]);
  const isThanNhanTao = checkRole([
    ROLES["PHUC_HOI_CHUC_NANG"].HIEN_THI_DS_DANG_KY_LOAI_THAN_NHAN_TAO,
  ]);
  const isBanChan = checkRole([
    ROLES["PHUC_HOI_CHUC_NANG"].HIEN_THI_DS_DANG_KY_LOAI_BAN_CHAN,
  ]);
  const isHoaXaTri = checkRole([
    ROLES["PHUC_HOI_CHUC_NANG"].HIEN_THI_DS_DANG_KY_LOAI_HOA_XA_TRI,
  ]);

  const isDieuTriKetHop = useMemo(() => {
    return LIST_LOAI_DANG_KY_ROLES.filter((item) => item.condition)?.length > 1;
  }, [LIST_LOAI_DANG_KY_ROLES]);

  useEffect(() => {
    onRegisterHotkey({
      layerId: layerId,
      hotKeys: [
        {
          keyCode: HOTKEY.F1, //F1
          onEvent: () => {
            refCreate.current && refCreate.current.click();
          },
        },
      ],
    });
  }, []);

  useEffect(() => {
    onAddLayer({ layerId: layerId });
    return () => {
      onRemoveLayer({ layerId: layerId });
    };
  }, []);

  useEffect(() => {
    if (
      isDieuTriKetHop ||
      isPhcn ||
      isYhct ||
      isThanNhanTao ||
      isBanChan ||
      isHoaXaTri
    ) {
      document.title = isDieuTriKetHop
        ? t("phcn.dieuTriKetHop")
        : isPhcn
        ? t("phcn.phcn")
        : isYhct
        ? t("phcn.yhct")
        : isThanNhanTao
        ? t("phcn.thanNhanTao")
        : isBanChan
        ? t("phcn.banChan")
        : isHoaXaTri
        ? t("phcn.hoaXaTri")
        : "";
    }
  }, [isDieuTriKetHop, isPhcn, isYhct, isThanNhanTao, isBanChan, isHoaXaTri]);

  const onChangeKhoa = (khoa) => {
    setState({ khoaLamViec: khoa });
  };

  return (
    <MainPage
      breadcrumb={[
        {
          title: isDieuTriKetHop
            ? t("phcn.dieuTriKetHop")
            : isPhcn
            ? t("phcn.phcn")
            : isYhct
            ? t("phcn.yhct")
            : isThanNhanTao
            ? t("phcn.thanNhanTao")
            : isBanChan
            ? t("phcn.banChan")
            : isHoaXaTri
            ? t("phcn.hoaXaTri")
            : "",
          link: "/phuc-hoi-chuc-nang",
        },
        {
          title: isDieuTriKetHop
            ? t("phcn.dsDieuTriKetHop")
            : isPhcn
            ? t("phcn.dsDieuTriPhcn")
            : isYhct
            ? t("phcn.dsDieuTriYhct")
            : isThanNhanTao
            ? t("phcn.dsDieuTriThanNhanTao")
            : isBanChan
            ? t("phcn.dsDieuTriBanChan")
            : isHoaXaTri
            ? t("phcn.dsDieuTriHoaXaTri")
            : "",
          link: "/phuc-hoi-chuc-nang/ds-dieu-tri-phcn",
        },
      ]}
      title={
        <div>
          <label>
            {isDieuTriKetHop
              ? t("phcn.dsDieuTriKetHop")
              : isPhcn
              ? t("phcn.dsDieuTriPhcn")
              : isYhct
              ? t("phcn.dsDieuTriYhct")
              : isThanNhanTao
              ? t("phcn.dsDieuTriThanNhanTao")
              : isBanChan
              ? t("phcn.dsDieuTriBanChan")
              : isHoaXaTri
              ? t("phcn.dsDieuTriHoaXaTri")
              : ""}
          </label>
        </div>
      }
      titleRight={
        <KhoaThucHien
          cacheKey={CACHE_KEY.DATA_KHOA_LAM_VIEC_PHCN}
          dsTinhChatKhoa={DS_TINH_CHAT_KHOA.NOI_TRU}
          onChange={onChangeKhoa}
          type={1}
        />
      }
    >
      <Main>
        <TimKiemDieuTriPHCN
          layerId={layerId}
          isPhcn={isPhcn}
          isYhct={isYhct}
          isThanNhanTao={isThanNhanTao}
          isBanChan={isBanChan}
          isHoaXaTri={isHoaXaTri}
          isDieuTriKetHop={isDieuTriKetHop}
        />
        <DanhSachDieuTriPHCN
          isPhcn={isPhcn}
          isYhct={isYhct}
          isThanNhanTao={isThanNhanTao}
          isBanChan={isBanChan}
          isHoaXaTri={isHoaXaTri}
        />
      </Main>
    </MainPage>
  );
};

export default DieuTriPHCN;
