import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import NgaySinh from '../NgaySinh';

// Mock các dependencies
jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key) => key,
  }),
}));

jest.mock('hooks', () => ({
  useEnum: () => [
    [
      { id: 1, ten: 'Nam' },
      { id: 2, ten: 'Nữ' },
    ],
  ],
  useStore: (path) => {
    if (path === 'nbDotDieuTri.thongTinCoBan') {
      return {
        ngaySinh: '1990-01-01',
        tuoi2: '33 tuổi',
        gioiTinh: 1,
        dsPhanLoaiNbId: [1, 2, 3],
        dsPhanLoaiNb: [
          { id: 1, ten: 'Bảo hiểm y tế' },
          { id: 2, ten: 'Người cao tuổi' },
          { id: 3, ten: 'Ưu tiên' },
        ],
      };
    }
    return {};
  },
}));

jest.mock('utils/index', () => ({
  isArray: (arr, checkLength) => {
    return Array.isArray(arr) && (!checkLength || arr.length > 0);
  },
}));

// Mock PhanLoaiNguoiBenh component
jest.mock('pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/ThongTinBenhNhan/PhanLoaiNguoiBenh', () => {
  return function MockPhanLoaiNguoiBenh({ thongTinBenhNhan }) {
    const { dsPhanLoaiNb = [] } = thongTinBenhNhan || {};
    return (
      <div data-testid="phan-loai-nguoi-benh">
        {dsPhanLoaiNb.slice(0, 2).map((item) => (
          <span key={item.id} data-testid="tag">
            {item.ten}
          </span>
        ))}
      </div>
    );
  };
});

const mockStore = configureStore({
  reducer: {
    test: (state = {}) => state,
  },
});

describe('NgaySinh Component', () => {
  const renderComponent = (props = {}) => {
    return render(
      <Provider store={mockStore}>
        <NgaySinh {...props} />
      </Provider>
    );
  };

  test('should render birth date and age correctly', () => {
    renderComponent();
    
    expect(screen.getByText('common.ngaySinh:')).toBeInTheDocument();
    expect(screen.getByText(/01\/01\/1990/)).toBeInTheDocument();
    expect(screen.getByText(/33 tuổi/)).toBeInTheDocument();
  });

  test('should render gender correctly', () => {
    renderComponent();
    
    expect(screen.getByText(/\(Nam\)/)).toBeInTheDocument();
  });

  test('should render PhanLoaiNguoiBenh component when dsPhanLoaiNbId exists', () => {
    renderComponent();
    
    expect(screen.getByTestId('phan-loai-nguoi-benh')).toBeInTheDocument();
    expect(screen.getAllByTestId('tag')).toHaveLength(2);
  });

  test('should render rightComponent when provided', () => {
    const rightComponent = <div data-testid="right-component">Right Content</div>;
    renderComponent({ rightComponent });
    
    expect(screen.getByTestId('right-component')).toBeInTheDocument();
  });

  test('should handle responsive behavior', () => {
    // Mock window.innerWidth
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 500, // Small screen
    });

    renderComponent();
    
    // Trigger resize event
    fireEvent(window, new Event('resize'));
    
    // Component should still render but with responsive behavior
    expect(screen.getByTestId('phan-loai-nguoi-benh')).toBeInTheDocument();
  });
});
