import React, { useMemo } from "react";
import { useTranslation } from "react-i18next";
import moment from "moment";
import { useEnum, useStore, useListAll } from "hooks";
import { ENUM } from "constants";
import { isArray } from "utils/index";

const NgaySinh = ({ rightComponent }) => {
  const { t } = useTranslation();
  const [listGioiTinh] = useEnum(ENUM.GIOI_TINH);
  const thongTinCoBan = useStore("nbDotDieuTri.thongTinCoBan", {});
  const [listAllPhanLoaiNB] = useListAll("phanLoaiNB", {}, true);

  const gioiTinh = useMemo(() => {
    return (listGioiTinh || []).find(
      (item) => item.id === thongTinCoBan?.gioiTinh
    )?.ten;
  }, [listGioiTinh, thongTinCoBan?.gioiTinh]);

  const renderPhanLoaiNguoiBenh = () => {
    const { dsPhanLoaiNbId = [] } = thongTinCoBan || {};
    if (!isArray(dsPhanLoaiNbId, true)) return null;

    const listPhanLoaiNb = dsPhanLoaiNbId.map((id) => {
      const item = listAllPhanLoaiNB.find((x) => x.id === id);
      return item || { id, ten: "", mauChu: "", mauNen: "" };
    });

    const displayTags = listPhanLoaiNb.slice(0, 2);
    const shouldShowTitle = listPhanLoaiNb.length > 2;
    const titleText = shouldShowTitle
      ? listPhanLoaiNb.map((item) => item.ten).join(", ")
      : undefined;

    return (
      <>
        {displayTags.map((tag) => (
          <span
            key={tag.id}
            style={{
              background: tag.mauNen,
              color: tag.mauChu,
              padding: "0 3px",
              margin: "0 3px",
              borderRadius: "2px",
              fontSize: "12px",
              whiteSpace: "nowrap",
            }}
            title={titleText}
          >
            {tag.ten}
          </span>
        ))}
      </>
    );
  };

  return (
    <div className="flex">
      <div className="label">{t("common.ngaySinh")}:</div>
      <div className="info">
        {thongTinCoBan?.ngaySinh
          ? moment(thongTinCoBan?.ngaySinh)
              .utcOffset("+0700")
              .format(thongTinCoBan?.chiNamSinh ? "YYYY" : "DD/MM/YYYY")
          : ""}
        {thongTinCoBan?.tuoi2 && <> - {thongTinCoBan?.tuoi2}</>}
        {gioiTinh && <> ({gioiTinh})</>}
        {renderPhanLoaiNguoiBenh()}
      </div>
      {rightComponent}
    </div>
  );
};

export default NgaySinh;
