import React, { useMemo } from "react";
import { useTranslation } from "react-i18next";
import moment from "moment";
import { useEnum, useStore } from "hooks";
import { ENUM } from "constants";
import PhanLoaiNguoiBenh from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/ThongTinBenhNhan/PhanLoaiNguoiBenh";

const NgaySinh = ({ rightComponent }) => {
  const { t } = useTranslation();
  const [listGioiTinh] = useEnum(ENUM.GIOI_TINH);
  const thongTinCoBan = useStore("nbDotDieuTri.thongTinCoBan", {});

  const gioiTinh = useMemo(() => {
    return (listGioiTinh || []).find(
      (item) => item.id === thongTinCoBan?.gioiTinh
    )?.ten;
  }, [listGioiTinh, thongTinCoBan?.gioiTinh]);

  return (
    <div className="flex">
      <div className="label">{t("common.ngaySinh")}:</div>
      <div className="info">
        {thongTinCoBan?.ngaySinh
          ? moment(thongTinCoBan?.ngaySinh)
              .utcOffset("+0700")
              .format(thongTinCoBan?.chiNamSinh ? "YYYY" : "DD/MM/YYYY")
          : ""}
        {thongTinCoBan?.tuoi2 && <> - {thongTinCoBan?.tuoi2}</>}
        {gioiTinh && <> ({gioiTinh})</>}
        {isArray(thongTinCoBan?.dsPhanLoaiNbId, true) && (
          <PhanLoaiNguoiBenh thongTinBenhNhan={thongTinBenhNhan} />
        )}
      </div>
      {rightComponent}
    </div>
  );
};

export default NgaySinh;
