import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from "react";
import { useTranslation } from "react-i18next";
import { Col, Row, Input, message } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { useLoading, useStore } from "hooks";
import {
  NumberFormat,
  Select,
  ModalTemplate,
  Button,
  InputTimeout,
  DatePicker,
} from "components";
import { firstLetterWordUpperCase } from "utils/index";
import { Main } from "./styled";
import { FORMAT_DATE_TIME, ROLES } from "constants/index";
import moment from "moment";
import { checkRole } from "lib-utils/role-utils";

const ModalSuaHoanTamUng = (props, ref) => {
  const { t } = useTranslation();
  const listAllPhuongThucThanhToan = useStore(
    "phuongThucTT.listAllPhuongThucThanhToan",
    []
  );
  const thongTinBenhNhan = useStore("nbDotDieuTri.thongTinBenhNhan");
  const auth = useSelector((state) => state.auth.auth);
  const listAllQuayTiepDon = useStore("quayTiepDon.listAllQuayTiepDon", []);

  const { showLoading, hideLoading } = useLoading();
  const refModal = useRef(null);
  const refCallBack = useRef(null);

  const [state, _setState] = useState({ show: false });

  const {
    phuongThucTT: { getListAllPhuongThucThanhToan },
    hoanTamUng: { suaHoanTamUng, onSearch },
  } = useDispatch();
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  useImperativeHandle(ref, () => ({
    show: (data = {}, func = {}) => {
      setState({
        show: true,
        data,
        tongTien: data?.tongTien,
        ghiChu: data?.ghiChu,
        tenLyDoTamUng: data?.tenLyDoTamUng,
        thoiGianThucHien: data?.thoiGianThucHien,
        phuongThucTtId: data?.phuongThucTtId,
        originalPhuongThucTtId: data?.phuongThucTtId,
        nhaTamUngId: data?.nhaTamUngId,
        quayId: data?.quayId,
        id: data?.id,
      });
      refCallBack.current = func?.callback;
    },
  }));

  useEffect(() => {
    if (state.show) {
      refModal.current && refModal.current.show();
      getListAllPhuongThucThanhToan({ page: "", size: "", active: true });
    } else {
      refModal.current && refModal.current.hide();
    }
  }, [state.show]);

  const submitHandler = async () => {
    try {
      const {
        phuongThucTtId,
        originalPhuongThucTtId,
        id,
        thoiGianThucHien,
        nhaTamUngId,
        quayId,
      } = state || {};
      if (!phuongThucTtId) {
        return;
      }

      if (
        phuongThucTtId !== originalPhuongThucTtId &&
        state.data?.thuNganId !== auth?.nhanVienId
      ) {
        message.error(
          t(
            "thuNgan.khongDuocSuaHinhThucThanhToanCuaPhieuHoanTamUngKhacThuNgan",
            { tenThuNgan: state.data?.tenThuNgan }
          )
        );
        return;
      }

      showLoading();
      await suaHoanTamUng({
        id,
        phuongThucTtId,
        thoiGianThucHien,
        nhaTamUngId,
        quayId,
      });

      onSearch({});
      setState({ show: false, originalPhuongThucTtId: phuongThucTtId });
    } catch (error) {
      console.error(error);
    } finally {
      hideLoading();
    }
  };

  const onChange = (key) => (e) => {
    let obj = {
      [key]: e?.target?.value || e?.floatValue || e,
    };
    setState(obj);
  };

  const handleClickBack = () => {
    setState({ show: false });
  };
  const handleClickNext = () => {
    submitHandler();
  };

  return (
    <ModalTemplate
      title={t("thuNgan.quanLyTamUng.phieuHoanTamUng")}
      width={752}
      ref={refModal}
      onCancel={handleClickBack}
      rightTitle={
        <div style={{ height: "100%", alignItems: "center" }}>
          <span className="font-color">
            {firstLetterWordUpperCase(thongTinBenhNhan?.tenNb)}
          </span>
          {thongTinBenhNhan?.tuoi && (
            <span className="normal-weight">
              {thongTinBenhNhan?.tuoi
                ? ` - ${thongTinBenhNhan?.tuoi} tuổi`
                : thongTinBenhNhan?.thangTuoi
                ? ` - ${thongTinBenhNhan?.thangTuoi} tháng`
                : ""}
            </span>
          )}
        </div>
      }
      actionLeft={
        <Button.QuayLai onClick={handleClickBack}>
          {t("common.quayLai")} [ESC]
        </Button.QuayLai>
      }
      actionRight={
        <Button type={"primary"} onClick={handleClickNext}>
          {t("thuNgan.quanLyTamUng.xacNhan")}
        </Button>
      }
    >
      <Main>
        <Row>
          <Col span={24}>
            <Col span={6}>
              <span>{t("thuNgan.quanLyTamUng.soTienHoan")}</span>
            </Col>
            <Col span={18}>
              <NumberFormat
                customInput={Input}
                thousandSeparator="."
                decimalSeparator=","
                style={{
                  textAlign: "right",
                }}
                disabled={true}
                value={state?.tongTien}
              />
            </Col>
          </Col>
          <Col span={24}>
            <Col span={6}>
              <span>{t("thuNgan.quanLyTamUng.hinhThucTT")}</span>
            </Col>
            <Col span={18}>
              <Select
                value={state.phuongThucTtId}
                onChange={onChange("phuongThucTtId")}
                data={listAllPhuongThucThanhToan}
                placeholder={t("thuNgan.chonPhuongThucThanhToan")}
              />
            </Col>
          </Col>
          <Col span={24}>
            <Col span={6}>{t("thuNgan.quanLyTamUng.lyDoHoanTamUng")}</Col>
            <Col span={18}>
              <InputTimeout
                value={state.tenLyDoTamUng}
                className="input-option"
                disabled={true}
              />
            </Col>
          </Col>
          <Col span={24}>
            <Col span={6}> {t("thuNgan.quanLyTamUng.ngayPhieuHoan")}</Col>
            <Col span={18}>
              <DatePicker
                onChange={onChange("thoiGianThucHien")}
                value={
                  state?.thoiGianThucHien && moment(state?.thoiGianThucHien)
                }
                format={FORMAT_DATE_TIME}
                disabled={
                  !checkRole([ROLES["THU_NGAN"].SUA_NGAY_PHIEU_HOAN_TAM_UNG])
                }
                className="full-width"
                showTime
              />
            </Col>
          </Col>
          <Col span={24}>
            <Col span={6}> {t("thuNgan.quanLyTamUng.ghiChu")}</Col>
            <Col span={18}>
              <InputTimeout
                value={state.ghiChu}
                className="input-option"
                disabled={true}
              />
            </Col>
          </Col>
          <Col span={24}>
            <Col span={6}>{t("baoCao.quayThu")}</Col>
            <Col span={18}>
              <Select
                data={listAllQuayTiepDon}
                value={state.quayId}
                onChange={onChange("quayId")}
                disabled={
                  !checkRole([ROLES["THU_NGAN"].SUA_QUAY_PHIEU_TAM_UNG])
                }
              />
            </Col>
          </Col>
        </Row>
      </Main>
    </ModalTemplate>
  );
};

export default forwardRef(ModalSuaHoanTamUng);
