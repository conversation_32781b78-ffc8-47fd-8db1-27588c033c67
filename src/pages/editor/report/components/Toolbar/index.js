import React, {
  useState,
  memo,
  useEffect,
  useContext,
  useRef,
  useLayoutEffect,
} from "react";
import { Button as Ant<PERSON><PERSON>on, Slider, Input, Col, Row, message } from "antd";
import {
  Pop<PERSON>,
  AuthWrapper,
  ButtonSetting as <PERSON><PERSON>,
  LazyLoad,
} from "components";
import {
  BoldOutlined,
  ItalicOutlined,
  UnderlineOutlined,
  MoreOutlined,
  FontColorsOutlined,
  HighlightOutlined,
} from "@ant-design/icons";
import printProvider, { printJS } from "data-access/print-provider";
import { Main, PopupTool, GlobalStyle } from "./styled";
import {
  FontSizeConfig,
  EditorTool,
  EMR2Context,
  useEditor,
} from "components/editor/config";
import {
  addFooterPage,
  combineFields,
  pageType,
  pdfGenerator,
} from "utils/editor-utils";
import pdfUtils from "utils/pdf-utils";
import fileUtils from "utils/file-utils";
import { useDispatch, useSelector } from "react-redux";
import { useParams } from "react-router-dom";
import { getBackendUrl } from "client/request";
import moment from "moment";
import {
  useConfirm,
  useEnum,
  useHotKey,
  useLoading,
  useQueryString,
  useStore,
  useThietLap,
} from "hooks";
import { t } from "i18next";
import { cloneDeep, debounce } from "lodash";
import { refValues } from "../File";
import { detectMob, openInNewTab } from "utils";
import {
  ENUM,
  HOTKEY,
  HUONG_GIAY,
  KHO_GIAY,
  PAGE_TYPE,
  ROLES,
  THIET_LAP_CHUNG,
} from "constants/index";
import { SVG } from "assets";
import useGuiGiamDinh from "./useGuiGiamDinh";
import xmlUtils from "utils/xml-utils";
import ModalLichSuKy from "../ModalLichSuKy";
import { TRANG_THAI_PHIEU_NUMBER } from "pages/quanLyBaoCaoAdr/config";
import ModalXemLichSu from "../ModalXemLichSu";
import JSZip from "jszip";
import JSZipUtils from "jszip-utils";
import { LIST_FILE_PRINT_NOT_EDIT } from "../../constants";
import ModalSaoChepPhieu from "../ModalSaoChepPhieu";
import { refGetIsChangeFile } from "components/Prompt";
import { handleCopyBieuMau } from "./utils";
import {
  refDropDownListError,
  refLoadingDroplistStatus,
} from "components/editor/cores/DropDownList";

const { utils: command, PickColor } = EditorTool;
const LIST_FILE_ADD_FOOTER = [
  "EMR_BA077",
  "EMR_BA077.1",
  "EMR_BA472",
  "EMR_HSDD094.1",
  "EMR_BA411",
  "EMR_BA410",
];

const Toolbar = ({
  onSaveData,
  fileOnShow = {},
  patientDocument,
  zoomValue,
  setZoomValue,
  changeFileOnshow,
  printNewTab,
  // onChangeNameDocument = () => {},
  ...props
}) => {
  const { showConfirm } = useConfirm();
  const refButtonPrint = useRef(null);
  const refButtonSave = useRef(null);
  const refLsKy = useRef(null);
  const refModalXemLichSu = useRef(null);
  const refModalSaoChepPhieu = useRef(null);
  const thongTinKy = useStore("files.thongTinKy", {});
  const refLuotApDungBieuMau = useRef(0);
  const { components } = fileOnShow;
  const { editorId } = useContext(EMR2Context);

  const { showLoading, hideLoading } = useLoading();

  useHotKey(editorId, [
    {
      keyCode: HOTKEY.F2,
      onEvent: () => {
        refButtonPrint.current?.click();
      },
    },
    {
      keyCode: HOTKEY.F4,
      onEvent: () => {
        refButtonSave.current?.click();
      },
    },
  ]);
  const listTemplate = useEditor(editorId, "templateBieuMau", []);
  const fileData = useEditor(editorId, "fileData", {});
  const file = useEditor(editorId, "file", {});
  const signStatus = useEditor(editorId, "signStatus", {});
  const isSaveFormLoading = useEditor(editorId, "isSaveFormLoading", false);
  const pathName = window.location.pathname;
  const isPreview = pathName.includes("preview");
  const isTemplate = pathName.includes("template");
  const { id, maBaoCao } = useParams();
  const [conThu] = useQueryString("conThu", 0);
  const [nbDotDieuTriId] = useQueryString("nbDotDieuTriId", 0);
  const { width: windowWidth } = useSelector((state) => state.application);
  const [onGuiGiamDinh, onHuyGiamDinh] = useGuiGiamDinh(fileOnShow.ma, id, {
    conThu,
    nbDotDieuTriId,
  });
  const [TO_DIEU_TRI_IN_THONG_TIN_HANH_CHINH] = useThietLap(
    THIET_LAP_CHUNG.TO_DIEU_TRI_IN_THONG_TIN_HANH_CHINH
  );

  const [dataNHOM_PHIEU_GUI_GIAM_DINH] = useThietLap(
    THIET_LAP_CHUNG.NHOM_PHIEU_GUI_GIAM_DINH
  );
  const [listHuongDieuTri] = useEnum(ENUM.HUONG_DIEU_TRI);

  // const { files } = useSelector((state) => state.documents);
  // const { auth } = useSelector((state) => state.auth);
  // const { signDigital, getHistorySigned } = useDispatch().signer;
  // const isAdmin = useDispatch().auth.isAdmin;
  // const { getCommonConfig } = useDispatch().common;
  // const { commonConfig } = useSelector((state) => state.common);
  // const updateDataDocument = useDispatch().documents.updateData;
  // const refFileSigned = useRef({});
  const [state, _setState] = useState({
    fontSize: "2",
    fontFamily: "timesNewRoman",
    idx: 0,
    isSigning: false,
    isAdmin: false,
    bieuMauTemplate: null,
    visibalPopover: false,
  });
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };
  const refModalName = useRef();
  // const refModalSignPdf = useRef(null);
  const {
    files: {
      deleteTemplateBieuMau,
      updateEditor,
      createTemplateBieuMau,
      getTemplateBieuMau,
      deletePhieuEditor,
    },
    application: { updateData },
    baoCao: { tongHop },
    phieuIn: { kyPhieu },
  } = useDispatch();

  const handleChangeFontSize = (value) => {
    setState({
      fontSize: value,
    });
    command.setFontSize(value);
  };
  const handleChangeColor = (value) => {
    command.foreColor(value);
  };
  const handleMark = (value) => {
    command.mark(value);
  };
  const handleChangeZoom = (value) => {
    setZoomValue(value);
  };
  const width = window.screen.width;
  // useEffect(() => {
  //   if (width > 1000 && width <= 1200) {
  //     setZoomValue(90);
  //   } else if (width > 768 && width <= 1000) {
  //     setZoomValue(80);
  //   } else if (width > 500 && width <= 768) {
  //     setZoomValue(60);
  //   } else if (width < 500) {
  //     setZoomValue(40);
  //   } else if (width > 1200) {
  //     setZoomValue(100);
  //   }
  // }, [width]);
  // useEffect(() => {
  //   isAdmin().then((s) => {
  //     setState({ isAdmin: s });
  //   });
  // }, [auth]);

  useEffect(() => {
    tongHop({ loai: 50, page: "", size: "" });
  }, []);

  useEffect(() => {
    if (props.layoutType !== "default" && width > 1300) {
      updateData({ therapyRightToolCollapse: true });
    } else if (props.layoutType == "default" && width > 1300) {
      updateData({ therapyRightToolCollapse: false });
    }
  }, [props.layoutType]);

  const onClickSaveData = () => {
    onSaveData(false);
  };
  const onPrintForm = async (returnBase64) => {
    if (
      state.isLoadingDataPhieu ||
      (refLoadingDroplistStatus &&
        !Object.values(refLoadingDroplistStatus?.current || {}).every(
          (a) => !a
        ))
    ) {
      if (Object.keys(refDropDownListError.current || {}).length) {
        const error = Object.keys(refDropDownListError.current).reduce(
          (a, b) => {
            const item = refDropDownListError.current[b];
            return (
              a +
              "\n" +
              `${item.label ? `${item.label}: ` : ""} ${item.message} ${
                item?.api || ""
              }`
            );
          },
          ""
        );
        message.error(error);
      } else {
        message.error(t("editor.duLieuChuaDuocLoad"));
      }

      return;
    }
    if (
      !fileOnShow ||
      fileOnShow?.ma ||
      fileOnShow?.type == "xnhis" ||
      fileOnShow?.type == "cdhahis"
    ) {
      try {
        showLoading();
        //kiểm tra phiếu có thay đổi => lưu phiếu trước khi in
        if (refGetIsChangeFile.current && refGetIsChangeFile.current())
          await onSaveData();
        const itemComponents = components?.length && components?.[1]?.props;

        let ListFilePrintNotEdit = LIST_FILE_PRINT_NOT_EDIT;
        if (
          !TO_DIEU_TRI_IN_THONG_TIN_HANH_CHINH ||
          itemComponents?.inThongTinHanhChinh
        ) {
          ListFilePrintNotEdit.push("EMR_BA077");
        }
        const htmlNotEdit =
          ListFilePrintNotEdit.some((item) => item === fileOnShow.ma) ||
          file.cauHinh?.notEditHtml;
        const layout = pageType.from(props.layoutType, props.pageType);
        const { pdfUrls } = await pdfGenerator({
          layout,
          htmlNotEdit,
          ma: fileOnShow.ma,
          pageType: props.pageType,
          inThongTinHanhChinh: itemComponents?.inThongTinHanhChinh
        });
        let blobUrl = null;
        if (pdfUrls.length > 1) {
          blobUrl = await pdfUtils.mergePdf(pdfUrls);
          if (!blobUrl) blobUrl = pdfUrls[0];
        } else {
          blobUrl = pdfUrls[0];
        }

        if (LIST_FILE_ADD_FOOTER.some((item) => item === fileOnShow.ma)) {
          blobUrl = await addFooterPage({
            dataPrint: fileData,
            urls: pdfUrls,
            khongHienThiNguoiIn: itemComponents?.khongHienThiNguoiIn,
          });
        }
        if (returnBase64) {
          return fileUtils.blobUrlToBase64(blobUrl, {
            removeMime: true,
          });
        } else {
          if (detectMob()) {
            window.open(blobUrl);
          } else {
            if (printNewTab) {
              openInNewTab(blobUrl);
            } else {
              printProvider.printPdf(
                [
                  {
                    khoGiay:
                      layout.name == PAGE_TYPE.FORM_A4
                        ? KHO_GIAY.A4
                        : KHO_GIAY.A3,
                    hinhThucIn: fileOnShow.hinhThucIn,
                    loaiIn: fileOnShow.loaiIn,
                    huongGiay: layout.landscape
                      ? HUONG_GIAY.NGANG
                      : HUONG_GIAY.DOC,
                    file: { pdf: blobUrl },
                  },
                ],
                { isEditor: true, mergePdfFile: blobUrl }
              );
            }
          }
        }
      } catch (error) {
      } finally {
        hideLoading();
      }
    } else {
      if (fileOnShow?.api) {
        if (fileUtils.isExternalUrl(fileOnShow?.sign?.api || fileOnShow?.api)) {
          printJS({
            printable: pdfUtils.getExtenalPdfUrl(
              fileOnShow?.sign?.api || fileOnShow?.api
            ),
            type: "pdf",
          });
        } else
          fileUtils
            .getFromUrl({ url: `${getBackendUrl()}${fileOnShow?.api}` })
            .then((s) => {
              const blob = new Blob([new Uint8Array(s)], {
                type: "application/pdf",
              });
              const blobUrl = window.URL.createObjectURL(blob);
              if (printNewTab) {
              } else {
                printJS({
                  printable: blobUrl,
                  type: "pdf",
                });
              }
            });
      }
    }
  };
  window.onPrintForm = onPrintForm;

  const onDownload = async () => {
    if (
      !fileOnShow ||
      fileOnShow?.ma ||
      fileOnShow?.type == "xnhis" ||
      fileOnShow?.type == "cdhahis"
    ) {
      try {
        showLoading();
        const { pdfUrls } = await pdfGenerator({
          layout: pageType.from(props.layoutType, props.pageType),
          pageType: props.pageType,
        });
        let urls = [];
        if (pdfUrls?.length) {
          if (pdfUrls?.length > 1) {
            const blobUrl = await pdfUtils.mergePdf(pdfUrls);
            urls.push(blobUrl);
          } else {
            urls.push(pdfUrls);
          }
        }

        //download xml nếu đã ký
        if (fileData.lichSuKy) {
          const xmlFile = await fileUtils.getFromUrl({
            url: fileUtils.absoluteFileUrl(fileData.lichSuKy?.duongDan),
          });
          const blob = new Blob([new Uint8Array(xmlFile)], {
            type: "text/xml",
          });
          const xmlFileUrl = window.URL.createObjectURL(blob);

          urls.push(xmlFileUrl);
        }
        const zip = new JSZip();
        let count = 0;
        const zipFilename = `${fileOnShow?.ma}.zip`;
        urls.forEach((url, index) => {
          const filename =
            index === 0 ? `${fileOnShow?.ma}.pdf` : `${fileOnShow?.ma}.xml`;
          // loading a file and add it in a zip file
          JSZipUtils.getBinaryContent(url, function (err, data) {
            if (err) {
              throw err; // or handle the error
            }
            zip.file(filename, data, { binary: true });
            count++;
            if (count === urls.length) {
              zip.generateAsync({ type: "blob" }).then(function (content) {
                fileUtils.downloadBlob(content, zipFilename);
              });
            }
          });
        });
      } catch (error) {
      } finally {
        hideLoading();
      }
    } else {
      if (fileOnShow?.api) {
        if (fileUtils.isExternalUrl(fileOnShow?.sign?.api || fileOnShow?.api)) {
          const url = getUrlFile(fileOnShow?.sign?.api || fileOnShow?.api);
          fileUtils
            .getFromUrl({
              url,
            })
            .then((s) => {
              const blob = new Blob([new Uint8Array(s)], {
                type: "application/pdf",
              });
              fileUtils.downloadBlob(
                blob,
                fileOnShow
                  ? `${fileOnShow?.ma}.pdf`
                  : `file_${moment(new Date()).format("DD/MM/YYYY")}.pdf`
              );
            })
            .catch((e) => {
              console.log(e);
            });
        } else {
          fileUtils
            .getFromUrl({ url: `${getBackendUrl()}${fileOnShow?.api}` })
            .then((s) => {
              const blob = new Blob([new Uint8Array(s)], {
                type: "application/pdf",
              });
              fileUtils.downloadBlob(
                blob,
                fileOnShow
                  ? `${fileOnShow?.ma}.pdf`
                  : `file_${moment(new Date()).format("DD/MM/YYYY")}.pdf`
              );
            });
        }
      }
    }
  };
  const onViewHistory = () => {
    refModalXemLichSu.current && refModalXemLichSu.current.show();
  };

  const getUrlFile = (url = "") => {
    return fileUtils.isExternalUrl(url)
      ? pdfUtils.getExtenalPdfUrl(url)
      : getBackendUrl() + url;
  };

  const renderPickColor = () => {
    return (
      <PickColor
        className="item-tool"
        iconComponent={FontColorsOutlined}
        changeColor={handleChangeColor}
      />
    );
  };
  const renderPickColorBackground = () => {
    return (
      <PickColor
        className="item-tool"
        iconComponent={HighlightOutlined}
        changeColor={handleMark}
      />
    );
  };
  const renderSelectFontSize = () => {
    return (
      <FontSizeConfig
        value={state.fontSize || "12 pt"}
        onChange={handleChangeFontSize}
        style={{ width: "auto", minWidth: "70px" }}
      />
    );
  };
  const renderButtonBold = () => {
    return (
      <AntButton
        icon={<BoldOutlined />}
        size={"small"}
        onClick={command.bold}
        className={"item-tool"}
      />
    );
  };
  const renderButtonItalic = () => {
    return (
      <AntButton
        icon={<ItalicOutlined />}
        size={"small"}
        onClick={command.italic}
        className={"item-tool"}
      />
    );
  };
  const renderButtonUnderline = () => {
    return (
      <AntButton
        icon={<UnderlineOutlined />}
        size={"small"}
        onClick={command.underline}
        className={"item-tool"}
      />
    );
  };
  const renderButtonPrint = () => {
    return (
      <Button
        onClick={() => onPrintForm(false)}
        rightIcon={<SVG.IcPrint />}
        ref={refButtonPrint}
      >
        {t("common.in")} [F2]
      </Button>
    );
  };
  const renderButtonDowload = () => {
    return (
      <Button onClick={onDownload} rightIcon={<SVG.IcDownload />}>
        {t("editor.taiFile")}
      </Button>
    );
  };

  const renderButtonViewHistory = () => {
    return <Button onClick={onViewHistory}>{t("editor.xemLichSu")}</Button>;
  };

  const onXemLsKy = () => {
    refLsKy.current && refLsKy.current.show({});
  };
  const renderButtonXemLsKy = () => {
    return <Button onClick={onXemLsKy}>{t("kySo.xemLsKy")}</Button>;
  };

  const renderButtonGuiGiamDinh = () => {
    const btnGiamDinh = (
      <Button
        type={"primary"}
        onClick={onGuiGiamDinh}
        rightIcon={<SVG.IcGuiCt />}
      >
        {t("giayDayCong.action.guiGiamDinh")}
      </Button>
    );
    const btnHuyGiamDinh = (
      <Button
        type={"default"}
        onClick={onHuyGiamDinh}
        rightIcon={<SVG.IcCloseCircle />}
      >
        {t("giayDayCong.action.huyGiamDinh")}
      </Button>
    );
    if (maBaoCao === "EMR_BA256" && !isPreview && !isTemplate) {
      return (
        <>
          {[10, 20, 30].includes(
            fileData.trangThai || fileData?.trangThaiDayCong
          ) && btnGiamDinh}

          {[40].includes(fileData.trangThai || fileData?.trangThaiDayCong) &&
            btnHuyGiamDinh}
        </>
      );
    } else if (
      !isPreview &&
      !isTemplate &&
      dataNHOM_PHIEU_GUI_GIAM_DINH.includes(fileOnShow.ma) &&
      (fileData?.trangThai || fileData?.trangThaiDayCong)
    ) {
      return (
        <>
          {[10, 20, 30].includes(
            fileData.trangThai || fileData?.trangThaiDayCong
          ) && (
            <AuthWrapper
              accessRoles={[
                ROLES["GIAY_DAY_CONG"].GIAY_NGHI_HUONG_DAY_LE,
                ROLES["GIAY_DAY_CONG"].GIAY_KSK_LAI_XE_DAY_LE,
              ]}
            >
              {btnGiamDinh}
            </AuthWrapper>
          )}

          {[40].includes(fileData.trangThai || fileData?.trangThaiDayCong) && (
            <AuthWrapper
              accessRoles={[
                ROLES["GIAY_DAY_CONG"].GIAY_NGHI_HUONG_HUY,
                ROLES["GIAY_DAY_CONG"].GIAY_KSK_LAI_XE_HUY,
              ]}
            >
              {btnHuyGiamDinh}
            </AuthWrapper>
          )}
        </>
      );
    }
  };

  const renderZoomTool = () => {
    return (
      <div className={"zoom-tool"}>
        <span>{"Zoom"}</span>
        <Slider
          className={"slider-tool"}
          onChange={handleChangeZoom}
          value={zoomValue}
        />
        <Input
          style={{ marginLeft: 6, width: 66 }}
          value={zoomValue}
          size={"small"}
          suffix={"%"}
          onChange={(e) => {
            handleChangeZoom(e.target.value);
          }}
        />
      </div>
    );
  };

  const onShowConfigForm = () => {
    // if (["emr", "form"].includes(file?.type)) {
    window.open("/editor/config/" + fileOnShow?.id);
    // }
  };
  const renderConfigForm = () => {
    // if (state.isAdmin && fileOnShow && !isEmpty(fileOnShow))
    return (
      <AuthWrapper accessRoles={[ROLES["EDITOR"].CONFIG_PHIEU]}>
        <Button rightIcon={<SVG.IcSetting />} onClick={onShowConfigForm}>
          <div>Config form</div>
        </Button>
      </AuthWrapper>
    );
  };
  const onDeleteForm = () => {
    if (fileData?.lichSuKy) {
      message.error(t("editor.vuiLongHuyKyTruocKhiXoa"));
      return;
    }
    if (
      Object.keys(signStatus).some((key) => {
        return signStatus[key].block;
      })
    ) {
      message.error(t("editor.bieuMauDaKhoaSauKhiKy"));
      return;
    }

    showConfirm(
      {
        title: t("common.xoaDuLieu"),
        content: t("editor.banCoMuonXoaBanGhiNay"),
        cancelText: t("common.quayLai"),
        okText: t("common.dongY"),
        classNameOkText: "button-error",
        showImg: true,
        showBtnOk: true,
      },
      async () => {
        try {
          showLoading();
          await deletePhieuEditor({ api: file?.api, id: fileData?.id });

          setTimeout(() => {
            window.location.reload();
          }, 500);
        } catch (error) {
          console.error(error);
        } finally {
          hideLoading();
        }
      }
    );
  };
  const renderXoaPhieu = () => {
    if (!fileData?.id) return null;

    if (file.cauHinh?.choPhepXoa) {
      return (
        <AuthWrapper accessRoles={[ROLES["EDITOR"].XOA_PHIEU]}>
          <Button
            type="error"
            rightIcon={<SVG.IcDelete />}
            onClick={onDeleteForm}
          >
            {t("editor.xoaBanGhi")}
          </Button>
        </AuthWrapper>
      );
    }

    return null;
  };

  const exportXml = () => {
    xmlUtils.exportHL7(fileData, { listHuongDieuTri: listHuongDieuTri });
  };

  const renderBtnAdr = () => {
    const trangThaiPhieu = fileData?.trangThaiPhieu?.[0];

    const onClickBtn = (trangThai) => () => {
      const refValuesClone = cloneDeep(refValues.current);

      if (trangThai === TRANG_THAI_PHIEU_NUMBER.CHO_DUYET) {
        if (!refValuesClone?.mucDo) {
          message.error("Vui lòng chọn mức độ nghiêm trọng của phản ứng!");
          return;
        }
        if (!refValuesClone?.ketQua) {
          message.error("Vui lòng chọn kết quả sau khi xử trí phản ứng!");
          return;
        }
        if (Array.isArray(refValuesClone?.dsThuocGayAdr)) {
          const invalidRows = refValuesClone?.dsThuocGayAdr
            .map((row, index) => {
              const hasThuoc = !!row?.ten || !!row?.hamLuong; // cột 13
              const hasKetQua =
                row?.caiThienSauKhiNgung && row.caiThienSauKhiNgung.length > 0; // cột 14
              const hasTaiSuDung =
                row?.tinhTrangSauKhiTaiSuDung &&
                row.tinhTrangSauKhiTaiSuDung.length > 0; // cột 15

              if (hasThuoc && (!hasKetQua || !hasTaiSuDung)) {
                return index + 1; // STT
              }
              return null;
            })
            .filter((x) => x !== null);

          if (invalidRows.length > 0) {
            message.error(
              `Vui lòng nhập mục 14 và 15 khi đã khai báo thuốc ở mục 13 (dòng ${invalidRows.join(
                ", "
              )})`
            );
            return;
          }
        }
      }

      if (trangThai == TRANG_THAI_PHIEU_NUMBER.HOAN_THANH) {
        const thoiGianDuyetPhieu = moment().format("YYYY-MM-DD HH:mm:ss");

        updateEditor(editorId, {
          fileData: {
            ...refValuesClone,
            trangThaiPhieu: [trangThai],
            thoiGianDuyetPhieu,
          },
        });
      } else {
        updateEditor(editorId, {
          fileData: { ...refValuesClone, trangThaiPhieu: [trangThai] },
        });
      }

      setTimeout(() => {
        onSaveData(false);
      }, 500);
    };

    return (
      <>
        {trangThaiPhieu === TRANG_THAI_PHIEU_NUMBER.TAO_MOI && (
          <AuthWrapper
            accessRoles={[ROLES["QUAN_LY_BAO_CAO_ADR"].GUI_DUYET_BAO_CAO]}
          >
            <Button
              onClick={onClickBtn(TRANG_THAI_PHIEU_NUMBER.CHO_DUYET)}
              type={"primary"}
              rightIcon={<SVG.IcSend color={"var(--color-blue-primary)"} />}
            >
              {"Gửi duyệt"}
            </Button>
          </AuthWrapper>
        )}

        {trangThaiPhieu === TRANG_THAI_PHIEU_NUMBER.CHO_DUYET && (
          <AuthWrapper
            accessRoles={[ROLES["QUAN_LY_BAO_CAO_ADR"].HUY_GUI_DUYET_BAO_CAO]}
          >
            <Button
              onClick={onClickBtn(TRANG_THAI_PHIEU_NUMBER.TAO_MOI)}
              type={"default"}
            >
              {"Hủy gửi duyệt"}
            </Button>
          </AuthWrapper>
        )}

        {trangThaiPhieu === TRANG_THAI_PHIEU_NUMBER.CHO_DUYET && (
          <AuthWrapper
            accessRoles={[ROLES["QUAN_LY_BAO_CAO_ADR"].DUYET_BAO_CAO]}
          >
            <Button
              onClick={onClickBtn(TRANG_THAI_PHIEU_NUMBER.HOAN_THANH)}
              type={"primary"}
              rightIcon={<SVG.IcSave />}
            >
              {"Duyệt"}
            </Button>
          </AuthWrapper>
        )}

        {trangThaiPhieu === TRANG_THAI_PHIEU_NUMBER.HOAN_THANH && (
          <AuthWrapper
            accessRoles={[ROLES["QUAN_LY_BAO_CAO_ADR"].HUY_DUYET_BAO_CAO]}
          >
            <Button
              onClick={onClickBtn(TRANG_THAI_PHIEU_NUMBER.CHO_DUYET)}
              type={"default"}
            >
              {"Hủy duyệt"}
            </Button>
          </AuthWrapper>
        )}
      </>
    );
  };

  const onSaoChepPhieu = () => {
    refModalSaoChepPhieu.current &&
      refModalSaoChepPhieu.current.show({
        api: fileOnShow?.api,
        data: fileData,
      });
  };

  const renderBtnSaoChep = () => {
    return (
      <Button
        rightIcon={<SVG.IcSaoChep />}
        minWidth={80}
        onClick={onSaoChepPhieu}
      >
        {t("common.saoChep")}
      </Button>
    );
  };

  const renderActionAntButton = () => {
    return (
      fileOnShow &&
      !isPreview && (
        <div className={"file-system-tool"}>
          {fileOnShow?.ma === "EMR_BA049" && (
            <Button
              type={"primary"}
              className={"item-tool text-btn"}
              onClick={exportXml}
            >
              {"Xuất HL7"}
            </Button>
          )}
          {fileOnShow?.ma === "EMR_BA275" && renderBtnAdr()}
          {thongTinKy?.chuKy1?.trangThai >= 50 && renderButtonXemLsKy()}
          {fileOnShow?.ma === "EMR_BA362" && renderBtnSaoChep()}
          {renderButtonGuiGiamDinh()}
          {renderButtonViewHistory()}
          {renderButtonDowload()}
          {renderButtonPrint()}
          {renderConfigForm()}
          {renderXoaPhieu()}

          <Button
            type="primary"
            rightIcon={<SVG.IcSave />}
            minWidth={80}
            loading={isSaveFormLoading}
            onClick={onClickSaveData}
            ref={refButtonSave}
            // type={"primary"}
          >
            {t("common.luu")} [F4]
            {/* {fileOnShow?.sign &&
              !fileOnShow?.editMode &&
              savedId != fileOnShow?.nbHoSoBaId
                ? "Chỉnh sửa"
                : "Lưu"} */}
          </Button>
        </div>
      )
    );
  };

  const renderStyleButtonTool = () => {
    return (
      <div className={"editor-tool"}>
        {windowWidth <= 450 ? (
          <>
            <Popover
              placement="topRight"
              content={
                <PopupTool>
                  {renderSelectFontSize()}
                  {renderButtonBold()}
                  {renderButtonItalic()}
                  {renderButtonUnderline()}
                </PopupTool>
              }
              trigger="click"
              style={{ alignItems: "flex-end" }}
            >
              <div
                style={{ flex: 1, justifyContent: "flex-end", display: "flex" }}
              >
                <AntButton
                  icon={<MoreOutlined />}
                  size={"small"}
                  className={"item-tool"}
                />
              </div>
            </Popover>
          </>
        ) : (
          <>
            {renderSelectFontSize()}
            {renderButtonBold()}
            {renderButtonItalic()}
            {renderButtonUnderline()}
            {renderPickColor()}
            {renderPickColorBackground()}
          </>
        )}
      </div>
    );
  };

  const content = () => {
    const handleSelectTemplate = (template) => {
      setState({
        bieuMauTemplate: combineFields(template),
      });
    };
    const handleEditTemplate = (item) => {
      window.open(
        `/editor/template/${item?.baoCaoId || file?.id}?templateId=${item?.id}`
      );
    };
    const handleDeleteTemplate = (item) => {
      showConfirm(
        {
          title: t("common.xoaDuLieu"),
          content: `${t("editor.xacNhanXoaMauBieuMau")} ${item?.ten}?`,
          cancelText: t("common.quayLai"),
          okText: t("common.dongY"),
          classNameOkText: "button-error",
          showImg: true,
          showBtnOk: true,
        },
        () => {
          deleteTemplateBieuMau(editorId, { api: file?.api, id: item.id }).then(
            (s) => {
              console.log("success");
            }
          );
        }
      );
    };
    const handleClick = (type) => () => {
      if (!state.bieuMauTemplate && type !== 3) {
        message.error(t("editor.vuiLongChonDuLieuMau"));
        return;
      }
      switch (type) {
        case 1:
          showConfirm(
            {
              title: t("common.thongBao"),
              content: `${t("editor.banCoChacChanMuonDungMau")}: <b>${
                state.bieuMauTemplate?.ten
              }</b>. ${t("editor.chonDongYSeThayDoiNoiDungTrenPhieu")}`,
              cancelText: t("common.huy"),
              okText: t("common.dongY"),
              classNameOkText: "button-error",
              showBtnOk: true,
              typeModal: "warning",
            },
            () => {
              let bieuMauTemplateClone = cloneDeep(state.bieuMauTemplate);
              let fileDataClone = cloneDeep(fileData);

              Object.keys(bieuMauTemplateClone).forEach((key) => {
                if (!bieuMauTemplateClone[key] || key === "id") {
                  delete bieuMauTemplateClone[key];
                }
              });
              Object.keys(bieuMauTemplateClone).forEach((key) => {
                fileDataClone[key] = bieuMauTemplateClone[key];
              });

              //biến áp dụng biểu mẫu để update các form check theo số lượng keys
              refLuotApDungBieuMau.current += 1;
              fileDataClone[
                `luotBieuMauApDung_${refLuotApDungBieuMau.current}`
              ] = refLuotApDungBieuMau.current;

              updateEditor(editorId, {
                fileData: fileDataClone,
              });
            }
          );
          break;
        case 2:
          showConfirm(
            {
              title: t("common.thongBao"),
              content: `${t("editor.banCoChacChanMuonDungMau")}: <b>${
                state.bieuMauTemplate?.ten
              }</b>. ${t("editor.chonDongYSeThayDoiNoiDungTrenPhieu")}`,
              cancelText: t("common.huy"),
              okText: t("common.dongY"),
              classNameOkText: "button-error",
              showBtnOk: true,
              typeModal: "warning",
            },
            () => {
              let refValuesClone = combineFields(
                handleCopyBieuMau({
                  maBaoCao,
                  bieuMauTemplateState: state.bieuMauTemplate,
                  refValues,
                })
              );
              //biến áp dụng biểu mẫu để update các form check theo số lượng keys
              refLuotApDungBieuMau.current += 1;
              refValuesClone[
                `luotBieuMauApDung_${refLuotApDungBieuMau.current}`
              ] = refLuotApDungBieuMau.current;
              updateEditor(editorId, {
                fileData: refValuesClone,
              });
            }
          );

          break;
        case 3:
          setState({
            bieuMauTemplate: null,
          });
          break;
        default:
          break;
      }
    };
    return (
      <div className="box">
        <div className="name-bieu-mau">{file?.ten}</div>
        <div className="list-template">
          {listTemplate.map((item, index) => {
            return (
              <div className="template" key={index}>
                <div
                  className="name"
                  onClick={() => handleSelectTemplate(item)}
                >
                  {item?.ten}
                </div>
                <div className="action-item">
                  <span className="edit">
                    <Button
                      width={100}
                      height={30}
                      style={{ zIndex: 100 }}
                      onClick={() => handleEditTemplate(item)}
                    >
                      {t("common.sua")}
                    </Button>
                  </span>
                  <span className="delete" style={{ zIndex: 100 }}>
                    <Button
                      height={30}
                      type="error"
                      onClick={() => handleDeleteTemplate(item)}
                    >
                      {t("common.xoa")}
                    </Button>
                  </span>
                </div>
              </div>
            );
          })}
        </div>
        <Row className="action" gutter={10}>
          <Col span={4}>
            <Button style={{ width: "100%" }} onClick={handleClick(3)}>
              {t("common.huy")}
            </Button>
          </Col>
          <Col span={8}>
            <Button style={{ width: "100%" }} onClick={handleClick(1)}>
              {t("editor.apToanBoBieuMau")}
            </Button>
          </Col>
          <Col span={12}>
            <Button style={{ width: "100%" }} onClick={handleClick(2)}>
              {t("editor.apNhungTruongChuaCoNoiDung")}
            </Button>
          </Col>
        </Row>
      </div>
    );
  };

  const handleClickOption = (value) => () => {
    const removeDataCss = (data) => {
      delete data.chiSoSongId;
      delete data.nbChiSoSongId;
      [
        "nbDotDieuTriId",
        "id",
        "active",
        "createdAt",
        "updatedAt",
        "createdBy",
        "updatedBy",
        "nguoiThucHienId",
        "nguoiThucHien",
        "chiDinhTuLoaiDichVu",
        "chiDinhTuDichVuId",
        "phanLoai",
        "khoaChiDinhId",
      ].forEach((key) => {
        if (data.chiSoSong) {
          delete data.chiSoSong[key];
        }
        if (data.nbChiSoSong) {
          delete data.nbChiSoSong[key];
        }
      });
    };
    switch (value) {
      case 1:
        changeShowPopover();
        break;
      case 2:
        window.open(`/editor/template/${file?.id}`);
        break;
      case 3:
        if (refModalName.current.show) {
          refModalName.current.show({}, (text) => {
            let cloneData = {};
            if (
              ["EMR_BA318", "EMR_HSDD067", "EMR_HSDD091"].includes(maBaoCao)
            ) {
              cloneData = { dsChamSoc: refValues.current.dsChamSoc };
              (cloneData.dsChamSoc || []).forEach((theoDoi) => {
                (theoDoi.dsChiTiet || []).forEach((chiTiet) => {
                  if (chiTiet?.chiSoSong && chiTiet?.chiSoSongId) {
                    removeDataCss(chiTiet);
                  }
                });
              });
            } else if (
              ["EMR_HSDD093", "EMR_HSDD094", "EMR_HSDD094.1"].includes(maBaoCao)
            ) {
              cloneData = { dsTheoDoi: refValues.current.dsTheoDoi };
              (cloneData.dsTheoDoi || []).forEach((theoDoi) => {
                delete theoDoi.dsChiSoSongCuId;
                (theoDoi.dsChiTiet || []).forEach((chiTiet) => {
                  if (chiTiet?.chiSoSong && chiTiet?.chiSoSongId) {
                    removeDataCss(chiTiet);
                  }
                });
              });
            } else if (["EMR_BA357"].includes(maBaoCao)) {
              cloneData = { dsTheoDoi: refValues.current.dsTheoDoi };
              (cloneData.dsTheoDoi || []).forEach((theoDoi) => {
                if (theoDoi?.nbChiSoSongId && theoDoi?.nbChiSoSongId) {
                  removeDataCss(theoDoi);
                }
              });
            } else if (["EMR_BA362"].includes(maBaoCao)) {
              cloneData = { dsTheoDoi: refValues.current.dsTheoDoi };
              (cloneData.dsTheoDoi || []).forEach((theoDoi) => {
                if (theoDoi?.chiSoSong && theoDoi?.chiSoSongId) {
                  removeDataCss(theoDoi);
                }
              });
            } else if (maBaoCao === "EMR_BA093") {
              cloneData = [
                "dsTheoDoi",
                "dsThuoc",
                "dsThuocTruyenDich",
                "dsDvChePhamMau",
              ].reduce(
                (prev, cur) => ({ ...prev, [cur]: refValues.current[cur] }),
                {}
              );
              (cloneData.dsTheoDoi || []).forEach((theoDoi) => {
                (theoDoi.dsChiSoSong || []).forEach((chiSoSong) => {
                  ["id"].forEach((key) => {
                    delete chiSoSong[key];
                  });
                });
              });
            } else if (maBaoCao === "EMR_HSDD095") {
              cloneData = { dsTheoDoi: refValues.current.dsTheoDoi };
            } else {
              cloneData = refValues.current;
            }
            createTemplateBieuMau(editorId, {
              ...cloneData,
              api: file?.api,
              baoCaoId: file?.id,
              ten: text,
            }).then(async (s) => {
              await getTemplateBieuMau(editorId, {
                baoCaoId: file?.id,
                api: file.apiTemplate || file?.api,
              });
              if (s?.data?.id) {
                window.open(
                  `/editor/template/${
                    s.data?.baoCaoId || file?.id
                  }?templateId=${s.data?.id}`
                );
              }
              changeShowPopover();
            });
          });
        }
        break;
      default:
        break;
    }
  };
  const contentCreateTemplate = () => {
    return (
      <div className="content-create-template">
        <div className="title">{t("editor.themMoiBieuMau")}</div>
        <div className="label">{t("editor.luaChonTuongUng")}</div>
        <ul>
          <li>
            - <b>{t("editor.taoTuBieuMauHienTai")}:</b>{" "}
            {t("editor.taoMauMoiTuNoiDungBieuMau")}
            {t("editor.hienTaiDangCo")}{" "}
          </li>
          <li>
            - <b>{t("editor.taoMauMoi")}:</b>{" "}
            {t("editor.tuNhapMoiHoanToanNoiDung")}
          </li>
          <li>
            - <b>{t("common.huy")}:</b> {t("editor.huyThaoTac")}
          </li>
        </ul>
        <div className="action">
          <Button onClick={handleClickOption(1)}>{t("common.huy")}</Button>
          <Button onClick={handleClickOption(2)}>
            {t("editor.taoMauMoi")}
          </Button>
          <Button onClick={handleClickOption(3)}>
            {t("editor.taoTuBieuMauHienTai")}
          </Button>
        </div>
      </div>
    );
  };
  const changeShowPopover = () => {
    setState({
      visibalPopover: !state.visibalPopover,
    });
  };
  const renderOptionRight = () => {
    return (
      <div className="editor-tool-right">
        <div className="title">{t("editor.mauNoiDung")}</div>
        <div>
          <Popover
            overlayClassName="popover-template"
            style={{ width: "400px" }}
            placement="bottomRight"
            content={content}
          >
            <Button width={150}>
              <div>
                {state.bieuMauTemplate
                  ? state.bieuMauTemplate?.ten
                  : t("editor.chonDuLieuMau")}
              </div>
            </Button>
          </Popover>
        </div>
        <Popover
          overlayClassName="popover-create-template"
          style={{ width: "400px" }}
          placement="bottomRight"
          content={contentCreateTemplate}
          open={state.visibalPopover}
          trigger="click"
        >
          <Button
            type="primary"
            size={"small"}
            minWidth={120}
            onClick={changeShowPopover}
          >
            {t("editor.taoMauMoi")}
          </Button>
        </Popover>
      </div>
    );
  };

  const refIsObserving = useRef();

  const callback = debounce((observer) => {
    hideLoading();
    setState({
      isLoadingDataPhieu: false,
    });
    observer.disconnect();
    refIsObserving.current = false;
  }, 1500);

  useEffect(() => {
    if (refIsObserving.current) return;
    refIsObserving.current = true;

    let hasHTMLChanged = false;

    // Observer để theo dõi thay đổi HTML
    let observer = new MutationObserver((mutationList, observer) => {
      hasHTMLChanged = true;
      callback(observer);
    });

    const targetNode = document.querySelector(".form-content");
    const config = { attributes: true, childList: true, subtree: true };

    if (targetNode) {
      observer.observe(targetNode, config);
    }

    // Set timeout để kiểm tra sau 1.5s
    const timeoutId = setTimeout(() => {
      // Nếu sau 1.5s mà HTML không thay đổi
      if (!hasHTMLChanged) {
        hideLoading();
        setState({
          isLoadingDataPhieu: false,
        });
        observer.disconnect();
        refIsObserving.current = false;
      }
    }, 1500);

    return () => {
      clearTimeout(timeoutId);
      observer.disconnect();
      refIsObserving.current = false;
    };
  }, [fileData]); // Khi fileData thay đổi
  return (
    <Main>
      <GlobalStyle />
      <div className="toolbar">
        {windowWidth >= 650 ? (
          <div style={{ display: "flex" }}>
            <div
              className={"toolbar-left"}
              style={{ borderRightWidth: 0, width: "100%" }}
            >
              <div style={{ display: "flex" }}>
                {renderZoomTool()}
                {renderActionAntButton()}
              </div>
            </div>
          </div>
        ) : (
          <>
            {renderActionAntButton()}
            {renderZoomTool()}
          </>
        )}

        <div className={"toolbarStyle1"}>
          <div style={{ marginTop: 0, width: "100%", display: "flex" }}>
            {renderStyleButtonTool()}
            {!isPreview && !isTemplate && renderOptionRight()}
          </div>
        </div>
      </div>
      <LazyLoad
        component={() => import("../ModalAddNameTemplate")}
        ref={refModalName}
      />
      <ModalLichSuKy ref={refLsKy} />
      <ModalXemLichSu ref={refModalXemLichSu} />
      <ModalSaoChepPhieu ref={refModalSaoChepPhieu} />
    </Main>
  );
};

export default memo(Toolbar);
