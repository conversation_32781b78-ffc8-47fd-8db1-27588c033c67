import React, {
  useImperative<PERSON>andle,
  useState,
  forwardRef,
  useRef,
  useEffect,
} from "react";
import { Main } from "./styled";
import { Col, InputNumber, Row, Spin, message, Input } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { SignaturePad, Button, ModalTemplate, Select } from "components";
import { useTranslation } from "react-i18next";
import { SVG } from "assets";
import isofhToolProvider from "data-access/isofh-tool-provider";
import { useFingerprint, useThietLap } from "hooks";
import { showError } from "utils/message-utils";
import { DS_LOAI_KY, THIET_LAP_CHUNG } from "constants/index";
import { useMemo } from "react";

const ModalPatientSign = (props, ref) => {
  const { getVanTay } = useFingerprint();
  const refCanvas = useRef(null);
  const refCallback = useRef(null);
  const refModal = useRef(null);
  const { t } = useTranslation();
  const [state, _setState] = useState({});
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };
  const {
    signer: { updateData },
  } = useDispatch();
  const isSigning = useSelector((state) => state.signer.isSigning || false);

  const [dataHIEN_THI_KY_SO_NB] = useThietLap(
    THIET_LAP_CHUNG.HIEN_THI_KY_SO_NB,
    "FALSE"
  );

  useImperativeHandle(ref, () => ({
    show: (data = {}, callback) => {
      refCallback.current = callback;
      if (refCanvas.current) {
        refCanvas.current.clear();
      }
      setState({
        show: true,
        loaiKy: dataHIEN_THI_KY_SO_NB.eval() ? 0 : 1,
        fileChuyenDoi: data.fileChuyenDoi,
        tenNguoiKy: data.tenNguoiKy,
        anhKy: data.anhKy,
        soDienThoai: data.soDienThoai,
      });
      updateData({
        isSigning: false,
      });
    },
  }));
  useEffect(() => {
    if (refCanvas.current) {
      refCanvas.current.height = 500;
    }
  }, [refCanvas.current]);

  useEffect(() => {
    if (state.show) {
      refModal.current && refModal.current.show();
    } else {
      refModal.current && refModal.current.hide();
      setState({
        fileChuyenDoi: null,
        soCanCuoc: null,
        anhKy: null,
        tenNguoiKy: null,
        soDienThoai: null,
      });
    }
  }, [state.show]);
  const onOK = (ok) => async () => {
    if (ok) {
      if ((!refCanvas.current || refCanvas.current.isEmpty()) && !state.anhKy && state.loaiKy) {
        message.error(t("editor.vuiLongVeChuKyCuaBan"));
      } else {
        if (refCallback.current) {
          let image = state.anhKy || refCanvas.current?.toDataURL() || "";
          if (!state.tenNguoiKy) {
            message.error(t("editor.vuiLongNhapMoiQuanHeVaHoTenNguoiKy"));
            return;
          }
          if (state.loaiKy === 0 && dataHIEN_THI_KY_SO_NB.eval()) {
            if (!state.soCanCuoc) {
              message.error(t("editor.vuiLongNhapSoCCCD"));
              return;
            }
            let fileChuyenDoi = null;
            if (state.fileChuyenDoi) {
              fileChuyenDoi = state.fileChuyenDoi;
            }
            if (window.onPrintForm) {
              fileChuyenDoi = await window.onPrintForm(true);
            }
            refCallback.current({
              soCanCuoc: state.soCanCuoc,
              fileChuyenDoi,
              tenNguoiKy: state.tenNguoiKy,
              soDienThoai: state.soDienThoai,
            });
            setState({ show: false });
          } else {
            refCallback.current({ image, tenNguoiKy: state.tenNguoiKy });
            setState({ show: false });
          }
        }
      }
    } else {
      setState({ show: false });
    }
  };
  const onReset = () => {
    if (refCanvas.current) {
      refCanvas.current.clear();
    }
    setState({
      anhKy: null,
    });
  };
  const onSignFingerprint = () => {
    if (!state.tenNguoiKy) {
      message.error(t("editor.vuiLongNhapMoiQuanHeVaHoTenNguoiKy"));
      return;
    }
    getVanTay({ api: isofhToolProvider.getVanTay }, (e, data) => {
      if (e) {
        showError(e?.message);
      } else {
        if (!data?.anhVanTay) {
          showError(t("common.layVanTayKhongThanhCong"));
        } else {
          refCallback.current({
            image: data?.anhVanTay,
            tenNguoiKy: state.tenNguoiKy,
          });
          setState({ show: false });
        }
      }
    });
  };

  const onChange = (key) => (value) => {
    if (key === "loaiKy" && value) {
      setState({
        soCanCuoc: null,
        loaiKy: value,
      });
    } else {
      setState({
        [key]: value,
      });
    }
  };

  const onChangeSoCanCuoc = (e) => {
    // chỉ giữ số, tối đa 12 ký tự
    const onlyDigits = e.target.value.replace(/\D/g, "").slice(0, 12);
    setState({ soCanCuoc: onlyDigits });
  };
  const onChangeSoDienThoai = (e) => {
    // chỉ giữ số, tối đa 12 ký tự
    const onlyDigits = e.target.value.replace(/\D/g, "").slice(0, 10);
    setState({ soDienThoai: onlyDigits });
  };
  const onChangeTenNguoiKy = (e) => {
    setState({ tenNguoiKy: e.target.value });
  };

  const isShowBtnKyVanTay = useMemo(() => {
    return (
      !dataHIEN_THI_KY_SO_NB.eval() ||
      (dataHIEN_THI_KY_SO_NB.eval() && state.loaiKy)
    );
  }, [state.loaiKy, dataHIEN_THI_KY_SO_NB]);
  return (
    <ModalTemplate
      ref={refModal}
      onCancel={onOK(false)}
      width={445}
      title={t("editor.kyTen")}
      actionLeft={
        <Button minWidth={100} onClick={onReset} leftIcon={<SVG.IcCancel />}>
          {t("editor.kyLai")}
        </Button>
      }
      actionRight={
        <>
          {isShowBtnKyVanTay && !state.anhKy ? (
            <Button
              type="primary"
              minWidth={100}
              onClick={onSignFingerprint}
              leftIcon={<SVG.IcFingerPrint />}
            >
              {t("common.kyVanTay")}
            </Button>
          ) : null}

          <Button
            type="primary"
            minWidth={100}
            onClick={onOK(true)}
            leftIcon={<SVG.IcSign />}
          >
            {t("editor.ky")}
          </Button>
        </>
      }
    >
      <Main>
        <Spin spinning={isSigning}>
          <div className="modal-des">
            <Row className="content-des" gutter={[12, 12]}>
              {dataHIEN_THI_KY_SO_NB.eval() ? (
                <>
                  <Col span={24}>{t("editor.loaiKy")}</Col>
                  <Select
                    data={[
                      { ten: t("editor.kySo"), id: 0 },
                      { ten: t("editor.kyDienTu"), id: 1 },
                    ]}
                    value={state.loaiKy || 0}
                    onChange={onChange("loaiKy")}
                    allowClear={false}
                  ></Select>
                  {state.loaiKy === 0 ? (
                    <>
                      <Col span={24}>{t("tiepDon.CMT_CCCD")}</Col>
                      <Col span={24}>
                        <Input
                          placeholder="Nhập 12 số"
                          inputMode="numeric" // mở bàn phím số trên mobile
                          pattern="\d*" // gợi ý chỉ nhập số
                          maxLength={12} // chặn vượt quá 12 ký tự
                          allowClear
                          value={state.soCanCuoc}
                          onChange={onChangeSoCanCuoc}
                        />
                      </Col>
                      <Col span={24}>{t("tiepDon.soDienThoai")}</Col>
                      <Col span={24}>
                        <Input
                          placeholder="Nhập 10 số"
                          inputMode="numeric" // mở bàn phím số trên mobile
                          pattern="\d*" // gợi ý chỉ nhập số
                          maxLength={10} // chặn vượt quá 10 ký tự
                          allowClear
                          value={state.soDienThoai}
                          onChange={onChangeSoDienThoai}
                        />
                      </Col>
                    </>
                  ) : null}
                </>
              ) : (
                <div></div>
              )}
              <Col span={24}>{t("editor.moiQuanHe")}</Col>
              <Col span={24}>
                <Input
                  placeholder={t("editor.nhapMoiQuanHeNguoiKy")}
                  allowClear
                  value={state.tenNguoiKy}
                  onChange={onChangeTenNguoiKy}
                />
                <div>
                  <i>{t("editor.viDuChaNguyenVanA")}</i>
                </div>
              </Col>
              {isShowBtnKyVanTay ? (
                <Col span={24}>
                  {!state.anhKy && (
                    <SignaturePad
                      redrawOnResize={true}
                      ref={refCanvas}
                      width={400}
                      height={200}
                      options={{
                        minWidth: 0.05,
                        maxWidth: 2,
                        penColor: "#000080",
                      }}
                    />
                  )}
                </Col>
              ) : null}
              {state.anhKy ? (
                <Col span={24}>
                  <img className="image-sign" src={state.anhKy} alt="" />
                </Col>
              ) : null}
            </Row>
          </div>
        </Spin>
      </Main>
    </ModalTemplate>
  );
};

export default forwardRef(ModalPatientSign);
