import React, { useMemo, useRef } from "react";
import { Main } from "./styled";
import { useDispatch, useSelector } from "react-redux";
import { message } from "antd";
import { <PERSON><PERSON>, AuthWrapper } from "components";
import { useHistory, useParams } from "react-router-dom";
import ModalNhapLyDo from "pages/khoMau/components/ModalNhapLyDo";
import ModalCapNhapNhomMauNb from "pages/khoMau/components/ModalCapNhapNhomMauNb";
import ModalDoiKhoPhatMau from "pages/khoMau/components/ModalDoiKhoPhatMau";
import { SVG } from "assets";
import { ROLES, THIET_LAP_CHUNG, TRANG_THAI_MAU } from "constants/index";
import { t } from "i18next";
import { useConfirm, useLoading, useThietLap } from "hooks";
import { isArray } from "utils/index";

const HeaderTruyenPhatMau = ({ title, isChePhamMau = false }) => {
  const history = useHistory();
  const { id, nbDotDieuTriId, trangThai } = useParams();
  const refModalNhapLyDo = useRef(null);
  const refModalCapNhapNhomMauNb = useRef(null);
  const refModalDoiKhoPhatMau = useRef(null);
  const { showLoading, hideLoading } = useLoading();
  const { listSelected, listChePhamMau, soPhieu, listEditChePhamMau } =
    useSelector((state) => state.truyenPhatMau);
  const [NHOM_MAU_NB_KHAC_NHOM_MAU_PHAT] = useThietLap(
    THIET_LAP_CHUNG.NHOM_MAU_NB_KHAC_NHOM_MAU_PHAT
  );
  const [dataKHONG_BAT_BUOC_DIEN_MA_TUI_MAU_NHOM_MAU_NGUOI_PHAT] = useThietLap(
    THIET_LAP_CHUNG.KHONG_BAT_BUOC_DIEN_MA_TUI_MAU_NHOM_MAU_NGUOI_PHAT
  );
  const { showConfirm } = useConfirm();

  const hienThiCapNhatNhomMauNb = useMemo(() => {
    if (!isArray(listChePhamMau, 1)) return false;
    const record = listChePhamMau[0];
    return (
      !record.nhomMauNb || record.nhomMauNb === 9 || record.nhomMauNb === 27
    );
  }, [listChePhamMau]);

  const khoId = useMemo(() => {
    return isArray(listChePhamMau, 1) ? listChePhamMau[0]?.khoId : undefined;
  }, [listChePhamMau]);

  const { phatMau, huyPhatMau, getDsChePhamMau, themThongTin } =
    useDispatch().truyenPhatMau;

  const validateChePhamMau = () => {
    if (!isChePhamMau) return false;

    if (!listSelected || listSelected.length === 0) {
      message.error(t("khoMau.vuiLongChonChePhamMau"));
      return true;
    }

    return listSelected.some((item) => {
      if (!item.kemChePhamMau && item.trangThai === TRANG_THAI_MAU.THUONG) {
        if (!item.maTuiMau) {
          message.error(t("khoMau.vuiLongChonMaTuiMau"));
          return true;
        }
        if (!item.nguoiPhat1Id) {
          message.error(t("khoMau.vuiLongChonNguoiPhatMau1"));
          return true;
        }
      }
      if (dataKHONG_BAT_BUOC_DIEN_MA_TUI_MAU_NHOM_MAU_NGUOI_PHAT?.eval()) {
        if (!item.dichVuId) {
          message.error(t("khoMau.vuiLongChonDichVuChePhamMau"));
          return true;
        }
        if (!item.maTuiMau) {
          message.error(t("khoMau.vuiLongChonMaTuiMau"));
          return true;
        }
        if (!item.nhomMau) {
          message.error(t("khoMau.vuiLongChonNhomMauPhat"));
          return true;
        }
        if (!item.nguoiPhat1Id) {
          message.error(t("khoMau.vuiLongChonNguoiPhatMau1"));
          return true;
        }
      }
      return false;
    });
  };

  const onPhatMau = async () => {
    if (validateChePhamMau()) {
      return;
    }

    const idPhieuXuat = listSelected[0].phieuNhapXuatId;

    const executePhatMau = async () => {
      if (listEditChePhamMau && listEditChePhamMau.length > 0) {
        const payload = (listEditChePhamMau || [])
          .filter((x) => x.isEdit)
          .map((item) => ({
            id: item?.id,
            nbDotDieuTriId: item?.nbDotDieuTriId,
            duongDungId: item?.duongDungId,
            dotDung: item?.dotDung,
            maTuiMau: item.maTuiMau,
            nhomMau: item.nhomMau,
            nhomMauNb: item.nhomMauNb,
            nguoiPhat1Id: item.nguoiPhat1Id,
            nguoiPhat2Id: item.nguoiPhat2Id,
            nguoiNhanId: item.nguoiNhanId,
            sotRet: item.sotRet,
            giangMai: item.giangMai,
            hcv: item.hcv,
            hbv: item.hbv,
            hiv: item.hiv,
            ngaySanXuat: item.ngaySanXuat,
            ngayHanSuDung: item.ngayHanSuDung,
            muoi1: item.muoi1,
            muoi2: item.muoi2,
            phanUngCheo1: item.phanUngCheo1,
            phanUngCheo2: item.phanUngCheo2,
            globulin1: item.globulin1,
            globulin2: item.globulin2,
            nbDichVu: {
              ghiChu: item?.ghiChu,
              soLuong: item?.soLuong,
              tuTra: item?.tuTra,
              khongTinhTien: item?.khongTinhTien,
              thoiGianThucHien: item.thoiGianThucHien,
              dichVuId: item?.dichVuId,
              chiDinhTuDichVuId: item?.chiDinhTuDichVuId,
              chiDinhTuLoaiDichVu: item?.chiDinhTuLoaiDichVu,
              khoaChiDinhId: item?.khoaChiDinhId,
            },
            nbDvKho: {
              loNhapId: item?.loNhapId,
            },
          }));

        try {
          showLoading();
          await themThongTin(payload);
          await phatMau(idPhieuXuat);

          await getDsChePhamMau({
            nbDotDieuTriId,
            phieuNhapXuatId: id,
          });
        } catch (error) {
          console.log("error", error);
        } finally {
          hideLoading();
        }
      } else {
        try {
          showLoading();
          await phatMau(idPhieuXuat);
          await getDsChePhamMau({
            nbDotDieuTriId,
            phieuNhapXuatId: id,
          });
        } catch (error) {
          console.log("error", error);
        } finally {
          hideLoading();
        }
      }
    };

    if (
      NHOM_MAU_NB_KHAC_NHOM_MAU_PHAT?.eval() &&
      listEditChePhamMau.some(
        (x) =>
          x.nhomMauNb &&
          x.nhomMau &&
          x.nhomMauNb != 9 &&
          x.nhomMauNb !== x.nhomMau
      )
    ) {
      showConfirm(
        {
          title: t("common.thongBao"),
          content: `${t("khoMau.nhomMauPhatKhongTrungVoiNhomMauNguoiBenh")}`,
          cancelText: t("common.huy"),
          classNameOkText: "button-warning",
          typeModal: "warning",
          showBtnOk: true,
        },
        () => {
          executePhatMau();
        }
      );
      return;
    }

    executePhatMau();
  };

  const onHuyPhatMau = () => {
    if (listSelected && listSelected.length > 0) {
      const idPhieuXuat = listSelected[0].phieuNhapXuatId;

      refModalNhapLyDo.current &&
        refModalNhapLyDo.current.show(
          {
            title: "Lý do hủy phát ",
            message: "Điền lý do hủy phát",
          },
          (lyDo) => {
            huyPhatMau({ id: idPhieuXuat, lyDo }).then(() => {
              // getDsChePhamMau({
              //   nbDotDieuTriId,
              //   phieuNhapXuatId: id,
              // });
              setTimeout(() => {
                history.go();
              }, 500);
            });
          }
        );
    } else {
      message.error("Vui lòng chọn chế phẩm máu!");
    }
  };

  const onCapNhatNhomMauNguoiBenh = () => {
    refModalCapNhapNhomMauNb.current?.show({ nbDotDieuTriId }, () => {
      return getDsChePhamMau({
        nbDotDieuTriId,
        phieuNhapXuatId: id,
      });
    });
  };

  const onDoiKhoPhatMau = () => {
    refModalDoiKhoPhatMau.current?.show({ khoId, phieuNhapXuatId: id }, () => {
      return getDsChePhamMau({
        khoId,
        phieuNhapXuatId: id,
      });
    });
  };

  return (
    <Main>
      <div>{`${title} - ${soPhieu}`}</div>
      <div>
        {/* Hiển thị khi tất cả dịch vụ được chọn ở trạng thái Đã duyệt (30) */}
        {/* {(listSelected || []).every((x) => x.trangThai == 30) && (
          <Button
            type="default"
            rightIcon={<IcHuy />}
            minWidth={100}
            onClick={onHuyDuyetMau}
          >
            {"Hủy duyệt máu"}
          </Button>
        )} */}
        {/* Button hủy phát máu Hiển thị khi có line máu ở trạng thái = Đã phát
          và Không có line máu nào ở trạng thái = Đã trả/Yêu cầu trả*/}
        {(listSelected || [])
          .filter(
            (o) =>
              ![TRANG_THAI_MAU.DA_TRA, TRANG_THAI_MAU.YEU_CAU_TRA].includes(
                o.trangThai
              )
          )
          .some((x) => x.trangThai == TRANG_THAI_MAU.DA_PHAT) && (
          <AuthWrapper accessRoles={[ROLES["KHO_MAU"].ACTION_TRUYEN_PHAT_MAU]}>
            <Button
              type="default"
              rightIcon={<SVG.IcCancel />}
              minWidth={100}
              onClick={onHuyPhatMau}
            >
              {t("khoMau.huyPhatMau")}
            </Button>
          </AuthWrapper>
        )}
        {/* Hiển thị khi 1 dịch vụ được chọn ở trạng thái Tạo mới (10) */}
        {hienThiCapNhatNhomMauNb && (
          <AuthWrapper accessRoles={[ROLES["KHO_MAU"].ACTION_TRUYEN_PHAT_MAU]}>
            <Button
              type="primary"
              minWidth={100}
              onClick={onCapNhatNhomMauNguoiBenh}
            >
              {t("khoMau.capNhatNhomMauNb")}
            </Button>
          </AuthWrapper>
        )}
        {(listSelected || []).some(
          (x) => x.trangThai == TRANG_THAI_MAU.THUONG
        ) && (
          <AuthWrapper accessRoles={[ROLES["KHO_MAU"].ACTION_TRUYEN_PHAT_MAU]}>
            <Button
              type="primary"
              rightIcon={<SVG.IcChePhamMau />}
              minWidth={100}
              onClick={onPhatMau}
            >
              {t("khoMau.phatMau")}
            </Button>
          </AuthWrapper>
        )}
        {trangThai != 30 && ( // enums TrangThaiPhieuNhapXuat
          <AuthWrapper accessRoles={[ROLES["KHO_MAU"].ACTION_TRUYEN_PHAT_MAU]}>
            <Button type="primary" minWidth={100} onClick={onDoiKhoPhatMau}>
              {t("khoMau.doiKhoPhatMau")}
            </Button>
          </AuthWrapper>
        )}
      </div>

      <ModalNhapLyDo ref={refModalNhapLyDo} />
      <ModalCapNhapNhomMauNb ref={refModalCapNhapNhomMauNb} />
      <ModalDoiKhoPhatMau ref={refModalDoiKhoPhatMau} />
    </Main>
  );
};

export default HeaderTruyenPhatMau;
