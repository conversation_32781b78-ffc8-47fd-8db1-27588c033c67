import styled from "styled-components";

export const Main = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  flex: 1;

  .dashboard {
    display: flex;
    justify-content: space-between;
    align-items: center;

    margin-bottom: 10px;
    padding-left: 12px;

    .check-all {
      &-text {
        font-weight: 800;
        font-size: 14px;
        line-height: 19px;
        color: #172b4d;
      }
    }

    .trangThai {
      display: flex;
      justify-content: flex-end;
      align-items: center;

      .info {
        display: inline-block;
        margin-left: 10px;
        padding: 8px 6px;
        border-radius: 8px;
        &--bold {
          font-weight: bold;
        }
      }
    }
  }

  .ant-collapse {
    border: none;

    > .ant-collapse-item {
      border-bottom: none;
      padding-bottom: 10px;
    }
  }

  & .tab-func {
    & .ant-collapse-header {
      background-color: #fff;
      border: none;
      padding: 0 !important;
    }
  }

  .ant-collapse-content {
    border: 2px solid rgba(7, 98, 247, 0.75);
    border-radius: 0px 0px 8px 8px;
    border-top: none;
  }

  .title-ketQua {
    font-weight: 700;
    font-size: 16px;
    line-height: 24px;

    color: #172b4d;
    margin-top: 20px;
    padding-left: 10px;
  }

  .panel {
    display: flex;
    justify-content: flex-start;
    align-items: center;

    &-header {
      flex: 0 0 40%;
      min-width: 400px;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      border-top: 2px solid rgba(7, 98, 247, 0.75);
      border-right: 2px solid rgba(7, 98, 247, 0.75);
      border-left: 2px solid rgba(7, 98, 247, 0.75);
      padding: 5px 10px;
      height: 32px;

      border-radius: 8px 8px 0 0;

      .anticon {
        padding: 0 5px;
        font-size: 24px;
      }

      .title {
        font-weight: 600;
        font-size: 14px;
        line-height: 19px;

        color: #172b4d;
      }

      &-space {
        flex: 1;
      }

      .btn-right {
        height: 25px;
      }
    }

    &-right {
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 2px solid rgba(7, 98, 247, 0.75);
      width: 100%;
    }

    &-state {
      flex: 1;
      display: flex;
      padding: 3px 10px;
      height: 32px;

      &-info {
        background: #c1d8fd;
        border-radius: 8px;
        font-size: 13px;
        font-weight: 600;
        color: #172b4d;
        padding: 2px 4px;
        margin-right: 10px;
      }
    }

    &-action {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      height: 32px;
    }
  }
`;
