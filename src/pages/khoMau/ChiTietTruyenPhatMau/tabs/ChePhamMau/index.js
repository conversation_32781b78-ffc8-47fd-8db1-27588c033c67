import React, { useEffect, useMemo, useState, memo, useRef } from "react";
import { Collapse, Typography } from "antd";
import TTChePhamMau from "pages/khoMau/ChiTietTruyenPhatMau/tabs/ChePhamMau/TTChePhamMau";
import { Main } from "./styled";
import Icon from "@ant-design/icons";
import { useDispatch } from "react-redux";
import {
  useConfirm,
  useEnum,
  useLoading,
  useThietLap,
  useQueryAll,
} from "hooks";
import {
  ENUM,
  LOAI_DICH_VU,
  ROLES,
  THIET_LAP_CHUNG,
  TRANG_THAI_MAU,
} from "constants/index";
import { Checkbox, Button, Tooltip, AuthWrapper, Popover } from "components";
import { useParams } from "react-router-dom";
import { SVG } from "assets";
import { t } from "i18next";
import ChiDinhDichVuVatTu from "pages/chiDinhDichVu/DichVuVatTu";
import { groupBy, uniqBy } from "lodash";
import useThongTinNb from "pages/phauThuatThuThuat/ChiTietNguoiBenh/hook/useThongTinNb";
import { useSelector } from "react-redux";
import { query } from "redux-store/stores";

const { Text } = Typography;

const { Panel } = Collapse;

const dashboardTrangThai = [
  {
    ten: t("cdha.thuong"),
    background: "#C1F3F7",
    trangThai: TRANG_THAI_MAU.THUONG,
  },
  {
    ten: t("common.daPhat"),
    background: "#C1D8FD",
    trangThai: TRANG_THAI_MAU.DA_PHAT,
  },
];

const ChePhamMau = ({ onSave }) => {
  const { showConfirm } = useConfirm();
  const refModalChiDinhVatTu = useRef(null);
  const [thongTinBenhNhan] = useThongTinNb();

  const {
    truyenPhatMau: {
      getDsChePhamMau,
      updateData,
      huyDuyetMau,
      getDsChePhamMauDaHuy,
      getDsXetNghiemMau,
      getDsVatTuMau,
      themThongTin,
    },
    duongDung: { getListAllDuongDung },
    nhanVien: { getListAllNhanVien },
    thietLapChonKho: { getListThietLapChonKhoTheoTaiKhoan },
    dichVuKyThuat: { getAll: getAllDichVuChePhamMau },
    chiDinhKhamBenh: { updateConfigData },
  } = useDispatch();

  const { listChePhamMau, listSelected } = useSelector(
    (state) => state.truyenPhatMau
  );

  const { id, nbDotDieuTriId } = useParams();
  const [listTrangThaiMau] = useEnum(ENUM.TRANG_THAI_MAU);

  const [state, _setState] = useState({
    activeKey: [],
  });
  const { showLoading, hideLoading } = useLoading();
  const [dataKHONG_CHO_XOA_CHI_DINH_MAU_KHI_DA_DIEN_MA_TUI_MAU] = useThietLap(
    THIET_LAP_CHUNG.KHONG_CHO_XOA_CHI_DINH_MAU_KHI_DA_DIEN_MA_TUI_MAU
  );
  const [dataMA_KHOA_HUYET_HOC] = useThietLap(
    THIET_LAP_CHUNG.MA_KHOA_HUYET_HOC
  );

  const { data: listAllKhoa } = useQueryAll(query.khoa.queryAllKhoa);

  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  useEffect(() => {
    if (nbDotDieuTriId && id) {
      getDsChePhamMau({
        nbDotDieuTriId,
        phieuNhapXuatId: id,
        dsTrangThaiHoan: [0, 10, 20],
      });
      getDsXetNghiemMau({
        nbDotDieuTriId,
        phieuNhapXuatId: id,
        page: 0,
      });
      getListAllDuongDung({
        page: "",
        size: "",
        active: true,
        sort: "ten,asc",
      });
      getListAllNhanVien({ page: "", size: "", active: true });
      getAllDichVuChePhamMau({
        page: 0,
        size: "",
        active: true,
        "dichVu.loaiDichVu": LOAI_DICH_VU.CHE_PHAM_MAU,
      });
    }
    return () => {
      updateData({ listChePhamMau: [] });
    };
  }, [id, nbDotDieuTriId]);

  const onSelectMau = (record) => (e) => {
    e.stopPropagation();

    if (e?.target?.checked) {
      updateData({
        listSelected: [...listSelected, record],
      });
    } else {
      updateData({
        listSelected: listSelected.filter((x) => x.id != record.id),
      });
    }
  };

  useEffect(() => {
    if (listChePhamMau && listChePhamMau.length > 0) {
      updateData({ listSelected: listChePhamMau });
    }
  }, [listChePhamMau]);

  const isCheckAll = useMemo(() => {
    if (
      listChePhamMau &&
      listChePhamMau.length > 0 &&
      listChePhamMau.length == listSelected.length
    )
      return true;

    return false;
  }, [listChePhamMau, listSelected]);

  const listSelectedIds = useMemo(() => {
    return listSelected.map((x) => x.id);
  }, [listSelected]);

  const onCheckAll = (e) => {
    if (e?.target?.checked) {
      updateData({ listSelected: listChePhamMau });
    } else {
      updateData({ listSelected: [] });
    }
  };

  const onChangeExpand = (e) => {
    let _activeKeys = state.activeKey;
    if (state.activeKey.includes(e)) {
      _activeKeys = state.activeKey.filter((x) => x != e);
    } else {
      _activeKeys.push(e);
    }

    setState({
      activeKey: _activeKeys,
    });
  };

  const onHuyDuyetMau = (record) => (e) => {
    e.preventDefault();
    e.stopPropagation();
    showLoading();
    huyDuyetMau([record.id])
      .then(() => {
        getDsChePhamMau({
          nbDotDieuTriId,
          phieuNhapXuatId: id,
          dsTrangThaiHoan: [0, 10, 20],
        });
        getDsChePhamMauDaHuy({ nbDotDieuTriId, phieuNhapXuatId: id });
      })
      .finally(() => hideLoading());
  };

  const onHuyLuu = (record) => (e) => {
    e?.preventDefault();
    e?.stopPropagation();

    showConfirm(
      {
        title: t("common.thongBao"),
        content: `${t("common.banCoChacMuon")} ${t(
          "khoMau.huyLuu"
        ).toLowerCase()} ${record?.maDichVu} - ${record?.tenDichVu}?`,
        cancelText: t("common.quayLai"),
        okText: t("common.dongY"),
        classNameOkText: "button-warning",
        showBtnOk: true,
        typeModal: "warning",
      },
      () => {
        onSave({ identifyIgnoreMaTuiMau: record.id });
      }
    );
  };

  const onChiDinhVatTu = (item) => async (e) => {
    e?.preventDefault();
    e?.stopPropagation();

    updateConfigData({
      configData: {
        nbDotDieuTriId,
        chiDinhTuDichVuId: item.id,
        khoaChiDinhId: thongTinBenhNhan?.khoaNbId,
        thongTinNguoiBenh: thongTinBenhNhan,
        nbThongTinId: thongTinBenhNhan.nbThongTinId,
        chiDinhTuLoaiDichVu: LOAI_DICH_VU.CHE_PHAM_MAU,
        thongTinNguoiBenh: thongTinBenhNhan,
        doiTuongKcb: thongTinBenhNhan.doiTuongKcb,
        phongThucHienId: thongTinBenhNhan?.phongId,
      },
    });

    let currentKhoa = listAllKhoa.find((x) => x.ma == dataMA_KHOA_HUYET_HOC);

    const listThietLapChonKho = await getListThietLapChonKhoTheoTaiKhoan({
      loaiDichVu: LOAI_DICH_VU.VAT_TU,
      khoaNbId: thongTinBenhNhan?.khoaNbId,
      khoaChiDinhId: currentKhoa?.id,
      doiTuong: thongTinBenhNhan?.doiTuong,
      loaiDoiTuongId: thongTinBenhNhan?.loaiDoiTuongId,
      capCuu: thongTinBenhNhan?.capCuu,
      phongId: thongTinBenhNhan?.phongId,
      noiTru: false,
      canLamSang: false,
    }).then((listThietLapChonKho) => {
      return listThietLapChonKho?.payload?.listThietLapChonKho || [];
    });

    const options = {
      dataKho: uniqBy(listThietLapChonKho || [], "id"),
      khoId:
        listThietLapChonKho?.length === 1 ? listThietLapChonKho[0]?.id : null,
    };
    const callbackFunc = () => {
      getDsVatTuMau({
        nbDotDieuTriId,
        phieuNhapXuatId: id,
        page: 0,
      });
    };
    refModalChiDinhVatTu.current &&
      refModalChiDinhVatTu.current.show(options, callbackFunc);
  };

  const handleSaveThongTin = (item) => async (e) => {
    e?.preventDefault();
    e?.stopPropagation();

    showLoading();
    const payload = {
      id: item?.id,
      nbDotDieuTriId: item?.nbDotDieuTriId,
      duongDungId: item?.duongDungId,
      dotDung: item?.dotDung,
      maTuiMau: item.maTuiMau,
      nhomMau: item.nhomMau,
      nhomMauNb: item.nhomMauNb,
      nguoiPhat1Id: item.nguoiPhat1Id,
      nguoiPhat2Id: item.nguoiPhat2Id,
      nguoiNhanId: item.nguoiNhanId,
      sotRet: item.sotRet,
      giangMai: item.giangMai,
      hcv: item.hcv,
      hbv: item.hbv,
      hiv: item.hiv,
      ngaySanXuat: item.ngaySanXuat,
      ngayHanSuDung: item.ngayHanSuDung,
      muoi1: item.muoi1,
      muoi2: item.muoi2,
      phanUngCheo1: item.phanUngCheo1,
      phanUngCheo2: item.phanUngCheo2,
      globulin1: item.globulin1,
      globulin2: item.globulin2,
      nbDichVu: {
        ghiChu: item?.ghiChu,
        soLuong: item?.soLuong,
        tuTra: item?.tuTra,
        khongTinhTien: item?.khongTinhTien,
        thoiGianThucHien: item.thoiGianThucHien,
        dichVuId: item?.dichVuId,
        chiDinhTuDichVuId: item?.chiDinhTuDichVuId,
        chiDinhTuLoaiDichVu: item?.chiDinhTuLoaiDichVu,
        khoaChiDinhId: item?.khoaChiDinhId,
      },
      nbDvKho: {
        loNhapId: item?.loNhapId,
      },
    };
    await themThongTin([payload]);
    hideLoading();
  };

  const renderContent = ({ listChePhamMauByTrangThai }) => {
    const groupByDichVuId = groupBy(listChePhamMauByTrangThai, "dichVuId");
    const listDichVu = Object.keys(groupByDichVuId).map((dichVuId) => {
      return {
        dichVuId,
        tenDichVu: groupByDichVuId[dichVuId][0].tenDichVu,
        soLuong: groupByDichVuId[dichVuId].length,
      };
    });
    return listDichVu.map((item) => {
      return (
        <div key={item.dichVuId}>
          <span>{item.tenDichVu}</span>: <Text strong>{item.soLuong}</Text>
        </div>
      );
    });
  };

  return (
    <Main>
      <div className="dashboard">
        <div className="check-all">
          <Checkbox checked={isCheckAll} onChange={onCheckAll}>
            <span className="check-all-text">
              {t("khoMau.chonTatCaChePhamMau")}
            </span>
          </Checkbox>
        </div>
        <div className="trangThai">
          {dashboardTrangThai.map((item, idx) => {
            const listChePhamMauByTrangThai = listChePhamMau.filter(
              (x) => x.trangThai === item.trangThai
            );

            const renderInnerContent = () => {
              return (
                <div className="info" style={{ background: item.background }}>
                  {item.ten}:{" "}
                  <span className="info--bold">
                    {listChePhamMauByTrangThai.length}
                  </span>
                </div>
              );
            };

            if (listChePhamMauByTrangThai.length === 0) {
              return renderInnerContent();
            }

            return (
              <Popover
                key={idx}
                content={renderContent({ listChePhamMauByTrangThai })}
              >
                {renderInnerContent()}
              </Popover>
            );
          })}
        </div>
      </div>
      {listChePhamMau && listChePhamMau.length > 0 ? (
        <Collapse
          className="tab-func"
          activeKey={(listChePhamMau || []).map((item) => item.id)}
        >
          {(listChePhamMau || [])
            .filter(
              (x) =>
                x.trangThai !== TRANG_THAI_MAU.HUY_DUYET &&
                !(
                  x.kemChePhamMau &&
                  x.chiDinhTuLoaiDichVu === LOAI_DICH_VU.CHE_PHAM_MAU
                )
            )
            .map((item, index) => {
              const _listDvKemTheo = (listChePhamMau || [])
                .filter(
                  (i) =>
                    i.kemChePhamMau &&
                    i.chiDinhTuDichVuId === item.id &&
                    i.chiDinhTuLoaiDichVu === LOAI_DICH_VU.CHE_PHAM_MAU
                )
                .map((mau, idx) => ({ ...mau, index: idx + 1 }));

              return (
                <Panel
                  header={
                    <div
                      className="panel"
                      onClick={() => onChangeExpand(item.id)}
                    >
                      <div className="panel-header">
                        <div onClick={onSelectMau(item)}>
                          <Checkbox
                            checked={(listSelectedIds || []).includes(item.id)}
                          />
                        </div>
                        <Icon
                          component={
                            state.activeKey.includes(item.id)
                              ? SVG.IcDown
                              : SVG.IcUp
                          }
                        />
                        <span className="title">
                          {`${index + 1}. `}
                          {t("khoMau.chePhamMau")}: {item.maDichVu}
                        </span>

                        <div className="panel-header-space"></div>

                        {item.trangThai == TRANG_THAI_MAU.THUONG && (
                          <AuthWrapper
                            accessRoles={[
                              ROLES["KHO_MAU"].ACTION_TRUYEN_PHAT_MAU,
                            ]}
                          >
                            <Button
                              type="default"
                              className="btn-right"
                              rightIcon={<SVG.IcCancel />}
                              minWidth={100}
                              onClick={onHuyDuyetMau(item)}
                            >
                              {t("khoMau.huyDuyetMau")}
                            </Button>
                          </AuthWrapper>
                        )}
                      </div>

                      <div className="panel-right">
                        <div className="panel-state">
                          <div className="panel-state-info">
                            {
                              (listTrangThaiMau || []).find(
                                (x) => x.id == item.trangThai
                              )?.ten
                            }
                          </div>
                          <div className="panel-state-action">
                            {item.trangThai == TRANG_THAI_MAU.THUONG &&
                              dataKHONG_CHO_XOA_CHI_DINH_MAU_KHI_DA_DIEN_MA_TUI_MAU?.eval() && (
                                <AuthWrapper
                                  accessRoles={[
                                    ROLES["KHO_MAU"].ACTION_TRUYEN_PHAT_MAU,
                                  ]}
                                >
                                  {" "}
                                  <Tooltip
                                    title={t("khoMau.huyLuu")}
                                    placement="bottom"
                                  >
                                    <SVG.IcHoanDv
                                      className="ic-action"
                                      onClick={onHuyLuu(item)}
                                    />
                                  </Tooltip>
                                </AuthWrapper>
                              )}
                          </div>
                        </div>
                        <AuthWrapper
                          accessRoles={[
                            ROLES["KHO_MAU"].ACTION_TRUYEN_PHAT_MAU,
                          ]}
                        >
                          <div className="panel-action">
                            <Button
                              rightIcon={<SVG.IcAdd />}
                              type="success"
                              onClick={onChiDinhVatTu(item)}
                              minWidth={100}
                            >
                              {t("common.themMoi")}
                            </Button>
                            <Button
                              type="primary"
                              rightIcon={<SVG.IcSave />}
                              minWidth={100}
                              onClick={handleSaveThongTin(item)}
                            >
                              {t("common.luu")}
                            </Button>
                          </div>
                        </AuthWrapper>
                      </div>
                    </div>
                  }
                  showArrow={false}
                  key={item.id}
                >
                  <TTChePhamMau
                    detail={item}
                    listDvKemTheo={_listDvKemTheo}
                    showDetail={state.activeKey.includes(item.id)}
                    onHuyDuyetMau={onHuyDuyetMau}
                  />
                </Panel>
              );
            })}
        </Collapse>
      ) : (
        <div>{t("khoMau.khongCoDuLieuChePhamMau")}</div>
      )}
      <ChiDinhDichVuVatTu ref={refModalChiDinhVatTu} />
    </Main>
  );
};
export default memo(ChePhamMau);
