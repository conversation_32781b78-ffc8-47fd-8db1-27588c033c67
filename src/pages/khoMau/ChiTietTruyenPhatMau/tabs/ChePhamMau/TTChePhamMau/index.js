import React, { useCallback, useEffect, useMemo, useState } from "react";
import { Row, Col, Input } from "antd";
import { Main } from "./styled";
import { Card, Select, DatePicker, InputTimeout } from "components";
import moment from "moment";
import { useConfirm, useEnum, useStore, useThietLap } from "hooks";
import {
  ENUM,
  LOAI_DICH_VU,
  ROLES,
  THIET_LAP_CHUNG,
  TRANG_THAI_DICH_VU,
  TRANG_THAI_MAU,
} from "constants/index";
import { useDispatch, useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import { selectMaTen } from "redux-store/selectors";
import { checkRole } from "lib-utils/role-utils";
import { isArray } from "utils";
import TableDichMauVuKemTheo from "./TableDichMauVuKemTheo";
import { SelectLoadMore } from "components";
import khoTonKhoProvider from "data-access/kho/kho-ton-kho-provider";

const TTChePhamMau = ({
  detail,
  showDetail = false,
  isChePhamMau = true,
  listDvKemTheo,
  onHuyDuyetMau,
}) => {
  const { showConfirm } = useConfirm();
  const { t } = useTranslation();
  const [listKetQuaXetNghiem] = useEnum(ENUM.KET_QUA_XET_NGHIEM);
  const [listNhomMau] = useEnum(ENUM.NHOM_MAU);
  const [listMucDo] = useEnum(ENUM.MUC_DO_CHE_PHAM_MAU);

  const {
    truyenPhatMau: { updateData, getDsNhanVienTheoKho },
  } = useDispatch();
  const { listEditChePhamMau, listXetNghiemMau } = useSelector(
    (state) => state.truyenPhatMau
  );
  const { listAllDuongDung = [] } = useSelector((state) => state.duongDung);

  const [HIEN_THI_NHOM_MAU_NB] = useThietLap(
    THIET_LAP_CHUNG.HIEN_THI_NHOM_MAU_NB
  );
  const [dataMucDoChePhamMau, isFinish] = useThietLap(
    THIET_LAP_CHUNG.MUC_DO_CHE_PHAM_MAU
  );

  const [dataTRUYEN_MAU_MAC_DINH_KHONG_HIEN_THI_NGUOI_PHAT_MAU] = useThietLap(
    THIET_LAP_CHUNG.TRUYEN_MAU_MAC_DINH_KHONG_HIEN_THI_NGUOI_PHAT_MAU
  );

  const listAllNhanVien = useStore("nhanVien.listAllNhanVien", []);
  const listAllDVChePhamMau = useStore("dichVuKyThuat.listAllDVChePhamMau", []);

  const [state, _setState] = useState({
    listNhanVienKho: [],
    errorField: {},
  });

  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  const isHienThiNhomMauNb = useMemo(() => {
    return (
      HIEN_THI_NHOM_MAU_NB && HIEN_THI_NHOM_MAU_NB.toLowerCase() === "true"
    );
  }, [HIEN_THI_NHOM_MAU_NB]);

  const listMucDoChePhamMau = useMemo(() => {
    if (isFinish && dataMucDoChePhamMau) {
      return listMucDo.filter((x) =>
        dataMucDoChePhamMau
          .split(",")
          .map((s) => Number(s.trim()))
          .includes(x.id)
      );
    }
    return listMucDo;
  }, [isFinish, dataMucDoChePhamMau, listMucDo]);

  const isEditDichVuChePhamMau = useMemo(() => {
    return checkRole([ROLES["KHO_MAU"].DOI_DICH_VU_CHE_PHAM_MAU]);
  }, []);

  const listDataChePhamMau = useMemo(() => {
    let result = listAllDVChePhamMau;
    if (
      isArray(listAllDVChePhamMau, 1) &&
      isArray(listXetNghiemMau, 1) &&
      detail
    ) {
      const { trangThai, dichVuId } = detail || {};
      let curMau = listAllDVChePhamMau.find((i) => i.id === dichVuId);
      if (
        trangThai === TRANG_THAI_MAU.THUONG &&
        listXetNghiemMau.every(
          (i) => i.trangThai > TRANG_THAI_DICH_VU.CHO_TIEP_NHAN
        ) &&
        curMau
      ) {
        result = listAllDVChePhamMau.filter(
          (i) => i.loaiMau === curMau.loaiMau
        );
      }
    }
    return result;
  }, [listAllDVChePhamMau, listXetNghiemMau, detail]);

  const getListMaTuiMau = useCallback((data) => {
    if (data?.maDichVu) {
      //nếu đã có mã túi máu thì call api get ds nhân viên theo kho
      if (data.maTuiMau) {
        getDsNhanVienTheoKho({
          khoId: data.khoId,
        }).then((res) => {
          setState({ listNhanVienKho: res });
        });
      }
    }
  }, []);

  useEffect(() => {
    getListMaTuiMau(detail);
  }, [getListMaTuiMau, detail]);

  useEffect(() => {
    if (
      detail &&
      detail.trangThai === TRANG_THAI_MAU.THUONG &&
      listEditChePhamMau &&
      listEditChePhamMau.length > 0
    ) {
      //set các field mặc định âm tính
      ["sotRet", "giangMai", "hcv", "hbv", "hiv"].forEach((key) => {
        if (!detail[key]) {
          let _listEditChePhamMau = listEditChePhamMau;
          const index = listEditChePhamMau.findIndex((x) => x.id == detail.id);
          if (index > -1) {
            _listEditChePhamMau[index][key] = -1; // ÂM TÍNH
            _listEditChePhamMau[index].isEdit = true;
          }
        }
      });

      //check chế phẩm máu thuộc loại "Khối hồng cầu" có enum là  30 (/enums?name=loaimau)
      if (detail?.loaiMau == 30) {
        ["muoi1", "globulin1", "phanUngCheo1"].forEach((key) => {
          if (!detail[key]) {
            let _listEditChePhamMau = listEditChePhamMau;
            const index = listEditChePhamMau.findIndex(
              (x) => x.id == detail.id
            );
            if (index > -1) {
              _listEditChePhamMau[index][key] = -1; // ÂM TÍNH
              _listEditChePhamMau[index].isEdit = true;
            }
          }
        });
      } else {
        ["muoi2", "globulin2", "phanUngCheo2"].forEach((key) => {
          if (!detail[key]) {
            let _listEditChePhamMau = listEditChePhamMau;
            const index = listEditChePhamMau.findIndex(
              (x) => x.id == detail.id
            );
            if (index > -1) {
              if (key != "globulin2") {
                _listEditChePhamMau[index][key] = -1; // ÂM TÍNH
              }
              _listEditChePhamMau[index].isEdit = true;
            }
          }
        });
      }
    }
  }, [detail, listEditChePhamMau]);

  const onChangeMaTuiMau = (e, data, callback) => {
    const isCPMActive = detail?.trangThai !== TRANG_THAI_MAU.HUY_DUYET;
    const condition = (x) =>
      isCPMActive
        ? x.trangThai !== TRANG_THAI_MAU.HUY_DUYET
        : x.trangThai === TRANG_THAI_MAU.HUY_DUYET;

    const listCPM = listEditChePhamMau.filter(condition).map((x) => x.maTuiMau);

    let value = e ?? null;
    const findIndex = listCPM.findIndex((x) => x == value);

    if (findIndex > -1) {
      showConfirm(
        {
          title: t("common.thongBao"),
          content: t("khoMau.maTuiMauDaDuocDienSttTiepTucChonPhatMau", {
            num: findIndex + 1,
          }),
          cancelText: t("common.huy"),
          okText: t("common.dongY"),
          classNameOkText: "button-warning",
          showImg: true,
          showBtnOk: true,
          typeModal: "warning",
        },
        () => {
          onChange("maTuiMau")(value, data);
          onChange("nhomMau")(data?.nhomMau);
        },
        () => {
          callback(detail?.maTuiMau);
        }
      );
    } else {
      onChange("maTuiMau")(value, data);
    }
  };

  const onChange = (key) => (e, option) => {
    let value = e ? e : null;

    let _listEditChePhamMau = listEditChePhamMau;
    const index = listEditChePhamMau.findIndex((x) => x.id == detail.id);
    if (index > -1) {
      _listEditChePhamMau[index][key] = value;
      _listEditChePhamMau[index].isEdit = true;

      if (key == "maTuiMau") {
        _listEditChePhamMau[index].nhomMau = option?.nhomMau;
        _listEditChePhamMau[index].loNhapId = option?.loNhapId;
        if (option) {
          getDsNhanVienTheoKho({
            khoId: option.khoId,
          }).then((res) => {
            setState({ listNhanVienKho: res });
          });
        }
      }
      //cập nhật field người nhận cho tất cả các lines
      if (!dataTRUYEN_MAU_MAC_DINH_KHONG_HIEN_THI_NGUOI_PHAT_MAU?.eval()) {
        if (["nguoiNhanId", "nguoiPhat1Id", "nguoiPhat2Id"].includes(key)) {
          _listEditChePhamMau.forEach((element) => {
            element[key] = value;
          });
        }
      }

      if (key === "dichVuId") {
        _listEditChePhamMau[index]["nhomMau"] = null;
        _listEditChePhamMau[index]["maTuiMau"] = null;
        // update maDichVu
        _listEditChePhamMau[index].maDichVu =
          listDataChePhamMau.find((i) => i.id === value)?.ma || null;
        getListMaTuiMau(_listEditChePhamMau[index]);
      }

      updateData({
        listEditChePhamMau: _listEditChePhamMau,
      });
    }
    //check validate
    if (
      ["maTuiMau", "nguoiPhat1Id", "nhomMau", "dichVuId"].includes(key) &&
      isChePhamMau
    ) {
      setState({
        errorField: {
          ...(key === "dichVuId" && { maTuiMau: true, nhomMau: true }),
          ...(key === "maTuiMau" && {
            nhomMau: !_listEditChePhamMau[index].nhomMau,
          }),
          [key]: !value,
        },
      });
    }
  };
  const onChangeDate = (key) => (e) => {
    let value = e instanceof moment ? e.format("DD-MM-YYYY HH:mm:ss") : "";

    let _listEditChePhamMau = listEditChePhamMau;
    const index = listEditChePhamMau.findIndex((x) => x.id == detail.id);
    if (index > -1) {
      _listEditChePhamMau[index][key] = value;
      _listEditChePhamMau[index].isEdit = true;

      updateData({
        listEditChePhamMau: _listEditChePhamMau,
      });
    }
  };

  const setDefaultValue = (key) => {
    return state.listNhanVienKho.length > 0 &&
      state.listNhanVienKho.findIndex((item) => item.id === detail?.[key]) > -1
      ? detail?.[key]
      : null;
  };

  const addValue = useMemo(() => {
    if (detail?.maTuiMau) {
      return [
        {
          ...detail,
          value: detail.maTuiMau,
          label: detail.maTuiMau,
          nhomMau: detail.nhomMau,
        },
      ];
    }
    return [];
  }, [detail?.maTuiMau]);

  return (
    <Main>
      <Row>
        <>
          <Col span={10}>
            <div className="item-select">
              <label
                className={
                  state.errorField.dichVuId && isChePhamMau
                    ? "label-error"
                    : "label"
                }
              >
                {t("khoMau.chePhamMau")}
                {isChePhamMau && <span style={{ color: "red" }}> *</span>}
              </label>
              {detail?.trangThai == TRANG_THAI_MAU.THUONG &&
              isArray(listDataChePhamMau, true) ? (
                <Select
                  value={detail?.dichVuId}
                  data={listDataChePhamMau}
                  getLabel={selectMaTen}
                  disabled={!isEditDichVuChePhamMau}
                  onChange={onChange("dichVuId")}
                  placeholder={t("khoMau.chonChePhamMau")}
                />
              ) : (
                <Input
                  disabled
                  value={`${detail.maDichVu}-${detail.tenDichVu}`}
                />
              )}
            </div>
          </Col>
          <Col span={5}>
            <div className="item-select">
              <label
                className={
                  state.errorField.maTuiMau && isChePhamMau
                    ? "label-error"
                    : "label"
                }
              >
                {t("khoMau.maTuiMau")}
                {isChePhamMau && <span style={{ color: "red" }}> *</span>}
              </label>
              {detail.trangThai === TRANG_THAI_MAU.DA_PHAT ? (
                <Input disabled value={detail?.maTuiMau} />
              ) : (
                <SelectLoadMore
                  value={detail?.maTuiMau}
                  addParam={{
                    khoId: detail?.khoId,
                    loaiDichVu: LOAI_DICH_VU.CHE_PHAM_MAU,
                    dichVuId: detail?.dichVuId,
                    dsLoaiDichVu: [LOAI_DICH_VU.CHE_PHAM_MAU],
                    ma: detail?.maDichVu,
                    theoSoLuongTonKho: 15,
                  }}
                  uniqByKey="value"
                  api={khoTonKhoProvider.theoLo}
                  addValue={addValue}
                  mapData={(i) => ({
                    ...i,
                    value: i.maTuiMau,
                    label: i.maTuiMau,
                    nhomMau: i.nhomMau,
                  })}
                  className="select"
                  placeholder={t("khoMau.chonMaTuiMau")}
                  onChange={onChangeMaTuiMau}
                  keySearch="maTuiMau"
                  blurReset={true}
                />
              )}
            </div>
          </Col>

          <Col span={3}>
            <div className="item-select">
              <label
                className={
                  state.errorField.nhomMau && isChePhamMau
                    ? "label-error"
                    : "label"
                }
              >
                {t("khoMau.nhomMauPhat")}
                {isChePhamMau && <span style={{ color: "red" }}> *</span>}
              </label>
              {detail?.trangThai === TRANG_THAI_MAU.THUONG &&
              isArray(listDataChePhamMau, true) &&
              isEditDichVuChePhamMau ? (
                <Select
                  value={detail?.nhomMau}
                  data={listNhomMau}
                  onChange={onChange("nhomMau")}
                  placeholder={t("sinhHieu.chonNhomMau")}
                />
              ) : (
                <Input
                  disabled
                  value={
                    (listNhomMau || []).find((x) => x.id == detail?.nhomMau)
                      ?.ten || ""
                  }
                />
              )}
            </div>
          </Col>

          <Col span={3}>
            <div className="item-select">
              <label className="label">{t("khoMau.nhomMauNb")}</label>
              <Input
                disabled
                value={
                  (listNhomMau || []).find((x) =>
                    isHienThiNhomMauNb
                      ? x.id == detail?.nhomMauNb
                      : x.id == detail?.nhomMau
                  )?.ten || ""
                }
              />
            </div>
          </Col>

          <Col span={3}>
            <div className="item-select">
              <label className="label">{t("khoMau.mucDo")}</label>
              {detail.trangThai === TRANG_THAI_MAU.DA_PHAT ? (
                <Input
                  disabled
                  value={
                    (listMucDoChePhamMau || []).find(
                      (x) => x.id == detail?.mucDo
                    )?.ten || ""
                  }
                />
              ) : (
                <Select
                  defaultValue={detail?.mucDo}
                  data={listMucDoChePhamMau}
                  placeholder={t("khoMau.chonMucDo")}
                  onChange={onChange("mucDo")}
                />
              )}
            </div>
          </Col>

          <Col span={6}>
            <div className="item-select">
              <label
                className={
                  state.errorField.nguoiPhat1Id && isChePhamMau
                    ? "label-error"
                    : "label"
                }
              >
                {t("khoMau.nguoiPhatMau1")}
                {isChePhamMau && <span style={{ color: "red" }}> *</span>}
              </label>
              {detail.trangThai === TRANG_THAI_MAU.DA_PHAT ? (
                <Input disabled value={detail?.tenNguoiPhat1} />
              ) : (
                <Select
                  defaultValue={setDefaultValue("nguoiPhat1Id")}
                  data={state.listNhanVienKho}
                  onChange={onChange("nguoiPhat1Id")}
                  placeholder={t("khoMau.chonNguoiPhat")}
                />
              )}
            </div>
          </Col>

          <Col span={6}>
            <div className="item-select">
              <label className="label">{t("khoMau.nguoiPhatMau2")}</label>
              {detail.trangThai === TRANG_THAI_MAU.DA_PHAT ? (
                <Input disabled value={detail?.tenNguoiPhat2} />
              ) : (
                <Select
                  defaultValue={setDefaultValue("nguoiPhat2Id")}
                  data={state.listNhanVienKho}
                  onChange={onChange("nguoiPhat2Id")}
                  placeholder={t("khoMau.chonNguoiPhat")}
                />
              )}
            </div>
          </Col>

          <Col span={6}>
            <div className="item-select">
              <label className={"label"}>{t("khoMau.nguoiNhanMau")}</label>
              <Select
                value={detail?.nguoiNhanId || ""}
                data={listAllNhanVien}
                onChange={onChange("nguoiNhanId")}
                placeholder={t("khoMau.chonNguoiNhanMau")}
              />
            </div>
          </Col>

          <Col span={6}>
            <div className="item-select">
              <label className="label">{t("kho.khoaChiDinh")}</label>
              <Input disabled value={detail?.tenKhoaChiDinh || ""} />
            </div>
          </Col>
        </>

        {showDetail && (
          <>
            <Col span={6}>
              <div className="item-select">
                <label className="label">{t("cdha.thoiGianChiDinh")}</label>
                <Input
                  disabled
                  value={moment(detail?.thoiGianChiDinh).format(
                    "DD-MM-YYYY HH:mm:ss"
                  )}
                />
              </div>
            </Col>
            <Col span={6}>
              <div className="item-select">
                <label className="label">{t("kho.thoiGianThucHien")}</label>
                <DatePicker
                  defaultValue={
                    detail?.thoiGianThucHien
                      ? moment(detail?.thoiGianThucHien)
                      : null
                  }
                  format="DD-MM-YYYY HH:mm:ss"
                  onChange={onChangeDate("thoiGianThucHien")}
                />
              </div>
            </Col>

            <Col span={6}>
              <div className="item-select">
                <label className="label">{t("hsba.thoiGianPhat")}</label>
                <DatePicker
                  defaultValue={
                    detail?.thoiGianDuyet ? moment(detail?.thoiGianDuyet) : null
                  }
                  format="DD-MM-YYYY HH:mm:ss"
                  onChange={onChange("thoiGianPhat")}
                />
              </div>
            </Col>
            <Col span={6}>
              <div className="item-select">
                <label className="label">{t("nhaThuoc.duongDung")}</label>
                <Select
                  data={listAllDuongDung || []}
                  placeholder={t("nhaThuoc.chonDuongDung")}
                  defaultValue={detail?.duongDungId}
                  onChange={onChange("duongDungId")}
                />
              </div>
            </Col>

            <Col span={6}>
              <div className="item-select">
                <label className="label">{t("nhaThuoc.dotDung")}</label>
                <InputTimeout
                  placeholder={t("nhaThuoc.nhapDotDung")}
                  value={detail?.dotDung}
                  onChange={onChange("dotDung")}
                />
              </div>
            </Col>
            <Col span={3}>
              <div className="item-select">
                <label className="label">
                  {t("quanLyNoiTru.dvNoiTru.theTich")}
                </label>
                <Input disabled value={detail?.theTich} />
              </div>
            </Col>
            <Col span={3}>
              <div className="item-select">
                <label className="label">{t("common.donViTinh")}</label>
                <Input disabled value={detail?.tenDonViTinh} />
              </div>
            </Col>

            <Col span={6}>
              <div className="item-select">
                <label className="label">
                  {t("quanLyNoiTru.dvNoiTru.ngaySanXuat")}
                </label>
                <DatePicker
                  defaultValue={
                    detail?.ngaySanXuat ? moment(detail?.ngaySanXuat) : null
                  }
                  format="DD-MM-YYYY"
                  onChange={onChangeDate("ngaySanXuat")}
                />
              </div>
            </Col>

            <Col span={6}>
              <div className="item-select">
                <label className="label">
                  {t("quanLyNoiTru.dvNoiTru.hanSuDung")}
                </label>
                <DatePicker
                  defaultValue={
                    detail?.ngayHanSuDung ? moment(detail?.ngayHanSuDung) : null
                  }
                  format="DD-MM-YYYY"
                  onChange={onChangeDate("ngayHanSuDung")}
                />
              </div>
            </Col>

            <Col span={24}>
              <div className="item-select">
                <label className="label">
                  {t("quanLyNoiTru.dvNoiTru.ghiChu")}
                </label>
                <InputTimeout
                  isTextArea
                  value={detail?.ghiChu}
                  placeholder={t("common.nhapGhiChu")}
                  onChange={onChange("ghiChu")}
                />
              </div>
            </Col>

            <Col span={4}>
              <div className="item-select">
                <label className="label">
                  {t("quanLyNoiTru.dvNoiTru.sotRet")}
                </label>
                <Select
                  defaultValue={detail?.sotRet}
                  data={listKetQuaXetNghiem}
                  placeholder={t("xetNghiem.chonKetQua")}
                  onChange={onChange("sotRet")}
                />
              </div>
            </Col>
            <Col span={4} offset={1}>
              <div className="item-select">
                <label className="label">
                  {t("quanLyNoiTru.dvNoiTru.giangMai")}
                </label>
                <Select
                  defaultValue={detail?.giangMai}
                  data={listKetQuaXetNghiem}
                  placeholder={t("xetNghiem.chonKetQua")}
                  onChange={onChange("giangMai")}
                />
              </div>
            </Col>
            <Col span={4} offset={1}>
              <div className="item-select">
                <label className="label">{"HCV"}</label>
                <Select
                  defaultValue={detail?.hcv}
                  data={listKetQuaXetNghiem}
                  placeholder={t("xetNghiem.chonKetQua")}
                  onChange={onChange("hcv")}
                />
              </div>
            </Col>
            <Col span={4} offset={1}>
              <div className="item-select">
                <label className="label">{"HBV"}</label>
                <Select
                  defaultValue={detail?.hbv}
                  data={listKetQuaXetNghiem}
                  placeholder={t("xetNghiem.chonKetQua")}
                  onChange={onChange("hbv")}
                />
              </div>
            </Col>
            <Col span={4} offset={1}>
              <div className="item-select">
                <label className="label">{"HIV"}</label>
                <Select
                  defaultValue={detail?.hiv}
                  data={listKetQuaXetNghiem}
                  placeholder={t("xetNghiem.chonKetQua")}
                  onChange={onChange("hiv")}
                />
              </div>
            </Col>
          </>
        )}
      </Row>

      {showDetail && (
        <>
          <div className="title-ketQua">
            {t("khoMau.ketQuaXetNghiemHoaHopMienDichVaXetNghiemKhac")}
          </div>

          <Card>
            <Row>
              <Col span={8}>
                <div className="item-select">
                  <label className="label">
                    {t("quanLyNoiTru.dvNoiTru.ong1MoiTruongMuoi22oC")}
                  </label>
                  <Select
                    defaultValue={detail?.muoi1}
                    data={listKetQuaXetNghiem}
                    placeholder={t("xetNghiem.chonKetQua")}
                    onChange={onChange("muoi1")}
                  />
                </div>
              </Col>
              <Col span={8}>
                <div className="item-select">
                  <label className="label">
                    {t("quanLyNoiTru.dvNoiTru.37oCKhangGlobulinOng1")}
                  </label>
                  <Select
                    defaultValue={detail?.globulin1}
                    data={listKetQuaXetNghiem}
                    placeholder={t("xetNghiem.chonKetQua")}
                    onChange={onChange("globulin1")}
                  />
                </div>
              </Col>
              <Col span={8}>
                <div className="item-select">
                  <label className="label">
                    {t("quanLyNoiTru.dvNoiTru.ketQuaPhanUngCheoOng1")}
                  </label>
                  <Select
                    defaultValue={detail?.phanUngCheo1}
                    data={listKetQuaXetNghiem}
                    placeholder={t("xetNghiem.chonKetQua")}
                    onChange={onChange("phanUngCheo1")}
                  />
                </div>
              </Col>

              <Col span={8}>
                <div className="item-select">
                  <label className="label">
                    {t("quanLyNoiTru.dvNoiTru.ong2MoiTruongMuoi22oC")}
                  </label>
                  <Select
                    defaultValue={detail?.muoi2}
                    data={listKetQuaXetNghiem}
                    placeholder={t("xetNghiem.chonKetQua")}
                    onChange={onChange("muoi2")}
                  />
                </div>
              </Col>
              <Col span={8}>
                <div className="item-select">
                  <label className="label">
                    {t("quanLyNoiTru.dvNoiTru.37oCKhangGlobulinOng2")}
                  </label>
                  <Select
                    defaultValue={detail?.globulin2}
                    data={listKetQuaXetNghiem}
                    placeholder={t("xetNghiem.chonKetQua")}
                    onChange={onChange("globulin2")}
                  />
                </div>
              </Col>
              <Col span={8}>
                <div className="item-select">
                  <label className="label">
                    {t("quanLyNoiTru.dvNoiTru.ketQuaPhanUngCheoOng2")}
                  </label>
                  <Select
                    defaultValue={detail?.phanUngCheo2}
                    data={listKetQuaXetNghiem}
                    placeholder={t("xetNghiem.chonKetQua")}
                    onChange={onChange("phanUngCheo2")}
                  />
                </div>
              </Col>
            </Row>
          </Card>
        </>
      )}
      {isArray(listDvKemTheo, 1) && (
        <div>
          <div className="title-ketQua">{t("khoMau.dsDvKemChePhamMau")}</div>
          <Card>
            <TableDichMauVuKemTheo
              listDvKemTheo={listDvKemTheo}
              onHuyDuyetMau={onHuyDuyetMau}
            />
          </Card>
        </div>
      )}
    </Main>
  );
};

export default TTChePhamMau;
