import phieuNhapXuatProvider from "data-access/kho/phieu-nhap-xuat-provider";
import { message } from "antd";
import { PAGE_SIZE, PAGE_DEFAULT } from "constants/index";
import { combineSort } from "utils";
import thuocChiTietProvider from "data-access/kho/thuoc-chi-tiet-provider";
import cloneDeep from "lodash/cloneDeep";
import { t } from "i18next";
import fileUtils from "utils/file-utils";
import printProvider from "data-access/print-provider";

export default {
  state: {
    listDonThuocNgoaiTru: [],
    totalElements: null,
    page: PAGE_DEFAULT,
    size: PAGE_SIZE,
    dataEditDefault: {},
    dataSortColumn: {
      stt: 1,
      soPhieu: 2,
    },
    dataSearch: {},
    isLoading: false,
    showButtonHuyGiuTon: false,
  },
  reducers: {
    updateData(state, payload = {}) {
      return { ...state, ...payload };
    },
  },
  effects: (dispatch) => ({
    onSizeChange: ({ page = 0, size = 10, ...rest }, state) => {
      const dataSearch = {
        ...(state.phatThuocNgoaiTru.dataSearch || {}),
        ...rest,
      };
      dispatch.phatThuocNgoaiTru.updateData({
        page,
        size,
        dataSearch,
      });
      dispatch.phatThuocNgoaiTru.getListDonThuocNgoaiTru({
        page,
        size,
        dataSearch,
      });
    },
    getListDonThuocNgoaiTru: ({ page = 0, ...payload }, state) => {
      let size = payload.size || state.phatThuocNgoaiTru.size || 10;
      const sort = combineSort(
        payload.dataSortColumn || state.phatThuocNgoaiTru.dataSortColumn || {}
      );
      const dataSearch =
        payload.dataSearch || state.phatThuocNgoaiTru.dataSearch || {};

      dispatch.phatThuocNgoaiTru.updateData({ isLoading: true });

      let dataSearchUpdate = cloneDeep(dataSearch);

      //check params rỗng ([]) thì ko truyền
      ["dsKhoaNbId", "dsPhongChiDinhId", "dsTrangThai", "dsKhoId"].forEach(
        (element) => {
          if (!dataSearchUpdate[element] || !dataSearchUpdate[element].length) {
            delete dataSearchUpdate[element];
          }
        }
      );

      return new Promise((resolve, reject) => {
        phieuNhapXuatProvider
          .getDanhSachDonThuocByPut({
            page,
            size,
            ...dataSearchUpdate,
            sort,
          })
          .then((s) => {
            dispatch.phatThuocNgoaiTru.updateData({
              listDonThuocNgoaiTru: (s?.data || []).map((item, index) => {
                item.index = page * size + index + 1;
                return item;
              }),
              totalElements: s?.totalElements || 0,
              page,
              size,
              dataSearch,
              sort,
              isLoading: false,
            });
            resolve(s);
          })
          .catch((e) => {
            message.error(e?.message || "Xảy ra lỗi vui lòng thử lại");
            dispatch.phatThuocNgoaiTru.updateData({
              sort,
              dataSearch,
              isLoading: false,
            });
          });
      });
    },
    onChangeInputSearch: ({ ...payload }, state) => {
      const dataSearch = {
        ...(state.phatThuocNgoaiTru.dataSearch || {}),
        ...payload,
      };
      dispatch.phatThuocNgoaiTru.updateData({
        page: 0,
        dataSearch,
      });
      dispatch.phatThuocNgoaiTru.getListDonThuocNgoaiTru({
        page: 0,
        dataSearch,
      });
    },
    onSortChange: ({ ...payload }, state) => {
      const dataSortColumn = {
        ...state.phatThuocNgoaiTru.dataSortColumn,
        ...payload,
      };
      dispatch.phatThuocNgoaiTru.updateData({
        page: 0,
        dataSortColumn,
      });
      dispatch.phatThuocNgoaiTru.getListDonThuocNgoaiTru({
        page: 0,
        dataSortColumn,
      });
    },

    postDuyet: ({ ...rest }, state) => {
      return new Promise((resolve, reject) => {
        thuocChiTietProvider
          .duyetPhieu(rest)
          .then((s) => {
            message.success(" Phát đơn thành công");
            resolve(s.data);
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          });
      });
    },
    postHuyDuyet: (payload = {}, state) => {
      return new Promise((resolve, reject) => {
        const { id, lyDo } = payload;
        phieuNhapXuatProvider
          .huyDuyet({ id, lyDo })
          .then((res) => {
            if (res && res.code === 0) {
              message.success("Hủy phát đơn thành công");
            }
            resolve(res.data);
          })
          .catch((res) => {
            message.error(res?.message || "Xảy ra lỗi vui lòng thử lại");
          });
      });
    },
    giuTon: (id, state) => {
      return new Promise((resolve, reject) => {
        phieuNhapXuatProvider
          .datTruoc(id)
          .then((s) => {
            message.success("Giữ tồn thành công");
            resolve(s.data);
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          });
      });
    },
    huyGiuTon: ({ id, body }, state) => {
      return new Promise((resolve, reject) => {
        phieuNhapXuatProvider
          .huyDatTruoc(id, body)
          .then((res) => {
            if (res && res.code === 0) {
              message.success("Hủy giữ tồn thành công");
            }
            resolve(res.data);
          })
          .catch((err) => {
            reject(err);
            message.error(err?.message || "Xảy ra lỗi vui lòng thử lại");
          });
      });
    },
    xuatDuLieu: (payload = {}, state) => {
      return new Promise((resolve, reject) => {
        phieuNhapXuatProvider
          .xuatDuLieu(payload)
          .then((res) => {
            if (res && res.code === 0) {
              fileUtils.downloadFile(
                res?.data?.file?.doc,
                "kho_phieu_nhap_xuat_don_thuoc.xlsx"
              );
              resolve(res?.data);
            } else {
              message.error(
                res?.message || t("common.xayRaLoiVuiLongThuLaiSau")
              );
              reject(res);
            }
          })
          .catch((e) => {
            message.error(e?.message || "Xảy ra lỗi vui lòng thử lại");
            reject(e);
          });
      });
    },

    inPhieuXuatThuoc: (payload, state) => {
      return new Promise((resolve, reject) => {
        phieuNhapXuatProvider
          .getPhieuPhatThuoc(payload)
          .then((s) => {
            if (s.data) {
              printProvider.printPdf(s.data);
            }
            resolve(s);
          })
          .catch((e) => {
            reject(e);
          });
      });
    },
  }),
};
