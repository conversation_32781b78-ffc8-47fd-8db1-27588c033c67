import dichVuKyThuatProvider from "data-access/categories/dm-dv-ky-thuat-provider";
import dichVuXNProvider from "data-access/nb-dv-xet-nghiem-provider";
import nbDvKhamProvider from "data-access/nb-dv-kham-provider";
import nbDvCLSProvider from "data-access/nb-dv-cdha-tdcn-pt-tt-provider";
import nbDvNgoaiDieuTriProvider from "data-access/nb-dv-ngoai-dieu-tri-provider";
import { message } from "antd";
import { groupBy, flatten, random } from "lodash";
import printProvider from "data-access/print-provider";
import dmBoChiDinhProvider from "data-access/categories/dm-bo-chi-dinh-provider";
import nbBoChiDinhProvider from "data-access/nb-bo-chi-dinh-provider";
import nbGoiDvProvider from "data-access/nb-goi-dv-provider";
import nbGoiDvChiTietProvider from "data-access/nb-goi-dv-chi-tiet-provider";
import { t } from "i18next";
import nbDvXetNghiemProvider from "data-access/nb-dv-xet-nghiem-provider";
import nbDvCdhaTdcnPtTtProvider from "data-access/nb-dv-cdha-tdcn-pt-tt-provider";
import {
  CACHE_KEY,
  DOI_TUONG_SU_DUNG,
  LOAI_DICH_VU,
  LOAI_IN,
  PAGE_DEFAULT,
  PAGE_SIZE,
} from "constants/index";
import nbDichVuProvider from "data-access/nb-dich-vu-provider";
import { refConfirm } from "app";
import nbDanhSachLichHenProvider from "data-access/nb-danh-sach-lich-hen-provider";
import cloneDeep from "lodash/cloneDeep";
import { toSafePromise } from "lib-utils";
import { guid } from "mainam-react-native-string-utils";
import moment from "moment";

const initState = {
  elementScrollingPdfKey: 1,
  totalElementsDv: null,
  pageDv: PAGE_DEFAULT,
  sizeDv: 20,
  dataSearchDv: {},
};
export default {
  state: cloneDeep(initState),
  reducers: {
    updateData(state, payload = {}) {
      return { ...state, ...payload };
    },
    clearData(state, payload = {}) {
      return { ...cloneDeep(initState), ...payload }; // không clear giá trị thiết lập
    },
  },
  effects: (dispatch) => ({
    onSizeChangeDvTiepDon: (size, state) => {
      dispatch.tiepDonDichVu.updateData({
        size,
        page: 0,
      });
      dispatch.tiepDonDichVu.searchDvTiepDon({ page: 0, size });
    },
    onChangeInputSearchDvTiepDon: ({ ...payload }, state, options) => {
      const dataSearch = {
        ...(state.tiepDonDichVu.dataSearchDv || {}),
        ...payload,
      };
      dispatch.tiepDonDichVu.updateData({
        pageDv: 0,
        dataSearchDv: dataSearch,
      });
      return dispatch.tiepDonDichVu.searchDvTiepDon(
        {
          pageDv: 0,
          dataSearch,
        },
        options
      );
    },
    searchDvTiepDon: async ({ page = 0, ...payload }, state, options) => {
      const { searchAll = false } = options || {};
      return new Promise(async (resolve, reject) => {
        try {
          let size = payload.size || state.tiepDonDichVu.sizeDv || 10;
          const dataSearch =
            payload.dataSearch || state.tiepDonDichVu.dataSearchDv || {};
          // let listDvKham = await cacheUtils.read(
          //   "",
          //   `${CACHE_KEY.DATA_DICH_VU_KHAM}_${payload?.loaiDichVu}_${DOI_TUONG_SU_DUNG.TIEP_DON}`,
          //   [],
          //   false
          // );
          // //kiểm tra xem có khác với dịch vụ trong redux không
          // if (!isEqual(listDvKham, state.tiepDonDichVu.listDvKham)) {
          //   dispatch.tiepDonDichVu.updateData({ listDvKham });
          // }
          // if (listDvKham?.length) {
          //   resolve(listDvKham);
          // }
          let data = [];
          let totalElements = 0;
          if (dataSearch?.loaiDichVu === LOAI_DICH_VU.BO_CHI_DINH) {
            const s = await dmBoChiDinhProvider.searchTongHop({
              ...dataSearch,
              dsDoiTuongSuDung: DOI_TUONG_SU_DUNG.TIEP_DON,
              dsLoaiDichVu: [
                LOAI_DICH_VU.KHAM,
                LOAI_DICH_VU.XET_NGHIEM,
                LOAI_DICH_VU.CDHA,
                LOAI_DICH_VU.PHAU_THUAT_THU_THUAT,
                LOAI_DICH_VU.NGOAI_DIEU_TRI,
              ],
              page: searchAll ? "" : page,
              size: searchAll ? "" : size,
            });
            data = (s?.data || []).map((item) => ({
              ...item,
              dichVuId: item.id,
            }));
            totalElements = s?.totalElements || 0;
          } else {
            const s = await dichVuKyThuatProvider.searchDMDichVuTachPhong({
              dsDoiTuongSuDung: DOI_TUONG_SU_DUNG.TIEP_DON,
              ...dataSearch,
              chiDinhTuLoaiDichVu: LOAI_DICH_VU.TIEP_DON,
              page,
              size,
            });
            data = s?.data || [];
            totalElements = s?.totalElements || 0;
          }
          data = data.map((item) => {
            if (item.dsDoiTuongSuDung) {
              item.dsDoiTuongSuDung = item.dsDoiTuongSuDung.sort();
            }
            return item;
          });
          // if (!isEqual(data, listDvKham)) {
          dispatch.tiepDonDichVu.updateData({
            listDvKham: data,
            pageDv: page,
            sizeDv: size,
            totalElementsDv: totalElements,
          });
          // cacheUtils.save(
          //   "",
          //   `${CACHE_KEY.DATA_DICH_VU_KHAM}_${payload?.loaiDichVu}_${DOI_TUONG_SU_DUNG.TIEP_DON}`,
          //   data,
          //   false
          // );
          // }
          resolve(data);
        } catch (e) {
          reject(e);
          message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
        }
      });
    },
    searchDvKSKTiepDon: async (payload = {}, state) => {
      return new Promise((resolve, reject) => {
        dichVuKyThuatProvider
          .searchDMDichVuTachPhong({
            ...payload,
            dsDoiTuongSuDung: DOI_TUONG_SU_DUNG.NB_KHAM_SUC_KHOE,
            chiDinhTuLoaiDichVu: LOAI_DICH_VU.TIEP_DON,
          })
          .then((s) => {
            let data = s?.data || [];
            dispatch.tiepDonDichVu.updateData({
              listDvKham: data,
              totalElementsDv: null,
            });
            resolve(s);
          })
          .catch((e) => {
            reject(e);
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          });
      });
    },
    keDichVuKham: ({ data }, state) => {
      return new Promise((resolve, reject) => {
        const dataKham = data.filter(
          (item) =>
            item.nbDichVu?.loaiDichVu === LOAI_DICH_VU.KHAM && !item.boChiDinhId
        );

        const dataXN = data.filter(
          (item) =>
            item.nbDichVu?.loaiDichVu === LOAI_DICH_VU.XET_NGHIEM &&
            !item.boChiDinhId
        );
        const dataCLS = data.filter(
          (item) =>
            [LOAI_DICH_VU.PHAU_THUAT_THU_THUAT, LOAI_DICH_VU.CDHA].includes(
              item.nbDichVu?.loaiDichVu
            ) && !item.boChiDinhId
        );
        const dataNgoaiDieuTri = data
          .filter(
            (item) =>
              [LOAI_DICH_VU.NGOAI_DIEU_TRI].includes(
                item.nbDichVu?.loaiDichVu
              ) && !item.boChiDinhId
          )
          .map((item) => {
            return {
              nbDotDieuTriId: item.nbDotDieuTriId,
              nbDichVu: item.nbDichVu,
              phongThucHienId: item.nbDvKyThuat.phongThucHienId,
            };
          });
        const thens = (resolve, reject) => (s) => {
          if (s.code == 0) {
            const errors = s.data
              .filter((item) => item.code !== 0 && !item.id)
              .map((item) => item.message)
              .filter((item, index, self) => self.indexOf(item) == index);
            // if (errors.length)
            resolve({
              code: errors.length ? 1 : 0,
              data: s.data.filter((item) => item.id),
              message: errors,
            });
            return;
            // else {
            //   resolve({ code: 0, data: [] });
            // }
          }
          resolve({
            code: 1,
            message: [s.message],
            data: [],
          });
        };

        const catchs = (resolve, reject) => (e) => {
          resolve({ code: 1, message: [e.message], data: [] });
        };
        const chiDinhKham = dataKham.length
          ? new Promise((resolve, reject) => {
              return nbDvKhamProvider
                .chiDinhDVKham(dataKham)
                .then(thens(resolve, reject))
                .catch(catchs(resolve, reject));
            })
          : { code: 0, data: [] };

        const chiDinhXN = dataXN.length
          ? new Promise((resolve, reject) => {
              return dichVuXNProvider
                .chiDinhXN(dataXN)
                .then(thens(resolve, reject))
                .catch(catchs(resolve, reject));
            })
          : { code: 0, data: [] };

        const chiDinhCLS = dataCLS.length
          ? new Promise((resolve, reject) => {
              return nbDvCLSProvider
                .chiDinhCLS(dataCLS)
                .then(thens(resolve, reject))
                .catch(catchs(resolve, reject));
            })
          : { code: 0, data: [] };
        const chiDinhNgoaiDieuTri = dataNgoaiDieuTri.length
          ? new Promise((resolve, reject) => {
              return nbDvNgoaiDieuTriProvider
                .chiDinhNgoaiDieuTri(dataNgoaiDieuTri)
                .then(thens(resolve, reject))
                .catch(catchs(resolve, reject));
            })
          : { code: 0, data: [] };

        const chiDinhGoiDV = new Promise((resolve, reject) => {
          const body = data.filter((item) => item.boChiDinhId);
          const groupedBody = groupBy(body, "boChiDinhId");
          let sendBody = [];
          Object.keys(groupedBody).forEach((element) => {
            sendBody.push({
              nbDotDieuTriId: groupedBody[element][0].nbDotDieuTriId,
              boChiDinhId: element,
              dsKham:
                groupedBody[element].filter(
                  (item) => item.nbDichVu.loaiDichVu === LOAI_DICH_VU.KHAM
                ) || null,
              dsXetNghiem:
                groupedBody[element].filter(
                  (item) => item.nbDichVu.loaiDichVu === LOAI_DICH_VU.XET_NGHIEM
                ) || null,
              dsCdhaTdcnPtTt:
                groupedBody[element].filter(
                  (item) =>
                    item.nbDichVu.loaiDichVu === LOAI_DICH_VU.CDHA ||
                    item.nbDichVu.loaiDichVu ===
                      LOAI_DICH_VU.PHAU_THUAT_THU_THUAT
                ) || null,
              dsNgoaiDieuTri:
                groupedBody[element].filter(
                  (item) =>
                    item.nbDichVu.loaiDichVu === LOAI_DICH_VU.NGOAI_DIEU_TRI
                ) || null,
              dsThuoc:
                groupedBody[element].filter(
                  (item) => item.nbDichVu.loaiDichVu === LOAI_DICH_VU.THUOC
                ) || null,
              dsVatTu:
                groupedBody[element].filter(
                  (item) => item.nbDichVu.loaiDichVu === LOAI_DICH_VU.VAT_TU
                ) || null,
              dsHoaChat:
                groupedBody[element].filter(
                  (item) => item.nbDichVu.loaiDichVu === LOAI_DICH_VU.HOA_CHAT
                ) || null,
              dsChePhamMau:
                groupedBody[element].filter(
                  (item) =>
                    item.nbDichVu.loaiDichVu === LOAI_DICH_VU.CHE_PHAM_MAU
                ) || null,
            });
          });

          if (sendBody.length)
            return nbBoChiDinhProvider
              .chiDinhGoiDV(sendBody)
              .then((s) => {
                if (s.code === 0) {
                  resolve(s);
                } else {
                  reject(s);
                }
              })
              .catch((e) => {
                reject(e);
              });
          else resolve({ code: 0, data: [] });
        });
        Promise.all([
          chiDinhKham,
          chiDinhXN,
          chiDinhCLS,
          chiDinhNgoaiDieuTri,
          chiDinhGoiDV,
        ])
          .then((values) => {
            resolve(values);
          })
          .catch((e) => {
            reject(e);
          });
      });
    },
    thayDoiThongTinDichVuDaChon: ({ data }) => {
      const loaiDichVu = data.nbDichVu?.loaiDichVu;
      const providerMap = {
        [LOAI_DICH_VU.KHAM]: nbDvKhamProvider,
        [LOAI_DICH_VU.XET_NGHIEM]: dichVuXNProvider,
        [LOAI_DICH_VU.CDHA]: nbDvCLSProvider,
        [LOAI_DICH_VU.PHAU_THUAT_THU_THUAT]: nbDvCLSProvider,
        [LOAI_DICH_VU.NGOAI_DIEU_TRI]: nbDvNgoaiDieuTriProvider,
      };

      const provider = providerMap[loaiDichVu];

      if (provider === nbDvNgoaiDieuTriProvider) {
        return nbDvNgoaiDieuTriProvider.themThongTinDV([
          {
            id: data.id,
            nbDichVu: data.nbDichVu,
            phongThucHienId: data?.nbDvKyThuat?.phongThucHienId,
          },
        ]);
      }

      return provider
        ? provider.themThongTinDV(data, data.id)
        : Promise.reject(null);
    },
    themDichVu: ({ dsDichVu, nbDotDieuTriId }, state) => {
      return new Promise(async (resolve, reject) => {
        try {
          const khoaTiepDonId =
            state.tiepDon?.khoaTiepDonId ||
            state.nbDotDieuTri.thongTinBenhNhan?.khoaTiepDonId;

          //Lấy khoa theo quầy
          const quayTiepDonId = state.goiSo.quayTiepDonId || null;
          const listAllQuayTiepDonTaiKhoan =
            state.quayTiepDon.listAllQuayTiepDonTaiKhoan || [];
          const quayTiepDonTaiKhoan = listAllQuayTiepDonTaiKhoan?.find(
            (x) => x.id === quayTiepDonId
          );

          const thongTinBenhNhan = state.nbDotDieuTri.thongTinBenhNhan;

          const listDvChoose = state.tiepDonDichVu.listDvChoose || [];
          const data = await Promise.all(
            dsDichVu.map((dichVu, index) => {
              return new Promise(async (resolve, reject) => {
                let loaiHinhThanhToanId =
                  dichVu?.loaiHinhThanhToanId &&
                  dichVu?.dsLoaiHinhThanhToan?.findIndex(
                    (loaiHinhTT) =>
                      loaiHinhTT.loaiHinhThanhToanId ===
                      dichVu?.loaiHinhThanhToanId
                  ) > -1
                    ? dichVu?.loaiHinhThanhToanId
                    : (dichVu?.dsLoaiHinhThanhToan?.sort(
                        (a, b) => b.uuTien - a.uuTien
                      ) || [])[0]?.loaiHinhThanhToanId;

                if (!loaiHinhThanhToanId) {
                  let resLoaiHinhTT =
                    await dispatch.loaiDoiTuongLoaiHinhTT.getListLoaiDoiTuongTT(
                      {
                        active: true,
                        page: "",
                        size: "",
                        dsDichVuId: dichVu?.dichVuId,
                        loaiDoiTuongId: thongTinBenhNhan?.loaiDoiTuongId,
                        khoaChiDinhId:
                          quayTiepDonTaiKhoan?.khoaId || khoaTiepDonId,
                        ngaySinh:
                          thongTinBenhNhan.ngaySinh &&
                          moment(thongTinBenhNhan.ngaySinh).format(
                            "YYYY-MM-DD"
                          ),
                        ngayVaoVien:
                          thongTinBenhNhan.thoiGianVaoVien &&
                          moment(thongTinBenhNhan.thoiGianVaoVien).format(
                            "YYYY-MM-DD"
                          ),
                        doiTuongKcb: thongTinBenhNhan?.doiTuongKcb,
                      }
                    );

                  loaiHinhThanhToanId = (resLoaiHinhTT?.sort(
                    (a, b) => b.uuTien - a.uuTien
                  ) || [])[0]?.loaiHinhThanhToanId;

                  dichVu.dsLoaiHinhThanhToan = resLoaiHinhTT || [];
                }
                dichVu.loaiHinhThanhToanId = loaiHinhThanhToanId;

                let resNoiLayMau = dichVu.dsNoiLayMau || [];
                if (
                  dichVu?.loaiDichVu === LOAI_DICH_VU.XET_NGHIEM &&
                  (!dichVu.dsNoiLayMau || !dichVu.dsNoiLayMau?.length)
                ) {
                  resNoiLayMau = await dispatch.noiLayBenhPham.getListNoiLayMau(
                    {
                      khoaChiDinhId:
                        quayTiepDonTaiKhoan?.khoaId || khoaTiepDonId,
                      dsDoiTuongKcb: thongTinBenhNhan?.doiTuongKcb,
                      dichVuId: dichVu.dichVuId,
                      nhomDichVuCap2Id: dichVu.nhomDichVuCap2Id,
                      nhomDichVuCap3Id: dichVu.nhomDichVuCap3Id,
                      dsLoaiDoiTuongId: thongTinBenhNhan?.loaiDoiTuongId,
                      loaiHinhThanhToanId: dichVu.loaiHinhThanhToanId,
                    }
                  );
                }

                dichVu.dsNoiLayMau = resNoiLayMau || [];
                dichVu.phongLayMauId =
                  (resNoiLayMau || []).length === 1
                    ? resNoiLayMau[0].phongLayMauId
                    : null;

                resolve(dichVu);

                // let tinhTien = await dispatch.tiepDonDichVu.tamTinhTien({
                //   data: [obj],
                //   loaiDichVu: dichVu?.loaiDichVu,
                //   alwaysResolve: true,
                // });

                // if (
                //   Array.isArray(tinhTien?.data) &&
                //   tinhTien?.data?.length > 0
                // ) {
                //   tinhTien = tinhTien.data[0];
                // }

                // if (tinhTien?.message) {
                //   refConfirm.current &&
                //     refConfirm.current.show(
                //       {
                //         title: t("common.canhBao"),
                //         // content: `<b>${tinhTien?.message} </b> <br/> Bạn có chắc chắn muốn <b> tiếp tục chỉ định dịch vụ </b>?`,
                //         content: t("tiepDon.tinhTienErrMsg", {
                //           message: tinhTien?.message || "",
                //         }),
                //         cancelText: t("common.quayLai"),
                //         okText: t("common.dongY"),
                //         classNameOkText: "button-warning",
                //         showBtnOk: true,
                //         typeModal: "warning",
                //       },
                //       () => {
                //         delete dichVu.id;
                //         dichVu.tinhTien = tinhTien?.nbDichVu || {};
                //         dichVu.thanhToan = false;
                //         dichVu.soLuong =
                //           dichVu?.soLuongMacDinh || dichVu.soLuong || 1;
                //         dichVu.isNew = true;
                //         dichVu.newId = random(1, 100000);
                //         dichVu.phongId = dichVu.phongId
                //           ? dichVu.phongId
                //           : dichVu?.dsPhongThucHien?.length === 1
                //           ? dichVu?.dsPhongThucHien[0]?.phongId
                //           : dichVu?.phongHenKhamId
                //           ? dichVu?.phongHenKhamId
                //           : [];
                //         dichVu.detachId =
                //           dichVu.detachId ||
                //           `${dichVu.dichVuId} - ${dichVu.phongId}`;
                //         if (dichVu?.henKham) {
                //           dichVu.bacSiKhamId = dichVu?.nguoiHenId;
                //           dichVu.ghiChu = dichVu?.ghiChu;
                //         }
                //         resolve(dichVu);
                //       },
                //       () => {
                //         reject({ message: tinhTien?.message });
                //         return;
                //       }
                //     );
                // } else {
                //   delete dichVu.id;
                //   dichVu.tinhTien = tinhTien?.nbDichVu || {};
                //   dichVu.giaKhongBaoHiem =
                //     tinhTien?.nbDichVu?.giaKhongBaoHiem ||
                //     dichVu.giaKhongBaoHiem;
                //   dichVu.giaBaoHiem =
                //     tinhTien?.nbDichVu?.giaBaoHiem || dichVu.giaBaoHiem;
                //   dichVu.thanhToan = false;
                //   dichVu.soLuong =
                //     dichVu?.soLuongMacDinh || dichVu.soLuong || 1;
                //   dichVu.isNew = true;
                //   dichVu.newId = random(1, 100000);
                //   dichVu.phongId = dichVu.phongId
                //     ? dichVu.phongId
                //     : dichVu?.dsPhongThucHien?.length === 1
                //     ? dichVu?.dsPhongThucHien[0]?.phongId
                //     : dichVu?.phongHenKhamId
                //     ? dichVu?.phongHenKhamId
                //     : [];
                //   dichVu.loaiHinhThanhToanId = loaiHinhThanhToanId;
                //   dichVu.detachId =
                //     dichVu.detachId || `${dichVu.dichVuId} - ${dichVu.phongId}`;
                //   if (dichVu?.henKham) {
                //     dichVu.bacSiKhamId = dichVu?.nguoiHenId;
                //     dichVu.ghiChu = dichVu?.ghiChu;
                //   }
                //   dichVu.dsNoiLayMau = resNoiLayMau || [];
                //   dichVu.phongLayMauId =
                //     (resNoiLayMau || []).length === 1
                //       ? resNoiLayMau[0].phongLayMauId
                //       : null;
                //   resolve(dichVu);
                // }
              });
            })
          );

          const mapBodyTinhTien = (_dsDv) =>
            _dsDv.map((_dv) => ({
              nbDotDieuTriId: nbDotDieuTriId,
              nbDichVu: {
                dichVuId: _dv?.dichVuId,
                boChiDinhId: _dv.boChiDinhId || undefined,
                soLuong: _dv?.soLuongMacDinh || _dv?.soLuong || 1,
                loaiDichVu: _dv?.loaiDichVu,
                khongTinhTien: _dv?.khongTinhTien,
                nbGoiDvId: _dv?.nbGoiDvId || undefined,
                nbGoiDvChiTietId: _dv?.nbGoiDvChiTietId || undefined,
                chiDinhTuLoaiDichVu: _dv?.chiDinhTuLoaiDichVu || 200,
                loaiHinhThanhToanId: _dv?.loaiHinhThanhToanId,
                nguonKhacId: _dv.nguonKhacId,
                khoaChiDinhId: khoaTiepDonId,
                ghiChu: _dv.ghiChu,
              },
              benhPhamId: _dv.benhPhamId,
              ...(_dv.phongHenKhamId && {
                nbDvKyThuat: {
                  phongThucHienId: _dv.phongHenKhamId,
                },
              }),
              ...(_dv?.nguoiHenId && {
                bacSiKhamId: _dv?.nguoiHenId,
              }),
            }));

          //Xử lý call api tính tiền theo loại dịch vụ
          const _dsDvKham = data.filter(
            (x) => x.loaiDichVu === LOAI_DICH_VU.KHAM
          );
          const tamTinhTienDVKham =
            _dsDvKham.length > 0
              ? await dispatch.tiepDonDichVu.tamTinhTien({
                  data: mapBodyTinhTien(_dsDvKham),
                  loaiDichVu: LOAI_DICH_VU.KHAM,
                  alwaysResolve: true,
                })
              : { data: [] };

          const _dsDvXN = data.filter(
            (x) => x.loaiDichVu === LOAI_DICH_VU.XET_NGHIEM
          );
          const tamTinhTienDVXN =
            _dsDvXN.length > 0
              ? await dispatch.tiepDonDichVu.tamTinhTien({
                  data: mapBodyTinhTien(_dsDvXN),
                  loaiDichVu: LOAI_DICH_VU.XET_NGHIEM,
                  alwaysResolve: true,
                })
              : { data: [] };

          const _dsDvCLS = data.filter(
            (x) =>
              x.loaiDichVu === LOAI_DICH_VU.CDHA ||
              x.loaiDichVu === LOAI_DICH_VU.PHAU_THUAT_THU_THUAT
          );
          const tamTinhTienDVCLS =
            _dsDvCLS.length > 0
              ? await dispatch.tiepDonDichVu.tamTinhTien({
                  data: mapBodyTinhTien(_dsDvCLS),
                  loaiDichVu: LOAI_DICH_VU.CDHA,
                  alwaysResolve: true,
                })
              : { data: [] };

          const _dsDvNgoaiDieuTri = data.filter(
            (x) => x.loaiDichVu === LOAI_DICH_VU.NGOAI_DIEU_TRI
          );
          const tamTinhTienDVNgoaiDieuTri =
            _dsDvNgoaiDieuTri.length > 0
              ? await dispatch.tiepDonDichVu.tamTinhTien({
                  data: mapBodyTinhTien(_dsDvNgoaiDieuTri),
                  loaiDichVu: LOAI_DICH_VU.NGOAI_DIEU_TRI,
                  alwaysResolve: true,
                })
              : { data: [] };

          const dataTinhTien = [
            ...(tamTinhTienDVKham?.data || []),
            ...(tamTinhTienDVXN?.data || []),
            ...(tamTinhTienDVCLS?.data || []),
            ...(tamTinhTienDVNgoaiDieuTri?.data || []),
          ];

          // Sort lại mảng tính tiền theo dữ liệu ban đầu
          const orderMap = new Map(data.map((val, idx) => [val, idx]));
          dataTinhTien.sort((x, y) => orderMap.get(x) - orderMap.get(y));

          const newData = await Promise.all(
            data.map((dichVu, index) => {
              return new Promise(async (resolve, reject) => {
                const tinhTien = dataTinhTien[index];

                if (tinhTien?.message) {
                  refConfirm.current &&
                    refConfirm.current.show(
                      {
                        title: t("common.canhBao"),
                        // content: `<b>${tinhTien?.message} </b> <br/> Bạn có chắc chắn muốn <b> tiếp tục chỉ định dịch vụ </b>?`,
                        content: t("tiepDon.tinhTienErrMsg", {
                          message: tinhTien?.message || "",
                        }),
                        cancelText: t("common.quayLai"),
                        okText: t("common.dongY"),
                        classNameOkText: "button-warning",
                        showBtnOk: true,
                        typeModal: "warning",
                      },
                      () => {
                        delete dichVu.id;
                        dichVu.tinhTien = tinhTien?.nbDichVu || {};
                        dichVu.thanhToan = false;
                        dichVu.soLuong =
                          dichVu?.soLuongMacDinh || dichVu.soLuong || 1;
                        dichVu.isNew = true;
                        dichVu.newId = random(1, 100000);
                        dichVu.phongId = dichVu.phongId
                          ? dichVu.phongId
                          : dichVu?.dsPhongThucHien?.length === 1
                          ? dichVu?.dsPhongThucHien[0]?.phongId
                          : dichVu?.phongHenKhamId
                          ? dichVu?.phongHenKhamId
                          : [];
                        dichVu.detachId =
                          dichVu.detachId ||
                          `${dichVu.dichVuId} - ${dichVu.phongId}`;
                        if (dichVu?.henKham) {
                          dichVu.bacSiKhamId = dichVu?.nguoiHenId;
                          dichVu.ghiChu = dichVu?.ghiChu;
                        }
                        resolve(dichVu);
                      },
                      () => {
                        reject({ message: tinhTien?.message });
                        return;
                      }
                    );
                } else {
                  delete dichVu.id;
                  dichVu.tinhTien = tinhTien?.nbDichVu || {};
                  dichVu.giaKhongBaoHiem =
                    tinhTien?.nbDichVu?.giaKhongBaoHiem ||
                    dichVu.giaKhongBaoHiem;
                  dichVu.giaBaoHiem =
                    tinhTien?.nbDichVu?.giaBaoHiem || dichVu.giaBaoHiem;
                  dichVu.thanhToan = false;
                  dichVu.soLuong =
                    dichVu?.soLuongMacDinh || dichVu.soLuong || 1;
                  dichVu.isNew = true;
                  dichVu.newId = random(1, 100000);
                  dichVu.phongId = dichVu.phongId
                    ? dichVu.phongId
                    : dichVu?.dsPhongThucHien?.length === 1
                    ? dichVu?.dsPhongThucHien[0]?.phongId
                    : dichVu?.phongHenKhamId
                    ? dichVu?.phongHenKhamId
                    : [];
                  // dichVu.loaiHinhThanhToanId = loaiHinhThanhToanId;
                  dichVu.detachId =
                    dichVu.detachId || `${dichVu.dichVuId} - ${dichVu.phongId}`;
                  if (dichVu?.henKham) {
                    dichVu.bacSiKhamId = dichVu?.nguoiHenId;
                    dichVu.ghiChu = dichVu?.ghiChu;
                  }
                  // dichVu.dsNoiLayMau = resNoiLayMau || [];
                  // dichVu.phongLayMauId =
                  //   (resNoiLayMau || []).length === 1
                  //     ? resNoiLayMau[0].phongLayMauId
                  //     : null;

                  resolve(dichVu);
                }
              });
            })
          );

          const finalData = [
            ...listDvChoose.map((item) => {
              item.isNew = false;
              return item;
            }),
            ...newData.map((item) => {
              item.isNew = true;
              return item;
            }),
          ];
          dispatch.tiepDonDichVu.updateData({
            listDvChoose: finalData,
          });
          resolve(finalData);
        } catch (error) {
          console.log(error);
          reject(error);
        }
      });
    },

    xoaDichVu: ({ dsDichVu }, state) => {
      return new Promise((resolve, reject) => {
        const listDvChoose = state.tiepDonDichVu.listDvChoose || [];
        const dsRemove = dsDichVu.map((item) => item.detachId);
        const data = listDvChoose.filter((x) => !dsRemove.includes(x.detachId));
        dispatch.tiepDonDichVu.updateData({ listDvChoose: data });
        resolve(data);
      });
    },
    themBo: ({ boChiDinhId, nbDotDieuTriId, loaiDoiTuongId }, state) => {
      return new Promise(async (resolve, reject) => {
        try {
          const dsDichVu = (
            await dichVuKyThuatProvider.searchAll({
              boChiDinhId,
              dsDoiTuongSuDung: 10,
              loaiDoiTuongId,
              chiDinhTuLoaiDichVu: LOAI_DICH_VU.TIEP_DON,
            })
          ).data;
          dispatch.tiepDonDichVu.themDichVu({
            dsDichVu,
            nbDotDieuTriId,
          });
          resolve(dsDichVu);
        } catch (error) {
          console.log(error);
          reject(error);
        }
      });
    },
    xoaBo: ({ boChiDinhId }, state) => {
      const { listDvChoose } = state.tiepDonDichVu;
      const listData = listDvChoose.filter(
        (x) => x.boChiDinhId !== boChiDinhId
      );
      dispatch.tiepDonDichVu.updateData({ listDvChoose: listData });
    },
    onSearchNbDv: ({ nbDotDieuTriId, ...rest }, state) => {
      return new Promise((resolve, reject) => {
        // const currentNbDotDieuTriId = state.tiepDonDichVu.currentNbDotDieuTriId; //lấy thông tin nbdotdieutri trong redux
        // const listDvChoose = state.tiepDonDichVu?.listDvChoose;
        // const { khoaTiepDonId } = state.tiepDon;
        const page = rest.page || 0;
        const size = rest.size || 10;
        nbDichVuProvider
          .searchAll({ nbDotDieuTriId, page, size, ...rest })
          .then((s) => {
            if (s?.code === 0) {
              let data = s.data || [];
              const listDvDaTiepDon = data.map((item, index) => {
                item.index = page * size + index + 1;
                item.key = guid();
                return item;
              });
              dispatch.tiepDonDichVu.updateData({
                listDvDaTiepDon,
                listDvDaTiepDonOriginal: data, // biến để xác định đata có thay đổi không để xử lý các thao tác liên quan kế tiếp vì listDvDaTiepDon có thể bị mutate
                totalElements: s?.totalElements || 0,
                page,
                size,
              });
              resolve(data);
              // const serviceSelected = groupBy(data, "loaiDichVu"); //gom nhóm theo loại dịch vụ
              // const promises = Object.keys(serviceSelected).map(
              //   //tao list promise theo loại dịch vụ
              //   (loaiDichVu) => {
              //     return new Promise((resolve, reject) => {
              //       dispatch.tiepDonDichVu
              //         .tamTinhTien({
              //           //voi mỗi loại dịch vụ thì gọi api tinh tien
              //           data: serviceSelected[loaiDichVu].map((dv) => {
              //             //{"data":[{"nbDotDieuTriId":"1255","nbDichVu":{"dichVuId":57,"soLuong":1,"loaiDichVu":10}}],"loaiDichVu":10}
              //             //generate body api tam tính tiền theo form phía trên
              //             return {
              //               nbDotDieuTriId:
              //                 serviceSelected[loaiDichVu][0].nbDotDieuTriId,
              //               nbDichVu: {
              //                 dichVuId: dv.dichVuId,
              //                 soLuong: dv.soLuong,
              //                 loaiDichVu: dv.loaiDichVu,
              //               },
              //             };
              //           }),
              //           loaiDichVu: parseInt(loaiDichVu),
              //         })
              //         .then((s) => {
              //           resolve(s.data || []);
              //         })
              //         .catch((e) => {
              //           resolve([]);
              //         });
              //     });
              //   }
              // );
              // Promise.all(promises)
              //   .then(async (s) => {
              //     //sau khi gọi xong tất cả api tạm tính tiền
              //     data = data.map((item) => {
              //       //duyệt qua danh sách dịch vụ đã chọn
              //       s.forEach(async (tts) => {
              //         //tìm tính tiền tương ứng với dịch vụ
              //         const tt = await tts.find((y) => {
              //           return y.nbDichVu?.dichVuId == item.dichVuId;
              //         });
              //         //cập nhật giá trị tiền vào dịch vụ
              //         item.tinhTien = tt?.nbDichVu || item?.tinhTien || {}; // thêm item.tinhTien vì khi lặp khi không tìm được sẽ gán giá trị không tìm được là undefined
              //       });
              //       item.tenPhong = item.tenPhongThucHien;

              //       return item;
              //     });
              //     let dataListDvChoose = data;
              //     if (currentNbDotDieuTriId == nbDotDieuTriId) {
              //       //check nếu trùng với thông tin đang request thì bỏ qua, không load ds dich vu ra nữa
              //       //muc đích là để hiển thị các dữ liệu caching trước đấy chưa được submit
              //       //tính lại tổng tiền theo người bệnh bhyt và nb không khám bảo hiểm , khi quay lại sửa thông tin
              //       let dataAfterChoose = listDvChoose?.filter((item) => {
              //         return data?.some(
              //           (item1) =>
              //             !(item1.dichVuId === item.dichVuId) && !item.id
              //         );
              //       });
              //       let checkData =
              //         dataAfterChoose?.length > 0
              //           ? [...dataAfterChoose, ...data]
              //           : data;
              //       const dataCustom =
              //         data.length === 0 ? listDvChoose : checkData;
              //       dataListDvChoose = dataCustom;
              //     }

              //     //dispart action
              //     let listPhong = [];
              //     try {
              //       listPhong =
              //         await dispatch.phongThucHien.getListPhongTheoDichVu({
              //           page: "",
              //           size: "",
              //           dsDichVuId: dataListDvChoose.map(
              //             (item) => item.dichVuId
              //           ),
              //           khoaChiDinhId: khoaTiepDonId,
              //         });
              //     } catch (error) {
              //       listPhong = [];
              //     }
              //     const phongByDichVuId = groupBy(listPhong, "dichVuId");
              //     dataListDvChoose.forEach((dichVu) => {
              //       dichVu.listPhongDv = phongByDichVuId[dichVu?.dichVuId];
              //     });
              //     dispatch.tiepDonDichVu.updateData({
              //       listDvChoose: dataListDvChoose,
              //       currentNbDotDieuTriId: nbDotDieuTriId, //cập nhật lại thong tin nbdotdieutriId
              //     });
              //     resolve(s);
              //   })
              //   .catch((e) => {
              //     dispatch.tiepDonDichVu.updateData({
              //       listDvChoose: [],
              //       currentNbDotDieuTriId: nbDotDieuTriId, //cập nhật lại thong tin nbdotdieutriId
              //     });
              //     reject(e);
              //   });
            } else {
              reject(s);
              message.error(s?.message);
            }
          })
          .catch((e) => {
            reject(e);
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          });
      });
    },
    getListonSearchNbDv: ({ nbDotDieuTriId, ...rest }, state) => {
      return new Promise((resolve, reject) => {
        nbDichVuProvider
          .searchAll({ nbDotDieuTriId, ...rest })
          .then((s) => {
            dispatch.tiepDonDichVu.updateData({
              listDvChoose: s.data,
            });
            resolve(s.data);
          })
          .catch((e) => {
            reject(e);
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          });
      });
    },
    tamTinhTien: (payload, state) => {
      return new Promise((resolve, reject) => {
        const { data, loaiDichVu, alwaysResolve = false } = payload;
        const api =
          loaiDichVu === LOAI_DICH_VU.KHAM
            ? nbDvKhamProvider.tamTinhTienDVKham
            : loaiDichVu === LOAI_DICH_VU.XET_NGHIEM
            ? dichVuXNProvider.tamTinhTienDVXN
            : loaiDichVu === LOAI_DICH_VU.CDHA
            ? nbDvCLSProvider.tamTinhTienDVCLS
            : loaiDichVu === LOAI_DICH_VU.PHAU_THUAT_THU_THUAT
            ? nbDvCLSProvider.tamTinhTienDVCLS
            : loaiDichVu === LOAI_DICH_VU.NGOAI_DIEU_TRI
            ? nbDvNgoaiDieuTriProvider.tamTinhTienDVNgoaiDieuTri
            : null;
        if (api) {
          api(data)
            .then((s) => {
              if (s?.code === 0 || alwaysResolve) {
                resolve(s);
              } else {
                reject(s);
                message.error(s?.message);
              }
            })
            .catch((e) => {
              if (alwaysResolve) {
                resolve(e);
              } else {
                reject(e);
                message.error(e?.message);
              }
            });
        }
      });
    },
    onDeleteDichVu: ({ id, loaiDichVu, data, index }, state) => {
      return new Promise((resolve, reject) => {
        if (id) {
          let api =
            loaiDichVu === 10
              ? nbDvKhamProvider.onDeleteDichVu
              : loaiDichVu === 20
              ? dichVuXNProvider.onDeleteDichVu
              : loaiDichVu === 30
              ? nbDvCLSProvider.onDeleteDichVu
              : loaiDichVu === 40
              ? nbDvCLSProvider.onDeleteDichVu
              : loaiDichVu === 60
              ? nbDvNgoaiDieuTriProvider.onDeleteDichVu
              : null;

          api &&
            api({ id })
              .then((s) => {
                if (s.code === 0) {
                  resolve(s);
                } else {
                  reject(s);
                  message.error(s.message);
                }
              })
              .catch((e) => {
                reject(e);
                message.error(e?.message);
              });
        }
        const { listDvKham, listDvChoose } = state.tiepDonDichVu;
        if (listDvKham && listDvChoose) {
          let listData = listDvChoose?.filter(
            (x) => x.detachId !== data.detachId
          );
          dispatch.tiepDonDichVu.updateData({
            listDvChoose: [...listData],
          });
        }
      });
    },
    resetLoadNbTiepDon: ({ tiepDonMoi = true }, state) => {
      const nbTiepTheoId = state.goiSo.nbTiepTheo?.id;
      const { loaiDoiTuongId, tiemChung } = state.tiepDon;
      dispatch.tiepDon.resetData({ loaiDoiTuongId, tiemChung });
      if (state.goiSo.quayTiepDonId && tiepDonMoi)
        //nếu đang chọn quầy tiếp đón thì get nbTiếp Theo
        dispatch.goiSo.getNbTiepTheo({
          id: state.goiSo.quayTiepDonId,
          data: {
            nbTiepTheoId: nbTiepTheoId,
          },
          isLoadNguoiBenhTiepDon: true,
        });
      dispatch.tiepDonDichVu.updateData({
        listDvChoose: [],
      });
    },
    getPhieuKhamBenh: (
      {
        id,
        data,
        isSilentSave = false,
        chiDinhTuLoaiDichVu,
        dsChiDinhTuLoaiDichVu,
        tiepDonMoi = true,
        inPhieuDichVuKham = true,
        isTuGoiNguoiBenhTiepTheo = true,
      },
      state,
      payload
    ) => {
      const nbTiepTheoId = state.goiSo.nbTiepTheo?.id;
      data = (data || []).filter((item) => item.id);
      const dataKham = data.filter(
        (item) => item.nbDichVu?.loaiDichVu === LOAI_DICH_VU.KHAM
      );
      const dataXN = data.filter(
        (item) => item.nbDichVu?.loaiDichVu === LOAI_DICH_VU.XET_NGHIEM
      );
      const dataCLS = data.filter((item) =>
        [LOAI_DICH_VU.PHAU_THUAT_THU_THUAT, LOAI_DICH_VU.CDHA].includes(
          item.nbDichVu?.loaiDichVu
        )
      );
      // const dataNgoaiDieuTri = data.filter(
      //   (item) => item.nbDichVu?.loaiDichVu === 60
      // );

      let promises = [];

      if (dataKham?.length > 0 && inPhieuDichVuKham) {
        promises.push(
          toSafePromise(
            nbDvKhamProvider.getPhieuChiDinh({
              nbDotDieuTriId: id,
              dsNbDichVuId: dataKham.map((item) => item.id),
              chiDinhTuLoaiDichVu,
              dsChiDinhTuLoaiDichVu,
              ...payload,
            })
          )
        );
      }
      if (dataXN?.length > 0) {
        promises.push(
          toSafePromise(
            nbDvXetNghiemProvider.getPhieuChiDinh({
              nbDotDieuTriId: id,
              dsNbDichVuId: dataXN.map((item) => item.id),
              chiDinhTuLoaiDichVu,
              dsChiDinhTuLoaiDichVu,
              ...payload,
            })
          )
        );
      }
      if (dataCLS?.length > 0) {
        promises.push(
          toSafePromise(
            nbDvCdhaTdcnPtTtProvider.getPhieuChiDinh({
              nbDotDieuTriId: id,
              dsNbDichVuId: dataCLS.map((item) => item.id),
              chiDinhTuLoaiDichVu,
              dsChiDinhTuLoaiDichVu,
              ...payload,
            })
          )
        );
      }
      //Bỏ tự động in với phiếu chỉ định dv ngoài điều trị
      // if (dataNgoaiDieuTri?.length > 0) {
      //   promises.push(
      //     nbDvNgoaiDieuTriProvider.getPhieuChiDinh({
      //       nbDotDieuTriId: id,
      //       dsNbDichVuId,
      //       ...payload,
      //     })
      //   );
      // }
      return Promise.all(promises).then((res) => {
        let error = res
          .filter((item) => item[0])
          .map((item) => item[0].message)
          .filter((item) => item);
        if (error?.length) message.error(error.join("\n"));
        let success = res.filter((item) => item[1]).map((item) => item[1]);

        let list = flatten(
          success.map((item) => {
            if (item.code == 0) {
              if (Array.isArray(item.data)) {
                return item.data;
              }
              return item.data ? [item.data] : [];
            }
            return [];
          })
          // res.reduce((init, item) => {
          //   if (Array.isArray(item?.data)) {
          //     let listPdfChild = item.data.map(
          //       (itemChild) => itemChild?.file?.pdf
          //     );
          //     init = [...init, ...listPdfChild];
          //     return init;
          //   }
          //   init = [...init, item?.data?.file?.pdf];
          //   return init;
          // }, [])
        );

        let isInNhanhBaoCao = list.some((item) =>
          [LOAI_IN.IN_NHANH, LOAI_IN.IN_NHANH_PREVIEW].includes(item.loaiIn)
        );
        if (isInNhanhBaoCao) {
          Promise.all(
            list.map((item) => {
              return printProvider.printPdf(item, { onlyInNhanh: true });
            })
          ).then((res) => {
            const listError = res
              .filter((item) => item.code != 0)
              .reduce((pre, cur) => {
                if (Array.isArray(cur.payload)) {
                  return [...pre, ...cur.payload];
                } else {
                  return [...pre, cur.payload];
                }
              }, [])
              .map((item) => item.file.pdf)
              .filter((item) => item);
            if (listError.length) {
              printProvider.printMergePdf(listError);
            }
          });
        } else {
          if (list.length > 0) {
            printProvider.printMergePdf(list.map((item) => item.file.pdf));
          }
        }
        if (isSilentSave) return; // nếu lưu khi bấm btn tạm ứng thì ko xử lý reset hay chuyển page

        if (state.goiSo.quayTiepDonId && tiepDonMoi && isTuGoiNguoiBenhTiepTheo)
          //nếu đang chọn quầy tiếp đón thì get nbTiếp Theo
          dispatch.goiSo.getNbTiepTheo({
            id: state.goiSo.quayTiepDonId,
            data: {
              nbTiepTheoId: nbTiepTheoId,
            },
            isLoadNguoiBenhTiepDon: true,
          });
        dispatch.tiepDonDichVu.updateData({
          listDvChoose: [],
        });
      });
    },
    getDsGoiDvChiTiet: (params) => {
      return new Promise((resolve, reject) => {
        nbGoiDvChiTietProvider
          .search(params)
          .then((s) => {
            if (s?.code === 0) {
              let data = (s?.data || []).map((x) => ({
                ...x,
                ten: x.tenDichVu,
                ma: x.maDichVu,
                id: undefined,
                nbGoiDvChiTietId: x.id,
              }));
              dispatch.tiepDonDichVu.updateData({ listDvKham: data });
              resolve(s);
            } else {
              reject(s);
            }
          })
          .catch((e) => {
            reject(e);
          });
      });
    },
    postNbGoiDv: (params) => {
      return new Promise((resolve, reject) => {
        nbGoiDvProvider
          .post(params)
          .then((s) => {
            if (s?.code === 0) {
              message.success(
                t("khamBenh.themMoiThanhCongGoiDichVuChoNguoiBenh")
              );
              resolve(s);
            } else {
              reject(s);
            }
          })
          .catch((e) => {
            reject(e);
          });
      });
    },
    tongTien: (params) => {
      return new Promise((resolve, reject) => {
        nbDichVuProvider
          .tongTien(params)
          .then((s) => {
            if (s?.code === 0) {
              dispatch.tiepDonDichVu.updateData({
                tienDieuTri: s.data,
              });
              resolve(s?.data);
            } else {
              reject(s);
            }
          })
          .catch((e) => {
            reject(e);
          });
      });
    },
    thayDoiThongTinDichVu: ({ data }, state) => {
      return new Promise((resolve, reject) => {
        const chiDinhKham = new Promise((resolve, reject) => {
          const body = data.filter(
            (item) => item.nbDichVu.loaiDichVu === LOAI_DICH_VU.KHAM
          );
          if (body.length)
            return nbDvKhamProvider
              .themThongTin(body)
              .then((s) => {
                resolve(s);
              })
              .catch((e) => {
                reject(e);
              });
          else resolve(0);
        });

        const chiDinhDVXN = new Promise((resolve, reject) => {
          const body = data.filter(
            (item) => item.nbDichVu.loaiDichVu === LOAI_DICH_VU.XET_NGHIEM
          );
          if (body.length)
            return dichVuXNProvider
              .themThongTin(body)
              .then((s) => {
                if (s.code === 0) {
                  resolve(s);
                } else {
                  reject(s);
                }
              })
              .catch((e) => {
                reject(e);
              });
          else resolve(0);
        });

        const chiDinhDVCLS = new Promise((resolve, reject) => {
          const body = data.filter(
            (item) =>
              item.nbDichVu.loaiDichVu === LOAI_DICH_VU.CDHA ||
              item.nbDichVu.loaiDichVu === LOAI_DICH_VU.PHAU_THUAT_THU_THUAT
          );
          if (body.length)
            nbDvCLSProvider
              .themThongTin(body)
              .then((s) => {
                if (s.code === 0) {
                  resolve(s);
                } else {
                  reject(s);
                }
              })
              .catch((e) => {
                reject(e);
              });
          else resolve(0);
        });

        const chiDinhNgoaiDieuTri = new Promise((resolve, reject) => {
          const body = data.filter(
            (item) => item.nbDichVu.loaiDichVu === LOAI_DICH_VU.NGOAI_DIEU_TRI
          );
          if (body.length)
            nbDvNgoaiDieuTriProvider
              .themThongTinDV(body)
              .then((s) => {
                if (s.code === 0) {
                  resolve(s);
                } else {
                  reject(s);
                }
              })
              .catch((e) => {
                reject(e);
              });
          else resolve(0);
        });
        return Promise.all([
          chiDinhKham,
          chiDinhDVXN,
          chiDinhDVCLS,
          chiDinhNgoaiDieuTri,
        ])
          .then((response) => {
            let errMessage = [];
            response.forEach((res) => {
              if (res === 0 || res.code === 0) {
                message.success(t("common.capNhatThanhCong"));
                return;
              }
              const listMessages = res.data
                .filter((item) => item.code && item.code !== 0)
                .map(
                  (item2) =>
                    `(${item2?.nbDichVu?.dichVu?.ten} - ${item2.message})`
                );
              errMessage = [...errMessage, ...listMessages];
            });
            errMessage = [...new Set(errMessage)];
            if (errMessage.length) {
              message.error(errMessage.join());
            } else {
              message.success(t("common.capNhatThanhCong"));
            }
            resolve({
              code: 0,
              response,
              errMessage,
            });
          })
          .catch((e) => {
            message.error(e?.message);
            reject(e);
          });
      });
    },

    tiepDonLichHenKham: (payload) => {
      return new Promise((resolve, reject) => {
        nbDanhSachLichHenProvider
          .tiepDon(payload)
          .then((s) => {
            if (s?.code === 0) {
              resolve(s);
            } else {
              reject(s);
            }
          })
          .catch((e) => {
            reject(e);
          });
      });
    },
    keDichVuKhamBenh: (payload) => {
      return new Promise((resolve, reject) => {
        nbDvKhamProvider
          .chiDinhDVKham(payload)
          .then((s) => {
            if (s?.code === 0) {
              message.success(t("common.capNhatThanhCong"));
              resolve(s);
            } else {
              reject(s);
            }
          })
          .catch((e) => {
            reject(e);
          });
      });
    },
  }),
};
