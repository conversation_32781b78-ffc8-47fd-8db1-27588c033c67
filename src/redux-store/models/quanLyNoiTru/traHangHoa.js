import { message } from "antd";
import { LOAI_DICH_VU } from "constants/index";
import nbDvThuocProvider from "data-access/nb-dv-thuoc-provider";
import nbDvVatTuProvider from "data-access/nb-dv-vat-tu-provider";
import nbDvHoaChatProvider from "data-access/nb-dv-hoa-chat-provider";
import { t } from "i18next";
import nbDvVacxinProvider from "data-access/tiemChung/nb-dv-vacxin-provider";
import khoTonKhoProvider from "data-access/kho/kho-ton-kho-provider";
import nbDvChePhamDinhDuongProvider from "data-access/nb-dv-che-pham-dinh-duong-provider";
import { cloneDeep, isEmpty, orderBy } from "lodash";
import { isArray, isObject } from "utils/index";

export default {
  state: {
    listHangHoaTra: [],
    loaiHangHoa: LOAI_DICH_VU.THUOC,
    dataSearch: {},
    dataSortColumn: {},
  },
  reducers: {
    updateData(state, payload = {}) {
      return { ...state, ...payload };
    },
  },
  effects: (dispatch) => ({
    getListHangHoa: ({ dataSortColumn: sort, ...payload }, state) => {
      const loaiHangHoa = payload?.loaiHangHoa || state.traHangHoa.loaiHangHoa;
      const dataSortColumn = sort || state.traHangHoa.dataSortColumn || {};

      const sortResData = (data) => {
        let _data = cloneDeep(data);

        let _dataSortColumn = Object.keys(dataSortColumn).reduce(
          (result, key) =>
            !!dataSortColumn[key]
              ? { ...result, [key]: dataSortColumn[key] }
              : result,
          {}
        );
        if (!isEmpty(_dataSortColumn)) {
          _data = orderBy(
            _data,
            Object.keys(dataSortColumn),
            Object.values(dataSortColumn).map((item) =>
              item === 1 ? "asc" : "desc"
            )
          );
        }

        return _data;
      };

      switch (loaiHangHoa) {
        case LOAI_DICH_VU.THUOC:
          return new Promise((resolve, reject) => {
            nbDvThuocProvider
              .searchTongHop({
                ...payload,
                nbDotDieuTriId: payload.nbDotDieuTriId,
                nhaThuoc: false,
                // trangThai: TRANG_THAI_THUOC.DA_PHAT.id,
                dsTrangThai: [30, 40, 50, 60],
              })
              .then((s) => {
                let _data = sortResData(s?.data || []);

                dispatch.traHangHoa.updateData({
                  listHangHoaTra: _data.map((item, index) => {
                    item.index = index + 1;
                    return item;
                  }),
                  loaiHangHoa,
                  dataSortColumn,
                });
                resolve(s);
              })
              .catch((e) => {
                message.error(
                  e?.message || t("common.xayRaLoiVuiLongThuLaiSau")
                );
                dispatch.traHangHoa.updateData({
                  listData: [],
                });
                reject(e);
              });
          });
        case LOAI_DICH_VU.VAT_TU:
          return new Promise((resolve, reject) => {
            nbDvVatTuProvider
              .searchTongHop({
                ...payload,
                nbDotDieuTriId: payload.nbDotDieuTriId,
                phat: true,
              })
              .then((s) => {
                let _data = sortResData(s?.data || []);

                dispatch.traHangHoa.updateData({
                  listHangHoaTra: _data.map((item, index) => {
                    item.index = index + 1;
                    item.dsTra = item.dsTra || [];
                    return item;
                  }),
                  loaiHangHoa,
                  dataSortColumn,
                });

                resolve(s);
              })
              .catch((e) => {
                message.error(
                  e?.message || t("common.xayRaLoiVuiLongThuLaiSau")
                );
                dispatch.traHangHoa.updateData({
                  listData: [],
                });
                reject(e);
              });
          });
        case LOAI_DICH_VU.HOA_CHAT:
          return new Promise((resolve, reject) => {
            nbDvHoaChatProvider
              .searchTongHop({
                ...payload,
                nbDotDieuTriId: payload.nbDotDieuTriId,
                phat: true,
              })
              .then((s) => {
                let _data = sortResData(s?.data || []);

                dispatch.traHangHoa.updateData({
                  listHangHoaTra: _data.map((item, index) => {
                    item.index = index + 1;
                    item.dsTra = item.dsTra || [];
                    return item;
                  }),
                  loaiHangHoa,
                  dataSortColumn,
                });

                resolve(s);
              })
              .catch((e) => {
                message.error(
                  e?.message || t("common.xayRaLoiVuiLongThuLaiSau")
                );
                dispatch.traHangHoa.updateData({
                  listData: [],
                });
                reject(e);
              });
          });
        case LOAI_DICH_VU.VAC_XIN:
          return new Promise((resolve, reject) => {
            nbDvVacxinProvider
              .search({
                ...payload,
                nbDotDieuTriId: payload.nbDotDieuTriId,
                trangThaiXuatKho: 30,
              })
              .then((s) => {
                let _data = sortResData(s?.data || []);

                dispatch.traHangHoa.updateData({
                  listHangHoaTra: _data.map((item, index) => {
                    item.index = index + 1;
                    return item;
                  }),
                  loaiHangHoa,
                  dataSortColumn,
                });
                resolve(s);
              })
              .catch((e) => {
                message.error(
                  e?.message || t("common.xayRaLoiVuiLongThuLaiSau")
                );
                dispatch.traHangHoa.updateData({
                  listData: [],
                });
                reject(e);
              });
          });
        case LOAI_DICH_VU.CHE_PHAM_DINH_DUONG:
          return new Promise((resolve, reject) => {
            nbDvChePhamDinhDuongProvider
              .searchTongHop({
                ...payload,
                nbDotDieuTriId: payload.nbDotDieuTriId,
                phat: true,
              })
              .then((s) => {
                let _data = sortResData(s?.data || []);

                dispatch.traHangHoa.updateData({
                  listHangHoaTra: _data.map((item, index) => {
                    item.index = index + 1;
                    return item;
                  }),
                  loaiHangHoa,
                  dataSortColumn,
                });
                resolve(s);
              })
              .catch((e) => {
                message.error(
                  e?.message || t("common.xayRaLoiVuiLongThuLaiSau")
                );
                dispatch.traHangHoa.updateData({
                  listData: [],
                });
                reject(e);
              });
          });
      }
    },

    postDsDvThuocTraKhoTatCa: (payload, state) => {
      return new Promise((resolve, reject) => {
        const loaiHangHoa =
          payload?.loaiHangHoa || state.traHangHoa.loaiHangHoa;

        const thenFunc = (s) => {
          if (s?.code === 0) {
            resolve(s);
            message.success(s?.message || t("common.themMoiThanhCongDuLieu"));
          } else {
            reject(s);
            message.error(s?.message);
          }
        };

        const catchFunc = (e) => {
          reject(e);
          message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
        };

        switch (loaiHangHoa) {
          case LOAI_DICH_VU.THUOC:
            nbDvThuocProvider
              .postDsDvThuocTraKhoTatCa(payload)
              .then(thenFunc)
              .catch(catchFunc);
            break;
          case LOAI_DICH_VU.VAT_TU:
            nbDvVatTuProvider
              .postDsDvVatTuTraKhoTatCa(payload)
              .then(thenFunc)
              .catch(catchFunc);
            break;

          case LOAI_DICH_VU.VAC_XIN:
            nbDvVacxinProvider
              .postDsDvVacxinTraKhoTatCa(payload)
              .then(thenFunc)
              .catch(catchFunc);
            break;
          case LOAI_DICH_VU.CHE_PHAM_DINH_DUONG:
            nbDvChePhamDinhDuongProvider
              .postDsDvThuocTraKhoTatCa(payload)
              .then(thenFunc)
              .catch(catchFunc);
            break;

          default:
            break;
        }
      });
    },

    postDsDvThuocTraKho: (payload, state) => {
      return new Promise((resolve, reject) => {
        const loaiHangHoa =
          payload?.loaiHangHoa || state.traHangHoa.loaiHangHoa;

        const thenFunc = (s) => {
          if (s?.code === 0) {
            resolve(s);
            message.success(s?.message || t("common.themMoiThanhCongDuLieu"));
          } else {
            reject(s);
            message.error(s?.message);
          }
        };

        const catchFunc = (e) => {
          reject(e);
          message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
        };
        const _payload = isArray(payload)
          ? payload
          : isObject(payload, true) && payload.payload
          ? payload.payload
          : [];
        switch (loaiHangHoa) {
          case LOAI_DICH_VU.THUOC:
            nbDvThuocProvider
              .postDsDvThuocTraKho(_payload)
              .then(thenFunc)
              .catch(catchFunc);
            break;

          case LOAI_DICH_VU.VAT_TU:
            nbDvVatTuProvider
              .postDsDvVatTuTraKho(_payload)
              .then(thenFunc)
              .catch(catchFunc);
            break;

          case LOAI_DICH_VU.HOA_CHAT:
            nbDvHoaChatProvider
              .postDsDvHoaChatTraKho(_payload)
              .then(thenFunc)
              .catch(catchFunc);
            break;

          case LOAI_DICH_VU.VAC_XIN:
            nbDvVacxinProvider
              .postDsDvVacxinTraKho(_payload)
              .then(thenFunc)
              .catch(catchFunc);
            break;
          case LOAI_DICH_VU.CHE_PHAM_DINH_DUONG:
            nbDvChePhamDinhDuongProvider
              .postDsDvThuocTraKho(_payload)
              .then(thenFunc)
              .catch(catchFunc);
            break;

          default:
            break;
        }
      });
    },

    putDsDvThuocTraKho: (payload, state) => {
      return new Promise((resolve, reject) => {
        const loaiHangHoa =
          payload?.loaiHangHoa || state.traHangHoa.loaiHangHoa;

        const thenFunc = (s) => {
          if (s?.code === 0) {
            resolve(s);
            message.success(s?.message || t("common.capNhatThanhCong"));
          } else {
            reject(s);
            message.error(s?.message);
          }
        };

        const catchFunc = (e) => {
          reject(e);
          message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
        };

        switch (loaiHangHoa) {
          case LOAI_DICH_VU.THUOC:
            nbDvThuocProvider
              .putDsDvThuocTraKho(payload)
              .then(thenFunc)
              .catch(catchFunc);
            break;

          case LOAI_DICH_VU.VAT_TU:
            nbDvVatTuProvider
              .putDsDvVatTuKho(payload)
              .then(thenFunc)
              .catch(catchFunc);
            break;

          case LOAI_DICH_VU.HOA_CHAT:
            nbDvHoaChatProvider
              .putDsDvHoaChatTraKho(payload)
              .then(thenFunc)
              .catch(catchFunc);
            break;

          case LOAI_DICH_VU.VAC_XIN:
            nbDvVacxinProvider
              .putDsDvVacxinTraKho(payload)
              .then(thenFunc)
              .catch(catchFunc);
            break;
          case LOAI_DICH_VU.CHE_PHAM_DINH_DUONG:
            nbDvChePhamDinhDuongProvider
              .putDsDvThuocTraKho(payload)
              .then(thenFunc)
              .catch(catchFunc);
            break;

          default:
            break;
        }
      });
    },

    deleteDsDvThuocTraKho: (payload, state) => {
      return new Promise((resolve, reject) => {
        const loaiHangHoa =
          payload?.loaiHangHoa || state.traHangHoa.loaiHangHoa;

        const thenFunc = (s) => {
          if (s?.code === 0) {
            resolve(s);
            message.success(s?.message || t("common.xoaDuLieuThanhCong"));
          } else {
            reject(s);
            message.error(s?.message);
          }
        };

        const catchFunc = (e) => {
          reject(e);
          message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
        };

        switch (loaiHangHoa) {
          case LOAI_DICH_VU.THUOC:
            nbDvThuocProvider
              .deleteDsDvThuocTraKho(payload)
              .then(thenFunc)
              .catch(catchFunc);
            break;

          case LOAI_DICH_VU.VAT_TU:
            nbDvVatTuProvider
              .deleteDsDvVatTuTraKho(payload)
              .then(thenFunc)
              .catch(catchFunc);
            break;

          case LOAI_DICH_VU.HOA_CHAT:
            nbDvHoaChatProvider
              .deleteDsDvHoaChatTraKho(payload)
              .then(thenFunc)
              .catch(catchFunc);
            break;

          case LOAI_DICH_VU.VAC_XIN:
            nbDvVacxinProvider
              .deleteDsDvVacxinTraKho(payload)
              .then(thenFunc)
              .catch(catchFunc);
            break;
          case LOAI_DICH_VU.CHE_PHAM_DINH_DUONG:
            nbDvChePhamDinhDuongProvider
              .deleteDsDvThuocTraKho(payload)
              .then(thenFunc)
              .catch(catchFunc);
            break;

          default:
            break;
        }
      });
    },
    checkHangHoaVuotCoso: (payload, state) => {
      return new Promise((resolve, reject) => {
        khoTonKhoProvider
          .checkHangHoaVuotCoSo(payload)
          .then((s) => {
            resolve(s);
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },
  }),
};
