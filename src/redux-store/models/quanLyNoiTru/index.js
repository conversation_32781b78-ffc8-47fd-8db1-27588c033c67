import nbDotDieuTriProvider from "data-access/nb-dot-dieu-tri-provider";
import nbChuyenVienProvider from "data-access/noiTru/nb-chuyen-vien-provider";
import { message } from "antd";
import { combineSort } from "utils";
import {
  PAGE_SIZE,
  PAGE_DEFAULT,
  LIST_CHON_TIEU_CHI_PTTT,
  LOAI_DICH_VU,
} from "constants/index";
import { t } from "i18next";
import nbDvXetNghiemProvider from "data-access/nb-dv-xet-nghiem-provider";
import nbDvCdhaProvider from "data-access/nb-dv-cdha-tdcn-pt-tt-provider";
import nhomDichVuCap2Provider from "data-access/categories/dm-nhom-dich-vu-cap2-provider";
import nbDvKyThuatProvider from "data-access/nb-dv-ky-thuat-provider";
import nbToDieuTriProvider from "data-access/nb-to-dieu-tri-provider";
import nbChuyenKhoaProvider from "data-access/noiTru/nb-chuyen-khoa-provider";
import nbDvChePhamMauProvider from "data-access/nb-dv-che-pham-mau-provider";
import nbPhieuThuProvider from "data-access/nb-phieu-thu-provider";
import { uniqBy } from "lodash";
import nbDvKhamProvider from "data-access/nb-dv-kham-provider";
import nbDichVuProvider from "data-access/nb-dich-vu-provider";

export default {
  state: {
    listNbLapBenhAn: [],
    totalElements: null,
    page: PAGE_DEFAULT,
    size: PAGE_SIZE,
    dataEditDefault: {},
    dataSearch: {},
    listData: [],
    dataSortColumn: {},
    thongTinBenhNhan: [],
    listNbTiepTheo: [],
    nbLapBenhAn: {},
    thongTinNb: {},
  },
  reducers: {
    updateData(state, payload = {}) {
      return { ...state, ...payload };
    },
  },
  effects: (dispatch) => ({
    onSizeChange: ({ ...rest }, state) => {
      dispatch.quanLyNoiTru.updateData({
        page: 0,
        ...rest,
      });
      dispatch.quanLyNoiTru.onSearch({ ...rest });
    },
    onSearch: ({ page = 0, ...payload }, state) => {
      let newState = { isLoading: true, page };
      dispatch.quanLyNoiTru.updateData(newState);
      let size = payload.size || state.quanLyNoiTru.size || 10;
      const sort = combineSort(
        payload.dataSortColumn || state.quanLyNoiTru.dataSortColumn || {}
      );
      const dataSearch =
        payload.dataSearch || state.quanLyNoiTru.dataSearch || {};

      nbDotDieuTriProvider
        .getNbLapBenhAn({
          page,
          size,
          sort,
          ...dataSearch,
        })
        .then((s) => {
          dispatch.quanLyNoiTru.updateData({
            listNbLapBenhAn: (s?.data || []).map((item, index) => {
              item.index = page * size + index + 1;
              return item;
            }),
            isLoading: false,
            totalElements: s?.totalElements || 0,
            page,
          });
        })
        .catch((e) => {
          message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          dispatch.quanLyNoiTru.updateData({
            listData: [],
            isLoading: false,
          });
        });
    },
    onSortChange: ({ ...payload }, state) => {
      const dataSortColumn = {
        ...state.quanLyNoiTru.dataSortColumn,
        ...payload,
      };
      dispatch.quanLyNoiTru.updateData({
        page: 0,
        dataSortColumn,
      });
      dispatch.quanLyNoiTru.onSearch({
        page: 0,
        dataSortColumn,
      });
    },
    onChangeInputSearch: ({ ...payload }, state) => {
      const dataSearch = {
        ...(state.quanLyNoiTru.dataSearch || {}),
        ...payload,
      };
      dispatch.quanLyNoiTru.updateData({
        page: 0,
        dataSearch,
      });
      dispatch.quanLyNoiTru.onSearch({
        page: 0,
        dataSearch,
      });
    },
    getNbLapBenhAnById: (id) => {
      nbDotDieuTriProvider.getNbLapBenhAnById(id).then((s) => {
        if (s?.code === 0) {
          dispatch.quanLyNoiTru.updateData({ nbLapBenhAn: s?.data });
        } else {
          dispatch.quanLyNoiTru.updateData({ nbLapBenhAn: {} });
        }
      });
    },
    postLapBenhAn: (payload) => {
      const id = payload.id;
      return new Promise((resolve, reject) => {
        nbDotDieuTriProvider
          .postLapBenhAn(id, { ...payload, id: null })
          .then((s) => {
            if (s?.code === 0 || s?.code === 7930) {
              resolve(s);
              if (s?.code === 0) {
                message.success(t("quanLyNoiTru.lapBenhAnThanhCong"));
              }
            } else {
              reject(s);
              message.error(s?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            }
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },
    xoaBenhAn: (id) => {
      return new Promise((resolve, reject) => {
        nbDotDieuTriProvider
          .xoaBenhAn(id)
          .then((s) => {
            if (s?.code === 0) {
              resolve(s);
              message.success(t("quanLyNoiTru.xoaBenhAnThanhCong"));
            } else {
              reject(s);
              message.error(s?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            }
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },

    huyBenhAn: (id) => {
      return new Promise((resolve, reject) => {
        nbDotDieuTriProvider
          .huyBenhAn(id)
          .then((s) => {
            if (s?.code === 0) {
              resolve(s);
              message.success(t("quanLyNoiTru.huyBenhAnThanhCong"));
            } else {
              reject(s);
              message.error(s?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            }
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },
    huyBenhAnDaiHan: (id) => {
      return new Promise((resolve, reject) => {
        nbDotDieuTriProvider
          .huyBenhAnDaiHan(id)
          .then((s) => {
            if (s?.code === 0) {
              resolve(s);
              message.success(t("quanLyNoiTru.huyBenhAnDaiHanThanhCong"));
            } else {
              reject(s);
              message.error(s?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            }
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },
    getDsGiayChuyenVien: (nbDotDieuTriId) => {
      return new Promise((resolve, reject) => {
        nbChuyenVienProvider
          .getDsGiayChuyenTuyen({ nbDotDieuTriId })
          .then((s) => {
            if (s?.code === 0) {
              resolve(s.data);
            } else {
              reject(s);
            }
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },
    updateGiayChuyenVien: (data) => {
      return new Promise((resolve, reject) => {
        nbChuyenVienProvider
          .put(data)
          .then((s) => {
            if (s?.code === 0) {
              resolve(s.data);
              message.success(t("common.capNhatThanhCong"));
            } else {
              reject(s);
            }
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },
    getPhieuChuyenVien: ({ nbDotDieuTriId }) => {
      return new Promise((resolve, reject) => {
        nbChuyenVienProvider
          .getPhieuChuyenVien({ nbDotDieuTriId })
          .then((s) => {
            if (s?.code === 0) {
              resolve(s.data);
            } else {
              reject(s);
            }
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },

    putLapBenhAn: (payload) => {
      return new Promise((resolve, reject) => {
        nbDotDieuTriProvider
          .putLapBenhAn(payload)
          .then((s) => {
            if (s?.code === 0) {
              resolve(s);
              message.success(t("quanLyNoiTru.capNhatBenhAnThanhCong"));
            } else {
              reject(s);
              message.error(s?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            }
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },
    getTieuChi: ({ maBaoCao, dataSearch }) => {
      return new Promise(async (resolve, reject) => {
        let dsTieuChi = [];
        try {
          switch (true) {
            case ["P144", "P943", "P944", "P1232", "P1233"].includes(maBaoCao):
              if (!dataSearch.dataNHOM_GIAI_PHAU_BENH) return;
              const res = await nhomDichVuCap2Provider.searchAll({
                ma: dataSearch.dataNHOM_GIAI_PHAU_BENH,
              });
              const nhomDichVuCap2Id = (res.data || []).find(
                (item) => item.ma === dataSearch.dataNHOM_GIAI_PHAU_BENH
              )?.id;
              dsTieuChi = await nbDvXetNghiemProvider.searchAll({
                nbDotDieuTriId: dataSearch.nbDotDieuTriId,
                dsTrangThaiHoan: [0, 10, 20],
                nhomDichVuCap2Id,
                size: "",
              });
              dsTieuChi = dsTieuChi.data;

              break;
            case maBaoCao === "P680":
              if (!dataSearch.dataNHOM_GIAI_PHAU_BENH) return;
              const _res = await nhomDichVuCap2Provider.searchAll({
                ma: dataSearch.dataNHOM_GIAI_PHAU_BENH,
              });
              const _nhomDichVuCap2Id = (_res.data || []).find(
                (item) => item.ma === dataSearch.dataNHOM_GIAI_PHAU_BENH
              )?.id;
              const dsTieuChiCls = await nbDvCdhaProvider.getTongHopDichVuCLS({
                nbDotDieuTriId: dataSearch.nbDotDieuTriId,
                dsTrangThaiHoan: [0, 10, 20],
                nhomDichVuCap2Id: _nhomDichVuCap2Id,
                size: "",
              });
              dsTieuChi = dsTieuChiCls?.data;

              break;
            case LIST_CHON_TIEU_CHI_PTTT.includes(maBaoCao):
              dsTieuChi = await nbDvCdhaProvider.getDsPttt({
                page: 0,
                size: 500,
                nbDotDieuTriId: dataSearch.nbDotDieuTriId,
                dsNhomDichVuCap1Id: dataSearch?.dsNhomDichVuCap1Id,
                dsTrangThaiHoan: [0, 10, 20],
              });
              dsTieuChi = dsTieuChi.data;
              break;
            case ["P167", "P168"].includes(maBaoCao):
              dsTieuChi = await nbDvCdhaProvider.getTongHopDichVuCLS({
                page: 0,
                size: 100,
                ...dataSearch,
              });
              dsTieuChi = dsTieuChi.data;
              break;
            case maBaoCao === "P137":
              dsTieuChi = await nbDvChePhamMauProvider.searchAll({
                page: 0,
                size: 100,
                dsTrangThaiHoan: [0, 10, 20],
                ...dataSearch,
              });
              dsTieuChi = dsTieuChi.data;
              break;
            case maBaoCao === "P372":
              dsTieuChi = await nbDvKyThuatProvider.getDsDichVu({
                nbDotDieuTriId: dataSearch.nbDotDieuTriId,
                guiVitimes: true,
                loaiDichVu: LOAI_DICH_VU.XET_NGHIEM,
                page: "",
                size: "",
              });
              dsTieuChi = dsTieuChi.data;

              break;
            case maBaoCao === "P143":
              dsTieuChi = await nbChuyenKhoaProvider.search({
                ...dataSearch,
                page: "",
                size: "",
              });
              dsTieuChi = uniqBy(dsTieuChi.data, "khoaId");
              break;
            case ["P929", "P930"]?.includes(maBaoCao):
              dsTieuChi = await nbDvKhamProvider.searchAll({
                ...dataSearch,
                page: "",
                size: "",
              });
              dsTieuChi = dsTieuChi.data;
              break;
            case [
              "P1043",
              "P1044",
              "P1045",
              "P1047",
              "P1049",
              "P1050",
              "P1051",
              "P1052",
            ]?.includes(maBaoCao):
              dsTieuChi = await nbDvChePhamMauProvider.searchAll({
                page: 0,
                size: 100,
                dsTrangThaiHoan: [0, 10, 20],
                nbDotDieuTriId: dataSearch?.nbDotDieuTriId,
                chiDinhTuDichVuId: dataSearch?.chiDinhTuDichVuId,
              });
              dsTieuChi = dsTieuChi.data;
              break;
            case ["P611"].includes(maBaoCao):
              dsTieuChi = await nbDichVuProvider.searchAll({
                page: "",
                size: "",
                active: "",
                nbDotDieuTriId: dataSearch?.nbDotDieuTriId,
                dsLoaiDichVu: [
                  LOAI_DICH_VU.CDHA,
                  LOAI_DICH_VU.PHAU_THUAT_THU_THUAT,
                ],
              });
              dsTieuChi = dsTieuChi.data;
              break;
            default:
              console.error("maBaoCao is required!");
              break;
          }
        } catch (error) {
          console.error(error);
        }

        resolve(dsTieuChi);
      });
    },
    updateTruongKhoa: ({ nbDotDieuTriId, ...rest }) => {
      return new Promise((resolve, reject) => {
        nbDotDieuTriProvider
          .updateTruongKhoa({ nbDotDieuTriId, ...rest })
          .then((s) => {
            if (s?.code === 0) {
              resolve(s);
            } else {
              reject(s);
              message.error(s?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            }
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },
    getDsPhieuThuNbNoiTru: (payload) => {
      return new Promise((resolve, reject) => {
        nbPhieuThuProvider
          .searchAll(payload)
          .then((s) => {
            if (s?.code === 0) {
              resolve(s.data);
            } else {
              reject(s);
            }
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },
    getChiTietPhieuThu: (id) => {
      return new Promise((resolve, reject) => {
        nbPhieuThuProvider
          .getByIdTongHop(id)
          .then((s) => {
            if (s?.code === 0) {
              resolve(s.data);
            } else {
              reject(s);
            }
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },
  }),
};
