import React from "react";
import appUtils from "utils/app-utils";
import i18n from "i18n";
import benhVienProvider from "data-access/categories/dm-benh-vien-provider";
import fileUtils from "utils/file-utils";
import cacheUtils from "lib-utils/cache-utils";
import { message } from "antd";
import { t } from "i18next";
import { ISOFH_TOOL_HOST, getBackendUrl, dataPath } from "client/request";
import { THONG_TIN_MAY_TINH } from "client/api";
import utilsProvider from "data-access/utils-provider";
import { CACHE_KEY } from "constants/index";
import { SVG } from "assets";
import { isArray } from "utils/index";
import isofhToolProvider from "data-access/isofh-tool-provider";

export default {
  state: {
    buildInfo: {},
    serverState: (() => {
      return localStorage.getItem("SERVER_STATE") == "offline"
        ? "offline"
        : "online";
    })(),
    // lastInteractive: new Date().getTime(),
    url: null,
    isVisible: false,
    showSidebar: false,
    lang: (() => {
      try {
        let lang = localStorage.getItem("LANG") || "vi";
        i18n.changeLanguage(lang);
        return lang;
      } catch (error) {
        console.log(error);
      }
      return "vi";
    })(),
    mayTinhId: (() => {
      try {
        return cacheUtils.read("", CACHE_KEY.MAY_TINH_ID, "", false, true);
      } catch (error) {
        console.log(error);
      }
      return "";
    })(),
    deviceInfo: (() => {
      try {
        return cacheUtils.read("", CACHE_KEY.DEVICE_INFO, {}, false, true);
      } catch (error) {
        console.log(error);
      }
      return {};
    })(),
    browser: (() => {
      try {
        const { appVersion, platform, userAgent, vendor, userAgentData } =
          navigator || {};
        return {
          appVersion,
          platform,
          userAgent,
          vendor,
          userAgentData,
          timeZone: {
            ...Intl.DateTimeFormat().resolvedOptions(),
            gmt: ((value) => {
              if (value > 0) return "+" + value;
              return value;
            })(-new Date().getTimezoneOffset() / 60),
          },
        };
      } catch (error) {
        console.log(error);
      }
      return {};
    })(),
  },
  reducers: {
    updateData(state, payload = {}) {
      return { ...state, ...payload };
    },
  },
  effects: (dispatch) => ({
    getBuildInfo: () => {
      appUtils.getBuildInfo().then((s) => {
        dispatch.application.updateData({
          buildInfo: s,
        });
      });
    },
    onChangeLanguage: ({ language = "vi" }, state) => {
      i18n.changeLanguage(language);
      localStorage.setItem("LANG", language);
      dispatch.application.updateData({
        lang: language,
      });
    },
    shouldLogout: (payload, state) => {
      //Nếu đang block UI thì không xử lý nữa
      if (state.application.blockUI)
        return false;
      // const lastInteractive = state.application.lastInteractive;
      const auth = state.auth.auth;
      const thoiGianDangXuat = (auth?.thoiGianDangXuat || 0) * 60000;
      const thoiGianHetHan = auth?.thoiGianHetHan;

      if (auth) {
        // Nếu có thời gian hết hạn thì check xem nếu đã quá hạn thì logout luôn
        if (thoiGianHetHan && new Date(thoiGianHetHan) < new Date()) {
          return true;
        }
        if (!thoiGianDangXuat) {
          //nếu không có thời gian đăng xuất thì không cần logout
          return false;
        }

        const checkTime = (lastInteractive, thoiGianDangXuat) => {
          const time = new Date().getTime();
          if (
            time - (lastInteractive || new Date().getTime()) >
            thoiGianDangXuat
          ) {
            return true;
          }
          return false
        }


        if (!window.lastInteractive || checkTime(window.lastInteractive, thoiGianDangXuat)) {
          //nếu chưa có thông tin lastinteractive hoặc thời gian không tương tác lớn hơn thời gian đăng xuất 
          // thì thử get lại từ localstorage
          window.lastInteractive = cacheUtils.read(
            "",
            CACHE_KEY.LAST_INTERACTIVE,
            null,
            false,
            true
          );
        }
        if (checkTime(window.lastInteractive, thoiGianDangXuat)) {
          // nếu thời gian ko tương tác lớn hơn thời gian đăng xuất thì logout
          return true;
        }
        return false;
      }
      return null;
    },
    onInteractive: async (payload, state) => {
      const auth = state.auth.auth;
      const time = new Date().getTime();
      //Nếu đang block UI thì không xử lý nữa
      if (state.application.blockUI) return;
      const shouldLogout = await dispatch.application.shouldLogout();
      // kiểm tra nếu cần logout thì thực hiện logout
      if (shouldLogout) {
        if (
          !["/login", "/logout", "/qms", "/kiosk"].some((item) =>
            window.location.pathname.startsWith(item)
          )
        ) {
          dispatch.application.updateData({ blockUI: true });
          dispatch.auth.onLogout();
        }
        return;
      } else {
        if (auth) {
          // mục đích 5s ở đây là để tránh việc ghi liên tục vào local
          if (
            !window.lastInteractive ||
            time - window.lastInteractive > 5000
          ) {
            window.lastInteractive = new Date().getTime();
            cacheUtils.save("", CACHE_KEY.LAST_INTERACTIVE, time, false, true);
          }
        }
      }
    },
    onGetThongTinBenhVien: ({ } = {}, { auth: { auth } }) => {
      const { coSoKcbId, dsCoSoKcb } = auth || {};
      const donViKcb = (dsCoSoKcb || []).find((i) => i.id === coSoKcbId)
        ?.donViKcb;
      const tenBenhVien = donViKcb?.ten;
      let maBenhVien = donViKcb?.ma;
      if (maBenhVien)
        benhVienProvider
          .searchTongHop({ ma: maBenhVien, page: "", size: "", active: true })
          .then(async (s) => {
            if (isArray(s?.data, true)) {
              let benhVien = s.data[0];
              const logo = benhVien.logo;
              const imageBase64 = await cacheUtils.read(
                CACHE_KEY.DATA_LOGO_BENH_VIEN,
                benhVien.id,
                "",
                false
              );
              dispatch.application.updateData({
                logo: imageBase64,
                logoThuongHieu: benhVien.logoThuongHieu,
                tenBenhVien: tenBenhVien || benhVien.ten,
              });
              cacheUtils.save(
                "",
                CACHE_KEY.THONG_TIN_CO_SO_BENH_VIEN,
                {
                  logo: `${getBackendUrl()}/api/his/v1/files/${logo}`,
                  ten: tenBenhVien || benhVien.ten,
                },
                false
              );
              if (logo)
                fileUtils
                  .getFromUrl({ url: fileUtils.absoluteFileUrl(logo) })
                  .then((s) => {
                    let base64 = btoa(
                      new Uint8Array(s).reduce(
                        (data, byte) => data + String.fromCharCode(byte),
                        ""
                      )
                    );
                    const newLogo = "data:image/png;base64," + base64;
                    if (imageBase64 != newLogo);
                    {
                      dispatch.application.updateData({
                        logo: newLogo,
                      });
                      cacheUtils.save(
                        CACHE_KEY.DATA_LOGO_BENH_VIEN,
                        benhVien.id,
                        newLogo,
                        false
                      );
                    }
                  });
            }
          });
      else {
        dispatch.application.updateData({
          logo: "",
        });
      }
    },
    updateStatusServer: (payload, state) => {
      if (payload.serverState != state.application.serverState) {
        if (state.application.serverState == "online") {
          message.error(
            t("common.khongTheKetNoiDenServer"),
            "",
            5,
            <SVG.IcDisconnect style={{ marginRight: 10 }} />
          );
        } else {
          message.success(
            t("common.daKhoiPhucKetNoi"),
            "",
            3,
            <SVG.IcConnected style={{ marginRight: 10 }} />
          );
        }
        localStorage.setItem(
          "SERVER_STATE",
          payload.serverState == "offline" ? "offline" : "online"
        );
        dispatch.application.updateData({
          serverState: payload.serverState == "offline" ? "offline" : "online",
        });
      }
    },
    registerISOFHTOOLS: (payload, state) => {
      var myHeaders = new Headers();
      myHeaders.append("Content-Type", "application/json");
      var requestOptions = {
        method: "POST",
        headers: myHeaders,
        body: JSON.stringify({
          be: getBackendUrl(),
          fe: window.location.origin,
        }),
        redirect: "follow",
        //targetAddressSpace: "private",
      };
      fetch(
        `${window.isofhToolUrl || ISOFH_TOOL_HOST
        }${dataPath}${THONG_TIN_MAY_TINH}/thiet-lap`,
        requestOptions
      )
        .then((response) => response.json())
        .then((result) => {
          const newState = {};
          if (result.mayTinhId) {
            newState.mayTinhId = result.mayTinhId;
            cacheUtils.save(
              "",
              CACHE_KEY.MAY_TINH_ID,
              result.mayTinhId,
              false,
              true
            );
          }
          if (result.code == 0 && result.data?.osInfo) {
            if (result.appVersion) {
              newState.isofhToolsVersion = result.appVersion;
              result.data.osInfo.appVersion = result.appVersion;
              result.data.osInfo.dsLicense = result.dsLicense;
              result.data.osInfo.dsCommand = result.dsCommand;
            }
            newState.deviceInfo = result.data.osInfo;
            cacheUtils.save(
              "",
              CACHE_KEY.DEVICE_INFO,
              result.data.osInfo,
              false,
              true
            );
          }
          dispatch.application.updateData(newState);
        })
        .catch(async (error) => {
          //chỉ version 1.0.0.25+ mới hỗ trợ mở nhanh isofhtools
          if (state.application.deviceInfo?.appVersion >= "1.0.0.25") {
            const { save, read, } = cacheUtils.init({ key: CACHE_KEY.OPEN_ISOFHTOOL_TIME, encrypt: false, });
            const time = await read(null);
            if (time == null || new Date().getTime() - time > 600000) //sau 10phut
            {
              save(new Date().getTime());
              window.location = "isofhtools:"
            }
          }
        });
    },
    cleanCache: () => {
      return new Promise((resolve, reject) => {
        utilsProvider
          .cacheFlushAll()
          .then((s) => {
            resolve(s);
          })
          .catch((e) => {
            message.error(e?.message);
            reject(e);
          });
      });
    },
    loadOnOffVoiceToText: async (payload, state) => {
      const voiceToTextOnOff = await cacheUtils.read(
        state.auth.auth?.id,
        CACHE_KEY.VOICE_TO_TEXT_ON_OFF,
        true
      );
      dispatch.application.updateData({ voiceToTextOnOff });
    },
    changeOnOffVoiceToText: (payload, state, saveData = true) => {
      if (saveData)
        cacheUtils.save(
          state.auth.auth?.id,
          CACHE_KEY.VOICE_TO_TEXT_ON_OFF,
          payload
        );
      dispatch.application.updateData({
        voiceToTextOnOff: payload,
      });
    },
    updateISofHTools: ({ link, save }) => {
      isofhToolProvider.setUpdate(link, save);
    }
  }),
};
