import { cloneDeep } from "lodash";
import nbGiayChungSinhProvider from "data-access/nb-giay-chung-sinh-provider";
import { message } from "antd";
import { combineSort } from "utils";
import { t } from "i18next";
import isofhToolProvider from "data-access/isofh-tool-provider";
import stringUtils from "mainam-react-native-string-utils";
import fileUtils from "utils/file-utils";
import danhSachPhieuChoKyProvider from "data-access/kySo/danh-sach-phieu-cho-ky-provider";

const initData = {
  listData: [],

  totalElements: 0,
  page: 0,
  dataSearch: {},
  dataSortColumn: {},

  selectedRowKeys: [],
};

export default {
  state: cloneDeep(initData),
  reducers: {
    updateData(state, payload = {}) {
      return { ...state, ...payload };
    },
    clearData(state, payload = {}) {
      return { ...cloneDeep(initData), ...payload };
    },
  },
  effects: (dispatch) => ({
    onSizeChange: ({ dataSearch, ...rest }) => {
      dispatch.nbGiayChungSinh.updateData({
        page: 0,
        ...rest,
      });
      dispatch.nbGiayChungSinh.onSearch({ ...rest });
    },

    searchNbGiayChungSinhByParams: ({ page = 0, ...payload }, state) => {
      const obj = {
        dataSearch: {
          ...state.nbGiayChungSinh.dataSearch,
          ...payload,
        },
      };

      dispatch.nbGiayChungSinh.updateData({
        page: 0,
        ...obj,
      });
      dispatch.nbGiayChungSinh.onSearch({ ...obj });
    },

    onSortChange: ({ ...payload }, state) => {
      const dataSortColumn = {
        ...state.nbGiayChungSinh.dataSortColumn,
        ...payload,
      };
      dispatch.nbGiayChungSinh.updateData({
        page: 0,
        dataSortColumn,
      });
      dispatch.nbGiayChungSinh.onSearch({
        page: 0,
        dataSortColumn,
      });
    },

    onSearch: ({ page = 0, ...payload }, state) => {
      let newState = { isLoading: true, page };
      dispatch.nbGiayChungSinh.updateData(newState);
      let size = payload.size || state.nbGiayChungSinh.size || 10;
      const sort = combineSort(
        payload.dataSortColumn || state.nbGiayChungSinh.dataSortColumn || {}
      );
      const dataSearch =
        payload.dataSearch || state.nbGiayChungSinh.dataSearch || {};

      nbGiayChungSinhProvider
        .searchAll({ page, size, sort, ...dataSearch })
        .then((s) => {
          dispatch.nbGiayChungSinh.updateData({
            listData: (s?.data || []).map((item, index) => {
              item.index = page * size + index + 1;
              return item;
            }),
            isLoading: false,
            totalElements: s?.totalElements || 0,
            page,
          });
        })
        .catch((e) => {
          message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          dispatch.nbGiayChungSinh.updateData({
            listData: [],
            isLoading: false,
          });
        });
    },
    dayGiayChungSinhById: (id, state) => {
      return new Promise((resolve, reject) => {
        nbGiayChungSinhProvider
          .dayGiayChungSinh(id)
          .then(async (s) => {
            if (s.data) {
              const { theChuKy, fileTruocKy, conThu, id } = s.data;
              const base64 = await isofhToolProvider.kyXml({
                xmlBase64String: fileTruocKy,
                theChuKy,
              });

              if (base64) {
                const file = fileUtils.base64ToFile(
                  base64,
                  stringUtils.guid() + ".xml",
                  "application/xml"
                );
                await nbGiayChungSinhProvider.dayGiayChungSinhToken({
                  file,
                  conThu,
                  id,
                });
              }
              message.success(
                t("giayDayCong.message.dayGiayChungSinhThanhCong")
              );
              resolve(s?.data);
            } else {
              message.success(
                t("giayDayCong.message.dayGiayChungSinhThanhCong")
              );

              resolve(s?.data);
            }
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },
    dayGiayChungSinh: (payload, state) => {
      return new Promise((resolve, reject) => {
        nbGiayChungSinhProvider
          .dayGiayChungSinhHangLoat(payload)
          .then(async (s) => {
            if (s.data?.length) {
              const promises = (s.data || []).map(async (item) => {
                try {
                  const { theChuKy, fileTruocKy, conThu, id } = item;

                  const base64 = await isofhToolProvider.kyXml({
                    xmlBase64String: fileTruocKy,
                    theChuKy,
                  });

                  if (base64) {
                    const file = fileUtils.base64ToFile(
                      base64,
                      stringUtils.guid() + ".xml",
                      "application/xml"
                    );
                    await nbGiayChungSinhProvider.dayGiayChungSinhToken({
                      file,
                      conThu,
                      id,
                    });
                  }
                  return { id, status: "success" };
                } catch (error) {
                  return { id: item.id, status: "failed", error };
                }
              });
              const results = await Promise.allSettled(promises);
              results.forEach((result) => {
                if (result.status === "fulfilled") {
                  console.log("OK:", result.value);
                } else {
                  console.error("FAIL:", result.reason);
                }
              });
            }
            message.success(t("giayDayCong.message.dayGiayChungSinhThanhCong"));

            resolve(s?.data);
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },
    huyGiayChungSinh: (payload, state) => {
      return new Promise((resolve, reject) => {
        nbGiayChungSinhProvider
          .huyGiayChungSinh(payload)
          .then((s) => {
            message.success(t("giayDayCong.message.huyGiayChungSinhThanhCong"));
            resolve(s.data);
          })
          .catch((e) => {
            message.error(e?.message || "Có lỗi xảy ra!");
            reject(e);
          });
      });
    },
    kyHangLoat: (payload, state) => {
      return new Promise((resolve, reject) => {
        nbGiayChungSinhProvider
          .kyHangLoat(payload)
          .then((s) => {
            const dsKyToken = (s?.data || []).filter(
              (x) => x.duLieu && x.code === 0
            );
            const erros = (s?.data || []).filter((x) => x.code !== 0);
            let messageErros = "";
            if (erros?.length) {
              messageErros = erros.map((item) => item.message)?.join(", ");
            }
            if (dsKyToken?.length) {
              async function kyToKen() {
                for (const item of dsKyToken) {
                  const { theChuKy, fileTruocKy, chuKySo, viTri, id } =
                    item.duLieu;
                  const base64 = await isofhToolProvider.kyXml({
                    xmlBase64String: fileTruocKy,
                    theChuKy,
                  });
                  if (base64) {
                    console.log("base64", base64);
                    const file = fileUtils.base64ToFile(
                      base64,
                      stringUtils.guid() + ".xml",
                      "application/xml"
                    );
                    await danhSachPhieuChoKyProvider.token({
                      file,
                      viTri,
                      chuKySo,
                      id,
                    });
                  }
                  message.success(
                    t("giayDayCong.message.kyGiayChungSinhThanhCong")
                  );
                }
              }
              kyToKen();
            } else if (!erros?.length) {
              message.success(
                t("giayDayCong.message.kyGiayChungSinhThanhCong")
              );
            }
            resolve({ data: s?.data, messageError: messageErros });
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },
  }),
};
