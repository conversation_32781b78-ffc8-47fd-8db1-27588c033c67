import fetchProvider from "data-access/categories/dm-ly-do-chi-dinh-dich-vu-provider";
import baseStore from "../../base-store";
import { t } from "i18next";

export default {
  ...baseStore({
    fetchProvider,
    storeName: "lyDoChiDinhDichVu",
    title: t("danhMuc.lyDoChiDinhDichVu"),
    initState: {
      listLyDoChiDinhDichVuTongHop: [],
    },
    customEffect: ({ dispatch }) => ({
      getListTongHop: (payload = {}, state) => {
        fetchProvider
          .searchTongHop(payload)
          .then((s) => {
            dispatch.lyDoChiDinhDichVu.updateData({
              listLyDoChiDinhDichVuTongHop: s?.data,
            });
          })
          .catch((e) => {
            dispatch.lyDoChiDinhDichVu.updateData({
              listLyDoChiDinhDichVuTongHop: [],
            });
          });
      },
    }),
  }),
};
