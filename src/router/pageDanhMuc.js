import { ROLES } from "constants/index";
import { Page } from "pages/constants";
import React from "react";

const SubPageDanhMuc = React.lazy(() => import("pages/home/<USER>/DanhMuc"));
const LoaiCapCuu = React.lazy(() => import("pages/danhMuc2/loaiCapCuu"));
const HangThe = React.lazy(() => import("pages/danhMuc2/hangThe"));
const TaiNanThuongTich = React.lazy(() =>
  import("pages/danhMuc2/taiNanThuongTich")
);
const NguonKhac = React.lazy(() => import("pages/danhMuc2/nguonKhac"));
const NhomHoaChat = React.lazy(() => import("pages/danhMuc2/nhomHoaChat"));
const ViTriChanThuong = React.lazy(() =>
  import("pages/danhMuc2/viTriChanThuong")
);
const ThoiGianCapCuu = React.lazy(() =>
  import("pages/danhMuc2/thoiGianCapCuu")
);
const QuanHam = React.lazy(() => import("pages/danhMuc2/quanHam"));
const Unit = React.lazy(() => import("pages/danhMuc2/donVi"));
const LoaiBenhAn = React.lazy(() => import("pages/danhMuc2/loaiBenhAn"));
const PhuongPhapVoCam = React.lazy(() =>
  import("pages/danhMuc2/phuongPhapVoCam")
);
const NhomVatTu = React.lazy(() => import("pages/danhMuc2/nhomVatTu"));
const PhuongPhapNhuom = React.lazy(() =>
  import("pages/danhMuc2/phuongPhapNhuom")
);
const ViTriSinhThiet = React.lazy(() =>
  import("pages/danhMuc2/viTriSinhThiet")
);
const LoaiDoiTuong = React.lazy(() => import("pages/danhMuc1n/loaiDoiTuong"));
const NhomChiSo = React.lazy(() => import("pages/danhMuc2/nhomChiSo"));
const HocHamHocVi = React.lazy(() => import("pages/danhMuc2/hocHamHocVi"));
const TheBaoHiem = React.lazy(() => import("pages/danhMuc2/theBaoHiem"));
const NguoiDaiDien = React.lazy(() => import("pages/danhMuc2/nguoiDaiDien"));
const BenhVien = React.lazy(() => import("pages/danhMuc2/benhVien"));
const DonViTinh = React.lazy(() => import("pages/danhMucn1/donViTinh"));
const PhanNhomDichVuKho = React.lazy(() =>
  import("pages/danhMuc2/phanNhomDichVuKho")
);
const ChiSoSong = React.lazy(() => import("pages/danhMuc2/chiSoSong"));
const NgayNghiLe = React.lazy(() => import("pages/danhMuc2/ngayNghiLe"));
const NhomThuoc = React.lazy(() => import("pages/danhMucn1/nhomThuoc"));
const canLamSang = React.lazy(() => import("pages/danhMuc1n/canLamSang"));
const MauKetQuaXN = React.lazy(() => import("pages/danhMuc1n/mauKetQuaXN"));
const DichVuXetNghiem = React.lazy(() =>
  import("pages/danhMuc1n/dichVuXetNghiem")
);
const BoChiDinh = React.lazy(() => import("pages/danhMuc1n/boChiDinh"));
const DanhMucThuoc = React.lazy(() => import("pages/danhMuc1n/danhMucThuoc"));
const DanhMucVatTu = React.lazy(() => import("pages/danhMuc1n/danhMucVatTu"));
const DanhMucKhamBenh = React.lazy(() => import("pages/danhMuc1n/khamBenh"));
const DanhMucTongHop = React.lazy(() => import("pages/danhMuc2/dichVuTongHop"));
const PhauThuat = React.lazy(() => import("pages/danhMuc1n/phauThuat"));
const DMBaoCao = React.lazy(() => import("pages/danhMuc1n/baoCao"));
const MayIn = React.lazy(() => import("pages/danhMuc2/mayIn"));
const NguonGioiThieu = React.lazy(() =>
  import("pages/danhMucn1/nguonGioiThieu")
);
const QuyenKy = React.lazy(() => import("pages/danhMuc2/quyenKy"));
const LoaiPhieu = React.lazy(() => import("pages/danhMuc2/loaiPhieu"));
const MauQms = React.lazy(() => import("pages/danhMuc2/mauQms"));
const PhuongThucTT = React.lazy(() => import("pages/danhMuc2/phuongThucTT"));
const ChePhamMau = React.lazy(() => import("pages/danhMuc1n/chePhamMau"));
const DiaChiHanhChinh = React.lazy(() =>
  import("pages/danhMucn1/diaChiHanhChinh")
);
const NhomBenh = React.lazy(() => import("pages/danhMucn1/nhomBenh"));
const maPtttQuocTe = React.lazy(() => import("pages/danhMucn1/maPtttQuocTe"));
const HoaChat = React.lazy(() => import("pages/danhMuc1n/hoaChat"));
const NhomDichVu = React.lazy(() => import("pages/danhMucn1/nhomDichVu"));
const CauHoiKhamSL = React.lazy(() => import("pages/danhMucn1/cauHoiKhamSL"));
const GoiPtTt = React.lazy(() => import("pages/danhMucn1/goiPtTt"));
const Phong = React.lazy(() => import("pages/danhMuc1n/phong"));
const PhanLoaiThuoc = React.lazy(() => import("pages/danhMuc2/phanLoaiThuoc"));
const Faculty = React.lazy(() => import("pages/danhMuc2/khoa"));
const DoiTac = React.lazy(() => import("pages/danhMuc1n/doiTac"));
const MaMay = React.lazy(() => import("pages/danhMuc2/maMay"));
const HoatChat = React.lazy(() => import("pages/danhMuc2/hoatChat"));
const DuongDung = React.lazy(() => import("pages/danhMuc2/duongDung"));
const LieuDung = React.lazy(() => import("pages/danhMuc2/lieuDung"));
const NhomChiPhi = React.lazy(() => import("pages/danhMuc2/nhomChiPhi"));
const ToaNha = React.lazy(() => import("pages/danhMuc2/toaNha"));
const LoiDan = React.lazy(() => import("pages/danhMuc2/loiDan"));
const DanToc = React.lazy(() => import("pages/danhMuc2/danToc"));
const VanBang = React.lazy(() => import("pages/danhMuc2/vanBang"));
const LoaGoiSo = React.lazy(() => import("pages/danhMuc2/loaGoiSo"));
const NgheNghiep = React.lazy(() => import("pages/danhMuc2/ngheNghiep"));
const ChucVu = React.lazy(() => import("pages/danhMuc2/chucVu"));
const MoiQuanHe = React.lazy(() => import("pages/danhMuc2/moiQuanHe"));
const LyDoDoiTra = React.lazy(() => import("pages/danhMuc2/lyDoDoiTra"));
const LoaiBuaAn = React.lazy(() => import("pages/danhMuc2/loaiBuaAn"));
const DichVuAn = React.lazy(() => import("pages/danhMuc2/dichVuAn"));
const MauKQXNDotBien = React.lazy(() =>
  import("pages/danhMuc2/mauKqXnDotBien")
);
const LyDoTamUng = React.lazy(() => import("pages/danhMuc2/lyDoTamUng"));
const LyDoDenKham = React.lazy(() => import("pages/danhMuc2/lyDoDenKham"));
const LyDoChiDinhDichVu = React.lazy(() => import("pages/danhMuc2/lyDoChiDinhDichVu"));

const MauDienBien = React.lazy(() => import("pages/danhMuc2/mauDienBien"));
const BenhPham = React.lazy(() => import("pages/danhMuc2/benhPham"));
const ChuyenKhoa = React.lazy(() => import("pages/danhMuc1n/chuyenKhoa"));
const Quay = React.lazy(() => import("pages/danhMuc2/quay"));
const NoiLayBenhPham = React.lazy(() =>
  import("pages/danhMucn1/noiLayBenhPham")
);
const KhuVuc = React.lazy(() => import("pages/danhMuc2/khuVuc"));
const NhanTheoDoi = React.lazy(() => import("pages/danhMuc2/nhanTheoDoi"));
const KhangNguyen = React.lazy(() => import("pages/danhMuc2/khangNguyen"));
const LuocDoPt = React.lazy(() => import("pages/danhMuc2/luocDoPt"));
const PhuongPhapCheBien = React.lazy(() =>
  import("pages/danhMuc2/phuongPhapCheBien")
);
const ManHinhPhieuIn = React.lazy(() =>
  import("pages/danhMucn1/manHinhPhieuIn")
);
const Protocol = React.lazy(() => import("pages/danhMucn1/protocol"));
const PhanLoaiNB = React.lazy(() => import("pages/danhMuc2/phanLoaiNB"));
const HinhThucNhapXuat = React.lazy(() =>
  import("pages/danhMuc2/hinhThucNhapXuat")
);
const ThuocKeNgoai = React.lazy(() => import("pages/danhMuc1n/thuocKeNgoai"));
const ChuongTrinhGiamGia = React.lazy(() =>
  import("pages/danhMucn1/chuongTrinhGiamGia")
);
const XuatXu = React.lazy(() => import("pages/danhMuc2/xuatXu"));
const ThangSoBanLe = React.lazy(() => import("pages/danhMuc2/thangSoBanLe"));
const MauKetQuaCLS = React.lazy(() => import("pages/danhMuc2/mauKetQuaCLS"));
const LieuDungBacSy = React.lazy(() => import("pages/danhMuc1n/lieuDungBacSy"));
const NguonNhapKho = React.lazy(() => import("pages/danhMuc2/nguonNhapKho"));
const Kiosk = React.lazy(() => import("pages/danhMuc2/kiosk"));
const HuongDanSuDung = React.lazy(() =>
  import("pages/danhMuc2/huongDanSuDung")
);
const HoiDong = React.lazy(() => import("pages/danhMuc1n/hoiDong"));
const NgoaiDieuTri = React.lazy(() => import("pages/danhMuc1n/ngoaiDieuTri"));
const DonViChiNhanh = React.lazy(() => import("pages/danhMucn1/donViChiNhanh"));
const DiTatBamSinh = React.lazy(() => import("pages/danhMuc2/diTatBamSinh"));
const LoaiGiuong = React.lazy(() => import("pages/danhMuc2/loaiGiuong"));
const SoHieuGiuong = React.lazy(() => import("pages/danhMuc2/soHieuGiuong"));
const DichVuGiuong = React.lazy(() => import("pages/danhMuc1n/dichVuGiuong"));
const BacSiNgoaiVien = React.lazy(() =>
  import("pages/danhMuc2/bacSiNgoaiVien")
);
const MaPhieuLinh = React.lazy(() => import("pages/danhMuc2/maPhieuLinh"));
const CheDoChamSoc = React.lazy(() => import("pages/danhMuc2/cheDoChamSoc"));
const MauKetQuaPTTT = React.lazy(() => import("pages/danhMuc2/mauKetQuaPTTT"));
const LoaiHinhThanhToan = React.lazy(() =>
  import("pages/danhMuc2/loaiHinhThanhToan")
);
const PhanLoaiBmi = React.lazy(() => import("pages/danhMuc2/phanLoaiBmi"));
const HauQuaTuongTac = React.lazy(() =>
  import("pages/danhMuc2/hauQuaTuongTac")
);
const MauBenhAnVaoVien = React.lazy(() =>
  import("pages/danhMuc2/mauBenhAnVaoVien")
);
const DacTinhDuocLy = React.lazy(() => import("pages/danhMuc2/dacTinhDuocLy"));
const MucDoTuongTac = React.lazy(() => import("pages/danhMuc2/mucDoTuongTac"));
const MucDoBangChung = React.lazy(() =>
  import("pages/danhMuc2/mucDoBangChung")
);
const TuongTacThuoc = React.lazy(() => import("pages/danhMuc1n/tuongTacThuoc"));
const ChiPhiHapSayVTYTTaiSuDung = React.lazy(() =>
  import("pages/danhMuc1n/chiPhiHapSayVTYTTaiSuDung")
);
const VacXin = React.lazy(() => import("pages/danhMuc2/vacXin"));
const CalamViec = React.lazy(() => import("pages/danhMuc2/caLamViec"));
const QuyTrinhXetNghiem = React.lazy(() =>
  import("pages/danhMuc2/quyTrinhXetNghiem")
);
const PhanLoaiPHCN = React.lazy(() => import("pages/danhMuc2/phanLoaiPHCN"));
const ThiLuc = React.lazy(() => import("pages/danhMuc2/thiLuc"));
const DonViSph = React.lazy(() => import("pages/danhMuc2/donViSph"));
const TatKhucXa = React.lazy(() => import("pages/danhMuc2/tatKhucXa"));
const DonViAxis = React.lazy(() => import("pages/danhMuc2/donViAxis"));
const DonViCyl = React.lazy(() => import("pages/danhMuc2/donViCyl"));
const NhanAp = React.lazy(() => import("pages/danhMuc2/nhanAp"));
const XangDau = React.lazy(() => import("pages/danhMuc2/xangDau"));
const PhanLoaiPhuongPhapVoCam = React.lazy(() =>
  import("pages/danhMuc2/phanLoaiPhuongPhapVoCam")
);
const LoaiNhiemKhuan = React.lazy(() =>
  import("pages/danhMuc2/loaiNhiemKhuan")
);
const BenhYHocCoTruyen = React.lazy(() =>
  import("pages/danhMuc2/benhYHocCoTruyen")
);
const KhoaDuLieuBaoCaoKho = React.lazy(() =>
  import("pages/danhMuc3/khoaDuLieuBaoCaoKho")
);
const MauKetQuaKhamBenh = React.lazy(() =>
  import("pages/danhMuc1n/mauKetQuaKhamBenh")
);
const DanhMucViKhuan = React.lazy(() => import("pages/danhMuc2/viKhuan"));
const DoiTuongKcb = React.lazy(() => import("pages/danhMuc2/doiTuongKcb"));
const HangBangLaiXe = React.lazy(() => import("pages/danhMuc2/hangBangLaiXe"));

const KhaiBaoPhuCapPttt = React.lazy(() =>
  import("pages/danhMuc1n/khaiBaoPhuCapPttt")
);
const LuongGiaPhcn = React.lazy(() => import("pages/danhMuc1n/luongGiaPhcn"));

const TraCuuTt = React.lazy(() => import("pages/danhMuc2/traCuuTt"));
const LaoKhangThuoc = React.lazy(() => import("pages/danhMuc2/laoKhangThuoc"));
const PhuongPhapChanDoan = React.lazy(() =>
  import("pages/danhMuc1n/phuongPhapChanDoan")
);
const DinhMucThuocVTYT = React.lazy(() =>
  import("pages/danhMuc1n/dinhMucThuocVTYT")
);
const PhanLoaiTienSu = React.lazy(() =>
  import("pages/danhMuc2/phanLoaiTienSu")
);
const VanDeLienQuanDenThuoc = React.lazy(() =>
  import("pages/danhMucn1/vanDeLienQuanDenThuoc")
);
const ChePhamDinhDuong = React.lazy(() =>
  import("pages/danhMuc1n/chePhamDinhDuong")
);

const XNSaoBenhAn = React.lazy(() => import("pages/danhMuc2/xnSaoBenhAn"));
const NgoiThai = React.lazy(() => import("pages/danhMuc2/ngoiThai"));
const CachThucDe = React.lazy(() => import("pages/danhMuc2/cachThucDe"));
const BienPhapCamMau = React.lazy(() =>
  import("pages/danhMuc2/bienPhapCamMau")
);
const ChinhSachHoaHong = React.lazy(() =>
  import("pages/danhMucn1/chinhSachHoaHong")
);
const NhomDichVuHoaHong = React.lazy(() =>
  import("pages/danhMuc2/nhomDichVuHoaHong")
);
const DoiTacHoaHong = React.lazy(() => import("pages/danhMuc2/doiTacHoaHong"));
const DichVuHoaHong = React.lazy(() => import("pages/danhMuc1n/dichVuHoaHong"));
const NhomLoaiDoiTuong = React.lazy(() =>
  import("pages/danhMuc2/nhomLoaiDoiTuong")
);
const NhomLoaiBenhAn = React.lazy(() =>
  import("pages/danhMuc2/nhomLoaiBenhAn")
);
const ThangBaoCao = React.lazy(() => import("pages/danhMuc2/thangBaoCao"));
const ThamGiaHoiChan = React.lazy(() =>
  import("pages/danhMuc2/thamGiaHoiChan")
);
const GoTat = React.lazy(() => import("pages/danhMuc2/goTat"));
const NhomDaiPhieuNhapXuat = React.lazy(() =>
  import("pages/danhMuc2/nhomDaiPhieuNhapXuat")
);
const CoPhim = React.lazy(() => import("pages/danhMuc2/coPhim"));
const PhanLoaiVatTu = React.lazy(() => import("pages/danhMuc2/phanLoaiVatTu"));
const ThuocVatTuBanGiao = React.lazy(() =>
  import("pages/danhMuc2/thuocVatTuBanGiao")
);
const PhanTangNguyCo = React.lazy(() =>
  import("pages/danhMuc2/phanTangNguyCo")
);
const PhuCapDvKyThuat = React.lazy(() =>
  import("pages/danhMuc2/phuCapDvKyThuat")
);
const TacNhanDiUng = React.lazy(() => import("pages/danhMuc2/tacNhanDiUng"));
const DieuTriKetHop = React.lazy(() => import("pages/danhMuc2/dieuTriKetHop"));
const DieuKienChiDinh = React.lazy(() =>
  import("pages/danhMuc2/dieuKienChiDinh")
);
const NhomHuongPhuCapPTTT = React.lazy(() =>
  import("pages/danhMuc1n/nhomHuongPhuCapPTTT")
);
const NhomPhuCapPtTt = React.lazy(() =>
  import("pages/danhMuc2/nhomPhuCapPtTt")
);
const LoaiHienThiPhieuIn = React.lazy(() =>
  import("pages/danhMuc2/loaiHienThiPhieuIn")
);
const KhaiBaoHangHoaDungKemDvkt = React.lazy(() =>
  import("pages/danhMuc2/khaiBaoHangHoaDungKemDvkt")
);
const ChuyenKhoaKhiRaVien = React.lazy(() => import("pages/danhMuc2/kyChuyenKhoaKhiRaVien"));

export default {
  subPageDanhMuc: {
    component: Page(SubPageDanhMuc, []),
    accessRoles: [],
    path: "/danh-muc",
    exact: true,
  },
  phuongThucTT: {
    component: Page(PhuongThucTT, [ROLES["DANH_MUC"].PTTT]),
    accessRoles: [],
    path: "/danh-muc/phuong-thuc-thanh-toan",
    exact: true,
  },
  chePhamMau: {
    component: Page(ChePhamMau, [ROLES["DANH_MUC"].CHE_PHAM_MAU]),
    accessRoles: [],
    path: "/danh-muc/che-pham-mau",
    exact: true,
  },
  diaChiHanhChinh: {
    component: Page(DiaChiHanhChinh, [ROLES["DANH_MUC"].DIA_CHI_HANH_CHINH]),
    accessRoles: [],
    path: "/danh-muc/dia-chi-hanh-chinh",
    exact: true,
  },
  nhomHoaChat: {
    component: Page(NhomHoaChat, [ROLES["DANH_MUC"].NHOM_HOA_CHAT]),
    accessRoles: [],
    path: "/danh-muc/nhom-hoa-chat",
    exact: true,
  },
  nhomBenh: {
    component: Page(NhomBenh, [ROLES["DANH_MUC"].BENH_TAT]),
    accessRoles: [],
    path: "/danh-muc/nhom-benh-tat",
    exact: true,
  },
  maPtttQuocTe: {
    component: Page(
      maPtttQuocTe
      // [ROLES["DANH_MUC"].BENH_TAT]
    ),
    accessRoles: [],
    path: "/danh-muc/ma-pttt-quoc-te",
    exact: true,
  },
  hoaChat: {
    component: Page(HoaChat, [ROLES["DANH_MUC"].HOA_CHAT]),
    accessRoles: [],
    path: "/danh-muc/hoa-chat",
    exact: true,
  },
  nhomDichVu: {
    component: Page(NhomDichVu, [ROLES["DANH_MUC"].NHOM_DICH_VU]),
    accessRoles: [],
    path: "/danh-muc/nhom-dich-vu",
    exact: true,
  },
  cauHoiKhamSL: {
    component: Page(CauHoiKhamSL, [ROLES["DANH_MUC"].CAU_HOI_KHAM_SANG_LOC]),
    accessRoles: [ROLES["DANH_MUC"].CAU_HOI_KHAM_SANG_LOC],
    path: "/danh-muc/cau-hoi-kham-sang-loc",
    exact: true,
  },
  goiPtTt: {
    component: Page(GoiPtTt, [ROLES["DANH_MUC"].GOI_MO_10_NGAY]),
    accessRoles: [ROLES["DANH_MUC"].GOI_MO_10_NGAY],
    path: "/danh-muc/goi-pt-tt",
    exact: true,
  },
  phong: {
    component: Page(Phong, [ROLES["DANH_MUC"].PHONG]),
    accessRoles: [],
    path: "/danh-muc/phong",
    exact: true,
  },
  phanLoaiThuoc: {
    component: Page(PhanLoaiThuoc, [ROLES["DANH_MUC"].PHAN_LOAI_THUOC]),
    accessRoles: [],
    path: "/danh-muc/phan-loai-thuoc",
    exact: true,
  },
  khoa: {
    component: Page(Faculty, [ROLES["DANH_MUC"].KHOA]),
    accessRoles: [],
    path: "/danh-muc/khoa",
    exact: true,
  },

  doiTac: {
    component: Page(DoiTac, [ROLES["DANH_MUC"].NHA_SAN_XUAT]),
    accessRoles: [],
    path: "/danh-muc/doi-tac",
    exact: true,
  },
  hoatChat: {
    component: Page(HoatChat, [ROLES["DANH_MUC"].HOAT_CHAT]),
    accessRoles: [],
    path: "/danh-muc/hoat-chat",
    exact: true,
  },
  tacNhanDiUng: {
    component: Page(TacNhanDiUng, []),
    accessRoles: [],
    path: "/danh-muc/tac-nhan-di-ung",
    exact: true,
  },
  maMay: {
    component: Page(MaMay, [ROLES["DANH_MUC"].MA_MAY]),
    accessRoles: [],
    path: "/danh-muc/ma-may",
    exact: true,
  },
  duongDung: {
    component: Page(DuongDung, [ROLES["DANH_MUC"].DUONG_DUNG]),
    accessRoles: [],
    path: "/danh-muc/duong-dung",
    exact: true,
  },
  lieuDung: {
    component: Page(LieuDung, [ROLES["DANH_MUC"].LIEU_DUNG]),
    accessRoles: [],
    path: "/danh-muc/lieu-dung",
    exact: true,
  },
  nhomChiPhi: {
    component: Page(NhomChiPhi, [ROLES["DANH_MUC"].NHOM_CHI_PHI]),
    accessRoles: [],
    path: "/danh-muc/nhom-chi-phi",
    exact: true,
  },
  loiDan: {
    component: Page(LoiDan, [ROLES["DANH_MUC"].LOI_DAN]),
    accessRoles: [],
    path: "/danh-muc/loi-dan",
    exact: true,
  },
  toaNha: {
    component: Page(ToaNha, [ROLES["DANH_MUC"].NHA]),
    accessRoles: [],
    path: "/danh-muc/toa-nha",
    exact: true,
  },
  vanBang: {
    component: Page(VanBang, [ROLES["DANH_MUC"].VAN_BANG]),
    accessRoles: [],
    path: "/danh-muc/van-bang-chuyen-mon",
    exact: true,
  },
  loaGoiSo: {
    component: Page(LoaGoiSo, [ROLES["DANH_MUC"].LOA_GOI_SO]),
    accessRoles: [],
    path: "/danh-muc/loa-goi-so",
    exact: true,
  },
  quayTiepDon: {
    component: Page(Quay, [ROLES["DANH_MUC"].QUAY]),
    accessRoles: [],
    path: "/danh-muc/quay",
    exact: true,
  },
  danToc: {
    component: Page(DanToc, [ROLES["DANH_MUC"].DAN_TOC]),
    accessRoles: [],
    path: "/danh-muc/dan-toc",
    exact: true,
  },
  ngheNghiep: {
    component: Page(NgheNghiep, [ROLES["DANH_MUC"].NGHE_NGHIEP]),
    accessRoles: [],
    path: "/danh-muc/nghe-nghiep",
    exact: true,
  },
  chucVu: {
    component: Page(ChucVu, [ROLES["DANH_MUC"].CHUC_VU]),
    accessRoles: [],
    path: "/danh-muc/chuc-vu",
    exact: true,
  },
  loaiCapCuu: {
    component: Page(LoaiCapCuu, [ROLES["DANH_MUC"].LOAI_CC]),
    accessRoles: [],
    path: "/danh-muc/loai-cap-cuu",
    exact: true,
  },
  nhomChiSo: {
    component: Page(NhomChiSo, [ROLES["DANH_MUC"].NHOM_CHI_SO]),
    accessRoles: [],
    path: "/danh-muc/nhom-chi-so",
    exact: true,
  },
  taiNanThuongTich: {
    component: Page(TaiNanThuongTich, [ROLES["DANH_MUC"].TAI_NAN_THUONG_TICH]),
    accessRoles: [],
    path: "/danh-muc/tai-nan-thuong-tich",
    exact: true,
  },
  NguonKhac: {
    component: Page(NguonKhac, [ROLES["DANH_MUC"].NGUON_KHAC]),
    accessRoles: [],
    path: "/danh-muc/nguon-khac",
    exact: true,
  },
  viTriChanThuong: {
    component: Page(ViTriChanThuong, [ROLES["DANH_MUC"].VI_TRI_CHAN_THUONG]),
    accessRoles: [],
    path: "/danh-muc/vi-tri-chan-thuong",
    exact: true,
  },
  nhomVatTu: {
    component: Page(NhomVatTu, [ROLES["DANH_MUC"].NHOM_VAT_TU]),
    accessRoles: [],
    path: "/danh-muc/nhom-vat-tu",
    exact: true,
  },
  thoiGianCapCuu: {
    component: Page(ThoiGianCapCuu, [ROLES["DANH_MUC"].TG_CC]),
    accessRoles: [],
    path: "/danh-muc/thoi-gian-cap-cuu",
    exact: true,
  },
  quanHam: {
    component: Page(QuanHam, [ROLES["DANH_MUC"].QUAN_HAM]),
    accessRoles: [],
    path: "/danh-muc/quan-ham",
    exact: true,
  },
  donVi: {
    component: Page(Unit, [ROLES["DANH_MUC"].CO_QUAN]),
    accessRoles: [],
    path: "/danh-muc/co-quan-don-vi",
    xact: true,
  },
  moiQuanHe: {
    component: Page(MoiQuanHe, [ROLES["DANH_MUC"].MOI_QUAN_HE]),
    accessRoles: [],
    path: "/danh-muc/moi-quan-he",
    exact: true,
  },
  lyDoDoiTra: {
    component: Page(LyDoDoiTra, [ROLES["DANH_MUC"].DOI_TRA_DICH_VU]),
    accessRoles: [],
    path: "/danh-muc/ly-do-tra-dv",
    exact: true,
  },
  loaiBuaAn: {
    component: Page(LoaiBuaAn, [ROLES["DANH_MUC"].LOAI_BUA_AN]),
    accessRoles: [],
    path: "/danh-muc/loai-bua-an",
    exact: true,
  },
  lyDoTamUng: {
    component: Page(LyDoTamUng, [ROLES["DANH_MUC"].LY_DO_TAM_UNG]),
    accessRoles: [],
    path: "/danh-muc/ly-do-tam-ung",
    exact: true,
  },
  lyDoDenKham: {
    component: Page(LyDoDenKham, [ROLES["DANH_MUC"].LY_DO_DEN_KHAM]),
    accessRoles: [],
    path: "/danh-muc/ly-do-den-kham",
    exact: true,
  },
  lyDoChiDinhDichVu: {
    component: Page(LyDoChiDinhDichVu, [ROLES["DANH_MUC"].LY_DO_CHI_DINH_DICH_VU]),
    accessRoles: [],
    path: "/danh-muc/ly-do-chi-dinh-dich-vu",
    exact: true,
  },
  mauDienBien: {
    component: Page(MauDienBien, [ROLES["DANH_MUC"].MAU_DIEN_BIEN]),
    accessRoles: [],
    path: "/danh-muc/mau-dien-bien",
    exact: true,
  },
  specimens: {
    component: Page(BenhPham, [ROLES["DANH_MUC"].BENH_PHAM]),
    accessRoles: [],
    path: "/danh-muc/benh-pham",
    exact: true,
  },
  chuyenKhoa: {
    component: Page(ChuyenKhoa, [ROLES["DANH_MUC"].CHUYEN_KHOA]),
    accessRoles: [],
    path: "/danh-muc/chuyen-khoa",
    exact: true,
  },
  loaiBenhAn: {
    component: Page(LoaiBenhAn, [ROLES["DANH_MUC"].LOAI_BA]),
    accessRoles: [],
    path: "/danh-muc/loai-benh-an",
    exact: true,
  },
  phuongPhapVoCam: {
    component: Page(PhuongPhapVoCam, [ROLES["DANH_MUC"].PHUONG_PHAP_VO_CAM]),
    accessRoles: [],
    path: "/danh-muc/phuong-phap-vo-cam",
    exact: true,
  },
  phuongPhapNhuom: {
    component: Page(PhuongPhapNhuom, [ROLES["DANH_MUC"].PHUONG_PHAP_NHUOM]),
    accessRoles: [],
    path: "/danh-muc/phuong-phap-nhuom",
    exact: true,
  },
  viTriSinhThiet: {
    component: Page(ViTriSinhThiet, [ROLES["DANH_MUC"].VI_TRI_SINH_THIET]),
    accessRoles: [],
    path: "/danh-muc/vi-tri-sinh-thiet",
    exact: true,
  },
  loaiDoiTuong: {
    component: Page(LoaiDoiTuong, [ROLES["DANH_MUC"].LOAI_DOI_TUONG]),
    accessRoles: [],
    path: "/danh-muc/loai-doi-tuong",
    exact: true,
  },
  hocHamHocVi: {
    component: Page(HocHamHocVi, [ROLES["DANH_MUC"].HOC_HAM]),
    accessRoles: [],
    path: "/danh-muc/hoc-ham-hoc-vi",
    exact: true,
  },
  theBaoHiem: {
    component: Page(TheBaoHiem, [ROLES["DANH_MUC"].THE_BAO_HIEM]),
    accessRoles: [],
    path: "/danh-muc/the-bao-hiem",
    exact: true,
  },
  nguoiDaiDien: {
    component: Page(NguoiDaiDien, [ROLES["DANH_MUC"].NGUOI_DAI_DIEN]),
    accessRoles: [],
    path: "/danh-muc/nguoi-dai-dien",
    exact: true,
  },
  benhVien: {
    component: Page(BenhVien, [ROLES["DANH_MUC"].BENH_VIEN]),
    accessRoles: [],
    path: "/danh-muc/benh-vien",
    exact: true,
  },
  donViTinh: {
    component: Page(DonViTinh, [ROLES["DANH_MUC"].DON_VI_TINH]),
    accessRoles: [],
    path: "/danh-muc/don-vi-tinh",
    exact: true,
  },
  phanNhomDichVuKho: {
    component: Page(PhanNhomDichVuKho, [ROLES["DANH_MUC"].PHAN_NHOM_THUOC]),
    accessRoles: [],
    path: "/danh-muc/phan-nhom-thuoc",
    exact: true,
  },
  nhomThuoc: {
    component: Page(NhomThuoc, [ROLES["DANH_MUC"].NHOM_THUOC]),
    accessRoles: [],
    path: "/danh-muc/nhom-thuoc",
    exact: true,
  },
  dichVuXetNghiem: {
    component: Page(DichVuXetNghiem, [ROLES["DANH_MUC"].DICH_VU_XN]),
    accessRoles: [],
    path: "/danh-muc/dich-vu-xet-nghiem",
    exact: true,
  },
  canLamSang: {
    component: Page(canLamSang, [ROLES["DANH_MUC"].CDHA_TDCN]),
    accessRoles: [],
    path: "/danh-muc/dich-vu-cdha-tdcn",
    exact: true,
  },
  danhMucThuoc: {
    component: Page(DanhMucThuoc, [ROLES["DANH_MUC"].THUOC]),
    accessRoles: [],
    path: "/danh-muc/thuoc",
    exact: true,
  },
  danhMucVatTu: {
    component: Page(DanhMucVatTu, [ROLES["DANH_MUC"].VAT_TU]),
    accessRoles: [],
    path: "/danh-muc/vat-tu",
    exact: true,
  },
  danhMucKhamBenh: {
    component: Page(DanhMucKhamBenh, [ROLES["DANH_MUC"].DICH_VU_KHAM_BENH]),
    accessRoles: [],
    path: "/danh-muc/dich-vu-kham-benh",
    exact: true,
  },
  danhMucTongHop: {
    component: Page(DanhMucTongHop, [ROLES["DANH_MUC"].DICH_VU_TONG_HOP]),
    accessRoles: [],
    path: "/danh-muc/dich-vu-tong-hop",
    exact: true,
  },
  phauThuat: {
    component: Page(PhauThuat, [ROLES["DANH_MUC"].DV_PHAU_THUAT_THU_THUAT]),
    accessRoles: [],
    path: "/danh-muc/dich-vu-phau-thuat",
    exact: true,
  },
  baoCao: {
    component: Page(DMBaoCao, [ROLES["DANH_MUC"].BAO_CAO]),
    accessRoles: [],
    path: "/danh-muc/bao-cao",
    exact: true,
  },
  mayIn: {
    component: Page(MayIn, [ROLES["DANH_MUC"].MAY_IN]),
    accessRoles: [],
    path: "/danh-muc/may-in",
    exact: true,
  },
  MauKetQuaXN: {
    component: Page(MauKetQuaXN, [ROLES["DANH_MUC"].MAU_KET_QUA_XN]),
    accessRoles: [],
    path: "/danh-muc/mau-ket-qua-xet-nghiem",
  },
  nNoiLayBenhPham: {
    component: Page(NoiLayBenhPham, [ROLES["DANH_MUC"].NOI_LAY_BENH_PHAM]),
    accessRoles: [],
    path: "/danh-muc/noi-lay-benh-pham",
    exact: true,
  },

  hinhThucNhapXuat: {
    component: Page(HinhThucNhapXuat, [
      ROLES["DANH_MUC"].HINH_THUC_NHAP_XUAT_LOAI_XUAT,
    ]),
    accessRoles: [],
    path: "/danh-muc/hinh-thuc-nhap-xuat",
    exact: true,
  },

  nguonNhapKho: {
    component: Page(NguonNhapKho, [ROLES["DANH_MUC"].NGUON_NHAP_KHO]),
    accessRoles: [],
    path: "/danh-muc/nguon-nhap-kho",
    exact: true,
  },
  boChiDinh: {
    component: Page(BoChiDinh, [ROLES["DANH_MUC"].BO_CHI_DINH]),
    accessRoles: [ROLES["DANH_MUC"].BO_CHI_DINH],
    path: "/danh-muc/bo-chi-dinh",
    exact: true,
  },
  voucher: {
    component: Page(ChuongTrinhGiamGia, [
      ROLES["DANH_MUC"].CHUONG_TRINH_GIAM_GIA,
    ]),
    accessRoles: [],
    path: "/danh-muc/chuong-trinh-giam-gia",
    exact: true,
  },
  nguonGioiThieu: {
    component: Page(NguonGioiThieu, [ROLES["DANH_MUC"].NGUON_NGUOI_BENH]),
    accessRoles: [],
    path: "/danh-muc/nguon-nguoi-benh",
    exact: true,
  },
  hangThe: {
    component: Page(HangThe, [ROLES["DANH_MUC"].HANG_THE]),
    accessRoles: [],
    path: "/danh-muc/hang-the",
    exact: true,
  },
  xuatXu: {
    component: Page(XuatXu, [ROLES["DANH_MUC"].XUAT_XU]),
    accessRoles: [],
    path: "/danh-muc/xuat-xu",
    exact: true,
  },
  thangSoBanLe: {
    component: Page(ThangSoBanLe, [ROLES["DANH_MUC"].DINH_MUC_THANG_SO]),
    accessRoles: [],
    path: "/danh-muc/thang-so-ban-le",
    exact: true,
  },
  mauKetQuaCLS: {
    component: Page(MauKetQuaCLS, [ROLES["DANH_MUC"].MAU_KQ_CDHA_TDCN]),
    accessRoles: [],
    path: "/danh-muc/mau-ket-qua-cls",
    exact: true,
  },
  quyenKy: {
    component: Page(QuyenKy, [ROLES["DANH_MUC"].QUYEN_KY]),
    accessRoles: [],
    path: "/danh-muc/quyen-ky",
    exact: true,
  },
  kyChuyenKhoaRaVien: {
    component: Page(ChuyenKhoaKhiRaVien, [ROLES["DANH_MUC"].QUYEN_KY_CHUYEN_KHOA]),
    accessRoles: [],
    path: "/danh-muc/thiet-lap-ky-hoan-thanh-chuyen-khoa-ra-vien",
    exact: true,
  },
  loaiPhieu: {
    component: Page(LoaiPhieu, [ROLES["DANH_MUC"].LOAI_PHIEU]),
    accessRoles: [],
    path: "/danh-muc/loai-phieu",
    exact: true,
  },

  mauQms: {
    component: Page(MauQms, [ROLES["DANH_MUC"].MAU_QMS]),
    accessRoles: [],
    path: "/danh-muc/mau-qms",
    exact: true,
  },
  thuocKeNgoai: {
    component: Page(ThuocKeNgoai, [ROLES["DANH_MUC"].THUOC_KE_NGOAI]),
    accessRoles: [],
    path: "/danh-muc/thuoc-ke-ngoai",
    exact: true,
  },
  lieuDungBacSy: {
    component: Page(LieuDungBacSy, [ROLES["DANH_MUC"].LIEU_DUNG_BS]),
    accessRoles: [],
    path: "/danh-muc/lieu-dung-bac-si",
    exact: true,
  },

  kiosk: {
    component: Page(Kiosk, [ROLES["THIET_LAP"].KIOSK]),
    accessRoles: [],
    path: "/danh-muc/kiosk",
    exact: true,
  },
  hdsd: {
    component: Page(HuongDanSuDung, [ROLES["DANH_MUC"].HDSD]),
    accessRoles: [],
    path: "/danh-muc/huong-dan-su-dung",
    exact: true,
  },
  hoiDong: {
    component: Page(HoiDong, [ROLES["DANH_MUC"].HOI_DONG]),
    accessRoles: [],
    path: "/danh-muc/hoi-dong",
    exact: true,
  },
  ngoaiDieuTri: {
    component: Page(NgoaiDieuTri, [ROLES["DANH_MUC"].DV_NGOAI_DIEU_TRI]),
    accessRoles: [],
    path: "/danh-muc/ngoai-dieu-tri",
    exact: true,
  },
  DonViChiNhanh: {
    component: Page(DonViChiNhanh),
    accessRoles: [],
    path: "/danh-muc/don-vi-chi-nhanh",
    exact: true,
  },
  diTatBamSinh: {
    component: Page(DiTatBamSinh, [ROLES["DANH_MUC"].DI_TAT_BAM_SINH]),
    accessRoles: [],
    path: "/danh-muc/di-tat-bam-sinh",
    exact: true,
  },
  loaiGiuong: {
    component: Page(LoaiGiuong),
    accessRoles: [],
    path: "/danh-muc/loai-giuong",
    exact: true,
  },
  soHieuGiuong: {
    component: Page(SoHieuGiuong),
    accessRoles: [],
    path: "/danh-muc/so-hieu-giuong",
    exact: true,
  },
  dichVuGiuong: {
    component: Page(DichVuGiuong),
    accessRoles: [],
    path: "/danh-muc/dich-vu-giuong",
    exact: true,
  },
  bacSiNgoaiVien: {
    component: Page(BacSiNgoaiVien),
    accessRoles: [],
    path: "/danh-muc/bac-si-ngoai-vien",
    exact: true,
  },
  maPhieuLinh: {
    component: Page(MaPhieuLinh, [ROLES["DANH_MUC"].MA_PHIEU_LINH]),
    accessRoles: [ROLES["DANH_MUC"].MA_PHIEU_LINH],
    path: "/danh-muc/phieu-linh",
    exact: true,
  },
  cheDoChamSoc: {
    component: Page(CheDoChamSoc, [ROLES["DANH_MUC"].CHE_DO_CHAM_SOC]),
    accessRoles: [],
    path: "/danh-muc/che-do-cham-soc",
    exact: true,
  },
  mauKetQuaPttt: {
    component: Page(MauKetQuaPTTT),
    accessRoles: [],
    path: "/danh-muc/mau-kq-pt-tt",
    exact: true,
  },
  dichVuAn: {
    component: Page(DichVuAn, [ROLES["DANH_MUC"].DICH_VU_AN]),
    accessRoles: [ROLES["DANH_MUC"].DICH_VU_AN],
    path: "/danh-muc/suat-an",
    exact: true,
  },
  loaiHinhThanhToan: {
    component: Page(LoaiHinhThanhToan, []),
    accessRoles: [],
    path: "/danh-muc/loai-hinh-thanh-toan",
    exact: true,
  },
  chiSoSong: {
    component: Page(ChiSoSong, [ROLES["DANH_MUC"].CHI_SO_SONG]),
    accessRoles: [],
    path: "/danh-muc/chi-so-song",
    exact: true,
  },
  phanLoaiBmi: {
    component: Page(PhanLoaiBmi, [ROLES["DANH_MUC"].PHAN_LOAI_BMI]),
    accessRoles: [],
    path: "/danh-muc/phan-loai-bmi",
    exact: true,
  },
  ngayNghiLe: {
    component: Page(NgayNghiLe, [ROLES["DANH_MUC"].NGAY_NGHI_LE]),
    accessRoles: [],
    path: "/danh-muc/ngay-nghi-le",
    exact: true,
  },
  hauQuaTuongTac: {
    component: Page(HauQuaTuongTac, [ROLES["DANH_MUC"].HAU_QUA_TUONG_TAC]),
    accessRoles: [],
    path: "/danh-muc/hau-qua-tuong-tac",
    exact: true,
  },
  mauBenhAnVaoVien: {
    component: Page(MauBenhAnVaoVien, [ROLES["DANH_MUC"].MAU_BENH_AN_VAO_VIEN]),
    accessRoles: [ROLES["DANH_MUC"].MAU_BENH_AN_VAO_VIEN],
    path: "/danh-muc/mau-benh-an-vao-vien",
    exact: true,
  },
  dacTinhDuocLy: {
    component: Page(DacTinhDuocLy, [ROLES["DANH_MUC"].DAC_TINH_DUOC_LY]),
    accessRoles: [],
    path: "/danh-muc/dac-tinh-duoc-ly",
    exact: true,
  },
  mucDoTuongTac: {
    component: Page(MucDoTuongTac, [ROLES["DANH_MUC"].MUC_DO_TUONG_TAC]),
    accessRoles: [],
    path: "/danh-muc/muc-do-tuong-tac",
    exact: true,
  },
  mucDoBangChung: {
    component: Page(MucDoBangChung, []),
    accessRoles: [],
    path: "/danh-muc/muc-do-bang-chung",
    exact: true,
  },
  tuongTacThuoc: {
    component: Page(TuongTacThuoc, [ROLES["DANH_MUC"].TUONG_TAC_THUOC]),
    accessRoles: [],
    path: "/danh-muc/tuong-tac-thuoc",
    exact: true,
  },
  chiPhiHapSayVTYTTaiSuDung: {
    component: Page(ChiPhiHapSayVTYTTaiSuDung, [
      ROLES["DANH_MUC"].CHI_PHI_HAP_SAY,
    ]),
    accessRoles: [],
    path: "/danh-muc/chi-phi-hap-say-vtyt-tai-su-dung",
    exact: true,
  },
  khuVuc: {
    component: Page(KhuVuc, [ROLES["DANH_MUC"].KHU_VUC]),
    accessRoles: [],
    path: "/danh-muc/khu-vuc",
    exact: true,
  },
  nhanTheoDoi: {
    component: Page(NhanTheoDoi, [ROLES["DANH_MUC"].NHAN_THEO_DOI]),
    accessRoles: [],
    path: "/danh-muc/nhan-theo-doi",
    exact: true,
  },
  khangNguyen: {
    component: Page(KhangNguyen, [ROLES["DANH_MUC"].KHANG_NGUYEN]),
    accessRoles: [ROLES["DANH_MUC"].KHANG_NGUYEN],
    path: "/danh-muc/khang-nguyen",
    exact: true,
  },
  luocDoPt: {
    component: Page(LuocDoPt, [ROLES["DANH_MUC"].ANH_LUOC_DO_PT]),
    accessRoles: [ROLES["DANH_MUC"].ANH_LUOC_DO_PT],
    path: "/danh-muc/luoc-do-pt",
    exact: true,
  },
  phuongPhapCheBien: {
    component: Page(PhuongPhapCheBien, [
      ROLES["DANH_MUC"].PHUONG_PHAP_CHE_BIEN,
    ]),
    accessRoles: [ROLES["DANH_MUC"].PHUONG_PHAP_CHE_BIEN],
    path: "/danh-muc/phuong-phap-che-bien",
    exact: true,
  },
  manHinhPhieuIn: {
    component: Page(ManHinhPhieuIn, [ROLES["DANH_MUC"].MAN_HINH_PHIEU_IN]),
    accessRoles: [ROLES["DANH_MUC"].MAN_HINH_PHIEU_IN],
    path: "/danh-muc/phieu-in",
    exact: true,
  },
  Protocol: {
    component: Page(Protocol, [ROLES["DANH_MUC"].PROTOCOL]),
    accessRoles: [ROLES["DANH_MUC"].PROTOCOL],
    path: "/danh-muc/protocol",
    exact: true,
  },
  phanLoaiNB: {
    component: Page(PhanLoaiNB, [ROLES["DANH_MUC"].PHAN_LOAI_NB]),
    accessRoles: [ROLES["DANH_MUC"].PHAN_LOAI_NB],
    path: "/danh-muc/phan-loai-nb",
    exact: true,
  },
  vacXin: {
    component: Page(VacXin, [ROLES["DANH_MUC"].VAC_XIN]),
    accessRoles: [ROLES["DANH_MUC"].VAC_XIN],
    path: "/danh-muc/vac-xin",
    exact: true,
  },
  caLamViec: {
    component: Page(CalamViec, [ROLES["DANH_MUC"].CA_LAM_VIEC]),
    accessRoles: [ROLES["DANH_MUC"].CA_LAM_VIEC],
    path: "/danh-muc/ca-lam-viec",
    exact: true,
  },
  mauKqXnDotBien: {
    component: Page(MauKQXNDotBien, [ROLES["DANH_MUC"].MAU_KQ_XN_DOT_BIEN]),
    accessRoles: [],
    path: "/danh-muc/mau-kq-xn-dot-bien",
    exact: true,
  },
  quyTrinhXetNghiem: {
    component: Page(QuyTrinhXetNghiem, []),
    accessRoles: [],
    path: "/danh-muc/quy-trinh-xet-nghiem",
    exact: true,
  },
  phanLoaiPHCN: {
    component: Page(PhanLoaiPHCN, [ROLES["DANH_MUC"].PHAN_LOAI_PHCN]),
    accessRoles: [ROLES["DANH_MUC"].PHAN_LOAI_PHCN],
    path: "/danh-muc/phan-loai-phcn",
    exact: true,
  },
  thiLuc: {
    component: Page(ThiLuc, [ROLES["DANH_MUC"].THI_LUC]),
    accessRoles: [],
    path: "/danh-muc/thi-luc",
    exact: true,
  },
  donViSph: {
    component: Page(DonViSph, [ROLES["DANH_MUC"].DON_VI_SPH]),
    accessRoles: [],
    path: "/danh-muc/don-vi-sph",
    exact: true,
  },
  tatKhucXa: {
    component: Page(TatKhucXa, [ROLES["DANH_MUC"].TAT_KHUC_XA]),
    accessRoles: [],
    path: "/danh-muc/tat-khuc-xa",
    exact: true,
  },
  donViAxis: {
    component: Page(DonViAxis, [ROLES["DANH_MUC"].DON_VI_AXIS]),
    accessRoles: [],
    path: "/danh-muc/don-vi-axis",
    exact: true,
  },
  donViCyl: {
    component: Page(DonViCyl, [ROLES["DANH_MUC"].DON_VI_CYL]),
    accessRoles: [],
    path: "/danh-muc/don-vi-cyl",
    exact: true,
  },
  nhanAp: {
    component: Page(NhanAp, [ROLES["DANH_MUC"].NHAN_AP]),
    accessRoles: [],
    path: "/danh-muc/nhan-ap",
    exact: true,
  },
  xangDau: {
    component: Page(XangDau, [ROLES["DANH_MUC"].XANG_DAU]),
    accessRoles: [],
    path: "/danh-muc/xang-dau",
    exact: true,
  },
  phanLoaiPhuongPhapVoCam: {
    component: Page(PhanLoaiPhuongPhapVoCam, [
      ROLES["DANH_MUC"].PHAN_LOAI_PHUONG_PHAP_VO_CAM,
    ]),
    accessRoles: [],
    path: "/danh-muc/phan-loai-phuong-phap-vo-cam",
    exact: true,
  },
  benhYHocCoTruyen: {
    component: Page(BenhYHocCoTruyen, [ROLES["DANH_MUC"].BENH_Y_HOC_CO_TRUYEN]),
    accessRoles: [],
    path: "/danh-muc/benh-y-hoc-co-truyen",
    exact: true,
  },
  loaiNhiemKhuan: {
    component: Page(LoaiNhiemKhuan, [ROLES["DANH_MUC"].LOAI_NHIEM_KHUAN]),
    accessRoles: [ROLES["DANH_MUC"].LOAI_NHIEM_KHUAN],
    path: "/danh-muc/loai-nhiem-khuan",
    exact: true,
  },
  khoaDuLieuBaoCaoKho: {
    component: Page(KhoaDuLieuBaoCaoKho, [
      ROLES["DANH_MUC"].KHOA_DU_LIEU_BAO_CAO_KHO,
    ]),
    accessRoles: [],
    path: "/danh-muc/khoa-du-lieu-bao-cao-kho",
    exact: true,
  },
  mauKetQuaKhamBenh: {
    component: Page(MauKetQuaKhamBenh, [
      ROLES["DANH_MUC"].MAU_KET_QUA_KHAM_BENH,
    ]),
    accessRoles: [],
    path: "/danh-muc/mau-ket-qua-kham-benh",
    exact: true,
  },
  viKhuan: {
    component: Page(DanhMucViKhuan, [ROLES["DANH_MUC"].VI_KHUAN]),
    accessRoles: [ROLES["DANH_MUC"].VI_KHUAN],
    path: "/danh-muc/vi-khuan",
    exact: true,
  },
  doiTuongKcb: {
    component: Page(DoiTuongKcb, [ROLES["DANH_MUC"].DOI_TUONG_KCB]),
    accessRoles: [],
    path: "/danh-muc/doi-tuong-kcb",
    exact: true,
  },
  hangBangLaiXe: {
    component: Page(HangBangLaiXe, [ROLES["DANH_MUC"].HANG_BANG_LAI_XE]),
    accessRoles: [ROLES["DANH_MUC"].HANG_BANG_LAI_XE],
    path: "/danh-muc/hang-bang-lai-xe",
    exact: true,
  },
  khaiBaoPhuCapPttt: {
    component: Page(KhaiBaoPhuCapPttt, [
      ROLES["DANH_MUC"].KHAI_BAO_PHU_CAP_PTTT,
    ]),
    accessRoles: [ROLES["DANH_MUC"].KHAI_BAO_PHU_CAP_PTTT],
    path: "/danh-muc/khai-bao-phu-cap-pttt",
    exact: true,
  },
  luongGiaPhcn: {
    component: Page(LuongGiaPhcn),
    accessRoles: [],
    path: "/danh-muc/luong-gia-phcn",
    exact: true,
  },
  TraCuuTt: {
    component: Page(TraCuuTt, [ROLES["DANH_MUC"].TU_DIEN_Y_KHOA]),
    accessRoles: [ROLES["DANH_MUC"].TU_DIEN_Y_KHOA],
    path: "/danh-muc/tu-dien-y-khoa",
    exact: true,
  },
  laoKhangThuoc: {
    component: Page(LaoKhangThuoc, [ROLES["DANH_MUC"].LAO_KHANG_THUOC]),
    accessRoles: [ROLES["DANH_MUC"].LAO_KHANG_THUOC],
    path: "/danh-muc/lao-khang-thuoc",
    exact: true,
  },
  dinhMucThuocVTYT: {
    component: Page(DinhMucThuocVTYT, [ROLES["DANH_MUC"].DINH_MUC_THUOC_VTYT]),
    accessRoles: [ROLES["DANH_MUC"].DINH_MUC_THUOC_VTYT],
    path: "/danh-muc/dinh-muc-thuoc-vtyt",
    exact: true,
  },
  phuongPhapChanDoan: {
    component: Page(PhuongPhapChanDoan, [
      ROLES["DANH_MUC"].PHUONG_PHAP_CHAN_DOAN,
    ]),
    accessRoles: [ROLES["DANH_MUC"].PHUONG_PHAP_CHAN_DOAN],
    path: "/danh-muc/phuong-phap-chan-doan",
    exact: true,
  },
  phanLoaiTienSu: {
    component: Page(PhanLoaiTienSu, [ROLES["DANH_MUC"].PHAN_LOAI_TIEN_SU]),
    accessRoles: [ROLES["DANH_MUC"].PHAN_LOAI_TIEN_SU],
    path: "/danh-muc/phan-loai-tien-su",
    exact: true,
  },
  vanDeLienQuanDenThuoc: {
    component: Page(VanDeLienQuanDenThuoc, [ROLES["DANH_MUC"].VAN_DE_THUOC]),
    accessRoles: [ROLES["DANH_MUC"].VAN_DE_THUOC],
    path: "/danh-muc/van-de-lien-quan-den-thuoc",
    exact: true,
  },
  XNSaoBenhAn: {
    component: Page(XNSaoBenhAn, [ROLES["DANH_MUC"].XN_SAO_BENH_AN]),
    accessRoles: [ROLES["DANH_MUC"].XN_SAO_BENH_AN],
    path: "/danh-muc/xet-nghiem-sao-benh-an",
    exact: true,
  },
  ngoiThai: {
    component: Page(NgoiThai, [ROLES["DANH_MUC"].NGOI_THAI]),
    accessRoles: [],
    path: "/danh-muc/ngoi-thai",
    exact: true,
  },
  cachThucDe: {
    component: Page(CachThucDe, [ROLES["DANH_MUC"].CACH_THUC_DE]),
    accessRoles: [],
    path: "/danh-muc/cach-thuc-de",
    exact: true,
  },
  chePhamDinhDuong: {
    component: Page(ChePhamDinhDuong, [ROLES["DANH_MUC"].CHE_PHAM_DINH_DUONG]),
    accessRoles: [],
    path: "/danh-muc/che-pham-dinh-duong",
    exact: true,
  },
  bienPapCamau: {
    component: Page(BienPhapCamMau, [ROLES["DANH_MUC"].BIEN_PHAP_CAM_MAU]),
    accessRoles: [],
    path: "/danh-muc/bien-phap-cam-mau",
    exact: true,
  },
  chinhSachHoaHong: {
    component: Page(ChinhSachHoaHong, [
      ROLES["COM"].XEM_DANH_MUC_HOA_HONG,
      ROLES["COM"].SUA_DANH_MUC_HOA_HONG,
    ]),
    accessRoles: [
      ROLES["COM"].XEM_DANH_MUC_HOA_HONG,
      ROLES["COM"].SUA_DANH_MUC_HOA_HONG,
    ],
    path: "/danh-muc/chinh-sach-hoa-hong",
    exact: true,
  },
  nhomDichVuHoaHong: {
    component: Page(NhomDichVuHoaHong, [
      ROLES["COM"].XEM_DANH_MUC_HOA_HONG,
      ROLES["COM"].SUA_DANH_MUC_HOA_HONG,
    ]),
    accessRoles: [
      ROLES["COM"].XEM_DANH_MUC_HOA_HONG,
      ROLES["COM"].SUA_DANH_MUC_HOA_HONG,
    ],
    path: "/danh-muc/nhom-dich-vu-hoa-hong",
    exact: true,
  },
  doiTacHoaHong: {
    component: Page(DoiTacHoaHong, [
      ROLES["COM"].XEM_DANH_MUC_HOA_HONG,
      ROLES["COM"].SUA_DANH_MUC_HOA_HONG,
    ]),
    accessRoles: [
      ROLES["COM"].XEM_DANH_MUC_HOA_HONG,
      ROLES["COM"].SUA_DANH_MUC_HOA_HONG,
    ],
    path: "/danh-muc/doi-tac-hoa-hong",
    exact: true,
  },
  dichVuHoaHong: {
    component: Page(DichVuHoaHong, [
      ROLES["COM"].XEM_DANH_MUC_HOA_HONG,
      ROLES["COM"].SUA_DANH_MUC_HOA_HONG,
    ]),
    accessRoles: [
      ROLES["COM"].XEM_DANH_MUC_HOA_HONG,
      ROLES["COM"].SUA_DANH_MUC_HOA_HONG,
    ],
    path: "/danh-muc/dich-vu-hoa-hong",
    exact: true,
  },
  nhomLoaiDoiTuong: {
    component: Page(NhomLoaiDoiTuong, [ROLES["DANH_MUC"].NHOM_LOAI_DOI_TUONG]),
    accessRoles: [ROLES["DANH_MUC"].NHOM_LOAI_DOI_TUONG],
    path: "/danh-muc/nhom-loai-doi-tuong",
    exact: true,
  },
  goTat: {
    component: Page(GoTat, [ROLES["DANH_MUC"].GO_TAT]),
    accessRoles: [ROLES["DANH_MUC"].GO_TAT],
    path: "/danh-muc/go-tat",
    exact: true,
  },
  nhomDaiPhieuNhapXuat: {
    component: Page(NhomDaiPhieuNhapXuat, [
      ROLES["DANH_MUC"].NHOM_DAI_PHIEU_XUAT,
    ]),
    accessRoles: [ROLES["DANH_MUC"].NHOM_DAI_PHIEU_XUAT],
    path: "/danh-muc/nhom-dai-phieu-nhap-xuat",
    exact: true,
  },
  coPhim: {
    component: Page(CoPhim, [ROLES["DANH_MUC"].CO_PHIM]),
    accessRoles: [ROLES["DANH_MUC"].CO_PHIM],
    path: "/danh-muc/co-phim",
    exact: true,
  },
  phanLoaiVatTu: {
    component: Page(PhanLoaiVatTu, [ROLES["DANH_MUC"].PHAN_LOAI_VAT_TU]),
    accessRoles: [ROLES["DANH_MUC"].PHAN_LOAI_VAT_TU],
    path: "/danh-muc/phan-loai-vat-tu",
    exact: true,
  },
  thuocVatTuBanGiao: {
    component: Page(ThuocVatTuBanGiao, [
      ROLES["DANH_MUC"].THUOC_VAT_TU_BAN_GIAO,
    ]),
    accessRoles: [ROLES["DANH_MUC"].THUOC_VAT_TU_BAN_GIAO],
    path: "/danh-muc/thuoc-vat-tu-ban-giao",
    exact: true,
  },
  phanTangNguyCo: {
    component: Page(PhanTangNguyCo, [ROLES["DANH_MUC"].PHAN_TANG_NGUY_CO]),
    accessRoles: [ROLES["DANH_MUC"].PHAN_TANG_NGUY_CO],
    path: "/danh-muc/phan-tang-nguy-co",
    exact: true,
  },
  phuCapDvKyThuat: {
    component: Page(PhuCapDvKyThuat, [ROLES["DANH_MUC"].PHU_CAP_DV_KY_THUAT]),
    accessRoles: [ROLES["DANH_MUC"].PHU_CAP_DV_KY_THUAT],
    path: "/danh-muc/phu-cap-dv-ky-thuat",
    exact: true,
  },
  nhomLoaiBenhAn: {
    component: Page(NhomLoaiBenhAn, [ROLES["DANH_MUC"].NHOM_LOAI_BENH_AN]),
    accessRoles: [ROLES["DANH_MUC"].NHOM_LOAI_BENH_AN],
    path: "/danh-muc/nhom-loai-benh-an",
    exact: true,
  },
  thangBaoCao: {
    component: Page(ThangBaoCao, [ROLES["DANH_MUC"].THANG_BAO_CAO]),
    accessRoles: [ROLES["DANH_MUC"].THANG_BAO_CAO],
    path: "/danh-muc/thang-bao-cao",
    exact: true,
  },
  thamGiaHoiChan: {
    component: Page(ThamGiaHoiChan, [ROLES["DANH_MUC"].THAM_GIA_HOI_CHAN]),
    accessRoles: [ROLES["DANH_MUC"].THAM_GIA_HOI_CHAN],
    path: "/danh-muc/tham-gia-hoi-chan",
    exact: true,
  },
  dieuTriKetHop: {
    component: Page(DieuTriKetHop, [ROLES["DANH_MUC"].DIEU_TRI_KET_HOP]),
    accessRoles: [],
    path: "/danh-muc/dieu-tri-ket-hop",
    exact: true,
  },
  dieuKienChiDinh: {
    component: Page(DieuKienChiDinh, [ROLES["DANH_MUC"].DIEU_KIEN_CHI_DINH]),
    accessRoles: [ROLES["DANH_MUC"].DIEU_KIEN_CHI_DINH],
    path: "/danh-muc/dieu-kien-chi-dinh",
    exact: true,
  },
  nhomHuongPhuCapPTTT: {
    component: Page(NhomHuongPhuCapPTTT, [
      ROLES["DANH_MUC"].NHOM_HUONG_PHU_CAP_PTTT,
    ]),
    accessRoles: [],
    path: "/danh-muc/nhom-huong-phu-cap-pttt",
    exact: true,
  },
  nhomPhuCapPtTt: {
    component: Page(NhomPhuCapPtTt, [ROLES["DANH_MUC"].NHOM_PHU_CAP_PT_TT]),
    accessRoles: [ROLES["DANH_MUC"].NHOM_PHU_CAP_PT_TT],
    path: "/danh-muc/nhom-phu-cap-pt-tt",
    exact: true,
  },
  loaiHienThiPhieuIn: {
    component: Page(LoaiHienThiPhieuIn, [
      ROLES["DANH_MUC"].LOAI_HIEN_THI_PHIEU_IN,
    ]),
    accessRoles: [],
    path: "/danh-muc/loai-hien-thi-phieu-in",
    exact: true,
  },
  khaiBaoHangHoaDungKemDvkt: {
    component: Page(KhaiBaoHangHoaDungKemDvkt, [
      ROLES["DANH_MUC"].KHAI_BAO_HANG_HOA_DUNG_KEM_DVKT,
    ]),
    accessRoles: [ROLES["DANH_MUC"].KHAI_BAO_HANG_HOA_DUNG_KEM_DVKT],
    path: "/danh-muc/khai-bao-hang-hoa-dung-kem-dvkt",
    exact: true,
  },
};
