import { client, dataPath } from "client/request";
import { NB_GIAY_CHUNG_SINH } from "client/api";
import apiBase from "./api-base";
import { combineUrlParams } from "utils";

export default {
  ...apiBase.init({ API: NB_GIAY_CHUNG_SINH }),
  getById: (id) => {
    return new Promise((resolve, reject) => {
      client
        .get(`${dataPath}${NB_GIAY_CHUNG_SINH}/${id}`)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  put: ({ id, ...rest }) => {
    return new Promise((resolve, reject) => {
      client
        .put(`${dataPath}${NB_GIAY_CHUNG_SINH}/${id}`, rest)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  dayGiayChungSinhHangLoat: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_GIAY_CHUNG_SINH}/day-giay-chung-sinh`, payload)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  dayGiayChungSinh: ({ id, conThu, nbDotDieuTriId }) => {
    return new Promise((resolve, reject) => {
      client
        .post(
          combineUrlParams(
            `${dataPath}${NB_GIAY_CHUNG_SINH}/day-giay-chung-sinh/${id}`,
            { conThu, nbDotDieuTriId }
          )
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  huyGiayChungSinh: ({ id, conThu, nbDotDieuTriId }) => {
    return new Promise((resolve, reject) => {
      client
        .post(
          combineUrlParams(
            `${dataPath}${NB_GIAY_CHUNG_SINH}/huy-giay-chung-sinh/${id}`,
            { conThu, nbDotDieuTriId }
          )
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  kyHangLoat: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_GIAY_CHUNG_SINH}/ky`, payload)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  dayGiayChungSinhToken: ({ file, conThu, id }) => {
    return new Promise((resolve, reject) => {
      const formData = new FormData();
      formData.append("file", file);
      formData.append("conThu", conThu);
      formData.append("id", id);
      client
        .post(
          `${dataPath}${NB_GIAY_CHUNG_SINH}/day-giay-chung-sinh/token`,
          formData
        )
        .then((res) => {
          if (res?.data?.code === 0) {
            resolve(res?.data);
          } else reject(res?.data);
        })
        .catch((e) => {
          resolve(e);
        });
    });
  },
};
