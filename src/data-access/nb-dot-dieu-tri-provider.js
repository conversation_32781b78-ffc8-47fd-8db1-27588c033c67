import { combineUrlParams } from "utils";
import { client, dataPath } from "client/request";
import { C06, NB_DOT_DIEU_TRI, NB_TU_VONG } from "client/api";
import { getThongTinPhieu } from "./nguoiBenh/getThongTinPhieu";
import apiBase from "./api-base";

const nbDotDieuTriProvider = {
  ...apiBase.init({ API: NB_DOT_DIEU_TRI }),
  kiemTraThanhToan: ({ maNb = "", ...payload }) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(
            `${dataPath}${NB_DOT_DIEU_TRI}/kiem-tra-thanh-toan`,
            {
              maNb,
              ...payload,
            }
          )
        )
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  kiemTraRaVien: ({ id, ...rest }) => {
    return new Promise((resolve, reject) => {
      client
        .post(
          combineUrlParams(
            `${dataPath}${NB_DOT_DIEU_TRI}/kiem-tra-ra-vien/${id}`,
            rest
          )
        )
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  tongTienDieuTri: (id) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(`${dataPath}${NB_DOT_DIEU_TRI}/tong-tien/${id}`, {})
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  searchNBDotDieuTriTongHop: ({
    signal,
    nbThongTinId,
    page = 0,
    sort,
    size = 500,
    ...payload
  }) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(`${dataPath}${NB_DOT_DIEU_TRI}/tong-hop`, {
            nbThongTinId,
            page: page,
            sort,
            size,
            ...payload,
          }),
          {
            signal,
          }
        )
        .then((s) => {
          if (s.data.code === 0) {
            resolve(s.data);
          } else {
            reject(s?.data);
          }
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  capNhatDotDieuTri: (data) => {
    return new Promise((resolve, reject) => {
      client
        .patch(`${dataPath}${NB_DOT_DIEU_TRI}/${data.id}`, data)
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  capNhatSoNgayDieuTri: (data) => {
    return new Promise((resolve, reject) => {
      client
        .put(`${dataPath}${NB_DOT_DIEU_TRI}/so-ngay-dieu-tri/${data.id}`, data)
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  getPhieuIn: ({
    nbDotDieuTriId,
    maManHinh,
    maViTri,
    chiDinhTuDichVuId,
    chiDinhTuLoaiDichVu,
    dsChiDinhTuLoaiDichVu,
    khoaId,
    dsLoaiPhieuInId,
  }) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(
            `${dataPath}${NB_DOT_DIEU_TRI}/phieu-in/${nbDotDieuTriId}`,
            {
              maManHinh,
              maViTri,
              chiDinhTuDichVuId,
              chiDinhTuLoaiDichVu,
              dsChiDinhTuLoaiDichVu,
              khoaId,
              dsLoaiPhieuInId,
            }
          )
        )
        .then((s) => {
          if (s.data.code === 0 && s.data.data) {
            if (window.nbDotDieuTriPhieuIn) s = window.nbDotDieuTriPhieuIn(s);
            resolve(s.data);
          } else {
            reject(s?.data);
          }
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  getThongTinPhieu: getThongTinPhieu,
  getPhieuHenKham: (nbDotDieuTriId) => {
    // /api/his/v1/nb-dv-kham/phieu-ket-luan/38065
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(
            `${dataPath}${NB_DOT_DIEU_TRI}/phieu-hen-kham/${nbDotDieuTriId}`
          )
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  getGiayHenDieuTri: (nbDotDieuTriId) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(
            `${dataPath}${NB_DOT_DIEU_TRI}/giay-hen-dieu-tri/${nbDotDieuTriId}`
          )
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  getBangKeChiPhiKb_Cb: (id) => {
    return new Promise((resolve, reject) => {
      client
        .get(`${dataPath}${NB_DOT_DIEU_TRI}/bang-ke-chi-phi/${id}`)
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  huyTiepDon: ({ active, ...payload }) => {
    return new Promise((resolve, reject) => {
      client
        .post(combineUrlParams(`${dataPath}${NB_DOT_DIEU_TRI}/huy-tiep-don`), {
          ...payload,
          active,
        })
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  macDinh: () => {
    return new Promise((resolve, reject) => {
      client
        .get(combineUrlParams(`${dataPath}${NB_DOT_DIEU_TRI}/mac-dinh`, {}))
        .then((s) => {
          if (s.data?.code === 0) {
            resolve(s.data);
          } else {
            reject(s?.data);
          }
        });
    });
  },
  getNbLapBenhAn: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(`${dataPath}${NB_DOT_DIEU_TRI}/lap-benh-an`, payload)
        )
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  getNbLapBenhAnById: (id) => {
    return new Promise((resolve, reject) => {
      client
        .get(`${dataPath}${NB_DOT_DIEU_TRI}/lap-benh-an/${id}`)
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  postLapBenhAn: (id, payload) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_DOT_DIEU_TRI}/lap-benh-an/${id}`, payload)
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  postLapBenhAnDaiHan: (nbDotDieuTriId, payload) => {
    return new Promise((resolve, reject) => {
      client
        .post(
          `${dataPath}${NB_DOT_DIEU_TRI}/lap-benh-an-dai-han/${nbDotDieuTriId}`,
          payload
        )
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  xoaBenhAn: (id) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_DOT_DIEU_TRI}/xoa-benh-an/${id}`)
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  huyBenhAn: (id) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_DOT_DIEU_TRI}/huy-benh-an/${id}`)
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  huyBenhAnDaiHan: (id) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_DOT_DIEU_TRI}/huy-benh-an-dai-han/${id}`)
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  getNbNoiTru: ({ signal, ...payload }) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(`${dataPath}${NB_DOT_DIEU_TRI}/noi-tru`, payload),
          {
            signal,
          }
        )
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  getNbRaVien: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .get(combineUrlParams(`${dataPath}${NB_DOT_DIEU_TRI}/ra-vien`, payload))
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  getNbNoiTruById: (id) => {
    return new Promise((resolve, reject) => {
      client
        .get(`${dataPath}${NB_DOT_DIEU_TRI}/noi-tru/${id}`)
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  getListNbKSK: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .get(combineUrlParams(`${dataPath}${NB_DOT_DIEU_TRI}/ksk`, payload))
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },

  postNbKSK: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_DOT_DIEU_TRI}`, payload)
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },

  patchNbKSK: ({ id, ...rest }) => {
    return new Promise((resolve, reject) => {
      client
        .patch(`${dataPath}${NB_DOT_DIEU_TRI}/${id}`, rest)
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },

  hoanThanhKSK: ({ id, ...rest }) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_DOT_DIEU_TRI}/ksk/hoan-thanh/${id}`, rest)
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },

  huyHoanThanhKSK: ({ id, ...rest }) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_DOT_DIEU_TRI}/ksk/huy-hoan-thanh/${id}`, rest)
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },

  tiepNhanVaoKhoa: ({ id, ...payload }) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_DOT_DIEU_TRI}/tiep-nhan-vao-khoa/${id}`, payload)
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  tuChoiVaoKhoa: ({ id, ...payload }) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_DOT_DIEU_TRI}/tu-choi-vao-khoa/${id}`, payload)
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  huyTiepNhanVaoKhoa: ({ id, ...payload }) => {
    return new Promise((resolve, reject) => {
      client
        .post(
          `${dataPath}${NB_DOT_DIEU_TRI}/huy-tiep-nhan-vao-khoa/${id}`,
          payload
        )
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },

  postTiepNhanNbKSK: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_DOT_DIEU_TRI}/ksk`, payload)
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  huyTiepNhanNbKSK: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_DOT_DIEU_TRI}/huy-tiep-nhan`, payload)
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  huyHoanThanhNbKSK: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_DOT_DIEU_TRI}/huy-hoan-thanh`, payload)
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  mienGiam: (id, payload) => {
    return new Promise((resolve, reject) => {
      client
        .patch(`${dataPath}${NB_DOT_DIEU_TRI}/mien-giam/${id}`, payload)
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  updateThongTinRaVien: ({ id, ...rest }) => {
    return new Promise((resolve, reject) => {
      client
        .put(`${dataPath}${NB_DOT_DIEU_TRI}/tong-ket-ra-vien/${id}`, rest)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  updatePhongDieuTri: ({ id, phongId }) => {
    return new Promise((resolve, reject) => {
      client
        .put(`${dataPath}${NB_DOT_DIEU_TRI}/phong-dieu-tri/${id}`, { phongId })
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  updateThongTinVaoVien: ({ id, ...rest }) => {
    return new Promise((resolve, reject) => {
      client
        .put(`${dataPath}${NB_DOT_DIEU_TRI}/cd-vao-vien/${id}`, rest)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  getThongTinRaVien: (id) => {
    return new Promise((resolve, reject) => {
      client
        .get(`${dataPath}${NB_DOT_DIEU_TRI}/tong-ket-ra-vien/${id}`)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  getBangKeChiPhi: ({ nbDotDieuTriId, ...payload }) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(
            `${dataPath}${NB_DOT_DIEU_TRI}/bang-ke-chi-phi/${nbDotDieuTriId}`,
            { ...payload }
          )
        )
        .then((s) => {
          resolve(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  getBangKeChiPhiNgoaiTru: ({ nbDotDieuTriId, ...payload }) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(
            `${dataPath}${NB_DOT_DIEU_TRI}/bang-ke-kcb-ngoai-tru/${nbDotDieuTriId}`,
            { ...payload }
          )
        )
        .then((s) => {
          resolve(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  getPhieuRaVien: ({ nbDotDieuTriId, ...rest }) => {
    // https://api-sakura-test.isofh.vn/api/his/v1/nb-dot-dieu-tri/bang-ke-chi-phi/\{nb-dot-dieu-tri-id}?
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(
            `${dataPath}${NB_DOT_DIEU_TRI}/phieu-ra-vien/${nbDotDieuTriId}`,
            rest
          )
        )
        .then((s) => {
          resolve(s?.data);
        })
        .catch((e) => reject(e));
    });
  },

  putPhieuRaVien: ({ id, ...rest }) => {
    return new Promise((resolve, reject) => {
      client
        .put(`${dataPath}${NB_DOT_DIEU_TRI}/phieu-ra-vien/${id}`, rest)
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },

  getPhieuBaoTu: (nbDotDieuTriId) => {
    // /api/his/v1/nb-dot-dieu-tri/phieu-bao-tu/{nbDotDieuTriId} (phiếu thường)
    return new Promise((resolve, reject) => {
      client
        .get(`${dataPath}${NB_TU_VONG}/phieu-bao-tu/${nbDotDieuTriId}`)
        .then((s) => {
          resolve(s?.data);
        })
        .catch((e) => reject(e));
    });
  },

  getNbLichKhamKSK: (params) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(
            `${dataPath}${NB_DOT_DIEU_TRI}/ksk/lich-kham`,
            params
          )
        )
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },

  postLichKhamKSK: (body) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_DOT_DIEU_TRI}/ksk/lich-kham`, body)
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },

  postImport: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_DOT_DIEU_TRI}/ksk/import`, payload, {
          responseType: "arraybuffer",
        })
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  ketThucDieuTri: ({ id, ...payload }) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_DOT_DIEU_TRI}/ra-vien/${id}`, payload)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  choVaoVienLai: ({ id, ...payload }) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_DOT_DIEU_TRI}/huy-ra-vien/${id}`, payload)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  duKienRaVien: ({ id, ...payload }) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_DOT_DIEU_TRI}/du-kien-ra-vien/${id}`, payload)
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  huyDuKienRaVien: ({ id, ...payload }) => {
    return new Promise((resolve, reject) => {
      client
        .post(
          `${dataPath}${NB_DOT_DIEU_TRI}/huy-du-kien-ra-vien/${id}`,
          payload
        )
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  ngatDieuTri: ({ id, ...payload }) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_DOT_DIEU_TRI}/ngat-dieu-tri/${id}`, payload)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },

  dayPhieuRaVienById: (id) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_DOT_DIEU_TRI}/day-phieu-ra-vien/${id}`)
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },

  dayPhieuRaVien: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_DOT_DIEU_TRI}/day-phieu-ra-vien`, payload)
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },

  huyPhieuRaVienById: (id) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_DOT_DIEU_TRI}/huy-phieu-ra-vien/${id}`)
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },

  getBangKeChiPhi10Ngay: (nbDotDieuTriId) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          `${dataPath}${NB_DOT_DIEU_TRI}/bang-ke-chi-phi-10ngay/${nbDotDieuTriId}`
        )
        .then((s) => {
          resolve(s?.data);
        })
        .catch((e) => reject(e));
    });
  },

  getBangKeChiPhiNgoai10Ngay: (nbDotDieuTriId) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          `${dataPath}${NB_DOT_DIEU_TRI}/bang-ke-chi-phi-ngoai-10ngay/${nbDotDieuTriId}`
        )
        .then((s) => {
          resolve(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  putLapBenhAn: ({ id, ...payload }) => {
    return new Promise((resolve, reject) => {
      client
        .put(`${dataPath}${NB_DOT_DIEU_TRI}/lap-benh-an/${id}`, payload)
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  putBenhAnDaiHan: (nbDotDieuTriId, payload) => {
    return new Promise((resolve, reject) => {
      client
        .put(
          `${dataPath}${NB_DOT_DIEU_TRI}/benh-an-dai-han/${nbDotDieuTriId}`,
          payload
        )
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  //tiêm chủng
  searchNbTiemChung: ({ page = 0, sort, size = 500, ...payload }) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(`${dataPath}${NB_DOT_DIEU_TRI}/tiem-chung`, {
            page: page,
            sort,
            size,
            ...payload,
          })
        )
        .then((s) => {
          if (s.data.code === 0) {
            resolve(s.data);
          } else {
            reject(s?.data);
          }
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  getThongTinNbTiemChung: (nbDotDieuTriId) => {
    return new Promise((resolve, reject) => {
      client
        .get(`${dataPath}${NB_DOT_DIEU_TRI}/tiem-chung/${nbDotDieuTriId}`)
        .then((s) => {
          resolve(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  bangCamKetNoiQuy: (nbDotDieuTriId) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          `${dataPath}${NB_DOT_DIEU_TRI}/bang-cam-ket-noi-quy/${nbDotDieuTriId}`
        )
        .then((s) => {
          resolve(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  camDoanSuDungDv: (nbDotDieuTriId, params = {}) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(
            `${dataPath}${NB_DOT_DIEU_TRI}/cam-doan-su-dung-dv/${nbDotDieuTriId}`,
            params
          )
        )
        .then((s) => {
          resolve(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  temNguoiBenh: (nbDotDieuTriId) => {
    return new Promise((resolve, reject) => {
      client
        .get(`${dataPath}${NB_DOT_DIEU_TRI}/tem-nguoi-benh/${nbDotDieuTriId}`)
        .then((s) => {
          resolve(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  temTheNuoiBenh: (id) => {
    return new Promise((resolve, reject) => {
      client
        .get(`${dataPath}${NB_DOT_DIEU_TRI}/tem-the-nuoi-benh/${id}`)
        .then((s) => {
          resolve(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  lienKetNb: ({ nbDotDieuTriId, ...payload }) => {
    return new Promise((resolve, reject) => {
      client
        .post(
          `${dataPath}${NB_DOT_DIEU_TRI}/lien-ket-nb/${nbDotDieuTriId}`,
          payload
        )
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  dongBoDuLieuGoiMo: ({ dsId }) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_DOT_DIEU_TRI}/tinh-lai-tien`, {
          dsId,
        })
        .then((s) => {
          resolve(s?.data);
        })
        .catch((e) => reject(e));
    });
  },

  tiepDonTheoHen: ({ id, ...rest }) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_DOT_DIEU_TRI}/tiep-don-theo-hen/${id}`, rest)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  getDoiTuongKcb: (id) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(
            `${dataPath}${NB_DOT_DIEU_TRI}/doi-tuong-kcb/${id}`,
            {}
          )
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  loiDan: (payload, id) => {
    return new Promise((resolve, reject) => {
      client
        .put(`${dataPath}${NB_DOT_DIEU_TRI}/loi-dan/${id}`, payload)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  getTaiKhoanIsc: (id) => {
    return new Promise((resolve, reject) => {
      client
        .get(`${dataPath}${NB_DOT_DIEU_TRI}/tai-khoan-isc/${id}`)
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  capNhatDvKSK: ({ hopDongKskId, payload }) => {
    return new Promise((resolve, reject) => {
      client
        .post(
          `${dataPath}${NB_DOT_DIEU_TRI}/ksk/dich-vu/${hopDongKskId}`,
          payload
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  getPhieuThongTinNb: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(
            `${dataPath}${NB_DOT_DIEU_TRI}/thong-tin-nb`,
            payload
          )
        )
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  updateLyDoDenKham: ({ id, ...payload }) => {
    return new Promise((resolve, reject) => {
      client
        .put(`${dataPath}${NB_DOT_DIEU_TRI}/ly-do-kham/${id}`, payload)
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  doiMaNguoiBenh: ({ nbDotDieuTriId, maNb }) => {
    return new Promise((resolve, reject) => {
      client
        .put(
          combineUrlParams(
            `${dataPath}${NB_DOT_DIEU_TRI}/ma-nb/${nbDotDieuTriId}`
          ),
          {
            maNb,
          }
        )
        .then((s) => {
          if (s?.data?.code === 0) {
            resolve(s?.data);
          } else {
            reject(s?.data);
          }
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  doiMaHoSo: ({ nbDotDieuTriTargetId, nbDotDieuTriSourceId }) => {
    return new Promise((resolve, reject) => {
      client
        .post(
          combineUrlParams(
            `${dataPath}${NB_DOT_DIEU_TRI}/ma-ho-so/${nbDotDieuTriSourceId}`
          ),
          {
            id: nbDotDieuTriTargetId,
          }
        )
        .then((s) => {
          if (s?.data?.code === 0) {
            resolve(s?.data);
          } else {
            reject(s?.data);
          }
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  getNbDotDieuTri: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(`${dataPath}${NB_DOT_DIEU_TRI}`, {
            ...payload,
          })
        )
        .then((s) => {
          if (s?.data?.code === 0) {
            resolve(s?.data);
          } else {
            reject(s?.data);
          }
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  guiDuyetChiPhi: ({ id, ...payload }) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_DOT_DIEU_TRI}/gui-duyet-chi-phi/${id}`, payload)
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  duyetChiPhi: (nbDotDieuTriId) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_DOT_DIEU_TRI}/duyet-chi-phi/${nbDotDieuTriId}`)
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  tuChoiDuyetChiPhi: ({ id, ...rest }) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_DOT_DIEU_TRI}/tu-choi-duyet-chi-phi/${id}`, rest)
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  huyDuyetChiPhi: (nbDotDieuTriId) => {
    return new Promise((resolve, reject) => {
      client
        .post(
          `${dataPath}${NB_DOT_DIEU_TRI}/huy-duyet-chi-phi/${nbDotDieuTriId}`
        )
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  dongBoNguoiBenhGut: ({ id, ...rest }) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}/gut${NB_DOT_DIEU_TRI}/${id}`)
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  dongBoNguoiBenhHisCu: ({ id, ...rest }) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}/dong-bo${NB_DOT_DIEU_TRI}`, rest, {
          responseType: "arraybuffer",
        })
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  temNbKSK: ({ ...rest }) => {
    return new Promise((resolve, reject) => {
      client
        .get(combineUrlParams(`${dataPath}${NB_DOT_DIEU_TRI}/tem-nb-ksk`, rest))
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  getGiayCamKetDieuTriLao: (nbDotDieuTriId) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          `${dataPath}${NB_DOT_DIEU_TRI}/giay-cam-ket-dieu-tri-lao/${nbDotDieuTriId}`
        )
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  phieuCamKetGiamSatVien: (nbDotDieuTriId) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          `${dataPath}${NB_DOT_DIEU_TRI}/phieu-cam-ket-giam-sat-vien/${nbDotDieuTriId}`
        )
        .then((s) => {
          resolve(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  dieuTriNgoaiTru: ({ id, ...rest }) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_DOT_DIEU_TRI}/dieu-tri-ngoai-tru/${id}`, rest)
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  donXinDieuTriTheoYC: (nbDotDieuTriId) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          `${dataPath}${NB_DOT_DIEU_TRI}/don-xin-dieu-tri-theo-yc/${nbDotDieuTriId}`
        )
        .then((s) => {
          resolve(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  updateTruongKhoa: ({ nbDotDieuTriId, ...rest }) => {
    return new Promise((resolve, reject) => {
      client
        .put(
          `${dataPath}${NB_DOT_DIEU_TRI}/tong-ket-ra-vien/truong-khoa/${nbDotDieuTriId}`,
          rest
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  onSaveThongTinNguoiBenh: (payload) => {
    return new Promise((resolve, reject) => {
      if (payload?.id) {
        client
          .patch(`${dataPath}${NB_DOT_DIEU_TRI}/${payload?.id}`, payload?.data)
          .then((s) => {
            resolve(s?.data);
          })
          .catch((e) => reject(e));
      } else {
        client
          .post(`${dataPath}${NB_DOT_DIEU_TRI}`, payload?.data)
          .then((s) => {
            resolve(s?.data);
          })
          .catch((e) => reject(e));
      }
    });
  },
  getCamKetDieuTriCovid: (id) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          `${dataPath}${NB_DOT_DIEU_TRI}/phieu-cam-ket-dtri-covid-tu-xa/${id}`,
          {}
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  getPhieuGiuTheBHYT: (id) => {
    return new Promise((resolve, reject) => {
      client
        .get(`${dataPath}${NB_DOT_DIEU_TRI}/giay-giu-the-bhyt/${id}`, {})
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  exportExcel: (params) => {
    return new Promise((resolve, reject) => {
      client
        .put(`${dataPath}${NB_DOT_DIEU_TRI}/export`, params)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  huyDieuTriNgoaiTru: (id) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_DOT_DIEU_TRI}/huy-dieu-tri-ngoai-tru/${id}`)
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  huyTiepDonTheoHen: (id) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_DOT_DIEU_TRI}/huy-tiep-don-theo-hen/${id}`)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  chinhSuaThoiGianNhapVien: ({ id, ...rest }) => {
    return new Promise((resolve, reject) => {
      client
        .put(`${dataPath}${NB_DOT_DIEU_TRI}/thoi-gian-lap-benh-an/${id}`, rest)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },

  getTemLuuTruBenhAn: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(
            `${dataPath}${NB_DOT_DIEU_TRI}/tem-luu-tru-ba`,
            payload
          )
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  getPhieuTongHop: ({ nbDotDieuTriId, baoCaoId }) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(
            `${dataPath}${NB_DOT_DIEU_TRI}/phieu-tong-hop/${nbDotDieuTriId}`,
            { baoCaoId }
          )
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  kiemTraSuaKhoaDieuTri: (nbDotDieuTriId) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          `${dataPath}${NB_DOT_DIEU_TRI}/sua-khoa-dieu-tri/${nbDotDieuTriId}`
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  importTiemChung: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_DOT_DIEU_TRI}/tiem-chung/import`, payload, {
          responseType: "arraybuffer",
        })
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  chinhSuaThoiGianVaoKhoaNhapVien: ({ nbDotDieuTriId, ...rest }) => {
    return new Promise((resolve, reject) => {
      client
        .put(
          `${dataPath}${NB_DOT_DIEU_TRI}/thoi-gian-vao-khoa-nhap-vien/${nbDotDieuTriId}`,
          rest
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  updateSoPhim: ({ id, ...payload }) => {
    return new Promise((resolve, reject) => {
      client
        .patch(`${dataPath}${NB_DOT_DIEU_TRI}/so-phim/${id}`, payload)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  updateBacSiDieuTri: ({ id, bacSiDieuTriId, dieuDuongPhuTrachId }) => {
    return new Promise((resolve, reject) => {
      client
        .put(`${dataPath}${NB_DOT_DIEU_TRI}/bac-si-dieu-tri/${id}`, {
          bacSiDieuTriId,
          dieuDuongPhuTrachId,
        })
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  updateThongTinCapCuu: ({ id, ...rest }) => {
    return new Promise((resolve, reject) => {
      client
        .put(`${dataPath}${NB_DOT_DIEU_TRI}/cap-cuu/${id}`, rest)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  traCuuLichSuKCB: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${C06}/lich-su-kcb`, payload)
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  traCuuBenhAnDienTu: ({ maLK, maCSKCB }) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${C06}/benh-an-dien-tu`, {
          maLK,
          maCSKCB,
        })
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  getInPhieuThanToanRaVien: (id) => {
    return new Promise((resolve, reject) => {
      client
        .get(`${dataPath}${NB_DOT_DIEU_TRI}/phieu-thanh-toan-ra-vien/${id}`)
        .then((s) => {
          resolve(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  lienKetHoSoSinh: ({ id, ...payload } = {}) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_DOT_DIEU_TRI}/ho-so-du-sinh/${id}`, payload)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  importNbPhanMemCu: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}/dong-bo/nb-dot-dieu-tri/import`, payload, {
          responseType: "arraybuffer",
        })
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  chuyenKhoaNgoaiTru: ({ nbDotDieuTriId, ...payload }) => {
    return new Promise((resolve, reject) => {
      client
        .post(
          `${dataPath}${NB_DOT_DIEU_TRI}/chuyen-khoa/${nbDotDieuTriId}`,
          payload
        )
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  updateNhomMauNb: ({ nbDotDieuTriId, nhomMau }) => {
    return new Promise((resolve, reject) => {
      client
        .put(`${dataPath}${NB_DOT_DIEU_TRI}/nhom-mau/${nbDotDieuTriId}`, {
          nhomMau,
        })
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
};

export default nbDotDieuTriProvider;
