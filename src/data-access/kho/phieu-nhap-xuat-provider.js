import { combineUrlParams } from "utils";
import { client, dataPath } from "client/request";
import { KHO_PHIEU_NHAP_XUAT } from "client/api";
import apiBase from "data-access/api-base";

export default {
  ...apiBase.init({ API: KHO_PHIEU_NHAP_XUAT }),
  //hàm này dùng PUT thay cho GET nên ko dùng searchAll tong-hop của base được
  searchAllByPut: ({ page = 0, sort, size = 10, ...payload }) => {
    return new Promise((resolve, reject) => {
      client
        .put(
          combineUrlParams(`${dataPath}${KHO_PHIEU_NHAP_XUAT}/tong-hop`, {
            page: page + "",
            sort,
            size,
          }),
          payload
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  taoPhieuLinhBu: (params) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${KHO_PHIEU_NHAP_XUAT}/phieu-linh`, params)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  taoPhieuTraBu: (params) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${KHO_PHIEU_NHAP_XUAT}/phieu-tra`, params)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  duyetPhieu: (payload) => {
    let id = payload,
      capNhatCoSo;
    if (typeof payload === "object") {
      capNhatCoSo = payload.capNhatCoSo;
      id = payload.id;
    }
    let url = `${dataPath}${KHO_PHIEU_NHAP_XUAT}/duyet/${id}`;
    if (typeof capNhatCoSo === "boolean") {
      url += `?capNhatCoSo=${capNhatCoSo}`;
    }
    return new Promise((resolve, reject) => {
      client
        .post(url)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  huyDuyet: ({ id, ...payload }) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${KHO_PHIEU_NHAP_XUAT}/huy-duyet/${id}`, {
          ...payload,
        })
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  guiDuyet: (id) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${KHO_PHIEU_NHAP_XUAT}/gui-duyet/${id}`)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  guiDuyetSTT: ({ tuStt, denStt, thoiGianLaySo, dsId }) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${KHO_PHIEU_NHAP_XUAT}/gui-duyet`, {
          tuStt,
          denStt,
          thoiGianLaySo,
          dsId,
        })
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  tuChoiDuyet: ({ id, ...payload }) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${KHO_PHIEU_NHAP_XUAT}/huy-gui-duyet/${id}`, {
          ...payload,
        })
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  kiemTraSoHoaDon: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${KHO_PHIEU_NHAP_XUAT}/so-hoa-don/${payload?.id}`, {
          ...payload,
        })
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  updateGhiChuDonThuocById: ({ id, ...rest }) => {
    return new Promise((resolve, reject) => {
      client
        .put(`${dataPath}${KHO_PHIEU_NHAP_XUAT}/don-thuoc/ghi-chu/${id}`, rest)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  getDanhSachDonThuoc: ({ page = 0, sort, size = 10, ...payload }) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(`${dataPath}${KHO_PHIEU_NHAP_XUAT}/don-thuoc`, {
            page: page + "",
            sort,
            size,
            ...payload,
          })
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  getDanhSachDonThuocByPut: ({ page = 0, sort, size = 10, ...payload }) => {
    return new Promise((resolve, reject) => {
      client
        .put(
          combineUrlParams(`${dataPath}${KHO_PHIEU_NHAP_XUAT}/don-thuoc`, {
            page,
            sort,
            size,
          }),
          payload
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  inPhieuLinh: ({ id, ...rest }) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(
            `${dataPath}${KHO_PHIEU_NHAP_XUAT}/phieu-linh/${id}`,
            rest
          )
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },

  inPhieuNhapXuat: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(
            `${dataPath}${KHO_PHIEU_NHAP_XUAT}/phieu-nhap-xuat`,
            payload
          )
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  inPhieuTraMau: ({ id }) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(
            `${dataPath}${KHO_PHIEU_NHAP_XUAT}/bien-ban-tra-mau/${id}`
          )
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },

  inPhieuTra: (id) => {
    return new Promise((resolve, reject) => {
      client
        .get(`${dataPath}${KHO_PHIEU_NHAP_XUAT}/phieu-tra/${id}`)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },

  getPhieuThuNhaThuoc: ({ id, ...rest }) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(
            `${dataPath}${KHO_PHIEU_NHAP_XUAT}/phieu-thu-nha-thuoc/${id}`,
            rest
          )
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  getTemThuoc: ({ phieuNhapXuatId, ...payload }) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(`${dataPath}${KHO_PHIEU_NHAP_XUAT}/tem-thuoc`, {
            phieuNhapXuatId,
            ...payload,
          })
        )
        .then((s) => {
          if (s?.data?.code == 0) resolve(s.data);
          reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  suaSoLuongDuyet: (id, dsNhapXuatChiTiet) => {
    return new Promise((resolve, reject) => {
      client
        .put(`${dataPath}${KHO_PHIEU_NHAP_XUAT}/sl-duyet/${id}`, {
          dsNhapXuatChiTiet,
        })
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  thoiGianPhat: ({ id, ...rest }) => {
    return new Promise((resolve, reject) => {
      client
        .patch(`${dataPath}${KHO_PHIEU_NHAP_XUAT}/thoi-gian-phat/${id}`, rest)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  huyDatTruoc: (id, body) => {
    return new Promise((resolve, reject) => {
      const url = body
        ? `${dataPath}${KHO_PHIEU_NHAP_XUAT}/huy-dat-truoc`
        : `${dataPath}${KHO_PHIEU_NHAP_XUAT}/huy-dat-truoc/${id}`;

      client
        .post(url, body || {})
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  datTruoc: (id) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${KHO_PHIEU_NHAP_XUAT}/dat-truoc/${id}`)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  inPhieuLinhChiTiet: ({ id, dsPhongId }) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(
            `${dataPath}${KHO_PHIEU_NHAP_XUAT}/phieu-linh-chi-tiet/${id}`,
            { dsPhongId }
          )
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  getDsSlLe: (params) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(
            `${dataPath}${KHO_PHIEU_NHAP_XUAT}/sl-linh-le`,
            params
          )
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  inPhieuDsLinhThuoc: ({ phieuLinhId, params }) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(
            `${dataPath}${KHO_PHIEU_NHAP_XUAT}/phieu-danh-sach-linh-thuoc/${phieuLinhId}`,
            params
          )
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  inPhieuDsLinhVatTu: (phieuLinhId) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(
            `${dataPath}${KHO_PHIEU_NHAP_XUAT}/phieu-danh-sach-linh-vat-tu/${phieuLinhId}`,
            {}
          )
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  getPhieuPhatThuoc: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(
            `${dataPath}${KHO_PHIEU_NHAP_XUAT}/phieu-phat-thuoc`,
            payload
          )
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  getDsTraLe: (params) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(
            `${dataPath}${KHO_PHIEU_NHAP_XUAT}/sl-tra-le`,
            params
          )
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  inPhieuLinhBuKichCo: (id) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(
            `${dataPath}${KHO_PHIEU_NHAP_XUAT}/phieu-linh-vt-kich-co/${id}`,
            {}
          )
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  taiLenFile: ({ file }) => {
    return new Promise((resolve, reject) => {
      const dataForm = new FormData();
      file.forEach((item) => {
        dataForm.append("file", item);
      });

      client
        .post(`${dataPath}${KHO_PHIEU_NHAP_XUAT}/tai-len`, dataForm, {
          headers: { "Content-Type": undefined },
        })
        .then((s) => {
          if (s?.data?.code === 0) {
            resolve(s?.data);
          } else reject(s?.data);
        })
        .catch(reject);
    });
  },
  themDonThuocNgoaiVien: (params) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${KHO_PHIEU_NHAP_XUAT}/don-thuoc-ngoai-vien`, params)
        .then((s) => {
          resolve(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  exportExcel: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .put(`${dataPath}${KHO_PHIEU_NHAP_XUAT}/export`, payload)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  inTemThuocNCC: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(
            `${dataPath}${KHO_PHIEU_NHAP_XUAT}/tem-thuoc-ncc`,
            payload
          )
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  xuatDuLieu: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .put(`${dataPath}${KHO_PHIEU_NHAP_XUAT}/export`, payload)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  inPhieuCongKhaiThuoc: (id) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(
            `${dataPath}${KHO_PHIEU_NHAP_XUAT}/phieu-cong-khai-thuoc/${id}`,
            {}
          )
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  inBienBanKiemNhap: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(
            `${dataPath}${KHO_PHIEU_NHAP_XUAT}/bien-ban-kiem-nhap`,
            payload
          )
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  inPhieuLinhThuocDieuTriNgoaiTru: (id) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          `${dataPath}${KHO_PHIEU_NHAP_XUAT}/phieu-linh-dieu-tri-ngoai-tru/${id}`
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  duyetNhapPhieu: (payload) => {
    let id = payload;
    if (typeof payload === "object" && payload.id) {
      id = payload.id;
    }
    let url = `${dataPath}${KHO_PHIEU_NHAP_XUAT}/duyet-nhap/${id}`;
    return new Promise((resolve, reject) => {
      client
        .post(url, {})
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  getBienBanGiaoNhan: ({ id }) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(
            `${dataPath}${KHO_PHIEU_NHAP_XUAT}/bien-ban-giao-nhan/${id}`
          )
        )
        .then((s) => {
          if (s?.data?.code == 0) resolve(s.data);
          reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  thanhToanNhaCungCap: (id) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${KHO_PHIEU_NHAP_XUAT}/thanh-toan-ncc/${id}`)
        .then((s) => {
          if (s?.data?.code == 0) resolve(s.data);
          reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  huyThanhToanNhaCungCap: (id) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${KHO_PHIEU_NHAP_XUAT}/huy-thanh-toan-ncc/${id}`)
        .then((s) => {
          if (s?.data?.code == 0) resolve(s.data);
          reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  chuyenPhieuLinhBu: ({ id, ...params }) => {
    return new Promise((resolve, reject) => {
      client
        .post(
          `${dataPath}${KHO_PHIEU_NHAP_XUAT}/phieu-linh/nguoi-benh/${id}`,
          params
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  getBienBanThanhLy: ({ id }) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(
            `${dataPath}${KHO_PHIEU_NHAP_XUAT}/bien-ban-thanh-ly/${id}`
          )
        )
        .then((s) => {
          if (s?.data?.code == 0) resolve(s.data);
          reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  inPhieuDsNbLinhThuoc: ({ phieuLinhId, ...params }) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(
            `${dataPath}${KHO_PHIEU_NHAP_XUAT}/phieu-danh-sach-linh-thuoc1/${phieuLinhId}`,
            params
          )
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  updateKhoPhieuNhapXuat: ({ id, ...rest }) => {
    return new Promise((resolve, reject) => {
      client
        .put(`${dataPath}${KHO_PHIEU_NHAP_XUAT}/kho/${id}`, rest)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  inPhieuLinhMau: ({ id, ...rest }) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(
            `${dataPath}${KHO_PHIEU_NHAP_XUAT}/phieu-linh-mau/${id}`,
            rest
          )
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  inTemDuTruMau: (params) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(
            `${dataPath}${KHO_PHIEU_NHAP_XUAT}/tem-du-tru-mau`,
            params
          )
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  huyDuyetNhapPhieu: (payload) => {
    let id = payload;
    if (typeof payload === "object" && payload.id) {
      id = payload.id;
    }
    let url = `${dataPath}${KHO_PHIEU_NHAP_XUAT}/huy-duyet-nhap/${id}`;
    return new Promise((resolve, reject) => {
      client
        .post(url, {})
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  soanDonPhieuNhapXuat: ({ id, ...rest }) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${KHO_PHIEU_NHAP_XUAT}/soan-don/${id}`, rest)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  huySoanDonPhieuNhapXuat: ({ id }) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${KHO_PHIEU_NHAP_XUAT}/huy-soan-don/${id}`, rest)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
};
