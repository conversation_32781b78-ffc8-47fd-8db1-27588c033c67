import { combineUrlParams } from "utils";
import { client, dataPath } from "client/request";
import {
  NB_DOT_DIEU_TRI,
  NB_DV_KHAM_BENH,
  NB_KHAM_CHUYEN_KHOA_CMU,
  NB_KHAM_CHUYEN_KHOA_DA_LIEU,
  NB_KHAM_CHUYEN_KHOA_NGOAI,
  NB_KHAM_CHUYEN_KHOA_RHM,
  NB_KHAM_CHUYEN_KHOA_TMH,
  NB_KHAM_CHUYEN_KHOA_SAN,
  NB_KHAM_CHUYEN_KHOA_NAM,
  NB_KHAM_CHUYEN_KHOA_IVF,
  NB_DV_PHONG,
} from "client/api";
import apiBase from "./api-base";
import { message } from "antd";
import { t } from "i18next";

export default {
  ...apiBase.init({ API: NB_DV_KHAM_BENH }),
  updateNbDvKham: ({ id, ...rest }) => {
    return new Promise((resolve, reject) => {
      client
        .put(`${dataPath}${NB_DV_KHAM_BENH}/kham-benh/${id}`, rest)
        .then((s) => {
          resolve(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  ketLuanKham: ({ id, ...rest }) => {
    return new Promise((resolve, reject) => {
      client
        .put(`${dataPath}${NB_DV_KHAM_BENH}/ket-luan/${id}`, rest)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  ketThucKham: ({ id, ...rest }) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_DV_KHAM_BENH}/ket-luan/${id}`, rest)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  huyKetLuan: ({ id, ...rest }) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_DV_KHAM_BENH}/huy-ket-luan/${id}`, rest)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  huyKetLuanGetMethod: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(
            `${dataPath}${NB_DV_KHAM_BENH}/huy-ket-luan`,
            payload
          )
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  getStatisticsRoom: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(`${dataPath}${NB_DV_KHAM_BENH}/tong-hop-sl`, payload)
        )
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  getHistory: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .get(combineUrlParams(`${dataPath}${NB_DV_KHAM_BENH}/lich-su`, payload))
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  getHanhTrinhKham: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(`${dataPath}${NB_DV_KHAM_BENH}/hanh-trinh-kham`, {
            ...payload,
          })
        )
        .then((s) => {
          if (s.data.code === 0) {
            resolve(s.data);
          } else {
            reject(s?.data);
          }
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  chiDinhDVKham: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_DV_KHAM_BENH}`, payload)
        .then((s) => {
          if (s.data.code === 0) {
            resolve(s.data);
          } else {
            reject(s?.data);
          }
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  tamTinhTienDVKham: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_DV_KHAM_BENH}/tinh-tien`, payload)
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  onDeleteDichVu: ({ id, listDeletingId }) => {
    if (id)
      return new Promise((resolve, reject) => {
        client
          .delete(`${dataPath}${NB_DV_KHAM_BENH}/${id}`)
          .then((s) => {
            resolve(s.data);
          })
          .catch((e) => {
            reject(e);
          });
      });
    return new Promise((resolve, reject) => {
      client
        .delete(`${dataPath}${NB_DV_KHAM_BENH}`, {
          data: listDeletingId,
        })
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  getDsDichVuChiDinhKham: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(`${dataPath}${NB_DV_KHAM_BENH}/tong-hop`, payload)
        )
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  themThongTinPhieu: (payload, id) => {
    return new Promise((resolve, reject) => {
      client
        .patch(
          `${dataPath}${NB_DV_KHAM_BENH}/them-thong-tin/so-phieu/${id}`,
          payload
        )
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  dangKham: (id, payload = {}) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_DV_KHAM_BENH}/dang-kham/${id}`, payload)
        .then((s) => {
          if (s?.data?.code == 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  dangKetLuan: (id, payload = {}) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_DV_KHAM_BENH}/dang-ket-luan/${id}`, payload)
        .then((s) => {
          if (s?.data?.code == 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  daKetLuan: (id, payload = {}) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_DV_KHAM_BENH}/da-ket-luan/${id}`, payload)
        .then((s) => {
          if (s?.data?.code == 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  boQuaKham: (id, payload = {}) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_DV_KHAM_BENH}/bo-qua/${id}`, payload)
        .then((s) => {
          if (s?.data?.code == 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  boQuaKetLuan: (id, payload = {}) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_DV_KHAM_BENH}/bo-qua-ket-luan/${id}`, payload)
        .then((s) => {
          if (s?.data?.code == 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  checkIn: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_DV_KHAM_BENH}/check-in`, payload)
        .then((s) => {
          if (s?.data?.code == 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  getDsNguoiBenhQms: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .get(combineUrlParams(`${dataPath}${NB_DV_KHAM_BENH}/qms`, payload))
        .then((s) => {
          if (s?.data?.code === 1007) {
            message.error(
              `${s?.data?.message}. ${t("qms.vuiLongThietLapPhong")}`
            );
          }
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  loiDan: (payload, id) => {
    return new Promise((resolve, reject) => {
      client
        .patch(`${dataPath}${NB_DV_KHAM_BENH}/loi-dan/${id}`, payload)
        .then((s) => {
          if (s?.data?.code == 0) resolve(s.data);
          reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  getPhieuChiDinh: ({
    nbDotDieuTriId,
    soPhieuId,
    phieuChiDinhId,
    loai,
    ...payload
  }) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(`${dataPath}${NB_DV_KHAM_BENH}/phieu-chi-dinh`, {
            nbDotDieuTriId,
            soPhieuId,
            phieuChiDinhId,
            loai,
            ...payload,
          })
        )
        .then((s) => {
          if (s?.data?.code == 0) resolve(s.data);
          reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  getPhieuChiDinhKetLuan: ({
    nbDotDieuTriId,
    soPhieuId,
    phieuChiDinhId,
    id,
    ...payload
  }) => {
    return new Promise((resolve, reject) => {
      const url = id
        ? `${dataPath}${NB_DV_KHAM_BENH}/phieu-ket-luan/${id}`
        : `${dataPath}${NB_DV_KHAM_BENH}/phieu-ket-luan`;

      client
        .get(
          combineUrlParams(url, {
            nbDotDieuTriId,
            soPhieuId,
            phieuChiDinhId,
            ...payload,
          })
        )
        .then((s) => {
          if (s?.data?.code == 0) resolve(s.data);
          reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  getNbTiepTheo: ({ phongThucHienId, nbTiepTheoId, ...payload }) => {
    return new Promise((resolve, reject) => {
      client
        .post(
          `${dataPath}${NB_DV_KHAM_BENH}/tiep-theo/${phongThucHienId}`,
          nbTiepTheoId ? { nbTiepTheoId } : ""
        )
        .then((s) => {
          if (s?.data?.code == 0) resolve(s.data);
          reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  doiTrangThai: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_DV_KHAM_BENH}/doi-trang-thai`, payload)
        .then((s) => {
          if (s?.data?.code == 0) resolve(s.data);
          reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  phieuKhamBenh: ({ nbDotDieuTriId, ...payload }) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(`${dataPath}${NB_DV_KHAM_BENH}/phieu-kham-benh`, {
            nbDotDieuTriId,
            ...payload,
          })
        )
        .then((s) => {
          if (s?.data?.data !== null) {
            resolve(s?.data);
          } else {
            reject();
          }
        })
        .catch((e) => reject(e));
    });
  },
  phieuKhamBenhTheoId: ({ nbDvKhamId }) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(
            `${dataPath}${NB_DV_KHAM_BENH}/phieu-kham-benh/${nbDvKhamId}`
          )
        )
        .then((s) => {
          if (s?.data?.data !== null) {
            resolve(s?.data);
          } else {
            reject();
          }
        })
        .catch((e) => reject(e));
    });
  },
  inGiayKskHopDong: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(`${dataPath}${NB_DV_KHAM_BENH}/giay-ksk`, payload)
        )
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  inGiayKskNb: ({ id, ...rest }) => {
    return new Promise((resolve, reject) => {
      const url = id
        ? `${dataPath}${NB_DV_KHAM_BENH}/giay-ksk/${id}`
        : `${dataPath}${NB_DV_KHAM_BENH}/giay-ksk`;
      client
        .get(combineUrlParams(url, rest))
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  inPhieuKskNb: ({ ...rest }) => {
    return new Promise((resolve, reject) => {
      client
        .get(combineUrlParams(`${dataPath}${NB_DV_KHAM_BENH}/phieu-ksk`, rest))
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  xuatFileSoKhamBenh: ({ ...rest }) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(
            `${dataPath}${NB_DV_KHAM_BENH}/phieu-ksk-dinh-ky`,
            rest
          )
        )
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  inPhieuKskNhat: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(
            `${dataPath}${NB_DV_KHAM_BENH}/phieu-ksk-nhat`,
            payload
          )
        )
        .then((s) => {
          resolve(s.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  getPhieuKhamChuyenKhoa: (params) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(
            `${dataPath}${NB_DV_KHAM_BENH}/phieu-kham-chuyen-khoa`,
            params
          )
        )
        .then((s) => {
          if (s?.data?.code == 0) resolve(s.data);
          reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  getPhieuKhamBenhKetLuanParams: (payload) => {
    // /api/his/v1/nb-dv-kham/phieu-ket-luan/38065
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(`${dataPath}${NB_DV_KHAM_BENH}/phieu-ket-luan`, {
            ...payload,
          })
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  getPhieuKhamBenhKetLuan: apiBase.init({
    API: `${NB_DV_KHAM_BENH}/phieu-ket-luan`,
  }).getById,

  khongThucHien: (id) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_DV_KHAM_BENH}/khong-thuc-hien/${id}`, {})
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  getNbDvKhamCmu: ({ nbDotDieuTriId }) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(`${dataPath}${NB_DV_KHAM_BENH}/kham-cmu`, {
            nbDotDieuTriId,
          })
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  inPhieuDonKinh: ({ nbDvKhamId }) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(
            `${dataPath}${NB_DV_KHAM_BENH}/phieu-don-kinh/${nbDvKhamId}`
          )
        )
        .then((s) => {
          if (s?.data?.data !== null) {
            resolve(s?.data);
          } else {
            reject();
          }
        })
        .catch((e) => reject(e));
    });
  },
  getNbKhamChuyenKhoaTmh: apiBase.init({ API: NB_KHAM_CHUYEN_KHOA_TMH })
    .getById,
  updateNbKhamChuyenKhoaTmh: apiBase.init({ API: NB_KHAM_CHUYEN_KHOA_TMH }).put,
  getNbKhamChuyenKhoaCmu: apiBase.init({ API: NB_KHAM_CHUYEN_KHOA_CMU })
    .getById,
  updateNbKhamChuyenKhoaCmu: apiBase.init({ API: NB_KHAM_CHUYEN_KHOA_CMU }).put,
  getNbKhamChuyenKhoaRhm: apiBase.init({ API: NB_KHAM_CHUYEN_KHOA_RHM })
    .getById,
  updateNbKhamChuyenKhoaRhm: apiBase.init({ API: NB_KHAM_CHUYEN_KHOA_RHM }).put,
  getNbKhamChuyenKhoaDaLieu: apiBase.init({ API: NB_KHAM_CHUYEN_KHOA_DA_LIEU })
    .getById,
  updateNbKhamChuyenKhoaDaLieu: apiBase.init({
    API: NB_KHAM_CHUYEN_KHOA_DA_LIEU,
  }).put,
  getNbKhamChuyenKhoaKhamNgoai: apiBase.init({
    API: NB_KHAM_CHUYEN_KHOA_NGOAI,
  }).getById,
  updateNbKhamChuyenKhoaKhamNgoai: apiBase.init({
    API: NB_KHAM_CHUYEN_KHOA_NGOAI,
  }).put,
  getNbKhamChuyenKhoaSan: apiBase.init({ API: NB_KHAM_CHUYEN_KHOA_SAN })
    .getById,
  updateNbKhamChuyenKhoaSan: apiBase.init({ API: NB_KHAM_CHUYEN_KHOA_SAN }).put,
  getNbKhamChuyenKhoaKhamNam: apiBase.init({
    API: NB_KHAM_CHUYEN_KHOA_NAM,
  }).getById,
  updateNbKhamChuyenKhoaKhamNam: apiBase.init({
    API: NB_KHAM_CHUYEN_KHOA_NAM,
  }).put,
  getNbKhamChuyenKhoaIVF: apiBase.init({
    API: NB_KHAM_CHUYEN_KHOA_IVF,
  }).getById,
  updateNbKhamChuyenKhoaIVF: apiBase.init({
    API: NB_KHAM_CHUYEN_KHOA_IVF,
  }).put,
  huyTiepNhan: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_DV_KHAM_BENH}/huy-kham`, payload)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  importThongTinKskHopDong: (payload) =>
    apiBase.onImport(payload, async (formData) => {
      if (formData) {
        formData.append("nbDotDieuTriId", payload.nbDotDieuTriId);
        formData.append("nbDvKhamId", payload.nbDvKhamId);
      }
      return apiBase
        .init({ API: `${NB_DV_KHAM_BENH}/ksk` })
        .import(formData)
        .then((s) => {
          message.success(t("khamBenh.importThongTinKskHopDongThanhCong"));
          return s;
        })
        .catch((e) => {
          return Promise.reject(e);
        });
    }),
  importKetQuaKskHopDong: (payload) =>
    apiBase.onImport(payload, async (formData) => {
      if (formData) {
        formData.append("dichVuId", payload.dichVuId);
        formData.append("hopDongKskId", payload.hopDongKskId);
      }
      return apiBase
        .init({ API: `${NB_DV_KHAM_BENH}/ksk` })
        .import(formData)
        .then((s) => {
          message.success(t("khamSucKhoe.importKetQuaKskHopDongThanhCong"));
          return s;
        })
        .catch((e) => {
          return Promise.reject(e);
        });
    }),
  updateChanDoanDvKham: ({ id, ...rest }) => {
    return new Promise((resolve, reject) => {
      client
        .put(`${dataPath}${NB_DV_KHAM_BENH}/chan-doan/${id}`, rest)
        .then((s) => {
          resolve(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  getPhieuTongHop: ({ nbDvKhamId, baoCaoId }) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(
            `${dataPath}${NB_DV_KHAM_BENH}/phieu-tong-hop/${nbDvKhamId}`,
            { baoCaoId }
          )
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },

  chuyenKhoa: ({ id, ...payload }) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_DV_KHAM_BENH}/chuyen-khoa/${id}`, payload)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },

  getNbDvPhong: (rest) => {
    return new Promise((resolve, reject) => {
      client
        .get(combineUrlParams(`${dataPath}${NB_DV_PHONG}`, rest))
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },

  getPhieuKetQuaCLS: (rest) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(
            `${dataPath}${NB_DV_KHAM_BENH}/phieu-ket-qua-cls`,
            rest
          )
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  ngungYLenh: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${NB_DV_KHAM_BENH}/ngung-y-lenh`, payload)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
};
