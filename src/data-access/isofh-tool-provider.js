import { message } from "antd";
import { getBackendUrl, ISOFH_TOOL_HOST } from "client/request";
import { t } from "i18next";
import { guid } from "mainam-react-native-string-utils";
import { getState } from "redux-store/stores";
import fileUtils from "utils/file-utils";

export default {
  getLinkVisualize: () => {
    return new Promise((resolve, reject) => {
      fetch(`${window.isofhToolUrl || ISOFH_TOOL_HOST}/api/command?ma=10`, {
        //targetAddressSpace: "private",
      })
        .then((s) => s.json())
        .then((s) => {
          if (s.code == 0) {
            if (s.data) {
              resolve(s.data);
            } else {
              message.error(t("common.chuaCauHinhThongTinVisualize"));
              reject(s);
            }
          } else {
            message.error(
              s.message || t("common.khongTheLayThongTinLinkVisualize")
            );
            reject(s);
          }
        })
        .catch((e) => {
          message.error(t("common.appiSofHToolChuaDuocKhoiDong"));
          reject(e);
        });
    });
  },
  reloadVisualize: () => {
    return new Promise((resolve, reject) => {
      fetch(`${window.isofhToolUrl || ISOFH_TOOL_HOST}/api/command?ma=20`, {
        //targetAddressSpace: "private",
      })
        .then((s) => s.json())
        .then((s) => {
          resolve(s);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  setTuDienGoTat: (userName, dics) => {
    return new Promise((resolve, reject) => {
      fetch(
        `${window.isofhToolUrl || ISOFH_TOOL_HOST}/api/go-tat/set-tu-dien`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json; charset=utf-8",
          },
          body: JSON.stringify({
            profile: "sakura-" + userName,
            data: dics,
          }),
        }
      )
        .then((s) => s.json())
        .then((s) => {
          resolve(s);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  setOnOffGoTat: (status) => {
    return new Promise((resolve, reject) => {
      fetch(`${window.isofhToolUrl || ISOFH_TOOL_HOST}/api/go-tat`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json; charset=utf-8",
        },
        body: JSON.stringify({
          status,
        }),
      })
        .then((s) => s.json())
        .then((s) => {
          resolve(s);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  autoPrint: (payload) => {
    return new Promise((resolve, reject) => {
      let state = getState();
      let token = state.auth.auth?.access_token;
      const requestOptions = {
        method: "POST",
        headers: {
          "Content-Type": "application/json; charset=utf-8",
          Authorization: token ? "Bearer " + token : "",
          DataPath: "",
          be: getBackendUrl(),
        },
        body: JSON.stringify(payload),
        //targetAddressSpace: "private",
      };

      fetch(
        `${window.isofhToolUrl || ISOFH_TOOL_HOST}/api/his/v1/in-pdf/bao-cao`,
        requestOptions
      )
        .then((s) => s.json())
        .then((s) => {
          if (s?.code !== 0) {
            reject(s);
          } else {
            resolve(s);
          }
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  setUpdate: (urlUpdate, save = 0) => {
    return new Promise((resolve, reject) => {
      fetch(`${window.isofhToolUrl || ISOFH_TOOL_HOST}/api/command?ma=46&url=${urlUpdate}&save=${save}`, {
        //targetAddressSpace: "private",
      })
        .then((s) => s.json())
        .then((s) => {
          console.log(s);
          if (s.code == 0) {
            resolve(s.data)
          }
        })
        .catch((e) => {
        });
    });
  },
  kyXml: async ({ file, xmlString, xmlBase64String, kyBHXH = 0, theChuKy, elementId = "SigningData" }) => {
    let formData = new FormData();
    if (file)
      formData.append('file', file);
    formData.append('kyBHXH', kyBHXH);
    if (xmlString)
      formData.append('xmlString', xmlString);
    if (xmlBase64String)
      formData.append('xmlBase64String', xmlBase64String);
    if (theChuKy)
      formData.append('signatureLocation', theChuKy);
    if (!kyBHXH) {
      //nếu không phải ký bhxh thì truyền elementId lên mặc định là SigningData
      formData.append('elementId', elementId);
    } else {
      //nếu là ký bhxh thì tự sinh Id và ký vào thẻ ./GIAMDINHHS/CHUKYDONVI
      formData.append('signatureId', "CHUKYDONVI-id-" + guid());
    }
    //isofhtool version ******** trở lên
    let res = await fetch(`${window.isofhToolUrl || ISOFH_TOOL_HOST}/api/ky-so/v1/ky-xml`, {
      method: 'POST',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'multipart/form-data'
      },
      body: formData
    })
    res = await res.json();
    if (res?.code == 0 && res.data) {
      const base64 = res.data.base64;
      return base64;
    }
    throw "Ký token không thành công";
  },
  restartApp: () => {
    return new Promise((resolve, reject) => {
      fetch(`${window.isofhToolUrl || ISOFH_TOOL_HOST}/api/command?ma=62`, {
        //targetAddressSpace: "private",
      })
        .then((s) => s.json())
        .then((s) => {
          message.success(s?.message);
          if (s.code == 0) {
            resolve(s.data)
          }
        })
        .catch((e) => {
        });
    });
  },
  putDataCenter: async (data) => {
    let myHeaders = new Headers();
    myHeaders.append("Content-Type", "application/json; charset=utf-8");

    let raw = JSON.stringify(data);

    let requestOptions = {
      method: "PUT",
      headers: myHeaders,
      body: raw,
      redirect: "follow",
      //targetAddressSpace: "private",
    };
    if (data.ma == "TT_THANH_TOAN" && data.value === true) {
      //nếu thanh toán thành công thì phát loa thông báo
      fetch(`${window.isofhToolUrl || ISOFH_TOOL_HOST}/api/qr/command?ma=15&type=1`);
      //clear màn hình
      fetch(`${window.isofhToolUrl || ISOFH_TOOL_HOST}/api/qr/command?ma=99`);
    } else
      if (data.ma == "QR_DATA" || data.ma == "QR_VALUE") {
        if (data.value == null) {
          //clear màn hình
          fetch(`${window.isofhToolUrl || ISOFH_TOOL_HOST}/api/qr/command?ma=99`);
        } else {
          let qrCode = data?.value?.qr?.qr || data?.value?.qrThanhToan?.qr;
          let anhQr = data?.value?.qr?.anhQr || data?.value?.qrThanhToan?.anhQr;
          let thanhTien = data?.value?.thanhTien || data?.value?.tongTien;
          let footer = data?.value?.nganHang ? [data.value?.nganHang?.ten, data.value?.nganHang?.tenNganHang].filter(item => item).join(" - ") : [data.value?.taiKhoan, data.value?.tenNganHang].filter(item => item).join(" - ") || "";
          if (qrCode || anhQr) {
            // phát loa mời quét mã
            fetch(`${window.isofhToolUrl || ISOFH_TOOL_HOST}/api/qr/command?ma=15&type=0`);
            // show qr
            let anhQrBase64 = null;
            if (!qrCode) {
              anhQrBase64 = await fileUtils.urlToBase64(
                anhQr
              );
            }
            fetch(
              `${window.isofhToolUrl || ISOFH_TOOL_HOST}/api/qr/show-qr`,
              {
                method: "POST",
                headers: myHeaders,
                body: JSON.stringify({
                  base64: anhQrBase64,
                  text: qrCode,
                  header: thanhTien || "",
                  footer: footer
                }),
                redirect: "follow",
              }
            )
              .then((response) => response.json())
              .catch((error) => console.log("error", error));
          }
        }
      }
    fetch(
      `${window.isofhToolUrl || ISOFH_TOOL_HOST}/api/his/v1/data-center`,
      requestOptions
    )
      .then((response) => response.json())
      .catch((error) => console.log("error", error));
  },

  getVanTay: async ({ signal } = {}) => {
    return new Promise((resolve, reject) => {
      fetch(`${window.isofhToolUrl || ISOFH_TOOL_HOST}/api/van-tay/get-van-tay?showUI=0`, {
        method: "get",
        signal,
        //targetAddressSpace: "private",
      })
        .then((s) => s.json())
        .then((s) => {
          if (s.code === 0) {
            resolve(s.data);
          } else {
            reject(s);
          }
        })
        .catch((e) => {
          if (e?.name === "AbortError") {
            reject(new Error(t("common.banDaHuyQuetVanTay")));
          }
          else {
            if (e?.name == "TypeError") {
              reject(new Error(t("common.appiSofHToolChuaDuocKhoiDong")))
            }
          }
          reject(e);
        });
    });
  },

  khoiTaoXacThucVanTay: async ({ signal, taiKhoan }) => {
    return new Promise((resolve, reject) => {
      fetch(`${window.isofhToolUrl || ISOFH_TOOL_HOST}/api/van-tay/login/khoi-tao`, {
        method: "post",
        body: JSON.stringify({ taiKhoan }),
        signal,
        headers: {
          "Content-Type": "application/json; charset=utf-8",
          be: getBackendUrl(),
        }
        //targetAddressSpace: "private",
      })
        .then((s) => s.json())
        .then((s) => {
          if (s?.code === 0 && s.data) {
            resolve(s);
          } else {
            reject(s?.message || new Error(t("common.vanTayKhongHopLe")));
          }
        })
        .catch((e) => {
          if (e?.name === "AbortError") {
            reject(new Error(t("common.banDaHuyQuetVanTay")));
          }
          else
            if (e?.name == "TypeError") {
              reject(new Error(t("common.appiSofHToolChuaDuocKhoiDong")))
            }
          reject(e);
        });
    });
  },

  loginVanTay: async ({ signal, taiKhoan, signature, coSoKcbId }) => {
    return new Promise((resolve, reject) => {
      fetch(`${window.isofhToolUrl || ISOFH_TOOL_HOST}/api/van-tay/login`, {
        method: "post",
        body: JSON.stringify({ taiKhoan, signature, coSoKcbId }),
        signal,
        headers: {
          "Content-Type": "application/json; charset=utf-8",
          be: getBackendUrl(),
        }
        //targetAddressSpace: "private",
      })
        .then((s) => s.json())
        .then((s) => {
          if (s?.code === 0 && s.data) {
            resolve(s);
          } else {
            reject(s?.message || new Error(t("common.vanTayKhongHopLe")));
          }
        })
        .catch((e) => {
          if (e?.name === "AbortError") {
            reject(new Error(t("common.banDaHuyQuetVanTay")));
          }
          else
            if (e?.name == "TypeError") {
              reject(new Error(t("common.appiSofHToolChuaDuocKhoiDong")))
            }
          reject(e);
        });
    });
  },


  xacThucVanTay: async ({ signal, dsVanTay, signature, score = 0.8 }) => {
    return new Promise((resolve, reject) => {
      fetch(`${window.isofhToolUrl || ISOFH_TOOL_HOST}/api/van-tay/xac-thuc`, {
        method: "post",
        body: JSON.stringify({ dsVanTay, showUI: 0, signature }),
        signal
        //targetAddressSpace: "private",
      })
        .then((s) => s.json())
        .then((s) => {
          if (s?.code === 0 && s.data?.score?.find(item => item >= score)) {
            resolve(s.data);
          } else {
            reject(new Error(t("common.vanTayKhongHopLe")));
          }
        })
        .catch((e) => {
          if (e?.name === "AbortError") {
            reject(new Error(t("common.banDaHuyQuetVanTay")));
          }
          else
            if (e?.name == "TypeError") {
              reject(new Error(t("common.appiSofHToolChuaDuocKhoiDong")))
            }
          reject(e);
        });
    });
  },
  signText: async (text, type) => {
    return new Promise((resolve, reject) => {
      let myHeaders = new Headers();
      myHeaders.append("Content-Type", "application/json; charset=utf-8");

      let raw = JSON.stringify({ text, type });

      let requestOptions = {
        method: "POST",
        headers: myHeaders,
        body: raw,
        redirect: "follow",
        //targetAddressSpace: "private",
      };
      fetch(`${window.isofhToolUrl || ISOFH_TOOL_HOST}/api/ky-so/v1/ky-text`, requestOptions)
        .then((s) => s.json())
        .then((s) => {
          if (s.code === 0) {
            resolve(s.data);
          } else {
            reject();
          }
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
};
