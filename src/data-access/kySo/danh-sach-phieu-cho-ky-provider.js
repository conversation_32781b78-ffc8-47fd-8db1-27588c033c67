import { combineUrlParams } from "utils";
import { client, dataPath } from "client/request";
import { DANH_SACH_PHIEU_CHO_KY } from "client/api";
import apiBase from "data-access/api-base";

export default {
  ...apiBase.init({ API: DANH_SACH_PHIEU_CHO_KY }),
  searchWithPhieuMoiNhat: ({ page = 0, sort, size = 10, ...payload }) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(`${dataPath}${DANH_SACH_PHIEU_CHO_KY}`, {
            phieuMoiNhat: true,
            page: page + "",
            sort,
            size,
            ...payload,
          })
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  getBaoCaoDaKy: ({ id, ...rest }) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(
            `${dataPath}${DANH_SACH_PHIEU_CHO_KY}/bao-cao/${id}`,
            rest
          )
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  signCustom: ({ api, ...payload }) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${api}`, payload)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  searchPhieu: ({ page = 0, sort, size = 10, ...payload }) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(
            `${dataPath}${DANH_SACH_PHIEU_CHO_KY}/lich-su-ky-chi-tiet`,
            {
              page: page + "",
              sort,
              size,
              ...payload,
            }
          )
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  huyKy: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${DANH_SACH_PHIEU_CHO_KY}/huy-ky`, payload)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  kyDienTu: (payload) => {
    debugger;
    return new Promise((resolve, reject) => {
      let apiName =
        payload.maBaoCao === "EMR_BA077.1"
          ? "api/his/v1/nb-to-dieu-tri/to-dieu-tri-dich-vu"
          : payload.api;
      client
        .post(
          `${apiName}/ky/${payload.idPhieu || payload.soPhieu || payload.id}`,
          {
            anhKy: payload.anhKy,
            id: payload.id,
            chuKySo: payload.chuKySo,
            viTri: payload.viTri,
            baoCaoId: payload.baoCaoId,
            khoaChiDinhId: payload.khoaChiDinhId,
            nguoiKyId: payload.nguoiKyId,
            nbDotDieuTriId: payload.nbDotDieuTriId,
            duLieu: payload?.duLieu,
            soCanCuoc: payload?.soCanCuoc,
            fileChuyenDoi: payload?.fileChuyenDoi,
            tenNguoiKy: payload?.tenNguoiKy,
            soDienThoai: payload?.soDienThoai,
          }
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },

  trinhKySo: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${DANH_SACH_PHIEU_CHO_KY}/trinh-ky`, payload)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },

  trinhKyDienTu: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${payload.api}/trinh-ky/${payload.id}`, {
          id: payload.lichSuKyId,
          chuKySo: payload.chuKySo,
          viTri: payload.viTri,
          nguoiKyId: payload.nguoiKyId,
        })
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },

  tuChoiKy: (id) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${DANH_SACH_PHIEU_CHO_KY}/tu-choi/${id}`)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => reject(e));
    });
  },
  getDuLieuTheoLichSuKyId: ({ lichSuKyId, ...rest }) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(
            `${dataPath}${DANH_SACH_PHIEU_CHO_KY}/du-lieu/${lichSuKyId}`,
            rest
          )
        )
        .then((res) => {
          if (res?.data?.code === 0) {
            resolve(res?.data);
          } else resolve(res?.data);
        })
        .catch((e) => {
          resolve(e);
        });
    });
  },
  getImageSign: ({ id, ...rest }) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(
            `${dataPath}${DANH_SACH_PHIEU_CHO_KY}${DU_LIEU}/${id}`,
            rest
          )
        )
        .then((res) => {
          if (res?.data?.code === 0) {
            resolve(res?.data?.data);
          } else reject(res?.data);
        })
        .catch((e) => {
          resolve(e);
        });
    });
  },
  token: ({ file, viTri, chuKySo, id, ...rest }) => {
    return new Promise((resolve, reject) => {
      const formData = new FormData();
      formData.append("file", file);
      formData.append("viTri", viTri);
      formData.append("chuKySo", chuKySo);
      formData.append("id", id);
      client
        .post(`${dataPath}${DANH_SACH_PHIEU_CHO_KY}/token`, formData)
        .then((res) => {
          if (res?.data?.code === 0) {
            resolve(res?.data);
          } else reject(res?.data);
        })
        .catch((e) => {
          resolve(e);
        });
    });
  },
};
