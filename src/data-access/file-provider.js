import { client, crmPath, dataPath } from "client/request";
import {
  DM_CHUYEN_KHOA,
  DM_KHOA,
  NB_DOT_DIEU_TRI,
  NB_THE_BAO_HIEM,
  DM_NHAN_VIEN,
  DM_BENH_VIEN,
  DM_HANG_THE,
  DM_HUONG_DAN_SU_DUNG,
  NB_DV_CLS_PT_TT,
  DM_MAU_KET_QUA_PT_TT,
  DM_LUOC_DO_PT,
  DM_TRA_CUU_TT,
  NB_PHUC_HOI_CN_KHAM,
  DM_TUONG_TAC_THUOC,
  DM_DICH_VU_KHO,
  DT_YEU_CAU,
} from "client/api";
import { isArray } from "utils/index";

const upload = (file, type, api, fileList, payload) => {  
  let url = "";
  if (api) {
    url = api + "/tai-len";
  } else if (type === "chuyenKhoa") {
    url += `${dataPath}${DM_CHUYEN_KHOA}/tai-len/logo`;
  } else if (type === "khoa") {
    url += `${dataPath}${DM_KHOA}/tai-len/logo`;
  } else if (type === "anhDaiDien") {
    url += `${dataPath}${NB_DOT_DIEU_TRI}/tai-len/anh-dai-dien`;
  } else if (type === "giayChuyenTuyen") {
    url += `${dataPath}${NB_THE_BAO_HIEM}/tai-len/giay-chuyen-tuyen`;
  } else if (type === "giayHenKham") {
    url += `${dataPath}${NB_THE_BAO_HIEM}/tai-len/giay-hen-kham`;
  } else if (type === "nhanVien") {
    url += `${dataPath}${DM_NHAN_VIEN}/tai-len/anh-dai-dien`;
  } else if (type === "benhVien") {
    url += `${dataPath}${DM_BENH_VIEN}/tai-len/logo`;
  } else if (type === "anhMatTruoc") {
    url += `${dataPath}${NB_DOT_DIEU_TRI}/tai-len/anh-can-cuoc`;
  } else if (type === "anhMatSau") {
    url += `${dataPath}${NB_DOT_DIEU_TRI}/tai-len/anh-can-cuoc`;
  } else if (type === "hangThe") {
    url += `${dataPath}${DM_HANG_THE}/tai-len/icon`;
  } else if (type === "anhKyNhanVien") {
    url += `${dataPath}${DM_NHAN_VIEN}/tai-len/anh-ky`;
  } else if (type === "huongDanSuDung") {
    url += `${dataPath}${DM_HUONG_DAN_SU_DUNG}/tai-len/hdsd`;
  } else if (type === "hdsdVideo") {
    url += `${dataPath}${DM_HUONG_DAN_SU_DUNG}/tai-len/video`;
  } else if (type === "hdsdAnhBia") {
    url += `${dataPath}${DM_HUONG_DAN_SU_DUNG}/tai-len/anh-bia`;
  } else if (type === "giayMienCungChiTra") {
    url += `${dataPath}${NB_THE_BAO_HIEM}/tai-len/giay-mien-cung-chi-tra`;
  } else if (type === "ptttLuocDo") {
    url += `${dataPath}${NB_DV_CLS_PT_TT}/pt-tt/tai-len/luoc-do`;
  } else if (type === "dmMauPtttLuocDo") {
    url += `${dataPath}${DM_MAU_KET_QUA_PT_TT}/tai-len/luoc-do`;
  } else if (type === "luocDoPt") {
    url += `${dataPath}${DM_LUOC_DO_PT}/tai-len/luoc-do`;
  } else if (type === "dsFile") {
    url += `${dataPath}${DM_TRA_CUU_TT}/tai-len`;
  } else if (type === "nbPHCNKham") {
    url += `${dataPath}${NB_PHUC_HOI_CN_KHAM}/tai-len`;
  } else if (type === "tuongTacThuoc") {
    url += `${dataPath}${DM_TUONG_TAC_THUOC}/tai-len`;
  } else if (type === "dvKhoHuongDanSuDung") {
    url += `${dataPath}${DM_DICH_VU_KHO}/tai-len/hdsd`;
  } else if (type === "dinhKem") {
    url += `${crmPath}${DT_YEU_CAU}/tai-len/dinh-kem`;
  } else if (type === "anhVanTayNb") {
    url += `${dataPath}${NB_DOT_DIEU_TRI}/tai-len/van-tay/${payload?.nbDotDieuTriId}`;
  }

  const formData = new FormData();

  // support upload multiple files
  if (isArray(fileList, true)) {
    fileList.forEach((item) => {
      formData.append("file", item);
    });
  } else {
    formData.append("file", file);
  }
  return new Promise((resolve, reject) => {
    client
      .post(url, formData)
      .then((s) => {
        resolve(s?.data);
      })
      .catch((e) => reject(e));
  });
};
export default {
  upload,
  uploadImage: ({ file, api, type, ...payload }) => {    
    return upload(file, type, api, null, payload);
  },
  uploadMultilImage: ({ files, api, type, ...payload }) => {
    const delay = (ms) => new Promise((resolve) => setTimeout(resolve, ms));
    return new Promise((resolve, reject) => {
      const uploadWithDelay = async (images) => {
        let results = [];
        for (let i = 0; i < images.length; i++) {
          const file = images[i].originFileObj || images[i];
          try {
            const result = await upload(file, type, api);
            results.push(result);
            // Chờ 0.5 giây trước khi tiếp tục
            if (i < images.length - 1) {
              await delay(500);
            }
          } catch (error) {
            console.error("Upload failed:", error);
            results.push(null); // Hoặc xử lý lỗi theo cách bạn muốn
          }
        }
        return results;
      };

      uploadWithDelay(files)
        .then((results) => {
          resolve({ data: results });
        })
        .catch((error) => {
          console.error("Error in upload process:", error);
          resolve({ data: [] });
        });
    });
  },
};
